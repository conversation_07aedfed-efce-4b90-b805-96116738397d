#!/bin/bash

# Database verification test
echo "🔍 Database Verification Test"
echo "============================"

# Generate JWT token
TOKEN=$(node generate-jwt.js)
BASE_URL="http://localhost:3003"

echo "✅ JWT token generated"
echo ""

# Test with a very simple community first
echo "📋 Testing with simple community links..."

# Test different formats to see which one works
test_formats=(
    "/freecontentcommunity/products/tbvw"
    "freecontentcommunity/products/tbvw"
    "/creators-lalala/events/swae"
    "creators-lalala/events/swae"
)

for url in "${test_formats[@]}"; do
    echo ""
    echo "Testing: $url"
    
    response=$(curl -s -w "%{http_code}" -X POST \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $TOKEN" \
        -d "{\"productLink\":\"$url\"}" \
        "$BASE_URL/api/v1/ebanx-whitelist/resolve-product")
    
    http_code="${response: -3}"
    response_body="${response%???}"
    
    echo "Status: $http_code"
    echo "Response: $response_body"
    
    if [ "$http_code" = "200" ]; then
        echo "✅ SUCCESS! This format works"
        break
    elif [ "$http_code" = "404" ]; then
        echo "❌ Not found - community or product doesn't exist"
    elif [ "$http_code" = "400" ]; then
        echo "❌ Bad request - invalid URL format"
    elif [ "$http_code" = "418" ]; then
        echo "❌ Community not found - database lookup failed"
    else
        echo "❌ Unexpected status: $http_code"
    fi
done

echo ""
echo "🎯 Summary:"
echo "==========="
echo "- If all tests return 418: Database connection or model issue"
echo "- If some return 404: Products don't exist in database"
echo "- If some return 400: URL format validation issue"
echo "- If any return 200: Success!"
echo ""
echo "💡 Next steps:"
echo "1. Check server logs for database connection errors"
echo "2. Verify MongoDB is running and accessible"
echo "3. Check if Community model import is working"
echo "4. Verify the exact community links in the database"