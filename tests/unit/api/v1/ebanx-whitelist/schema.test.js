const schema = require('../../../../../src/api/v1/ebanx-whitelist/schema');

describe('EBANX Whitelist Schema', () => {
  describe('resolveProductSchema', () => {
    describe('Valid product links', () => {
      it('should validate basic product link format', async () => {
        const validData = {
          productLink: '/grandfather-2/products/fsdw',
        };

        const result = await schema.resolveProductSchema.validate(validData);
        expect(result).toEqual(validData);
      });

      it('should validate challenges product link', async () => {
        const validData = {
          productLink: '/pro-54/challenges/boost-your-online-presence-in-28-days',
        };

        const result = await schema.resolveProductSchema.validate(validData);
        expect(result).toEqual(validData);
      });

      it('should validate digital-files product link', async () => {
        const validData = {
          productLink: '/399-401-members.-addremove-within-range/digital-files/frxf',
        };

        const result = await schema.resolveProductSchema.validate(validData);
        expect(result).toEqual(validData);
      });

      it('should validate events product link', async () => {
        const validData = {
          productLink: '/test-community/events/event-slug',
        };

        const result = await schema.resolveProductSchema.validate(validData);
        expect(result).toEqual(validData);
      });

      it('should validate sessions product link', async () => {
        const validData = {
          productLink: '/test-community/sessions/session-slug',
        };

        const result = await schema.resolveProductSchema.validate(validData);
        expect(result).toEqual(validData);
      });

      it('should validate courses product link', async () => {
        const validData = {
          productLink: '/test-community/courses/course-slug',
        };

        const result = await schema.resolveProductSchema.validate(validData);
        expect(result).toEqual(validData);
      });

      it('should validate link with numbers in community code', async () => {
        const validData = {
          productLink: '/community123/products/product456',
        };

        const result = await schema.resolveProductSchema.validate(validData);
        expect(result).toEqual(validData);
      });

      it('should validate link with dots in community code', async () => {
        const validData = {
          productLink: '/community.test/products/product.slug',
        };

        const result = await schema.resolveProductSchema.validate(validData);
        expect(result).toEqual(validData);
      });

      it('should validate link with underscores in community code', async () => {
        const validData = {
          productLink: '/community_test/products/product_slug',
        };

        const result = await schema.resolveProductSchema.validate(validData);
        expect(result).toEqual(validData);
      });

      it('should trim whitespace from product link', async () => {
        const inputData = {
          productLink: '  /grandfather-2/products/fsdw  ',
        };

        const result = await schema.resolveProductSchema.validate(inputData);
        expect(result).toEqual({
          productLink: '/grandfather-2/products/fsdw',
        });
      });
    });

    describe('Invalid product links', () => {
      it('should reject missing productLink', async () => {
        const invalidData = {};

        await expect(schema.resolveProductSchema.validate(invalidData)).rejects.toThrow();
      });

      it('should reject empty productLink', async () => {
        const invalidData = {
          productLink: '',
        };

        await expect(schema.resolveProductSchema.validate(invalidData)).rejects.toThrow('Invalid product URL format');
      });

      it('should reject null productLink', async () => {
        const invalidData = {
          productLink: null,
        };

        await expect(schema.resolveProductSchema.validate(invalidData)).rejects.toThrow();
      });

      it('should reject productLink without leading slash', async () => {
        const invalidData = {
          productLink: 'grandfather-2/products/fsdw',
        };

        await expect(schema.resolveProductSchema.validate(invalidData)).rejects.toThrow('Invalid product URL format');
      });

      it('should reject productLink with only one segment', async () => {
        const invalidData = {
          productLink: '/grandfather-2',
        };

        await expect(schema.resolveProductSchema.validate(invalidData)).rejects.toThrow('Invalid product URL format');
      });

      it('should reject productLink with only two segments', async () => {
        const invalidData = {
          productLink: '/grandfather-2/products',
        };

        await expect(schema.resolveProductSchema.validate(invalidData)).rejects.toThrow('Invalid product URL format');
      });

      it('should reject productLink with more than three segments', async () => {
        const invalidData = {
          productLink: '/grandfather-2/products/fsdw/extra',
        };

        await expect(schema.resolveProductSchema.validate(invalidData)).rejects.toThrow('Invalid product URL format');
      });

      it('should reject productLink with invalid characters in community code', async () => {
        const invalidData = {
          productLink: '/community@test/products/slug',
        };

        await expect(schema.resolveProductSchema.validate(invalidData)).rejects.toThrow('Invalid product URL format');
      });

      it('should reject productLink with spaces', async () => {
        const invalidData = {
          productLink: '/grandfather 2/products/fsdw',
        };

        await expect(schema.resolveProductSchema.validate(invalidData)).rejects.toThrow('Invalid product URL format');
      });

      it('should reject productLink with only slashes', async () => {
        const invalidData = {
          productLink: '///',
        };

        await expect(schema.resolveProductSchema.validate(invalidData)).rejects.toThrow('Invalid product URL format');
      });

      it('should reject productLink with special characters in product type', async () => {
        const invalidData = {
          productLink: '/grandfather-2/products@test/fsdw',
        };

        await expect(schema.resolveProductSchema.validate(invalidData)).rejects.toThrow('Invalid product URL format');
      });

      it('should reject productLink with numbers in product type', async () => {
        const invalidData = {
          productLink: '/grandfather-2/products123/fsdw',
        };

        await expect(schema.resolveProductSchema.validate(invalidData)).rejects.toThrow('Invalid product URL format');
      });

      it('should reject productLink with uppercase letters in product type', async () => {
        const invalidData = {
          productLink: '/grandfather-2/Products/fsdw',
        };

        await expect(schema.resolveProductSchema.validate(invalidData)).rejects.toThrow('Invalid product URL format');
      });

      it('should reject productLink with invalid characters in product slug', async () => {
        const invalidData = {
          productLink: '/grandfather-2/products/fsdw@test',
        };

        await expect(schema.resolveProductSchema.validate(invalidData)).rejects.toThrow('Invalid product URL format');
      });

      it('should reject boolean productLink', async () => {
        const invalidData = {
          productLink: true,
        };

        await expect(schema.resolveProductSchema.validate(invalidData)).rejects.toThrow();
      });

      it('should reject numeric productLink', async () => {
        const invalidData = {
          productLink: 123,
        };

        await expect(schema.resolveProductSchema.validate(invalidData)).rejects.toThrow();
      });

      it('should reject array productLink', async () => {
        const invalidData = {
          productLink: ['/grandfather-2/products/fsdw'],
        };

        await expect(schema.resolveProductSchema.validate(invalidData)).rejects.toThrow();
      });

      it('should reject object productLink', async () => {
        const invalidData = {
          productLink: { url: '/grandfather-2/products/fsdw' },
        };

        await expect(schema.resolveProductSchema.validate(invalidData)).rejects.toThrow();
      });
    });
  });

  describe('Schema structure', () => {
    it('should export resolveProductSchema', () => {
      expect(schema.resolveProductSchema).toBeDefined();
    });

    it('should be a yup schema', () => {
      expect(schema.resolveProductSchema.constructor.name).toBe('ObjectSchema');
    });
  });
});