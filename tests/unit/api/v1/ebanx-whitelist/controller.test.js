const controller = require('../../../../../src/api/v1/ebanx-whitelist/controller');
const urlParserService = require('../../../../../src/services/ebanx-whitelist/url-parser.service');

// Mock dependencies
jest.mock('../../../../../src/services/ebanx-whitelist/url-parser.service');

describe('EBANX Whitelist Controller', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('resolveProduct', () => {
    it('should successfully resolve product and return product details', async () => {
      const mockReq = {
        body: {
          productLink: '/grandfather-2/products/fsdw',
        },
      };

      const mockProductDetails = {
        productId: '507f1f77bcf86cd799439011',
        communityCode: 'grandfather-2',
        productType: 'DIGITAL_FILES',
      };

      urlParserService.parseProductUrl.mockResolvedValue(mockProductDetails);

      const result = await controller.resolveProduct(mockReq);

      expect(urlParserService.parseProductUrl).toHaveBeenCalledWith('/grandfather-2/products/fsdw');
      expect(result).toEqual(mockProductDetails);
    });

    it('should handle challenge product type', async () => {
      const mockReq = {
        body: {
          productLink: '/pro-54/challenges/boost-your-online-presence-in-28-days',
        },
      };
      const mockProductDetails = {
        productId: '507f1f77bcf86cd799439013',
        communityCode: 'pro-54',
        productType: 'CHALLENGE',
      };

      urlParserService.parseProductUrl.mockResolvedValue(mockProductDetails);

      const result = await controller.resolveProduct(mockReq);

      expect(urlParserService.parseProductUrl).toHaveBeenCalledWith('/pro-54/challenges/boost-your-online-presence-in-28-days');
      expect(result).toEqual(mockProductDetails);
    });

    it('should handle digital-files product type', async () => {
      const mockReq = {
        body: {
          productLink: '/399-401-members.-addremove-within-range/digital-files/frxf',
        },
      };

      const mockProductDetails = {
        productId: '507f1f77bcf86cd799439012',
        communityCode: '399-401-members.-addremove-within-range',
        productType: 'DIGITAL_FILES',
      };

      urlParserService.parseProductUrl.mockResolvedValue(mockProductDetails);

      const result = await controller.resolveProduct(mockReq);

      expect(urlParserService.parseProductUrl).toHaveBeenCalledWith('/399-401-members.-addremove-within-range/digital-files/frxf');
      expect(result).toEqual(mockProductDetails);
    });

    it('should propagate errors from urlParserService', async () => {
      const mockReq = {
        body: {
          productLink: '/invalid/url/format',
        },
      };
      const mockError = new Error('Invalid product URL format');
      urlParserService.parseProductUrl.mockRejectedValue(mockError);

      await expect(controller.resolveProduct(mockReq)).rejects.toThrow(mockError);

      expect(urlParserService.parseProductUrl).toHaveBeenCalledWith('/invalid/url/format');
    });

    it('should handle empty productLink', async () => {
      const mockReq = {
        body: {
          productLink: '',
        },
      };
      const mockError = new Error('Invalid product URL format');
      urlParserService.parseProductUrl.mockRejectedValue(mockError);

      await expect(controller.resolveProduct(mockReq)).rejects.toThrow(mockError);

      expect(urlParserService.parseProductUrl).toHaveBeenCalledWith('');
    });

    it('should handle product not found error', async () => {
      const mockReq = {
        body: {
          productLink: '/non-existent/products/slug',
        },
      };
      const mockError = new Error('Product not found with the provided URL');
      urlParserService.parseProductUrl.mockRejectedValue(mockError);

      await expect(controller.resolveProduct(mockReq)).rejects.toThrow(mockError);

      expect(urlParserService.parseProductUrl).toHaveBeenCalledWith('/non-existent/products/slug');
    });

    it('should handle all product types correctly', async () => {
      const testCases = [
        {
          productLink: '/test/events/event-slug',
          expected: { productId: '1', communityCode: 'test', productType: 'EVENT' },
        },
        {
          productLink: '/test/sessions/session-slug',
          expected: { productId: '2', communityCode: 'test', productType: 'SESSION' },
        },
        {
          productLink: '/test/courses/course-slug',
          expected: { productId: '3', communityCode: 'test', productType: 'COURSE' },
        },
      ];

      for (const testCase of testCases) {
        const mockReq = {
          body: {
            productLink: testCase.productLink,
          },
        };
        urlParserService.parseProductUrl.mockResolvedValue(testCase.expected);

        const result = await controller.resolveProduct(mockReq);

        expect(urlParserService.parseProductUrl).toHaveBeenCalledWith(testCase.productLink);
        expect(result).toEqual(testCase.expected);

        jest.clearAllMocks();
      }
    });
  });

  describe('Controller structure', () => {
    it('should export resolveProduct function', () => {
      expect(controller.resolveProduct).toBeDefined();
      expect(typeof controller.resolveProduct).toBe('function');
    });

    it('should have correct function signature', () => {
      expect(controller.resolveProduct.length).toBe(1); // req parameter
    });
  });
});