const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const {
  getWhitelistEntry,
  isProductWhitelisted,
  getProductIdFromTransaction,
  WHITELIST_STATUS,
} = require('../../../src/services/ebanx/ebanxWhitelist.service');
const EbanxWhitelist = require('../../../src/models/ebanxWhitelist.model');
const { PURCHASE_TYPE } = require('../../../src/constants/common');
const logger = require('../../../src/services/logger.service');

// Mock the logger
jest.mock('../../../src/services/logger.service', () => ({
  error: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
}));

// Mock the Community model
jest.mock('../../../src/communitiesAPI/models/community.model', () => ({
  findOne: jest.fn(),
}));

// Mock the constants
jest.mock('../../../src/constants/common', () => ({
  PURCHASE_TYPE: {
    SUBSCRIPTION: 'SUBSCRIPTION',
  },
  ADDON_PURCHASE_TYPES: {
    EVENT: 'EVENT',
    DIGITAL_FILES: 'DIGITAL_FILES',
    COURSE: 'COURSE',
    CHALLENGE: 'CHALLENGE',
    SESSION: 'SESSION',
  },
}));

// Mock the plan constants
jest.mock('../../../src/services/plan/constants', () => ({
  ENTITY_TYPE: {
    NAS_IO_PRO: 'NAS_IO_PRO',
    NAS_IO_PRO_PLUS: 'NAS_IO_PRO_PLUS',
  },
}));

const CommunityModel = require('../../../src/communitiesAPI/models/community.model');
const { ADDON_PURCHASE_TYPES } = require('../../../src/constants/common');
const {
  ENTITY_TYPE: PLAN_ENTITY_TYPE,
} = require('../../../src/services/plan/constants');

describe('EBANX Whitelist Service', () => {
  let mongoServer;

  beforeAll(async () => {
    process.env.NODE_ENV = 'test';
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();

    await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    await EbanxWhitelist.deleteMany({});
    jest.clearAllMocks();
    // Reset mocks
    CommunityModel.findOne.mockReset();
  });

  describe('getWhitelistEntry', () => {
    it('should return whitelist entry when product exists', async () => {
      const productId = new mongoose.Types.ObjectId();
      const whitelistEntry = await EbanxWhitelist.create({
        productId,
        productType: 'COURSE',
        productLink: 'https://example.com/course/123',
        communityCode: 'test-community',
        status: WHITELIST_STATUS.ACTIVE,
        reason: 'Test reason',
        whitelistedBy: '<EMAIL>',
      });

      const result = await getWhitelistEntry(productId);

      expect(result).toBeTruthy();
      expect(result.productId.toString()).toBe(productId.toString());
      expect(result.status).toBe(WHITELIST_STATUS.ACTIVE);
    });

    it('should return null when product does not exist', async () => {
      const productId = new mongoose.Types.ObjectId();

      const result = await getWhitelistEntry(productId);

      expect(result).toBeNull();
    });

    it('should return null when productId is null', async () => {
      const result = await getWhitelistEntry(null);

      expect(result).toBeNull();
    });

    it('should return null and log error when database error occurs', async () => {
      const productId = new mongoose.Types.ObjectId();

      // Mock the findOne method to throw an error
      const originalFindOne = EbanxWhitelist.findOne;
      EbanxWhitelist.findOne = jest.fn().mockImplementation(() => {
        throw new Error('Database error');
      });

      const result = await getWhitelistEntry(productId);

      expect(result).toBeNull();
      expect(logger.error).toHaveBeenCalledWith(
        'EBANX Whitelist: Error getting whitelist entry:',
        expect.any(Error)
      );

      // Restore original method
      EbanxWhitelist.findOne = originalFindOne;
    });
  });

  describe('isProductWhitelisted', () => {
    it('should return true when product is whitelisted and active', async () => {
      const productId = new mongoose.Types.ObjectId();
      await EbanxWhitelist.create({
        productId,
        productType: 'COURSE',
        productLink: 'https://example.com/course/123',
        communityCode: 'test-community',
        status: WHITELIST_STATUS.ACTIVE,
        reason: 'Test reason',
        whitelistedBy: '<EMAIL>',
      });

      const result = await isProductWhitelisted(productId);

      expect(result).toBe(true);
    });

    it('should return false when product is whitelisted but inactive', async () => {
      const productId = new mongoose.Types.ObjectId();
      await EbanxWhitelist.create({
        productId,
        productType: 'COURSE',
        productLink: 'https://example.com/course/123',
        communityCode: 'test-community',
        status: WHITELIST_STATUS.INACTIVE,
        reason: 'Test reason',
        whitelistedBy: '<EMAIL>',
      });

      const result = await isProductWhitelisted(productId);

      expect(result).toBe(false);
    });

    it('should return false when product is not whitelisted', async () => {
      const productId = new mongoose.Types.ObjectId();

      const result = await isProductWhitelisted(productId);

      expect(result).toBe(false);
    });

    it('should return false when productId is null', async () => {
      const result = await isProductWhitelisted(null);

      expect(result).toBe(false);
    });

    it('should return false and log error when database error occurs', async () => {
      const productId = new mongoose.Types.ObjectId();

      // Mock the findOne method to throw an error
      const originalFindOne = EbanxWhitelist.findOne;
      EbanxWhitelist.findOne = jest.fn().mockImplementation(() => {
        throw new Error('Database error');
      });

      const result = await isProductWhitelisted(productId);

      expect(result).toBe(false);
      expect(logger.error).toHaveBeenCalledWith(
        'EBANX Whitelist: Error getting whitelist entry:',
        expect.any(Error)
      );

      // Restore original method
      EbanxWhitelist.findOne = originalFindOne;
    });
  });

  describe('getProductIdFromTransaction', () => {
    describe('Subscription transactions', () => {
      it('should return community ObjectId for subscription transaction', async () => {
        const communityId = new mongoose.Types.ObjectId();
        const communityCode = 'test-community';

        // Mock the community findOne method
        CommunityModel.findOne.mockReturnValue({
          lean: jest.fn().mockResolvedValue({
            _id: communityId,
            code: communityCode,
          }),
        });

        const transaction = {
          community_code: communityCode,
        };

        const result = await getProductIdFromTransaction(
          transaction,
          PURCHASE_TYPE.SUBSCRIPTION
        );

        expect(result.toString()).toBe(communityId.toString());
        expect(CommunityModel.findOne).toHaveBeenCalledWith({
          code: communityCode,
        });
      });

      it('should return null and log error when community_code is missing', async () => {
        const transaction = {};

        const result = await getProductIdFromTransaction(
          transaction,
          PURCHASE_TYPE.SUBSCRIPTION
        );

        expect(result).toBeNull();
        expect(logger.error).toHaveBeenCalledWith(
          'EBANX Whitelist: community_code not found in subscription transaction'
        );
      });

      it('should return null and log error when community is not found', async () => {
        const transaction = {
          community_code: 'non-existent-community',
        };

        // Mock the community findOne method to return null
        CommunityModel.findOne.mockReturnValue({
          lean: jest.fn().mockResolvedValue(null),
        });

        const result = await getProductIdFromTransaction(
          transaction,
          PURCHASE_TYPE.SUBSCRIPTION
        );

        expect(result).toBeNull();
        expect(logger.error).toHaveBeenCalledWith(
          'EBANX Whitelist: Community not found for code: non-existent-community'
        );
      });
    });

    describe('Addon transactions', () => {
      it.each([
        ADDON_PURCHASE_TYPES.EVENT,
        ADDON_PURCHASE_TYPES.DIGITAL_FILES,
        ADDON_PURCHASE_TYPES.COURSE,
        ADDON_PURCHASE_TYPES.CHALLENGE,
        ADDON_PURCHASE_TYPES.SESSION,
      ])(
        'should return entityObjectId for %s addon transaction',
        async (purchaseType) => {
          const entityObjectId = new mongoose.Types.ObjectId();
          const transaction = {
            entityObjectId,
          };

          const result = await getProductIdFromTransaction(
            transaction,
            purchaseType
          );

          expect(result.toString()).toBe(entityObjectId.toString());
        }
      );

      it('should return null and log error when entityObjectId is missing', async () => {
        const transaction = {};

        const result = await getProductIdFromTransaction(
          transaction,
          ADDON_PURCHASE_TYPES.COURSE
        );

        expect(result).toBeNull();
        expect(logger.error).toHaveBeenCalledWith(
          'EBANX Whitelist: entityObjectId not found in addon transaction'
        );
      });
    });

    describe('Plan transactions', () => {
      it.each(Object.values(PLAN_ENTITY_TYPE))(
        'should return planObjectId for %s plan transaction',
        async (entityType) => {
          const planObjectId = new mongoose.Types.ObjectId();
          const transaction = {
            planObjectId,
          };

          const result = await getProductIdFromTransaction(
            transaction,
            entityType
          );

          expect(result.toString()).toBe(planObjectId.toString());
        }
      );

      it('should return null and log error when planObjectId is missing', async () => {
        const transaction = {};

        const result = await getProductIdFromTransaction(
          transaction,
          Object.values(PLAN_ENTITY_TYPE)[0]
        );

        expect(result).toBeNull();
        expect(logger.error).toHaveBeenCalledWith(
          'EBANX Whitelist: planObjectId not found in plan transaction'
        );
      });
    });

    describe('Invalid inputs', () => {
      it('should return null and log error when transaction is null', async () => {
        const result = await getProductIdFromTransaction(
          null,
          PURCHASE_TYPE.SUBSCRIPTION
        );

        expect(result).toBeNull();
        expect(logger.error).toHaveBeenCalledWith(
          'EBANX Whitelist: Transaction is required'
        );
      });

      it('should return null and log error when transaction is undefined', async () => {
        const result = await getProductIdFromTransaction(
          undefined,
          PURCHASE_TYPE.SUBSCRIPTION
        );

        expect(result).toBeNull();
        expect(logger.error).toHaveBeenCalledWith(
          'EBANX Whitelist: Transaction is required'
        );
      });

      it('should return null and log error for unsupported purchase type', async () => {
        const transaction = {
          someField: 'value',
        };

        const result = await getProductIdFromTransaction(
          transaction,
          'UNSUPPORTED_TYPE'
        );

        expect(result).toBeNull();
        expect(logger.error).toHaveBeenCalledWith(
          'EBANX Whitelist: Unsupported purchase type: UNSUPPORTED_TYPE'
        );
      });

      it('should return null and log error when database error occurs', async () => {
        const transaction = {
          community_code: 'test-community',
        };

        // Mock the community findOne method to throw an error
        CommunityModel.findOne.mockReturnValue({
          lean: jest.fn().mockRejectedValue(new Error('Database error')),
        });

        const result = await getProductIdFromTransaction(
          transaction,
          PURCHASE_TYPE.SUBSCRIPTION
        );

        expect(result).toBeNull();
        expect(logger.error).toHaveBeenCalledWith(
          'EBANX Whitelist: Error in getProductIdFromTransaction:',
          expect.any(Error)
        );
      });
    });
  });

  describe('WHITELIST_STATUS constants', () => {
    it('should have correct status values', () => {
      expect(WHITELIST_STATUS.ACTIVE).toBe('ACTIVE');
      expect(WHITELIST_STATUS.INACTIVE).toBe('INACTIVE');
    });
  });

  describe('Integration with real data', () => {
    it('should work end-to-end with real database operations', async () => {
      const productId = new mongoose.Types.ObjectId();

      // First check should return false (not whitelisted)
      let result = await isProductWhitelisted(productId);
      expect(result).toBe(false);

      // Create whitelist entry
      await EbanxWhitelist.create({
        productId,
        productType: 'COURSE',
        productLink: 'https://example.com/course/123',
        communityCode: 'test-community',
        status: WHITELIST_STATUS.ACTIVE,
        reason: 'Test reason',
        whitelistedBy: '<EMAIL>',
      });

      // Now should return true
      result = await isProductWhitelisted(productId);
      expect(result).toBe(true);

      // Get the entry directly
      const entry = await getWhitelistEntry(productId);
      expect(entry).toBeTruthy();
      expect(entry.status).toBe(WHITELIST_STATUS.ACTIVE);
    });
  });
});
