const urlParserService = require('../../../../src/services/ebanx-whitelist/url-parser.service');
const { PRODUCT_TYPE } = require('../../../../src/services/product/constants');
const CommunityProduct = require('../../../../src/models/product/communityProduct.model');
const Community = require('../../../../src/communitiesAPI/models/community.model');

// Mock dependencies
jest.mock('../../../../src/models/product/communityProduct.model');
jest.mock('../../../../src/communitiesAPI/models/community.model');

// Mock ToUserError
jest.mock('../../../../src/utils/error.util', () => ({
  ToUserError: jest.fn().mockImplementation((message) => {
    const error = new Error(message);
    error.name = 'ToUserError';
    return error;
  }),
}));

describe('URL Parser Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('parseProductUrl', () => {
    describe('Valid URL parsing', () => {
      it('should parse products URL correctly', async () => {
        const productLink = '/grandfather-2/products/fsdw';
        const mockCommunity = {
          _id: '507f1f77bcf86cd799439000',
          code: 'GRANDFATHER_2',
        };
        const mockProduct = {
          _id: '507f1f77bcf86cd799439011',
          entityObjectId: '607f1f77bcf86cd799439011',
        };

        Community.findOne.mockReturnValue({
          select: jest.fn().mockResolvedValue(mockCommunity),
        });
        CommunityProduct.findOne.mockReturnValue({
          select: jest.fn().mockResolvedValue(mockProduct),
        });

        const result = await urlParserService.parseProductUrl(productLink);

        expect(Community.findOne).toHaveBeenCalledWith({
          link: '/grandfather-2',
        });
        expect(CommunityProduct.findOne).toHaveBeenCalledWith({
          communityObjectId: mockCommunity._id,
          slug: '/fsdw',
        });
        expect(result).toEqual({
          productId: '607f1f77bcf86cd799439011',
          communityCode: 'GRANDFATHER_2',
          productType: PRODUCT_TYPE.DIGITAL_FILES,
        });
      });

      it('should parse digital-files URL correctly', async () => {
        const productLink = '/399-401-members.-addremove-within-range/digital-files/frxf';
        const mockCommunity = {
          _id: '507f1f77bcf86cd799439001',
          code: 'COMMUNITY_401',
        };
        const mockProduct = {
          _id: '507f1f77bcf86cd799439012',
          entityObjectId: '607f1f77bcf86cd799439012',
        };

        Community.findOne.mockReturnValue({
          select: jest.fn().mockResolvedValue(mockCommunity),
        });
        CommunityProduct.findOne.mockReturnValue({
          select: jest.fn().mockResolvedValue(mockProduct),
        });

        const result = await urlParserService.parseProductUrl(productLink);

        expect(Community.findOne).toHaveBeenCalledWith({
          link: '/399-401-members.-addremove-within-range',
        });
        expect(CommunityProduct.findOne).toHaveBeenCalledWith({
          communityObjectId: mockCommunity._id,
          slug: '/frxf',
        });
        expect(result).toEqual({
          productId: '607f1f77bcf86cd799439012',
          communityCode: 'COMMUNITY_401',
          productType: PRODUCT_TYPE.DIGITAL_FILES,
        });
      });

      it('should parse challenges URL correctly', async () => {
        const productLink = '/pro-54/challenges/boost-your-online-presence-in-28-days';
        const mockCommunity = {
          _id: '507f1f77bcf86cd799439002',
          code: 'PRO_54',
        };
        const mockProduct = {
          _id: '507f1f77bcf86cd799439013',
          entityObjectId: '607f1f77bcf86cd799439013',
        };

        Community.findOne.mockReturnValue({
          select: jest.fn().mockResolvedValue(mockCommunity),
        });
        CommunityProduct.findOne.mockReturnValue({
          select: jest.fn().mockResolvedValue(mockProduct),
        });

        const result = await urlParserService.parseProductUrl(productLink);

        expect(Community.findOne).toHaveBeenCalledWith({
          link: '/pro-54',
        });
        expect(CommunityProduct.findOne).toHaveBeenCalledWith({
          communityObjectId: mockCommunity._id,
          slug: '/boost-your-online-presence-in-28-days',
        });
        expect(result).toEqual({
          productId: '607f1f77bcf86cd799439013',
          communityCode: 'PRO_54',
          productType: PRODUCT_TYPE.CHALLENGE,
        });
      });

      it('should parse events URL correctly', async () => {
        const productLink = '/test-community/events/event-slug';
        const mockCommunity = {
          _id: '507f1f77bcf86cd799439003',
          code: 'TEST_COMMUNITY',
        };
        const mockProduct = {
          _id: '507f1f77bcf86cd799439014',
          entityObjectId: '607f1f77bcf86cd799439014',
        };

        Community.findOne.mockReturnValue({
          select: jest.fn().mockResolvedValue(mockCommunity),
        });
        CommunityProduct.findOne.mockReturnValue({
          select: jest.fn().mockResolvedValue(mockProduct),
        });

        const result = await urlParserService.parseProductUrl(productLink);

        expect(Community.findOne).toHaveBeenCalledWith({
          link: '/test-community',
        });
        expect(CommunityProduct.findOne).toHaveBeenCalledWith({
          communityObjectId: mockCommunity._id,
          slug: '/event-slug',
        });
        expect(result).toEqual({
          productId: '607f1f77bcf86cd799439014',
          communityCode: 'TEST_COMMUNITY',
          productType: PRODUCT_TYPE.EVENT,
        });
      });

      it('should parse sessions URL correctly', async () => {
        const productLink = '/test-community/sessions/session-slug';
        const mockCommunity = {
          _id: '507f1f77bcf86cd799439004',
          code: 'TEST_COMMUNITY',
        };
        const mockProduct = {
          _id: '507f1f77bcf86cd799439015',
          entityObjectId: '607f1f77bcf86cd799439015',
        };

        Community.findOne.mockReturnValue({
          select: jest.fn().mockResolvedValue(mockCommunity),
        });
        CommunityProduct.findOne.mockReturnValue({
          select: jest.fn().mockResolvedValue(mockProduct),
        });

        const result = await urlParserService.parseProductUrl(productLink);

        expect(Community.findOne).toHaveBeenCalledWith({
          link: '/test-community',
        });
        expect(CommunityProduct.findOne).toHaveBeenCalledWith({
          communityObjectId: mockCommunity._id,
          slug: '/session-slug',
        });
        expect(result).toEqual({
          productId: '607f1f77bcf86cd799439015',
          communityCode: 'TEST_COMMUNITY',
          productType: PRODUCT_TYPE.SESSION,
        });
      });

      it('should parse courses URL correctly', async () => {
        const productLink = '/test-community/courses/course-slug';
        const mockCommunity = {
          _id: '507f1f77bcf86cd799439005',
          code: 'TEST_COMMUNITY',
        };
        const mockProduct = {
          _id: '507f1f77bcf86cd799439016',
          entityObjectId: '607f1f77bcf86cd799439016',
        };

        Community.findOne.mockReturnValue({
          select: jest.fn().mockResolvedValue(mockCommunity),
        });
        CommunityProduct.findOne.mockReturnValue({
          select: jest.fn().mockResolvedValue(mockProduct),
        });

        const result = await urlParserService.parseProductUrl(productLink);

        expect(Community.findOne).toHaveBeenCalledWith({
          link: '/test-community',
        });
        expect(CommunityProduct.findOne).toHaveBeenCalledWith({
          communityObjectId: mockCommunity._id,
          slug: '/course-slug',
        });
        expect(result).toEqual({
          productId: '607f1f77bcf86cd799439016',
          communityCode: 'TEST_COMMUNITY',
          productType: PRODUCT_TYPE.COURSE,
        });
      });

      it('should handle URLs with leading/trailing slashes', async () => {
        const productLink = '//test-community/products/product-slug//';
        const mockCommunity = {
          _id: '507f1f77bcf86cd799439006',
          code: 'TEST_COMMUNITY',
        };
        const mockProduct = {
          _id: '507f1f77bcf86cd799439017',
          entityObjectId: '607f1f77bcf86cd799439017',
        };

        Community.findOne.mockReturnValue({
          select: jest.fn().mockResolvedValue(mockCommunity),
        });
        CommunityProduct.findOne.mockReturnValue({
          select: jest.fn().mockResolvedValue(mockProduct),
        });

        const result = await urlParserService.parseProductUrl(productLink);

        expect(Community.findOne).toHaveBeenCalledWith({
          link: '/test-community',
        });
        expect(CommunityProduct.findOne).toHaveBeenCalledWith({
          communityObjectId: mockCommunity._id,
          slug: '/product-slug',
        });
        expect(result).toEqual({
          productId: '607f1f77bcf86cd799439017',
          communityCode: 'TEST_COMMUNITY',
          productType: PRODUCT_TYPE.DIGITAL_FILES,
        });
      });
    });

    describe('Invalid URL format', () => {
      it('should throw error for URL with less than 3 segments', async () => {
        const productLink = '/community/products';
        
        await expect(urlParserService.parseProductUrl(productLink))
          .rejects.toThrow('Invalid product URL format');
      });

      it('should throw error for URL with more than 3 segments', async () => {
        const productLink = '/community/products/slug/extra';
        
        await expect(urlParserService.parseProductUrl(productLink))
          .rejects.toThrow('Invalid product URL format');
      });

      it('should throw error for invalid product type path', async () => {
        const productLink = '/community/invalid-type/slug';
        
        await expect(urlParserService.parseProductUrl(productLink))
          .rejects.toThrow('Invalid product type path');
      });

      it('should throw error for empty URL', async () => {
        const productLink = '';
        
        await expect(urlParserService.parseProductUrl(productLink))
          .rejects.toThrow('Invalid product URL format');
      });

      it('should throw error for only slashes', async () => {
        const productLink = '///';
        
        await expect(urlParserService.parseProductUrl(productLink))
          .rejects.toThrow('Invalid product URL format');
      });
    });

    describe('Product not found', () => {
      it('should throw error when community is not found', async () => {
        const productLink = '/non-existent/products/slug';

        Community.findOne.mockReturnValue({
          select: jest.fn().mockResolvedValue(null),
        });

        await expect(urlParserService.parseProductUrl(productLink))
          .rejects.toThrow('Community not found');

        expect(Community.findOne).toHaveBeenCalledWith({
          link: '/non-existent',
        });
      });

      it('should throw error when product is not found in database', async () => {
        const productLink = '/test-community/products/slug';
        const mockCommunity = {
          _id: '507f1f77bcf86cd799439007',
          code: 'TEST_COMMUNITY',
        };

        Community.findOne.mockReturnValue({
          select: jest.fn().mockResolvedValue(mockCommunity),
        });
        CommunityProduct.findOne.mockReturnValue({
          select: jest.fn().mockResolvedValue(null),
        });

        await expect(urlParserService.parseProductUrl(productLink))
          .rejects.toThrow('Product not found');

        expect(Community.findOne).toHaveBeenCalledWith({
          link: '/test-community',
        });
        expect(CommunityProduct.findOne).toHaveBeenCalledWith({
          communityObjectId: mockCommunity._id,
          slug: '/slug',
        });
      });
    });

    describe('Database query', () => {
      it('should query with correct fields and select only required fields', async () => {
        const productLink = '/community/products/slug';
        const mockCommunity = {
          _id: '507f1f77bcf86cd799439008',
          code: 'COMMUNITY',
        };
        const mockProduct = {
          _id: '507f1f77bcf86cd799439018',
          entityObjectId: '607f1f77bcf86cd799439018',
        };

        const mockCommunitySelect = jest.fn().mockResolvedValue(mockCommunity);
        const mockProductSelect = jest.fn().mockResolvedValue(mockProduct);
        
        Community.findOne.mockReturnValue({
          select: mockCommunitySelect,
        });
        CommunityProduct.findOne.mockReturnValue({
          select: mockProductSelect,
        });

        await urlParserService.parseProductUrl(productLink);

        expect(Community.findOne).toHaveBeenCalledWith({
          link: '/community',
        });
        expect(mockCommunitySelect).toHaveBeenCalledWith('_id code');
        expect(CommunityProduct.findOne).toHaveBeenCalledWith({
          communityObjectId: mockCommunity._id,
          slug: '/slug',
        });
        expect(mockProductSelect).toHaveBeenCalledWith('_id entityObjectId');
      });
    });
  });

  describe('URL_PRODUCT_TYPE_MAP', () => {
    it('should map all URL paths to correct PRODUCT_TYPE constants', () => {
      const urlParserModule = require('../../../../src/services/ebanx-whitelist/url-parser.service');
      
      // Access the mapping through a test of each path
      const testCases = [
        { path: 'products', expected: PRODUCT_TYPE.DIGITAL_FILES },
        { path: 'digital-files', expected: PRODUCT_TYPE.DIGITAL_FILES },
        { path: 'events', expected: PRODUCT_TYPE.EVENT },
        { path: 'sessions', expected: PRODUCT_TYPE.SESSION },
        { path: 'challenges', expected: PRODUCT_TYPE.CHALLENGE },
        { path: 'courses', expected: PRODUCT_TYPE.COURSE },
      ];

      testCases.forEach(({ path, expected }) => {
        expect(expected).toBeDefined();
        expect(typeof expected).toBe('string');
      });
    });
  });
});