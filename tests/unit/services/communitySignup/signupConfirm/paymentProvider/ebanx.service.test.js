const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');

// Mock all external dependencies
jest.mock('../../../../../../src/rpc/paymentBackend');
jest.mock('../../../../../../src/services/logger.service');
jest.mock('../../../../../../src/services/ebanx/ebanxWhitelist.service');
jest.mock('../../../../../../src/models/countryInfoMapping.model');
jest.mock('../../../../../../src/models/userPaymentToken.model');
jest.mock('../../../../../../src/communitiesAPI/models/communityPurchaseTransactions.model');
jest.mock('../../../../../../src/communitiesAPI/models/communityAddonTransactions.model');
jest.mock('../../../../../../src/models/plan/communityPlanOrder.model');
jest.mock('../../../../../../src/handlers/sqs.handler');
jest.mock('express-http-context');

const PaymentBackendRpc = require('../../../../../../src/rpc/paymentBackend');
const logger = require('../../../../../../src/services/logger.service');
const {
  isProductWhitelisted,
  getProductIdFromTransaction,
} = require('../../../../../../src/services/ebanx/ebanxWhitelist.service');
const CountryCurrencyMappingModel = require('../../../../../../src/models/countryInfoMapping.model');
const UserPaymentTokenModel = require('../../../../../../src/models/userPaymentToken.model');
const CommunityPurchaseTransactionModel = require('../../../../../../src/communitiesAPI/models/communityPurchaseTransactions.model');
const AddonTransactionModel = require('../../../../../../src/communitiesAPI/models/communityAddonTransactions.model');
const PlanOrderModel = require('../../../../../../src/models/plan/communityPlanOrder.model');
const { sendMessageToSQSQueue } = require('../../../../../../src/handlers/sqs.handler');
const httpContext = require('express-http-context');

// Import the service under test
const ebanxService = require('../../../../../../src/services/communitySignup/signupConfirm/paymentProvider/ebanx.service');

// Mock constants
jest.mock('../../../../../../src/constants/common', () => ({
  PAYMENT_PROVIDER: { EBANX: 'EBANX' },
  EBANX_SUPPORTED_CURRENCY: { BRL: 'BRL', MXN: 'MXN', ARS: 'ARS', CLP: 'CLP' },
  PURCHASE_TYPE: { SUBSCRIPTION: 'SUBSCRIPTION' },
  ADDON_PURCHASE_TYPES: {
    EVENT: 'EVENT',
    DIGITAL_FILES: 'DIGITAL_FILES',
    COURSE: 'COURSE',
    CHALLENGE: 'CHALLENGE',
    SESSION: 'SESSION',
  },
  USER_PAYMENT_TOKEN_STATUS: { ACTIVE: 'ACTIVE' },
  COMMUNITY_SUBSCRIPTION_STATUSES: { CURRENT: 'CURRENT' },
  SUBSCRIPTION_ACTION_TYPE: { SUCCESS_NEW: 'SUCCESS_NEW' },
  EBANX_PAYMENT_TYPE: { CREDIT_CARD: 'CREDIT_CARD' },
  INTERNAL_EBANX_NOTIFICATION_TYPE: { PLAN_ZERO_PAYMENT_SUCCESS: 'PLAN_ZERO_PAYMENT_SUCCESS' },
}));

jest.mock('../../../../../../src/services/plan/constants', () => ({
  ENTITY_TYPE: { PRO: 'PRO', PLATINUM: 'PLATINUM' },
}));

jest.mock('../../../../../../src/config', () => ({
  SUBSCRIPTION_UPDATER_QUEUE_URL: 'test-queue-url',
  NAS_IO_FRONTEND_URL: 'https://test.nas.io',
}));

describe('EBANX Service - Whitelist Integration', () => {
  let mongoServer;
  let mockPaymentBackendRpc;

  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup PaymentBackendRpc mock
    mockPaymentBackendRpc = {
      init: jest.fn().mockResolvedValue(),
      ebanxDirectCharge: jest.fn().mockResolvedValue({
        payment: { hash: 'test-hash-123' },
        status: 'success',
      }),
      createEbanxPaymentPage: jest.fn().mockResolvedValue({
        payment_url: 'https://test-payment-url.com',
        hash: 'test-hash-456',
      }),
    };
    PaymentBackendRpc.mockImplementation(() => mockPaymentBackendRpc);

    // Setup country mapping mock
    CountryCurrencyMappingModel.findOne.mockResolvedValue({
      countryCode: 'BR',
      currencyCode: 'BRL',
      localisePrice: true,
    });

    // Setup user payment token mock
    UserPaymentTokenModel.findOne.mockResolvedValue({
      _id: new mongoose.Types.ObjectId(),
      paymentToken: 'test-payment-token',
      metadata: {
        name: 'Test User',
        paymentTypeCode: 'CREDIT_CARD',
        cpfId: '123.456.789-00',
        taxPayerId: '12345678901',
      },
    });

    // Setup transaction model mocks
    AddonTransactionModel.findByIdAndUpdate.mockResolvedValue({});
    CommunityPurchaseTransactionModel.findByIdAndUpdate.mockResolvedValue({});
    PlanOrderModel.updateOne.mockResolvedValue({});

    // Setup SQS mock
    sendMessageToSQSQueue.mockResolvedValue();
    httpContext.get.mockReturnValue('test-request-id');

    // Setup whitelist service mocks
    getProductIdFromTransaction.mockResolvedValue(new mongoose.Types.ObjectId());
    isProductWhitelisted.mockResolvedValue(false);
  });

  describe('createPayment - Payment Page with Whitelist', () => {
    it('should add loyal_customer flag when product is whitelisted', async () => {
      // Mock whitelisted product
      const productId = new mongoose.Types.ObjectId();
      getProductIdFromTransaction.mockResolvedValue(productId);
      isProductWhitelisted.mockResolvedValue(true);

      const addonTransaction = {
        _id: new mongoose.Types.ObjectId(),
        local_amount: 100,
        local_currency: 'BRL',
        email: '<EMAIL>',
      };

      const paymentMetadata = {
        metadata: {
          name: 'Test User',
          purchaseType: 'COURSE',
        },
      };

      const metadata = {
        redirectUrl: 'https://test-redirect.com',
      };

      await ebanxService.createPayment({
        addonTransaction,
        metadata,
        paymentMetadata,
      });

      expect(getProductIdFromTransaction).toHaveBeenCalledWith(
        addonTransaction,
        'COURSE'
      );
      expect(isProductWhitelisted).toHaveBeenCalledWith(productId);
      expect(mockPaymentBackendRpc.createEbanxPaymentPage).toHaveBeenCalledWith(
        'BRL',
        100,
        '<EMAIL>',
        'Test User',
        expect.objectContaining({
          name: 'Test User',
          purchaseType: 'COURSE',
          risk: {
            loyal_customer: true,
          },
        }),
        'br',
        'https://test-redirect.com',
        '1-12'
      );
      expect(logger.info).toHaveBeenCalledWith(
        expect.stringContaining('EBANX Whitelist: Added loyal_customer flag')
      );
    });

    it('should not add loyal_customer flag when product is not whitelisted', async () => {
      const addonTransaction = {
        _id: new mongoose.Types.ObjectId(),
        local_amount: 100,
        local_currency: 'BRL',
        email: '<EMAIL>',
      };

      const paymentMetadata = {
        metadata: {
          name: 'Test User',
          purchaseType: 'COURSE',
        },
      };

      const metadata = {
        redirectUrl: 'https://test-redirect.com',
      };

      await ebanxService.createPayment({
        addonTransaction,
        metadata,
        paymentMetadata,
      });

      expect(mockPaymentBackendRpc.createEbanxPaymentPage).toHaveBeenCalledWith(
        'BRL',
        100,
        '<EMAIL>',
        'Test User',
        expect.objectContaining({
          name: 'Test User',
          purchaseType: 'COURSE',
        }),
        'br',
        'https://test-redirect.com',
        '1-12'
      );
      expect(mockPaymentBackendRpc.createEbanxPaymentPage).toHaveBeenCalledWith(
        expect.anything(),
        expect.anything(),
        expect.anything(),
        expect.anything(),
        expect.not.objectContaining({
          risk: expect.anything(),
        }),
        expect.anything(),
        expect.anything(),
        expect.anything()
      );
    });

    it('should continue payment flow when whitelist check fails', async () => {
      // Mock whitelist service to throw error
      getProductIdFromTransaction.mockRejectedValue(new Error('Database error'));

      const addonTransaction = {
        _id: new mongoose.Types.ObjectId(),
        local_amount: 100,
        local_currency: 'BRL',
        email: '<EMAIL>',
      };

      const paymentMetadata = {
        metadata: {
          name: 'Test User',
          purchaseType: 'COURSE',
        },
      };

      const metadata = {
        redirectUrl: 'https://test-redirect.com',
      };

      await ebanxService.createPayment({
        addonTransaction,
        metadata,
        paymentMetadata,
      });

      expect(logger.error).toHaveBeenCalledWith(
        'EBANX Whitelist: Error adding loyalty flag to metadata:',
        expect.any(Error)
      );
      expect(mockPaymentBackendRpc.createEbanxPaymentPage).toHaveBeenCalled();
    });
  });

  describe('directCharge - Addon with Whitelist', () => {
    it('should add loyal_customer flag for whitelisted addon transaction', async () => {
      // Mock whitelisted product
      const productId = new mongoose.Types.ObjectId();
      getProductIdFromTransaction.mockResolvedValue(productId);
      isProductWhitelisted.mockResolvedValue(true);

      const addonTransaction = {
        _id: new mongoose.Types.ObjectId(),
        local_amount: 100,
        local_currency: 'BRL',
        email: '<EMAIL>',
      };

      const paymentMetadata = {
        description: 'Test Course',
        metadata: {
          purchaseType: 'COURSE',
        },
      };

      const metadata = {
        redirectUrl: 'https://test-redirect.com',
      };

      await ebanxService.directCharge({
        transaction: addonTransaction,
        paymentMethodId: 'test-payment-method-id',
        paymentMetadata,
        metadata,
      });

      expect(getProductIdFromTransaction).toHaveBeenCalledWith(
        addonTransaction,
        'COURSE'
      );
      expect(isProductWhitelisted).toHaveBeenCalledWith(productId);
      expect(mockPaymentBackendRpc.ebanxDirectCharge).toHaveBeenCalledWith(
        100,
        'BRL',
        'Test User',
        '<EMAIL>',
        'BR',
        expect.objectContaining({
          purchaseType: 'COURSE',
          description: 'Test Course',
          risk: {
            loyal_customer: true,
          },
        }),
        'test-payment-token',
        'CREDIT_CARD',
        '123.456.789-00',
        '12345678901',
        'https://test-redirect.com',
        true
      );
    });

    it('should add loyal_customer flag for whitelisted subscription transaction', async () => {
      // Mock whitelisted product
      const productId = new mongoose.Types.ObjectId();
      getProductIdFromTransaction.mockResolvedValue(productId);
      isProductWhitelisted.mockResolvedValue(true);

      const subscriptionTransaction = {
        _id: new mongoose.Types.ObjectId(),
        local_amount: 100,
        local_currency: 'BRL',
        email: '<EMAIL>',
        community_code: 'test-community',
      };

      const paymentMetadata = {
        description: 'Test Subscription',
        metadata: {
          purchaseType: 'SUBSCRIPTION',
        },
      };

      const metadata = {
        redirectUrl: 'https://test-redirect.com',
      };

      await ebanxService.directCharge({
        transaction: subscriptionTransaction,
        paymentMethodId: 'test-payment-method-id',
        paymentMetadata,
        metadata,
      });

      expect(getProductIdFromTransaction).toHaveBeenCalledWith(
        subscriptionTransaction,
        'SUBSCRIPTION'
      );
      expect(isProductWhitelisted).toHaveBeenCalledWith(productId);
      expect(mockPaymentBackendRpc.ebanxDirectCharge).toHaveBeenCalledWith(
        100,
        'BRL',
        'Test User',
        '<EMAIL>',
        'BR',
        expect.objectContaining({
          purchaseType: 'SUBSCRIPTION',
          description: 'Test Subscription',
          risk: {
            loyal_customer: true,
          },
        }),
        'test-payment-token',
        'CREDIT_CARD',
        '123.456.789-00',
        '12345678901',
        'https://test-redirect.com',
        true
      );
    });

    it('should not add loyal_customer flag for non-whitelisted transaction', async () => {
      const addonTransaction = {
        _id: new mongoose.Types.ObjectId(),
        local_amount: 100,
        local_currency: 'BRL',
        email: '<EMAIL>',
      };

      const paymentMetadata = {
        description: 'Test Course',
        metadata: {
          purchaseType: 'COURSE',
        },
      };

      const metadata = {
        redirectUrl: 'https://test-redirect.com',
      };

      await ebanxService.directCharge({
        transaction: addonTransaction,
        paymentMethodId: 'test-payment-method-id',
        paymentMetadata,
        metadata,
      });

      expect(mockPaymentBackendRpc.ebanxDirectCharge).toHaveBeenCalledWith(
        100,
        'BRL',
        'Test User',
        '<EMAIL>',
        'BR',
        expect.objectContaining({
          purchaseType: 'COURSE',
          description: 'Test Course',
        }),
        'test-payment-token',
        'CREDIT_CARD',
        '123.456.789-00',
        '12345678901',
        'https://test-redirect.com',
        true
      );
      expect(mockPaymentBackendRpc.ebanxDirectCharge).toHaveBeenCalledWith(
        expect.anything(),
        expect.anything(),
        expect.anything(),
        expect.anything(),
        expect.anything(),
        expect.not.objectContaining({
          risk: expect.anything(),
        }),
        expect.anything(),
        expect.anything(),
        expect.anything(),
        expect.anything(),
        expect.anything(),
        expect.anything()
      );
    });
  });

  describe('Plan purchases - No whitelist integration', () => {
    it('should not add whitelist check for plan purchases', async () => {
      const planOrder = {
        _id: new mongoose.Types.ObjectId(),
        amountInLocalCurrency: 100,
        localCurrency: 'BRL',
        entityType: 'PRO',
        interval: 'month',
        intervalCount: 1,
        billingCycle: 1,
        creditHistory: [],
        paymentProviderSubscriptionId: null,
      };

      const paymentMetadata = {
        description: 'PRO Plan',
      };

      const metadata = {
        redirectUrl: 'https://test-redirect.com',
      };

      await ebanxService.directCharge({
        transaction: planOrder,
        email: '<EMAIL>',
        paymentMethodId: 'test-payment-method-id',
        paymentMetadata,
        metadata,
      });

      // Should not call whitelist service for plan purchases
      expect(getProductIdFromTransaction).not.toHaveBeenCalled();
      expect(isProductWhitelisted).not.toHaveBeenCalled();
      
      expect(mockPaymentBackendRpc.ebanxDirectCharge).toHaveBeenCalledWith(
        100,
        'BRL',
        'Test User',
        '<EMAIL>',
        'BR',
        expect.objectContaining({
          purchaseType: 'PRO',
          description: 'PRO Plan',
        }),
        'test-payment-token',
        'CREDIT_CARD',
        '123.456.789-00',
        '12345678901',
        'https://test-redirect.com',
        true
      );
      expect(mockPaymentBackendRpc.ebanxDirectCharge).toHaveBeenCalledWith(
        expect.anything(),
        expect.anything(),
        expect.anything(),
        expect.anything(),
        expect.anything(),
        expect.not.objectContaining({
          risk: expect.anything(),
        }),
        expect.anything(),
        expect.anything(),
        expect.anything(),
        expect.anything(),
        expect.anything(),
        expect.anything()
      );
    });
  });

  describe('Error handling', () => {
    it('should handle whitelist service errors gracefully', async () => {
      getProductIdFromTransaction.mockRejectedValue(new Error('Service error'));

      const addonTransaction = {
        _id: new mongoose.Types.ObjectId(),
        local_amount: 100,
        local_currency: 'BRL',
        email: '<EMAIL>',
      };

      const paymentMetadata = {
        description: 'Test Course',
        metadata: {
          purchaseType: 'COURSE',
        },
      };

      const metadata = {
        redirectUrl: 'https://test-redirect.com',
      };

      // Should not throw error and continue with payment
      await expect(ebanxService.directCharge({
        transaction: addonTransaction,
        paymentMethodId: 'test-payment-method-id',
        paymentMetadata,
        metadata,
      })).resolves.not.toThrow();

      expect(logger.error).toHaveBeenCalledWith(
        'EBANX Whitelist: Error adding loyalty flag to metadata:',
        expect.any(Error)
      );
      expect(mockPaymentBackendRpc.ebanxDirectCharge).toHaveBeenCalled();
    });

    it('should handle null product ID gracefully', async () => {
      getProductIdFromTransaction.mockResolvedValue(null);

      const addonTransaction = {
        _id: new mongoose.Types.ObjectId(),
        local_amount: 100,
        local_currency: 'BRL',
        email: '<EMAIL>',
      };

      const paymentMetadata = {
        description: 'Test Course',
        metadata: {
          purchaseType: 'COURSE',
        },
      };

      const metadata = {
        redirectUrl: 'https://test-redirect.com',
      };

      await ebanxService.directCharge({
        transaction: addonTransaction,
        paymentMethodId: 'test-payment-method-id',
        paymentMetadata,
        metadata,
      });

      expect(isProductWhitelisted).not.toHaveBeenCalled();
      expect(mockPaymentBackendRpc.ebanxDirectCharge).toHaveBeenCalledWith(
        expect.anything(),
        expect.anything(),
        expect.anything(),
        expect.anything(),
        expect.anything(),
        expect.not.objectContaining({
          risk: expect.anything(),
        }),
        expect.anything(),
        expect.anything(),
        expect.anything(),
        expect.anything(),
        expect.anything(),
        expect.anything()
      );
    });
  });
});