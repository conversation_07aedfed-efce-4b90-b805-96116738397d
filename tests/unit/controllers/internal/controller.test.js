const {
  generateIssueDescription,
  generateAIResponse
} = require('../../../../src/api/v1/internal/controller');
const { askAQuestionToProductFeedbackBot } = require('../../../../src/clients/openai.client');
const larkService = require('../../../../src/communitiesAPI/services/common/lark.service');

// Mock the dependencies
jest.mock('../../../../src/clients/openai.client');
jest.mock('../../../../src/communitiesAPI/services/common/lark.service');

describe('generateIssueDescription', () => {
  const MESSAGE_TYPE_POST = 'post';
  const NON_POST_TYPE = 'text';

  describe('non-POST message type', () => {
    it('should return text content directly for non-POST messages', () => {
      const messageContent = { text: 'Simple text message' };
      expect(generateIssueDescription(NON_POST_TYPE, messageContent)).toBe(
        'Simple text message'
      );
    });

    it('should handle empty text content', () => {
      const messageContent = { text: '' };
      expect(generateIssueDescription(NON_POST_TYPE, messageContent)).toBe(
        ''
      );
    });
  });

  describe('POST message type', () => {
    it('should handle empty content array', () => {
      const messageContent = { content: [] };
      expect(
        generateIssueDescription(MESSAGE_TYPE_POST, messageContent)
      ).toBe('');
    });

    it('should handle text tags', () => {
      const messageContent = {
        content: [
          [
            { tag: 'text', text: 'Hello' },
            { tag: 'text', text: 'World' },
          ],
        ],
      };
      expect(
        generateIssueDescription(MESSAGE_TYPE_POST, messageContent)
      ).toBe('Hello World');
    });

    it('should handle link (a) tags', () => {
      const messageContent = {
        content: [
          [
            { tag: 'text', text: 'Visit' },
            { tag: 'a', href: 'https://example.com', text: 'Example' },
          ],
        ],
      };
      expect(
        generateIssueDescription(MESSAGE_TYPE_POST, messageContent)
      ).toBe('Visit https://example.com');
    });

    it('should handle markdown tags', () => {
      const messageContent = {
        content: [
          [{ tag: 'md', text: '**Bold text**\nNew line\nAnother line' }],
        ],
      };
      expect(
        generateIssueDescription(MESSAGE_TYPE_POST, messageContent)
      ).toBe('**Bold text**. New line. Another line');
    });

    it('should ignore unsupported tags', () => {
      const messageContent = {
        content: [
          [
            { tag: 'text', text: 'Start' },
            { tag: 'img', image_key: 'abc123' },
            { tag: 'emotion', emoji_type: 'SMILE' },
            { tag: 'text', text: 'End' },
          ],
        ],
      };
      expect(
        generateIssueDescription(MESSAGE_TYPE_POST, messageContent)
      ).toBe('Start End');
    });

    it('should handle complex nested content', () => {
      const messageContent = {
        content: [
          [
            { tag: 'text', text: 'First line:' },
            { tag: 'a', href: 'https://example.com', text: 'Link' },
          ],
          [{ tag: 'text', text: 'Second line' }],
          [{ tag: 'md', text: '**Bold**\nNew line' }],
        ],
      };
      expect(
        generateIssueDescription(MESSAGE_TYPE_POST, messageContent)
      ).toBe(
        'First line: https://example.com Second line **Bold**. New line'
      );
    });

    it('should handle multiple consecutive spaces', () => {
      const messageContent = {
        content: [
          [
            { tag: 'text', text: 'Too    many    spaces' },
            { tag: 'text', text: '   here   too   ' },
          ],
        ],
      };
      expect(
        generateIssueDescription(MESSAGE_TYPE_POST, messageContent)
      ).toBe('Too many spaces here too');
    });

    it('should handle empty blocks and items', () => {
      const messageContent = {
        content: [
          [],
          [{ tag: 'text', text: 'Content' }],
          [{ tag: 'unknown' }],
          [{ tag: 'text', text: '' }],
        ],
      };
      expect(
        generateIssueDescription(MESSAGE_TYPE_POST, messageContent)
      ).toBe('Content');
    });

    it('should use a real sample text', () => {
      const messageContent = {
        content: [
          [
            {
              tag: 'text',
              text: 'Hi Team! Need help to check why payment was not posted for this user:',
              style: [],
            },
          ],
          [
            {
              tag: 'img',
              image_key:
                'img_v3_02k8_3df3404c-fd66-4c2e-aab2-1693369ef81h',
              width: 1000,
              height: 2048,
            },
          ],
          [],
          [
            {
              tag: 'a',
              href: '<EMAIL>',
              text: '<EMAIL>',
              style: [],
            },
          ],
          [{ tag: 'text', text: 'THE_BUDGETARIAN_BRIDE_15', style: [] }],
          [
            { tag: 'text', text: 'cc ', style: [] },
            {
              tag: 'at',
              user_id: '@_user_1',
              user_name: 'Aman Minhas (online)',
              style: [],
            },
            { tag: 'text', text: ' ', style: [] },
            {
              tag: 'at',
              user_id: '@_user_2',
              user_name: 'Leon',
              style: [],
            },
            { tag: 'text', text: ' ', style: [] },
          ],
          [
            { tag: 'text', text: 'For vis ', style: [] },
            {
              tag: 'at',
              user_id: '@_user_3',
              user_name: 'Jacq Lim',
              style: [],
            },
            { tag: 'text', text: ' ', style: [] },
            {
              tag: 'at',
              user_id: '@_user_4',
              user_name: 'Paavan Singh',
              style: [],
            },
            { tag: 'text', text: ' ', style: [] },
          ],
          [
            {
              tag: 'text',
              text: 'Found Matching transaction in xendit and same time: ',
              style: [],
            },
            {
              tag: 'a',
              href: 'https://dashboard.xendit.co/transactions-new/ew_b5b0b724-ec31-44b3-962e-933f4ae4b37b',
              text: 'https://dashboard.xendit.co/transactions-new/ew_b5b0b724-ec31-44b3-962e-933f4ae4b37b',
              style: [],
            },
          ],
        ],
      };
      expect(
        generateIssueDescription(MESSAGE_TYPE_POST, messageContent)
      ).toBe(
        'Hi Team! Need help to check why payment was not posted for this user: <EMAIL> THE_BUDGETARIAN_BRIDE_15 cc @Aman Minhas (online) @Leon For vis @Jacq Lim @Paavan Singh Found Matching transaction in xendit and same time: https://dashboard.xendit.co/transactions-new/ew_b5b0b724-ec31-44b3-962e-933f4ae4b37b'
      );
    });

    it('should handle community deactivation message', () => {
      const messageContent = {
        content: [
          [
            {
              tag: 'text',
              text: 'Hello, can you please help me check if deactivation of this community will be permanent? ',
              style: [],
            },
          ],
          [],
          [
            { tag: 'text', text: 'CB email: ', style: [] },
            {
              tag: 'a',
              href: '<EMAIL>',
              text: '<EMAIL>',
              style: [],
            },
          ],
          [
            {
              tag: 'text',
              text: 'Community name: Amazon Interview Questions',
              style: [],
            },
          ],
          [
            {
              tag: 'text',
              text: 'Community code: AMAZON_INTERVIEW_QUESTIONS',
              style: [],
            },
          ],
          [],
          [
            {
              tag: 'img',
              image_key:
                'img_v3_02k8_08f82a41-38cf-48e3-9ad5-93b104e87d7h',
              width: 802,
              height: 180,
            },
          ],
          [],
          [
            {
              tag: 'img',
              image_key:
                'img_v3_02k8_b295067d-06c9-4ae2-aa75-8238a88ac32h',
              width: 393,
              height: 470,
            },
          ],
          [],
          [
            {
              tag: 'img',
              image_key:
                'img_v3_02k8_bcd9f074-e50b-4aea-b99c-7e63ce87207h',
              width: 871,
              height: 790,
            },
          ],
          [],
          [],
          [
            {
              tag: 'a',
              href: 'https://applink.larksuite.com/client/message/link/open?token=Amcy7oI3AQAGZ87UFPJAQAw%3D',
              text: 'https://applink.larksuite.com/client/message/link/open?token=Amcy7oI3AQAGZ87UFPJAQAw%3D',
              style: [],
            },
          ],
          [],
          [],
          [
            {
              tag: 'at',
              user_id: '@_user_1',
              user_name: 'Anne Caballero',
              style: [],
            },
            { tag: 'text', text: ' ', style: [] },
            {
              tag: 'at',
              user_id: '@_user_2',
              user_name: 'Patch',
              style: [],
            },
            { tag: 'text', text: '', style: [] },
          ],
        ],
      };
      expect(
        generateIssueDescription(MESSAGE_TYPE_POST, messageContent)
      ).toBe(
        'Hello, can you please help me check if deactivation of this community will be permanent? CB email: <EMAIL> Community name: Amazon Interview Questions Community code: AMAZON_INTERVIEW_QUESTIONS https://applink.larksuite.com/client/message/link/open?token=Amcy7oI3AQAGZ87UFPJAQAw%3D @Anne Caballero @Patch'
      );
    });
  });
});

describe('generateAIResponse', () => {
  const mockTenantAccessToken = 'mock-token';
  const mockMessage = {
    message_id: 'msg123',
    chat_id: 'chat123'
  };
  const mockSender = {
    sender_id: {
      open_id: 'sender123'
    }
  };
  const mockTextContent = 'test question';

  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  it('should handle successful AI response and send Lark reply', async () => {
    // Mock successful AI response
    const mockAiResponse = [{
      content: [{
        text: {
          value: 'AI generated response'
        }
      }]
    }];
    askAQuestionToProductFeedbackBot.mockResolvedValue(mockAiResponse);
    larkService.replyToMessageOnLark.mockResolvedValue({ code: 0 });

    await generateAIResponse(mockTenantAccessToken, mockMessage, mockSender, mockTextContent);

    // Verify AI bot was called with correct parameters
    expect(askAQuestionToProductFeedbackBot).toHaveBeenCalledWith({
      question: mockTextContent
    });

    // Verify Lark reply was sent with correct parameters
    expect(larkService.replyToMessageOnLark).toHaveBeenCalledWith({
      payload: {
        ...mockSender.sender_id,
        chat_id: mockMessage.chat_id,
        receive_id: mockMessage.chat_id,
        msg_type: 'post',
        reply_in_thread: true,
        content: JSON.stringify({
          en_us: {
            title: 'Report Analysis',
            content: [[{
              tag: 'md',
              text: 'AI generated response'
            }]]
          }
        })
      },
      tenantAccessToken: mockTenantAccessToken,
      receiveIdType: 'chat_id',
      messageId: mockMessage.message_id
    });
  });

  it('should handle empty AI response', async () => {
    // Mock empty AI response
    askAQuestionToProductFeedbackBot.mockResolvedValue([]);
    larkService.replyToMessageOnLark.mockResolvedValue({ code: 0 });

    await generateAIResponse(mockTenantAccessToken, mockMessage, mockSender, mockTextContent);

    expect(larkService.replyToMessageOnLark).toHaveBeenCalledWith(
      expect.objectContaining({
        payload: expect.objectContaining({
          content: expect.stringContaining('Unexpected AI response received: []')
        })
      })
    );
  });

  it('should handle AI response with empty content', async () => {
    // Mock AI response with empty content
    const mockAiResponse = [{
      content: []
    }];
    askAQuestionToProductFeedbackBot.mockResolvedValue(mockAiResponse);
    larkService.replyToMessageOnLark.mockResolvedValue({ code: 0 });

    await generateAIResponse(mockTenantAccessToken, mockMessage, mockSender, mockTextContent);

    expect(larkService.replyToMessageOnLark).toHaveBeenCalledWith(
      expect.objectContaining({
        payload: expect.objectContaining({
          content: expect.stringContaining('Unexpected AI response received:')
        })
      })
    );
  });

  it('should handle undefined AI response', async () => {
    // Mock AI service returning undefined
    askAQuestionToProductFeedbackBot.mockResolvedValue(undefined);
    larkService.replyToMessageOnLark.mockResolvedValue({ code: 0 });

    await generateAIResponse(mockTenantAccessToken, mockMessage, mockSender, mockTextContent);

    expect(larkService.replyToMessageOnLark).toHaveBeenCalledWith(
      expect.objectContaining({
        payload: expect.objectContaining({
          content: expect.stringContaining('Unexpected AI response received: undefined')
        })
      })
    );
  });

  it('should handle Lark service error', async () => {
    // Mock successful AI response
    const mockAiResponse = [{
      content: [{
        text: {
          value: 'AI generated response'
        }
      }]
    }];
    askAQuestionToProductFeedbackBot.mockResolvedValue(mockAiResponse);
    // Mock Lark service returning error code
    larkService.replyToMessageOnLark.mockResolvedValue({
      code: 1, // Non-zero code indicates error
      msg: 'Failed to send message'
    });

    await expect(generateAIResponse(
      mockTenantAccessToken,
      mockMessage,
      mockSender,
      mockTextContent
    )).rejects.toThrow('Failed sending lark reply:');
  });
});
