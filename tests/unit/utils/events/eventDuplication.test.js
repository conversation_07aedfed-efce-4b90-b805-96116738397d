const {
  generateDuplicateEvents,
} = require('../../../../src/utils/events/generateDuplicateEvents.util');

const testEvent = {
  _id: '67ca62e48be8930170cec882',
  title: 'test',
  description: 'test',
  startTime: '2025-03-08T10:00:00Z',
  endTime: '2025-03-08T11:00:00Z',
  timeBeforeStartTime: [
    {
      type: 'FIVE_MINS',
      date: {
        $date: '2025-03-08T03:55:00.000Z',
      },
    },
    {
      type: 'ONE_HOUR',
      date: {
        $date: '2025-03-08T03:00:00.000Z',
      },
    },
    {
      type: 'ONE_DAY',
      date: {
        $date: '2025-03-07T04:00:00.000Z',
      },
    },
  ],
  icsFileLink: 'https://dev-nas.io/ce_67ca62e48be8930170cec882',
  shortUrl: 'https://dev-nas.io/test-get-inspired/ogjl',
};

describe('Event Duplication', () => {
  // Helper function to clean event for comparison
  const cleanEventForDisplay = (event) => ({
    title: event.title,
    startTime: event.startTime,
    endTime: event.endTime,
  });

  describe('Simple Frequency Tests', () => {
    test('should generate daily events (every day)', () => {
      const duplicationCriteria = {
        minStartDate: '2025-06-01T00:00:00Z',
        isCustomFrequency: false,
        frequency: 'daily',
        frequencyInterval: 1,
        endCondition: 'occurrence',
        maxOccurrences: 5,
      };

      const expectedOutput = [
        {
          title: 'test',
          startTime: '2025-06-01T10:00:00.000Z',
          endTime: '2025-06-01T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-06-02T10:00:00.000Z',
          endTime: '2025-06-02T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-06-03T10:00:00.000Z',
          endTime: '2025-06-03T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-06-04T10:00:00.000Z',
          endTime: '2025-06-04T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-06-05T10:00:00.000Z',
          endTime: '2025-06-05T11:00:00.000Z',
        },
      ];

      const { events: generatedEvents } = generateDuplicateEvents(
        testEvent,
        duplicationCriteria
      );
      const cleanedEvents = generatedEvents.map(cleanEventForDisplay);
      expect(cleanedEvents).toEqual(expectedOutput);
    });

    test('should generate events every two days until specific date', () => {
      const duplicationCriteria = {
        minStartDate: '2025-06-01T00:00:00Z',
        isCustomFrequency: false,
        frequency: 'daily',
        frequencyInterval: 2,
        endCondition: 'specificDate',
        endDate: '2025-07-10T10:00:00Z',
      };

      const expectedOutput = [
        {
          title: 'test',
          startTime: '2025-06-01T10:00:00.000Z',
          endTime: '2025-06-01T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-06-03T10:00:00.000Z',
          endTime: '2025-06-03T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-06-05T10:00:00.000Z',
          endTime: '2025-06-05T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-06-07T10:00:00.000Z',
          endTime: '2025-06-07T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-06-09T10:00:00.000Z',
          endTime: '2025-06-09T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-06-11T10:00:00.000Z',
          endTime: '2025-06-11T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-06-13T10:00:00.000Z',
          endTime: '2025-06-13T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-06-15T10:00:00.000Z',
          endTime: '2025-06-15T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-06-17T10:00:00.000Z',
          endTime: '2025-06-17T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-06-19T10:00:00.000Z',
          endTime: '2025-06-19T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-06-21T10:00:00.000Z',
          endTime: '2025-06-21T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-06-23T10:00:00.000Z',
          endTime: '2025-06-23T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-06-25T10:00:00.000Z',
          endTime: '2025-06-25T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-06-27T10:00:00.000Z',
          endTime: '2025-06-27T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-06-29T10:00:00.000Z',
          endTime: '2025-06-29T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-07-01T10:00:00.000Z',
          endTime: '2025-07-01T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-07-03T10:00:00.000Z',
          endTime: '2025-07-03T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-07-05T10:00:00.000Z',
          endTime: '2025-07-05T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-07-07T10:00:00.000Z',
          endTime: '2025-07-07T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-07-09T10:00:00.000Z',
          endTime: '2025-07-09T11:00:00.000Z',
        },
      ];

      const { events: generatedEvents } = generateDuplicateEvents(
        testEvent,
        duplicationCriteria
      );
      const cleanedEvents = generatedEvents.map(cleanEventForDisplay);
      expect(cleanedEvents).toEqual(expectedOutput);
    });

    test('should generate weekly events (every week)', () => {
      const duplicationCriteria = {
        minStartDate: '2025-06-01T00:00:00Z',
        isCustomFrequency: false,
        frequency: 'weekly',
        frequencyInterval: 1,
        endCondition: 'occurrence',
        maxOccurrences: 4,
      };

      const expectedOutput = [
        {
          title: 'test',
          startTime: '2025-06-01T10:00:00.000Z',
          endTime: '2025-06-01T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-06-08T10:00:00.000Z',
          endTime: '2025-06-08T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-06-15T10:00:00.000Z',
          endTime: '2025-06-15T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-06-22T10:00:00.000Z',
          endTime: '2025-06-22T11:00:00.000Z',
        },
      ];

      const { events: generatedEvents } = generateDuplicateEvents(
        testEvent,
        duplicationCriteria
      );
      const cleanedEvents = generatedEvents.map(cleanEventForDisplay);
      expect(cleanedEvents).toEqual(expectedOutput);
    });

    test('should generate monthly events (every month)', () => {
      const duplicationCriteria = {
        minStartDate: '2025-06-01T00:00:00Z',
        isCustomFrequency: false,
        frequency: 'monthly',
        frequencyInterval: 1,
        endCondition: 'specificDate',
        endDate: '2025-08-01T10:00:00Z',
      };

      const expectedOutput = [
        {
          title: 'test',
          startTime: '2025-06-01T10:00:00.000Z',
          endTime: '2025-06-01T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-07-01T10:00:00.000Z',
          endTime: '2025-07-01T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-08-01T10:00:00.000Z',
          endTime: '2025-08-01T11:00:00.000Z',
        },
      ];

      const { events: generatedEvents } = generateDuplicateEvents(
        testEvent,
        duplicationCriteria
      );
      const cleanedEvents = generatedEvents.map(cleanEventForDisplay);
      expect(cleanedEvents).toEqual(expectedOutput);
    });

    test('should generate monthly events on 31st (every month)', () => {
      const duplicationCriteria = {
        minStartDate: '2025-07-31T00:00:00Z',
        isCustomFrequency: false,
        frequency: 'monthly',
        frequencyInterval: 1,
        endCondition: 'occurrence',
        maxOccurrences: 6,
      };

      const expectedOutput = [
        {
          title: 'test',
          startTime: '2025-07-31T10:00:00.000Z',
          endTime: '2025-07-31T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-08-31T10:00:00.000Z',
          endTime: '2025-08-31T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-10-31T10:00:00.000Z',
          endTime: '2025-10-31T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-12-31T10:00:00.000Z',
          endTime: '2025-12-31T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2026-01-31T10:00:00.000Z',
          endTime: '2026-01-31T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2026-03-31T10:00:00.000Z',
          endTime: '2026-03-31T11:00:00.000Z',
        },
      ];

      const { events: generatedEvents } = generateDuplicateEvents(
        testEvent,
        duplicationCriteria
      );
      const cleanedEvents = generatedEvents.map(cleanEventForDisplay);
      expect(cleanedEvents).toEqual(expectedOutput);
    });
  });

  describe('Custom Frequency Tests', () => {
    test('should generate events for specific days of week (Mon, Wed, Fri)', () => {
      const duplicationCriteria = {
        minStartDate: '2025-06-01T00:00:00Z',
        isCustomFrequency: true,
        frequency: 'weekly',
        frequencyInterval: 1,
        daysOfWeek: [1, 3, 5], // Monday, Wednesday, Friday
        endCondition: 'occurrence',
        maxOccurrences: 4,
      };

      const expectedOutput = [
        {
          title: 'test',
          startTime: '2025-06-02T10:00:00.000Z',
          endTime: '2025-06-02T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-06-04T10:00:00.000Z',
          endTime: '2025-06-04T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-06-06T10:00:00.000Z',
          endTime: '2025-06-06T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-06-09T10:00:00.000Z',
          endTime: '2025-06-09T11:00:00.000Z',
        },
      ];

      const { events: generatedEvents } = generateDuplicateEvents(
        testEvent,
        duplicationCriteria
      );
      const cleanedEvents = generatedEvents.map(cleanEventForDisplay);
      expect(cleanedEvents).toEqual(expectedOutput);
    });

    test('should generate events every 2 weeks on Wednesday', () => {
      const duplicationCriteria = {
        minStartDate: '2025-06-03T00:00:00Z',
        isCustomFrequency: true,
        frequency: 'weekly',
        frequencyInterval: 2,
        daysOfWeek: [3], // Wednesday
        endCondition: 'occurrence',
        maxOccurrences: 4,
      };

      const expectedOutput = [
        {
          title: 'test',
          startTime: '2025-06-04T10:00:00.000Z',
          endTime: '2025-06-04T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-06-18T10:00:00.000Z',
          endTime: '2025-06-18T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-07-02T10:00:00.000Z',
          endTime: '2025-07-02T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-07-16T10:00:00.000Z',
          endTime: '2025-07-16T11:00:00.000Z',
        },
      ];

      const { events: generatedEvents } = generateDuplicateEvents(
        testEvent,
        duplicationCriteria
      );
      const cleanedEvents = generatedEvents.map(cleanEventForDisplay);
      expect(cleanedEvents).toEqual(expectedOutput);
    });

    test('should generate events for first Monday every 2 months', () => {
      const duplicationCriteria = {
        minStartDate: '2025-06-01T00:00:00Z',
        isCustomFrequency: true,
        frequency: 'monthly',
        frequencyInterval: 2,
        customMonthRules: {
          type: 'nth_day_of_week',
          day: [1], // Monday
          nth: 1, // First occurrence
        },
        endCondition: 'occurrence',
        maxOccurrences: 3,
      };

      const expectedOutput = [
        {
          title: 'test',
          startTime: '2025-06-02T10:00:00.000Z',
          endTime: '2025-06-02T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-08-04T10:00:00.000Z',
          endTime: '2025-08-04T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-10-06T10:00:00.000Z',
          endTime: '2025-10-06T11:00:00.000Z',
        },
      ];

      const { events: generatedEvents } = generateDuplicateEvents(
        testEvent,
        duplicationCriteria
      );
      const cleanedEvents = generatedEvents.map(cleanEventForDisplay);
      expect(cleanedEvents).toEqual(expectedOutput);
    });

    test('should generate events for specific dates every 2 months', () => {
      const duplicationCriteria = {
        minStartDate: '2025-06-01T00:00:00Z',
        isCustomFrequency: true,
        frequency: 'monthly',
        frequencyInterval: 2,
        customMonthRules: {
          type: 'specific_days_of_month',
          days: [1, 15, 30], // 1st, 15th and 30th of each month
        },
        endCondition: 'occurrence',
        maxOccurrences: 6,
      };

      const expectedOutput = [
        {
          title: 'test',
          startTime: '2025-06-01T10:00:00.000Z',
          endTime: '2025-06-01T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-06-15T10:00:00.000Z',
          endTime: '2025-06-15T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-06-30T10:00:00.000Z',
          endTime: '2025-06-30T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-08-01T10:00:00.000Z',
          endTime: '2025-08-01T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-08-15T10:00:00.000Z',
          endTime: '2025-08-15T11:00:00.000Z',
        },
        {
          title: 'test',
          startTime: '2025-08-30T10:00:00.000Z',
          endTime: '2025-08-30T11:00:00.000Z',
        },
      ];

      const { events: generatedEvents } = generateDuplicateEvents(
        testEvent,
        duplicationCriteria
      );
      const cleanedEvents = generatedEvents.map(cleanEventForDisplay);
      expect(cleanedEvents).toEqual(expectedOutput);
    });
  });

  describe('Edge Cases', () => {
    test('should respect max events limit of 30', () => {
      const duplicationCriteria = {
        minStartDate: '2025-06-01T00:00:00Z',
        isCustomFrequency: false,
        frequency: 'daily',
        frequencyInterval: 1,
        endCondition: 'occurrence',
        maxOccurrences: 50, // Should still only generate 30 events
      };

      const { events: generatedEvents } = generateDuplicateEvents(
        testEvent,
        duplicationCriteria
      );
      expect(generatedEvents.length).toBeLessThanOrEqual(30);
    });

    test('should handle end date condition properly', () => {
      const duplicationCriteria = {
        minStartDate: '2025-06-01T00:00:00Z',
        isCustomFrequency: false,
        frequency: 'daily',
        frequencyInterval: 1,
        endCondition: 'specificDate',
        endDate: '2025-06-05T23:59:59Z',
      };

      const { events: generatedEvents } = generateDuplicateEvents(
        testEvent,
        duplicationCriteria
      );
      const lastEvent = generatedEvents[generatedEvents.length - 1];

      expect(new Date(lastEvent.startTime).getTime()).toBeLessThanOrEqual(
        new Date(duplicationCriteria.endDate).getTime()
      );
    });
  });
});
