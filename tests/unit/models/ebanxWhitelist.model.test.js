const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const EbanxWhitelist = require('../../../src/models/ebanxWhitelist.model');
const { PRODUCT_TYPE } = require('../../../src/services/product/constants');

describe('EbanxWhitelist Model Test', () => {
  let mongoServer;

  beforeAll(async () => {
    // Set required environment variables
    process.env.NODE_ENV = 'test';
    process.env.MONGO_URI = 'mongodb://localhost:27017/test';

    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();

    await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    await EbanxWhitelist.deleteMany({});
  });

  describe('Model Validation', () => {
    it('should create a valid whitelist entry', async () => {
      const validWhitelist = {
        productId: new mongoose.Types.ObjectId(),
        productType: PRODUCT_TYPE.COURSE,
        productLink: 'https://example.com/course/123',
        communityCode: 'test-community',
        status: 'ACTIVE',
        reason: 'High-value trusted product',
        whitelistedBy: '<EMAIL>',
      };

      const whitelistEntry = new EbanxWhitelist(validWhitelist);
      const savedEntry = await whitelistEntry.save();

      expect(savedEntry._id).toBeDefined();
      expect(savedEntry.productId).toEqual(validWhitelist.productId);
      expect(savedEntry.productType).toBe(validWhitelist.productType);
      expect(savedEntry.productLink).toBe(validWhitelist.productLink);
      expect(savedEntry.communityCode).toBe(validWhitelist.communityCode);
      expect(savedEntry.status).toBe(validWhitelist.status);
      expect(savedEntry.reason).toBe(validWhitelist.reason);
      expect(savedEntry.whitelistedBy).toBe(validWhitelist.whitelistedBy);
      expect(savedEntry.createdAt).toBeDefined();
      expect(savedEntry.updatedAt).toBeDefined();
    });

    it('should use ACTIVE as default status', async () => {
      const whitelistWithoutStatus = {
        productId: new mongoose.Types.ObjectId(),
        productType: PRODUCT_TYPE.EVENT,
        productLink: 'https://example.com/event/456',
        communityCode: 'test-community',
        reason: 'Trusted event',
        whitelistedBy: '<EMAIL>',
      };

      const whitelistEntry = new EbanxWhitelist(whitelistWithoutStatus);
      const savedEntry = await whitelistEntry.save();

      expect(savedEntry.status).toBe('ACTIVE');
    });

    it('should fail validation when required fields are missing', async () => {
      const invalidWhitelist = {
        productType: PRODUCT_TYPE.COURSE,
      };

      const whitelistEntry = new EbanxWhitelist(invalidWhitelist);

      await expect(whitelistEntry.save()).rejects.toThrow();
    });

    it('should fail when productId is missing', async () => {
      const whitelistWithoutProductId = {
        productType: PRODUCT_TYPE.COURSE,
        productLink: 'https://example.com/course/123',
        communityCode: 'test-community',
        reason: 'Test reason',
        whitelistedBy: '<EMAIL>',
      };

      const whitelistEntry = new EbanxWhitelist(whitelistWithoutProductId);

      await expect(whitelistEntry.save()).rejects.toThrow(
        /productId.*required/
      );
    });

    it('should fail when productType is invalid', async () => {
      const whitelistWithInvalidType = {
        productId: new mongoose.Types.ObjectId(),
        productType: 'INVALID_TYPE',
        productLink: 'https://example.com/course/123',
        communityCode: 'test-community',
        reason: 'Test reason',
        whitelistedBy: '<EMAIL>',
      };

      const whitelistEntry = new EbanxWhitelist(whitelistWithInvalidType);

      await expect(whitelistEntry.save()).rejects.toThrow(
        /is not a valid enum value/
      );
    });

    it('should fail when status is invalid', async () => {
      const whitelistWithInvalidStatus = {
        productId: new mongoose.Types.ObjectId(),
        productType: PRODUCT_TYPE.COURSE,
        productLink: 'https://example.com/course/123',
        communityCode: 'test-community',
        status: 'INVALID_STATUS',
        reason: 'Test reason',
        whitelistedBy: '<EMAIL>',
      };

      const whitelistEntry = new EbanxWhitelist(
        whitelistWithInvalidStatus
      );

      await expect(whitelistEntry.save()).rejects.toThrow(
        /is not a valid enum value/
      );
    });
  });

  describe('Unique Index', () => {
    it('should enforce unique productId constraint', async () => {
      const productId = new mongoose.Types.ObjectId();

      const firstWhitelist = {
        productId,
        productType: PRODUCT_TYPE.COURSE,
        productLink: 'https://example.com/course/123',
        communityCode: 'test-community',
        reason: 'First entry',
        whitelistedBy: '<EMAIL>',
      };

      const secondWhitelist = {
        productId, // Same productId
        productType: PRODUCT_TYPE.EVENT,
        productLink: 'https://example.com/event/456',
        communityCode: 'another-community',
        reason: 'Second entry',
        whitelistedBy: '<EMAIL>',
      };

      // First save should succeed
      const firstEntry = new EbanxWhitelist(firstWhitelist);
      await firstEntry.save();

      // Second save with same productId should fail
      const secondEntry = new EbanxWhitelist(secondWhitelist);
      await expect(secondEntry.save()).rejects.toThrow(
        /duplicate key error/
      );
    });

    it('should allow different productIds', async () => {
      const firstWhitelist = {
        productId: new mongoose.Types.ObjectId(),
        productType: PRODUCT_TYPE.COURSE,
        productLink: 'https://example.com/course/123',
        communityCode: 'test-community',
        reason: 'First entry',
        whitelistedBy: '<EMAIL>',
      };

      const secondWhitelist = {
        productId: new mongoose.Types.ObjectId(), // Different productId
        productType: PRODUCT_TYPE.EVENT,
        productLink: 'https://example.com/event/456',
        communityCode: 'test-community',
        reason: 'Second entry',
        whitelistedBy: '<EMAIL>',
      };

      const firstEntry = new EbanxWhitelist(firstWhitelist);
      const secondEntry = new EbanxWhitelist(secondWhitelist);

      await firstEntry.save();
      await secondEntry.save();

      const count = await EbanxWhitelist.countDocuments();
      expect(count).toBe(2);
    });
  });

  describe('Timestamps', () => {
    it('should automatically add createdAt and updatedAt timestamps', async () => {
      const whitelist = {
        productId: new mongoose.Types.ObjectId(),
        productType: PRODUCT_TYPE.COURSE,
        productLink: 'https://example.com/course/123',
        communityCode: 'test-community',
        reason: 'Test reason',
        whitelistedBy: '<EMAIL>',
      };

      const whitelistEntry = new EbanxWhitelist(whitelist);
      const savedEntry = await whitelistEntry.save();

      expect(savedEntry.createdAt).toBeInstanceOf(Date);
      expect(savedEntry.updatedAt).toBeInstanceOf(Date);
      expect(savedEntry.createdAt.getTime()).toBe(
        savedEntry.updatedAt.getTime()
      );
    });

    it('should update updatedAt on modification', async () => {
      const whitelist = {
        productId: new mongoose.Types.ObjectId(),
        productType: PRODUCT_TYPE.COURSE,
        productLink: 'https://example.com/course/123',
        communityCode: 'test-community',
        reason: 'Test reason',
        whitelistedBy: '<EMAIL>',
      };

      const whitelistEntry = new EbanxWhitelist(whitelist);
      const savedEntry = await whitelistEntry.save();
      const originalUpdatedAt = savedEntry.updatedAt;

      // Wait a bit to ensure timestamp difference
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Update the entry
      savedEntry.status = 'INACTIVE';
      const updatedEntry = await savedEntry.save();

      expect(updatedEntry.updatedAt.getTime()).toBeGreaterThan(
        originalUpdatedAt.getTime()
      );
      expect(updatedEntry.createdAt.getTime()).toBe(
        savedEntry.createdAt.getTime()
      );
    });
  });

  describe('Product Types', () => {
    it.each([
      PRODUCT_TYPE.EVENT,
      PRODUCT_TYPE.DIGITAL_FILES,
      PRODUCT_TYPE.COURSE,
      PRODUCT_TYPE.CHALLENGE,
      PRODUCT_TYPE.SESSION,
    ])('should accept valid product type: %s', async (productType) => {
      const whitelist = {
        productId: new mongoose.Types.ObjectId(),
        productType,
        productLink: `https://example.com/${productType.toLowerCase()}/123`,
        communityCode: 'test-community',
        reason: `Test ${productType}`,
        whitelistedBy: '<EMAIL>',
      };

      const whitelistEntry = new EbanxWhitelist(whitelist);
      const savedEntry = await whitelistEntry.save();

      expect(savedEntry.productType).toBe(productType);
    });

    it('should accept SUBSCRIPTION product type (whitelist-only enum)', async () => {
      const whitelist = {
        productId: new mongoose.Types.ObjectId(),
        productType: 'SUBSCRIPTION',
        productLink: 'https://example.com/subscription/123',
        communityCode: 'test-community',
        reason: 'Test SUBSCRIPTION type',
        whitelistedBy: '<EMAIL>',
      };

      const whitelistEntry = new EbanxWhitelist(whitelist);
      const savedEntry = await whitelistEntry.save();

      expect(savedEntry.productType).toBe('SUBSCRIPTION');
    });
  });

  describe('Status Values', () => {
    it.each(['ACTIVE', 'INACTIVE'])(
      'should accept valid status: %s',
      async (status) => {
        const whitelist = {
          productId: new mongoose.Types.ObjectId(),
          productType: PRODUCT_TYPE.COURSE,
          productLink: 'https://example.com/course/123',
          communityCode: 'test-community',
          status,
          reason: 'Test reason',
          whitelistedBy: '<EMAIL>',
        };

        const whitelistEntry = new EbanxWhitelist(whitelist);
        const savedEntry = await whitelistEntry.save();

        expect(savedEntry.status).toBe(status);
      }
    );
  });

  describe('Collection Names', () => {
    it('should use correct collection name following community model pattern', () => {
      // Test that the model uses the correct collection name
      expect(EbanxWhitelist.collection.name).toBe('ebanx_whitelists');
    });

    it('should have history middleware configured with correct collection name', async () => {
      // Create a test entry to ensure history middleware is working
      const whitelist = {
        productId: new mongoose.Types.ObjectId(),
        productType: PRODUCT_TYPE.COURSE,
        productLink: 'https://example.com/course/123',
        communityCode: 'test-community',
        reason: 'Test reason',
        whitelistedBy: '<EMAIL>',
      };

      const whitelistEntry = new EbanxWhitelist(whitelist);
      const savedEntry = await whitelistEntry.save();

      // Update the entry to trigger history middleware
      savedEntry.status = 'INACTIVE';
      await savedEntry.save();

      // Check that history collection exists (it would be created by the history middleware)
      const historyCollectionName = 'ebanx_whitelists_history';
      const collections = await mongoose.connection.db.listCollections().toArray();
      const historyCollection = collections.find(col => col.name === historyCollectionName);
      
      // The history collection should exist after the update
      expect(historyCollection).toBeDefined();
    });
  });
});
