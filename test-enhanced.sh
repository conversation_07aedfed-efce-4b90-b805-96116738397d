#!/bin/bash

# EBANX Whitelist API Test Script (Enhanced)
# Tests the POST /api/v1/ebanx-whitelist/resolve-product endpoint

BASE_URL="http://localhost:3003"
API_ENDPOINT="/api/v1/ebanx-whitelist/resolve-product"

echo "🚀 Testing EBANX Whitelist API (Enhanced)"
echo "========================================="

# Generate JWT token using Node.js
echo "🔑 Generating JWT token..."
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is required to generate JWT tokens"
    exit 1
fi

TOKEN=$(node generate-jwt.js)
if [ $? -ne 0 ]; then
    echo "❌ Failed to generate JWT token"
    exit 1
fi

echo "✅ JWT token generated successfully"

# Test function with improved error handling
test_api() {
    local test_name="$1"
    local product_link="$2"
    local expected_community_code="$3"
    local expected_product_type="$4"
    
    echo ""
    echo "📋 Test: $test_name"
    echo "   URL: $product_link"
    echo "   Expected Community Code: $expected_community_code"
    echo "   Expected Product Type: $expected_product_type"
    echo "   ---"
    
    # Make the API call
    response=$(curl -s -w "\n%{http_code}" -X POST \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $TOKEN" \
        -d "{\"productLink\":\"$product_link\"}" \
        "$BASE_URL$API_ENDPOINT")
    
    # Split response and status code
    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)
    
    echo "   HTTP Status: $http_code"
    echo "   Response: $response_body"
    
    # Check HTTP status
    if [ "$http_code" -eq 200 ]; then
        echo "   ✅ HTTP Status: PASS"
        
        # Check if response contains expected values
        if echo "$response_body" | grep -q "\"communityCode\":\"$expected_community_code\""; then
            echo "   ✅ Community Code: PASS"
        else
            echo "   ❌ Community Code: FAIL"
        fi
        
        if echo "$response_body" | grep -q "\"productType\":\"$expected_product_type\""; then
            echo "   ✅ Product Type: PASS"
        else
            echo "   ❌ Product Type: FAIL"
        fi
        
        if echo "$response_body" | grep -q "\"productId\":"; then
            echo "   ✅ Product ID: PASS"
        else
            echo "   ❌ Product ID: FAIL"
        fi
    else
        echo "   ❌ HTTP Status: FAIL (Expected 200, got $http_code)"
    fi
    
    echo ""
}

# Test error cases
test_error() {
    local test_name="$1"
    local product_link="$2"
    local expected_error="$3"
    local expected_status="$4"
    
    echo ""
    echo "📋 Test: $test_name"
    echo "   URL: $product_link"
    echo "   Expected Error: $expected_error"
    echo "   Expected Status: $expected_status"
    echo "   ---"
    
    response=$(curl -s -w "\n%{http_code}" -X POST \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $TOKEN" \
        -d "{\"productLink\":\"$product_link\"}" \
        "$BASE_URL$API_ENDPOINT")
    
    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)
    
    echo "   HTTP Status: $http_code"
    echo "   Response: $response_body"
    
    if [ "$http_code" -eq "$expected_status" ]; then
        echo "   ✅ HTTP Status: PASS"
    else
        echo "   ❌ HTTP Status: FAIL (Expected $expected_status, got $http_code)"
    fi
    
    if echo "$response_body" | grep -q "$expected_error"; then
        echo "   ✅ Error Message: PASS"
    else
        echo "   ❌ Error Message: FAIL"
    fi
    
    echo ""
}

# Test cases with real data from MongoDB

echo "🔍 Testing with real data from MongoDB..."

# Test 1: EVENT type - Creator Community
test_api \
    "Event - Creator Community" \
    "/creators-lalala/events/swae" \
    "NAS_DAILY_CREATOR_COMMUNITY" \
    "EVENT"

# Test 2: DIGITAL_FILES type - Free Content Community (products path)
test_api \
    "Digital Files - Free Content Community (products)" \
    "/freecontentcommunity/products/tbvw" \
    "NAS_IO_FREE_CONTENT_COMMUNITY" \
    "DIGITAL_FILES"

# Test 3: CHALLENGE type - Dont Stop Believing Community
test_api \
    "Challenge - Dont Stop Believing Community" \
    "/dont-stop-believing/challenges/geekout-fake" \
    "DONT_STOP_BELIEVING" \
    "CHALLENGE"

# Test 4: DIGITAL_FILES with digital-files path
test_api \
    "Digital Files (digital-files path)" \
    "/freecontentcommunity/digital-files/aoqb" \
    "NAS_IO_FREE_CONTENT_COMMUNITY" \
    "DIGITAL_FILES"

# Test 5: EVENT with different slug
test_api \
    "Event - Creator Community (different slug)" \
    "/creators-lalala/events/zrdx" \
    "NAS_DAILY_CREATOR_COMMUNITY" \
    "EVENT"

echo "🧪 Testing error cases..."

# Test 6: Invalid URL format
test_error \
    "Invalid URL Format" \
    "/invalid/format" \
    "Invalid product URL format" \
    400

# Test 7: Non-existent community
test_error \
    "Non-existent Community" \
    "/non-existent-community/products/test" \
    "Community not found" \
    404

# Test 8: Invalid product type
test_error \
    "Invalid Product Type" \
    "/creators-lalala/invalid-type/test" \
    "Invalid product type path" \
    400

# Test 9: Non-existent product
test_error \
    "Non-existent Product" \
    "/creators-lalala/events/non-existent-slug" \
    "Product not found" \
    404

echo "🔐 Testing authentication..."

# Test 10: Missing Authorization
echo ""
echo "📋 Test: Missing Authorization"
echo "   Expected: Authentication error"
echo "   ---"

response=$(curl -s -w "\n%{http_code}" -X POST \
    -H "Content-Type: application/json" \
    -d '{"productLink":"/creators-lalala/events/swae"}' \
    "$BASE_URL$API_ENDPOINT")

http_code=$(echo "$response" | tail -n1)
response_body=$(echo "$response" | head -n -1)

echo "   HTTP Status: $http_code"
echo "   Response: $response_body"

if [ "$http_code" -eq 401 ]; then
    echo "   ✅ Authentication check: PASS"
else
    echo "   ❌ Authentication check: FAIL (Expected 401, got $http_code)"
fi

# Test 11: Invalid JWT token
echo ""
echo "📋 Test: Invalid JWT Token"
echo "   Expected: Authentication error"
echo "   ---"

response=$(curl -s -w "\n%{http_code}" -X POST \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer invalid.jwt.token" \
    -d '{"productLink":"/creators-lalala/events/swae"}' \
    "$BASE_URL$API_ENDPOINT")

http_code=$(echo "$response" | tail -n1)
response_body=$(echo "$response" | head -n -1)

echo "   HTTP Status: $http_code"
echo "   Response: $response_body"

if [ "$http_code" -eq 401 ]; then
    echo "   ✅ Invalid token check: PASS"
else
    echo "   ❌ Invalid token check: FAIL (Expected 401, got $http_code)"
fi

echo ""
echo "🎯 Test Summary Complete!"
echo "================================"
echo "📝 All tests use real data from the MongoDB database:"
echo "   • Community: creators-lalala (NAS_DAILY_CREATOR_COMMUNITY)"
echo "   • Community: freecontentcommunity (NAS_IO_FREE_CONTENT_COMMUNITY)"
echo "   • Community: dont-stop-believing (DONT_STOP_BELIEVING)"
echo "   • Product types: EVENT, DIGITAL_FILES, CHALLENGE"
echo "   • Real product slugs: /swae, /tbvw, /geekout-fake, /aoqb, /zrdx"
echo ""
echo "🔐 Authentication uses COPS JWT token with secret: youShallNotPass_Cops1!"
echo "🌐 API endpoint: $BASE_URL$API_ENDPOINT"
echo ""
echo "💡 To run this test:"
echo "   1. Make sure the server is running on localhost:3003"
echo "   2. Make sure Node.js is installed (for JWT generation)"
echo "   3. Run: chmod +x test-enhanced.sh && ./test-enhanced.sh"