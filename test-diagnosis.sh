#!/bin/bash

# Diagnostic test for EBANX Whitelist API
BASE_URL="http://localhost:3003"

echo "🔍 EBANX Whitelist API Diagnosis"
echo "================================"

# Check if generate-jwt.js exists
if [ ! -f "generate-jwt.js" ]; then
    echo "❌ generate-jwt.js not found"
    exit 1
fi

# Generate JWT token
echo "🔑 Generating JWT token..."
TOKEN=$(node generate-jwt.js)
if [ $? -ne 0 ] || [ -z "$TOKEN" ]; then
    echo "❌ Failed to generate JWT token"
    exit 1
fi
echo "✅ JWT token generated: ${TOKEN:0:50}..."

echo ""
echo "🧪 Step-by-step diagnosis:"

# Step 1: Check server is running
echo ""
echo "Step 1: Check server connectivity"
server_response=$(curl -s -w "%{http_code}" -o /dev/null "$BASE_URL")
if [ "$server_response" = "200" ] || [ "$server_response" = "404" ]; then
    echo "✅ Server is running (Status: $server_response)"
else
    echo "❌ Server is not responding (Status: $server_response)"
    exit 1
fi

# Step 2: Check if API routes are working
echo ""
echo "Step 2: Check API routes"
api_response=$(curl -s -w "%{http_code}" -o /dev/null "$BASE_URL/api/v1/server-time")
if [ "$api_response" = "200" ]; then
    echo "✅ API routes are working (Status: $api_response)"
else
    echo "❌ API routes not working (Status: $api_response)"
fi

# Step 3: Test our specific endpoint without auth (should return 401)
echo ""
echo "Step 3: Test endpoint without auth (should return 401)"
no_auth_response=$(curl -s -w "%{http_code}" -o /dev/null -X POST \
    -H "Content-Type: application/json" \
    -d '{"productLink":"/test"}' \
    "$BASE_URL/api/v1/ebanx-whitelist/resolve-product")

echo "Response status: $no_auth_response"
if [ "$no_auth_response" = "401" ]; then
    echo "✅ Authentication middleware is working"
elif [ "$no_auth_response" = "404" ]; then
    echo "❌ Endpoint not found - routing issue"
elif [ "$no_auth_response" = "418" ]; then
    echo "❌ HTTP 418 (I'm a teapot) - unusual error, check server logs"
else
    echo "❌ Unexpected response: $no_auth_response"
fi

# Step 4: Test with auth but invalid data
echo ""
echo "Step 4: Test with auth but invalid data"
invalid_data_response=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $TOKEN" \
    -d '{"productLink":"/invalid"}' \
    "$BASE_URL/api/v1/ebanx-whitelist/resolve-product")

echo "Response: $invalid_data_response"

# Step 5: Test with auth and valid data
echo ""
echo "Step 5: Test with auth and valid data"
echo "Testing URL: /creators-lalala/events/swae"

valid_response=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $TOKEN" \
    -d '{"productLink":"/creators-lalala/events/swae"}' \
    "$BASE_URL/api/v1/ebanx-whitelist/resolve-product")

echo "Response: $valid_response"

# Step 6: Check if middleware is working correctly
echo ""
echo "Step 6: Check middleware stack"
echo "Testing with different Content-Type..."

content_type_test=$(curl -s -w "%{http_code}" -o /dev/null -X POST \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -H "Authorization: Bearer $TOKEN" \
    -d "productLink=/test" \
    "$BASE_URL/api/v1/ebanx-whitelist/resolve-product")

echo "Response with different Content-Type: $content_type_test"

echo ""
echo "🎯 Diagnosis Summary:"
echo "===================="
echo "1. Server Status: $server_response"
echo "2. API Routes: $api_response"
echo "3. No Auth Test: $no_auth_response"
echo "4. Invalid Data Test: See above"
echo "5. Valid Data Test: See above"
echo "6. Content-Type Test: $content_type_test"
echo ""
echo "💡 Next steps:"
echo "   - If status is 418: Check server logs for teapot errors"
echo "   - If status is 404: Check router configuration"
echo "   - If status is 401: Check JWT token generation"
echo "   - If status is 500: Check database connection and service code"
echo ""
echo "📋 Quick manual test:"
echo "curl -X POST \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"Authorization: Bearer $TOKEN\" \\"
echo "  -d '{\"productLink\":\"/creators-lalala/events/swae\"}' \\"
echo "  \"$BASE_URL/api/v1/ebanx-whitelist/resolve-product\""