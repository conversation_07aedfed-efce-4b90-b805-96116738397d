const Agent = require('agentkeepalive');
const axios = require('axios');
const { performance } = require('perf_hooks');
const URL = require('url');

const { getMetric } = require('../monitoring/metricRegistry');

const logger = require('../services/logger.service');

const {
  DEPENDENCY_REQUEST_DIMENSIONS,
  DEPENDENCY_REQUEST_METRICS,
} = require('../monitoring/constants');

const {
  AXIOS_KEEP_ALIVE_MAX_SOCKETS,
  AXIOS_KEEP_ALIVE_MAX_FREE_SOCKETS,
  AXIOS_KEEP_ALIVE_TIMEOUT,
  AXIOS_KEEP_ALIVE_FREE_SOCKET_TIMEOUT,
} = require('../config');

const keepAliveAgent = new Agent({
  maxSockets: AXIOS_KEEP_ALIVE_MAX_SOCKETS,
  maxFreeSockets: AXIOS_KEEP_ALIVE_MAX_FREE_SOCKETS,
  timeout: AXIOS_KEEP_ALIVE_TIMEOUT,
  freeSocketTimeout: AXIOS_KEEP_ALIVE_FREE_SOCKET_TIMEOUT,
});

// HTTPS agent
const httpsKeepAliveAgent = new Agent.HttpsAgent({
  maxSockets: AXIOS_KEEP_ALIVE_MAX_SOCKETS,
  maxFreeSockets: AXIOS_KEEP_ALIVE_MAX_FREE_SOCKETS,
  timeout: AXIOS_KEEP_ALIVE_TIMEOUT,
  freeSocketTimeout: AXIOS_KEEP_ALIVE_FREE_SOCKET_TIMEOUT,
});

const axiosInstance = axios.create({
  httpAgent: keepAliveAgent,
  httpsAgent: httpsKeepAliveAgent,
});

function getBaseUrl(url) {
  const parsedUrl = URL.parse(url);
  const paths = parsedUrl.pathname.split('/');
  const objectIdRegex = new RegExp('^[0-9a-fA-F]{24}$');
  const ipRegex = new RegExp(/^(\d{1,3}\.){3}\d{1,3}$/);
  const numberRegex = new RegExp(/\d/);
  const versionRegex = new RegExp(/^v\d+$/);
  for (let i = 0; i < paths.length; i += 1) {
    if (objectIdRegex.test(paths[i])) {
      paths[i] = ':some_object_id';
    } else if (paths[i].includes('price_')) {
      paths[i] = ':price_id';
    } else if (ipRegex.test(paths[i])) {
      paths[i] = ':ip_address';
    } else if (
      numberRegex.test(paths[i]) &&
      !versionRegex.test(paths[i])
    ) {
      paths[i] = ':some-unique-value';
    }
  }
  return `${parsedUrl.protocol}//${parsedUrl.host}${paths.join('/')}`;
}

function recordPreMetrics(dimensionData) {
  try {
    if (dimensionData.method !== 'head') {
      const labels = {
        [DEPENDENCY_REQUEST_DIMENSIONS.API_METHOD]:
          dimensionData.method || 'unknown',
        [DEPENDENCY_REQUEST_DIMENSIONS.API_PATH]:
          getBaseUrl(dimensionData.url) || 'unknown',
      };

      const incomingRequestGauge = getMetric(
        DEPENDENCY_REQUEST_METRICS.INCOMING_REQUEST_GAUGE
      );
      if (incomingRequestGauge) {
        incomingRequestGauge.labels(labels).inc();
      }
    }
  } catch (err) {
    logger.error('Axios instance recordPreMetrics error', err, err.stack);
  }
}

axiosInstance.interceptors.request.use((config) => {
  const newConfig = config;
  newConfig.startTime = performance.now();
  recordPreMetrics(config);
  return newConfig;
});

function recordPostMetrics(response, error) {
  try {
    if (response?.config && response?.config?.method !== 'head') {
      const duration = performance.now() - response.config.startTime;
      const processedRequestGauge = getMetric(
        DEPENDENCY_REQUEST_METRICS.PROCESSED_REQUEST_GAUGE
      );
      const latencyHistogram = getMetric(
        DEPENDENCY_REQUEST_METRICS.REQUEST_LATENCY_HISTOGRAM
      );
      const labels = {
        [DEPENDENCY_REQUEST_DIMENSIONS.API_METHOD]:
          response.config.method || 'unknown',
        [DEPENDENCY_REQUEST_DIMENSIONS.API_PATH]:
          getBaseUrl(response.config.url) || 'unknown',
        [DEPENDENCY_REQUEST_DIMENSIONS.STATUS_CODE]:
          response.status || 'unknown',
      };

      if (labels[DEPENDENCY_REQUEST_DIMENSIONS.STATUS_CODE] !== 200) {
        logger.error(
          `Request failed: ${
            labels[DEPENDENCY_REQUEST_DIMENSIONS.API_PATH]
          }, statusMessage: ${response.statusText}, data=${
            response.data ? JSON.stringify(response.data) : ''
          }`
        );
        if (error && response?.data?.errorMessage) {
          // Assign the error message from other service
          // eslint-disable-next-line no-param-reassign
          error.message = `${error.message}: ${response?.data?.errorMessage}`;
        }
      }

      if (processedRequestGauge) {
        processedRequestGauge.labels(labels).inc();
      }
      if (latencyHistogram) {
        latencyHistogram.labels(labels).observe(duration);
      }
    }
  } catch (err) {
    logger.error('Axios instance recordPostMetrics error', err, err.stack);
  }
}

axiosInstance.interceptors.response.use(
  (response) => {
    recordPostMetrics(response);
    return response;
  },
  (error) => {
    recordPostMetrics(error.response, error);
    throw error;
  }
);

module.exports = axiosInstance;
