const Stripe = require('stripe');

const { getConfigByType } = require('../../src/services/config.service');

const { CONFIG_TYPES } = require('../constants/common');

let stripeGlobalClient;
let stripeIndiaClient;

const init = async () => {
  const configType = CONFIG_TYPES.PAYMENT_BACKEND_ENV_CONFIG_TYPE;

  const config = await getConfigByType(configType);
  const STRIPE_GLOBAL_SECRET_KEY =
    config?.envVarData?.['STRIPE_SECRET_KEY'];
  const STRIPE_INDIA_SECRET_KEY =
    config?.envVarData?.['STRIPE_INDIA_SECRET_KEY'];

  stripeGlobalClient = new Stripe(STRIPE_GLOBAL_SECRET_KEY);
  stripeIndiaClient = new Stripe(STRIPE_INDIA_SECRET_KEY);
};

function getStripeGlobalClient() {
  return stripeGlobalClient;
}

function getStripeIndiaClient() {
  return stripeIndiaClient;
}

module.exports = {
  init,
  getStripeGlobalClient,
  getStripeIndiaClient,
};
