const CommunityModel = require('../../communitiesAPI/models/community.model');
const CommunitySubscriptionsModel = require('../../communitiesAPI/models/communitySubscriptions.model');
const learnersModel = require('../../models/learners.model');
const mobileNotificationsModel = require('../../models/mobileNotifications/mobileNotifications.model');
const ProgramModel = require('../../models/program/program.model');
const usersModel = require('../../models/users.model');
const CommunityEventsModel = require('../../communitiesAPI/models/communityEvents.model');
const communityFoldersModel = require('../../communitiesAPI/models/communityFolders.model');
const { PROGRAM_STATUS } = require('../../services/program/constants');
const EmailActivityModel = require('../../models/sendgrid/EmailActivity.model');
const rawTransactionModel = require('../../models/rawTransaction.model');
const PaymentBackendRpc = require('../../rpc/paymentBackend');

/**
 * Convert an array of { key, value } objects into a filter object
 * @param {Array<{key: string, value: string}>} keyValueObjArray
 * @returns {Record<string, string>}
 */
const getFilter = (keyValueObjArray) => {
  return (keyValueObjArray || []).reduce((acc, { key, value }) => {
    acc[key] = value;
    return acc;
  }, {});
};

/**
 * Returns a new string with a leading slash if it's not already present.
 * If `link` is falsy, it returns an empty string.
 * @param {string} link
 * @returns {string}
 */
const insertSlashIfNotPresent = (link = '') => {
  if (!link) return '';
  return link.startsWith('/') ? link : `/${link}`;
};

/**
 * Build a standard error JSON response for the LLM assistant
 * @param {string} message
 * @param {any} tool
 * @returns {object}
 */
const buildErrorResponse = (message, tool) => ({
  tool_call_id: tool.id,
  output: JSON.stringify({ message }),
});

/**
 * Build a standard success JSON response with data for the LLM assistant
 * @param {any} data
 * @param {any} tool
 * @returns {object}
 */
const buildSuccessResponse = (data, tool) => ({
  tool_call_id: tool.id,
  output: JSON.stringify(data),
});

/**
 * Retrieve tool arguments, parse them to get filters and type, handle errors gracefully.
 * @param {object} tool
 * @returns {{ filters: object; type?: string }}
 */
const parseToolArguments = (tool) => {
  const args = JSON.parse(tool.function.arguments || '{}');
  return {
    filters: getFilter(args.filters || []),
    type: args.type,
  };
};

/**
 * Get an admin JS link based on the resource type.
 * @param {string} id
 * @param {'User' | 'Community' | 'Challenge' | 'CommunityFolder' | 'CommunityEvents'} type
 * @returns {string}
 */
const getAdminJSLink = (id, type) => {
  const baseUrl = 'https://cops-admin.nas.io/admin/resources';
  switch (type) {
    case 'User':
      return `${baseUrl}/UserSchema/records/${id}/show`;
    case 'Community':
      return `${baseUrl}/Community/records/${id}/show`;
    case 'Challenge':
      return `${baseUrl}/Challenge/records/${id}/show`;
    case 'CommunityFolder':
      return `${baseUrl}/CommunityFolder/records/${id}/show`;
    case 'CommunityEvents':
      return `${baseUrl}/CommunityEvents/records/${id}/show`;
    default:
      return 'no admin js link found';
  }
};

/**
 * Find a user by email and return the user document plus its AdminJS link.
 * @param {string} email
 * @returns {Promise<{ user: object | null, adminLink: string }>}
 */
const findUserByEmail = async (email) => {
  if (!email) {
    return { user: null, adminLink: '' };
  }
  const user = await usersModel
    .findOne({ email: email.toLowerCase() })
    .lean();
  if (!user?._id) {
    return { user: null, adminLink: '' };
  }
  return {
    user,
    adminLink: getAdminJSLink(user._id, 'User'),
  };
};

/**
 * Given a community object, find the creator user by `createdBy` email,
 * attach it to the community under `community.createdBy`,
 * and build relevant AdminJS links.
 * @param {object} community
 * @returns {Promise<{ community: object, communityLink: string, ownerLink: string }>}
 */
const attachCommunityOwnerAndLinks = async (community) => {
  if (!community) {
    return {
      community: null,
      communityLink: '',
      ownerLink: '',
    };
  }

  // Find user who created the community
  const { user: owner, adminLink: ownerLink } = await findUserByEmail(
    community.createdBy
  );
  // Attach user doc to community
  // eslint-disable-next-line no-param-reassign
  community.createdBy = owner;

  // Build link to the community in AdminJS
  const communityLink = getAdminJSLink(community._id, 'Community');

  return { community, communityLink, ownerLink };
};

const getCommunityInfo = async (tool) => {
  const { filters } = parseToolArguments(tool);
  const community = await CommunityModel.findOne(filters).lean();

  if (!community) {
    return buildErrorResponse(
      `No community found for the filter = ${JSON.stringify(filters)}`,
      tool
    );
  }

  const {
    community: communityWithOwner,
    communityLink,
    ownerLink,
  } = await attachCommunityOwnerAndLinks(community, 'Community');

  const response = buildSuccessResponse(
    {
      community: communityWithOwner,
      adminJSLinkForTheCommunityOwner: ownerLink,
      adminJsLinkForTheCommunity: communityLink,
    },
    tool
  );
  return response;
};
/**
 * Get User Info
 * @param {object} tool
 * @returns {Promise<object>}
 */
const getUserInfo = async (tool) => {
  const { filters } = parseToolArguments(tool);

  const { email } = filters;
  const [user, learner] = await Promise.all([
    usersModel
      .findOne({
        email: email?.toLowerCase(),
      })
      .lean(),
    learnersModel
      .findOne({
        email: email?.toLowerCase(),
      })
      .lean(),
  ]);

  if (!user && !learner) {
    return buildErrorResponse(
      `No user or learner found for the filter = ${JSON.stringify(
        filters
      )}`,
      tool
    );
  }

  const adminLink = user?._id ? getAdminJSLink(user._id, 'User') : '';

  return buildSuccessResponse(
    {
      users: user,
      learners: learner,
      adminJsLinkForTheEmail: adminLink,
    },
    tool
  );
};

/**
 * Get Challenge Info
 * @param {object} filter
 * @param {object} tool
 * @returns {Promise<object>}
 */
const getChallengeInfo = async (filter, tool) => {
  const slug = insertSlashIfNotPresent(
    filter.link ?? filter.slug ?? filter.productLink
  );

  const challenge = await ProgramModel.findOne({
    slug,
    status: PROGRAM_STATUS.PUBLISHED,
  }).lean();

  if (!challenge) {
    return buildErrorResponse(
      `No challenge found for the filter = ${JSON.stringify(filter)}`,
      tool
    );
  }

  // Retrieve the community info
  const community = await CommunityModel.findOne({
    _id: challenge.communityObjectId,
  }).lean();

  const {
    community: communityWithOwner,
    communityLink,
    ownerLink,
  } = await attachCommunityOwnerAndLinks(community, 'Community');

  const challengeLink = getAdminJSLink(challenge._id, 'Challenge');

  return buildSuccessResponse(
    {
      challenge,
      communityInfo: communityWithOwner,
      adminJsLinkForTheChallenge: challengeLink,
      adminJsLinkForTheCommunity: communityLink,
      adminJsLinkForTheCommunityOwner: ownerLink,
    },
    tool
  );
};

/**
 * Get Products/Course Info
 * @param {object} filter
 * @param {object} tool
 * @returns {Promise<object>}
 */
const getProductsInfo = async (filter, tool) => {
  const resourceSlug = filter.link ?? filter.slug ?? filter.productLink;

  const product = await communityFoldersModel
    .findOne({ resourceSlug })
    .lean();

  if (!product) {
    return buildErrorResponse(
      `No product found for the filter = ${JSON.stringify(filter)}`,
      tool
    );
  }

  // Retrieve community info
  const community = await CommunityModel.findOne({
    _id: product.communityObjectId,
  }).lean();

  const {
    community: communityWithOwner,
    communityLink,
    ownerLink,
  } = await attachCommunityOwnerAndLinks(community, 'Community');

  const productLink = getAdminJSLink(product._id, 'CommunityFolder');

  return buildSuccessResponse(
    {
      product,
      communityInfo: communityWithOwner,
      adminJsLinkForTheProduct: productLink,
      adminJsLinkForTheCommunity: communityLink,
      adminJsLinkForTheCommunityOwner: ownerLink,
    },
    tool
  );
};

/**
 * Get Event Info
 * @param {object} filter
 * @param {object} tool
 * @returns {Promise<object>}
 */
const getEventInfo = async (filter, tool) => {
  const slug =
    filter.link ?? filter.slug ?? filter.productLink ?? filter.eventLink;

  const event = await CommunityEventsModel.findOne({ slug }).lean();
  if (!event) {
    return buildErrorResponse(
      `No event found for the filter = ${JSON.stringify(filter)}`,
      tool
    );
  }

  const communityId = event.communities?.[0];
  const community = await CommunityModel.findOne({
    _id: communityId,
  }).lean();

  const {
    community: communityWithOwner,
    communityLink,
    ownerLink,
  } = await attachCommunityOwnerAndLinks(community, 'Community');

  const eventLink = getAdminJSLink(event._id, 'CommunityEvents');

  return buildSuccessResponse(
    {
      event,
      communityInfo: communityWithOwner,
      adminJsLinkForTheEvent: eventLink,
      adminJsLinkForTheCommunity: communityLink,
      adminJsLinkForTheCommunityOwner: ownerLink,
    },
    tool
  );
};
/**
 * Get top N mobile notifications for a user.
 * @param {string} userId
 * @param {number} [N=10]
 * @returns {Promise<Array<object>>}
 */
const topNMobileNotifications = async (userId, N = 10) => {
  return mobileNotificationsModel.find({ userId }).limit(N).lean();
};

/**
 * Get top N email notifications sent to an email.
 * @param {string} emailId
 * @param {number} [N=10]
 * @returns {Promise<Array<object>>}
 */
const topNEmailNotificationsSent = async (emailId, N = 10) => {
  return EmailActivityModel.find({ email: emailId }).limit(N).lean();
};

/**
 * Get user notifications (mobile or email)
 * @param {object} tool
 * @returns {Promise<object>}
 */
const getUserNotifications = async (tool) => {
  const { filters, type } = parseToolArguments(tool);

  // Find the user
  const user = await usersModel.findOne(filters).lean();
  if (!user) {
    return buildErrorResponse(
      `No user found for filter = ${JSON.stringify(filters)}`,
      tool
    );
  }

  const adminJSLinkForTheUser = getAdminJSLink(user._id, 'User');
  const { user_id: userId, email } = user;

  let notificationsData = [];

  switch (type) {
    case 'mobile':
      notificationsData = await topNMobileNotifications(userId);
      break;
    case 'email':
      notificationsData = await topNEmailNotificationsSent(email);
      break;
    default:
      return buildErrorResponse(`Invalid type = ${type}`, tool);
  }

  return buildSuccessResponse(
    { notificationsData, adminJSLinkForTheUser },
    tool
  );
};

/**
 * Get product info (event, product, challenge, etc.)
 * @param {object} tool
 * @returns {Promise<object>}
 */
const getProductInfo = async (tool) => {
  const { filters, type } = parseToolArguments(tool);

  switch (type) {
    case 'event':
    case 'events':
      return getEventInfo(filters, tool);

    case 'products':
    case 'product':
    case 'course':
    case 'courses':
    case '1:1 sessions':
    case '1:1 session':
    case 'folders':
    case '1:1':
    case 'folder':
      return getProductsInfo(filters, tool);

    case 'challenge':
    case 'challenges':
      return getChallengeInfo(filters, tool);

    default:
      return buildErrorResponse(`Invalid type = ${type}`, tool);
  }
};

/**
 * Get payment information for a user
 * @param {object} tool
 * @returns {Promise<object>}
 */
const getPaymentInformation = async (tool) => {
  const { filters } = parseToolArguments(tool);
  const { email } = filters;

  if (!email) {
    return buildErrorResponse(
      'Email is required to search the raw transactions',
      tool
    );
  }

  const transactions = await rawTransactionModel
    .find({
      email: email.toLowerCase(),
    })
    .sort({ transactionCreatedAt: -1 })
    .limit(5)
    .lean();

  if (!transactions?.length) {
    return buildErrorResponse(
      `No transactions found for email = ${email} maybe the email id might be different or the user has not made any payment yet`,
      tool
    );
  }

  const paymentBackendRpc = new PaymentBackendRpc();
  await paymentBackendRpc.init();

  // Process each transaction and fetch Razorpay details if applicable
  const processedTransactions = await Promise.all(
    transactions.map(async (transaction) => {
      if (transaction.paymentProvider?.toLowerCase() === 'razorpay') {
        try {
          const razorpayDetails =
            await paymentBackendRpc.getRazorpayPayment(
              transaction.transactionReferenceId
            );
          return {
            ...transaction,
            razorpayPaymentDetails: razorpayDetails,
          };
        } catch (error) {
          console.error('Error fetching Razorpay payment details:', error);
          return transaction;
        }
      }
      return transaction;
    })
  );

  // get admin js link for the user
  const { adminLink } = await findUserByEmail(email);

  return buildSuccessResponse(
    {
      transactions: processedTransactions,
      adminJSLinkForTheUser: adminLink,
    },
    tool
  );
};

/**
 * Get subscription information
 * @param {object} tool
 * @returns {Promise<object>}
 */
const getSubscriptionInformation = async (tool) => {
  const { filters } = parseToolArguments(tool);

  // Build a filter object for subscription
  const { code, communityCode, email, link } = filters;
  const subscriptionFilter = {
    communityCode: code ?? communityCode,
    ...(email ? { email: String(email).toLowerCase() } : {}),
  };

  // If there's a link, find the corresponding community
  if (link) {
    const community = await CommunityModel.findOne({ link }).lean();

    if (!community) {
      return buildErrorResponse(
        `No community found for the filter = ${JSON.stringify(
          subscriptionFilter
        )}`,
        tool
      );
    }
    subscriptionFilter.communityCode = community.code;
  }

  const subscriptions = await CommunitySubscriptionsModel.find(
    subscriptionFilter
  ).lean();
  if (!subscriptions?.length) {
    return buildErrorResponse(
      `No subscription found for the filter = ${JSON.stringify(
        subscriptionFilter
      )}`,
      tool
    );
  }

  const primaryEmail = subscriptions?.[0]?.email;
  const { adminLink: adminJsLinkForTheEmail } = await findUserByEmail(
    primaryEmail
  );

  return buildSuccessResponse(
    { subscriptions, adminJsLinkForTheEmail },
    tool
  );
};

module.exports = {
  getCommunityInfo,
  getProductInfo,
  getUserInfo,
  getUserNotifications,
  getPaymentInformation,
  getSubscriptionInformation,
};
