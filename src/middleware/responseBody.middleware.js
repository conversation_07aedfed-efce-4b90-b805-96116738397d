const morgan = require('morgan');
const logger = require('../services/logger.service');

const MAX_CHAR_LIMIT = 100;
const MAX_BODY_LIMIT = 1000;

function removeJwt(value) {
  const jwtRegex = /^[A-Za-z0-9-_]+?\.[A-Za-z0-9-_]+?\.[A-Za-z0-9-_=]+?$/;
  return value.replace(jwtRegex, '');
}

function applySubstring(value, maxLength) {
  return typeof value === 'string'
    ? removeJwt(value).substring(0, maxLength)
    : value;
}

function substringObjectValues(obj, maxLength) {
  return JSON.parse(
    JSON.stringify(obj, (_, value) => applySubstring(value, maxLength))
  );
}

const responseBodyMiddleware = async (req, res, next) => {
  const oldWrite = res.write;
  const oldEnd = res.end;

  const chunks = [];
  res.write = (...chunk) => {
    chunks.push(Buffer.from(chunk[0]));

    return oldWrite.apply(res, chunk);
  };

  res.end = (...chunk) => {
    if (chunk[0]) {
      chunks.push(Buffer.from(chunk[0]));
    }

    const body = Buffer.concat(chunks).toString('utf8');

    res.body = body;

    morgan.token('res-body', () => {
      try {
        // Skip SSE responses (Content-Type: text/event-stream)
        // If FE pass if-none-match in header, the response from backend will be null
        // 304 means resource hasn't changed
        const contentType = res.getHeader?.('content-type') || '';
        if (
          res.statusCode === 304 ||
          contentType.includes('text/event-stream')
        ) {
          return '';
        }

        const string = JSON.stringify(
          substringObjectValues(JSON.parse(res.body) ?? {}, MAX_CHAR_LIMIT)
        ).substring(0, MAX_BODY_LIMIT);
        return string;
      } catch (error) {
        if (req.originalUrl !== '/metrics') {
          logger.error(
            'Could not parse response body',
            {
              responseType: typeof res.body,
              rawBody: res.body,
              method: req.method,
              url: req.originalUrl,
              headers: req.headers,
              requestBody: req.body,
              requestQuery: req.query,
              statusCode: res.statusCode,
            },
            error,
            error.stack
          );
        }
        return 'Could not parse response body';
      }
    });

    oldEnd.apply(res, chunk);
  };
  next();
};

module.exports = { responseBodyMiddleware };
