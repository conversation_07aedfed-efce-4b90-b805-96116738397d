const morgan = require('morgan');
const logConfig = require('../services/logConfig.service');
const winston = require('../services/loggerCreation.service');
const logger = require('../services/logger.service');

const setupMorganMiddleware = (app) => {
  // Only set up Morgan if request logging is enabled
  if (!logConfig.enableRequestLogging) {
    logger.info('Request logging disabled');
    return;
  }

  // Enhanced tokens
  morgan.token('correlation-id', (req) => {
    return req.headers['x-correlation-id'] || '-';
  });

  morgan.token('session-id', (req) => {
    return req.headers['x-session-id'] || '-';
  });

  morgan.token('remote-ip', (req) => {
    return req.headers['x-forwarded-for'] || req.ip;
  });

  morgan.token('api-route', (req) => {
    let apiRoute = req.originalUrl.split('?')[0];
    Object.entries(req.params).forEach(([key, value]) => {
      apiRoute = apiRoute.replace(value, `:${key}`);
    });
    return apiRoute;
  });

  morgan.token('full-url', (req) => {
    return `${req.protocol}://${req.get('host')}${req.originalUrl}`;
  });

  // Conditional tokens based on config
  if (logConfig.enableBodyLogging) {
    morgan.token('req-body', (req) => {
      if (!req.body || Object.keys(req.body).length === 0) return '-';

      const body = JSON.stringify(logger.maskSensitiveData(req.body));
      return body.length > logConfig.maxBodyLength
        ? body.substring(0, logConfig.maxBodyLength) + '...[truncated]'
        : body;
    });

    morgan.token('req-query', (req) => {
      if (!req.query || Object.keys(req.query).length === 0) return '-';
      return JSON.stringify(logger.maskSensitiveData(req.query));
    });

    morgan.token('req-params', (req) => {
      if (!req.params || Object.keys(req.params).length === 0) return '-';
      return JSON.stringify(req.params);
    });
  }

  if (logConfig.enableResponseLogging) {
    morgan.token('res-body', (req, res) => {
      if (!res.body) return '-';

      const body = JSON.stringify(logger.maskSensitiveData(res.body));
      return body.length > logConfig.maxBodyLength
        ? body.substring(0, logConfig.maxBodyLength) + '...[truncated]'
        : body;
    });
  }

  // Different formats based on configuration
  let format;
  if (logConfig.logFormat === 'json') {
    // For JSON format, create a function that returns JSON
    format = function (tokens, req, res) {
      const logEntry = {
        method: tokens.method(req, res),
        url: tokens.url(req, res),
        apiRoute: tokens['api-route'](req, res),
        fullUrl: tokens['full-url'](req, res),
        httpVersion: tokens['http-version'](req, res),
        status: tokens.status(req, res),
        responseTime: tokens['response-time'](req, res),
        contentLength: tokens.res(req, res, 'content-length'),
        correlationId: tokens['correlation-id'](req, res),
        sessionId: tokens['session-id'](req, res),
        remoteIp: tokens['remote-ip'](req, res),
        userAgent: tokens['user-agent'](req, res),
        referrer: tokens.referrer(req, res),
      };

      // Add body logging if enabled
      if (logConfig.enableBodyLogging) {
        logEntry.requestBody = tokens['req-body'](req, res);
        logEntry.requestQuery = tokens['req-query'](req, res);
        logEntry.requestParams = tokens['req-params'](req, res);
      }

      if (logConfig.enableResponseLogging) {
        logEntry.responseBody = tokens['res-body'](req, res);
      }

      return JSON.stringify(logEntry);
    };
  } else {
    // Legacy format - but with session ID in the message if available
    // const baseFormat =
    //   ':method|:api-route|:url|:full-url|HTTP/:http-version|:status|:res[content-length]|:referrer|:user-agent|:remote-ip';
    //
    // Create format function that includes session ID
    format = function (tokens, req, res) {
      //      const sessionId = tokens['session-id'](req, res);
      let message =
        tokens.method(req, res) +
        '|' +
        tokens['api-route'](req, res) +
        '|' +
        tokens.url(req, res) +
        '|' +
        tokens['full-url'](req, res) +
        '|' +
        'HTTP/' +
        tokens['http-version'](req, res) +
        '|' +
        tokens.status(req, res) +
        '|' +
        tokens.res(req, res, 'content-length') +
        '|' +
        tokens.referrer(req, res) +
        '|' +
        tokens['user-agent'](req, res) +
        '|' +
        tokens['remote-ip'](req, res);

      if (logConfig.enableBodyLogging) {
        message += '|' + (req.headers ? JSON.stringify(req.headers) : '-');
        message += '|' + tokens['req-query'](req, res);
        message += '|' + tokens['req-params'](req, res);
        message += '|' + tokens['req-body'](req, res);
      }

      if (logConfig.enableResponseLogging) {
        message +=
          '|' + (res.getHeaders ? JSON.stringify(res.getHeaders()) : '-');
        message += '|' + tokens['res-body'](req, res);
      }

      message += '|' + tokens['response-time'](req, res);

      // Add session ID to the message if present
      // if (sessionId && sessionId !== '-') {
      //   message = `[SessionId: ${sessionId}] ${message}`;
      // }

      return message;
    };
  }

  // Apply Morgan middleware
  app.use(
    morgan(format, {
      stream: winston.stream,
      skip: (req, res) => {
        // Skip health checks unless in debug mode
        if (
          logConfig.logLevel !== 'debug' &&
          (req.url === '/ping' || req.url === '/health')
        ) {
          return true;
        }

        // Apply sampling for non-error responses
        if (res.statusCode < 400 && !logConfig.shouldSample()) {
          return true;
        }

        return false;
      },
    })
  );
};

module.exports = {
  setupMorganMiddleware,
};
