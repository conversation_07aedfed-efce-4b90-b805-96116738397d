const BlacklistedUsers = require('../models/blacklistedUsers.model');
const logger = require('../services/logger.service');
const { ForbiddenError } = require('../utils/error.util');
const {
  BLACKLISTED_USERS_IDENTIFIER,
} = require('../constants/blacklistedUsers.constant');
const { getUserIP } = require('../utils/headers.util');

const rejectBlacklistedUser = async (req, res, next) => {
  try {
    const orConditions = [];

    const ip = getUserIP(req) || null;
    if (ip) {
      orConditions.push({
        identifier: BLACKLISTED_USERS_IDENTIFIER.IP,
        value: ip,
      });
    }
    if (req.user?.email) {
      const email = req.user.email.toLowerCase();

      orConditions.push({
        identifier: BLACKLISTED_USERS_IDENTIFIER.EMAIL,
        value: email,
      });
    }

    if (!orConditions.length) {
      return next();
    }

    const blacklistedUser = await BlacklistedUsers.findOne({
      $or: orConditions,
    }).lean();

    if (blacklistedUser) {
      logger.info('Rejected blacklisted user ', blacklistedUser);
      const err = new ForbiddenError(
        'User is not authorized to access this resource'
      );
      return next(err);
    }

    next();
  } catch (err) {
    logger.error('Error in blacklisted user check:', err);
    next(err);
  }
};

module.exports = rejectBlacklistedUser;
