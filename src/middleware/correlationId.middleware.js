const { v4: uuidv4 } = require('uuid');
const httpContext = require('express-http-context');

// Standard header names for correlation ID
const CORRELATION_ID_HEADERS = [
  'x-correlation-id',
  'x-request-id',
  'correlationid',
  'x-amzn-trace-id',
];

// Standard header names for session ID
const SESSION_ID_HEADERS = [
  'x-session-id',
  'x-user-session-id',
  'sessionid',
];

const correlationIdMiddleware = (req, res, next) => {
  // 1. Handle Correlation ID (per request)
  let correlationId = null;
  for (const header of CORRELATION_ID_HEADERS) {
    if (req.headers[header]) {
      correlationId = req.headers[header];
      break;
    }
  }

  if (!correlationId) {
    correlationId = uuidv4();
  }

  // 2. Handle Session ID (per user session)
  let sessionId = null;
  for (const header of SESSION_ID_HEADERS) {
    if (req.headers[header]) {
      sessionId = req.headers[header];
      break;
    }
  }

  // Check other sources for session ID
  // if (!sessionId) {
  //   // From JWT token
  //   if (req.user && req.user.sessionId) {
  //     sessionId = req.user.sessionId;
  //   }
  //   // From cookies
  //   else if (req.cookies && req.cookies.sessionId) {
  //     sessionId = req.cookies.sessionId;
  //   }
  // }

  // 3. Set all IDs in context
  httpContext.set('correlationId', correlationId);
  httpContext.set('sessionId', sessionId);
  httpContext.set('reqId', correlationId); // Legacy support

  // 4. Set headers for downstream services
  req.headers['x-correlation-id'] = correlationId;
  if (sessionId) {
    req.headers['x-session-id'] = sessionId;
  }

  // 5. Add to response headers
  res.setHeader('X-Correlation-Id', correlationId);
  if (sessionId) {
    res.setHeader('X-Session-Id', sessionId);
  }

  // 6. Store other useful context
  httpContext.set('userId', req.user?.id); // will get the user ID if we have a user object
  httpContext.set('reqIp', req.headers['x-forwarded-for'] || req.ip);
  httpContext.set('userAgent', req.headers['user-agent']);
  httpContext.set('requestMethod', req.method);
  httpContext.set('requestPath', req.path);

  if (req.headers['cloudfront-viewer-country']) {
    httpContext.set('country', req.headers['cloudfront-viewer-country']);
  }

  next();
};

// Helper to propagate IDs to outbound HTTP requests
const propagateRequestContext = (headers = {}) => {
  const correlationId = httpContext.get('correlationId');
  const sessionId = httpContext.get('sessionId');
  const userId = httpContext.get('userId'); // TODO: get from JWT or session

  if (correlationId) {
    /* eslint-disable no-param-reassign */
    headers['X-Correlation-Id'] = correlationId;
  }

  if (sessionId) {
    /* eslint-disable no-param-reassign */
    headers['X-Session-Id'] = sessionId;
  }

  if (userId) {
    /* eslint-disable no-param-reassign */

    headers['X-User-Id'] = userId;
  }

  return headers;
};

module.exports = {
  correlationIdMiddleware,
  propagateRequestContext,
};
