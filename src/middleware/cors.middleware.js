const cors = require('cors');
const logger = require('../services/logger.service');

const CORS_WHITELIST = [
  // eg, *.test.dev-nasacademy.com, *.test.dev-nasacademy.com/, *.test.dev-nasacademy.ph
  /\.test.dev-nasacademy\.(com|ph)(\/?)$/,
  // eg, *.dev-nasacademy.com, *.dev-nasacademy.com/, *.dev-nasacademy.ph
  /\.dev-nasacademy\.(com|ph)(\/?)$/,
  // e.g *.staging-nasacademy.com, *.staging-nasacademy.com/, *.staging-nasacademy.ph
  /\.staging-nasacademy\.(com|ph)(\/?)$/,
  // e.g *.test-nasacademy.com, *.test-nasacademy.com/, *.test-nasacademy.ph
  /\.test-nasacademy\.(com|ph)(\/?)$/,
  // eg, *.nasacademy.com, *.nasacademy.com/, *.nasacademy.ph
  /\.nasacademy\.(com|ph)(\/?)$/,
  // eg, *.dev-nas.io/
  /\.dev-nas\.(io)(\/?)$/,
  // eg, *.nas.io/
  /\.nas\.(io)(\/?)$/,
  // eg, https://nas.io
  /https:\/\/nas.(io)/,
  // eg, d2sou8fjwjnjyx.amplifyapp.com, d2sou8fjwjnjyx.amplifyapp.com/
  /\.d2sou8fjwjnjyx\.amplifyapp\.com(\/?)$/,
  /\.d1gfad0vhpehx9\.amplifyapp\.com(\/?)$/,
  // eg, nas-company.vercel.app
  /nas-company\.vercel\.app(\/?)$/,
  // eg, https://nasacademy.ph
  /https:\/\/nasacademy.(com|ph)/,
  // eg, http://localhost:3000, http://localhost:3001
  /http:\/\/localhost:(3000|3001|3002|443)/,
  /https:\/\/localhost:(3000|3001|3002|443)/,
  /https:\/\/d1b6ra2xfc58va.cloudfront.net/,
  /http:\/\/************:(3000|3001|3002|443)/, // Nick's ip to remove eventually
  // eg, https://dev-nas.io
  /https:\/\/dev-nas.(io)/,
  /https:\/\/intricate-facet-265607.web.app/,
  // eg, *.pillar.io
  /\.pillar\.io(\/?)$/,
  /^https:\/\/lovable-states-151735.framer.app/,
  /^https:\/\/nasio-test-pages.framer.website/,
  /^https:\/\/project-[a-z0-9-]+\.framercanvas\.com/,
];

const CORS_LOG_TYPE = {
  ORIGIN_EXISTS: 'origin_exists',
  ORIGIN_ALLOWED: 'origin_allowed',
  ORIGIN_BLOCKED: 'origin_blocked',
};

const CORS_ERROR_MESSAGE = 'Not allowed by CORS';

/**
 * Logs cors-related information for origin
 *
 * @param  {enum} type
 * @param  {String} origin
 * @param  {{ isAllowed: Boolean }} params
 * @return void
 */
const logCorsInfo = function (type, origin, params = {}) {
  switch (type) {
    case CORS_LOG_TYPE.ORIGIN_EXISTS:
      logger.info(
        'regex match for ',
        origin,
        'status : ',
        params.isAllowed
      );
      break;
    case CORS_LOG_TYPE.ORIGIN_ALLOWED:
      logger.info('Origin is in Whitelist ', origin, ' Approving request');
      break;
    case CORS_LOG_TYPE.ORIGIN_BLOCKED:
      logger.error('Cors failed for origin ', origin);
      break;
    default:
      break;
  }
};

/**
 * Checks if an origin is allowed by a whitelist
 *
 * @param  {String} origin
 * @param  {Array[Regex]} whitelist
 * @returns {Boolean}
 */
const isAllowedOrigin = function (origin, whitelist) {
  // TODO: Revisit this logic
  if (!origin) {
    return true;
  }

  if (typeof origin !== 'string') {
    return false;
  }

  for (const regex of whitelist) {
    const isValid = regex.test(origin);
    if (isValid) {
      return true;
    }
  }

  return false;
};

/**
 * @param  {String} origin
 * @param  {Function} callback
 */
const validateOriginCallback = function (origin, callback) {
  const hasOrigin = !!origin;
  const isAllowed = isAllowedOrigin(origin, CORS_WHITELIST);

  // log origin stats
  if (hasOrigin) {
    logCorsInfo(CORS_LOG_TYPE.ORIGIN_EXISTS, origin, { isAllowed });
  }

  if (isAllowed) {
    logCorsInfo(CORS_LOG_TYPE.ORIGIN_ALLOWED, origin);
    callback(null, true);
  } else {
    logCorsInfo(CORS_LOG_TYPE.ORIGIN_BLOCKED, origin);
    callback(new Error(CORS_ERROR_MESSAGE));
  }
};

/** Cors Options **/
const corsOptions = {
  origin: validateOriginCallback,
  credentials: true,
};

module.exports = {
  CORS_WHITELIST,
  isAllowedOrigin,
  validateOriginCallback,
  cors: cors(corsOptions),
};
