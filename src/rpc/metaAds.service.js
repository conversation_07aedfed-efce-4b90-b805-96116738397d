const axios = require('axios');
const {
  META_APP_ACCESS_TOKEN,
  META_APP_SECRET_KEY,
  META_APP_ID,
  META_BUSINESS_ID,
  META_SYSTEM_USER_ID,
} = require('../config');
const {
  META_ADS_GRAPH_API,
  DEFAULT_AD_ACCOUNT_ID_DEV,
  META_BID_STRATEGY,
  META_ADS_STATUS,
} = require('../services/magicAudience/constants');
const logger = require('../services/logger.service');

const createMetaAdsCamapaign = async ({
  campaignName,
  status,
  metaAdsIntegration,
  objective,
}) => {
  try {
    const { adsAccountId = DEFAULT_AD_ACCOUNT_ID_DEV } =
      metaAdsIntegration;
    const data = {
      name: campaignName,
      objective,
      status,
      access_token: META_APP_ACCESS_TOKEN,
      special_ad_categories: [],
    };

    const response = await axios.post(
      `${META_ADS_GRAPH_API}act_${adsAccountId}/campaigns`,
      data,
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    if (response.status !== 200) {
      throw new Error(
        `Error creating Meta Ads campaign: ${response.statusText}`
      );
    }
    const { id: campaignId } = response.data;
    return campaignId;
  } catch (error) {
    logger.error(
      `Error creating Meta Ads campaign:  ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
    throw new Error(
      `${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
  }
};

const updateMetaAdsCampaign = async ({ campaignId, graphPayload }) => {
  try {
    const response = await axios.post(
      `${META_ADS_GRAPH_API}${campaignId}`,
      graphPayload,
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    if (response.status !== 200) {
      throw new Error(
        `Error updating Meta Ads campaign: ${response.statusText}`
      );
    }
    return response.data;
  } catch (error) {
    logger.error(
      `Error updating Meta Ads campaign: ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
    throw new Error(
      `${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
  }
};

const deleteMetaAdsCampaign = async (campaignId) => {
  try {
    if (!campaignId) {
      return;
    }
    const response = await axios.delete(
      `${META_ADS_GRAPH_API}${campaignId}?access_token=${META_APP_ACCESS_TOKEN}`
    );
    if (response.status !== 200) {
      throw new Error(
        `Error deleting Meta Ads campaign: ${response.statusText}`
      );
    }
    return response.data;
  } catch (error) {
    logger.error(
      `Error deleting Meta Ads campaign: ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
    throw new Error(
      `${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
  }
};

const createMetaAdSet = async ({
  metaAdsIntegration,
  campaignName,
  metaAdsCampaignId,
  totalBudgetInUSD,
  billingEvent,
  optimizationGoal,
  targeting,
  promotedObject,
}) => {
  try {
    const { adsAccountId = DEFAULT_AD_ACCOUNT_ID_DEV } =
      metaAdsIntegration;

    const data = {
      name: `${campaignName}_adset`,
      campaign_id: metaAdsCampaignId,
      // daily_budget: dailyBudgetInUSD, // add this for time being ( because without ad set we cannot create ad objects - so just adding a minimum budget)
      lifetime_budget: totalBudgetInUSD, // Meta Ads API expects budget in cents
      // set this to 1 year from now
      time_stop: new Date(
        Date.now() + 366 * 24 * 60 * 60 * 1000
      ).toISOString(),
      start_time: new Date(
        Date.now() + 365 * 24 * 60 * 60 * 1000
      ).toISOString(),
      billing_event: billingEvent,
      optimization_goal: optimizationGoal,
      bid_strategy: META_BID_STRATEGY.LOWEST_COST_WITHOUT_CAP, // default value set by facebook for any ad set bid strategy
      status: META_ADS_STATUS.PAUSED,
      targeting,
      access_token: META_APP_ACCESS_TOKEN,
      promoted_object: promotedObject,
    };

    const response = await axios.post(
      `${META_ADS_GRAPH_API}act_${adsAccountId}/adsets`,
      data,
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
    if (response.status !== 200) {
      throw new Error(
        `Error creating Meta Ads ad set: ${response.statusText}`
      );
    }
    const { id: adSetId } = response.data;
    return adSetId;
  } catch (error) {
    logger.error(
      `Error creating Meta Ads ad set:  ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
    throw new Error(
      ` ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
  }
};

const updateAdSet = async ({ adSetId, graphPayload }) => {
  try {
    if (graphPayload?.start_time) {
      // if start_time is provided, we need to check if it is in the past
      const adSetResponse = await axios.get(
        `${META_ADS_GRAPH_API}${adSetId}?access_token=${META_APP_ACCESS_TOKEN}&fields=start_time`
      );
      if (adSetResponse.status !== 200) {
        throw new Error(
          `Error fetching ad set info: ${adSetResponse.statusText}`
        );
      }
      // Check if the start_time is in the past
      const adSetResponseData = adSetResponse.data;
      // check if start_time is in the past
      if (adSetResponseData.start_time) {
        const adSetStartTime = new Date(adSetResponseData.start_time);
        if (adSetStartTime < new Date()) {
          // eslint-disable-next-line no-param-reassign
          delete graphPayload.start_time; // remove start_time from payload if it is in the past
        }
      }
    }
    const response = await axios.post(
      `${META_ADS_GRAPH_API}${adSetId}`,
      graphPayload,
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    if (response.status !== 200) {
      throw new Error(
        `Error updating Meta Ads ad set: ${response.statusText}`
      );
    }
    return response.data;
  } catch (error) {
    logger.error(
      `Error updating Meta Ads ad set: ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
    throw new Error(
      ` ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
  }
};

const deleteMetaAdsAdSet = async (adSetId) => {
  try {
    if (!adSetId) {
      return; // if adSetId is not provided, return null
    }
    const response = await axios.delete(
      `${META_ADS_GRAPH_API}${adSetId}?access_token=${META_APP_ACCESS_TOKEN}`
    );
    if (response.status !== 200) {
      throw new Error(
        `Error deleting Meta Ads ad set: ${response.statusText}`
      );
    }
    return response.data;
  } catch (error) {
    logger.error(
      `Error deleting Meta Ads ad set: ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
    throw new Error(
      `${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
  }
};
const createMetaAdsAdObject = async (payload) => {
  try {
    const response = await axios.post(
      `${META_ADS_GRAPH_API}act_${payload.adsAccountId}/ads`,
      payload,
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
    if (response.status !== 200) {
      throw new Error(
        `Error creating Meta Ads ad object: ${response.statusText}`
      );
    }
    const { id: adId } = response.data;
    return adId;
  } catch (error) {
    logger.error(
      `Error creating Meta Ads ad object:  ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
    throw new Error(
      `${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
  }
};

// THE BELOW API IS NOT USED ANYWHERE IN THE CODE AND WILL NOT BE USED BUT KEPT FOR FUTURE USE
const createAdCreative = async ({
  assetFeedSpec,
  adsAccountId,
  pageId,
  instagramPageInfo,
}) => {
  try {
    const objectStorySpec = {
      page_id: pageId,
    };

    if (instagramPageInfo) {
      objectStorySpec.instagram_actor_id = instagramPageInfo.id;
    }
    const creativeRes = await axios.post(
      `${META_ADS_GRAPH_API}act_${adsAccountId}/adcreatives`,
      {
        name: `Patch ${Date.now()}`,
        object_story_spec: objectStorySpec,
        asset_feed_spec: assetFeedSpec,
        access_token: META_APP_ACCESS_TOKEN,
      }
    );

    if (creativeRes.status !== 200) {
      throw new Error(
        `Error creating Meta Ads ad creative: ${creativeRes.statusText}`
      );
    }

    return creativeRes.data.id;
  } catch (error) {
    logger.error(
      `Error creating Meta Ads ad creative: ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
    throw new Error(
      ` ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
  }
};

const createVideoOnMeta = async ({ adsAccountId, videoLink }) => {
  try {
    const response = await axios.post(
      `${META_ADS_GRAPH_API}act_${adsAccountId}/advideos`,
      {
        file_url: videoLink,
        access_token: META_APP_ACCESS_TOKEN,
      }
    );

    if (response.status !== 200) {
      throw new Error(
        `Error uploading video to Meta Ads: ${response.statusText}`
      );
    }

    return response.data.id;
  } catch (error) {
    logger.error(
      `Error uploading video to Meta Ads: ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
    throw new Error(
      ` ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
  }
};

const getVideoUploadStatusFromMeta = async ({ videoId }) => {
  try {
    const response = await axios.get(
      `${META_ADS_GRAPH_API}${videoId}?access_token=${META_APP_ACCESS_TOKEN}&fields=status`
    );
    if (response.status !== 200) {
      throw new Error(
        `Error fetching video upload status from Meta Ads: ${response.statusText}`
      );
    }
    return response.data.status;
  } catch (error) {
    logger.error(
      `Error fetching video upload status from Meta Ads: ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
    throw new Error(
      ` ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
  }
};
const updateAdObject = async ({ fbAdId, graphPayload }) => {
  try {
    if (!fbAdId) {
      return;
    }
    const updatedInfo = await axios.post(
      `${META_ADS_GRAPH_API}${fbAdId}`,
      graphPayload
    );

    if (updatedInfo.status !== 200) {
      throw new Error(
        `Error updating Meta Ads ad object: ${updatedInfo.statusText}`
      );
    }
    return updatedInfo.data;
  } catch (error) {
    logger.error(
      `Error updating Meta Ads ad object: ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
    throw new Error(
      ` ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
  }
};

const deleteMetaAdsAdObject = async (adId) => {
  try {
    if (!adId) {
      return;
    }
    const response = await axios.delete(
      `${META_ADS_GRAPH_API}${adId}?access_token=${META_APP_ACCESS_TOKEN}`
    );
    if (response.status !== 200) {
      throw new Error(
        `Error deleting Meta Ads ad object: ${response.statusText}`
      );
    }
    return response.data;
  } catch (error) {
    logger.error(
      `Error deleting Meta Ads ad object: ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
    throw new Error(
      `${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
  }
};

const getEstimatedDeliveryOfAdSet = async ({
  adSetId,
  optimizationGoal,
  targeting,
}) => {
  try {
    const response = await axios.get(
      `${META_ADS_GRAPH_API}${adSetId}/delivery_estimate?access_token=${META_APP_ACCESS_TOKEN}&optimization_goal=${optimizationGoal}&targeting_spec=${targeting}`
    );

    if (response.status !== 200) {
      throw new Error(
        `Error fetching estimated delivery of ad set: ${response.statusText}`
      );
    }
    return response.data?.data;
  } catch (error) {
    logger.error(
      `Error fetching estimated delivery of ad set: ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
    throw new Error(
      ` ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
  }
};

const getCampaignInsights = async (campaignId, filters) => {
  try {
    const query = new URLSearchParams({
      access_token: META_APP_ACCESS_TOKEN,
      level: 'campaign',
      fields: filters.fields,
      date_preset: 'maximum', // Pulls all available data
      time_increment: 'all_days',
    });

    const response = await axios.get(
      `${META_ADS_GRAPH_API}${campaignId}/insights?${query.toString()}`
    );

    if (response.status !== 200) {
      throw new Error(
        `Error fetching campaign insights: ${response.statusText}`
      );
    }
    return response.data?.data;
  } catch (error) {
    logger.error(
      `Error fetching campaign insights: ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
    throw new Error(
      ` ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
  }
};

const createMetaAdsPixel = async ({ adsAccountId, pixelName }) => {
  try {
    const response = await axios.post(
      `${META_ADS_GRAPH_API}act_${adsAccountId}/adspixels`,
      {
        name: pixelName,
        access_token: META_APP_ACCESS_TOKEN,
      },
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
    if (response.status !== 200) {
      throw new Error(
        `Error creating Meta Ads Pixel: ${response.statusText}`
      );
    }
    return response.data.id; // Return the pixel ID
  } catch (error) {
    logger.error(
      `Error creating Meta Ads Pixel: ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
    throw new Error(
      ` ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
  }
};

const getMetaInsights = async ({
  objectId,
  level,
  fields,
  dateStart, // 'YYYY-MM-DD' (string) | undefined
  dateEnd, // 'YYYY-MM-DD' (string) | undefined
  timeIncrement = null, // 1 for daily graph, null for totals
  allTime = false,
}) => {
  try {
    if (!objectId) {
      return [];
    }
    const qs = new URLSearchParams({
      level,
      fields,
      access_token: META_APP_ACCESS_TOKEN,
    });

    // add either time_range or date_preset
    if (dateStart && dateEnd) {
      qs.append(
        'time_range',
        JSON.stringify({ since: dateStart, until: dateEnd })
      );
    } else {
      qs.append('date_preset', 'last_7d'); // default
    }

    if (allTime) qs.append('date_preset', 'maximum');
    if (timeIncrement) qs.append('time_increment', timeIncrement);

    const url = `${META_ADS_GRAPH_API}${objectId}/insights?${qs.toString()}`;
    const { data, status, statusText } = await axios.get(url);

    if (status !== 200) throw new Error(`Meta API error → ${statusText}`);
    return data.data; // always an array
  } catch (error) {
    logger.error(
      `Error fetching Meta Ads insights: ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
    throw new Error(
      ` ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
  }
};

const getCampaignInfo = async (campaignId, fields = ['status']) => {
  try {
    const response = await axios.get(
      `${META_ADS_GRAPH_API}${campaignId}?access_token=${META_APP_ACCESS_TOKEN}&fields=${fields.join(
        ','
      )}`
    );

    if (response.status !== 200) {
      throw new Error(
        `Error fetching campaign info: ${response.statusText}`
      );
    }
    return response.data;
  } catch (error) {
    logger.error(
      `Error fetching campaign info: ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
    throw new Error(
      `${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
  }
};
// THE BELOW API IS NOT USED ANYWHERE IN THE CODE AND WILL NOT BE USED BUT KEPT FOR FUTURE USE
const fetchInstagramAccount = async (pageId) => {
  // first we need to get the page access token
  // then we can use the page access token to get the instagram account
  try {
    const response = await axios.get(
      `${META_ADS_GRAPH_API}${pageId}?fields=access_token,name&access_token=${META_APP_ACCESS_TOKEN}`
    );

    if (response.status !== 200) {
      throw new Error(
        `Error fetching Instagram account: ${response.statusText}`
      );
    }
    const { access_token: pageAccessToken } = response.data;

    const igResponse = await axios.get(
      `${META_ADS_GRAPH_API}${pageId}/instagram_accounts?access_token=${pageAccessToken}`
    );
    if (igResponse.status !== 200) {
      throw new Error(
        `Error fetching Instagram account: ${igResponse.statusText}`
      );
    }
    const igAccountInfo = igResponse.data || {};
    if (igAccountInfo.length === 0) {
      return null;
    }
    return igResponse.data?.data[0];
  } catch (error) {
    logger.error(
      `Error fetching Instagram account: ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
    throw new Error(
      ` ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
  }
};

const getUserAccessToken = async (code, redirectUri) => {
  try {
    const response = await axios.get(
      `${META_ADS_GRAPH_API}oauth/access_token?client_id=${META_APP_ID}&redirect_uri=${redirectUri}&client_secret=${META_APP_SECRET_KEY}&code=${code}`
    );

    if (response.status !== 200) {
      throw new Error(
        `Error fetching user access token: ${response.statusText}`
      );
    }
    return response.data.access_token;
  } catch (error) {
    logger.error(
      `Error fetching user access token: ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
    throw new Error(
      ` ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
  }
};

const getUsersMetaAccountPages = async ({ accessToken }) => {
  try {
    const response = await axios.get(
      `${META_ADS_GRAPH_API}me/accounts?access_token=${accessToken}`
    );

    if (response.status !== 200) {
      throw new Error(
        `Error fetching user's Meta account pages: ${response.statusText}`
      );
    }
    return response.data.data;
  } catch (error) {
    logger.error(
      `Error fetching user's Meta account pages: ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
    throw new Error(
      ` ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
  }
};

const addAgencyToTheUsersPage = async ({ pageId, accessToken }) => {
  try {
    const response = await axios.post(
      `${META_ADS_GRAPH_API}/${pageId}/agencies?access_token=${accessToken}&business=${META_BUSINESS_ID}&permitted_tasks=['ADVERTISE','ANALYZE']`
    );

    if (response.status !== 200) {
      throw new Error(
        `Error adding agency to user's page: ${response.statusText}`
      );
    }
  } catch (error) {
    if (
      error?.response?.data?.error?.error_user_title ===
        'Partner already has access' ||
      error?.response?.data?.error?.code === 3989 ||
      error?.response?.data?.error?.code === 3946
    ) {
      return true;
    }
    logger.error(
      `Error adding agency to user's page: ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
    throw new Error(
      ` ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
  }
};
const addUsersPageToBusiness = async ({ pageId }) => {
  try {
    const response = await axios.post(
      `${META_ADS_GRAPH_API}${META_BUSINESS_ID}/client_pages`,
      {
        page_id: pageId,
        access_token: META_APP_ACCESS_TOKEN, // This should be the user's access token (with proper scopes)
        permitted_tasks: ['ADVERTISE', 'ANALYZE'],
      }
    );
    if (response.status !== 200) {
      throw new Error(
        `Error adding user's page to business: ${response.statusText}`
      );
    }
    return response.data;
  } catch (error) {
    if (
      error?.response?.data?.error?.error_user_title ===
        'You Already Have Access to This Page' ||
      error?.response?.data?.error?.error_user_title ===
        'Duplicate request' ||
      error?.response?.data?.error?.error_user_msg ===
        'Your business already has access to the object.' ||
      error?.response?.data?.error?.error_subcode === 1752044 || // This is a specific error code for duplicate requests (permission already granted)
      error?.response?.data?.error?.code === 200 // permissions error
    ) {
      return true;
    }
    logger.error(
      `Error adding user's page to business: ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
    throw new Error(
      ` ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
  }
};

const approveUserPageToBusiness = async ({ pageId }) => {
  try {
    const response = await axios.post(
      `${META_ADS_GRAPH_API}${pageId}/assigned_users`,
      {
        asset_id: pageId,
        tasks: ['ADVERTISE', 'ANALYZE'],
        user: META_SYSTEM_USER_ID,
        business: META_BUSINESS_ID,
        access_token: META_APP_ACCESS_TOKEN,
      }
    );
    if (response.status !== 200) {
      throw new Error(
        `Error approving user's page to business: ${response.statusText}`
      );
    }
    return response.data;
  } catch (error) {
    logger.error(
      `Error approving user's page to business: ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
    throw new Error(
      ` ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
  }
};

const addNasIOCommunityLinkToPage = async ({
  communityLink,
  pageId,
  accessToken, // This should be the user's access token (with proper scopes)
}) => {
  try {
    const pageAccessTokenResponse = await axios.get(
      `${META_ADS_GRAPH_API}${pageId}?fields=access_token,name&access_token=${accessToken}`
    );

    if (pageAccessTokenResponse.status !== 200) {
      throw new Error(
        `Error fetching page access token: ${pageAccessTokenResponse.statusText}`
      );
    }

    const { access_token: pageAccessToken } = pageAccessTokenResponse.data;
    const response = await axios.post(
      `${META_ADS_GRAPH_API}${pageId}?access_token=${pageAccessToken}`,
      {
        website: communityLink,
      }
    );

    if (response.status !== 200) {
      throw new Error(
        `Error adding community link to page: ${response.statusText}`
      );
    }

    return response.data;
  } catch (error) {
    logger.error(
      `Error adding community link to page: ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
    throw new Error(
      ` ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
  }
};
const getPageInformation = async (pageId, fields) => {
  try {
    const response = await axios.get(
      `${META_ADS_GRAPH_API}${pageId}?fields=${fields.join(
        ','
      )}&access_token=${META_APP_ACCESS_TOKEN}`
    );

    if (response.status !== 200) {
      throw new Error(
        `Error fetching page information: ${response.statusText}`
      );
    }
    return response.data;
  } catch (error) {
    logger.error(
      `Error fetching page information: ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
    throw new Error(
      ` ${
        error?.response?.data?.error?.error_user_msg ??
        error?.response?.data?.error?.message
      }`
    );
  }
};
module.exports = {
  addAgencyToTheUsersPage,
  getVideoUploadStatusFromMeta,
  addNasIOCommunityLinkToPage,
  approveUserPageToBusiness,
  addUsersPageToBusiness,
  getUserAccessToken,
  getUsersMetaAccountPages,
  getPageInformation,
  createMetaAdsCamapaign,
  createMetaAdSet,
  createMetaAdsAdObject,
  createAdCreative,
  createMetaAdsPixel,
  updateAdObject,
  createVideoOnMeta,
  fetchInstagramAccount,
  getEstimatedDeliveryOfAdSet,
  getCampaignInsights,
  getMetaInsights,
  getCampaignInfo,
  updateAdSet,
  updateMetaAdsCampaign,
  deleteMetaAdsAdSet,
  deleteMetaAdsCampaign,
  deleteMetaAdsAdObject,
};
