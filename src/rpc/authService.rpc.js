const { AUTH_SERVICE_URL } = require('../config');

const { ROUTE_API_KEY } = process.env;

const axios = require('../clients/axios.client');

class AuthServiceRpc {
  constructor() {
    this.host = AUTH_SERVICE_URL;
    this.authKey = ROUTE_API_KEY;
  }

  async generateLoginAccessAndRefreshToken(email) {
    const axiosConfig = {
      method: 'POST',
      url: `${this.host}/api/v1/login-tokens`,
      headers: {
        'api-key': this.authKey,
      },
      data: {
        email,
      },
    };

    const result = await axios(axiosConfig).then((res) => res.data);
    return result;
  }

  async generateEmailToken(email) {
    const axiosConfig = {
      method: 'POST',
      url: `${this.host}/api/v1/email-tokens`,
      headers: {
        'api-key': this.authKey,
      },
      data: {
        email,
      },
    };

    const result = await axios(axiosConfig).then((res) => res.data);
    return result;
  }
}

module.exports = AuthServiceRpc;
