const axios = require('axios');
const logger = require('../services/logger.service');
const { getConfigByTypeFromCache } = require('../services/config.service');
const { CONFIG_TYPES } = require('../constants/common');

exports.retrieveUnsplashImages = async (searchTerm, limit = 1) => {
  const { envVarData = null } = await getConfigByTypeFromCache(
    CONFIG_TYPES.LEARN_PORTAL_BACKEND_ENV_CONFIG_TYPE
  );
  const unsplashApiKey = envVarData?.UNSPLASH_API_KEY;

  if (!unsplashApiKey) {
    throw new Error('Missing unsplash api key');
  }

  try {
    const response = await axios
      .get(
        `https://api.unsplash.com/search/photos?page=1&query=${encodeURIComponent(
          searchTerm
        )}&client_id=${unsplashApiKey}&per_page=${limit}&orientation=landscape`
      )
      .then((data) => data.data);

    const data = response.results.map((imageData) => ({
      url: `${imageData.urls.raw}&w=1920`,
    }));

    return data;
  } catch (error) {
    logger.error(error);
    return [];
  }
};
