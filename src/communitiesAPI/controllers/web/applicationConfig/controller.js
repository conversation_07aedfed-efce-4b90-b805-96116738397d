const logger = require('../../../../services/logger.service');
const {
  getActiveCommunityApplicationConfig,
} = require('../../../services/web/communityApplicationConfig.service');

const getCommunityApplicationConfig = async (req, res, next) => {
  try {
    const params = {
      communityId: req.params.communityId,
      adminEmail: req.user.email,
    };
    res.json(await getActiveCommunityApplicationConfig(params));
  } catch (err) {
    logger.info('Error in getCommunityApplicationConfig: ', err);
    const error = new Error('Error in getCommunityApplicationConfig');
    error.status = 400;
    return next(error);
  }
};

module.exports = {
  getCommunityApplicationConfig,
};
