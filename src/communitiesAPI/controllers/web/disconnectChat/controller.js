const status = require('http-status');
const yup = require('yup');
const {
  disconnectCommunityChatService,
} = require('../../../services/web');
const logger = require('../../../../services/logger.service');

const disconnectCommunityChatSchema = yup.object().shape({
  chatType: yup.string().required(),
});

const disconnectCommunityChat = async (req, res, next) => {
  try {
    let isBodyValid = await disconnectCommunityChatSchema.isValid(
      req.body
    );
    if (!isBodyValid) {
      logger.info('Body is invalid: ', req.body);
      const error = new Error('Invalid Body type');
      error.status = status.BAD_REQUEST;
      return next(error);
    }
    const { communityId } = req?.params;
    const { chatType } = req?.body;
    const user = req?.user;
    const chatDisconnected =
      await disconnectCommunityChatService.disconnectChat({
        communityId,
        chatType,
        disconnectedBy: user?.email || 'Community Admin',
      });
    return res.status(status.OK).json({ data: chatDisconnected });
  } catch (err) {
    logger.error('Error occured when removing member:', err, err.stack);
    const error = new Error(err || 'Error occured when removing member');
    error.status = err.status || 500;
    return next(error);
  }
};

const disconnectCommunityAllChat = async (req, res, next) => {
  try {
    const { communityId } = req?.params;
    const user = req?.user;
    const chatDisconnected =
      await disconnectCommunityChatService.disconnectAllChat(
        communityId,
        user?.email ?? 'Cops Portal Admin'
      );
    return res.status(status.OK).json({ data: chatDisconnected });
  } catch (err) {
    logger.error('Error occured when disconnecting chat:', err, err.stack);
    const error = new Error(
      err || 'Error occured when disconnecting chat'
    );
    error.status = err.status || 500;
    return next(error);
  }
};

module.exports = {
  disconnectCommunityChat,
  disconnectCommunityAllChat,
};
