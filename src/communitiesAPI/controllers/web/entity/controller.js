const yup = require('yup');
const status = require('http-status');
const axios = require('../../../../clients/axios.client');
const logger = require('../../../../services/logger.service');
const { communityEntityService } = require('../../../services/web');
const { getConfigByType } = require('../../../../services/config.service');
const {
  getUserIP,
  getUserAgent,
} = require('../../../../utils/headers.util');
const {
  CONFIG_TYPES,
  ACCESS_TOKEN_TYPE,
} = require('../../../../constants/common');
const {
  MAIN_PAYMENT_BACKEND_URL,
  LP_MAIN_WEBSITE_AUTH_KEY,
  authCookieDomain,
  cookieTokenMaxAgeMS,
  cookieRefreshTokenMaxAgeMS,
} = require('../../../../config');
const { ToUserError } = require('../../../../utils/error.util');
const {
  EVENT_ERROR,
  GENERIC_ERROR,
} = require('../../../../constants/errorCode');

const entitySignUpSchema = yup.object().shape({
  entityId: yup.string().trim().required(),
  entityType: yup.string().required(),
  timezone: yup.string().notRequired(),
});

const entitySignUp = async (req, res, next) => {
  const isBodyValid = await entitySignUpSchema.isValid(req.body);
  if (!isBodyValid) {
    const error = new Error('Invalid Body Params');
    error.status = status.BAD_REQUEST;
    return next(error);
  }
  const ip = getUserIP(req) || null;
  const userAgent = getUserAgent(req) || null;
  const {
    entityType = null,
    entityId = null,
    timezone = 'UTC',
  } = req?.body;
  if (!entityId) {
    logger.error(
      'Error signing up to community entity due to Invalid Parameters'
    );
    const error = new Error('Invalid Parameters');
    error.status = status.BAD_REQUEST;
    return next(error);
  }
  const email = req?.user?.email;
  let LP_BE_MW_AUTH_KEY = LP_MAIN_WEBSITE_AUTH_KEY;
  if (!LP_BE_MW_AUTH_KEY) {
    const { envVarData = null } = await getConfigByType(
      CONFIG_TYPES.LEARN_PORTAL_BACKEND_ENV_CONFIG_TYPE
    );
    LP_BE_MW_AUTH_KEY = envVarData?.LP_MAIN_WEBSITE_AUTH_KEY;
  }
  const axiosConfig = {
    method: 'POST',
    url: `${MAIN_PAYMENT_BACKEND_URL}/api/v1/community-entity-checkout`,
    headers: {
      Authorization: `Bearer ${LP_BE_MW_AUTH_KEY}`,
      'X-requester-service-name': 'Learn Portal Backend',
      'X-client-ip-address': ip,
      'X-client-user-agent': userAgent,
    },
    data: {
      entityCollection: entityType,
      entityObjectId: entityId,
      email,
      timezone,
    },
  };
  let statusCode;
  let result;
  try {
    const axiosResponse = await axios(axiosConfig);
    result = { data: axiosResponse?.data };
    statusCode = status.CREATED;
  } catch (error) {
    const errorData = error.response.data;
    const errorCode = errorData.error;
    errorData.name = errorCode;
    let userError = new ToUserError(errorData.message, errorData);
    if (errorCode === EVENT_ERROR.CANNOT_REGISTER_PAST_EVENT.name) {
      userError = new ToUserError(
        errorData.message,
        EVENT_ERROR.CANNOT_REGISTER_PAST_EVENT
      );
    } else if (errorCode === EVENT_ERROR.EVENT_SOLD_OUT.name) {
      userError = new ToUserError(
        errorData.message,
        EVENT_ERROR.EVENT_SOLD_OUT
      );
    }
    return next(userError);
  }
  logger.info('Community Entity Signup Result: ', result);
  return res.status(statusCode).json(result);
};

const entityStripeCheckout = async (req, res, next) => {
  const ip = getUserIP(req) || null;
  const userAgent = getUserAgent(req) || null;
  const {
    entitySignupId = null,
    entityType = null,
    couponCode = null,
  } = req?.body;
  if (!entitySignupId || !entityType) {
    logger.error(
      'Error community entity stripe checkout due to Invalid Path Parameters'
    );
    const error = new Error('Invalid Path Parameters');
    error.status = status.BAD_REQUEST;
    return next(error);
  }
  let LP_BE_MW_AUTH_KEY = LP_MAIN_WEBSITE_AUTH_KEY;
  if (!LP_BE_MW_AUTH_KEY) {
    const { envVarData = null } = await getConfigByType(
      CONFIG_TYPES.LEARN_PORTAL_BACKEND_ENV_CONFIG_TYPE
    );
    LP_BE_MW_AUTH_KEY = envVarData?.LP_MAIN_WEBSITE_AUTH_KEY;
  }
  const axiosConfig = {
    method: 'POST',
    url: `${MAIN_PAYMENT_BACKEND_URL}/api/v1/community-entity/stripe-checkout`,
    headers: {
      Authorization: `Bearer ${LP_BE_MW_AUTH_KEY}`,
      'X-requester-service-name': 'Learn Portal Backend',
      'X-client-ip-address': ip,
      'X-client-user-agent': userAgent,
    },
    data: {
      entitySignupId,
      entityCollection: entityType,
      couponCode,
    },
  };
  let statusCode;
  let result;
  try {
    const axiosResponse = await axios(axiosConfig);
    result = { data: axiosResponse?.data };
    statusCode = status.CREATED;
  } catch (error) {
    const errorData = {};
    errorData.message =
      error?.response?.data?.info ||
      new Error('Error getting community entity stripe checkout');
    errorData.status = error?.response?.data?.status || status.BAD_REQUEST;
    return next(errorData);
  }
  logger.info('Event Stripe Checkout Result: ', result);
  return res.status(statusCode).json(result);
};

const entityXenditEwalletCheckout = async (req, res, next) => {
  const ip = getUserIP(req) || null;
  const userAgent = getUserAgent(req) || null;
  const {
    entitySignupId = null,
    entityType = null,
    paymentMethodType = null,
    successRedirectURL = null,
    failureRedirectURL = null,
    cancelRedirectURL = null,
    mobileNumber = null,
    cashtag = null,
    couponCode = null,
  } = req?.body;
  if (!entitySignupId) {
    logger.error(
      'Error xendit ewallet community entity checkout due to Invalid Path Parameters'
    );
    const error = new Error('Invalid Path Parameters');
    error.status = status.BAD_REQUEST;
    return next(error);
  }
  if (!paymentMethodType) {
    logger.error(
      'Error xendit ewallet community entity checkout due to Invalid paymentMethodType'
    );
    const error = new Error('Invalid Parameters');
    error.status = status.BAD_REQUEST;
    return next(error);
  }
  let LP_BE_MW_AUTH_KEY = LP_MAIN_WEBSITE_AUTH_KEY;
  if (!LP_BE_MW_AUTH_KEY) {
    const { envVarData = null } = await getConfigByType(
      CONFIG_TYPES.LEARN_PORTAL_BACKEND_ENV_CONFIG_TYPE
    );
    LP_BE_MW_AUTH_KEY = envVarData?.LP_MAIN_WEBSITE_AUTH_KEY;
  }
  const axiosConfig = {
    method: 'POST',
    url: `${MAIN_PAYMENT_BACKEND_URL}/api/v1/community-entity/xendit-ewallet-checkout`,
    headers: {
      Authorization: `Bearer ${LP_BE_MW_AUTH_KEY}`,
      'X-requester-service-name': 'Learn Portal Backend',
      'X-client-ip-address': ip,
      'X-client-user-agent': userAgent,
    },
    data: {
      entitySignupId,
      paymentMethodType,
      successRedirectURL,
      failureRedirectURL,
      cancelRedirectURL,
      mobileNumber,
      cashtag,
      entityCollection: entityType,
      couponCode,
    },
  };
  let statusCode;
  let result;
  try {
    const axiosResponse = await axios(axiosConfig);
    result = { data: axiosResponse?.data };
    statusCode = status.CREATED;
  } catch (error) {
    const errorData = {};
    errorData.message =
      error?.response?.data?.info ||
      new Error('Error getting community entity xendit ewallet checkout');
    errorData.status = error?.response?.data?.status || status.BAD_REQUEST;
    return next(errorData);
  }
  logger.info('Community Entity Xendit Ewallet Checkout Result: ', result);
  return res.status(statusCode).json(result);
};

const verifyPaidEntityPayment = async (req, res, next) => {
  const statusCode = status.OK;
  let result;
  const { entitySignupId = null } = req?.query;
  const isLoginToken = req.accessTokenType === ACCESS_TOKEN_TYPE.LOGIN;

  try {
    result = await communityEntityService.verifyPaidEntityPayment(
      entitySignupId,
      isLoginToken
    );

    if (result.isFailedPayment) {
      throw new ToUserError(
        result?.error?.message ?? 'Payment is failed',
        result.paymentError ?? GENERIC_ERROR.PAYMENT_FAILURE
      );
    }

    // Set access token to cookie for first time user to auto login and access the purchase
    if (!isLoginToken && result.token) {
      res.cookie('accessTokenNA', result.token, {
        domain: authCookieDomain,
        path: '/',
        sameSite: 'None',
        secure: true,
        httpOnly: false,
        maxAge: cookieTokenMaxAgeMS,
      });
      res.cookie('refreshTokenNA', result.refreshToken, {
        domain: authCookieDomain,
        path: '/',
        sameSite: 'None',
        secure: true,
        httpOnly: false,
        maxAge: cookieRefreshTokenMaxAgeMS,
      });
    }
  } catch (error) {
    return next(error);
  }
  logger.info(
    'Paid Community Entity Payment Verification Result: ',
    result
  );
  return res.status(statusCode).json(result);
};

module.exports = {
  entitySignUp,
  entityStripeCheckout,
  entityXenditEwalletCheckout,
  verifyPaidEntityPayment,
};
