const { communityVideosService } = require('../../../services/web');
const logger = require('../../../../services/logger.service');
const utils = require('../../../utils');

const getCommunityVideos = async (req, res, next) => {
  try {
    // eslint-disable-next-line no-param-reassign
    res = utils.addSignedCookiesToResponse(res);
    const communityVideos =
      await communityVideosService.getCommunityVideos(
        req.params.communityId
      );

    res.json(communityVideos);
  } catch (err) {
    logger.error(
      'Error on get community videos request: ',
      err,
      err.stack
    );
    const error = new Error(`Error on get request: ${err}`);
    error.status = err.status || 500;
    return next(error);
  }
};

module.exports = {
  getCommunityVideos,
};
