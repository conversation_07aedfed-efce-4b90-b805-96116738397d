const ObjectId = require('mongoose').Types.ObjectId;
const {
  communityFoldersService,
  communityFolderAccessLogsService,
} = require('../../../services/web');
const {
  updateFolderViewersAccessDate,
} = require('../../../../services/folder/index');
const logger = require('../../../../services/logger.service');
const status = require('http-status');

const createFolderAccessLogs = async (req, res, next) => {
  try {
    const userData = req.user;
    const folderId = req?.params?.communityFolderId;
    if (!userData) {
      throw new Error('Missing userData');
    }

    const folder = await communityFoldersService.getFolderById(folderId);
    if (!folder) {
      throw new Error(`Folder with id ${folderId} does not exists`);
    }

    const body = {
      communityFolderObjectId: new ObjectId(folderId),
      learnerObjectId: new ObjectId(userData.learner._id),
      communityObjectId: folder.communityObjectId,
    };

    const [folderAccessLog, folderViewer] = await Promise.all([
      communityFolderAccessLogsService.createOneFolderAccessLog(body),
      updateFolderViewersAccessDate(
        folder,
        userData._id,
        userData.learner._id
      ),
    ]);

    res.status(status.CREATED).json(folderViewer);
  } catch (err) {
    logger.error(
      'Error creating community folder access log: ',
      err,
      err.stack
    );
    err.status = err.status || 500;
    next(err);
  }
};

module.exports = {
  createFolderAccessLogs,
};
