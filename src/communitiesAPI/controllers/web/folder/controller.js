const { ObjectId } = require('mongoose').Types;
const httpStatus = require('http-status');
const {
  communityFoldersService,
  communityFolderItemsService,
} = require('../../../services/web');

const manageOneOneService = require('../../../../services/oneOnOneSession/manageOneOnOneSession.service');
const logger = require('../../../../services/logger.service');
const utils = require('../../../utils');
const {
  getAddonPriceInLocalCurrency,
} = require('../../../services/common/communityAddonPrice.service');
const { getUserIP } = require('../../../../utils/headers.util');
const {
  communityFolderItemTypesMap,
  communityLibraryStatusMap,
  COMMUNITY_FOLDER_PURCHASE_TYPES,
  communityFolderTypesMap,
} = require('../../../constants');
const {
  MILESTONE_ACTIVITY_TYPES,
  PURCHASE_TYPE,
} = require('../../../../constants/common');
const {
  createFolderSchema,
} = require('../../../apiSchemas/communityFolders.schema');
const communityFolderPurchases = require('../../../models/communityFolderPurchases.model');
const communityFolderViews = require('../../../models/communityFolderAccessLogs.model');
const CommunityModel = require('../../../models/community.model');
const ActionEventService = require('../../../../services/actionEvent');
const communityFoldersModel = require('../../../models/communityFolders.model');
const {
  ResourceNotFoundError,
  ParamError,
} = require('../../../../utils/error.util');
const folderService = require('../../../../services/folder');
const {
  affiliateProductService,
} = require('../../../../services/affiliate');
const folderCommonService = require('../../../services/common/communityFolders.service');
const {
  generateCoverMediaItems,
} = require('../../../../utils/multipleCoverMediaItems.util');
const {
  COVER_MEDIA_ENTITY_TYPES,
} = require('../../../../constants/coverMediaItems.constant');
const {
  getEntityCampaignInfo,
} = require('../../../../services/magicAudience/magicAudienceCampaign.service');
const {
  purgeEntityLandingPageCache,
  ENTITY_LANDING_PAGE,
} = require('../../../../utils/memberPortalLinks.utils');

const getResourceBySlug = async (req, res, next) => {
  try {
    const { communityLink, slug } = req.params;
    const ip = getUserIP(req) || null;
    const learnerObjectId = req.user?.learner?._id;
    const {
      selectedAmount,
      affiliateCode,
      paymentMethodCountryCode,
      paymentProvider,
    } = req.query;

    const resource = await communityFolderItemsService.getResourceBySlug(
      communityLink,
      slug,
      ip,
      learnerObjectId,
      selectedAmount,
      affiliateCode,
      paymentMethodCountryCode,
      paymentProvider
    );

    res.json(resource);
  } catch (err) {
    logger.error('Error on get Event by slug request: ', err, err.stack);
    const error = new Error('Error on get request');
    error.status = err.status || 500;
    return next(error);
  }
};

const getFolders = async (req, res, next) => {
  try {
    // eslint-disable-next-line no-param-reassign
    res = utils.addSignedCookiesToResponse(res);
    let isCommunityManager = false;
    if (req.user) {
      isCommunityManager =
        req.user.community_admin === true ||
        req.user.roles?.manager === true;
    }
    const filter = req?.query?.type ? { type: req.query.type } : {};
    let { paginate = 0, pageNum = null, pageSize = null } = req?.query;
    paginate = parseInt(paginate, 10) || 0;
    if (paginate === 1) {
      pageNum = parseInt(pageNum, 10) || 1;
      pageSize = parseInt(pageSize, 10) || 10;
    }
    const folders = await communityFoldersService.getFoldersByCommunityId(
      req?.params?.communityId,
      isCommunityManager,
      req?.user?.learner?._id,
      filter,
      paginate,
      pageNum,
      pageSize
    );
    res.json(folders);
  } catch (err) {
    logger.error('Error on get community folders request: ', err);
    err.status = httpStatus.INTERNAL_SERVER_ERROR;
    return next(err);
  }
};

const getProducts = async (req, res, next) => {
  try {
    // eslint-disable-next-line no-param-reassign
    res = utils.addSignedCookiesToResponse(res);
    let isCommunityManager = false;
    if (req.user) {
      isCommunityManager = req.user.isCommunityManager;
    }
    const filter = req?.query?.type ? { type: req.query.type } : {};
    let { paginate = 0, pageNum = null, pageSize = null } = req?.query;
    paginate = parseInt(paginate, 10) || 0;
    if (paginate === 1) {
      pageNum = parseInt(pageNum, 10) || 1;
      pageSize = parseInt(pageSize, 10) || 10;
    }
    const folders = await communityFoldersService.getFoldersByCommunityId(
      req?.params?.communityId,
      isCommunityManager,
      req?.user?.learner?._id,
      filter,
      paginate,
      pageNum,
      pageSize
    );
    res.json(folders);
  } catch (err) {
    logger.error('Error on get community product request: ', err);
    err.status = err.status || 500;
    return next(err);
  }
};

const getFolderById = async (req, res, next) => {
  try {
    const ip = getUserIP(req) || null;
    const { communityId, isPublicPage = false } = req.params;
    const { timezone } = req.query;
    const {
      selectedAmount,
      affiliateCode,
      paymentMethodCountryCode,
      paymentProvider,
    } = req.query;

    if (req?.user) {
      // eslint-disable-next-line no-param-reassign
      res = utils.addSignedCookiesToResponse(res);
    }

    const { isCommunityManager = false } = req?.user ?? {};

    const folderInfo = await communityFoldersModel
      .findById(req?.params?.communityFolderId)
      .select('type');

    if (!folderInfo) {
      throw new ResourceNotFoundError('Folder not found');
    }

    let isSession = false;
    let folder = null;
    if (folderInfo.type === communityFolderTypesMap.SESSION) {
      folder = await manageOneOneService.getOneOnOneSession(
        req.user?.learner?._id,
        req?.params?.communityFolderId,
        isPublicPage,
        timezone,
        isCommunityManager
      );
      isSession = true;
    } else {
      folder = await communityFoldersService.getFolderById(
        req?.params?.communityFolderId,
        isCommunityManager,
        { communityObjectId: new ObjectId(communityId) },
        { affiliateCode }
      );
    }
    let { paginate = 0, pageNum = null, pageSize = null } = req?.query;
    paginate = parseInt(paginate, 10) || 0;
    if (paginate === 1) {
      pageNum = parseInt(pageNum, 10) || 1;
      pageSize = parseInt(pageSize, 10) || 10;
    }
    let result = {};
    if (folder && !isSession) {
      result = folder;
      if (result?.access === COMMUNITY_FOLDER_PURCHASE_TYPES.PAID) {
        const priceDetails = await getAddonPriceInLocalCurrency({
          ip,
          addon: folder,
          communityObjectId: communityId,
          selectedAmount,
          paymentMethodCountryCode,
          paymentProvider,
        });
        logger.info('Price in local currency: ', priceDetails);
        result = { priceDetails, ...folder };
      }
      const isPurchased = await communityFolderPurchases.count({
        learnerObjectId: new ObjectId(req?.user?.learner?._id),
        folderObjectId: new ObjectId(folder._id),
      });
      result['isPurchased'] = Boolean(isPurchased);
      if (isCommunityManager) result['isPurchased'] = true;
      const folderViewCount = await communityFolderViews.count({
        communityFolderObjectId: new ObjectId(folder._id),
      });
      result['folderViewCount'] = folderViewCount;
      const priceFieldsToRemove = [
        'localiseForAllCountries',
        'countryWisePrice',
      ];
      priceFieldsToRemove.forEach(
        (priceFieldToRemove) => delete result[priceFieldToRemove]
      );
      const folderItems =
        await communityFolderItemsService.getFolderItemsByFolderId(
          folder?._id,
          isCommunityManager,
          {},
          paginate,
          pageNum,
          pageSize
        );
      let totalItemsCount = 0;
      folderItems.forEach((folderItem) => {
        if (folderItem.type === 'section') {
          totalItemsCount += folderItem.items.length;
        } else {
          totalItemsCount += 1;
        }
      });
      let folderItemData =
        paginate === 1 ? folderItems?.data : folderItems;
      const videoCount = folderItemData.filter(
        (i) => i?.type === communityFolderItemTypesMap.VIDEO
      ).length;

      if (
        !result.isPurchased &&
        result.access === COMMUNITY_FOLDER_PURCHASE_TYPES.PAID
      ) {
        const updatedFolderItemData = folderItemData.map((item) => {
          const { link, mp4Link, videoObjectId, video, ...rest } = item;
          return rest;
        });
        folderItemData = updatedFolderItemData;
      }
      result.videoCount = videoCount;
      result.otherFolderItemCount = folderItemData?.length - videoCount;
      result.totalItemsCount = totalItemsCount;
      result.folderItems = folderItemData;
    } else if (isSession) {
      result = folder;
      if (result.access === COMMUNITY_FOLDER_PURCHASE_TYPES.PAID) {
        const priceDetails = await getAddonPriceInLocalCurrency({
          ip,
          addon: folder,
          communityObjectId: communityId,
          selectedAmount,
          paymentMethodCountryCode,
          paymentProvider,
        });
        logger.info('Price in local currency: ', priceDetails);
        result = { priceDetails, ...folder };
        result['isPurchased'] = true;
        const priceFieldsToRemove = [
          'localiseForAllCountries',
          'countryWisePrice',
        ];
        priceFieldsToRemove.forEach(
          (priceFieldToRemove) => delete result[priceFieldToRemove]
        );
        const folderViewCount = await communityFolderViews.count({
          communityFolderObjectId: new ObjectId(folder._id),
        });
        result['folderViewCount'] = folderViewCount;
      }
    } else {
      logger.error('No folder found ');
      throw new Error('Error No folder found');
    }

    result.campaignInfo =
      (await getEntityCampaignInfo({
        entityObjectId: result._id,
        entityType: !isSession ? 'folder' : 'session',
      })) || null;
    res.status(200).json(result);
  } catch (err) {
    logger.error(
      'Error on get community folder by id request',
      err,
      err.stack
    );
    err.status = err.status || 500;
    return next(err);
  }
};

const getFilteredLibraryFromCommunity = async (req, res, next) => {
  try {
    const { communityId } = req.params;
    const { filter } = req.query;
    const { isCommunityManager } = req.user;
    const result = [];
    const folders =
      await communityFoldersService.getFilteredFoldersByCommunityId(
        communityId,
        isCommunityManager,
        filter
      );
    result.push(...folders);
    const folderItems =
      await communityFolderItemsService.getFilteredFolderItemsByCommunityId(
        communityId,
        isCommunityManager,
        filter
      );
    result.push(...folderItems);
    res.json(result);
  } catch (err) {
    logger.error('Error searching library from the community: ', err);
    err.status = httpStatus.INTERNAL_SERVER_ERROR;
    return next(err);
  }
};

const createOneFolder = async (req, res, next) => {
  const input = req.body;
  const { communityId } = req.params;
  input.communityObjectId = communityId;
  const { learner } = req.user;
  const isQueryValid = await createFolderSchema.isValid(input);
  if (!isQueryValid) {
    const error = new Error('Invalid Parameters');
    return next(error);
  }
  const body = createFolderSchema.cast(input);

  try {
    if (req.file) {
      body.thumbnail = req.file.location;
    }
    const listOfFolders =
      await communityFoldersService.getFoldersByCommunityId(
        communityId,
        true
      );

    body.index = listOfFolders.length + 1;

    const folder = await communityFoldersService.createOneFolder({
      ...body,
      createdByLearnerObjectId: learner._id,
      communityObjectId: communityId,
      createdBy: req.user.email,
    });

    res.json(folder);
  } catch (err) {
    logger.error('Error creating folder from the community: ', err);
    err.status = httpStatus.INTERNAL_SERVER_ERROR;
    return next(err);
  }
};

const updateOneFolder = async (req, res, next) => {
  const input = {
    ...req.body,
    communityObjectId: req.params.communityId,
  };
  const { isCommunityManager } = req.user;
  const { communityFolderId } = req.params;
  const isQueryValid = await createFolderSchema.isValid(input);
  if (!isQueryValid) {
    const error = new Error('Invalid Parameters');
    return next(error);
  }
  const body = {
    ...createFolderSchema.cast(input),
    ...(req.file && { thumbnail: req.file.location }),
    createdBy: req.user.email,
  };

  try {
    const folder = await communityFoldersService.updateOneFolder(
      { _id: new ObjectId(communityFolderId) },
      body
    );
    const folderItems =
      await communityFolderItemsService.getFolderItemsByFolderId(
        communityFolderId,
        isCommunityManager
      );

    if (folder.coverMediaItems && folder.coverMediaItems.length > 0) {
      folder.coverMediaItems = await generateCoverMediaItems({
        entity: folder,
        entityType: COVER_MEDIA_ENTITY_TYPES.FOLDER,
        isCommunityManager: true,
      });
    }
    const videoCount = folderItems.filter(
      (i) => i.type === communityFolderItemTypesMap.VIDEO
    ).length;
    const totalItemsCount = folderItems?.length;
    const result = {
      ...folder,
      videoCount,
      totalItemsCount,
      otherFolderItemCount: folderItems.length - videoCount,
      folderItems,
    };

    res.json(result);
  } catch (err) {
    logger.error('Error updating folder from the community: ', err);
    err.status = httpStatus.INTERNAL_SERVER_ERROR;
    return next(err);
  }
};

const updateFolderIndices = async (req, res, next) => {
  const communityFolders = req.body;
  const updated = [];
  await Promise.all(
    // eslint-disable-next-line array-callback-return
    communityFolders.map(async (folder) => {
      const { communityFolderId, index } = folder;
      try {
        const updatedFolder = await communityFoldersService.patchOneFolder(
          communityFolderId,
          { index },
          false
        );
        updated.push(updatedFolder);
      } catch (err) {
        logger.error('Error patching folder from the community: ', err);
        err.status = httpStatus.INTERNAL_SERVER_ERROR;
        return next(err);
      }
    })
  );
  res.json(updated);
};

const deleteOneFolder = async (req, res, next) => {
  const { communityId, communityFolderId } = req.params;

  try {
    const folderResult =
      await folderService.softDeleteFolderAndFolderItems(
        communityFolderId,
        communityId
      );

    res.json(folderResult);
  } catch (err) {
    logger.error('Error deleting folder from the community: ', err);
    return next(err);
  }
};

const saveAndPublishChanges = async (req, res, next) => {
  try {
    const { status } = req.body;
    const { communityId, communityFolderId } = req.params;
    const { isCommunityManager, learner } = req.user;
    const [folder, community, publishedFolderCount] = await Promise.all([
      communityFoldersService.getFolderById(communityFolderId, true, {
        communityObjectId: communityId,
      }),
      CommunityModel.findById(communityId, {
        code: 1,
        link: 1,
      }).lean(),
      communityFoldersService.getPublishedFolderCount(communityId),
    ]);
    const oldStatus = folder.status;

    let result = {};
    if (!folder || !status) {
      throw new Error(
        `Error in saving and publishing folder. Folder with id ${communityFolderId} does not exists in community ${communityId}`
      );
    }
    if (status === communityLibraryStatusMap.PUBLISHED) {
      result = await communityFoldersService.patchOneFolder(
        communityFolderId,
        { isDraft: false, status },
        true
      );

      result.discountsApplied = folder.discountsApplied;

      const {
        matchedCount: folderItems,
        modifiedCount: folderItemsModified,
      } = await communityFolderItemsService.patchManyFolderItemsByFolderId(
        communityFolderId,
        { status },
        { status: communityLibraryStatusMap.UNPUBLISHED }
      );

      result = { ...result, folderItems, folderItemsModified };
      // enrol manager to folder if paid
      if (
        isCommunityManager &&
        result?.access === COMMUNITY_FOLDER_PURCHASE_TYPES?.PAID
      ) {
        await communityFolderPurchases.findOneAndUpdate(
          {
            learnerObjectId: new ObjectId(req?.user?.learner?._id),
            folderObjectId: new ObjectId(folder._id),
          },
          {
            learnerObjectId: new ObjectId(req?.user?.learner?._id),
            folderObjectId: new ObjectId(folder._id),
            purchaseType: COMMUNITY_FOLDER_PURCHASE_TYPES.FREE,
          },
          {
            new: true,
          }
        );
      }

      await ActionEventService.sendMilestoneEvent({
        actionEventType: MILESTONE_ACTIVITY_TYPES.MILESTONE_FIRST_CONTENT,
        communityCode: community.code,
        communityObjectId: communityId,
        learnerObjectId: learner._id,
      });
    }

    if (status === communityLibraryStatusMap.UNPUBLISHED) {
      result = await communityFoldersService.patchOneFolder(
        communityFolderId,
        { isDraft: true, status },
        true
      );

      result.discountsApplied = folder.discountsApplied;

      const {
        matchedCount: folderItems,
        modifiedCount: folderItemsModified,
      } = await communityFolderItemsService.patchManyFolderItemsByFolderId(
        communityFolderId,
        { status },
        { status: communityLibraryStatusMap.PUBLISHED }
      );

      result = { ...result, folderItems, folderItemsModified };

      const entityType =
        result.type === communityFolderTypesMap.SESSION
          ? PURCHASE_TYPE.SESSION
          : PURCHASE_TYPE.FOLDER;

      await affiliateProductService.disableAffiliateProduct({
        communityObjectId: communityId,
        entityType,
        entityObjectId: communityFolderId,
      });
    }

    // purge landing page cache
    const isStatusUpdated = oldStatus !== result.status;
    if (isStatusUpdated) {
      const isPublishAction =
        result.status === communityLibraryStatusMap.PUBLISHED;

      const purgeCommunityLandingPage = isPublishAction
        ? publishedFolderCount === 0 // i.e first folder to be published in the community ( for community landing page tabs )
        : publishedFolderCount === 1; // i.e unpublishing the only published folder ( for community landing page tabs )

      await purgeEntityLandingPageCache({
        community,
        purgeCommunityLandingPage,
        entityType: ENTITY_LANDING_PAGE[folder.type],
        entitySlug: folder.resourceSlug,
      });
    }

    result = {
      ...result,
      ...(await communityFolderItemsService.getFolderItemsByIdWithMetaData(
        communityFolderId,
        true
      )),
    };

    res.json(result);
  } catch (err) {
    logger.error(
      'Error in save and publishing folder from the community: ',
      err
    );
    err.status = httpStatus.INTERNAL_SERVER_ERROR;
    return next(err);
  }
};

const reorderFoldersItems = async (req, res, next) => {
  try {
    const { communityFolderId } = req.params;
    const { folderItems } = req.body;

    logger.info(
      `reorderingFolderItemsController: reordering the folderItems for ${communityFolderId} with folderItems length ${folderItems.length}`
    );
    const result = await communityFolderItemsService.reorderFoldersItems(
      communityFolderId,
      folderItems
    );

    logger.info(
      'reorderingFolderItemsController: result of reordering the folderItems for the folderId ',
      communityFolderId,
      ' is',
      result
    );
    res.status(httpStatus.OK).json(result);
  } catch (err) {
    logger.error('Error in reorder folder items: ', err, err.stack);
    err.status = httpStatus.INTERNAL_SERVER_ERROR;
    return next(err);
  }
};

// this controller is invoked by the event bridge when a status on the video transcoding changes
const updateVideoUploadStatus = async (req, res) => {
  try {
    logger.info('updateVideoUploadStatus: req.body', req.body);
    const { detail } = req.body || {};
    const updatedStatus =
      await communityFolderItemsService.updateVideoFileStatus({ detail });
    // front end doesn't call this API so no need to structure the API response
    res.status(httpStatus.OK).json(updatedStatus);
  } catch (err) {
    logger.error('Error setting video upload status: ', err, err.stack);
    err.status = httpStatus.INTERNAL_SERVER_ERROR;
    res.status(httpStatus.INTERNAL_SERVER_ERROR).json(err);
  }
};

const retrieveCommunityStorage = async (req, res) => {
  const { communityId } = req.params;

  const community = await CommunityModel.findOne(
    {
      _id: communityId,
    },
    { communityVideoSizeLimit: 1 }
  ).lean();

  if (!community) {
    throw new ParamError(`Community with id ${communityId} not found`);
  }

  const { communityVideoSizeLimit } = community;

  if (communityVideoSizeLimit) {
    try {
      const currentMemoryInfo =
        await communityFolderItemsService.getCurrentVideoItemsSizeByCommunityId(
          communityId,
          communityVideoSizeLimit
        );

      const availableStorageInMb = Math.floor(
        (currentMemoryInfo?.currentMemoryAvailable ?? 0) * 1000
      );

      res.json({
        data: {
          availableStorageInMb,
          usedStorageInMb: communityVideoSizeLimit - availableStorageInMb,
          totalStorageInMb: communityVideoSizeLimit,
        },
      });
    } catch (err) {
      logger.error(
        `Error getting the current video items size for the community ${communityId} error ${err}`
      );
      res.json({
        data: {
          availableStorageInMb: 0,
          totalStorageInMb: communityVideoSizeLimit,
        },
      });
    }
  }
};

const checkCommunityStorage = async (req, res) => {
  try {
    const { uploadingFileSize, communityId } = req.params;
    // get the current size of the community using a covered query (only the communityVideoSizeLimit field is returned)
    const community = await CommunityModel.findOne(
      {
        _id: new ObjectId(communityId),
      },
      { communityVideoSizeLimit: 1, _id: 0 }
    );
    // if the community has a video size limit, check if the current size of the community is less than the limit
    const { communityVideoSizeLimit } = community || {};
    logger.info(
      `The community ${communityId} has the video size limit of ${communityVideoSizeLimit}`
    );
    let canProceedToUploadTheFile = false;
    let availableStorage;
    logger.info(
      `checking if we have crossed the community size limit for the community Id ${communityId}`
    );
    if (communityVideoSizeLimit) {
      try {
        const currentMemoryInfo =
          await communityFolderItemsService.getCurrentVideoItemsSizeByCommunityId(
            communityId,
            communityVideoSizeLimit,
            Number(uploadingFileSize)
          );
        canProceedToUploadTheFile =
          currentMemoryInfo?.canProceedToUploadTheFile;
        availableStorage = Number(
          currentMemoryInfo?.currentMemoryAvailable
        );
        logger.info(
          `The community ${communityId} has the video size limit of ${communityVideoSizeLimit} and status of permission to upload = ${canProceedToUploadTheFile}`
        );
      } catch (err) {
        logger.error(
          `Error getting the current video items size for the community ${communityId} error ${err}`
        );
      }
    }
    if (!canProceedToUploadTheFile) {
      logger.info(
        `The community ${communityId} has reached its limit for uploading videos ${communityVideoSizeLimit}`
      );
      // Unable to upload due to storage limit. You have {x gb} letft. Try compressing the files or upload them one by one.
      const error = new Error(
        `Unable to upload due to storage limit. You have ${availableStorage.toFixed(
          2
        )} gb left.  Try compressing the files or upload them one by one`
      );
      error.status = 418; // status.I_AM_A_TEAPOT;
      throw error;
    }
    res.status(httpStatus.OK).json({
      data: canProceedToUploadTheFile,
      message: 'Successfully checked community storage and can proceed',
    });
  } catch (error) {
    logger.error('Error checking community storage: ', error, error.stack);

    error.status = error.status || httpStatus.INTERNAL_SERVER_ERROR;
    return res.status(error.status).json({
      data: null,
      errorCode: error?.status || httpStatus.INTERNAL_SERVER_ERROR,
      errorMessage: error?.message || 'Error checking community storage',
      error,
    });
  }
};

const autoPurchaseFolder = async (req) => {
  const { communityFolderId: folderObjectId } = req.params;
  const { learnerObjectId, addonTransactionObjectId } = req.body;

  const result = await folderCommonService.autoPurchaseFolder({
    learnerObjectId,
    folderObjectId,
    addonTransactionObjectId,
  });

  return result;
};

module.exports = {
  getFolders,
  getProducts,
  getFolderById,
  getFilteredLibraryFromCommunity,
  createOneFolder,
  updateOneFolder,
  deleteOneFolder,
  updateFolderIndices,
  saveAndPublishChanges,
  getResourceBySlug,
  reorderFoldersItems,
  updateVideoUploadStatus,
  checkCommunityStorage,
  autoPurchaseFolder,
  retrieveCommunityStorage,
};
