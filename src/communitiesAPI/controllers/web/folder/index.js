const {
  getFolders,
  getProducts,
  getFolderById,
  getFilteredLibraryFromCommunity,
  createOneFolder,
  updateOneFolder,
  reorderFoldersItems,
  deleteOneFolder,
  updateFolderIndices,
  saveAndPublishChanges,
  getResourceBySlug,
  updateVideoUploadStatus,
  checkCommunityStorage,
  autoPurchaseFolder,
  retrieveCommunityStorage,
} = require('./controller');

module.exports = {
  getFolders,
  getProducts,
  getFolderById,
  getFilteredLibraryFromCommunity,
  createOneFolder,
  updateOneFolder,
  reorderFoldersItems,
  deleteOneFolder,
  updateFolderIndices,
  saveAndPublishChanges,
  getResourceBySlug,
  updateVideoUploadStatus,
  checkCommunityStorage,
  autoPurchaseFolder,
  retrieveCommunityStorage,
};
