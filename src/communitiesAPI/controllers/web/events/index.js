const {
  getCommunityEvents,
  getUpcomingEventsByCommunityId,
  getPastEventsByCommunityId,
  getEventById,
  getEventBySlug,
  registerForEvent,
  registerForEventV2,
  deleteEvent,
  updateEvent,
  createEvent,
  sendEmailToRsvpUsers,
  eventSignUp,
  eventStripeCheckout,
  eventXenditEwalletCheckout,
  eventFreeCheckout,
  verifyPaidEventPayment,
  getEventRSVPList,
  getUpcomingPaidEventsForLearner,
  getEventByIdForAdmin,
  checkAttendees,
} = require('./controller');

module.exports = {
  getCommunityEvents,
  getUpcomingEventsByCommunityId,
  getPastEventsByCommunityId,
  getEventById,
  getEventBySlug,
  registerForEvent,
  registerForEventV2,
  deleteEvent,
  updateEvent,
  createEvent,
  sendEmailToRsvpUsers,
  eventSignUp,
  eventStripeCheckout,
  eventXenditEwalletCheckout,
  eventF<PERSON>Checkout,
  verifyPaidEventPayment,
  getEventRSVPList,
  getUpcomingPaidEventsFor<PERSON>earner,
  getEventByIdForAdmin,
  checkAttende<PERSON>,
};
