const yup = require('yup');
const status = require('http-status');
const ObjectId = require('mongoose').Types.ObjectId;
const logger = require('../../../../services/logger.service');
const {
  communityAnalyticsService,
  communityService,
  communityRevenueService,
} = require('../../../services/web');

const getAnalytics = async (req, res, next) => {
  try {
    const communityId = req.params.communityId;
    const community = await communityService.getCommunitiesByIds([
      communityId,
    ]);
    if (!community || community.length === 0) {
      logger.error('Community does not exists');
      throw new Error('Community does not exists');
    }
    if (community[0]?.isDemo) {
      const demoAnalytics =
        await communityAnalyticsService.getDemoAnalytics();
      return res.status(status.OK).json(demoAnalytics);
    }

    let result = {
      members: {
        total: 0,
        new: 0,
        unsubscribed: 0,
      },
      revenue: 0,
      countries: 0,
    };

    const {
      members: totalMembers,
      lastModifiedTimeStamp,
      exists,
    } = await communityAnalyticsService.getLatestFigure(communityId);

    if (!exists) {
      logger.info(
        `Can't find overall figures from analytics for commmunity ${communityId}`
      );
    } else {
      const thirtyDayMembers =
        await communityAnalyticsService.getLastThirtyDaysMembers(
          communityId
        );
      const countries = await communityAnalyticsService.getCountriesData(
        communityId
      );
      result['members'] = {
        total: totalMembers?.active,
        new: thirtyDayMembers?.netNew,
        unsubscribed: thirtyDayMembers?.unsubscribed,
      };
      result['countries'] = countries;
      result['lastModifiedTimeStamp'] = lastModifiedTimeStamp;
    }

    const now = new Date();
    const currentMonth = now.getMonth();
    const currentMonthYear = now.getFullYear();
    let previousMonth = currentMonth - 1;
    let previousMonthYear = now.getFullYear();
    if (currentMonth === 0) {
      previousMonthYear -= 1;
      previousMonth = 11;
    }

    const previousMonthRevenue =
      await communityRevenueService.calculateRevenueOfCommunity(
        previousMonth,
        previousMonthYear,
        { code: { $in: [community[0]?.code] } }
      );

    const currentMonthRevenue =
      await communityRevenueService.calculateRevenueOfCommunity(
        currentMonth,
        currentMonthYear,
        { code: { $in: [community[0]?.code] } }
      );

    result['revenue'] = {
      currentMonth:
        currentMonthRevenue && currentMonthRevenue.length !== 0
          ? currentMonthRevenue[0]
          : null,
      previousMonth:
        previousMonthRevenue && previousMonthRevenue.length !== 0
          ? previousMonthRevenue[0]
          : null,
    };

    return res.status(status.OK).json(result);
  } catch (ex) {
    return res.status(status.BAD_REQUEST).json({ error: ex });
  }
};

const getAnalyticsGraph = async (req, res, next) => {
  try {
    const communityId = req.params.communityId;

    const community = await communityService.getCommunitiesByIds([
      communityId,
    ]);
    if (community?.length === 0) {
      logger.error('Community does not exists');
      throw new Error('Community does not exists');
    }
    const timePrior = { day: req?.query?.days };
    if (community[0]?.isDemo) {
      const demoGraph = await communityAnalyticsService.getDemoGraph(
        timePrior
      );
      return res.status(status.OK).json(demoGraph);
    }

    const dataPoints = await communityAnalyticsService.getGraphPoints(
      communityId,
      timePrior
    );

    return res.status(status.OK).json(dataPoints);
  } catch (ex) {
    return res.status(status.BAD_REQUEST).json({ error: ex });
  }
};

module.exports = {
  getAnalytics,
  getAnalyticsGraph,
};
