const logger = require('../../../../services/logger.service');
const {
  signupMemberCheckService,
  phoneNumberCheckService,
} = require('../../../services/web');

const signupMemberCheck = async (req, res, next) => {
  try {
    const response = await signupMemberCheckService.communityMemberCheck(
      req.user?._id,
      req.user?.learner?._id,
      req.params?.communityId
    );
    return res.json(response);
  } catch (err) {
    logger.error('SignupMemberCheck Failed due to', err);
    return next(err);
  }
};

const phoneNumberCheck = async (req, res, next) => {
  try {
    const response =
      await phoneNumberCheckService.communityPhoneNumberCheck(
        req.query?.phoneNumber,
        req.query?.communityId
      );
    return res.json(response);
  } catch (err) {
    logger.error('PhoneNumberCheck Failed due to', err);
    return next(err);
  }
};

const updateLearnerPhoneNumber = async (req, res, next) => {
  try {
    const response = await phoneNumberCheckService.updatePhoneNumber(
      req.body?.phoneNumber,
      req.user?.learner?._id
    );
    return res.json(response);
  } catch (err) {
    logger.error('Update Learner PhoneNumber Failed due to', err);
    return next(err);
  }
};

module.exports = {
  signupMemberCheck,
  phoneNumberCheck,
  updateLearnerPhoneNumber,
};
