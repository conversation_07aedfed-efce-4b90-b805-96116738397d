const { ObjectId } = require('mongoose').Types;
const status = require('http-status');
const logger = require('../../../../services/logger.service');
const {
  getManagerTodoProgress,
  getAdditionalManagerTodoProgress,
  validateCompletion,
  updateManagerTodoProgress,
} = require('../../../services/common/communityManagerTodosProgress.service');
const { communityManagerTodoCodeMap } = require('../../../constants');

const getManagerTodosProgress = async (req, res, next) => {
  try {
    const { communityId } = req.params;
    const todoType = req.query.type ? { todoType: req.query.type } : {};
    const result = await getManagerTodoProgress({
      communityObjectId: new ObjectId(communityId),
      ...todoType,
    });

    const extraTodo = await getAdditionalManagerTodoProgress(
      communityManagerTodoCodeMap.COMMUNITY_SETUP_OWNER_PROFILE_PIC,
      req.user
    );

    result.splice(3, 0, extraTodo);
    res.json(result);
  } catch (err) {
    logger.error('Error on get maanagerTodoProgress request: ', err);
    err.status = status.BAD_REQUEST;
    return next(err);
  }
};

const updateManagerTodosProgress = async (req, res, next) => {
  const { communityId, todoCode } = req.params;
  let result;
  try {
    const validation = await validateCompletion(
      todoCode,
      communityId,
      'desktop'
    );

    if (validation) {
      result = await updateManagerTodoProgress(
        {
          communityObjectId: new ObjectId(communityId),
          todoCode,
        },
        { completed: validation }
      );

      if (!result) {
        logger.error(
          `None updated as there is no manager todos progress with the given params`
        );
        const error = new Error(
          `None updated as there is no manager todos progress with the given params`
        );
        error.status = status.BAD_REQUEST;
        throw error;
      }
    } else {
      result = 'Validation is false. No change needed';
    }

    res.json(result);
  } catch (err) {
    logger.error('Error updating ManagerTodosProgress: ', err);
    err.status = status.BAD_REQUEST;
    return next(err);
  }
};

module.exports = {
  getManagerTodosProgress,
  updateManagerTodosProgress,
};
