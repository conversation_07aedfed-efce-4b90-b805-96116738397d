const yup = require('yup');
const status = require('http-status');
const logger = require('../../../../services/logger.service');
const {
  communitySubscriptionsService,
  whatsappService,
} = require('../../../services/web');
const {
  getUserSubscriptionWithProjection,
  updateSubscription,
  verifyPaidSubscriptionPayment,
  verifyPaidPlanPayment,
} = require('../../../services/common/communitySubscriptions.service');
const { ToUserError } = require('../../../../utils/error.util');
const { GENERIC_ERROR } = require('../../../../constants/errorCode');
const { ACCESS_TOKEN_TYPE } = require('../../../../constants/common');
const {
  authCookieDomain,
  cookieTokenMaxAgeMS,
  cookieRefreshTokenMaxAgeMS,
} = require('../../../../config');

const updateSubscriptionInputSchema = yup.object().shape({
  userInChat: yup.boolean().notRequired(),
  reviewerNotes: yup.string().notRequired(),
});
const updateSubscriptionPathSchema = yup.object().shape({
  subscriptionId: yup.string().required(),
});

// DEPRECATED
const getCommunityMembers = async (req, res) => {
  res.json({
    data: [],
    meta: {
      total: 0,
      limit: 100,
      page: 1,
      pages: 0,
    },
  });
};

const getWhatsappCommunityMemberPathSchema = yup.object().shape({
  communityId: yup.string().required(),
  whatsappId: yup.string().required(),
});

const getWhatsappCommunityMember = async (req, res) => {
  try {
    const isPathValid = await getWhatsappCommunityMemberPathSchema.isValid(
      req.params
    );

    if (!isPathValid) {
      throw new Error('Invalid Parameters or Path');
    }

    const { communityId, whatsappId } = updateSubscriptionPathSchema.cast(
      req.params
    );

    const data = await whatsappService.retrieveWhatsappCommunityMember(
      whatsappId,
      communityId
    );

    return res.json({ status: 'success', data });
  } catch (err) {
    return res.status(status.BAD_REQUEST).json({ error: err.message });
  }
};

const deleteWhatsappMemberPathSchema = yup.object().shape({
  communityId: yup.string().required(),
  whatsappId: yup.string().required(),
});

const deleteWhatsappMember = async (req, res) => {
  try {
    const isPathValid = await deleteWhatsappMemberPathSchema.isValid(
      req.params
    );

    if (!isPathValid) {
      throw new Error('Invalid Parameters or Path');
    }

    const { whatsappId } = deleteWhatsappMemberPathSchema.cast(req.params);

    await whatsappService.removeWhatsappMemberByWhatsappId(whatsappId);

    return res.json({ status: 'success' });
  } catch (err) {
    return res.status(status.BAD_REQUEST).json({ error: err.message });
  }
};

// DEPRECATED
const getCommunityMembersForAdmin = async (req, res) => {
  res.json({
    data: [],
    meta: {
      total: 0,
      limit: 100,
      page: 1,
      pages: 0,
    },
  });
};

const getCommunityMemberLeadsForAdmin = async (req, res, next) => {
  try {
    res.json(
      await communitySubscriptionsService.getCommunityMembersForAdmin({
        communityId: req.params.communityId,
        skipQuery: parseInt(req.query.pageNo, 10),
        limitQuery: parseInt(req.query.pageSize, 10),
        sortByQuery: req.query.sortBy,
        sortOrderQuery: req.query.sortOrder,
        subscriptionStatus: req.query.subscriptionStatus,
        memberType: req.query.memberType,
        search: req.query.search,
        role: 'all',
      })
    );
  } catch (err) {
    logger.error(
      'Error on get community members request for all leads: ',
      err,
      err.stack
    );
    const error = new Error('Error on get request');
    error.status = err.status || 500;
    return next(error);
  }
};

const updateSubscriptionByAdmin = async (req, res, next) => {
  try {
    const isQueryValid = await updateSubscriptionInputSchema.isValid(
      req.body
    );
    const isPathValid = await updateSubscriptionPathSchema.isValid(
      req.params
    );
    if (!isQueryValid && !isPathValid) {
      const error = new Error('Invalid Parameters or Path');
      error.status = status.BAD_REQUEST;
      return next(error);
    }

    const body = updateSubscriptionInputSchema.cast(req.body);
    const path = updateSubscriptionPathSchema.cast(req.params);
    const subscription =
      await communitySubscriptionsService.updateSubscriptionByAdmin(
        path.subscriptionId,
        body
      );

    return res.status(status.OK).json({ data: subscription });
  } catch (ex) {
    return res.status(status.BAD_REQUEST).json({ error: ex });
  }
};

const toggleUserConfirmedJoiningChat = async (req, res, next) => {
  try {
    const { communityId = null } = req?.params;
    const { learner = null } = req?.user;
    const { learnerId = null } = learner;
    /*
    payload structure: 
    const {
      discord = false,
      facebook = false,
      linkedin = false,
      slack = false,
      telegram = false,
      whatsapp = false,
      ...otherPlatformDetails
    } = req?.body;
    */
    const { _id: subscriptionObjectId, userConfirmedJoiningChat = {} } =
      await getUserSubscriptionWithProjection({
        learnerId,
        communityObjectId: communityId,
        projectionParams: 'userConfirmedJoiningChat',
      });
    logger.info(
      'Existing userConfirmedJoiningChat: ',
      userConfirmedJoiningChat
    );
    let updatedUserConfirmedJoiningChat = {};
    for (const [key, value] of Object.entries(req?.body)) {
      // eslint-disable-next-line no-prototype-builtins
      if (!userConfirmedJoiningChat.hasOwnProperty(key)) {
        if (Object.keys(userConfirmedJoiningChat).length) {
          updatedUserConfirmedJoiningChat = {
            [key]: value,
            ...userConfirmedJoiningChat,
          };
        } else {
          updatedUserConfirmedJoiningChat = {
            [key]: value,
          };
        }
      } else {
        userConfirmedJoiningChat[key] = value;
        if (Object.keys(userConfirmedJoiningChat).length) {
          updatedUserConfirmedJoiningChat = {
            ...userConfirmedJoiningChat,
          };
        } else {
          updatedUserConfirmedJoiningChat = {
            [key]: value,
          };
        }
      }
    }
    let statusCode = status.BAD_REQUEST;
    let result = {
      error: { message: 'Error on toggling user confirmed joining chat' },
    };
    if (Object.keys(updatedUserConfirmedJoiningChat).length) {
      const updatedSubscription = await updateSubscription(
        subscriptionObjectId,
        { userConfirmedJoiningChat: updatedUserConfirmedJoiningChat }
      );
      logger.info('Updated subscription: ', updatedSubscription);
      if (updatedSubscription && Object.keys(updatedSubscription).length) {
        statusCode = status.OK;
        result = { data: { message: 'Updated successfully' } };
      }
    }
    return res.status(statusCode).send(result);
  } catch (err) {
    logger.error(
      'Error on toggling user confirmed joining chat: ',
      err,
      err.stack
    );
    const error = new Error(
      'Error on toggling user confirmed joining chat'
    );
    error.status = err.status || 500;
    return next(error);
  }
};

const verifySubscriptionPayment = async (req, res, next) => {
  const { signupId = null, subscriptionId = null } = req?.query;
  const isLoginToken = req.accessTokenType === ACCESS_TOKEN_TYPE.LOGIN;

  let result;
  try {
    result = await verifyPaidSubscriptionPayment(
      signupId,
      subscriptionId,
      isLoginToken
    );
    if (result.isFailedPayment) {
      throw new ToUserError(
        result?.error?.message ?? 'Payment is failed',
        result.paymentError ?? GENERIC_ERROR.PAYMENT_FAILURE
      );
    }

    // Set access token to cookie for first time user to auto login and access the purchase
    if (!isLoginToken && result.token) {
      res.cookie('accessTokenNA', result.token, {
        domain: authCookieDomain,
        path: '/',
        sameSite: 'None',
        secure: true,
        httpOnly: false,
        maxAge: cookieTokenMaxAgeMS,
      });
      res.cookie('refreshTokenNA', result.refreshToken, {
        domain: authCookieDomain,
        path: '/',
        sameSite: 'None',
        secure: true,
        httpOnly: false,
        maxAge: cookieRefreshTokenMaxAgeMS,
      });
    }
  } catch (err) {
    return next(err);
  }

  return res.json({ data: result });
};

const verifyPlanPayment = async (req, res, next) => {
  const { signupId = null } = req?.query;
  let result;
  try {
    result = await verifyPaidPlanPayment(signupId);
    if (result.isFailedPayment) {
      throw new ToUserError(
        result?.error?.message ?? 'Payment is failed',
        result.paymentError ?? GENERIC_ERROR.PAYMENT_FAILURE
      );
    }
  } catch (err) {
    return next(err);
  }

  return res.json({ data: result });
};

module.exports = {
  getCommunityMembers,
  getWhatsappCommunityMember,
  getCommunityMembersForAdmin,
  getCommunityMemberLeadsForAdmin,
  deleteWhatsappMember,
  updateSubscriptionByAdmin,
  toggleUserConfirmedJoiningChat,
  verifySubscriptionPayment,
  verifyPlanPayment,
};
