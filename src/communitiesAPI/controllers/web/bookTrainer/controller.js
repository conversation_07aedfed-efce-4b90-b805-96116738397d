const ObjectId = require('mongoose').Types.ObjectId;
const logger = require('../../../../services/logger.service');
const { communityBookTrainerService } = require('../../../services/web');

const registerBookingEvent = async (req, res, next) => {
  try {
    const params = {
      learnerId: req.user.learner.learnerId,
      learnerObjectId: new ObjectId(req.user.learner._id),
      email: req.user.email,
      communityId: req.params.communityId,
      eventUri: req.body.eventUri,
      inviteeUri: req.body.inviteeUri,
    };

    res.json(
      await communityBookTrainerService.registerBookingEvent(params)
    );
  } catch (err) {
    logger.info('Error in Register for booking 1:1 event request: ', err);
    const error = new Error('Error booking 1:1 event');
    error.status = 400;
    return next(error);
  }
};

const cancelBookingEvent = async (req, res, next) => {
  try {
    const params = {
      learnerId: req.user.learner.learnerId,
      learnerObjectId: new ObjectId(req.user.learner._id),
      email: req.user.email,
      communityId: req.params.communityId,
      cancelReason: req.body.cancelReason,
    };
    res.json(await communityBookTrainerService.cancelBookingEvent(params));
  } catch (err) {
    logger.error('Error in Cancel Booking Event: ', err);
    if (
      err.message === 'Event is already canceled' ||
      err.message === 'No booking found'
    ) {
      const error = new Error(err.message);
      error.status = 403;
      return next(error);
    }
    const error = new Error('Error in Cancel Booking Event');
    error.status = 400;
    return next(error);
  }
};

module.exports = {
  registerBookingEvent,
  cancelBookingEvent,
};
