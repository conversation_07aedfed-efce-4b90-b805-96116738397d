const yup = require('yup');
const status = require('http-status');

const whatsappServiceCommon = require('../../../services/common/whatsapp.service');
const {
  whatsappTemplateService,
  whatsappService,
} = require('../../../services/web');
const {
  magicReachWhatsappResponse,
  magicReachEventListeners,
  magicReachOptInOut,
} = require('../../../../services/magicReach');
const {
  getConfigByTypeFromCache,
} = require('../../../../services/config.service');

const { WHATSAPP_VERIFY_TOKEN } = require('../../../../config');
const logger = require('../../../../services/logger.service');

const { ParamError } = require('./../../../../utils/error.util');

const { MAGIC_REACH_RESPONSES } = require('./constants');
const { CONFIG_TYPES } = require('../../../../constants/common');
const whatsappEventMessagesModel = require('../../../../models/whatsapp/whatsappEventMessages.model');

async function getMagicReachResponses(type) {
  const lpbeConfig = await getConfigByTypeFromCache(
    CONFIG_TYPES.LEARN_PORTAL_BACKEND_ENV_CONFIG_TYPE
  );
  const configName = `MAGIC_REACH_WHATSAPP_${type}_RESPONSE`;
  const response =
    lpbeConfig?.envVarData?.[configName] || MAGIC_REACH_RESPONSES[type];
  return response;
}

const getWhatsappTemplates = async (req, res, next) => {
  logger.info(
    `Query params received for getWhatsappTemplates: ${JSON.stringify(
      req.query
    )}`
  );
  try {
    // eslint-disable-next-line no-param-reassign
    res.json(
      await whatsappTemplateService.getWhatsappTemplates({
        name: req.query?.name,
        templateObjectId: req.query?.id,
      })
    );
  } catch (err) {
    logger.error(
      `Error on getting whatsapp templates with following query params : ${JSON.stringify(
        req.query
      )}`,
      err
    );
    const error = new Error('Error getting the whatsapp templates');
    error.status = err.status || 500;
  }
};

const sendWhatsappTemplateMessage = async (req, res, next) => {
  try {
    const { communityId } = req.params;
    const { templateId, phoneNumber, ...rest } = req.body;
    const result = await whatsappServiceCommon.sendWhatsappTemplateMessage(
      {
        communityId,
        templateId,
        phoneNumber,
      }
    );
    res.status(status.OK).json(result);
  } catch (err) {
    logger.error(
      `Error on sending whatsapp template message: `,
      err,
      err.stack
    );
    const error = new Error('Error on sending whatsapp template message');
    error.status = err.status || 500;
    return next(error);
  }
};

// Accepts GET requests at the /webhook endpoint. You need this URL to setup webhook initially.
// info on verification request payload: https://developers.facebook.com/docs/graph-api/webhooks/getting-started#verification-requests
const whatsappWebhookGetListener = async (req, res, next) => {
  const verifyToken = WHATSAPP_VERIFY_TOKEN;
  // Parse params from the webhook verification request
  const mode = req.query['hub.mode'];
  const token = req.query['hub.verify_token'];
  const challenge = req.query['hub.challenge'];
  // Check if a token and mode were sent
  if (mode && token) {
    // Check the mode and token sent are correct
    if (mode === 'subscribe' && token === verifyToken) {
      // Respond with 200 OK and challenge token from the request
      logger.info('WEBHOOK_VERIFIED');
      res.status(200).send(challenge);
    } else {
      // Responds with '403 Forbidden' if verify tokens do not match
      res.sendStatus(403);
    }
  }
  res.status(200).send('Welcome!');
};

const whatsappWebhookPostListener = async (req, res, next) => {
  // Check the Incoming webhook message
  logger.info(
    `Received event in Whatsapp webhook`,
    `body=${JSON.stringify(req.body)}`
  );

  // info on WhatsApp text message payload: https://developers.facebook.com/docs/whatsapp/cloud-api/webhooks/payload-examples#text-messages
  if (req.body.object) {
    try {
      // assuming only 1 account, 1 change
      const entry = req.body.entry?.[0];
      const eventValue = entry?.changes?.[0].value;
      if (!eventValue) {
        throw new ParamError('Unexpected request body');
      }
      if (eventValue.messages?.[0]) {
        // received message from whatsapp webhook
        const whatsappData = eventValue.messages[0];
        const from = whatsappData?.from; // extract the phone number from the webhook payload
        let msgBody = '';
        if (whatsappData?.text) {
          msgBody = whatsappData.text?.body?.toLocaleLowerCase().trim(); // extract the message text from the webhook payload
        } else {
          msgBody = whatsappData?.button?.text?.toLocaleLowerCase().trim();
        }
        const acceptedResponses = await getMagicReachResponses('ACCEPTED');
        const optOutResponses = await getMagicReachResponses('OPT_OUT');
        const optInResponses = await getMagicReachResponses('OPT_IN');
        if (
          acceptedResponses
            .map((resp) => resp.toLowerCase().trim())
            .includes(msgBody)
        ) {
          const payload = whatsappData.button?.payload;
          if (payload) {
            if (payload)
              await magicReachWhatsappResponse.acceptWhatsappResponse({
                payload,
                recipientPhoneNumberId: from,
                nasPhoneNumberId: eventValue.metadata?.phone_number_id,
              });
          }
        } else if (
          optOutResponses
            .map((resp) => resp.toLowerCase().trim())
            .includes(msgBody)
        ) {
          const payload = whatsappData.button?.payload;
          if (payload) {
            await magicReachWhatsappResponse.optOutWhatsappResponse({
              payload,
              recipientPhoneNumberId: from,
              nasPhoneNumberId: eventValue.metadata?.phone_number_id,
            });
          }
        } else if (
          optInResponses
            .map((resp) => resp.toLowerCase().trim())
            .includes(msgBody)
        ) {
          const payload = whatsappData.button?.payload;
          if (payload) {
            await magicReachWhatsappResponse.optInWhatsappResponse({
              payload,
              recipientPhoneNumberId: from,
              nasPhoneNumberId: eventValue.metadata?.phone_number_id,
            });
          }
        } else {
          const formattedMessages = eventValue.messages.map((msg) => {
            const final = msg;
            if (msg.timestamp) {
              final.datetime = new Date(
                parseInt(msg.timestamp, 10) * 1000
              ).toISOString();
            }
            return final;
          });
          await whatsappEventMessagesModel.create({
            businessAccountId: entry.id,
            phoneNumberId: eventValue.metadata?.phone_number_id,
            displayPhoneNumberId:
              eventValue.metadata?.display_phone_number,
            messages: formattedMessages,
          });
        }
      } else if (eventValue.statuses?.[0]) {
        // received message status from whatsapp webhook
        const messageStatus = eventValue.statuses[0];
        await magicReachEventListeners.whatsappMessageStatusEventListener({
          payload: messageStatus,
        });
      }
      res.sendStatus(200);
    } catch (err) {
      logger.error(
        `Error in whatsapp webhook listener|`,
        `error=${err}`,
        `trace=${err.stack}`
      );
      const error = new Error('Error in whatsapp webhook listener');
      error.status = err.status || status.INTERNAL_SERVER_ERROR;
      next(error);
    }
  } else {
    // Return a '404 Not Found' if event is not from a WhatsApp API
    res.sendStatus(status.NOT_FOUND);
  }
};

const getWhatsappMembersCountPathSchema = yup.object().shape({
  communityCode: yup.string().required(),
});

const getWhatsappMembersCount = async (req, res) => {
  try {
    const isPathValid = await getWhatsappMembersCountPathSchema.isValid(
      req.params
    );

    if (!isPathValid) {
      throw new ParamError('Invalid Parameters or Path');
    }

    const { communityCode } = getWhatsappMembersCountPathSchema.cast(
      req.params
    );

    const membersCount =
      await whatsappService.retrieveWhatsappCommunityMembersCountByCode(
        communityCode
      );

    return res.json({ status: 'success', data: { membersCount } });
  } catch (err) {
    return res
      .status(err.status || status.INTERNAL_SERVER_ERROR)
      .json({ error: err.message });
  }
};

const getWhatsappStatusPathSchema = yup.object().shape({
  communityId: yup.string().required(),
});

const getWhatsappStatus = async (req, res) => {
  try {
    const isPathValid = await getWhatsappStatusPathSchema.isValid(
      req.params
    );

    if (!isPathValid) {
      throw new ParamError('Invalid Parameters or Path');
    }

    const { communityId } = getWhatsappStatusPathSchema.cast(req.params);

    const whatsappCommunityStatus =
      await whatsappService.retrieveWhatsappCommunityStatus(communityId);

    return res.json({ status: 'success', data: whatsappCommunityStatus });
  } catch (err) {
    return res
      .status(err.status || status.INTERNAL_SERVER_ERROR)
      .json({ error: err.message });
  }
};

const getExportWhatsappMembersPathSchema = yup.object().shape({
  communityId: yup.string().required(),
});
const getExportWhatsappMembersQuerySchema = yup.object().shape({
  search: yup.string(),
});

const getExportWhatsappMembers = async (req, res) => {
  try {
    const isPathValid = await getExportWhatsappMembersPathSchema.isValid(
      req.params
    );
    if (!isPathValid) {
      throw new ParamError(
        'Invalid Parameters or Path',
        'INVALID_PATH_PARAMS'
      );
    }

    const isQueryValid = await getExportWhatsappMembersQuerySchema.isValid(
      req.query
    );
    if (!isQueryValid) {
      throw new ParamError(
        'Invalid Parameters or Query',
        'INVALID_QUERY_PARAMS'
      );
    }

    const { communityId } = getExportWhatsappMembersPathSchema.cast(
      req.params
    );
    const { search } = getExportWhatsappMembersQuerySchema.cast(req.query);

    const communityCode =
      await whatsappService.retrieveWhatsappCommunityCode(communityId);

    const [currentDate, currentTime] = new Date().toISOString().split('T');
    const fileName = encodeURIComponent(
      req.query?.fileName ??
        `${communityCode}_whatsapp_${currentDate.replace(
          /-/g,
          ''
        )}_${currentTime.split('.')[0].replace(/:/g, '')}.csv`
    );

    res.setHeader(
      'Content-Disposition',
      `attachment; filename=${fileName}`
    );
    res.setHeader('Content-Type', 'text/csv');

    const limit = 100;

    const whatsappMembersCsvStream =
      await whatsappService.generateWhatsappMembersCsvStream(
        communityId,
        limit,
        search
      );

    whatsappMembersCsvStream.pipe(res);
    whatsappMembersCsvStream.on('end', () => {
      res.end();
    });
  } catch (err) {
    logger.error(
      'Error in exporting whatsapp members|',
      `error=${err}`,
      `trace=${err.stack}`
    );
    return res.status(status.BAD_REQUEST).json({ error: err.message });
  }
};

const getWhatsappAnalyticsSchema = yup.object().shape({
  communityId: yup.string().required(),
  duration: yup.string().lowercase().required().trim(),
});

const getWhatsappAnalytics = async (req, res) => {
  try {
    const { communityId } = req.params;
    const { duration } = req.query;

    logger.info('getting analytics of the community', communityId);
    const isPathValid = await getWhatsappAnalyticsSchema.isValid({
      communityId,
      duration,
    });

    if (!isPathValid) {
      throw new ParamError('Invalid Parameters or Path');
    }

    const analyticsParams = getWhatsappAnalyticsSchema.cast({
      ...req.params,
      ...req.query,
    });

    const analytics = await whatsappService.getWhatsappAnalytics(
      analyticsParams
    );
    return res.json({ status: 'success', data: analytics });
  } catch (err) {
    logger.error(
      'Error in getWhatsappAnalytics ',
      err,
      'error stack',
      err.stack
    );

    return res
      .status(err.status || status.INTERNAL_SERVER_ERROR)
      .json({ error: err.message });
  }
};
module.exports = {
  sendWhatsappTemplateMessage,
  whatsappWebhookGetListener,
  whatsappWebhookPostListener,
  getWhatsappTemplates,
  getWhatsappStatus,
  getWhatsappMembersCount,
  getExportWhatsappMembers,
  getWhatsappAnalytics,
};
