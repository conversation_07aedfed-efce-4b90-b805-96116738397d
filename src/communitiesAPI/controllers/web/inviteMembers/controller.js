const yup = require('yup');
const { inviteService } = require('../../../../services/membership');

const { ParamError } = require('../../../../utils/error.util');

const sendMemberInviteSchema = yup.object().shape({
  emails: yup
    .array()
    .of(
      yup.lazy((value) =>
        yup
          .string()
          .trim()
          .email(`"${value}" is not a valid email`)
          .required()
      )
    )
    .required(),
});

const bulkInviteMembersSchema = yup.object().shape({
  data: yup.array().of(
    yup.object().shape({
      email: yup.string().trim().required(),
      phoneNumber: yup.string().trim().notRequired(),
      firstName: yup.string().trim().notRequired(),
      lastName: yup.string().trim().notRequired(),
    })
  ),
});

const resumeInviteSchema = yup.object().shape({
  communityData: yup.object().shape({
    _id: yup.string().required(),
    code: yup.string().required(),
    createdBy: yup.string().required(),
  }),
  toSendInviteEmail: yup.boolean().required(),
});

const inviteMembers = async (req, res, next) => {
  const { communityId } = req?.params;
  const { emails = [] } = req?.body;
  const formattedEmails =
    emails?.map((email) => email?.toLowerCase()) || [];
  const user = req.user;
  try {
    await sendMemberInviteSchema.validate(req.body);
  } catch (error) {
    throw new ParamError(error.message);
  }
  const invitedResults = await inviteService.inviteMembers({
    communityId,
    user,
    data: formattedEmails.map((email) => ({ email })),
  });
  return invitedResults;
};

const bulkInviteMembers = async (req, res, next) => {
  const { communityId } = req?.params;
  const user = req.user;
  try {
    await bulkInviteMembersSchema.validate(req.body);
  } catch (error) {
    throw new ParamError(error.message);
  }
  const { data } = await bulkInviteMembersSchema.cast(req.body);
  const invitedResults = await inviteService.inviteMembers({
    communityId,
    user,
    data,
  });
  return invitedResults;
};

const resumeInvite = async (req, res, next) => {
  try {
    await resumeInviteSchema.validate(req.body);
  } catch (error) {
    throw new ParamError(error.message);
  }
  const { communityData, toSendInviteEmail } =
    await resumeInviteSchema.cast(req.body);
  inviteService.sendPendingInviteEmail({
    communityData,
    toSendInviteEmail,
  });
};

module.exports = {
  inviteMembers,
  bulkInviteMembers,
  resumeInvite,
};
