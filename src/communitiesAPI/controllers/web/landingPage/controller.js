const axios = require('../../../../clients/axios.client');
const logger = require('../../../../services/logger.service');

const {
  NEXT_JS_SECRET_TOKEN,
  NAS_IO_FRONTEND_URL,
} = require('../../../../config');

const revalidateFEPath = async (req, res, next) => {
  try {
    const slug = req.body?.slug;
    const pathToValidate = `${NAS_IO_FRONTEND_URL}/api/revalidate?path=${slug}&secret=${NEXT_JS_SECRET_TOKEN}`;
    const response = await axios(pathToValidate);
    return res.json(response.data);
  } catch (err) {
    logger.error('Something went wrong while revalidating landing page');
    logger.error(err);
    return res
      .status(500)
      .send('Something went wrong, but page will update soon');
  }
};

module.exports = {
  revalidateFEPath,
};
