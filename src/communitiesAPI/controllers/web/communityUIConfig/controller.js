const logger = require('../../../../services/logger.service');

const {
  getCommunityUIConfigService,
} = require('../../../services/web/communityUIConfig.service');

const getCommunityUIConfig = async (req, res, next) => {
  const communityId = req.params.communityId;
  try {
    const config = await getCommunityUIConfigService(communityId);
    res.json(config);
  } catch (err) {
    logger.info(
      'Error on get community UI config request: ',
      err,
      err.stack
    );
    const error = new Error(err);
    error.status = err.status || 500;
    return next(error);
  }
};

module.exports = {
  getCommunityUIConfig,
};
