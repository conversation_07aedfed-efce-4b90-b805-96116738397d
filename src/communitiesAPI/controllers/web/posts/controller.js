const mongoose = require('mongoose');
const produceCommonService = require('../../../../services/common/communityProducts.service');
const { communityPostsService } = require('../../../services/web');
const logger = require('../../../../services/logger.service');
const { addSignedCookiesToResponse } = require('../../../utils');
const CommunityPostModel = require('../../../models/communityPost.model');
const {
  countCommunityMembers,
} = require('../../../../services/membership/count.service');
const { MAX_PAGINATION_LIMIT } = require('../../../../constants/common');
const {
  COMMUNITY_POSTS_STATUS,
  MAX_PINNED_ANNOUNCEMENTS,
} = require('../../../constants');
const {
  getUserCommunityAnnouncementsPosts,
} = require('../../../services/common/communityPosts.service');
const { ToUserError } = require('../../../../utils/error.util');
const { ANNOUNCEMENET_ERROR } = require('../../../../constants/errorCode');
const { isCommunityIndexable } = require('../../../services/common/utils');

const getCommunityPosts = async (req, res, next) => {
  try {
    // eslint-disable-next-line no-param-reassign
    res = addSignedCookiesToResponse(res);

    res.json(
      await communityPostsService.getCommunityPosts(req.params.communityId)
    );
  } catch (err) {
    logger.error('Error on get All Posts request: ', err, err.stack);
    const error = new Error(`Error on get request: ${err}`);
    error.status = err.status || 500;
    return next(error);
  }
};

const getUserAnnouncementPosts = async (req) => {
  let { paginate = 0, pageNum = null, pageSize = null } = req.query;
  const { status = COMMUNITY_POSTS_STATUS.APPROVED } = req.query;

  const { communityId } = req.params;

  paginate = parseInt(paginate, 10) || 0;

  if (paginate === 1) {
    pageNum = parseInt(pageNum, 10) || 1;
    pageSize = parseInt(pageSize, 10) || 10;
  }
  const posts = await getUserCommunityAnnouncementsPosts(
    communityId,
    req.user,
    paginate,
    pageNum,
    pageSize,
    status
  );

  return posts;
};
// Announcements non pinned - community manager portal
const getAnnouncementPosts = async (req, res, next) => {
  try {
    const { learner = null } = req?.user;
    const isUserPartOfCommunity = req?.user?.isPartOfCommunity;
    const { _id: learnerObjectId } = learner || {};
    const { communityId = null } = req?.params;
    let { paginate = 0, pageNum = null, pageSize = null } = req?.query;
    const { status = COMMUNITY_POSTS_STATUS.APPROVED, postedBy } =
      req?.query;

    const searchQuery = req.query?.searchQuery;
    paginate = parseInt(paginate, 10) || 0;
    if (paginate === 1) {
      pageNum = parseInt(pageNum, 10) || 1;
      pageSize = parseInt(pageSize, 10) || 10;
    }
    res.json(
      await communityPostsService.getCommunityAnnouncementsWithAuthors(
        communityId,
        learnerObjectId,
        paginate,
        pageNum,
        pageSize,
        isUserPartOfCommunity,
        searchQuery,
        status,
        postedBy
      )
    );
  } catch (err) {
    logger.info('Error on get author announcements request: ', err);
    const error = new Error(
      `Error on get author announcement request: ${err}`
    );
    error.status = err.status || 500;
    return next(error);
  }
};

// Announcements pinned - community manager portal
const getPinnedAnnouncementPosts = async (req, res, next) => {
  try {
    const { learner = null } = req?.user;
    const { _id: learnerObjectId } = learner || {};
    const isUserPartOfCommunity = req?.user?.isPartOfCommunity;
    const { communityId = null } = req?.params;
    let { paginate = 0, pageNum = null, pageSize = null } = req?.query;
    const { status = COMMUNITY_POSTS_STATUS.APPROVED, postedBy } =
      req?.query;

    const searchQuery = req.query?.searchQuery;
    paginate = parseInt(paginate, 10) || 0;
    if (paginate === 1) {
      pageNum = parseInt(pageNum, 10) || 1;
      pageSize = parseInt(pageSize, 10) || 10;
    }
    res.json(
      await communityPostsService.getCommunityPinnedAnnouncementsWithAuthors(
        communityId,
        learnerObjectId,
        paginate,
        pageNum,
        pageSize,
        isUserPartOfCommunity,
        searchQuery,
        status,
        postedBy
      )
    );
  } catch (err) {
    logger.error(
      'Error on get author announcements request: ',
      err,
      err.stack
    );
    const error = new Error('Error on get request');
    error.status = err.status || 500;
    return next(error);
  }
};

const getAnnouncementPost = async (req, res, next) => {
  try {
    const isUserPartOfCommunity = req.user.isPartOfCommunity;
    const learnerObjectId = req?.user?.learner?._id;
    const { announcementId: postId } = req.params;
    res.json(
      await communityPostsService.getAnnouncementWithAuthor(
        postId,
        isUserPartOfCommunity,
        learnerObjectId
      )
    );
  } catch (err) {
    logger.error('Error on get announcement request: ', err, err.stack);
    const error = new Error(`Error on get request announcement: ${err}`);
    error.status = err.status || 500;
    return next(error);
  }
};

const createAnnouncementPost = async (req, res, next) => {
  try {
    const author = req.user?._id;
    const learnerObjectId = req.user?.learner?._id;
    const communityId = req.params?.communityId;
    const {
      title,
      content,
      visibilityType,
      blurHashString,
      blurImageUrl,
      postedBy,
      mentionedProducts,
      status,
    } = req.body;
    const data = {
      author,
      communities: [communityId],
      title,
      content,
      status,
      visibilityType,
      blurHashString,
      blurImageUrl,
      postedBy,
      mentionedProducts,
    };
    const announcement =
      await communityPostsService.createAnnouncementPost(
        data,
        learnerObjectId
      );

    res.json(announcement);
  } catch (err) {
    logger.error(
      'Error on creating author announcement request: ',
      err,
      err.stack
    );
    err.status = err.status || 500;
    return next(
      new Error(`Error on creating author announcement request: ${err}`)
    );
  }
};

// Loop through and unpin existing announcements
// eslint-disable-next-line no-unused-vars
const unPinExisting = async (communityId, editor) => {
  const communityObjectId = new mongoose.Types.ObjectId(communityId);
  const announcements =
    await communityPostsService.getPinnedAnnouncementPosts(
      communityObjectId
    );

  if (announcements.length > 0) {
    const announcementsToUnpin = [];
    for (let i = 0; i < announcements.length; i++) {
      const data = { isPinned: false, editor };
      const announcement = announcements[i];
      announcementsToUnpin.push(
        communityPostsService.updatePost(announcement._id, data)
      );
    }
    await Promise.all(announcementsToUnpin);
  }
};

const checkPinnedAnnouncementLimit = async (
  communityId,
  numberOfPinned
) => {
  const communityObjectId = new mongoose.Types.ObjectId(communityId);
  const announcements =
    await communityPostsService.getPinnedAnnouncementPosts(
      communityObjectId
    );

  return announcements.length >= numberOfPinned;
};

const updateAnnouncementPost = async (req, res, next) => {
  try {
    const { id, ...rest } = req.body;
    const editor = req.user?._id;
    const data = { editor, ...rest };
    const learnerObjectId = req.user?.learner?._id;
    // For now only one pinned allowed per community
    if (data.isPinned) {
      // check if the existing pinned announcement is not more than 3
      // await unPinExisting(req?.params?.communityId, editor);
      const isPinnedLimitReached = await checkPinnedAnnouncementLimit(
        req?.params?.communityId,
        MAX_PINNED_ANNOUNCEMENTS
      );

      if (isPinnedLimitReached) {
        throw new ToUserError(
          'You can pin up to 3 posts only',
          ANNOUNCEMENET_ERROR.PINNED_POST_LIMIT_REACHED
        );
      }
    }
    res.json(
      await communityPostsService.updatePost(id, data, learnerObjectId)
    );
  } catch (err) {
    logger.error('Error updating author announcement', err, err.stack);
    err.status = err.status || 500;
    return next(new Error(`${err}`));
  }
};

const deleteAnnouncementPost = async (req, res, next) => {
  try {
    const postId = req.params?.announcementId;
    const deleter = req.user?._id;

    res.json(await communityPostsService.deletePost(postId, deleter));
  } catch (err) {
    logger.error('Error deleting author announcement', err, err.stack);
    err.status = err.status || 500;
    return next(
      new Error(`Error on deleting author announcement request: ${err}`)
    );
  }
};

const getAnnouncementPostBySlug = async (req, res) => {
  try {
    const isUserPartOfCommunity = req.user.isPartOfCommunity;

    const { slug } = req.params;
    const community = req.community;

    if (!community) {
      const error = new Error(
        'Community does not exist for the link provided'
      );
      error.status = 400;
      throw new Error(error);
    }

    if (
      community.indexable === undefined ||
      community.indexable === null
    ) {
      community.indexable = isCommunityIndexable(community);
    }

    const post = await CommunityPostModel.findOne({
      communities: [community?._id],
      slug: `/${slug}`,
    });

    if (!post) {
      const error = new Error('Announcement does not exist');
      error.status = 404;
      throw error;
    }

    const postId = post?._id;

    const learnerObjectId = req?.user?.learner?._id;

    const postInfo = await communityPostsService.getAnnouncementWithAuthor(
      postId,
      isUserPartOfCommunity,
      learnerObjectId
    );

    const communityInfo = community;

    delete communityInfo?.platforms?.[0]?.link;
    delete communityInfo?.botsPreviousData;
    delete communityInfo?.platformPreviousData;
    delete communityInfo?.createdBy;
    const memberCountInfo = await countCommunityMembers({
      communityId: communityInfo?._id,
    });

    const profileImage =
      communityInfo.thumbnailImgData?.desktopImgData?.src;
    communityInfo.totalMemberCount = memberCountInfo.count;

    communityInfo.profileImage = profileImage;

    const backgroundImage =
      communityInfo?.fullScreenBannerImgData?.desktopImgProps?.src;

    communityInfo.backgroundImage = backgroundImage;
    res.status(200).json({
      postInfo,
      communityInfo,
    });
  } catch (error) {
    logger.error(
      'error getting announcement info by slug',
      error,
      error.stack
    );
    const errorStatus = error?.status ?? 500;
    res.status(errorStatus).json({
      error,
      errorStack: error.stack,
      errorCode: errorStatus,
      data: null,
    });
  }
};

const getAllCommunityProducts = async (req) => {
  const { communityId: communityObjectId } = req.params;

  const { pageNo = 1, pageSize, search } = req.query;

  let allProducts = await produceCommonService.retrieveAllProducts({
    search,
    communityObjectId,
    onlyPublished: true,
    onlyPaid: false,
    pageSize: pageNo * pageSize,
  });

  const totalSize = await produceCommonService.retrieveTotalEntitiesSize({
    communityObjectId,
    onlyPublished: true,
    search,
    onlyPaid: false,
  });
  if (pageSize > MAX_PAGINATION_LIMIT) {
    throw new Error('Pagination limit exceeded');
  }

  if (pageSize) {
    const start = (pageNo - 1) * pageSize;
    const end = start + pageSize;
    allProducts = allProducts.products.slice(start, end);
  }
  const metadata = {
    total: totalSize,
    limit: Number(pageSize),
    page: Number(pageNo),
    pages: Math.ceil(totalSize / pageSize),
  };
  return { products: allProducts, metadata };
};

const reportPost = async (req) => {
  const { _id: postId, reportedReason = 'No reason Provided' } = req.body;
  const reportedByLearnerObjectId = req.user?.learner?._id;
  const reportedPost = await communityPostsService.reportPost({
    postId,
    reportedByLearnerObjectId,
    reason: reportedReason,
  });
  return reportedPost;
};

module.exports = {
  getCommunityPosts,
  getAnnouncementPosts,
  getPinnedAnnouncementPosts,
  getAnnouncementPost,
  createAnnouncementPost,
  updateAnnouncementPost,
  deleteAnnouncementPost,
  getAnnouncementPostBySlug,
  getAllCommunityProducts,
  reportPost,
  getUserAnnouncementPosts,
};
