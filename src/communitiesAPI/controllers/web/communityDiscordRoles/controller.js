const yup = require('yup');
const jwt = require('jsonwebtoken');
const status = require('http-status');
const axios = require('../../../../clients/axios.client');
const ObjectId = require('mongoose').Types.ObjectId;
const discordRolesModel = require('../../../models/discordRoles.model');
const communityModel = require('../../../models/community.model');
const {
  DISCORD_URL,
  DISCORD_AUTH,
  DISCORD_MEMBER_ROLE_SQS_QUEUE_URL,
} = require('../../../../config');
const {
  sendMessageToSQSFifoQueue,
} = require('../../../../handlers/sqs.handler');
const {
  discordVerificationChannelInit,
  discordGetAccessVerificationChannelInit,
} = require('../../../constants');
const logger = require('../../../../services/logger.service');

const getCommunityDiscordRoles = async (req, res, next) => {
  try {
    const user = req.user;
    const params = req.query;
    const { communityId } = req.params;
    const communityData = await communityModel.findOne({
      _id: new ObjectId(communityId),
    });
    const botData = communityData?.bots.filter(
      (bot) => bot.type === 'Discord'
    )?.[0];
    const roleFilters = { guildID: botData?.serverKey };
    if (!params?.isAdminRole) {
      roleFilters.isAdminRole = { $ne: true };
    }
    if (!params?.everyOne) {
      roleFilters['rolename'] = { $ne: '@everyone' };
    }
    if (params?.configured) {
      roleFilters['isConfigured'] = true;
    }
    const communityDicordRolesData = await discordRolesModel.find(
      roleFilters
    );
    res.status(status.OK).json(communityDicordRolesData);
  } catch (err) {
    logger.error(
      'Error occured when updating the community:',
      err,
      err.stack
    );
    const error = new Error(
      err || 'Error occured when updating the community:'
    );
    error.status = err.status || 500;
    return next(error);
  }
};

const updateConfigSchema = yup.object().shape({
  roleDescription: yup.string().trim(),
  collectionAddress: yup.string().required().trim(),
  collectionName: yup.string().required().trim(),
  minTokens: yup.number().required(),
  maxTokens: yup.mixed().notRequired().default(null),
});

const updateDiscordRoleConfiguration = async (req, res, next) => {
  try {
    const { communityId, roleId } = req.params;

    const community = await communityModel.findOne({
      _id: new ObjectId(communityId),
    });

    if (!community) {
      const error = new Error('Invalid Community');
      error.status = status.BAD_REQUEST;
      return next(error);
    }

    let payload = {};
    let roleDescription = 'Non-Token Gated Default';
    let channelMessage = {
      ...discordGetAccessVerificationChannelInit,
      isTokenGated: false,
    };

    if (community.isTokenGated) {
      const isQueryValid = await updateConfigSchema.isValid(req.body);
      if (!isQueryValid) {
        const error = new Error('Invalid Parameters');
        error.status = status.BAD_REQUEST;
        return next(error);
      }
      payload = updateConfigSchema.cast(req.body);
      roleDescription = payload?.roleDescription;
      channelMessage = {
        ...discordVerificationChannelInit,
        isTokenGated: true,
      };
    }

    const config = { ...payload };
    delete config.roleDescription;
    const roleFilters = { roleid: roleId };

    const configuredRoles = await discordRolesModel.count({
      ...roleFilters,
      isConfigured: true,
    });

    const communityDiscordRolesData =
      await discordRolesModel.findOneAndUpdate(
        roleFilters,
        {
          config,
          isConfigured: true,
          roleDescription: roleDescription,
        },
        { returnNewDocument: true }
      );

    // create channel on discord for first role configured
    const serverKey = communityDiscordRolesData?.guildID;

    if (serverKey !== community?.bots?.[0]?.serverKey) {
      const error = new Error(
        'Role does not belong to the guild attached to the community'
      );
      error.status = status.BAD_REQUEST;
      return next(error);
    }
    if (
      configuredRoles === 0 &&
      communityDiscordRolesData &&
      !community?.verificationChannelId
    ) {
      const requestUrl = `${DISCORD_URL}/api/v1/createChannel/${serverKey}`;
      const authHeader = jwt.sign({}, DISCORD_AUTH);
      const response = await axios.post(requestUrl, channelMessage, {
        headers: {
          Authorization: `Bearer ${authHeader}`,
        },
      });
      if (response?.data?.channel) {
        await communityModel.findOneAndUpdate(
          {
            _id: community._id,
            'bots.serverKey': serverKey,
          },
          {
            verificationChannelId: response?.data?.channel?.id,
          }
        );
      }
    }
    //Assign new role to all existing new members

    const messageBody = {
      assign_role_to_existing_members: {
        communityCode: community.code,
        guildId: serverKey,
        reason: 'Assign Role to Existing Members',
        roleid: communityDiscordRolesData.roleid,
      },
    };

    await sendMessageToSQSFifoQueue(
      DISCORD_MEMBER_ROLE_SQS_QUEUE_URL,
      `discord_role_assign_${serverKey}_${communityDiscordRolesData.roleid}`,
      messageBody
    );

    res.status(status.OK).json(communityDiscordRolesData);
  } catch (err) {
    logger.error('Error occured when updating the role config:', err);
    const error = new Error(
      err || 'Error occured when updating the role config:'
    );
    error.status = status.BAD_REQUEST;
    return next(error);
  }
};

const deleteDiscordRoleConfiguration = async (req, res, next) => {
  try {
    const { roleId } = req.params;

    const communityDiscordRolesData =
      await discordRolesModel.findOneAndUpdate(
        { roleid: roleId },
        { isConfigured: false, config: null }
      );

    res.status(status.OK).json(communityDiscordRolesData);
  } catch (err) {
    logger.error('Error occured when deleting the role config:', err);
    const error = new Error(
      err || 'Error occured when deleting the role config:'
    );
    error.status = status.BAD_REQUEST;
    return next(error);
  }
};

const assignRoleSchema = yup.object().shape({
  discordUserId: yup.string().required().trim(),
  guildId: yup.string().required().trim(),
});

const assignConfiguredDiscordRoleToUser = async (req, res, next) => {
  try {
    // Validate the request parameters using a schema
    const isQueryValid = await assignRoleSchema.isValid(req.params);

    // If the request parameters are invalid, return an error response with status code 400
    if (!isQueryValid) {
      const error = new Error('Invalid Parameters');
      error.status = status.BAD_REQUEST;
      return next(error);
    }

    // Extract the required parameters from the request params
    const { discordUserId, guildId } = assignRoleSchema.cast(req.params);

    // Log a message indicating that the role assignment process has started
    logger.info(
      `assigning role to user with discord User ID = ${discordUserId} and guild ID = ${guildId}`
    );

    // Log a message indicating that the process of fetching the configured roles from the database has started
    logger.info(
      'fetching configured roles for the guild from DB' + guildId
    );

    // Find all configured roles for the given guild ID in the database
    const configuredRoles = await discordRolesModel
      .find({
        guildID: guildId,
        isConfigured: true,
      })
      .lean();

    // If no configured roles are found, return a success response with message "No configured roles found for the guild"
    if (configuredRoles.length === 0) {
      logger.info('No configured roles found for the guild' + guildId);
      res.status(status.OK).json({
        message: 'No configured roles found for the guild',
      });
    }

    // Log a message indicating that the process of assigning the roles to the user has started
    logger.info(
      ' looping through the configured roles and assigning the to the user'
    );

    // Loop through each configured role and assign it to the user
    for await (const configuredRole of configuredRoles) {
      const { roleid } = configuredRole;

      // Prepare the body of the request to assign the role to the user
      const roleAssignBody = {
        discordUserId,
        roleId: roleid,
        reason: 'Assigning configured role to user',
      };

      try {
        // Send a POST request to the Discord API to assign the role to the user
        const requestUrl = `${DISCORD_URL}/api/v1/assignRole/${guildId}`;
        const authHeader = jwt.sign({}, DISCORD_AUTH);
        const response = await axios.post(requestUrl, roleAssignBody, {
          headers: {
            Authorization: `Bearer ${authHeader}`,
          },
        });

        // If the role is assigned successfully, log a success message and return a success response
        logger.info(
          'role assigned to user',
          JSON.stringify(response.data)
        );
        logger.info(
          'role assigned to user with discord User ID = ' +
            discordUserId +
            ' and guild ID = ' +
            guildId +
            ' and role ID = ' +
            roleid
        );
        res.status(status.OK).json({
          message: 'Role assigned to user',
          status: true,
        });
      } catch (error) {
        // If an error occurs while assigning the role, log an error message and return an error response
        logger.info(
          ' Error occured while assigning role to user',
          error,
          error?.stack
        );
        res.status(status.BAD_REQUEST).json({
          message: 'Error occured while assigning role to user',
          status: false,
        });
      }
    }
  } catch (error) {
    logger.error(
      'Error occured when validating the request body',
      error,
      error?.stack
    );
    res.status(status.BAD_REQUEST).json({
      message: 'Error occured when validating the request body',
      error,
    });
  }
};
module.exports = {
  getCommunityDiscordRoles,
  updateDiscordRoleConfiguration,
  deleteDiscordRoleConfiguration,
  assignConfiguredDiscordRoleToUser,
};
