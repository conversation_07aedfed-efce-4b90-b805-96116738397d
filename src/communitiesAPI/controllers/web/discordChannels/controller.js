const status = require('http-status');
const logger = require('../../../../services/logger.service');
const ObjectId = require('mongoose').Types.ObjectId;
const CommunityModel = require('../../../models/community.model');
const {
  discordChannelAnnouncement,
} = require('../../../services/web/discordChannelAnnouncement.service');
const {
  discordChannels,
} = require('../../../services/web/discordChannels.service');

const getAllDiscordChannelsFromAServer = async (req, res) => {
  try {
    const { communityId } = req.params;
    let error = null;
    const community = await CommunityModel.findOne({
      _id: new ObjectId(communityId),
    });
    if (!community) {
      error = new Error('Invalid Community');
      error.status = status.BAD_REQUEST;
      throw error;
    }
    const serverKey = community?.bots?.[0]?.serverKey;
    if (!serverKey) {
      error = new Error('Invalid Server Key');
      error.status = status.BAD_REQUEST;
      throw error;
    }
    const { data: channels, error: discordApiError } =
      await discordChannels(serverKey);
    if (discordApiError) {
      error = new Error(discordApiError);
      error.status = status.BAD_REQUEST;
      throw error;
    }
    res.status(status.OK).json({ data: channels, error: false });
  } catch (err) {
    logger.error(`Error occured when removing member: ${err?.message}`);
    return res
      .status(status.BAD_REQUEST)
      .json({ data: null, error: err?.message });
  }
};

const connectDiscordChannelWithCommunityForAnnouncements = async (
  req,
  res
) => {
  try {
    const { communityId } = req.params;
    const { channelId } = req.body;
    let error = null;
    if (!channelId) {
      error = new Error('Invalid Channel Id');
      error.status = status.BAD_REQUEST;
      throw error;
    }
    const { data: communityUpdatedData, error: communityUpdatingError } =
      await discordChannelAnnouncement(communityId, channelId);
    res
      .status(status.OK)
      .json({ data: communityUpdatedData, error: false });
  } catch (err) {
    logger.error(`Error occured when removing member: ${err?.message}`);
    return res
      .status(status.BAD_REQUEST)
      .json({ data: null, error: err?.message });
  }
};

module.exports = {
  getAllDiscordChannelsFromAServer,
  connectDiscordChannelWithCommunityForAnnouncements,
};
