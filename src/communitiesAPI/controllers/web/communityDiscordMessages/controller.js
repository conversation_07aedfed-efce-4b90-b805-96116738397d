const yup = require('yup');
const jwt = require('jsonwebtoken');
const status = require('http-status');
const axios = require('../../../../clients/axios.client');
const ObjectId = require('mongoose').Types.ObjectId;
const discordRolesModel = require('../../../models/discordRoles.model');
const communityModel = require('../../../models/community.model');
const {
  DISCORD_URL,
  DISCORD_AUTH,
  DISCORD_MEMBER_ROLE_SQS_QUEUE_URL,
} = require('../../../../config');
const {
  sendMessageToSQSFifoQueue,
} = require('../../../../handlers/sqs.handler');
const {
  discordVerificationChannelInit,
  discordGetAccessVerificationChannelInit,
} = require('../../../constants');
const logger = require('../../../../services/logger.service');
const CommunityMagicReachEmailModel = require('../../../../models/magicReach/communityMagicReachEmail.model');
const MagicReachNodeWrapper = require('../../../../services/magicReach/contentFormatter/MagicReachNodeWrapper');

const sendDiscordMessageFromMagicReachSchema = yup.object().shape({
  communityId: yup.string().required().trim(),
  draftId: yup.string().required().trim(),
  title: yup.string().required().trim(),
});
const sendDiscordMessageFromMagicReach = async (req, res, next) => {
  try {
    const user = req.user;
    const params = req.query;
    const { communityId } = req.params;
    const isValid = await sendDiscordMessageFromMagicReachSchema.isValid(
      req.body
    );
    if (!isValid) {
      const error = new Error('Invalid Parameters');
      error.status = status.BAD_REQUEST;
      return next(error);
    }

    const { draftId, title } = sendDiscordMessageFromMagicReachSchema.cast(
      req.body
    );

    const community = await communityModel.findOne({
      _id: new ObjectId(communityId),
    });
    if (!community) {
      const error = new Error('Community not found');
      error.status = status.NOT_FOUND;
      return next(error);
    }

    const discordServer = community?.bots?.filter(
      (bot) => bot?.type === 'Discord'
    );
    if (!discordServer?.length) {
      const error = new Error('Discord server not found');
      error.status = status.NOT_FOUND;
      return next(error);
    }
    const serverKey = discordServer?.[0]?.serverKey;
    if (!serverKey) {
      const error = new Error('Discord server key not found');
      error.status = status.NOT_FOUND;
      return next(error);
    }

    const magicReachDraft = await CommunityMagicReachEmailModel.findOne({
      _id: new ObjectId(draftId),
      communityId: new ObjectId(communityId),
    });
    if (!magicReachDraft) {
      const error = new Error('Draft not found');
      error.status = status.NOT_FOUND;
      return next(error);
    }

    const content = magicReachDraft.content;
    if (!content) {
      const error = new Error('Draft content not found');
      error.status = status.NOT_FOUND;
      return next(error);
    }
    const rootPayload = {
      ...content?.root,
    };
    const rootWrapper = new MagicReachNodeWrapper(rootPayload);
    const discordMessage = rootWrapper.getOptimizedDiscordMessage();
    //TODO: Get this from the community or some discord settings
    const channelId = community?.discordAnnouncementChannelId;

    const requestUrl = `${DISCORD_URL}/api/v1/sendMessageToChannel/${serverKey}/${channelId}`;
    const authHeader = jwt.sign({}, DISCORD_AUTH);
    const response = await axios.post(
      requestUrl,
      {
        message: [
          { type: 'embed', title: magicReachDraft.title },
          ...discordMessage,
        ],
      },
      {
        headers: {
          Authorization: `Bearer ${authHeader}`,
        },
      }
    );

    res.status(status.OK).json(response?.data);
  } catch (err) {
    logger.error(
      'Error occured when updating the community:',
      err,
      err.stack
    );
    const error = new Error(
      err || 'Error occured when updating the community:'
    );
    error.status = err.status || 500;
    return next(error);
  }
};

module.exports = {
  sendDiscordMessageFromMagicReach,
};
