const { DateTime } = require('luxon');
const { communityRevenueService } = require('../../../services/web');

const calculateRevenue = async (req, res, next) => {
  try {
    const { sendEmailToManagers, testEmail, specificCommunities } =
      req.body;
    const now = DateTime.utc();
    const currentMonth = now.month;
    let previousMonth = currentMonth - 1;
    let previousMonthYear = now.year;
    if (currentMonth === 0) {
      previousMonthYear -= 1;
      previousMonth = 11;
    }

    const filter = {};
    if (specificCommunities) {
      filter['code'] = { $in: specificCommunities };
    }

    const result =
      await communityRevenueService.calculateRevenueOfCommunity(
        previousMonth,
        previousMonthYear,
        filter,
        true,
        sendEmailToManagers,
        testEmail
      );
    res.json(result);
  } catch (err) {
    logger.error('Error on calculate revenue: ', err, err.stack);
    const error = new Error(`Error on get request: ${err}`);
    next(error);
  }
};

module.exports = {
  calculateRevenue,
};
