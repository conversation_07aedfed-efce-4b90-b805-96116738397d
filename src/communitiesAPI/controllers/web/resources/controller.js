const { communityResourcesService } = require('../../../services/web');
const logger = require('../../../../services/logger.service');
const cf = require('aws-cloudfront-sign');
const {
  PUBLIC_KEY,
  PRIVATE_KEY_BASE64,
  SIGNED_URL_EXPIRY_TIME,
  PRIVATE_VIDEO_BASE_URL,
  SIGNED_COOKIE_DOMAIN,
} = require('../../../../config');

const getCommunityResources = async (req, res, next) => {
  try {
    const options = {
      keypairId: PUBLIC_KEY,
      privateKeyString: Buffer.from(
        PRIVATE_KEY_BASE64,
        'base64'
      ).toString(),
      expireTime:
        new Date().getTime() + parseInt(SIGNED_URL_EXPIRY_TIME, 10),
    };
    const signedCookies = cf.getSignedCookies(
      `${PRIVATE_VIDEO_BASE_URL}/*`,
      options
    );
    for (const cookieId in signedCookies) {
      if (cookieId) {
        res.cookie(cookieId, signedCookies[cookieId], {
          domain: `${SIGNED_COOKIE_DOMAIN}`,
          path: '/',
          sameSite: 'None',
          secure: true,
          httpOnly: false,
        });
      }
    }
    res.json(
      await communityResourcesService.getCommunityResources(
        req.params.communityId
      )
    );
  } catch (err) {
    logger.info(
      'Error on get community resources request: ',
      err,
      err.stack
    );
    const error = new Error(`Error on get request: ${err}`);
    error.status = err.status || 500;
    return next(error);
  }
};

module.exports = {
  getCommunityResources,
};
