const {
  createMagicReachDraftEmail,
  deleteDraftMagicReachEmail,
  getAllDraftMagicReachEmails,
  getAllSentMagicReachEmails,
  updateDraftMagicReachEmail,
  getMagicReachBucketData,
  fetchBucketMetaData,
  uploadDraftMagicReachImage,
  sendDraftEmail,
  sendWhatsappTemplateMsg,
  saveMagicReachEmailsByDraftId,
  deleteMagicReachRecipientsByDraftId,
  getMagicReachRecipientsForDraftId,
  getAllRecipientsMetaDataForDraftId,
} = require('./controller');

module.exports = {
  createMagicReachDraftEmail,
  deleteDraftMagicReachEmail,
  getAllDraftMagicReachEmails,
  getAllSentMagicReachEmails,
  updateDraftMagicReachEmail,
  getMagicReachBucketData,
  fetchBucketMetaData,
  uploadDraftMagicReachImage,
  sendDraftEmail,
  sendWhatsappTemplateMsg,
  saveMagicReachEmailsByDraftId,
  deleteMagicReachRecipientsByDraftId,
  getMagicReachRecipientsForDraftId,
  getAllRecipientsMetaDataForDraftId,
};
