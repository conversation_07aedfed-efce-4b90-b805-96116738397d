const { magicReachService } = require('../../../services/web');
const http = require('http-status');
const logger = require('../../../../services/logger.service');
const CommunityMagicReachEmailService = require('../../../services/common/communityMagicReachEmails.service');
const httpStatus = require('http-status');
const { getLearner } = require('../../../../services/learner.service');

const fetchBucketMetaData = async (req, res, next) => {
  try {
    res.json(
      await magicReachService.fetchTotalUsersInBucket({
        communityId: req.params.communityId,
      })
    );
  } catch (err) {
    const error = new Error(`Error on get request: ${err}`);
    next(error);
  }
};

const createMagicReachDraftEmail = async (req, res, next) => {
  try {
    const { _id: learnerId } = req.user;
    const { communityId } = req.params;
    const { title, content, sentBucketName, sentEmails } = req.body;
    const magicReachEmailData = {
      learnerId,
      communityId,
      title,
      content,
      sentBucketName,
      sentEmails,
    };
    const result =
      await CommunityMagicReachEmailService.createDraftMagicReachEmail(
        magicReachEmailData
      );
    res.status(http.OK).json(result);
  } catch (err) {
    logger.info(`Error on creating a magic reach draft email: `, err);
    const error = new Error(`Error creating magic reach draft: ${err}`);
    error.status = 400;
    return next(error);
  }
};
const getMagicReachBucketData = async (req, res, next) => {
  try {
    res.json(
      await magicReachService.getMagicReachPipeline({
        communityId: req.params.communityId,
        skipQuery: parseInt(req.query.pageNo, 10),
        limitQuery: parseInt(req.query.pageSize, 10),
        sortByQuery: req.query.sortBy,
        sortOrderQuery: req.query.sortOrder,
        memberType: req.query.memberType,
        search: req.query.search,
        magicReachBucket: req.query.magicReachBucket,
      })
    );
  } catch (err) {
    const error = new Error('Error on get request');
  }
};

const getAllDraftMagicReachEmails = async (req, res, next) => {
  try {
    const { communityId } = req.params;
    const result =
      await CommunityMagicReachEmailService.getAllDraftMagicReachEmails({
        communityId,
      });
    res.status(http.OK).json({ data: result });
  } catch (err) {
    logger.info(`Error on getting all draft emails: `, err);
    const error = new Error('Error on getting all draft emails');
    error.status = 400;
    return next(error);
  }
};

const getAllSentMagicReachEmails = async (req, res, next) => {
  try {
    const { communityId } = req.params;
    const result =
      await CommunityMagicReachEmailService.getAllSentMagicReachEmails({
        communityId,
      });
    res.status(http.OK).json({ data: result });
  } catch (err) {
    logger.info(`Error on getting all sent emails: `, err);
    const error = new Error('Error on getting all sent emails');
    error.status = 400;
    return next(error);
  }
};

const updateDraftMagicReachEmail = async (req, res, next) => {
  try {
    const { _id: learnerId } = req.user;
    const { communityId } = req.params;
    const { id, title, content, sentBucketName, sentEmails } = req.body;
    const magicReachEmailData = {
      id,
      learnerId,
      communityId,
      title,
      content,
      sentBucketName,
      sentEmails,
    };
    const result =
      await CommunityMagicReachEmailService.updateDraftMagicReachEmail(
        magicReachEmailData
      );
    res.status(http.OK).json(result);
  } catch (err) {
    logger.info(`Error updating draft email: `, err);
    const error = new Error('Error updating sent email');
    error.status = 400;
    return next(error);
  }
};

const deleteDraftMagicReachEmail = async (req, res, next) => {
  try {
    const { communityId, id } = req.params;
    const params = {
      id,
      communityId,
    };
    const result =
      await CommunityMagicReachEmailService.deleteDraftMagicReachEmail(
        params
      );
    res.status(http.OK).json({});
  } catch (err) {
    logger.info(`Error deleting magic reach draft email: `, err);
    const error = new Error('Error deleting magic reach draft');
    error.status = 400;
    return next(error);
  }
};

const sendDraftEmail = async (req, res, next) => {
  try {
    const { communityId } = req.params;
    const {
      id,
      toMail,
      isPreview,
      activeBucketId,
      sentFromRecipients,
      platforms,
    } = req.body;
    const { email, learner } = req.user;
    let learnerObj;
    try {
      learnerObj = await getLearner({ email });
      if (!learner) {
        logger.error('No learner found for given params');
        const err = new Error('No Learner found');
        throw err;
      }
    } catch (err) {
      logger.error('Error when finding the learner: ', err);
      const error = new Error('Invalid Learner');
      error.status = httpStatus.BAD_REQUEST;
      return next(error);
    }
    let replyToMailName;
    if (learnerObj?.firstName?.length && learnerObj?.lastName?.length) {
      replyToMailName = `${learnerObj?.firstName} ${learnerObj?.lastName}`;
    } else if (learnerObj?.firstName?.length) {
      replyToMailName = learnerObj?.firstName;
    } else if (learnerObj?.lastName?.length) {
      replyToMailName = learnerObj?.lastName;
    } else {
      replyToMailName = 'Community Manager';
    }
    const params = {
      id,
      communityId,
      toMail,
      isPreview,
      replyToMail: email,
      replyToMailName,
      activeBucketId,
      sentFromRecipients,
      platforms,
    };
    const result = await CommunityMagicReachEmailService.sendDraftEmail(
      params
    );
    res.status(http.OK).json(result);
  } catch (err) {
    logger.info(`Error sending magic reach draft email: `, err);
    const error = new Error('Error sending magic reach draft');
    error.status = 400;
    return next(error);
  }
};

const sendWhatsappTemplateMsg = async (req, res, next) => {
  try {
    const { communityId } = req.params;
    const {
      id,
      toMail,
      isPreview,
      isOnlyWhatsapp,
      activeBucketId,
      sentFromRecipients,
      platforms,
      templateObjectId,
    } = req.body;

    const { email, learner } = req.user;
    let managerLearnerObj;
    try {
      managerLearnerObj = await getLearner({ email });
      if (!learner) {
        logger.error('No learner found for given params');
        const err = new Error('No Learner found');
        throw err;
      }
    } catch (err) {
      logger.error('Error when finding the learner: ', err);
      const error = new Error('Invalid Learner');
      error.status = httpStatus.BAD_REQUEST;
      return next(error);
    }

    const params = {
      id,
      communityId,
      toMail,
      isPreview,
      isOnlyWhatsapp,
      activeBucketId,
      sentFromRecipients,
      platforms,
      templateObjectId,
    };
    const result =
      await CommunityMagicReachEmailService.sendWhatsappTemplateMsg(
        params
      );
    res.status(http.OK).json(result);
  } catch (err) {
    logger.info(`Error sending magic reach whatsapp template msg: `, err);
    const error = new Error(
      'Error sending magic reach whatsapp template msg'
    );
    error.status = 400;
    return next(error);
  }
};
const uploadDraftMagicReachImage = async (req, res, next) => {
  // TODO: Make the upload image give the link
  return res.status(http.OK).json({ imageUrl: req.file.location });
};

const saveMagicReachEmailsByDraftId = async (req, res, next) => {
  try {
    const { bucketName, draftId, emailArray } = req.body;
    const { communityId } = req.params;
    const result = { bucket: false, emailArray: false };
    if (bucketName) {
      result.bucket =
        await magicReachService.saveMagicReachEmailsFromBucket(
          communityId,
          bucketName,
          draftId
        );
    }
    if (emailArray) {
      result.emailArray =
        await magicReachService.saveMagicReachEmailsFromArray(
          emailArray,
          draftId
        );
    }
    await magicReachService.addFullNameAndProfileImageForRecipients(
      draftId
    );
    res.json(result);
  } catch (err) {
    logger.info(`Error saving draft emails : `, err);
    err.status = 400;
    return next(err);
  }
};

const deleteMagicReachRecipientsByDraftId = async (req, res, next) => {
  try {
    const { bucketName, draftId, emailArray, allRecipients } = req.body;
    const { communityId } = req.params;
    if (!draftId) {
      throw new Error('DraftId not defined');
    }
    const result = await magicReachService.deleteMagicReachEmailRecipients(
      draftId,
      communityId,
      { bucketName, emailArray, allRecipients }
    );
    res.json(result);
  } catch (err) {
    logger.info(`Error deleteing draft emails : `, err);
    err.status = 400;
    return next(err);
  }
};

const getMagicReachRecipientsForDraftId = async (req, res, next) => {
  try {
    const { draftId } = req.query;
    let { pageNumber = 1, pageSize = 10 } = req.query;
    pageNumber = parseInt(pageNumber, 10);
    pageSize = parseInt(pageSize, 10);

    if (!draftId) {
      throw new Error('DraftId not defined');
    }
    if (pageNumber < 1) pageNumber = 1;
    if (pageSize < 1) pageSize = 10;

    const result = await magicReachService.getAllRecipientsForDraftId(
      draftId,
      pageNumber,
      pageSize
    );
    res.json(result);
  } catch (err) {
    logger.info(`Error getting draft emails : `, err);
    err.status = 400;
    return next(err);
  }
};

const getAllRecipientsMetaDataForDraftId = async (req, res, next) => {
  try {
    const { communityId } = req.params;
    const { buckets, draftId } = req.body;

    if (!draftId) {
      throw new Error('DraftId not defined');
    }

    const result = await magicReachService.getAllRecipientsMetaData(
      draftId,
      buckets,
      communityId
    );
    res.json(result);
  } catch (err) {
    logger.info(`Error getting draft metadata : `, err);
    err.status = 400;
    return next(err);
  }
};

module.exports = {
  getMagicReachBucketData,
  fetchBucketMetaData,
  createMagicReachDraftEmail,
  getAllDraftMagicReachEmails,
  getAllSentMagicReachEmails,
  updateDraftMagicReachEmail,
  deleteDraftMagicReachEmail,
  uploadDraftMagicReachImage,
  sendDraftEmail,
  sendWhatsappTemplateMsg,
  saveMagicReachEmailsByDraftId,
  deleteMagicReachRecipientsByDraftId,
  getMagicReachRecipientsForDraftId,
  getAllRecipientsMetaDataForDraftId,
};
