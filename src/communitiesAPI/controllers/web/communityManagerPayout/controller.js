const status = require('http-status');
const { communityEventsService } = require('../../../services/web');
const communityManagerPayoutService = require('../../../services/common/communityManagerPayout.service');
const logger = require('../../../../services/logger.service');
const {
  createPayoutBankAccountSchema,
  updateInputSchema,
} = require('../../../apiSchemas/communityManagerPayout.schema');

exports.createPayoutBankAccount = async (req, res) => {
  // save user Id and learner id as well
  try {
    const params = { ...req.body, ...req.params };
    await createPayoutBankAccountSchema.validate(params);
    const body = createPayoutBankAccountSchema.cast(params);
    const payout =
      await communityManagerPayoutService.createPayoutBankAccount(body);
    return res.status(status.CREATED).json(payout);
  } catch (err) {
    logger.error('Error creating payout: ', err);
    return res.status(status.BAD_REQUEST).json({
      message: err.message,
      fieldError: err?.path,
    });
  }
};

exports.updatePayoutBankAccount = async (req, res) => {
  try {
    const params = { ...req.body, ...req.params };
    await updateInputSchema.validate(params);
    const body = updateInputSchema.cast(params);
    const payout =
      await communityManagerPayoutService.updatePayoutBankAccount(
        { communityId: body.communityId },
        body
      );
    logger.info('Updated payout, ready to return response: ', payout);
    return res.status(status.OK).json(payout);
  } catch (error) {
    logger.error('Error updating payout: ', error);
    return res.status(status.BAD_REQUEST).json({
      message: error.message,
      fieldError: error?.path,
    });
  }
};

exports.deletePayoutBankAccount = async (req, res) => {
  try {
    const { communityId } = req.params;
    await communityManagerPayoutService.deletePayoutBankAccount({
      communityId,
    });

    return res
      .status(status.OK)
      .json(`Deleted payout for communityId: ${communityId}`);
  } catch (error) {
    logger.error('Error deleting payout: ', error);
    return res.status(status.BAD_REQUEST).json({
      message: error.message,
      fieldError: error?.path,
    });
  }
};

exports.getPayoutBankAccount = async (req, res) => {
  try {
    const response = {
      hasPaidEvents: false,
      payoutDetails: null,
    };
    const { communityId } = req.params;
    response.hasPaidEvents =
      await communityEventsService.doesCommunityHavePaidEvents(
        communityId
      );
    const data = await communityManagerPayoutService.getPayoutBankAccount({
      communityId,
    });
    if (data) {
      response.payoutDetails = data;
    }
    return res.status(status.OK).json(response);
  } catch (error) {
    logger.error('Error getting payout: ', error);
    return res.status(status.BAD_REQUEST).json({
      message: error.message,
      fieldError: error?.path,
    });
  }
};
