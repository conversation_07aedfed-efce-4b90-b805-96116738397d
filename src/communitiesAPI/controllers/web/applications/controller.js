const yup = require('yup');
const status = require('http-status');
const communityApplicationService = require('../../../services/web/communityApplication.service');

const inputSchema = yup.object().shape({
  status: yup.string().required().trim(),
  subscriptionId: yup.number().required(),
});
const pathSchema = yup.object().shape({
  applicationId: yup
    .string()
    .required()
    .notOneOf(['undefined', undefined]),
});

async function updateCommunityApplicationStatus(req, res, next) {
  try {
    const isQueryValid = await inputSchema.isValid(req.body);
    const userEmail = req.user.email;
    if (!isQueryValid) {
      const error = new Error('Invalid Parameters');
      error.status = status.BAD_REQUEST;
      return next(error);
    }
    const body = inputSchema.cast(req.body);

    const applicationId = req.params.applicationId;
    const subscriptionId = req.body.subscriptionId;

    const application =
      await communityApplicationService.updateApplicationStatus(
        // applicationId,
        subscriptionId,
        body,
        userEmail
      );

    return res.status(200).json({ data: application });
  } catch (errorData) {
    return next(errorData);
  }
}

async function updateApplicationByAdmin(req, res, next) {
  try {
    const isQueryValid = await inputSchema.isValid(req.body);
    const isPathValid = await pathSchema.isValid(req.params);
    const userEmail = req.user.email;
    if (!isQueryValid && !isPathValid) {
      const error = new Error('Invalid Parameters or Path');
      error.status = status.BAD_REQUEST;
      return next(error);
    }

    const body = inputSchema.cast(req.body);
    const path = pathSchema.cast(req.params);
    const application =
      await communityApplicationService.updateApplicationByAdmin(
        path.applicationId,
        body,
        userEmail
      );
    return res.status(200).json({ data: application });
  } catch (errorData) {
    errorData.status = status.BAD_REQUEST;
    return next(errorData);
  }
}

module.exports = {
  updateCommunityApplicationStatus,
  updateApplicationByAdmin,
};
