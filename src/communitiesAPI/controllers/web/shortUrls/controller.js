const { shortUrlService } = require('../../../services/web');
const logger = require('../../../../services/logger.service');

const generateAllCommunityShortUrls = async (req, res, next) => {
  try {
    const allCommunityShortUrls =
      await shortUrlService.allCommunityShortUrls();
    return res.json(allCommunityShortUrls);
  } catch (err) {
    logger.error(
      'Error on generate Community Short Urls: ',
      err,
      err.stack
    );
    const error = new Error('Error on get request');
    error.status = 500;
    return next(error);
  }
};

const generateSpecificCommunityShortUrls = async (req, res, next) => {
  try {
    const communityObjectId = req.params.communityId;
    const specificCommunityShortUrls =
      await shortUrlService.specificCommunityShortUrls(communityObjectId);
    return res.json(specificCommunityShortUrls);
  } catch (err) {
    logger.error(
      'Error on generate Community Short Urls: ',
      err,
      err.stack
    );
    const error = new Error(`Error on get request: ${err}`);
    error.status = err.status || 500;
    return next(error);
  }
};

const generateSpecificTypeShortUrl = async (req, res, next) => {
  try {
    const { communityId, type, typeId } = req.body;
    const specificTypeShortUrl =
      await shortUrlService.specificTypeShortUrl(
        communityId,
        type,
        typeId
      );
    return res.json(specificTypeShortUrl);
  } catch (err) {
    logger.error(
      'Error on generate Community Short Urls: ',
      err,
      err.stack
    );
    const error = new Error(`Error on get request: ${err}`);
    error.status = err.status || 500;
    return next(error);
  }
};

module.exports = {
  generateAllCommunityShortUrls,
  generateSpecificCommunityShortUrls,
  generateSpecificTypeShortUrl,
};
