const {
  getCommunityBotDetails,
  getCommunities,
  getActiveCommunity,
  getCommunityDataForLearner,
  getAllCommunity,
  getCommunityData,
  getCommunityById,
  getAdminCommunities,
  addBotToCommunity,
  deleteBotFromCommunity,
  uploadThumbnailImage,
  uploadBannerImage,
  updateCommunity,
  updateCommunityRevenueGoal,
  linkValidation,
  setMonetisationModalViewed,
  setMoneyPageViewed,
  createLog,
  getCommunityProductPrices,
  getCommunitySubscriptionDisplayPrices,
  updateCommunityForPublicApi,
} = require('./controller');

module.exports = {
  getCommunityBotDetails,
  getCommunities,
  getActiveCommunity,
  getCommunityDataForLearner,
  getAllCommunity,
  getCommunityData,
  getCommunityById,
  getAdminCommunities,
  addBotToCommunity,
  deleteBotFromCommunity,
  uploadThumbnailImage,
  uploadBannerImage,
  updateCommunity,
  updateCommunityRevenueGoal,
  linkValidation,
  setMonetisationModalViewed,
  setMoney<PERSON>ageViewed,
  createLog,
  getCommunityProductPrices,
  getCommunitySubscriptionDisplayPrices,
  updateCommunityForPublicApi,
};
