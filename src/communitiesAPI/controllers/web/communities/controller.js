/* eslint-disable no-unused-vars */
const ObjectId = require('mongoose').Types.ObjectId;
const yup = require('yup');
const status = require('http-status');
const {
  communityService,
  communitySubscriptionsService,
} = require('../../../services/web');
const subscriptionCommonService = require('../../../../services/common/subscription.service');
const {
  IMAGEASSETBUCKET_CLOUDFRONT_BASE_URL,
  DEFAULT_CURRENCY,
} = require('../../../../constants/common');
const { BOT_STATUSES, CENTS_PER_DOLLAR } = require('../../../constants');
const logger = require('../../../../services/logger.service');
const axios = require('../../../../clients/axios.client');
const { MAIN_PAYMENT_BACKEND_URL } = require('../../../../config');
const {
  ToUserError,
  ParamError,
} = require('../../../../utils/error.util');
const { getUserIP } = require('../../../../utils/headers.util');
const utils = require('../../../utils');
const { getCommunityService } = require('../../../../services/community');
const schema = require('./schema');

const addBotToCommunitySchema = yup.object().shape({
  type: yup.string().trim().required(),
  serverKey: yup.string().trim().required(),
});

const getCommunities = async (req, res, next) => {
  const learnerId = req.user.learner.learnerId;
  const learnerObjectId = new ObjectId(req.user.learner._id);
  const userObjectId = new ObjectId(req.user._id);
  const ip = req.ip;
  const email = req?.userObject?.email;

  try {
    const communities = await communityService.getLearnerCommunities(
      learnerId,
      learnerObjectId,
      userObjectId,
      req.query,
      ip,
      email
    );
    // eslint-disable-next-line no-param-reassign
    res = utils.addSignedCookiesToResponse(res, true);

    res.json(communities);
  } catch (err) {
    logger.error('Error occured when getting communities:', err);
    const error = new Error('Error getting communities');
    error.status = 400;
    return next(error);
  }
};

const getActiveCommunity = async (req) => {
  const learnerObjectId = new ObjectId(req.user.learner._id);
  const email = req?.userObject?.email;
  const ip = req.ip;

  const activeCommunity = await communityService.getActiveCommunity({
    learnerObjectId,
    params: req.query ?? {},
    email,
    ip,
  });

  return activeCommunity;
};

const getCommunityDataForLearner = async (req) => {
  const communityId = req.params.communityId;
  const communityObjectId = new ObjectId(communityId);
  const learnerObjectId = new ObjectId(req.user.learner._id);
  const userObjectId = new ObjectId(req.user._id);

  const result =
    await communityService.getCommunityDataWithSubscriptionAndRole({
      communityObjectId,
      learnerObjectId,
      userObjectId,
    });

  return result;
};

const getAllCommunity = async (req, res, next) => {
  try {
    const learnerObjectId = req.user?.learner?._id;
    const {
      showPaid,
      showManager,
      showPrice,
      includePendingSubs,
      pageNo,
      pageSize,
    } = req.query;

    const data = await getCommunityService.getAllCommunity({
      learnerObjectId,
      showPaid: showPaid === 'true',
      showManager: showManager === 'true',
      showPrice: showPrice === 'true',
      includePendingSubs: includePendingSubs === 'true',
      pageNo,
      pageSize,
    });

    res.json({ data });
  } catch (err) {
    logger.error('getAllCommunity: error:', err);
    return next(err);
  }
};

const getCommunityData = async (req, res, next) => {
  try {
    const { communityCode, affiliateCode, communitySlug } = req.query;

    const result = await getCommunityService.getCommunityByCode({
      communityCode,
      communitySlug,
      affiliateCode,
    });

    res.json(result);
  } catch (err) {
    logger.error('getCommunityData: error:', err);
    return next(err);
  }
};

const getCommunityProductPrices = async (req, res, next) => {
  const { communityId } = req.params;

  try {
    const data = await communityService.retrieveCommunityProductPrices(
      communityId
    );

    res.json({ data });
  } catch (err) {
    logger.error(
      'Error occured when getting community product prices:',
      err
    );
    const error = new Error('Error getting community product prices');
    error.status = 400;
    return next(error);
  }
};

const getCommunitySubscriptionDisplayPrices = async (req, res, next) => {
  const { communityId } = req.params;
  const user = req.user;
  const ip = getUserIP(req) || null;
  const { paymentMethodCountryCode, paymentProvider } = req.query;

  try {
    const data =
      await subscriptionCommonService.getSubscriptionDisplayPrices({
        communityId,
        user,
        ip,
        paymentMethodCountryCode,
        selectedPaymentProvider: paymentProvider,
      });

    res.json(data);
  } catch (err) {
    logger.error(
      'Error occured when getting community product prices:',
      err
    );
    const error = new Error('Error getting community product prices');
    error.status = 400;
    return next(error);
  }
};

const getCommunityById = async (req, res, next) => {
  try {
    const { communityId = null } = req?.params;
    if (!communityId) {
      const errorMessage =
        'Unable to get community details due to missing parameter';
      logger.error(errorMessage);
      const error = new Error(errorMessage);
      return next(error);
    }
    const community = await communityService.getCommunityById(communityId);
    res.status(200).json(community);
  } catch (error) {
    logger.error('Error occured when getting community by id:', error);
    error.status = 400;
    return next(error);
  }
};

const getCommunityBotDetails = async (req, res, next) => {
  const { communityId } = req.params;
  try {
    const communities = await communityService.getCommunityBotDetails(
      communityId
    );
    res.json(communities);
  } catch (err) {
    logger.error(
      'Error occured when getting communities bots:',
      err,
      err.stack
    );
    const error = new Error('Error getting communities bots');
    error.status = 400;
    return next(error);
  }
};

const getAdminCommunities = async (req, res, next) => {
  const email = req.user.email;
  try {
    const communities = await communityService.getAdminCommunities(email);

    const result = await Promise.all(
      communities.map(async (community) => {
        const doc = community;
        const memberCount =
          await communitySubscriptionsService.getMemberCountByCommunityCode(
            community?.code
          );
        const memberCountForPriceCheck =
          await communitySubscriptionsService.getMemberCountByCommunityCode(
            community?.code,
            false,
            true
          );
        doc.members = memberCount;
        doc.isPricingEditable = !memberCountForPriceCheck;

        try {
          if (community?.stripeProductId) {
            const productRes = await axios.get(
              `${MAIN_PAYMENT_BACKEND_URL}/api/v1/get-community-pricing?product_id=${community?.stripeProductId}`
            );
            doc.productPricing =
              productRes.data?.[0]?.unit_amount / CENTS_PER_DOLLAR;
          }
        } catch (error) {
          logger.error('Error fetching community pricing details:', error);
        }
        return doc;
      })
    );

    res.json(result);
  } catch (err) {
    logger.error(
      'Error occured when getting communities:',
      err,
      err.stack
    );
    const error = new Error('Error getting communities');
    error.status = err.status || 500;
    return next(error);
  }
};

const setMonetisationModalViewed = async (req, res, next) => {
  const communityId = req.params.communityId;
  try {
    const community = await communityService.setMonetisationModalViewed(
      communityId
    );

    res.json(community);
  } catch (err) {
    logger.error(
      'Error occured when setting hasViewedMonetisationModal:',
      err,
      err.stack
    );
    const error = new Error(
      'Error occured when setting hasViewedMonetisationModal'
    );
    error.status = err.status || 500;
    return next(error);
  }
};

const setMoneyPageViewed = async (req, res, next) => {
  const communityId = req?.params?.communityId;
  const { hasViewedMoneyPage = false } = req?.body;
  try {
    const community = await communityService.setMoneyPageViewed(
      communityId,
      hasViewedMoneyPage
    );

    res.json(community);
  } catch (err) {
    logger.error(
      'Error occured when setting setMoneyPageViewed:',
      err,
      err.stack
    );
    const error = new Error(
      'Error occured when setting setMoneyPageViewed'
    );
    error.status = err.status || 500;
    return next(error);
  }
};

const addBotToCommunity = async (req, res, next) => {
  const { communityId } = req.params;
  const isBodyValid = await addBotToCommunitySchema.isValid(req.body);
  if (!isBodyValid) {
    logger.info('Body is invalid: ', req.body);
    const error = new Error('Invalid Body type');
    error.status = status.BAD_REQUEST;
    return next(error);
  }
  const {
    type,
    serverKey,
    botStatus = BOT_STATUSES.CONNECTED,
  } = await addBotToCommunitySchema.cast(req.body);
  try {
    const { data, error } = await communityService.addBotToCommunity(
      communityId,
      type,
      serverKey,
      botStatus
    );
    if (error) {
      const err = new Error(error.message);
      err.status = status.BAD_REQUEST;
      return next(err);
    }
    res.json(data);
  } catch (err) {
    logger.error('Error occured adding bot data to community:', err);
    const error = new Error('Error occured adding bot data to community:');
    error.status = 400;
    return next(error);
  }
};

const deleteBotFromCommunity = async (req, res, next) => {
  const { communityId } = req.params;
  const { type } = req.body;
  try {
    const community = await communityService.deleteBotFromCommunity(
      communityId,
      type
    );
    res.json(community);
  } catch (err) {
    logger.info(
      'Error occured deleting bot data from community:',
      err,
      err.stack
    );
    const error = new Error(
      'Error occured deleting bot data from community:'
    );
    error.status = err.status || 500;
    return next(error);
  }
};

const uploadThumbnailImage = async (req, res, next) => {
  const { communityId } = req.params;
  const { width, height } = req.body;
  const file = req.file;
  if (!file) {
    logger.info('No file uploaded to s3 for community thumbnail');
    const error = new Error(
      'No file uploaded to s3 for community thumbnail'
    );
    error.status = 400;
    return next(error);
  }

  try {
    const pathToReplace = `${file.location?.split(file?.bucket)[0]}${
      file?.bucket
    }`;
    const src = file.location?.replace(
      pathToReplace,
      `${IMAGEASSETBUCKET_CLOUDFRONT_BASE_URL}`
    );

    const community = await communityService.getCommunitiesByIds([
      communityId,
    ]);

    if (community.length !== 1) {
      logger.info('Cannot find community with id ', communityId);
      const error = new Error('Cannot find community');
      error.status = 400;
      return next(error);
    }
    const thumbnailImgData = community?.[0]?.thumbnailImgData ?? {};
    thumbnailImgData.desktopImgData = {
      src,
      meta: { width, height },
    };
    thumbnailImgData.mobileImgData = {
      src,
      meta: { width, height },
    };
    const props = { src, width, height };
    const communityCheckoutCardData =
      community?.[0]?.communityCheckoutCardData ?? {};
    let imgData = community?.[0]?.communityCheckoutCardData?.imgData ?? {};
    let mobileImgProps =
      community?.[0]?.communityCheckoutCardData?.imgData?.mobileImgProps ??
      {};
    let desktopImgProps =
      community?.[0]?.communityCheckoutCardData?.imgData
        ?.desktopImgProps ?? {};
    mobileImgProps = { ...mobileImgProps, ...props };
    desktopImgProps = { ...desktopImgProps, ...props };
    imgData = { ...imgData, mobileImgProps, desktopImgProps };
    communityCheckoutCardData.imgData = imgData;

    const updatedData = await communityService.updateOneCommunity(
      { _id: new ObjectId(communityId) },
      { thumbnailImgData, communityCheckoutCardData }
    );
    res.json(updatedData);
  } catch (err) {
    logger.info('Error occured when updating community thumbnail:', err);
    const error = new Error('Error updating community thumbnail');
    error.status = 400;
    return next(error);
  }
};

const uploadBannerImage = async (req, res, next) => {
  const { communityId } = req.params;
  const file = req.file;
  if (!file) {
    logger.info('No file uploaded to s3 for community banner');
    const error = new Error('No file uploaded to s3 for community banner');
    error.status = 400;
    return next(error);
  }

  try {
    const pathToReplace = `${file.location?.split(file?.bucket)[0]}${
      file?.bucket
    }`;
    const src = file.location?.replace(
      pathToReplace,
      `${IMAGEASSETBUCKET_CLOUDFRONT_BASE_URL}`
    );

    const community = await communityService.getCommunitiesByIds([
      communityId,
    ]);

    if (community.length !== 1) {
      logger.info('Cannot find community with id ', communityId);
      const error = new Error('Cannot find community');
      error.status = 400;
      return next(error);
    }
    const fullScreenBannerImgData =
      community?.[0]?.fullScreenBannerImgData ?? {};
    const mobileImgProps =
      community?.[0]?.fullScreenBannerImgData?.mobileImgProps ?? {};
    const desktopImgProps =
      community?.[0]?.fullScreenBannerImgData?.desktopImgProps ?? {};
    mobileImgProps.src = src;
    desktopImgProps.src = src;
    fullScreenBannerImgData.mobileImgProps = mobileImgProps;
    fullScreenBannerImgData.desktopImgProps = desktopImgProps;
    const updatedData = await communityService.updateOneCommunity(
      { _id: new ObjectId(communityId) },
      { fullScreenBannerImgData }
    );
    res.json(updatedData);
  } catch (err) {
    logger.info('Error occured when updating community banner:', err);
    const error = new Error('Error updating community banner');
    error.status = 400;
    return next(error);
  }
};

const updateCommunityForPublicApi = async (req, res, next) => {
  const { communityId } = req.params;
  const body = schema.updateCommunityPublicSchema.cast(req.body, {
    stripUnknown: true,
  });

  if (body?.length === 0) {
    throw new ParamError('Empty Body');
  }
  const updatedData = await communityService.updateCommunityForPublicApi(
    communityId,
    body
  );
  return updatedData;
};

const updateCommunity = async (req, res, next) => {
  try {
    const { communityId } = req.params;
    const body = req.body;
    const user = req.user;
    if (body?.length === 0) {
      logger.info('Empty Body');
      const error = new Error('Empty Body');
      error.status = 400;
      return next(error);
    }
    const userAgent = req?.headers['user-agent'] || null;
    const updatedData = await communityService.updateCommunityData(
      communityId,
      body,
      user,
      userAgent
    );
    return res.json(updatedData);
  } catch (err) {
    logger.info('Error occured when updating community :', err);

    if (err instanceof ToUserError) {
      return next(err);
    }

    const error = new Error(err || 'Error updating community');
    error.status = 400;
    return next(error);
  }
};

const updateCommunityRevenueGoal = async (req, res, next) => {
  try {
    const { communityId = null } = req?.params;
    const { revenueGoalAmount = null, currency = DEFAULT_CURRENCY } =
      req?.body;

    if (!revenueGoalAmount) {
      logger.info('Invalid Payload');
      const error = new Error('Invalid Payload');
      error.status = 400;
      return next(error);
    }

    const updatedData = await communityService.updateOneCommunity(
      { _id: new ObjectId(communityId) },
      {
        revenueGoal: {
          amount: revenueGoalAmount,
          currency,
        },
      }
    );

    res.json(updatedData);
  } catch (err) {
    logger.info(
      'Error occured when updating community revenue goal:',
      err,
      err.stack
    );
    const error = new Error(
      err || 'Error updating community revenue goal'
    );
    error.status = err.status || 500;
    return next(error);
  }
};

const linkValidation = async (req, res, next) => {
  try {
    const { communityId } = req.params;
    const body = req.body;
    if (body?.length === 0) {
      logger.info('Empty Body');
      const error = new Error('Empty Body');
      error.status = 400;
      return next(error);
    }
    const isLink = await communityService.linkValidation(
      body?.link,
      communityId
    );

    if (!isLink) {
      throw new ParamError('Validation of link failed');
    }
    res.json({
      status: 'valid',
      message: 'Nice, this domain is available',
    });
  } catch (err) {
    logger.info('Error vallidating link :', err, err.stack);
    const error = new Error(err || 'Error vallidating link');
    error.status = err.status || 500;
    return next(error);
  }
};

const createLog = async (req, res, next) => {
  try {
    const { communityId, message } = req.body;
    if (!communityId || !message) {
      logger.info('Invalid Payload');
      const error = new Error('Invalid Payload');
      error.status = 400;
      return next(error);
    }

    const messageInfo = `Front end Log for communityId: ${communityId} and with message: ${message}`;
    logger.info(messageInfo);
    return res.json({ status: 'success' });
  } catch (err) {
    logger.info('Error creating log:', err, err.stack);
    const error = new Error(err || 'Error creating log');
    error.status = err.status || 500;
    return next(error);
  }
};

module.exports = {
  updateCommunityForPublicApi,
  getCommunityBotDetails,
  getCommunities,
  getActiveCommunity,
  getAllCommunity,
  getCommunityDataForLearner,
  getCommunityData,
  getCommunityById,
  getAdminCommunities,
  addBotToCommunity,
  deleteBotFromCommunity,
  uploadThumbnailImage,
  uploadBannerImage,
  updateCommunity,
  updateCommunityRevenueGoal,
  linkValidation,
  setMonetisationModalViewed,
  setMoneyPageViewed,
  createLog,
  getCommunityProductPrices,
  getCommunitySubscriptionDisplayPrices,
};
