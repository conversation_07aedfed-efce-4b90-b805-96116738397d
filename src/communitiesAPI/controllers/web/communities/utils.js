const { CHAT_PLATFORMS } = require('../../../../constants/common');
const logger = require('../../../../services/logger.service');
const { BOT_STATUSES } = require('../../../constants');
const CommunityModel = require('../../../models/community.model');
const SocialChatConnection = require('../../../models/socialChatConnection.model');

const performAdditionalTasksForSocialBots = async (
  communityId,
  type,
  serverKey,
  botStatus
) => {
  if (botStatus === BOT_STATUSES.CONNECTED) {
    if (
      [
        CHAT_PLATFORMS.FACEBOOK,
        CHAT_PLATFORMS.LINKEDIN,
        CHAT_PLATFORMS.SLACK,
        CHAT_PLATFORMS.LINE,
        CHAT_PLATFORMS.TELEGRAM,
      ].includes(type)
    ) {
      const communityDoc = await CommunityModel.findById(communityId);
      if (!communityDoc) {
        logger.error('No community found for id: ', communityId);
        return null;
      }
      const socialChatDoc = await SocialChatConnection.findById(serverKey);
      if (!socialChatDoc) {
        logger.error(
          'No social chat connection found for id: ',
          serverKey
        );
        return null;
      }
      const { inviteLink } = socialChatDoc;
      if (!inviteLink) {
        logger.error(
          'No invite link found for social chat connection: ',
          serverKey
        );
        return null;
      }
      const platformItem = {
        name: type.toLowerCase(),
        link: inviteLink,
      };
      if (communityDoc.platforms.length === 0) {
        communityDoc.platforms.push(platformItem);
        await communityDoc.save();
      }
    }
  }
};

module.exports = {
  performAdditionalTasksForSocialBots,
};
