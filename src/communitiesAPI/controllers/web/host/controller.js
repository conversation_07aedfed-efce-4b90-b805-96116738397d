const { communityHostService } = require('../../../services/web');
const status = require('http-status');
const logger = require('../../../../services/logger.service');

const createGuestHost = async (req, res, next) => {
  try {
    const params = {
      firstName: req.body.firstName,
      lastName: req.body.lastName,
      profileImage: req.body.profileImage,
      communityObjectId: req.params.communityId,
    };

    res.json(await communityHostService.createGuestHost(params));
  } catch (err) {
    logger.info('Error in creating new guest host: ', err);
    const error = new Error('Error creating new guest host');
    error.status = status.BAD_REQUEST;
    return next(error);
  }
};

const getCommunityHosts = async (req, res, next) => {
  try {
    const search = req.query?.search;
    const communityObjectId = req.params.communityId;
    res.json(
      await communityHostService.getCommunityHosts(
        communityObjectId,
        search
      )
    );
  } catch (err) {
    logger.info('Error in getting community hosts: ', err);
    const error = new Error('Error in getting community hosts');
    error.status = status.BAD_REQUEST;
    return next(error);
  }
};

module.exports = {
  createGuestHost,
  getCommunityHosts,
};
