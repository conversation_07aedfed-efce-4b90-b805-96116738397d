const status = require('http-status');
const {
  REACTION_ENTITY_COLLECTIONS,
  REACTION_TYPES,
  REACTION_STATES,
} = require('../../../../constants/common');
const logger = require('../../../../services/logger.service');
const reactionService = require('../../../../services/reaction.service');
const {
  isDemoCommunity,
} = require('../../../services/common/community.service');

const addReaction = async (req, res, next) => {
  let statusCode;
  let result;
  try {
    const { _id: userObjectId, learner = null } = req?.user;
    const { _id: learnerObjectId } = learner;
    const { communityId = null, commentId = null } = req?.params;
    const isDemo = (await isDemoCommunity(communityId)) || false;
    const { type = REACTION_TYPES.LIKE } = req?.body;
    const output = await reactionService.addReaction({
      entityCollection: REACTION_ENTITY_COLLECTIONS.COMMENT,
      entityObjectId: commentId,
      userObjectId,
      learnerObjectId,
      type,
      isDemo,
    });
    statusCode = status.CREATED;
    result = { data: output };
  } catch (error) {
    const errorMessage = 'Unable to react to announcement comment';
    logger.error(errorMessage, ' due to: ', error);
    statusCode = status.BAD_REQUEST;
    result = { error: { code: statusCode, message: errorMessage } };
  }
  return res.status(statusCode).json(result);
};

const removeReaction = async (req, res, next) => {
  let statusCode;
  let result;
  try {
    const { _id: userObjectId, learner = null } = req?.user;
    const { _id: learnerObjectId } = learner;
    const { commentId = null } = req?.params;
    const { type = REACTION_TYPES.LIKE } = req?.query;
    const output = await reactionService.removeReaction({
      entityCollection: REACTION_ENTITY_COLLECTIONS.COMMENT,
      entityObjectId: commentId,
      userObjectId,
      learnerObjectId,
      type,
    });
    statusCode = status.NO_CONTENT;
    result = { data: output };
  } catch (error) {
    const errorMessage =
      'Unable to remove reaction to announcement comment';
    logger.error(errorMessage, ' due to: ', error);
    statusCode = status.BAD_REQUEST;
    result = { error: { code: statusCode, message: errorMessage } };
  }
  return res.status(statusCode).json(result);
};

const reactionsForAnnouncementComment = async (req, res, next) => {
  let statusCode;
  let result;
  try {
    const { _id: userObjectId, learner = null } = req?.user;
    const { _id: learnerObjectId } = learner || {};
    const { commentId = null } = req?.params;
    const { type = REACTION_TYPES.LIKE } = req?.query;
    let { paginate = 0, pageNum = null, pageSize = null } = req?.query;
    paginate = parseInt(paginate, 10) || 0;
    if (paginate === 1) {
      pageNum = parseInt(pageNum, 10) || 1;
      pageSize = parseInt(pageSize, 10) || 10;
    }
    const reactions = await reactionService.getAllEntityReactions({
      entityCollection: REACTION_ENTITY_COLLECTIONS.COMMENT,
      entityObjectId: commentId,
      learnerObjectId,
      type,
      state: REACTION_STATES.REACTED,
      paginate,
      pageNum,
      pageSize,
    });
    if (!reactions?.length) {
      const errorMessage =
        'Unable to fetch reactions for announcement comment!';
      logger.error(errorMessage);
      statusCode = status.BAD_REQUEST;
      result = { error: { code: statusCode, message: errorMessage } };
    }
    statusCode = status.OK;
    result = { data: { reactions } };
  } catch (error) {
    const errorMessage =
      'Unable to get reactions for announcement comment';
    logger.error(errorMessage, ' due to: ', error);
    statusCode = status.BAD_REQUEST;
    result = { error: { code: statusCode, message: errorMessage } };
  }
  return res.status(statusCode).json(result);
};

module.exports = {
  addReaction,
  removeReaction,
  reactionsForAnnouncementComment,
};
