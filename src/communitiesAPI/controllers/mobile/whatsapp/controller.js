const status = require('http-status');
const yup = require('yup');
const { whatsappService } = require('../../../services/mobile');

const getWhatsappMembersCountPathSchema = yup.object().shape({
  communityCode: yup.string().required(),
});

const getWhatsappMembersCount = async (req, res) => {
  try {
    const isPathValid = await getWhatsappMembersCountPathSchema.isValid(
      req.params
    );

    if (!isPathValid) {
      throw new Error('Invalid Parameters or Path');
    }

    const { communityCode } = getWhatsappMembersCountPathSchema.cast(
      req.params
    );

    const membersCount =
      await whatsappService.retrieveWhatsappCommunityMembersCountByCode(
        communityCode
      );

    return res.json({ status: 'success', data: { membersCount } });
  } catch (err) {
    return res.status(status.BAD_REQUEST).json({ error: err.message });
  }
};

const getWhatsappStatusPathSchema = yup.object().shape({
  communityId: yup.string().required(),
});

const getWhatsappStatus = async (req, res) => {
  try {
    const isPathValid = await getWhatsappStatusPathSchema.isValid(
      req.params
    );

    if (!isPathValid) {
      throw new Error('Invalid Parameters or Path');
    }

    const { communityId } = getWhatsappStatusPathSchema.cast(req.params);

    const whatsappCommunityStatus =
      await whatsappService.retrieveWhatsappCommunityStatus(communityId);

    return res.json({ status: 'success', data: whatsappCommunityStatus });
  } catch (err) {
    return res.status(status.BAD_REQUEST).json({ error: err.message });
  }
};

module.exports = {
  getWhatsappStatus,
  getWhatsappMembersCount,
};
