const status = require('http-status');
const {
  communitySubscriptionsService,
} = require('../../../services/mobile');
const {
  getUserSubscriptionWithProjection,
  updateSubscription,
} = require('../../../services/common/communitySubscriptions.service');
const logger = require('../../../../services/logger.service');
const { aclRoles } = require('../../../constants');

const getCommunityMemberships = async (req, res, next) => {
  try {
    const { communityId = null } = req?.params;
    const { roles } = req.user;
    let {
      paginate = 0,
      pageNum = null,
      pageSize = null,
      sortBy = 'fullName',
      sortOrder = 1,
      filter = null,
      countryId = null,
    } = req?.query;
    paginate = parseInt(paginate, 10) ?? 0;
    if (paginate === 1) {
      pageNum = parseInt(pageNum, 10) ?? 1;
      pageSize = parseInt(pageSize, 10) ?? 10;
    }
    if (sortOrder) {
      sortOrder = parseInt(sortOrder, 10) ?? null;
    }

    const isManager = roles?.[aclRoles.ADMIN] === true;

    res.json(
      await communitySubscriptionsService.getCommunityMembers({
        communityId,
        paginate,
        pageNum,
        pageSize,
        sortBy,
        sortOrder,
        filterQuery: filter,
        countryId,
        isManager,
      })
    );
  } catch (err) {
    logger.error('Error on get community members request: ', err);
    const error = new Error('Error on get request');
    error.status = 400;
    return next(error);
  }
};

const toggleUserConfirmedJoiningChat = async (req, res, next) => {
  try {
    const { communityId = null } = req?.params;
    const { learner = null } = req?.user;
    const { learnerId = null } = learner;
    /*
    payload structure: 
    const {
      discord = false,
      facebook = false,
      linkedin = false,
      slack = false,
      telegram = false,
      whatsapp = false,
      ...otherPlatformDetails
    } = req?.body;
    */
    const { _id: subscriptionObjectId, userConfirmedJoiningChat = {} } =
      await getUserSubscriptionWithProjection({
        learnerId,
        communityObjectId: communityId,
        projectionParams: 'userConfirmedJoiningChat',
      });
    logger.info(
      'Existing userConfirmedJoiningChat: ',
      userConfirmedJoiningChat
    );
    let updatedUserConfirmedJoiningChat = {};
    for (const [key, value] of Object.entries(req?.body)) {
      // eslint-disable-next-line no-prototype-builtins
      if (!userConfirmedJoiningChat.hasOwnProperty(key)) {
        if (Object.keys(userConfirmedJoiningChat).length) {
          updatedUserConfirmedJoiningChat = {
            [key]: value,
            ...userConfirmedJoiningChat,
          };
        } else {
          updatedUserConfirmedJoiningChat = {
            [key]: value,
          };
        }
      } else {
        userConfirmedJoiningChat[key] = value;
        if (Object.keys(userConfirmedJoiningChat).length) {
          updatedUserConfirmedJoiningChat = {
            ...userConfirmedJoiningChat,
          };
        } else {
          updatedUserConfirmedJoiningChat = {
            [key]: value,
          };
        }
      }
    }
    let statusCode = status.BAD_REQUEST;
    let result = {
      error: { message: 'Error on toggling user confirmed joining chat' },
    };
    if (Object.keys(updatedUserConfirmedJoiningChat).length) {
      const updatedSubscription = await updateSubscription(
        subscriptionObjectId,
        { userConfirmedJoiningChat: updatedUserConfirmedJoiningChat }
      );
      logger.info('Updated subscription: ', updatedSubscription);
      if (updatedSubscription && Object.keys(updatedSubscription).length) {
        statusCode = status.OK;
        result = { data: { message: 'Updated successfully' } };
      }
    }
    return res.status(statusCode).send(result);
  } catch (err) {
    logger.error(
      'Error on toggling user confirmed joining chat: ',
      err,
      err.stack
    );
    const error = new Error(
      'Error on toggling user confirmed joining chat'
    );
    error.status = err.status || 500;
    return next(error);
  }
};

module.exports = {
  getCommunityMemberships,
  toggleUserConfirmedJoiningChat,
};
