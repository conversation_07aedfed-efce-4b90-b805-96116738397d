const { communityVideosService } = require('../../../services/mobile');
const logger = require('../../../../services/logger.service');
const utils = require('../../../utils');

const getCommunityClassPreview = async (req, res, next) => {
  try {
    // eslint-disable-next-line no-param-reassign
    res = utils.addSignedCookiesToResponse(res);
    const communityVideos =
      await communityVideosService.getCommunityClassVideoPreview(
        req?.params?.communityId
      );

    res.json(communityVideos);
  } catch (err) {
    logger.error(
      'Error on get community video preview request: ',
      err,
      err.stack
    );
    const error = new Error(
      'Error on get community video preview request'
    );
    error.status = err.status || 500;
    return next(error);
  }
};

const getCommunityVideos = async (req, res, next) => {
  try {
    // eslint-disable-next-line no-param-reassign
    res = utils.addSignedCookiesToResponse(res);
    const communityVideos =
      await communityVideosService.getCommunityVideos(
        req?.params?.communityId
      );

    res.json(communityVideos);
  } catch (err) {
    logger.error('Error on get community videos request: ', err);
    const error = new Error('Error on get community videos request');
    error.status = 400;
    return next(error);
  }
};

module.exports = {
  getCommunityClassPreview,
  getCommunityVideos,
};
