const { ObjectId } = require('mongoose').Types;
const httpStatus = require('http-status');
const {
  communityFoldersService,
  communityFolderItemsService,
} = require('../../../services/mobile');
const logger = require('../../../../services/logger.service');
const utils = require('../../../utils');
const {
  MILESTONE_ACTIVITY_TYPES,
  PURCHASE_TYPE,
} = require('../../../../constants/common');

const { getUserIP } = require('../../../../utils/headers.util');
const {
  communityFolderItemTypesMap,
  communityLibraryStatusMap,
  COMMUNITY_FOLDER_PURCHASE_TYPES,
  communityFolderTypesMap,
} = require('../../../constants');
const {
  createFolderSchema,
} = require('../../../apiSchemas/communityFolders.schema');
const communityFolderPurchases = require('../../../models/communityFolderPurchases.model');
const communityFolderViews = require('../../../models/communityFolderAccessLogs.model');
const {
  getAddonPriceInLocalCurrency,
} = require('../../../services/common/communityAddonPrice.service');
const CommunityModel = require('../../../models/community.model');
const ActionEventService = require('../../../../services/actionEvent');
const folderService = require('../../../../services/folder');
const {
  affiliateProductService,
} = require('../../../../services/affiliate');
const {
  purgeEntityLandingPageCache,
  ENTITY_LANDING_PAGE,
} = require('../../../../utils/memberPortalLinks.utils');

const getFolders = async (req, res, next) => {
  try {
    // eslint-disable-next-line no-param-reassign
    res = utils.addSignedCookiesToResponse(res);
    let isCommunityManager = false;
    if (req.user) {
      isCommunityManager =
        req.user.community_admin === true ||
        req.user.roles?.manager === true;
    }
    const filter = req?.query?.type ? { type: req.query.type } : {};
    let { paginate = 0, pageNum = null, pageSize = null } = req?.query;
    paginate = parseInt(paginate, 10) || 0;
    if (paginate === 1) {
      pageNum = parseInt(pageNum, 10) || 1;
      pageSize = parseInt(pageSize, 10) || 10;
    }
    const folders = await communityFoldersService.getFoldersByCommunityId(
      req?.params?.communityId,
      isCommunityManager,
      req?.user?.learner?._id,
      filter,
      paginate,
      pageNum,
      pageSize
    );

    res.json(folders);
  } catch (err) {
    logger.error('Error on get community folders request: ', err);
    err.status = httpStatus.INTERNAL_SERVER_ERROR;
    return next(err);
  }
};

const getProducts = async (req, res, next) => {
  try {
    // eslint-disable-next-line no-param-reassign
    res = utils.addSignedCookiesToResponse(res);
    let isCommunityManager = false;
    if (req.user) {
      isCommunityManager = req.user.isCommunityManager;
    }
    const filter = req?.query?.type ? { type: req.query.type } : {};
    let { paginate = 0, pageNum = null, pageSize = null } = req?.query;
    paginate = parseInt(paginate, 10) || 0;
    if (paginate === 1) {
      pageNum = parseInt(pageNum, 10) || 1;
      pageSize = parseInt(pageSize, 10) || 10;
    }
    const folders = await communityFoldersService.getFoldersByCommunityId(
      req?.params?.communityId,
      isCommunityManager,
      req?.user?.learner?._id,
      filter,
      paginate,
      pageNum,
      pageSize
    );

    res.json(folders);
  } catch (err) {
    logger.error('Error on get community folders request: ', err);
    err.status = httpStatus.INTERNAL_SERVER_ERROR;
    return next(err);
  }
};

const getFolderById = async (req, res, next) => {
  try {
    const ip = getUserIP(req) || null;
    const { communityId } = req.params;
    // eslint-disable-next-line no-param-reassign
    res = utils.addSignedCookiesToResponse(res);
    const { isCommunityManager } = req?.user;
    const folder = await communityFoldersService.getFolderById(
      req?.params?.communityFolderId,
      isCommunityManager,
      { communityObjectId: new ObjectId(communityId) }
    );

    const { selectedAmount, paymentMethodCountryCode, paymentProvider } =
      req.query;

    let { paginate = 0, pageNum = null, pageSize = null } = req?.query;

    paginate = parseInt(paginate, 10) || 0;
    if (paginate === 1) {
      pageNum = parseInt(pageNum, 10) || 1;
      pageSize = parseInt(pageSize, 10) || 10;
    }
    let result = {};
    if (folder) {
      result = folder;
      if (result?.access === COMMUNITY_FOLDER_PURCHASE_TYPES.PAID) {
        const priceDetails = await getAddonPriceInLocalCurrency({
          ip,
          addon: folder,
          communityObjectId: communityId,
          selectedAmount,
          paymentMethodCountryCode,
          paymentProvider,
        });
        logger.info('Price in local currency: ', priceDetails);
        result = { priceDetails, ...folder };
      }
      const isPurchased = await communityFolderPurchases.count({
        learnerObjectId: new ObjectId(req?.user?.learner?._id),
        folderObjectId: new ObjectId(folder._id),
      });
      result['isPurchased'] = Boolean(isPurchased);
      if (isCommunityManager) result['isPurchased'] = true;
      const folderViewCount = await communityFolderViews.count({
        communityFolderObjectId: new ObjectId(folder._id),
      });
      result['folderViewCount'] = folderViewCount;
      const priceFieldsToRemove = [
        'localiseForAllCountries',
        'countryWisePrice',
      ];
      priceFieldsToRemove.forEach(
        (priceFieldToRemove) => delete result[priceFieldToRemove]
      );
      const folderItems =
        await communityFolderItemsService.getFolderItemsByFolderId(
          folder?._id,
          isCommunityManager,
          {},
          paginate,
          pageNum,
          pageSize
        );
      const folderItemData =
        paginate === 1 ? folderItems?.data : folderItems;
      const videoCount = folderItemData.filter(
        (i) => i?.type === communityFolderItemTypesMap.VIDEO
      ).length;
      result.videoCount = videoCount;
      result.otherFolderItemCount = folderItemData?.length - videoCount;
      result.totalItemsCount = folderItemData?.length;
      result.folderItems = folderItems;
    } else {
      logger.error('No folder found ');
      throw new Error('Error No folder found');
    }
    res.json(result);
  } catch (err) {
    logger.error('Error on get community folder by id request: ', err);
    err.status = httpStatus.INTERNAL_SERVER_ERROR;
    return next(err);
  }
};

const getFilteredLibraryFromCommunity = async (req, res, next) => {
  try {
    const { communityId } = req.params;
    const { filter } = req.query;
    const { isCommunityManager } = req.user;
    const result = [];
    const folders =
      await communityFoldersService.getFilteredFoldersByCommunityId(
        communityId,
        isCommunityManager,
        filter
      );
    result.push(...folders);
    const folderItems =
      await communityFolderItemsService.getFilteredFolderItemsByCommunityId(
        communityId,
        isCommunityManager,
        filter
      );
    result.push(...folderItems);
    res.json(result);
  } catch (err) {
    logger.error('Error searching library from the community: ', err);
    err.status = httpStatus.INTERNAL_SERVER_ERROR;
    return next(err);
  }
};

const createOneFolder = async (req, res, next) => {
  const input = req.body;
  const { communityId } = req.params;
  input.communityObjectId = communityId;
  const isQueryValid = await createFolderSchema.isValid(input);
  if (!isQueryValid) {
    const error = new Error('Invalid Parameters');
    return next(error);
  }
  const body = createFolderSchema.cast(input);
  const { learner } = req.user;

  try {
    if (req.file) {
      body.thumbnail = req.file.location;
    }

    const folder = await communityFoldersService.createOneFolder({
      ...body,
      createdByLearnerObjectId: learner._id,
      communityObjectId: communityId,
    });

    res.json(folder);
  } catch (err) {
    logger.error('Error creating folder from the community: ', err);
    err.status = httpStatus.INTERNAL_SERVER_ERROR;
    return next(err);
  }
};

const updateOneFolder = async (req, res, next) => {
  const input = {
    ...req.body,
    communityObjectId: req.params.communityId,
  };
  const { isCommunityManager } = req.user;
  const { communityFolderId } = req.params;
  const body = {
    ...createFolderSchema.cast(input),
    ...(req.file && { thumbnail: req.file.location }),
  };

  try {
    const folder = await communityFoldersService.updateOneFolder(
      { _id: new ObjectId(communityFolderId) },
      body
    );
    const folderItems =
      await communityFolderItemsService.getFolderItemsByFolderId(
        communityFolderId,
        isCommunityManager
      );
    const videoCount = folderItems.filter(
      (i) => i.type === communityFolderItemTypesMap.VIDEO
    ).length;
    const totalItemsCount = folderItems?.length;
    const result = {
      ...folder,
      videoCount,
      totalItemsCount,
      otherFolderItemCount: folderItems.length - videoCount,
      folderItems,
    };

    res.json(result);
  } catch (err) {
    logger.error('Error updating folder from the community: ', err);
    err.status = httpStatus.INTERNAL_SERVER_ERROR;
    return next(err);
  }
};

const deleteOneFolder = async (req, res, next) => {
  const { communityId, communityFolderId } = req.params;

  try {
    const folderResult =
      await folderService.softDeleteFolderAndFolderItems(
        communityFolderId,
        communityId
      );

    res.json(folderResult);
  } catch (err) {
    logger.error('Error deleting folder from the community: ', err);
    return next(err);
  }
};
const saveAndPublishChanges = async (req, res, next) => {
  try {
    const { status } = req.body;
    const { communityId, communityFolderId } = req.params;
    const { isCommunityManager, learner } = req.user;
    const [folder, community, publishedFolderCount] = await Promise.all([
      communityFoldersService.getFolderById(communityFolderId, true, {
        communityObjectId: communityId,
      }),
      CommunityModel.findById(communityId, {
        code: 1,
        link: 1,
      }).lean(),
      communityFoldersService.getPublishedFolderCount(communityId),
    ]);
    const oldStatus = folder.status;

    let result = {};
    if (!folder || !status) {
      throw new Error(
        `Error in saving and publishing folder. Folder with id ${communityFolderId} does not exists in community ${communityId}`
      );
    }
    if (status === communityLibraryStatusMap.PUBLISHED) {
      result = await communityFoldersService.patchOneFolder(
        communityFolderId,
        { isDraft: false, status },
        true
      );

      result.discountsApplied = folder.discountsApplied;

      const {
        matchedCount: folderItems,
        modifiedCount: folderItemsModified,
      } = await communityFolderItemsService.patchManyFolderItemsByFolderId(
        communityFolderId,
        { status },
        { status: communityLibraryStatusMap.UNPUBLISHED }
      );
      result = { ...result, folderItems, folderItemsModified };
      // enrol manager to folder if paid
      if (
        isCommunityManager &&
        result?.access === COMMUNITY_FOLDER_PURCHASE_TYPES?.PAID
      ) {
        await communityFolderPurchases.findOneAndUpdate(
          {
            learnerObjectId: new ObjectId(req?.user?.learner?._id),
            folderObjectId: new ObjectId(folder._id),
          },
          {
            learnerObjectId: new ObjectId(req?.user?.learner?._id),
            folderObjectId: new ObjectId(folder._id),
            purchaseType: COMMUNITY_FOLDER_PURCHASE_TYPES.FREE,
          },
          {
            new: true,
          }
        );
      }

      await ActionEventService.sendMilestoneEvent({
        actionEventType: MILESTONE_ACTIVITY_TYPES.MILESTONE_FIRST_CONTENT,
        communityCode: community.code,
        communityObjectId: communityId,
        learnerObjectId: learner._id,
      });
    }
    if (status === communityLibraryStatusMap.UNPUBLISHED) {
      result = await communityFoldersService.patchOneFolder(
        communityFolderId,
        { isDraft: true, status },
        true
      );

      result.discountsApplied = folder.discountsApplied;

      const {
        matchedCount: folderItems,
        modifiedCount: folderItemsModified,
      } = await communityFolderItemsService.patchManyFolderItemsByFolderId(
        communityFolderId,
        { status },
        { status: communityLibraryStatusMap.PUBLISHED }
      );

      const entityType =
        result.type === communityFolderTypesMap.SESSION
          ? PURCHASE_TYPE.SESSION
          : PURCHASE_TYPE.FOLDER;

      await affiliateProductService.disableAffiliateProduct({
        communityObjectId: communityId,
        entityType,
        entityObjectId: communityFolderId,
      });

      result = { ...result, folderItems, folderItemsModified };
    }

    // purge landing page cache
    const isStatusUpdated = oldStatus !== result.status;
    if (isStatusUpdated) {
      const isPublishAction =
        result.status === communityLibraryStatusMap.PUBLISHED;

      const purgeCommunityLandingPage = isPublishAction
        ? publishedFolderCount === 0 // i.e first folder to be published in the community ( for community landing page tabs )
        : publishedFolderCount === 1; // i.e unpublishing the only published folder ( for community landing page tabs )

      await purgeEntityLandingPageCache({
        community,
        purgeCommunityLandingPage,
        entityType: ENTITY_LANDING_PAGE[folder.type],
        entitySlug: folder.resourceSlug,
      });
    }

    result = {
      ...result,
      ...(await communityFolderItemsService.getFolderItemsByIdWithMetaData(
        communityFolderId,
        true
      )),
    };

    res.json(result);
  } catch (err) {
    logger.error(
      'Error in save and publishing folder from the community: ',
      err
    );
    err.status = httpStatus.INTERNAL_SERVER_ERROR;
    return next(err);
  }
};

module.exports = {
  getFolders,
  getProducts,
  getFolderById,
  getFilteredLibraryFromCommunity,
  createOneFolder,
  updateOneFolder,
  deleteOneFolder,
  saveAndPublishChanges,
};
