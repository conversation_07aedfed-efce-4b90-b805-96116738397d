const { communitiesService } = require('../../../services/mobile');
const {
  IMAGEASSETBUCKET_CLOUDFRONT_BASE_URL,
} = require('../../../../constants/common');
const {
  getCommunityDataBySlug,
} = require('../../../services/common/community.service');
const logger = require('../../../../services/logger.service');
const ObjectId = require('mongoose').Types.ObjectId;
const utils = require('../../../utils');

const getCommunities = async (req, res, next) => {
  const learnerId = req.user.learner.learnerId;
  const learnerObjectId = new ObjectId(req.user.learner._id);
  const userObjectId = new ObjectId(req.user._id);

  try {
    const communities = await communitiesService.getLearnerCommunities(
      learnerId,
      learnerObjectId,
      userObjectId,
      req.query
    );

    res.setHeader('Access-Control-Expose-Headers', '*');
    res.setHeader('Access-Control-Allow-Credentials', 'true');
    // eslint-disable-next-line no-param-reassign
    res = utils.addSignedCookiesToResponse(res, true);

    res.json(communities);
  } catch (err) {
    logger.error('Error occured when getting communities', err, err.stack);
    const error = new Error('Error getting communities');
    error.status = 400;
    return next(error);
  }
};

const getCommunityById = async (req, res, next) => {
  try {
    const { communityId = null } = req?.params;
    if (!communityId) {
      const errorMessage =
        'Unable to get community details due to missing parameter';
      logger.error(errorMessage);
      const error = new Error(errorMessage);
      return next(error);
    }
    const community = await communitiesService.getCommunityById(
      communityId
    );
    res.status(200).json(community);
  } catch (error) {
    logger.error('Error occured when getting community by id:', error);
    error.status = 400;
    return next(error);
  }
};

const getCommunityBotDetails = async (req, res, next) => {
  const { communityId } = req.params;
  try {
    const communities = await communitiesService.getCommunityBotDetails(
      communityId
    );
    res.json(communities);
  } catch (err) {
    logger.info('Error occured when getting communities bots:', err);
    const error = new Error('Error getting communities bots');
    error.status = 400;
    return next(error);
  }
};

const uploadThumbnailImage = async (req, res, next) => {
  const { communityId } = req.params;
  const { width, height } = req.body;
  const file = req.file;
  if (!file) {
    logger.info('No file uploaded to s3 for community thumbnail');
    const error = new Error(
      'No file uploaded to s3 for community thumbnail'
    );
    error.status = 400;
    return next(error);
  }

  try {
    const pathToReplace = `${file.location?.split(file?.bucket)[0]}${
      file?.bucket
    }`;
    const src = file.location?.replace(
      pathToReplace,
      `${IMAGEASSETBUCKET_CLOUDFRONT_BASE_URL}`
    );

    const community = await communitiesService.getCommunitiesByIds([
      communityId,
    ]);

    if (community.length !== 1) {
      logger.info('Cannot find community with id ', communityId);
      const error = new Error('Cannot find community');
      error.status = 400;
      return next(error);
    }
    const thumbnailImgData = community?.[0]?.thumbnailImgData ?? {};
    thumbnailImgData.mobileImgData = {
      src,
      meta: { width, height },
    };
    thumbnailImgData.desktopImgData = {
      src,
      meta: { width, height },
    };
    const props = { src, width, height };
    const communityCheckoutCardData =
      community?.[0]?.communityCheckoutCardData ?? {};
    let imgData = community?.[0]?.communityCheckoutCardData?.imgData ?? {};
    let mobileImgProps =
      community?.[0]?.communityCheckoutCardData?.imgData?.mobileImgProps ??
      {};
    let desktopImgProps =
      community?.[0]?.communityCheckoutCardData?.imgData
        ?.desktopImgProps ?? {};
    mobileImgProps = { ...mobileImgProps, ...props };
    desktopImgProps = { ...desktopImgProps, ...props };
    imgData = { ...imgData, mobileImgProps, desktopImgProps };
    communityCheckoutCardData.imgData = imgData;

    const updatedData = await communitiesService.updateOneCommunity(
      { _id: new ObjectId(communityId) },
      { thumbnailImgData, communityCheckoutCardData }
    );
    res.json(updatedData);
  } catch (err) {
    logger.info('Error occured when updating community thumbnail:', err);
    const error = new Error('Error updating community thumbnail');
    error.status = 400;
    return next(error);
  }
};

const uploadBannerImage = async (req, res, next) => {
  const { communityId } = req.params;
  const { width, height } = req.body;
  const file = req.file;
  if (!file) {
    logger.info('No file uploaded to s3 for community banner');
    const error = new Error('No file uploaded to s3 for community banner');
    error.status = 400;
    return next(error);
  }

  try {
    const pathToReplace = `${file.location?.split(file?.bucket)[0]}${
      file?.bucket
    }`;
    const src = file.location?.replace(
      pathToReplace,
      `${IMAGEASSETBUCKET_CLOUDFRONT_BASE_URL}`
    );

    const community = await communitiesService.getCommunitiesByIds([
      communityId,
    ]);

    if (community.length !== 1) {
      logger.info('Cannot find community with id ', communityId);
      const error = new Error('Cannot find community');
      error.status = 400;
      return next(error);
    }
    const fullScreenBannerImgData =
      community?.[0]?.fullScreenBannerImgData ?? {};
    const mobileImgProps =
      community?.[0]?.fullScreenBannerImgData?.mobileImgProps ?? {};
    const desktopImgProps =
      community?.[0]?.fullScreenBannerImgData?.desktopImgProps ?? {};
    mobileImgProps.src = src;
    desktopImgProps.src = src;
    fullScreenBannerImgData.mobileImgProps = mobileImgProps;
    fullScreenBannerImgData.desktopImgProps = desktopImgProps;
    const updatedData = await communitiesService.updateOneCommunity(
      { _id: new ObjectId(communityId) },
      { fullScreenBannerImgData }
    );
    res.json(updatedData);
  } catch (err) {
    logger.info('Error occured when updating community banner:', err);
    const error = new Error('Error updating community banner');
    error.status = 400;
    return next(error);
  }
};

module.exports = {
  getCommunities,
  getCommunityById,
  getCommunityBotDetails,
  uploadThumbnailImage,
  uploadBannerImage,
};
