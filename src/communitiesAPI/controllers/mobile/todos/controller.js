const { communityTodosService } = require('../../../services/mobile');
const logger = require('../../../../services/logger.service');
const status = require('http-status');
const yup = require('yup');
const ObjectId = require('mongoose').Types.ObjectId;

const updateProgressBodySchema = yup.object().shape({
  completed: yup.boolean().required(),
});

const updateProgressParamSchema = yup.object().shape({
  todoId: yup.mixed(ObjectId).required(),
});

const getOrientationParamSchema = yup.object().shape({
  communityId: yup.mixed(ObjectId).required(),
});

const getOrientationTodos = async (req, res, next) => {
  try {
    const learnerId = req.user?.learner?.learnerId;
    const isParamsValid = await getOrientationParamSchema.isValid(
      req.params
    );

    if (!isParamsValid) {
      logger.info('Invalid params.');
      const err = new Error('Invalid parameters provided');
      err.status = status.BAD_REQUEST;
      return next(err);
    }

    const params = updateProgressParamSchema.cast(req.params);
    const { communityId } = params;
    const response = await communityTodosService.getOrientation(
      communityId,
      learnerId
    );

    const compeletedTodoIndex = response.findIndex(
      (item) => item.completed === true
    );

    // All todos are not completed
    if (compeletedTodoIndex === -1) {
      const scheduleResponse = await communityTodosService.scheduleTodoMobileNotification(
        learnerId,
        communityId
      );
    }

    res.json(response);
  } catch (err) {
    logger.info('Error on get community orientaton request: ', err);
    const error = new Error('Error on get request');
    error.status = status.BAD_REQUEST;
    return next(error);
  }
};

const updateTodoProgress = async (req, res, next) => {
  try {
    const learnerId = req.user?.learner?.learnerId;

    const isBodyValid = await updateProgressBodySchema.isValid(req.body);
    const isParamsValid = await updateProgressParamSchema.isValid(
      req.params
    );

    if (!isParamsValid) {
      logger.info('Invalid params.');
      const err = new Error('Invalid parameters provided');
      err.status = status.BAD_REQUEST;
      return next(err);
    }

    if (!isBodyValid) {
      const error = new Error('Invalid Body Params');
      error.status = status.BAD_REQUEST;
      return next(error);
    }
    const body = updateProgressBodySchema.cast(req.body);
    const params = updateProgressParamSchema.cast(req.params);

    const { todoId } = params;
    res.json(
      await communityTodosService.updateOrCreateProgress(
        todoId,
        learnerId,
        body
      )
    );
  } catch (err) {
    logger.info('Error on updating user todo progress: ', err);
    const error = new Error('Error on get request');
    error.status = status.BAD_REQUEST;
    return next(error);
  }
};

module.exports = {
  getOrientationTodos,
  updateTodoProgress,
};
