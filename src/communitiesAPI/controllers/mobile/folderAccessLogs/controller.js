const {
  communityFolderAccessLogsService,
  communityFoldersService,
  communityService,
  communitySubscriptionsService,
} = require('../../../services/web');
const logger = require('../../../../services/logger.service');
const status = require('http-status');
const ObjectId = require('mongoose').Types.ObjectId;

const createFolderAccessLogs = async (req, res, next) => {
  try {
    const userData = req.user;
    const folderId = req?.params?.communityFolderId;
    if (!userData) {
      throw new Error('Missing userData');
    }

    const folder = await communityFoldersService.getFolderById(folderId);
    if (!folder) {
      throw new Error(`Folder with id ${folderId} does not exists`);
    }

    const codes = await communityService.getCommunityCodesByIds([
      new ObjectId(folder.communityObjectId),
    ]);

    if (codes.length < 1) {
      throw new Error(`No code found for ${folder.communityObjectId}`);
    }

    const subscription =
      await communitySubscriptionsService.getCurrentSubscription({
        learnerObjectId: new ObjectId(userData.learner._id),
        activeCommunityCode: codes[0],
      });

    if (!folder) {
      throw new Error(`Folder with id ${folderId} does not exists`);
    }
    const body = {
      communityFolderObjectId: new ObjectId(folderId),
      learnerObjectId: new ObjectId(userData.learner._id),
      subscriptionObjectId: new ObjectId(subscription?._id),
    };

    const folderAccessLog =
      await communityFolderAccessLogsService.createOneFolderAccessLog(
        body
      );

    res.status(status.CREATED).json(folderAccessLog);
  } catch (err) {
    logger.error('Error creating community folder access log: ', err);
    err.status = status.BAD_REQUEST;
    next(err);
  }
};

module.exports = {
  createFolderAccessLogs,
};
