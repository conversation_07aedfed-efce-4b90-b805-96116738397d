const {
  communityLeaveService,
} = require('../../../services/common/communityLeave.service');
const ObjectId = require('mongoose').Types.ObjectId;

const CommunitySubscriptionsModel = require('../../../models/communitySubscriptions.model');

const leaveCommunityController = async (req, res) => {
  try {
    // check if the payload is in this form { cancellationReason: 'string', communityCode: 'string'}
    const { cancellationReason, communityCode, subscriptionObjectId } =
      req.body;
    const user = req?.user;
    let { communityId } = req?.params;

    // make sure that there is a cancellation reason and a community Code in the payload
    if (!cancellationReason || !communityCode) {
      return res.status(400).json({
        message: 'cancellationReason and communityCode are required',
      });
    }
    // find the community subscription in the database
    const communitySubscription =
      await CommunitySubscriptionsModel.findOne({
        communityCode: communityCode,
        _id: new ObjectId(subscriptionObjectId),
      })
        .populate('learnerObjectId')
        .lean();
    let userInfo = {
      fullName: communitySubscription?.learnerObjectId?.firstName ?? '',
    };
    // if there is no community subscription then error out
    if (!communitySubscription) {
      return res.status(400).json({
        message: 'No subscription found',
      });
    }
    // check if the community subscription is not a paid subscription
    // if paid subscription then error out
    // if not paid subscription then proceed to leave the community
    // (since the unsubscription is handled by main website backend not this repo)
    if (communitySubscription.stripeSubscriptionId) {
      return res.status(400).json({
        message: 'this service cannot unsubscribe a paid community',
        error: true,
      });
    }
    // call the service to leave the community
    const { data: communityLeaveResponse, error } =
      await communityLeaveService(
        communitySubscription,
        cancellationReason,
        communityCode,
        userInfo,
        communityId
      );
    if (error) {
      throw new Error(error);
    }
    // if there is a response then return the response
    if (communityLeaveResponse) {
      return res.status(200).json({
        message: 'Community left successfully',
        data: communityLeaveResponse,
      });
    }
  } catch (error) {
    return res.status(500).json({
      message: 'Internal server error',
      error: error.message,
    });
  }
};

module.exports = {
  leaveCommunityController,
};
