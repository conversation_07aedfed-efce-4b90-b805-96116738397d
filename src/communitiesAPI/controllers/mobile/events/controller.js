const ObjectId = require('mongoose').Types.ObjectId;
const { communityEventsService } = require('../../../services/mobile');
const logger = require('../../../../services/logger.service');
const {
  getUserIP,
  getUserAgent,
} = require('../../../../utils/headers.util');
const {
  COMMUNITY_EVENT_ATTENDEE_PURCHASE_TYPES,
} = require('../../../constants');

const getCommunityEvents = async (req, res, next) => {
  try {
    const learnerObjectId = req?.user?.learner?._id;
    const userObjectId = req?.user?._id;
    const communityId = req?.params?.communityId;
    let { paginate = 0, pageNum = null, pageSize = null } = req?.query;
    paginate = parseInt(paginate, 10) || 0;
    if (paginate === 1) {
      pageNum = parseInt(pageNum, 10) || 1;
      pageSize = parseInt(pageSize, 10) || 10;
    }
    const upcomingCommunityEvents =
      await communityEventsService.getUpcomingEventsByCommunityId(
        learnerObjectId,
        userObjectId,
        communityId,
        paginate,
        pageNum,
        pageSize
      );

    const pastCommunityEvents =
      await communityEventsService.getPastEventsByCommunityId(
        learnerObjectId,
        userObjectId,
        communityId,
        paginate,
        pageNum,
        pageSize
      );

    let allAttendingCommunityEvents = null;
    let upcoming = null;
    let past = null;
    let attending = null;
    if (paginate === 1) {
      const allUpcomingCommunityEvents =
        await communityEventsService.getUpcomingEventsByCommunityId(
          learnerObjectId,
          userObjectId,
          communityId
        );

      allAttendingCommunityEvents =
        communityEventsService.getAttendingCommunityEvents(
          allUpcomingCommunityEvents
        );

      upcoming = {
        metadata: upcomingCommunityEvents?.metadata,
        data: communityEventsService.getTitledCommunityEvents(
          upcomingCommunityEvents?.data
        ),
      };

      past = {
        metadata: pastCommunityEvents?.metadata,
        data: communityEventsService.getTitledCommunityEvents(
          pastCommunityEvents?.data
        ),
      };

      attending = communityEventsService.getTitledCommunityEvents(
        allAttendingCommunityEvents,
        { today: true, tomorrow: true }
      );
    } else {
      allAttendingCommunityEvents =
        communityEventsService.getAttendingCommunityEvents(
          upcomingCommunityEvents
        );
      upcoming = communityEventsService.getTitledCommunityEvents(
        upcomingCommunityEvents
      );
      past = communityEventsService.getTitledCommunityEvents(
        pastCommunityEvents
      );
      attending = communityEventsService.getTitledCommunityEvents(
        allAttendingCommunityEvents,
        { today: true, tomorrow: true }
      );
    }

    res.json({ upcoming, past, attending });
  } catch (err) {
    logger.info('Error on get All Events request: ', err);
    const error = new Error('Error on get request');
    error.status = 400;
    return next(error);
  }
};

const getEventById = async (req, res, next) => {
  try {
    const { communityId = null, eventId = null } = req?.params;
    const {
      quantity = 1,
      selectedAmount,
      affiliateCode,
      paymentMethodCountryCode,
      paymentProvider,
    } = req.query;
    const learnerObjectId = req?.user?.learner?._id;
    const ip = getUserIP(req) || null;
    const event = await communityEventsService.getEventById(
      communityId,
      eventId,
      learnerObjectId,
      ip,
      quantity,
      selectedAmount,
      affiliateCode,
      paymentMethodCountryCode,
      paymentProvider
    );
    if (!event) {
      const errorMessage = `Unable to get event by id ${eventId} for community id ${communityId}`;
      logger.error(errorMessage);
      const error = new Error(errorMessage);
      error.status = 400;
      return next(error);
    }
    return res.status(200).json(event);
  } catch (err) {
    logger.error('Error on get Event by id request: ', err);
    const error = new Error('Error on get event by id request');
    error.status = 400;
    return next(error);
  }
};

const getEventByIdForAdmin = async (req, res, next) => {
  try {
    const { communityId = null, eventId = null } = req?.params;
    if (!communityId || !eventId) {
      const errorMessage = `Unable to get event by id ${eventId} for community id ${communityId}`;
      logger.error(errorMessage);
      const error = new Error(errorMessage);
      error.status = 400;
      throw error;
    }
    const { data: event, error: dataError } =
      await communityEventsService.getEventByIdForAdmin(
        communityId,
        eventId
      );
    if (dataError) {
      const errorMessage = `Unable to get event by id ${eventId} for community id ${communityId}`;
      logger.error(errorMessage);
      const error = new Error(errorMessage);
      error.status = 400;
      res.status(400).json({ error: dataError });
    }
    return res.status(200).json(event);
  } catch (err) {
    logger.error('Error on get Event by id request: ', err);
    const error = new Error('Error on get event by id request');
    error.status = 400;
    res.status(400).json({ error: err.message });
  }
};
const registerForEvent = async (req, res, next) => {
  try {
    const ip = getUserIP(req) || null;
    const userAgent = getUserAgent(req) || null;

    const learnerObjectId = new ObjectId(req?.user?.learner?._id);
    const email = req.user.email;
    const { quantity = 1 } = req.body;

    res.json(
      await communityEventsService.registerForEvent(
        req.params.eventId,
        learnerObjectId,
        COMMUNITY_EVENT_ATTENDEE_PURCHASE_TYPES.FREE,
        email,
        quantity,
        ip,
        userAgent
      )
    );
  } catch (err) {
    logger.info('Error in register for event request: ', err);
    const error = new Error('Error on post request');
    error.status = 400;
    return next(error);
  }
};

module.exports = {
  getCommunityEvents,
  getEventById,
  registerForEvent,
  getEventByIdForAdmin,
};
