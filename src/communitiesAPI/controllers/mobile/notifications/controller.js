const { notificationsService } = require('../../../services/mobile');
const logger = require('../../../../services/logger.service');
const ObjectId = require('mongoose').Types.ObjectId;
const status = require('http-status');
const {
  updateUser,
} = require('../../../../services/user/updateUser.service');

const getUserMobileNotificationsController = async (req, res, next) => {
  try {
    let { paginate = 0, pageNum = null, pageSize = null } = req?.query;
    paginate = parseInt(paginate, 10) || 0;
    if (paginate === 1) {
      pageNum = parseInt(pageNum, 10) || 1;
      pageSize = parseInt(pageSize, 10) || 10;
    }
    // eslint-disable-next-line no-param-reassign
    const userNotifications =
      await notificationsService.getUserMobileNotifications(
        req?.user?.user_id,
        paginate,
        pageNum,
        pageSize,
        req?.query?.communityCode,
        req?.query?.communityId
      );

    const userNotificationData =
      paginate === 1 ? userNotifications?.data : userNotifications;

    const response = notificationsService.formatNotifications(
      userNotificationData
    );
    let result = response;
    if (paginate === 1) {
      result = {
        metadata: userNotifications?.metadata,
        data: response,
      };
    }
    return res.json(result);
  } catch (err) {
    logger.error(
      'Error on get user mobile notifications controller',
      err,
      err.stack
    );
    const error = new Error(
      'Error on get user mobile notifications controller'
    );
    error.status = err.status || status.INTERNAL_SERVER_ERROR;
    return next(error);
  }
};

const notificationsSeen = async (req, res, next) => {
  try {
    const userId = req?.user?.user_id;
    const notificationIds = req?.body?.notificationIds;
    const notificationObjectIds = notificationIds.map((item) => {
      return new ObjectId(item);
    });
    const response = await notificationsService.setNotificationsSeen(
      userId,
      notificationObjectIds
    );
    res.json(response);
  } catch (err) {
    logger.error(
      'Error on get setting notifications of user as seen: ',
      err,
      err.stack
    );
    const error = new Error(
      'Error on get setting notifications of user as seen: '
    );
    error.status = err.status || status.INTERNAL_SERVER_ERROR;
    return next(error);
  }
};

const userNotificationPermissions = async (req, res, next) => {
  try {
    const userObjectId = req?.user?._id;
    const body = req?.body;
    const response = await updateUser(userObjectId, {
      mobileNotificationPermissions: body,
    });
    res.json(response);
  } catch (err) {
    logger.error('Error when updating user permissions');
    const error = new Error('Error when updating user permissions');
    error.status = err.status || status.INTERNAL_SERVER_ERROR;
    return next(error);
  }
};

module.exports = {
  getUserMobileNotificationsController,
  notificationsSeen,
  userNotificationPermissions,
};
