const { communityPostsService } = require('../../../services/mobile');
const logger = require('../../../../services/logger.service');

// Announcements - community manager portal
const getCommunityPosts = async (req, res, next) => {
  try {
    const { learner = null } = req?.user;
    const { _id: learnerObjectId } = learner;
    const { communityId = null } = req?.params;
    let { paginate = 0, pageNum = null, pageSize = null } = req?.query;
    paginate = parseInt(paginate, 10) || 0;
    if (paginate === 1) {
      pageNum = parseInt(pageNum, 10) || 1;
      pageSize = parseInt(pageSize, 10) || 10;
    }
    res.json(
      await communityPostsService.getCommunityPostsHtml(
        communityId,
        learnerObjectId,
        paginate,
        pageNum,
        pageSize
      )
    );
  } catch (err) {
    logger.info('Error on get author announcements request: ', err);
    const error = new Error('Error on get request');
    error.status = 400;
    return next(error);
  }
};

const getPinnedCommunityPosts = async (req, res, next) => {
  try {
    const { learner = null } = req?.user;
    const { _id: learnerObjectId } = learner;
    const { communityId = null } = req?.params;
    let { paginate = 0, pageNum = null, pageSize = null } = req?.query;
    paginate = parseInt(paginate, 10) || 0;
    if (paginate === 1) {
      pageNum = parseInt(pageNum, 10) || 1;
      pageSize = parseInt(pageSize, 10) || 10;
    }
    res.json(
      await communityPostsService.getPinnedCommunityPostsHtml(
        communityId,
        learnerObjectId,
        paginate,
        pageNum,
        pageSize
      )
    );
  } catch (err) {
    logger.error('Error on get author announcements request: ', err);
    const error = new Error('Error on get request');
    error.status = 400;
    return next(error);
  }
};

const getCommunityPost = async (req, res, next) => {
  try {
    const { postId } = req.params;
    res.json(await communityPostsService.getCommunityPostHtml(postId));
  } catch (err) {
    logger.info('Error on get announcement request: ', err);
    const error = new Error('Error on get request');
    error.status = 400;
    return next(error);
  }
};

module.exports = {
  getCommunityPosts,
  getCommunityPost,
  getPinnedCommunityPosts,
};
