const logger = require('../../../../services/logger.service');
const {
  communityBookTrainerService,
} = require('../../../services/mobile');

const calendlyWebhookController = async (req, res, next) => {
  try {
    const payload = req?.body;
    res.json(
      await communityBookTrainerService.calendlyWebhookListener(payload)
    );
  } catch (err) {
    logger.error('Error in calendly webhook controller: ', err);
    const error = new Error('Error in calendly webhook controller');
    error.status = 400;
    return next(error);
  }
};

const getBookingEventForLearner = async (req, res, next) => {
  try {
    const communityId = req?.params?.communityId;
    const learnerId = req?.user?.learner?._id;
    res.json(
      await communityBookTrainerService.getLatestBookingForLearner(
        communityId,
        learnerId
      )
    );
  } catch (err) {
    logger.info('Error in getting learner booking: ', err);
    const error = new Error('Error in getting learner booking');
    error.status = 400;
    return next(error);
  }
};

module.exports = {
  calendlyWebhookController,
  getBookingEventForLearner,
};
