const status = require('http-status');
const {
  COMMENT_TYPES,
  COMMENT_CATEGORIES,
  COMMENT_ENTITY_COLLECTIONS,
} = require('../../../../constants/common');
const { aclRoles } = require('../../../constants');
const logger = require('../../../../services/logger.service');
const commentService = require('../../../../services/comment.service');
const { getUserAgent } = require('../../../../utils/headers.util');
const {
  isDemoCommunity,
} = require('../../../services/common/community.service');

const addComment = async (req, res, next) => {
  let statusCode;
  let result;
  try {
    const { _id: userObjectId, learner = null, roles = null } = req?.user;
    const { _id: learnerObjectId } = learner;
    const { communityId = null, announcementId = null } = req?.params;
    const isDemo = (await isDemoCommunity(communityId)) || false;
    const {
      type = COMMENT_TYPES.COMMENT,
      category = COMMENT_CATEGORIES.TEXT,
      content,
      rootCommentObjectId = null,
      parentCommentObjectId = null,
    } = req?.body;
    let userRole = aclRoles.MEMBER;
    if (roles?.manager) {
      userRole = aclRoles.MANAGER;
    }
    const userAgent = getUserAgent(req) || null;

    const output = await commentService.addComment({
      entityCollection: COMMENT_ENTITY_COLLECTIONS.COMMUNITY_POST,
      entityObjectId: announcementId,
      userObjectId,
      learnerObjectId,
      userRole,
      type,
      category,
      content,
      rootCommentObjectId,
      parentCommentObjectId,
      userAgent,
      isDemo,
    });
    statusCode = status.CREATED;
    result = { data: output };
  } catch (error) {
    const errorMessage = 'Unable to add announcement comment';
    logger.error(errorMessage, ' due to: ', error);
    statusCode = status.BAD_REQUEST;
    result = { error: { code: statusCode, message: errorMessage } };
  }
  return res.status(statusCode).json(result);
};

const removeComment = async (req, res, next) => {
  let statusCode;
  let result;
  try {
    const { _id: userObjectId, learner = null } = req?.user;
    const { _id: learnerObjectId } = learner;
    const { announcementId = null, commentId = null } = req?.params;
    const { type = COMMENT_TYPES.COMMENT } = req?.query;
    const output = await commentService.removeComment({
      entityCollection: COMMENT_ENTITY_COLLECTIONS.COMMUNITY_POST,
      entityObjectId: announcementId,
      userObjectId,
      learnerObjectId,
      type,
      commentObjectId: commentId,
      isDeleted: true,
    });
    statusCode = status.NO_CONTENT;
    result = { data: output };
  } catch (error) {
    const errorMessage = 'Unable to remove announcement comment';
    logger.error(errorMessage, ' due to: ', error);
    statusCode = status.BAD_REQUEST;
    result = { error: { code: statusCode, message: errorMessage } };
  }
  return res.status(statusCode).json(result);
};

const commentsForAnnouncement = async (req, res, next) => {
  let statusCode;
  let result;
  try {
    const { _id: userObjectId, learner = null } = req?.user;
    const { _id: learnerObjectId } = learner;
    const { announcementId = null } = req?.params;
    const { type = COMMENT_TYPES.COMMENT } = req?.query;
    let {
      paginate = 0,
      pageNum = null,
      pageSize = null,
      sortBy = 'createdAt',
      sortOrder = -1,
    } = req?.query;
    paginate = parseInt(paginate, 10) || 0;
    if (paginate === 1) {
      pageNum = parseInt(pageNum, 10) || 1;
      pageSize = parseInt(pageSize, 10) || 10;
    }
    if (sortBy && sortOrder) {
      sortOrder = parseInt(sortOrder, 10) || -1;
    }
    const comments = await commentService.getAllEntityComments({
      entityCollection: COMMENT_ENTITY_COLLECTIONS.COMMUNITY_POST,
      entityObjectId: announcementId,
      learnerObjectId,
      type: COMMENT_TYPES.COMMENT,
      isDeleted: false,
      paginate,
      pageNum,
      pageSize,
      sortBy,
      sortOrder,
    });
    if (!comments?.length) {
      const errorMessage = 'Unable to fetch comments for announcement!';
      logger.error(errorMessage);
      statusCode = status.BAD_REQUEST;
      result = { error: { code: statusCode, message: errorMessage } };
    }
    statusCode = status.OK;
    result = { data: { comments } };
  } catch (error) {
    const errorMessage = 'Unable to get comments for announcement';
    logger.error(errorMessage, ' due to: ', error);
    statusCode = status.BAD_REQUEST;
    result = { error: { code: statusCode, message: errorMessage } };
  }
  return res.status(statusCode).json(result);
};

const repliesForAnnouncementComment = async (req, res, next) => {
  let statusCode;
  let result;
  try {
    const { _id: userObjectId, learner = null } = req?.user;
    const { _id: learnerObjectId } = learner;
    const { announcementId = null, commentId = null } = req?.params;
    const { type = COMMENT_TYPES.REPLY } = req?.query;
    let {
      paginate = 0,
      pageNum = null,
      pageSize = null,
      sortBy = 'createdAt',
      sortOrder = 1,
    } = req?.query;
    paginate = parseInt(paginate, 10) || 0;
    if (paginate === 1) {
      pageNum = parseInt(pageNum, 10) || 1;
      pageSize = parseInt(pageSize, 10) || 10;
    }
    if (sortBy && sortOrder) {
      sortOrder = parseInt(sortOrder, 10) || -1;
    }
    const comments = await commentService.getAllEntityCommentReplies({
      entityCollection: COMMENT_ENTITY_COLLECTIONS.COMMUNITY_POST,
      entityObjectId: announcementId,
      learnerObjectId,
      rootCommentObjectId: commentId,
      type,
      isDeleted: false,
      paginate,
      pageNum,
      pageSize,
      sortBy,
      sortOrder,
    });
    if (!comments?.length) {
      const errorMessage =
        'Unable to fetch replies for announcement comment!';
      logger.error(errorMessage);
      statusCode = status.BAD_REQUEST;
      result = { error: { code: statusCode, message: errorMessage } };
    }
    statusCode = status.OK;
    result = { data: { comments } };
  } catch (error) {
    const errorMessage = 'Unable to get replies for announcement comment';
    logger.error(errorMessage, ' due to: ', error);
    statusCode = status.BAD_REQUEST;
    result = { error: { code: statusCode, message: errorMessage } };
  }
  return res.status(statusCode).json(result);
};

module.exports = {
  addComment,
  removeComment,
  commentsForAnnouncement,
  repliesForAnnouncementComment,
};
