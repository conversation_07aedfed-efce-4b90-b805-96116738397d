const { Schema, model } = require('mongoose');

const CommunityLandingPageTemplateSchema = new Schema(
  {
    slug: { type: String, required: true },
    communityCode: { type: String, required: true },
    sectionOrder: { type: Array, required: true },
    customSectionData: { type: Object, required: false },
    overriddenData: { type: Object, required: false },
    isDemo: { type: Boolean, required: false, default: false },
  },
  {
    collection: 'community_landing_page_template',
    timestamps: true,
  }
);

const CommunityLandingPageTemplateModel = model(
  'CommunityLandingPageTemplate',
  CommunityLandingPageTemplateSchema
);

module.exports = CommunityLandingPageTemplateModel;
