const { Schema, model } = require('mongoose');
const usersModel = require('../../models/users.model');
const CommunityModel = require('./community.model');

const configSchema = new Schema(
  {
    hideEarning: { type: Boolean, required: false },
    hideTransaction: { type: Boolean, required: false },
    hidePayout: { type: Boolean, required: false },
  },
  { _id: false }
);

const CommunityRoleSchema = new Schema(
  {
    userObjectId: {
      type: Schema.Types.ObjectId,
      required: true,
      // add connection to user model
      ref: usersModel,
    },
    email: { type: String, required: true },
    communityObjectId: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: CommunityModel,
    },
    communityCode: { type: String, required: false },
    role: { type: [String], required: true },
    config: { type: configSchema, required: false },
  },
  {
    collection: 'community_role',
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'lastModifiedTimeStamp',
    },
  }
);

const CommunityRoleModel = model('CommunityRole', CommunityRoleSchema);

module.exports = CommunityRoleModel;
