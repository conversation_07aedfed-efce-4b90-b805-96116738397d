const { Schema, model } = require('mongoose');
const { UserSchema } = require('../../models/discordUsers.model');

const ConfigSchema = new Schema(
  {
    collectionAddress: { type: String },
    collectionName: { type: String },
    minTokens: { type: Number },
    maxTokens: { type: Number },
  },
  { _id: false }
);

const AllDiscordRolesSchema = new Schema(
  {
    roleid: { type: String, unique: true },
    rolename: { type: String },
    roleDescription: { type: String },
    guildID: { type: String },
    guildName: { type: String },
    isAdminRole: { type: Boolean, default: false },
    permissions: { type: Array, default: [] },
    config: { type: ConfigSchema, default: null },
    isConfigured: { type: Boolean, default: false },
  },
  {
    collection: 'discord_roles',
    timestamps: true,
  }
);

AllDiscordRolesSchema.pre('remove', { query: true }, async function () {
  await UserSchema.updateMany(
    { _roles: this.roleid },
    { $pull: { _roles: this.roleid } },
    { multi: true }
  );
});

const AllDiscordRolesModel = model('discordroles', AllDiscordRolesSchema);
module.exports = AllDiscordRolesModel;
