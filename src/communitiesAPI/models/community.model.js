const { DateTime } = require('luxon');

const mongoose = require('mongoose');

const history = require('../../middleware/mongoose/history.middleware');

const { BOT_STATUSES } = require('../constants');
const {
  DEFAULT_CURRENCY,
  AI_TEMPLATE_GENERATION_STATUS,
  INTERVALS,
} = require('../../constants/common');

const collectionName = 'communities';

const FeatureListSchema = new mongoose.Schema(
  {
    featureId: { type: Number, required: true },
    featureName: { type: String, required: true },
    allowed: { type: Boolean, required: false, default: false },
    limit: { type: Number, required: false, default: 0 },
    interval: {
      type: String,
      enum: [...Object.values(INTERVALS), null],
      required: false,
    },
    intervalCount: { type: Number, required: false, default: null },
  },
  {
    _id: false,
  }
);

const earningAnalyticsSchema = new mongoose.Schema(
  {
    quantity: { type: Number, required: true },
    revenueInUsd: { type: Number, required: true },
    revenueInLocalCurrency: { type: Number, required: true },
  },
  { _id: false }
);

const ApplicationConfigDataFieldSchema = new mongoose.Schema(
  {
    label: { type: String, required: true },
    fieldName: { type: String, required: true },
    fieldDataType: {
      type: String,
      required: true,
      enum: [
        'text',
        'number',
        'date',
        'time',
        'boolean',
        'dropdown',
        'multi-select',
        'multi-select-with-other',
        'text-area',
        'phone',
        'radio',
        'checkbox',
        'email',
        'url',
      ],
    },
    inputSectionKey: { type: String, required: true },
    isEditable: { type: Boolean, required: true, default: false },
    isRequired: { type: Boolean, required: true, default: false },
    isVisible: { type: Boolean, required: true, default: true },
    isDisabled: { type: Boolean, required: true, default: false },
    placeholder: { type: String, required: false },
    defaultValue: { type: String, required: false },
    options: { type: Array, required: false },
    isEditableByAdmin: { type: Boolean, required: false },
    isSunlightUrl: { type: Boolean, default: false },
    isDeleted: { type: Boolean, default: false },
    notes: { type: String, required: false },
  },
  { _id: false }
);
const ImageMetaSchema = new mongoose.Schema(
  {
    width: { type: Number, default: null },
    height: { type: Number, default: null },
  },
  { _id: false }
);
const ImageDetailsSchema = new mongoose.Schema(
  {
    src: { type: String, required: true },
    meta: { type: ImageMetaSchema },
  },
  { _id: false }
);

const ImageSchema = new mongoose.Schema(
  {
    mobileImgData: { type: ImageDetailsSchema, required: false },
    desktopImgData: { type: ImageDetailsSchema, required: false },
  },
  { _id: false }
);

const botsScehema = new mongoose.Schema(
  {
    type: { type: String },
    serverKey: { type: String },
    status: {
      type: String,
      enum: Object.values(BOT_STATUSES),
      default: BOT_STATUSES.CONNECTED,
    },
    connectedDate: { type: Date },
  },
  { _id: false }
);

const tasksMetaDataSchema = new mongoose.Schema(
  {
    tasksProgress: {
      type: Object,
    },
    tasksCompletionPercentage: {
      type: Number,
      default: 0,
    },
  },
  {
    _id: false,
  }
);
const whatsappInfo = new mongoose.Schema(
  {
    isBotWhatsappGroupAdmin: {
      type: Boolean,
      required: false,
    },
    isBotInWhatsappGroup: {
      type: Boolean,
      required: false,
    },
  },
  { _id: false }
);

const trackingPixelSchema = new mongoose.Schema(
  {
    facebookPixel: {
      type: String,
      required: false,
    },
    tikTokPixel: {
      type: String,
      required: false,
    },
    gaMeasurementId: {
      type: String,
      required: false,
    },
    twitterPixelId: {
      type: String,
      required: false,
    },
    linkedinPartnerId: {
      type: String,
      required: false,
    },
  },
  { _id: false }
);

const fullScreenBannerImgDataSchema = new mongoose.Schema(
  {
    alt: { type: String },
    mobileImgProps: {
      src: { type: String },
      layout: { type: String, default: 'fill' },
      objectFit: { type: String, default: 'cover' },
      objectPosition: { type: String, default: 'center' },
    },
    desktopImgProps: {
      src: { type: String },
      layout: { type: String, default: 'fill' },
      objectFit: { type: String, default: 'cover' },
      objectPosition: { type: String, default: 'center' },
    },
  },
  { _id: false }
);

const revenueGoalSchema = new mongoose.Schema(
  {
    amount: { type: Number, default: 100000 }, // stored as cents
    currency: { type: String, default: DEFAULT_CURRENCY },
  },
  { _id: false }
);

const defaultPaymentMethods = [
  {
    value: 'stripe',
    label: 'stripe',
    icon: 'https://d2oi1rqwb0pj00.cloudfront.net/na-website/Payment/svg/bank.svg',
  },
];

const MemberManagementConfig = new mongoose.Schema(
  {
    maxInviteCount: { type: Number, required: false },
  },
  { _id: false }
);

const MagicReachSendLimit = new mongoose.Schema(
  {
    total: { type: Number, required: false },
    member: { type: Number, required: false },
    nonMember: { type: Number, required: false },
    window: { type: String, required: false },
  },
  { _id: false }
);

const MagicReachSendLimitConfig = new mongoose.Schema(
  {
    Whatsapp: { type: MagicReachSendLimit, required: false },
    Email: { type: MagicReachSendLimit, required: false },
  },
  { _id: false }
);

const MagicReachFraudConfig = new mongoose.Schema(
  {
    whitelisted: { type: Boolean, required: false, default: false },
    enforceInReviewProcess: {
      type: Boolean,
      required: false,
    },
  },
  { _id: false }
);

const AIAssistantInfo = new mongoose.Schema(
  {
    assistantId: { type: String, required: false },
    isEnabledForChallenges: { type: Boolean, required: false },
    isEnabledForEvents: { type: Boolean, required: false },
    isEnabledForCourse: { type: Boolean, required: false },
    isEnabledFor1on1: { type: Boolean, required: false },
  },
  {
    _id: false,
  }
);
const ApplicationConfig = new mongoose.Schema(
  {
    autoApproval: { type: Boolean, required: false, default: false },
  },
  { _id: false }
);

const WaitListConfig = new mongoose.Schema(
  {
    toNotify: { type: Object, required: false },
    features: { type: [String], required: false },
  },
  { _id: false }
);

const FeeDetails = new mongoose.Schema(
  {
    processingFee: { type: Number, required: true },
    revenueShare: { type: Number, required: false },
  },
  { _id: false }
);

const Price = new mongoose.Schema(
  {
    cmSetPrice: { type: Number, required: true },
    stripePrice: { type: Number, required: false },
    feeDetails: { type: FeeDetails, required: false },
    currency: { type: String, required: true },
    interval: { type: String, required: true },
    intervalCount: { type: Number, required: true },
    isHidden: { type: Boolean, required: false },
  },
  { _id: false }
);

const PayoutFeeConfigSchema = new mongoose.Schema(
  {
    paymentProviderFee: { type: Object, required: false },
    revenueShareInPercentage: { type: Number, required: false },
    effectiveTimeStart: { type: Date, required: true },
    effectiveTimeEnd: { type: Date, required: true },
    purchaseTypeConfigs: { type: Object, required: false },
    operator: { type: String, required: false },
    internationalFeePercentageByPaymentProvider: {
      type: Object,
      required: false,
    },
  },
  { _id: false }
);

const BasePayoutFeeConfigSchema = new mongoose.Schema(
  {
    revenueShareInPercentage: { type: Number, required: false },
    gstOnRevenueShareInPercentage: { type: Number, required: false },
    gatewayFeeInPercentage: { type: Number, required: false },
    gstOnGatewayFeeInPercentage: { type: Number, required: false },
    whtFeeInPercentage: { type: Number, required: false },
    minGatewayFee: { type: Number, required: false },
    processingFee: { type: Number, required: false },
    refundProcessingFee: { type: Number, required: false },
    currency: { type: String, required: false },
    effectiveTimeStart: { type: Date, required: true },
    effectiveTimeEnd: { type: Date, required: true },
    purchaseTypeConfigs: { type: Object, required: false },
    internationalFeePercentageByPaymentProvider: {
      type: Object,
      required: false,
    },
  },
  { _id: false }
);

const MembershipSyncSchema = new mongoose.Schema(
  {
    v1: { type: Boolean, required: false },
  },
  { _id: false }
);

const FraudInfo = new mongoose.Schema(
  {
    ticketOperations: { type: [Object], default: [] },
  },
  { _id: false }
);

const RestrictedSchema = new mongoose.Schema(
  {
    checkout: { type: Boolean, required: false },
    freeSubscribe: { type: Boolean, required: false },
    invite: { type: Boolean, required: false },
    magicReach: { type: Boolean, required: false },
    customEmail: { type: Boolean, required: false },
    upload: { type: Boolean, required: false },
  },
  { _id: false }
);

const responseTimeSchema = new mongoose.Schema(
  {
    frequencyCount: { type: Number, required: true },
    frequencyUnit: { type: String, required: true },
  },
  { _id: false }
);

const messageSettingsSchema = new mongoose.Schema(
  {
    isEnabled: { type: Boolean, required: true },
    welcomeMessage: { type: String, required: true, maxlength: 1200 },
    responseTime: { type: responseTimeSchema, required: true },
  },
  { _id: false }
);

const batchMetadataEnabledTypeSchema = new mongoose.Schema(
  {
    MEMBERSHIP: { type: Boolean, required: false },
    EVENT_ATTENDEE: { type: Boolean, required: false },
    FOLDER_VIEWER: { type: Boolean, required: false },
    SESSION_ATTENDEE: { type: Boolean, required: false },
    PROGRAM_PARTICIPANT: { type: Boolean, required: false },
    SUBSCRIPTION: { type: Boolean, required: false },
  },
  { _id: false }
);

const ConfigSchema = new mongoose.Schema(
  {
    hideGetInspiredBanner: { type: Boolean, required: false },
    hideNextThingsToDo: { type: Boolean, required: false },
    preferences: { type: Object, required: false },
    hideSignUpOverlay: { type: Boolean, required: false, default: true }, // default true for new communities ( as per the new request from product)
    hideEventAttendeeCount: { type: Boolean, required: false },
    firstDollarCampaign: { type: Boolean, required: false },
    localizeAddonPrice: { type: Boolean, required: false, default: true },
    localizeSubscriptionPrice: {
      type: Boolean,
      required: false,
      default: true,
    },
    hasSetProcessingFeePreference: { type: Boolean, required: false },
    feb2024CampaignRewardReceived: { type: Boolean, required: false },
    jul2024CampaignRewardReceived: { type: Boolean, required: false },
    aug2024CampaignRewardReceived: { type: Boolean, required: false },
    hideGetStarted: { type: Boolean, required: false, default: false },
    hideMemberCount: { type: Boolean, required: false, default: false },
    hideMemberTab: { type: Boolean, required: false, default: false },
    hideViewsOnPost: { type: Boolean, required: false, default: false },
    disableLikesOnPost: { type: Boolean, required: false, default: false },
    disableCommentsOnPost: {
      type: Boolean,
      required: false,
      default: false,
    },
    hideLikesOnPost: { type: Boolean, required: false, default: false },
    hideEventViewCount: { type: Boolean, required: false, default: false },
    fixedSubscriptionDuration: { type: Number, required: false },
    allowMembersToPost: { type: Boolean, required: false, default: true },
    memberPostApprovalRequired: {
      type: Boolean,
      required: false,
      default: true,
    },
    linkedCommunityCodesForOneSubscription: {
      type: [String],
      required: false,
    },
    hideProducts: { type: Boolean, required: false, default: false },
    checkoutRedirectLink: { type: String, required: false },
    disableNotification: {
      type: Boolean,
      required: false,
      default: false,
    },
    batchMetadataEnabledType: {
      type: batchMetadataEnabledTypeSchema,
      required: false,
    },
    messageSettings: { type: messageSettingsSchema, required: false },
    planType: { type: String, required: false },
    customFeeConfigs: { type: Object, required: false },
    showCommunityReferralLink: { type: Boolean, required: false },
  },
  { _id: false }
);

const CountrySchema = new mongoose.Schema(
  {
    id: { type: Number, required: true },
    name: { type: String, required: true },
    code: { type: String, required: true },
    count: { type: Number, required: false },
  },
  { _id: false }
);

const AccountManagerHistorySchema = new mongoose.Schema(
  {
    email: { type: String },
    startDate: { type: Date },
    endDate: { type: Date },
  },
  { _id: false }
);

const FollowerStatsSchema = new mongoose.Schema(
  {
    id: { type: String, required: true },
    label: { type: String, required: true },
  },
  { _id: false }
);

const AITemplateGenerationConfigSchema = new mongoose.Schema(
  {
    latestRequestId: { type: String, required: false },
    generationVersion: { type: Number, required: false }, // First template version will be 1
    generationStatus: {
      type: String,
      required: false,
      enum: Object.values(AI_TEMPLATE_GENERATION_STATUS),
    },
    processingStartTime: { type: Date, required: false },
    processingEndTime: { type: Date, required: false },
  },
  {
    _id: false,
  }
);

const {
  coverMediaItemSchema,
} = require('../../models/common/coverMediaItems.schema');

const CommunitySchema = new mongoose.Schema(
  {
    communityId: { type: Number, required: true },
    name: { type: String, required: true },
    banner: { type: String, required: false },
    title: { type: String, required: true },
    description: { type: String, default: '' },
    payment_methods: { type: Array, default: defaultPaymentMethods },
    configId: { type: mongoose.Schema.Types.ObjectId, required: false },
    stripeProductId: { type: String, required: false },
    stripeUsProductId: { type: String, required: false },
    previousStripeProductId: { type: String, required: false },
    platforms: { type: Array, default: [] },
    request_approval: { type: Boolean, default: false },
    isFreeCommunity: { type: Boolean, default: false },
    isWaitlist: { type: Boolean, default: false },
    code: { type: String, required: true, index: { unique: true } },
    communityShortCode: {
      type: String,
      required: true,
      index: { unique: true },
    },
    isActive: { type: Boolean, required: true, default: true },
    isDraft: { type: Boolean, required: true },
    avatarImgData: { type: ImageSchema, required: false },
    By: { type: String, required: false },
    trainerId: { type: mongoose.Schema.Types.ObjectId, ref: 'Trainer' },
    featuredMemberIds: { type: [Number], required: false, default: [] },
    tableDisplayData: { type: Object, required: false },
    applicationConfigDataFields: {
      type: [ApplicationConfigDataFieldSchema],
      required: false,
      default: [],
    },
    mobileApplicationConfigDataFields: {
      type: [ApplicationConfigDataFieldSchema],
      required: false,
      default: [],
    },
    bots: {
      type: [botsScehema],
    },
    completed_steps: { type: [Object], default: [] },
    countryCreatedIn: { type: String, required: false },
    expectedSize: { type: Number, required: false },
    communityApplicationId: {
      type: mongoose.Schema.Types.ObjectId,
      default: null,
    },
    managedByWhatsappBot: { type: String },
    isWhatsappExperienceConvertedDate: { type: Date, required: false },
    unmanaged: { type: Boolean, default: true },
    verificationChannelId: { type: String, required: false },
    fbGroupId: { type: String, required: false },
    hasViewedMonetisationModal: { type: Boolean, default: false },
    hasViewedMoneyPage: { type: Boolean, default: false },
    isTokenGated: { type: Boolean, required: false },
    isPaidCommunity: { type: Boolean, required: false },
    link: { type: String, required: true, index: { unique: true } },
    communityCheckoutCardData: { type: Object, required: false },
    thumbnailImgData: { type: ImageSchema, required: false },
    fullScreenBannerImgData: {
      type: fullScreenBannerImgDataSchema,
      required: false,
    },
    memberBenefits: { type: [String], default: [] },
    about: { type: [Object], required: false },
    robotContentIndexed: { type: Boolean, default: true },
    createdBy: { type: String, required: false },
    lastEditedApplicationForm: { type: Date, required: false },
    primarySocialLink: { type: String, required: false },
    platformPreviousData: { type: Object, required: false },
    isWhatsappExperienceCommunity: {
      type: Boolean,
      default: false,
      required: false,
    },
    discordAnnouncementChannelId: { type: String, required: false },
    botsPreviousData: { type: Object, required: false },
    payoutConfig: { type: Object, required: false },
    payoutFeeConfigs: {
      type: [PayoutFeeConfigSchema],
      required: false,
    },
    basePayoutFeeConfigs: {
      type: [BasePayoutFeeConfigSchema],
      required: false,
    },
    isDemo: { type: Boolean, required: false, default: false },
    revenueGoal: {
      type: revenueGoalSchema,
      required: true,
    },
    showEventRegistrationPrice: { type: Boolean, default: true }, // for not showing price on community checkout page for events
    updatedBy: { type: String, required: false },
    referralCodeUsed: { type: String, required: false },
    referrerEmail: { type: String, required: false },
    referrerObjectId: {
      type: mongoose.Schema.Types.ObjectId,
      required: false,
    },
    memberManagementConfig: {
      type: MemberManagementConfig,
      required: false,
    },
    communityVideoSizeLimit: {
      type: Number,
      required: false,
      default: 10000, // MB - 10 GB
    },
    magicReachSendLimitConfig: {
      type: MagicReachSendLimitConfig,
      required: false,
    },
    magicReachFraudConfig: {
      type: MagicReachFraudConfig,
      required: false,
    },
    whatsappInfo: {
      type: whatsappInfo,
    },
    aiAssistantInfo: {
      type: AIAssistantInfo,
      required: false,
    },
    applicationConfig: {
      type: ApplicationConfig,
      required: false,
      default: {
        autoApproval: false,
      },
    },
    discountsApplied: {
      type: [mongoose.Schema.Types.ObjectId],
      default: [],
    },
    trackingPixels: {
      type: trackingPixelSchema,
    },
    baseCurrency: { type: String, required: false },
    taskMetaData: {
      type: tasksMetaDataSchema,
    },
    waitListConfig: {
      type: WaitListConfig,
    },
    passOnPaymentGatewayFee: { type: Boolean, default: false },
    passOnTakeRate: { type: Boolean, default: false },
    prices: { type: [Price], required: false },
    config: {
      type: ConfigSchema,
      required: false,
      default: {},
    },
    membershipSyncInfo: {
      type: MembershipSyncSchema,
      required: false,
    },
    fraudInfo: {
      type: FraudInfo,
    },
    timezone: { type: String, required: false },
    communityCategory: {
      type: Object,
      required: false,
      default: {},
    },
    communityGoal: {
      type: Object,
      required: false,
    },
    indexable: { type: Boolean },
    metadata: {
      type: Object,
      required: false,
      default: {},
    },
    paypalProductId: { type: String, required: false },
    earningAnalytics: { type: earningAnalyticsSchema, required: false },
    affiliateEarningAnalytics: {
      type: earningAnalyticsSchema,
      required: false,
    },
    memberCountries: { type: [CountrySchema], required: false },
    notes: { type: String, required: false },
    restrictedInfo: { type: RestrictedSchema, required: false },
    currentAccountManagerEmail: { type: String, required: false },
    currentAccountManagerStartDate: { type: Date, required: false },
    currentAccountManagerEndDate: { type: Date, required: false },
    accountManagerHistory: {
      type: [AccountManagerHistorySchema],
      required: false,
    },
    followerStats: { type: FollowerStatsSchema, required: false },
    countriesUpdated: { type: Boolean, required: false },
    communityIntent: { type: String, required: false },
    aiTemplateGenerationConfig: {
      type: AITemplateGenerationConfigSchema,
      required: false,
    },
    coverMediaItems: {
      type: [coverMediaItemSchema],
      required: false,
    },
    communityReferralCode: { type: String, required: false },
    communityReferralCodeCreatedAt: { type: Date, required: false },
    featurePermissions: { type: [FeatureListSchema], required: false },
    currentStorageUsageInBytes: {
      type: Number,
      required: false,
      default: null,
      get: (v) => v,
      set(v) {
        if (v === null || v === undefined) return v;
        // Convert to Long to ensure Int64 storage
        return mongoose.mongo.Long.fromNumber(v);
      },
    },
    templateLibraryId: {
      type: mongoose.Schema.Types.ObjectId,
      required: false,
    },
  },
  {
    collection: collectionName,
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'lastModifiedTimeStamp',
    },
  }
);

CommunitySchema.pre('save', async function save(next) {
  try {
    this.lastModifiedTimeStamp = DateTime.local().setZone('UTC');
    return next();
  } catch (err) {
    return next(err);
  }
});

CommunitySchema.plugin(history, {
  collection: collectionName + '_history',
  mongoose,
});

const CommunityModel = mongoose.model('Community', CommunitySchema);

module.exports = CommunityModel;
