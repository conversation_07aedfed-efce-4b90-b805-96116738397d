const mongoose = require('mongoose');

const { Schema } = mongoose;

const whatsappAnalyticsSchema = new Schema({
  startDate: { type: Date },
  endDate: { type: Date },
  messagesSent: { type: [String], default: [] },
  reactions: { type: [String], default: [] },
  messageRead: { type: [String], default: [] },
  groupId: { type: String, require: true },
  uniqueMessagesSent: { type: Number },
  allMessagesSent: { type: Number, default: 0 },
  uniqueReactions: { type: Number, default: 0 },
  membersAddedToday: { type: Number, default: 0 },
  lastReadMessageRate: { type: Number, default: 0 },
  communityObjectId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
  },
  linkVisits: { type: Number, default: 0 },
  homePageLinkVisits: { type: Number, default: 0 },
  libraryFolderLinkVisits: { type: Number, default: 0 },
  eventLinkVisits: { type: Number, default: 0 },
});

const whatsappAnalyticsModel = mongoose.model(
  'whatsapp_analytics',
  whatsappAnalyticsSchema
);
module.exports = { whatsappAnalyticsModel };
