const { Schema, model } = require('mongoose');

const productDemoConfigsSchema = new Schema(
  {
    baseCommunityCode: { type: String, required: false },
    demoUserPassword: { type: String, required: false },
    demoUserFirstName: { type: String, required: false },
    demoUserLastName: { type: String, required: false },
    demoUserCountryId: { type: Number, required: false },
    demoUserProfilePic: { type: String, required: false },
    analytics: { type: Object, required: false },
    analyticsGraph: { type: Object, required: false },
    analyticsEngagement: { type: Object, required: false },
  },
  {
    collection: 'product_demo_configs',
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'lastModifiedTimeStamp',
    },
  }
);

const ProductDemoConfigs = model(
  'productDemoConfigsSchema',
  productDemoConfigsSchema
);
module.exports = ProductDemoConfigs;
