const { Schema, model } = require('mongoose');

const MagicReachBucketsSchema = new Schema(
  {
    bucket: { type: String, default: '', maxlength: 150, required: true },
    description: { type: String, default: '', maxlength: 240 },
    status: {
      type: Boolean,
      required: true,
      default: false,
    },
    showInComposer: {
      type: Boolean,
      default: true,
    },
  },
  {
    collection: 'community_magic_reach_buckets',
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'updatedAt',
    },
  }
);

const MagicReachBucketsModel = model(
  'MagicReachBuckets',
  MagicReachBucketsSchema
);

module.exports = MagicReachBucketsModel;
