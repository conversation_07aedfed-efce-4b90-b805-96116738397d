const { Schema, model } = require('mongoose');
const { communityFolderItemTypes } = require('../constants');
const Community = require('./community.model');
const Video = require('../../models/videos.model');
const Folder = require('./communityFolders.model');

const communityFolderItemSignedUrlDetailsSchema = new Schema(
  {
    communityFolderItemObjectId: {
      type: Schema.Types.ObjectId,
      required: false,
      default: null,
    },
    communityObjectId: {
      type: Schema.Types.ObjectId,
      ref: Community,
      required: true,
    },
    communityFolderObjectId: {
      type: Schema.Types.ObjectId,
      ref: Folder,
      required: true,
    },
    type: {
      type: String,
      required: false,
      enum: communityFolderItemTypes,
    },
    videoObjectId: {
      type: Schema.Types.ObjectId,
      ref: Video,
      required: false,
      default: null,
    },
    fileName: { type: String, required: false },
    s3Key: { type: String, required: false, default: '' },
    uploadId: { type: String, required: false, default: '' },
    location: { type: String, required: false, default: '' },
    bucket: { type: String, required: false, default: '' },
    eTag: { type: String, required: false, default: '' },
    reserveStorageId: { type: String, required: false, default: null },
  },
  {
    collection: 'community_folder_item_signed_url_details',
    timestamps: true,
  }
);

module.exports = model(
  'communityFolderItemSignedUrlDetailsSchema',
  communityFolderItemSignedUrlDetailsSchema
);
