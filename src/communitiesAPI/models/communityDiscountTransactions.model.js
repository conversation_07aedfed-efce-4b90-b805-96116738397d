const { Schema, model } = require('mongoose');

const CommunityDiscountTransactionSchema = new Schema(
  {
    purchaseTransactionObjectId: {
      type: Schema.Types.ObjectId,
      required: true,
    },
    communityCode: { type: String, required: true },
    communityDiscountObjectId: {
      type: Schema.Types.ObjectId,
      required: true,
    },
    status: {
      type: Number,
      default: 0,
      required: true,
    },
    quantity: { type: Number, required: false, default: 1 },
    redeemedAt: { type: Date, required: false },
    entityCollection: { type: String, required: false },
  },
  {
    collection: 'community_discount_transactions',
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'updatedAt',
    },
  }
);

module.exports = model(
  'CommunityDiscountTransactionSchema',
  CommunityDiscountTransactionSchema
);
