const { Schema, model } = require('mongoose');

const CommunityBookTrainerSchema = new Schema(
  {
    trainerId: { type: Schema.Types.ObjectId, ref: 'Trainer' },
    learnerId: { type: Schema.Types.ObjectId, ref: 'Learner' },
    communityId: { type: Schema.Types.ObjectId, ref: 'Community' },
    status: { type: String },
    startTime: { type: Date },
    endTime: { type: Date },
    bookingLink: { type: String },
    rescheduleLink: { type: String },
    cancelLink: { type: String },
    inviteeLink: { type: String },
    zoomLink: { type: String },
  },
  {
    collection: 'community_book_trainer',
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'lastModifiedTimeStamp',
    },
  }
);

const CommunityBookTrainerModel = model(
  'CommunityBookTrainer',
  CommunityBookTrainerSchema
);

module.exports = CommunityBookTrainerModel;
