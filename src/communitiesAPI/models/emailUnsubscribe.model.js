const { Schema, model } = require('mongoose');
const Community = require('./community.model');

const EmailUnsubscribeSchema = new Schema(
  {
    email: { type: String, required: true },
    communityObjectId: {
      type: Schema.Types.ObjectId,
      ref: Community,
      default: null,
    },
    communityCode: { type: String, default: null },
    identifier: { type: String, required: false },
    source: { type: String, required: false },
  },
  {
    collection: 'email_unsubscribe',
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'lastModifiedTimeStamp',
    },
  }
);

module.exports = model('EmailUnsubscribeSchema', EmailUnsubscribeSchema);
