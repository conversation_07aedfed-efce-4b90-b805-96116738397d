const { Schema, model } = require('mongoose');
const {
  COMMUNITY_INVITED_USERS_STATUSES,
} = require('../../constants/common');

const CommunityInvitedUsersSchema = new Schema(
  {
    email: { type: String, required: true },
    communityObjectId: { type: Schema.Types.ObjectId, required: true },
    communityCode: { type: String, required: true },
    invitedByEmail: { type: String, required: false },
    invitedByUserObjectId: { type: Schema.Types.ObjectId, required: true },
    status: {
      type: String,
      enum: Object.values(COMMUNITY_INVITED_USERS_STATUSES),
      default: COMMUNITY_INVITED_USERS_STATUSES.INVITED,
    },
    type: { type: String, default: 'member' },
  },
  {
    collection: 'community_invited_users',
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'updatedAt',
    },
  }
);

const CommunityInvitedUsersModel = model(
  'CommunityInvitedUsers',
  CommunityInvitedUsersSchema
);

module.exports = CommunityInvitedUsersModel;
