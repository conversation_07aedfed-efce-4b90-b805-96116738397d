const { Schema, model } = require('mongoose');
const { COMMUNITY_FOLDER_PURCHASE_TYPES } = require('../constants');
const { DEFAULT_CURRENCY } = require('../../constants/common');
const Folder = require('./communityFolders.model');
const Learner = require('../../models/learners.model');
const EntitySignup = require('./communityAddonTransactions.model');

const communityFolderPurchasesSchema = new Schema(
  {
    folderObjectId: {
      type: Schema.Types.ObjectId,
      ref: Folder,
      required: true,
    },
    learnerObjectId: {
      type: Schema.Types.ObjectId,
      ref: <PERSON>rner,
      required: true,
    },
    amount: { type: Number, required: false }, // stored in cents
    currency: { type: String, default: DEFAULT_CURRENCY },
    folderCheckoutId: {
      type: Schema.Types.ObjectId,
      ref: EntitySignup,
      required: true,
    },
    local_amount: { type: Number, required: false }, // stored in cents,
    local_currency: { type: String, default: DEFAULT_CURRENCY },
    purchaseType: {
      type: String,
      enum: Object.values(COMMUNITY_FOLDER_PURCHASE_TYPES),
      default: COMMUNITY_FOLDER_PURCHASE_TYPES.FREE,
    },
    note: { type: String, required: false },
    scriptExecutedDate: { type: Date, required: false },
  },
  {
    collection: 'community_folder_purchases',
    timestamps: true,
  }
);

module.exports = model(
  'communityFolderPurchasesSchema',
  communityFolderPurchasesSchema
);
