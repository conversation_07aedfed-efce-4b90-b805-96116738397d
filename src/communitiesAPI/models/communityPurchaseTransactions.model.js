const { Schema, model } = require('mongoose');

const PostApprovalProcessesSchema = new Schema(
  {
    type: { type: String, required: true },
    origin: { type: Schema.Types.ObjectId, required: true },
    quantity: { type: Number, required: false, default: 1 },
    transactionObjectId: { type: Schema.Types.ObjectId, required: false },
  },
  { _id: false }
);

const affiliateSchema = new Schema(
  {
    commissionPercentage: { type: Number, required: true },
    learnerObjectId: { type: Schema.Types.ObjectId, required: true },
    code: { type: String, required: true },
  },
  { _id: false }
);

const CommunityPurchaseTransactionSchema = new Schema(
  {
    community_code: { type: String, required: true },
    email: { type: String, required: true },
    country_id: { type: Number, required: false },
    requestor: { type: String, required: true },
    payment_details: { type: Object, required: false },
    country: { type: String, required: false },
    learnerId: { type: Number, required: false },
    learnerObjectId: { type: Schema.Types.ObjectId, required: false },
    amount: { type: Number, required: true },
    currency: { type: String, required: true },
    local_amount: { type: Number, required: true },
    local_currency: { type: String, required: true },
    stripeSubscriptionId: { type: String, required: false },
    post_approval_processes: {
      type: [PostApprovalProcessesSchema],
      required: false,
    },
    event_source: { type: Schema.Types.ObjectId, required: false },
    applyDiscount: { type: Boolean, required: false },
    discountAvailed: { type: Boolean, required: false },
    discountTransactionId: {
      type: Schema.Types.ObjectId,
      required: false,
    },
    full_amount: { type: Number, required: false },
    full_local_amount: { type: Number, required: false },
    promoCodeStripeId: { type: String, required: false },
    discountObjectId: { type: Schema.Types.ObjectId, required: false },
    subscriptionId: { type: Number, required: false },
    subscriptionObjectId: { type: Schema.Types.ObjectId, required: false },
    isDemo: { type: Boolean, default: false, required: false },
    applicationObjectId: { type: Schema.Types.ObjectId, required: false },
    phoneNumber: { type: String, required: false },
    firstName: { type: String, required: false },
    lastName: { type: String, required: false },
    timezone: { type: String, required: true },
    stripeProductProvider: { type: String, required: false },
    stripePriceId: { type: String, required: false },
    passOnPaymentGatewayFee: { type: Boolean, default: false },
    passOnTakeRate: { type: Boolean, default: false },
    languagePreference: { type: String, required: false },
    tracking_data: { type: Object, required: false },
    interval: { type: String, required: false },
    interval_count: { type: Number, required: false },
    isFreeTrial: { type: Boolean, required: false },
    metadata: { type: Object, required: false },
    priceDetails: { type: Object, required: false },
    affiliate: { type: affiliateSchema, required: false },
    upsellObjectId: { type: Schema.Types.ObjectId, required: false },
  },
  {
    collection: 'community_purchase_transactions',
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'updatedAt',
    },
  }
);

module.exports = model(
  'CommunityPurchaseTransactionSchema',
  CommunityPurchaseTransactionSchema
);
