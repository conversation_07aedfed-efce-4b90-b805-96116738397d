const mongoose = require('mongoose');

const history = require('../../middleware/mongoose/history.middleware');
const Community = require('./community.model');
const {
  createPayoutSchema,
} = require('../../models/common/commonPayoutSchema');

const collectionName = 'community_manager_payout';

const CommunityManagerPayoutSchema = createPayoutSchema(
  'communityId',
  Community,
  collectionName
);

CommunityManagerPayoutSchema.plugin(history, {
  collection: collectionName + '_history',
  mongoose,
});

const CommunityManagerPayoutModel = mongoose.model(
  'CommunityManagerPayoutSchema',
  CommunityManagerPayoutSchema
);

module.exports = CommunityManagerPayoutModel;
