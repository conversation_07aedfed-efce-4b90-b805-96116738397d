const { Schema, model } = require('mongoose');
const { communityManagerTodoTypes } = require('../constants');
const CommunityManagerTodos = require('./communityManagerTodos.model');
const Community = require('./community.model');
const User = require('../../models/users.model');

const CommunityManagerTodosProgressSchema = new Schema(
  {
    todoType: {
      type: String,
      required: true,
      enum: communityManagerTodoTypes,
    },
    todoCode: { type: String, required: true },
    communityManagerTodoObjectId: {
      type: Schema.Types.ObjectId,
      ref: CommunityManagerTodos,
      required: true,
    },
    communityObjectId: {
      type: Schema.Types.ObjectId,
      ref: Community,
      required: true,
    },
    completed: { type: Boolean, required: true },
    isActive: { type: Boolean, default: true },
  },

  {
    collection: 'community_manager_todos_progress',
    timestamps: true,
  }
);

const CommunityManagerTodosProgressModel = model(
  'CommunityManagerTodosProgress',
  CommunityManagerTodosProgressSchema
);

module.exports = CommunityManagerTodosProgressModel;
