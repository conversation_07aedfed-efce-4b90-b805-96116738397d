const { Schema, model } = require('mongoose');
const Community = require('../models/community.model');

const whatsappMessageSchema = new Schema({
  messageId: { type: String, required: true },
  groupId: { type: String, required: true },
  _data: {
    rowId: {
      type: Number,
    },
    body: {
      type: String,
    },
    type: {
      type: String,
    },
    t: {
      type: Number,
    },
    from: {
      server: {
        type: String,
      },
      user: {
        type: String,
      },
      _serialized: {
        type: String,
      },
    },
    to: {
      server: {
        type: String,
      },
      user: {
        type: String,
      },
      _serialized: {
        type: String,
      },
    },
    author: {
      server: {
        type: String,
      },
      user: {
        type: String,
      },
      _serialized: {
        type: String,
      },
    },
    self: {
      type: String,
    },
    ack: {
      type: Number,
    },
    invis: {
      type: Boolean,
    },
    star: {
      type: Boolean,
    },
    kicNotified: {
      type: Boolean,
    },
    isFromTemplate: {
      type: Boolean,
    },
    pollOptions: {
      type: Array,
    },
    pollInvalidated: {
      type: <PERSON><PERSON><PERSON>,
    },
    latestEditMsgKey: {
      type: 'Mixed',
    },
    latestEditSenderTimestampMs: {
      type: 'Mixed',
    },
    mentionedJidList: {
      type: Array,
    },
    groupMentions: {
      type: Array,
    },
    isVcardOverMmsDocument: {
      type: Boolean,
    },
    isForwarded: {
      type: Boolean,
    },
    labels: {
      type: Array,
    },
    hasReaction: {
      type: Boolean,
    },
    productHeaderImageRejected: {
      type: Boolean,
    },
    lastPlaybackProgress: {
      type: Number,
    },
    isDynamicReplyButtonsMsg: {
      type: Boolean,
    },
    isMdHistoryMsg: {
      type: Boolean,
    },
    stickerSentTs: {
      type: Number,
    },
    isAvatar: {
      type: Boolean,
    },
    requiresDirectConnection: {
      type: Boolean,
    },
    isEphemeral: {
      type: Boolean,
    },
    isStatusV3: {
      type: Boolean,
    },
    links: {
      type: Array,
    },
  },
  msgId: {
    fromMe: {
      type: Boolean,
    },
    remote: {
      type: String,
    },
    id: {
      type: String,
    },
    participant: {
      server: {
        type: String,
      },
      user: {
        type: String,
      },
      _serialized: {
        type: String,
      },
    },
    _serialized: {
      type: String,
    },
  },
  ack: {
    type: Number,
  },
  hasMedia: {
    type: Boolean,
  },
  body: {
    type: String,
  },
  type: {
    type: String,
  },
  timestamp: {
    type: Number,
  },
  from: {
    type: String,
  },
  to: {
    type: String,
  },
  author: {
    type: String,
    required: true,
  },
  deviceType: {
    type: String,
  },
  isForwarded: {
    type: Boolean,
  },
  forwardingScore: {
    type: Number,
  },
  isStatus: {
    type: Boolean,
  },
  isStarred: {
    type: Boolean,
  },
  fromMe: {
    type: Boolean,
  },
  hasQuotedMsg: {
    type: Boolean,
  },
  vCards: {
    type: Array,
  },
  mentionedIds: {
    type: Array,
  },
  isGif: {
    type: Boolean,
  },
  isEphemeral: {
    type: Boolean,
  },
  links: {
    type: Array,
  },
  communityObjectId: {
    type: Schema.Types.ObjectId,
    required: true,
    ref: Community,
  },
  reactions: [String],
});

const whatsappMessagesModel = model(
  'whatsapp_messages',
  whatsappMessageSchema
);

module.exports = whatsappMessagesModel;
