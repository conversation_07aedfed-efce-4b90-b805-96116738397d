const mongoose = require('mongoose');

const { Schema } = mongoose;
const connectionString = process.env.MONGO_URI;
const connection = mongoose.createConnection(connectionString, {
  dbName: 'whatsapp',
  useNewUrlParser: true,
  useUnifiedTopology: true,
});
const sessionsModelSchema = new Schema({
  id: { type: String },
  status: { type: String },
  groupid: { type: String },
  communityObjectId: { type: String },
  remoteSessionSaved: { type: Boolean, default: false },
  loggedOutStatus: { type: String, default: false },
  isSessionActive: { type: Boolean, default: false },
  clientLastActiveTime: { type: Date, default: Date.now },
  isBot: { type: Boolean, default: false },
  isOnBoardingDone: { type: Boolean, default: false },
});

const whatsappSession = connection.model(
  'whatsapp_sessions',
  sessionsModelSchema
);
module.exports = { whatsappSession };
