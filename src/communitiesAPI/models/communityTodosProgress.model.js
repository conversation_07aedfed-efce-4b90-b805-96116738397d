const { Schema, model } = require('mongoose');
const { communityTodoModes, communityTodoTypes } = require('../constants');

const CommunityTodosProgressSchema = new Schema(
  {
    todo: { type: Schema.Types.ObjectId, required: true },
    learnerId: { type: Number, required: true },
    completed: { type: Boolean, required: true },
    progress: { type: Object, required: false },
  },

  {
    collection: 'community_todos_progress',
  }
);

const CommunityTodosProgressModel = model(
  'CommunityTodosProgress',
  CommunityTodosProgressSchema
);

module.exports = CommunityTodosProgressModel;
