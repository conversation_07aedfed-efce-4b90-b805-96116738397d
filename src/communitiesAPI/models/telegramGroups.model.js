const { Schema, model } = require('mongoose');

const TelegramGroupSchema = new Schema(
  {
    group_id: { type: String, required: false, unique: true },
    group_user_name: { type: String, required: false },
    group_title: { type: String, required: false },
    admin_user_name: { type: String, required: false },
    admin_user_id: { type: String, required: false },
    phone_number: { type: String, required: false },
    users_added: {
      type: Boolean,
      required: false,
      default: false,
    },
  },
  {
    collection: 'telegram_groups',
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'lastModifiedTimeStamp',
    },
  }
);

const TelegramGroup = model('TelegramGroupSchema', TelegramGroupSchema);
module.exports = TelegramGroup;
