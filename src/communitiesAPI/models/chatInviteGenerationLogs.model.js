const { Schema, model } = require('mongoose');

const ChatInviteGenerationLogsSchema = new Schema(
  {
    communityId: { type: Schema.Types.ObjectId },
    chatType: { type: String },
    timesTried: { type: String },
    serverKey: { type: String },
    status: { type: String },
  },
  {
    collection: 'chat_invite_generation_logs',
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'lastModifiedTimeStamp',
    },
  }
);

const ChatInviteGenerationLogsModel = model(
  'ChatInviteGenerationLogs',
  ChatInviteGenerationLogsSchema
);

module.exports = ChatInviteGenerationLogsModel;
