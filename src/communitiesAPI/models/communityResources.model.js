const { Schema, model } = require('mongoose');
const Community = require('./community.model');

const communityResourcesSchema = new Schema(
  {
    communityObjectId: {
      type: Schema.Types.ObjectId,
      ref: Community,
      required: false,
    },
    thumbnail: {
      type: String,
      required: false,
    },
    title: { type: String, required: false },
    topic: { type: String, required: false },
    topicIndex: { type: Number, required: false },
    subIndex: { type: Number, required: false },
    shortUrl: { type: String, required: false },
    type: {
      type: String,
      default: 'video',
      enum: ['video', 'resource', 'external_link'],
    },
    time: { type: Date, required: false },
    tag: { type: String, required: false },
    icon: { type: String, default: null },
    emoji: { type: String, default: null },
  },
  {
    collection: 'community_resources',
    timestamps: {
      updatedAt: 'lastModifiedTimeStamp',
    },
  }
);

module.exports = model(
  'communityResourcesSchema',
  communityResourcesSchema
);
