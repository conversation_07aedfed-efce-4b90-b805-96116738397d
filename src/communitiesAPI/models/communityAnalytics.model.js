const { Schema, model } = require('mongoose');
const { analyticsTypes } = require('../constants');

const linkVisitsSchema = new Schema({
  linkVisits: { type: Number, default: 0 },
  homePageLinkVisits: { type: Number, default: 0 },
  libraryFolderLinkVisits: { type: Number, default: 0 },
  eventLinkVisits: { type: Number, default: 0 },
});

const membersSchema = new Schema(
  {
    netNew: { type: Number, required: false, default: 0 },
    unsubscribed: { type: Number, required: false, default: 0 },
    active: { type: Number, required: false, default: 0 },
    activeUnsubscribed: { type: Number, required: false, default: 0 },
    removed: { type: Number, required: false, default: 0 },
    paid: { type: Number, required: false, default: 0 },
    free: { type: Number, required: false, default: 0 },
  },
  { _id: false }
);

const revenueSchema = new Schema(
  {
    revenue: { type: Number, required: false, default: 0 },
    totalRevenue: { type: Number, required: false, default: 0 },
  },
  { _id: false }
);

const applicationsSchema = new Schema(
  {
    total: { type: Number, required: false, default: 0 },
    approved: { type: Number, required: false, default: 0 },
    pending: { type: Number, required: false, default: 0 },
    rejected: { type: Number, required: false, default: 0 },
  },
  { _id: false }
);

const waitlistSchema = new Schema(
  {
    conversion: { type: Number, required: false, default: 0 },
    total: { type: Number, required: false, default: 0 },
    purchase: { type: Number, required: false, default: 0 },
  },
  { _id: false }
);

const timeObject = new Schema(
  {
    day: { type: Number, required: false, default: null },
    week: { type: Number, required: false, default: null },
    month: { type: Number, required: false, default: null },
    year: { type: Number, required: false, default: null },
    intervalStartDate: { type: Date, required: false },
    intervalEndDate: { type: Date, required: false },
  },
  { _id: false }
);
const CommunityAnalyticsSchema = new Schema(
  {
    type: {
      type: String,
      required: true,
      enum: analyticsTypes,
    },
    communityId: { type: Schema.Types.ObjectId, required: true },
    timeObject: { type: timeObject, required: true },
    members: { type: membersSchema, required: false },
    revenue: { type: revenueSchema, required: false },
    applications: { type: applicationsSchema, required: false },
    waitlist: { type: waitlistSchema, required: false },
    linkVisits: { type: linkVisitsSchema, required: false },
  },
  {
    collection: 'community_analytics',
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'lastModifiedTimeStamp',
    },
  }
);

const CommunityAnalyticsModel = model(
  'CommunityAnalytics',
  CommunityAnalyticsSchema
);

module.exports = CommunityAnalyticsModel;
