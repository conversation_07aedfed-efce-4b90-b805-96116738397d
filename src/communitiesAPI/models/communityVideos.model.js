const { Schema, model } = require('mongoose');
const Video = require('../../models/videos.model');
const Community = require('./community.model');

const communityVideosSchema = new Schema(
  {
    communityObjectId: {
      type: Schema.Types.ObjectId,
      ref: Community,
      required: false,
    },
    title: { type: String, required: false },
    topic: { type: String, required: false },
    topicIndex: { type: Number, required: false },
    subIndex: { type: Number, required: false },
    shortUrl: { type: String, required: false },
    video: {
      type: Schema.Types.ObjectId,
      ref: Video,
      required: false,
    },
    time: { type: Date, required: false },
    tag: { type: String, required: false },
  },
  {
    collection: 'community_videos',
    timestamps: {
      updatedAt: 'lastModifiedTimeStamp',
    },
  }
);

module.exports = model('communityVideosSchema', communityVideosSchema);
