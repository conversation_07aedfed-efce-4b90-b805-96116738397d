const { Schema, model } = require('mongoose');

const CommunityUIConfigSchema = new Schema(
  {
    config: { type: Object, required: true },
  },
  {
    collection: 'community_ui_config',
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'updatedAt',
    },
  }
);

const CommunityUIConfigModel = model(
  'CommunityUIConfig',
  CommunityUIConfigSchema
);

module.exports = CommunityUIConfigModel;
