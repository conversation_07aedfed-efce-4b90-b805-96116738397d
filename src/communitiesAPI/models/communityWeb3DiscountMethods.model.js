const { Schema, model } = require('mongoose');

const {
  CONTRACT_TYPES,
  NETWORK_TYPES,
} = require('../../constants/common');

const countryWiseDiscountsScheme = new Schema(
  {
    country: { type: String, required: true },
    currency: { type: String, required: true },
    value: { type: Number, required: true },
  },
  { _id: false }
);

const collectionDataScheme = new Schema(
  {
    imgLink: {
      type: String,
      required: false,
      default:
        'https://s3.ap-southeast-1.amazonaws.com/image-assets.nasdaily.com/na-website/communities-page/png/nft_default.png',
    },
    name: {
      type: String,
      required: true,
    },
  },
  { _id: false }
);

const discountDetailsSchema = new Schema(
  {
    type: { type: String, required: true },
    value: { type: Number, required: true },
    currency: { type: String, reuired: false, default: 'USD' },
    allowInAllCountries: {
      type: Boolean,
      required: false,
      default: false,
    },
    countryWiseDiscounts: {
      type: [countryWiseDiscountsScheme],
      required: false,
      default: [],
    },
  },
  { _id: false }
);

const communityWeb3DiscountMethods = new Schema(
  {
    isActive: { type: Boolean, default: true },
    startDate: { type: Date, required: true },
    endDate: { type: Date, required: true },
    type: {
      type: String,
      enum: ['allowlisting', 'project_collaboration'],
    },
    contractAddress: { type: String, required: true },
    collectionName: { type: String, required: false },
    contractType: { type: String, default: CONTRACT_TYPES.ERC721 },
    contractAbi: { type: String, required: false },
    communityCode: { type: String },
    network: { type: String, default: NETWORK_TYPES.ETH },
    discountDetails: { type: discountDetailsSchema, required: false },
    collectionData: { type: collectionDataScheme, required: true },
  },
  {
    collection: 'community_web3_discount_methods',
  }
);

module.exports = model(
  'communityWeb3DiscountMethods',
  communityWeb3DiscountMethods
);
