const { Schema, model } = require('mongoose');
const { communityManagerTodoTypes } = require('../constants');

const CommunityManagerTodosSchema = new Schema(
  {
    todoCode: { type: String, required: true, index: { unique: true } },
    title: { type: String, required: true },
    description: { type: String, default: '' },
    icon: { type: String, required: true },
    link: { type: String, required: true },

    type: {
      type: String,
      required: true,
      enum: communityManagerTodoTypes,
    },
    index: { type: Number, required: true },
    isActive: { type: Boolean, default: true },
  },

  {
    collection: 'community_manager_todos',
  }
);

const CommunityManagerTodosModel = model(
  'CommunityManagerTodos',
  CommunityManagerTodosSchema
);

module.exports = CommunityManagerTodosModel;
