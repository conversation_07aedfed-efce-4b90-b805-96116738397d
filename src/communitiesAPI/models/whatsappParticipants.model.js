const { Schema, model } = require('mongoose');
const CommunitySubscription = require('../models/communitySubscriptions.model');
const Community = require('../models/community.model');

const whatsappParticipantsSchema = new Schema({
  contactId: { type: String, required: true }, // serialized Id of the user example 91(number)@c.us
  partId: {
    server: { type: String },
    user: { type: String },
    _serialized: { type: String },
  },
  number: { type: String, required: true },
  isBusiness: { type: Boolean },
  isEnterprise: { type: Boolean },
  name: { type: String },
  pushname: { type: String },
  shortName: { type: String },
  fullName: { type: String, required: true },
  type: { type: String },
  isMe: { type: Boolean },
  isUser: { type: Boolean },
  isGroup: { type: Boolean },
  isWAContact: { type: Boolean },
  isMyContact: { type: Boolean },
  isBlocked: { type: Boolean },
  joinedAt: { type: Date, default: Date.now, required: true },
  updatedAt: { type: Date, default: Date.now, required: true },
  chatLeftDate: { type: Date },
  isInWhatsApp: { type: Boolean, default: true, required: true },
  communityObjectId: {
    type: Schema.Types.ObjectId,
    required: true,
    ref: Community,
  },
  groupId: { type: String },
  profilePicUrl: { type: String },
  countryCodeNumber: { type: String, required: true },
  lastActive: { type: Date, default: Date.now },
  lastReacted: { type: Date },
  lastMessageSent: { type: Date },
  countryCode: { type: String, required: true },
  country: { type: String, required: true },
  subscriptionObjectId: {
    type: Schema.Types.ObjectId,
    ref: CommunitySubscription,
  },
  removeWhatsappDate: { type: Date, required: false },
  removeWhatsappStatus: { type: Boolean, required: false },
});

const whatsappParticipantsModel = model(
  'whatsapp_participants',
  whatsappParticipantsSchema
);

module.exports = whatsappParticipantsModel;
