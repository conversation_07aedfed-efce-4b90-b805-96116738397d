const { Schema, model } = require('mongoose');
const { communityTodoModes, communityTodoTypes } = require('../constants');

const CommunityTodosSchema = new Schema(
  {
    communityId: { type: Schema.Types.ObjectId, required: true },
    description: { type: String, required: false },
    title: { type: String, required: false },
    icon: { type: String, required: false },
    mode: { type: String, required: true, enum: communityTodoModes },
    isActive: { type: Boolean, required: true },
    type: {
      type: String,
      required: true,
      enum: communityTodoTypes,
    },
    scope: { type: Object, required: false, default: null },
    task: { type: Object, required: false, default: null },
    ctaButtonText: { type: String, required: false, default: '' },
  },

  {
    collection: 'community_todos',
  }
);

const CommunityTodosModel = model('CommunityTodos', CommunityTodosSchema);

module.exports = CommunityTodosModel;
