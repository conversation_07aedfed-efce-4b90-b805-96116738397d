const { Schema, model } = require('mongoose');
const { PRODUCT_TYPE } = require('../../services/product/constants');

const CommunityProductChangeLogsSchema = new Schema(
  {
    productType: {
      type: String,
      required: true,
      enum: Object.values(PRODUCT_TYPE),
    },
    entityObjectId: {
      type: Schema.Types.ObjectId,
      required: true,
    },
    changeFlags: {
      type: [String],
      required: true,
    },
    operatorLearnerObjectId: {
      type: Schema.Types.ObjectId,
      required: true,
    },
    notiStatus: {
      type: String,
      required: true,
    },
  },
  {
    collection: 'community_product_change_logs',
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'updatedAt',
    },
  }
);

const CommunityProductChangeLogsModel = model(
  'community_product_change_logs',
  CommunityProductChangeLogsSchema
);

module.exports = CommunityProductChangeLogsModel;
