const { Schema, model } = require('mongoose');
const CommunityEvent = require('./communityEvents.model');
const { EVENT_PAYMENT_STATUSES } = require('../constants');

const paymentDetailsSchema = new Schema(
  {
    status: {
      type: String,
      enum: Object.values(EVENT_PAYMENT_STATUSES),
      default: EVENT_PAYMENT_STATUSES.INCOMPLETE,
    },
    create_time: { type: Date, required: false },
    paymentProvider: { type: String, required: false },
    paymentCategory: { type: String, required: false },
    paymentType: { type: String, required: false },
  },
  { _id: false }
);

const CommunityEventCheckoutSchema = new Schema(
  {
    eventObjectId: {
      type: Schema.Types.ObjectId,
      ref: CommunityEvent,
      required: true,
    },
    email: { type: String, required: true },
    countryId: { type: String, required: false },
    payment_details: { type: paymentDetailsSchema, required: true },
    local_amount: { type: Number, required: true },
    local_currency: { type: String, required: true },
    amount: { type: Number, required: true },
    currency: { type: String, required: true },
    country: { type: String, required: false },
    learnerId: { type: Number, required: true },
    learnerObjectId: { type: Schema.Types.ObjectId, required: true },
  },
  {
    collection: 'event_checkout',
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'updatedAt',
    },
  }
);

module.exports = model(
  'CommunityEventCheckoutSchema',
  CommunityEventCheckoutSchema
);
