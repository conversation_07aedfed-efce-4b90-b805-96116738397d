const { Schema, model } = require('mongoose');

const GuestHostSchema = new Schema(
  {
    firstName: { type: String, required: true },
    lastName: { type: String, required: false },
    profileImage: { type: String, required: true },
    communityObjectId: { type: Schema.Types.ObjectId, required: true },
  },
  {
    collection: 'community_guest_host',
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'updatedAt',
    },
  }
);

const GuestHostModel = model('GuestHost', GuestHostSchema);

module.exports = GuestHostModel;
