const { Schema, model } = require('mongoose');
const mongoose = require('mongoose');

const BotsNumberSchema = new Schema(
  {
    botUserId: { type: String },
    botWhatsappId: { type: String },
    number: { type: String, required: false },
    botInNumberOfGroups: { type: Number, default: 0 },
    isBotActive: { type: Boolean, default: true },
    botName: { type: String, required: false },
    botQr: { type: String, required: false },
  },
  {
    collection: 'bots_numbers',
    timestamps: true,
  }
);

const whatsappBotNumbers = mongoose.model(
  'BotsNumberSchema',
  BotsNumberSchema
);
module.exports = whatsappBotNumbers;
