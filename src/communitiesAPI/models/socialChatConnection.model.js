const { Schema, model } = require('mongoose');
const { SOCIAL_CHAT_CONNECTION_TYPES } = require('../../constants/common');
const { BOT_STATUSES } = require('../constants');

const SocialChatConnectionSchema = new Schema(
  {
    communityObjectId: { type: Schema.Types.ObjectId, required: true },
    name: { type: String, required: false },
    description: { type: String, required: false },
    profileImageLink: { type: String, required: false },
    inviteLink: { type: String, default: null },
    status: {
      type: String,
      enum: Object.values(BOT_STATUSES),
      default: BOT_STATUSES.PENDING,
    },
    type: {
      type: String,
      enum: Object.values(SOCIAL_CHAT_CONNECTION_TYPES),
      required: true,
    },
    notes: { type: String, required: false },
  },
  {
    collection: 'social_chat_connection',
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'lastModifiedTimeStamp',
    },
  }
);
const SocialChatConnectionModel = model(
  'SocialChatConnection',
  SocialChatConnectionSchema
);
module.exports = SocialChatConnectionModel;
