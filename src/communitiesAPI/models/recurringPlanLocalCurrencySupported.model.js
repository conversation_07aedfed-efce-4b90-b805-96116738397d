const { Schema, model } = require('mongoose');

const countrySupportedSchema = new Schema(
  {
    country: { type: String, required: true },
    currency: { type: String, required: true, default: [] },
    allowDecimal: { type: Boolean, required: true, default: true },
    minInCents: { type: Number, required: true, default: 0 },
    limitInCents: { type: Number, required: true, default: -1 },
  },
  { _id: false }
);

const recurringPlanLocalCurrencySupportedSchema = new Schema(
  {
    supported: { type: [countrySupportedSchema], required: true },
  },
  {
    collection: 'recurring_plan_local_currency_supported',
  }
);

const RecurringPlanLocalCurrencySupported = model(
  'RecurringPlanLocalCurrencySupportedSchema',
  recurringPlanLocalCurrencySupportedSchema
);

module.exports = RecurringPlanLocalCurrencySupported;
