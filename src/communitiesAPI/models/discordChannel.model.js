const { Schema, model } = require('mongoose');

const DiscordChannelSchema = new Schema(
  {
    channel_id: { type: String, unique: true },
    channelName: { type: String },
    channelType: { type: String },
    guildId: { type: String },
  },
  {
    collection: 'discord_channels',
    timestamps: true,
  }
);

const discordChannel = model('DiscordChannelSchema', DiscordChannelSchema);
module.exports = discordChannel;
