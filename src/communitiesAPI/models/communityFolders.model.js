const { Schema, model } = require('mongoose');
const {
  communityFolderTypes,
  communityLibraryStatus,
  communityLibraryStatusMap,
  DEFAULT_COUNTRY_WISE_PRICE_CONFIG,
  COMMUNITY_EVENT_ACCESS_TYPES,
  communityLibraryTemplates,
} = require('../constants');
const { DEFAULT_CURRENCY } = require('../../constants/common');
const Community = require('./community.model');
const {
  coverMediaItemSchema,
} = require('../../models/common/coverMediaItems.schema');

const defaultPaymentMethods = [
  {
    value: 'stripe',
    label: 'stripe',
    icon: 'https://d2oi1rqwb0pj00.cloudfront.net/na-website/Payment/svg/bank.svg',
  },
];

const defaultFolderThumbnail =
  'https://d2oi1rqwb0pj00.cloudfront.net/nasIO/library/png/content-thumbnail-yellow-01.jpg';

const CountryWisePriceSchema = new Schema(
  {
    country: { type: String, required: false },
    currency: { type: String, required: false },
    amount: { type: Number, required: false }, // stored as cents
    localiseBasePrice: { type: Boolean, required: false },
  },
  { _id: false }
);

const ApplicationConfigDataFieldSchema = new Schema(
  {
    label: { type: String, required: true },
    fieldName: { type: String, required: true },
    fieldDataType: {
      type: String,
      required: true,
      enum: [
        'text',
        'number',
        'date',
        'time',
        'boolean',
        'dropdown',
        'multi-select',
        'multi-select-with-other',
        'text-area',
        'phone',
        'radio',
        'checkbox',
        'email',
        'url',
      ],
    },
    inputSectionKey: { type: String, required: true },
    isEditable: { type: Boolean, required: true, default: false },
    isRequired: { type: Boolean, required: true, default: false },
    isVisible: { type: Boolean, required: true, default: true },
    isDisabled: { type: Boolean, required: true, default: false },
    placeholder: { type: String, required: false },
    defaultValue: { type: String, required: false },
    options: { type: Array, required: false },
    isEditableByAdmin: { type: Boolean, required: false },
    isSunlightUrl: { type: Boolean, default: false },
    isDeleted: { type: Boolean, default: false },
  },
  { _id: false }
);

const availabilitySchema = new Schema(
  {
    day: { type: String, required: true },
    intervals: [
      {
        from: { type: String, required: true },
        to: { type: String, required: true },
      },
    ],
  },
  { _id: false }
);

const unavailabilitySchema = new Schema(
  {
    date: { type: String, required: true },
    intervals: [
      {
        from: { type: String, required: true },
        to: { type: String, required: true },
      },
    ],
  },
  { _id: false }
);

const earningAnalyticsSchema = new Schema(
  {
    quantity: { type: Number, required: true },
    revenueInUsd: { type: Number, required: false },
    revenueInLocalCurrency: { type: Number, required: false },
  },
  { _id: false }
);

const PricingConfigSchema = new Schema(
  {
    priceType: { type: String, required: false },
    minAmount: { type: Number, required: false },
    suggestedAmount: { type: Number, required: false },
  },
  {
    _id: false,
  }
);

const communityFoldersSchema = new Schema(
  {
    templateLibraryId: { type: Schema.Types.ObjectId, required: false },
    communityObjectId: {
      type: Schema.Types.ObjectId,
      ref: Community,
      required: true,
    },
    type: { type: String, required: true, enum: communityFolderTypes },
    index: { type: Number, required: true },
    title: { type: String, required: true },
    earningAnalytics: { type: earningAnalyticsSchema, required: false },
    accessCount: { type: Number, default: 0 },
    description: {
      type: String,
      required: false,
      default: '',
      limit: 5000,
    },
    resourceSlug: { type: String },
    thumbnail: { type: String, default: defaultFolderThumbnail },
    emoji: { type: String, required: false },
    shortUrl: { type: String, required: false },
    longUrl: { type: String, required: false },
    tags: { type: [{ type: String, required: false }], required: false },
    videoCount: { type: Number, required: false, default: 0 },
    totalItemsCount: { type: Number, required: false, default: 0 },
    otherFolderItemCount: { type: Number, required: false, default: 0 },
    isFeatured: { type: Boolean, required: false, default: false },
    isDraft: { type: Boolean, required: false },
    access: {
      type: String,
      enum: Object.values(COMMUNITY_EVENT_ACCESS_TYPES),
      default: COMMUNITY_EVENT_ACCESS_TYPES.FREE,
    },
    countryWisePrice: {
      type: [CountryWisePriceSchema],
      default: DEFAULT_COUNTRY_WISE_PRICE_CONFIG,
    },
    localiseForAllCountries: { type: Boolean, required: false },
    amount: { type: Number, required: false }, // stored in cents
    currency: { type: String, default: DEFAULT_CURRENCY },
    paymentMethods: { type: Array, default: defaultPaymentMethods },
    isDemo: { type: Boolean, default: false },
    status: {
      type: String,
      required: true,
      enum: communityLibraryStatus,
      default: communityLibraryStatusMap.UNPUBLISHED,
    },
    isPurchased: { type: Boolean, default: false },
    createdByLearnerObjectId: {
      type: Schema.Types.ObjectId,
      required: false,
    },
    templateId: {
      type: String,
      enum: communityLibraryTemplates,
    },
    discountsApplied: {
      type: [Schema.Types.ObjectId],
      default: [],
    },
    hostInfo: {
      hostLearnerObjectId: {
        type: Schema.Types.ObjectId,
        required: false,
        ref: 'LearnerSchema',
      },
      hostTitle: {
        type: String,
        required: false,
      },
      hostBio: {
        type: String,
        required: false,
      },
    },
    unAvailableDates: {
      type: [unavailabilitySchema],
      required: false,
    },
    availability: {
      type: [availabilitySchema],
      required: false,
    },
    applicationConfigDataFields: {
      type: [ApplicationConfigDataFieldSchema],
      required: false,
    },
    durationIntervalInMinutes: {
      type: Number,
      required: false,
    },
    minimumNoticeInDaysForBooking: {
      type: Number,
      required: false,
    },
    stopAcceptingBookings: {
      type: Boolean,
      required: false,
    },
    location: {
      type: Object,
      required: false,
    },
    timezoneChosenForAvailability: {
      type: String,
      required: false,
    },
    instalmentOptions: { type: Object, required: false },
    showLocation: {
      type: Boolean,
      required: false,
      default: false,
    },
    isSoldOut: { type: Boolean, required: false, default: false },
    pricingConfig: { type: PricingConfigSchema, required: false },
    affiliateEarningAnalytics: {
      type: earningAnalyticsSchema,
      required: false,
    },
    coverMediaItems: {
      type: [coverMediaItemSchema],
      required: false,
    },
  },
  {
    collection: 'community_folders',
    timestamps: true,
  }
);

module.exports = model('communityFoldersSchema', communityFoldersSchema);
