const { Schema, model } = require('mongoose');

const whatsappConnectedGroupSchema = new Schema({
  whatsappGroupId: { type: String, required: true },
  groupMetadata: {
    id: {
      server: { type: String },
      user: { type: String },
      _serialized: { type: String },
    },
    creation: { type: Number },
    owner: {
      server: { type: String },
      user: { type: String },
      _serialized: { type: String },
    },
    subject: { type: String },
    subjectTime: { type: Number },
    descTime: { type: Number },
    restrict: { type: Boolean },
    announce: { type: <PERSON>olean },
    noFrequentlyForwarded: { type: Boolean },
    ephemeralDuration: { type: Number },
    membershipApprovalMode: { type: Boolean },
    size: { type: Number },
    support: { type: Boolean },
    suspended: { type: Boolean },
    terminated: { type: Boolean },
    uniqueShortNameMap: {},
    isParentGroup: { type: Boolean },
    isParentGroupClosed: { type: Boolean },
    defaultSubgroup: { type: <PERSON>olean },
    lastActivityTimestamp: { type: Number },
    lastSeenActivityTimestamp: { type: Number },
    incognito: { type: Boolean },
    participants: { type: [Object] },
    pendingParticipants: { type: Array },
    pastParticipants: { type: Array },
    membershipApprovalRequests: { type: Array },
  },
  groupId: {
    server: { type: String },
    user: { type: String },
    _serialized: {
      type: String,
    },
  },
  name: { type: String, required: true },
  isGroup: { type: Boolean, required: true },
  isReadOnly: { type: Boolean },
  unreadCount: { type: Number },
  timestamp: { type: Number },
  pinned: { type: Boolean },
  isMuted: { type: Boolean },
  muteExpiration: { type: Number },
});

const whatsappConnectedGroupModel = model(
  'whatsapp_connected_groups',
  whatsappConnectedGroupSchema
);

module.exports = whatsappConnectedGroupModel;
