const { Schema, model } = require('mongoose');

const addressSchema = new Schema({
  additionalAddress: { type: String, required: false },
  address: { type: String, required: false },
  city: { type: String, required: false },
  postalCode: { type: String, required: false },
});

const CommunityModeratorsSchema = new Schema(
  {
    moderatorId: { type: Number, required: true },
    firstName: { type: String, required: true },
    lastName: { type: String, required: true },
    countryId: { type: Number, default: null },
    profileImage: { type: String, default: null },
    description: { type: String, default: '', maxlength: 140 },
    longDescription: { type: String, default: '', maxlength: 250 },
    isActive: { type: Boolean, required: true, default: true },
    email: { type: String, required: true },
    phoneNumber: { type: String, required: false },
    address: { type: addressSchema, required: false },
  },
  {
    collection: 'community_moderators',
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'lastModifiedTimeStamp',
    },
  }
);

const CommunityModeratorsModel = model(
  'communityModerators',
  CommunityModeratorsSchema
);

module.exports = CommunityModeratorsModel;
