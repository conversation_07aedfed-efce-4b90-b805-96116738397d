const { Schema, model } = require('mongoose');
const {
  communityFolderItemTypes,
  communityLibraryStatus,
  communityLibraryStatusMap,
  EMBEDDED_VIDEO_PLATFORMS,
} = require('../constants');
const Community = require('./community.model');
const Video = require('../../models/videos.model');

const communityFolderItemsSchema = new Schema(
  {
    communityObjectId: {
      type: Schema.Types.ObjectId,
      ref: Community,
      required: true,
    },
    communityFolderObjectId: {
      type: Schema.Types.ObjectId,
      //ref: Folder,
      required: true,
    },
    type: { type: String, required: true, enum: communityFolderItemTypes },
    platform: {
      type: String,
      required: false,
      enum: Object.values(EMBEDDED_VIDEO_PLATFORMS),
    },
    folderType: { type: String, required: false },
    videoObjectId: {
      type: Schema.Types.ObjectId,
      ref: Video,
      required: false,
      default: null,
    },
    format: {
      type: String,
      required: false,
    },
    duration: {
      type: String,
      required: false,
    },
    size: {
      type: String,
      required: false,
    },
    resourceSlug: { type: String },
    title: { type: String, required: true },
    description: { type: String, required: false, default: '' },
    link: { type: String, required: false },
    mp4Link: { type: String, required: false },
    thumbnail: { type: String, required: false },
    icon: { type: String, required: false },
    index: { type: Number },
    shortUrl: { type: String, required: false },
    clientUploadDuration: { type: String, required: false },
    longUrl: { type: String, required: false },
    isDemo: { type: Boolean, required: false, default: false },
    /**
     * hasValidFolderObjectId
     *
     * is false when video coverMediaItem is created before entity is created.
     *  - In this case, we set folderObjectId = communityObjectId and hasValidFolderObjectId = false.
     * is true
     *  - for non cover media items and
     *  - when entity is created and folderObjectId is set to entityObjectId and
     *  - when video coverMediaItem is created after entity.
     */
    hasValidFolderObjectId: {
      type: Boolean,
      required: false,
      default: true,
    },
    status: {
      type: String,
      required: true,
      enum: communityLibraryStatus,
      default: communityLibraryStatusMap.DRAFT,
    },
    processingPercentComplete: {
      type: String,
      required: false,
    },
    processingError: {
      type: String,
      required: false,
    },
    currentProcessingPhase: {
      type: String,
      required: false,
    },
    createdByLearnerObjectId: {
      type: Schema.Types.ObjectId,
      required: false,
    },
    parentSectionId: {
      type: Schema.Types.ObjectId,
      required: false,
    },
    s3UniqueId: {
      type: String,
      required: false,
    },
    isCoverVideo: {
      type: Boolean,
      required: false,
      default: false,
    },
  },
  {
    collection: 'community_folder_items',
    timestamps: true,
  }
);

module.exports = model(
  'communityFolderItemsSchema',
  communityFolderItemsSchema
);
