const mongoose = require('mongoose');

const communityAIAssistantSchema = new mongoose.Schema(
  {
    communityId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Community',
      required: true,
    },
    productId: {
      type: mongoose.Schema.Types.ObjectId,
    },
    productType: {
      type: String,
    },
    // TODO: add more fields later
    messagesCount: {
      type: Number,
    },
    sessionId: {
      type: String,
    },
    productPageViewed: {
      type: Boolean,
    },
    faiqViewed: {
      type: Boolean,
    },
    clickedJoin: {
      type: Boolean,
    },
    threadId: {
      type: String,
    },
    learnerId: {
      type: mongoose.Schema.Types.ObjectId,
      required: false,
    },
    countryId: {
      type: String,
      required: false,
    },
    isFAQ: {
      type: Boolean,
      required: false,
      default: false,
    },
    likedMessages: {
      type: Map,
      of: new mongoose.Schema(
        {
          messageId: String,
          text: String,
        },
        { _id: false }
      ),
      required: false,
    },
    dislikedMessages: {
      type: Map,
      of: new mongoose.Schema(
        {
          messageId: String,
          text: String,
        },
        { _id: false }
      ),
      required: false,
    },
    purchased: {
      type: Boolean,
      required: false,
    },
  },
  {
    timestamps: true,
    collection: 'communityAIAssistant',
  }
);

module.exports = mongoose.model(
  'CommunityAIAssistant',
  communityAIAssistantSchema
);
