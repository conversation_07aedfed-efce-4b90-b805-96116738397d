const { Schema, model } = require('mongoose');
const { BOT_STATUSES } = require('../constants');

const WhatsappGroupSchema = new Schema(
  {
    communityObjectId: { type: Schema.Types.ObjectId, required: true },
    creatorNumber: { type: String, required: true },
    name: { type: String, required: false },
    description: { type: String, required: false },
    profileImageLink: { type: String, required: false },
    inviteLink: { type: String, default: null },
    status: {
      type: String,
      enum: Object.values(BOT_STATUSES),
      default: BOT_STATUSES.PENDING,
    },
    notes: { type: String, required: false },
  },
  {
    collection: 'whatsapp_group_request',
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'lastModifiedTimeStamp',
    },
  }
);

module.exports = model('WhatsappGroupSchema', WhatsappGroupSchema);
