const { Schema, model } = require('mongoose');
const {
  COMMUNITY_EVENT_ATTENDEE_PURCHASE_TYPES,
  COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES,
} = require('../constants');

const StatusHistorySchema = new Schema(
  {
    status: {
      type: String,
      enum: Object.values(COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES),
      required: true,
    },
    timestamp: {
      type: Date,
      default: Date.now,
      required: true,
    },
    updatedTimestamp: {
      type: Date,
      default: Date.now,
      required: true,
    },
    updatedByEmail: { type: String, required: true },
    updatedByLearnerObjectId: {
      type: Schema.Types.ObjectId,
      required: true,
    },
  },
  {
    _id: false,
  }
);

const ticketReferenceSchema = new Schema(
  {
    ticketReference: { type: String, required: true },
    qrCodeSrc: { type: String, required: false },
    isCheckedIn: { type: Boolean, required: true },
    updatedAt: {
      type: Date,
      default: Date.now,
      required: true,
    },
    updatedByLearnerObjectId: {
      type: Schema.Types.ObjectId,
      required: true,
    },
    isViaQrCode: { type: Boolean, required: false },
  },
  {
    _id: false,
  }
);

const EventAttendeesSchema = new Schema(
  {
    email: { type: String, required: true },
    learnerId: { type: Number, required: false },
    learnerObjectId: {
      type: Schema.Types.ObjectId,
      ref: 'LearnerSchema',
      required: true,
    },
    communityObjectId: {
      type: Schema.Types.ObjectId,
      ref: 'Community',
      required: false,
    },
    eventObjectId: {
      type: Schema.Types.ObjectId,
      ref: 'CommunityEvents',
      required: true,
    },
    purchaseType: {
      type: String,
      enum: Object.values(COMMUNITY_EVENT_ATTENDEE_PURCHASE_TYPES),
      default: COMMUNITY_EVENT_ATTENDEE_PURCHASE_TYPES.FREE,
      required: true,
    },
    status: {
      type: String,
      enum: Object.values(COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES),
      default: COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING,
      required: true,
    },
    statusHistory: {
      type: [StatusHistorySchema],
      required: true,
      default: [],
    },
    ticketReferences: {
      type: [ticketReferenceSchema],
      required: false,
      default: [],
    },
    amount: { type: Number, required: true },
    local_amount: { type: Number, required: true },
    currency: { type: String, required: true },
    local_currency: { type: String, required: true },
    ticketReference: { type: String, required: true },
    eventCheckoutId: { type: Schema.Types.ObjectId, required: false },
    applicationInfo: { type: Array, required: false },
    approvalReviewDate: { type: Date, required: false },
    quantity: { type: Number, required: false, default: 1 },
  },
  {
    collection: 'event_attendees',
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'lastModifiedTimeStamp',
    },
  }
);

const EventAttendeesModel = model('EventAttendees', EventAttendeesSchema);

module.exports = EventAttendeesModel;
