const { Schema, model } = require('mongoose');

const whatsappTemplateSchema = new Schema(
  {
    category: { type: String, required: false },
    name: { type: String, required: true },
    language: { type: String, required: true },
    components: { type: Array, required: true },
  },
  {
    collection: 'whatsapp_templates',
    timestamps: true,
  }
);

module.exports = model('whatsappTemplateSchema', whatsappTemplateSchema);
