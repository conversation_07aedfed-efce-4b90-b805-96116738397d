const { Schema, model } = require('mongoose');
const CommunityFolder = require('./communityFolders.model');
const Community = require('./community.model');
const Learner = require('../../models/learners.model');

const communityFolderAccessLogsSchema = new Schema(
  {
    communityObjectId: {
      type: Schema.Types.ObjectId,
      ref: Community,
      required: true,
    },
    communityFolderObjectId: {
      type: Schema.Types.ObjectId,
      ref: CommunityFolder,
      required: true,
    },
    learnerObjectId: {
      type: Schema.Types.ObjectId,
      ref: Learner,
      required: true,
    },
  },
  {
    collection: 'community_folder_access_logs',
    timestamps: true,
  }
);

module.exports = model(
  'communityFolderAccessLogsSchema',
  communityFolderAccessLogsSchema
);
