const { Schema, model } = require('mongoose');
const { COMMUNITY_APPLICATION_STATUS } = require('../../constants/common');

const rejectedReasonSchema = new Schema(
  {
    label: { type: String, required: true },
    value: { type: String, required: false },
  },
  { _id: false }
);

const ApplicationSubmissionSchema = new Schema(
  {
    questionKey: { type: String, required: true },
    label: { type: String, required: true },
    fieldDataType: { type: String, required: true },
    isRequired: { type: Boolean, required: false },
    answer: { type: Array, required: false },
  },
  { _id: false }
);

const CommunityApplicationSchema = new Schema(
  {
    fullName: { type: String, required: false },
    primarySocialMediaLink: { type: String, required: false },
    secondarySocialMediaLink: { type: String, required: false },
    followersCount: { type: Number, required: false },
    telegramUsername: { type: String, required: false },
    discordUsername: { type: String, required: false },
    joiningReason: { type: [Object], required: false },
    niche: { type: Object, required: false },
    hasJoinedChat: { type: Boolean, required: false },
    joiningReasonFull: { type: [Object], required: false },
    selectedNicheOptions: { type: [Object], required: false },
    communityCode: { type: String, required: true },
    communitySignupId: { type: Schema.Types.ObjectId, required: true },
    learnerId: { type: Number, required: false },
    learnerObjectId: {
      type: Schema.Types.ObjectId,
      ref: 'LearnerSchema',
      index: true,
    },
    email: { type: String, required: true, index: true },
    status: {
      type: String,
      enum: Object.values(COMMUNITY_APPLICATION_STATUS),
    },
    rejectionReason: { type: rejectedReasonSchema, required: false },
    approvedBy: { type: String, required: false },
    rejectedBy: { type: String, required: false },
    statusUpdatedAt: { type: Date, required: false },
    reviewerNotes: { type: String, required: false },
    subscriptionId: { type: Number, required: false },
    subscriptionObjectId: { type: Schema.ObjectId, required: false },
    lastUpdatedByAdminEmail: { type: String, required: false }, // when an admin updates an application
    lastUpdatedByAdminAt: { type: Date, required: false },
    country: { type: String, required: false },
    isDemo: { type: Boolean, required: false, default: false },
    payment_details: { type: Object, required: false },
    applicationSubmission: {
      type: [ApplicationSubmissionSchema],
      required: false,
    },
  },
  {
    strict: false,
    collection: 'community_application',
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'updatedAt',
    },
  }
);

module.exports = model(
  'CommunityApplicationSchema',
  CommunityApplicationSchema
);
