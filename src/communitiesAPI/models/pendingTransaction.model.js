const { Schema, model } = require('mongoose');

const PendingTransactionSchema = new Schema(
  {
    entity: { type: String, required: true },
    entityObjectId: { type: Schema.Types.ObjectId, required: true },
    communityObjectId: {
      type: Schema.Types.ObjectId,
      required: true,
    },
    email: { type: String, required: false },
    quantity: { type: Number, required: false },
    applyToEntityType: { type: String, required: false },
    applyToEntityObjectId: {
      type: Schema.Types.ObjectId,
      required: false,
    },
  },
  {
    collection: 'pending_transactions',
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'updatedAt',
    },
  }
);

PendingTransactionSchema.index(
  { updatedAt: -1 },
  {
    expireAfterSeconds: 600,
  }
);

const PendingTransactionModel = model(
  'PendingTransaction',
  PendingTransactionSchema
);

module.exports = PendingTransactionModel;
