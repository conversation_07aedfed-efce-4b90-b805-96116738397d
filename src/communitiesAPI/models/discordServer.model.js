const { Schema, model } = require('mongoose');

const DiscordServerSchema = new Schema(
  {
    id: { type: String, required: true, unique: true },
    name: { type: String, required: false },
    ownerId: { type: String, required: true },
    joinedTimestamp: { type: Date, required: false },
  },
  {
    collection: 'discord_servers',
    timestamps: true,
  }
);

const DiscordServer = model('DiscordServerSchema', DiscordServerSchema);
module.exports = DiscordServer;
