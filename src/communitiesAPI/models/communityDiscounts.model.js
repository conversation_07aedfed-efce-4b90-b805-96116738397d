const { Schema, model } = require('mongoose');

const LinkedEntitySchema = new Schema(
  {
    type: {
      type: String,
      required: true,
    },
    entityObjectId: {
      type: Schema.Types.ObjectId,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    slug: {
      type: String,
      required: false,
    },
  },
  { _id: false }
);

const CommunityDiscountsSchema = new Schema(
  {
    code: { type: String, required: true },
    communityCode: { type: String, required: true },
    type: { type: String, required: true },
    value: { type: Number, required: true },
    isActive: { type: Boolean, default: true, required: true },
    maxRedemptions: { type: Number, default: null, required: false },
    maxRedemptionsPerPerson: {
      type: Number,
      default: null,
      required: false,
    },
    totalRedemptions: { type: Number, default: 0, required: false },
    effectiveTimeStart: { type: Date, required: false },
    effectiveTimeEnd: { type: Date, default: null, required: false },
    linkedEntities: { type: [LinkedEntitySchema], default: [] },
    discountPlatform: { type: String, required: false },
    promoCodeStripe: { type: String, required: false },
    discountCouponStripeId: { type: String, required: false },
    promoCodeStripeId: { type: String, required: false },
    communityStripeProductId: { type: String, required: false },
    trialDays: { type: Number, required: false },
    intervalCount: { type: Number, required: false },
    prefix: { type: String, required: false },
    timezone: { type: String, required: false },
    discountCategory: { type: String, required: false },
    expiredDuration: { type: Object, required: false },
    stripePromoCodeDetails: { type: Object, required: false },
    duration: { type: String, required: false },
    durationInMonths: { type: String, required: false },
  },
  {
    collection: 'community_discount',
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'updatedAt',
    },
  }
);

const CommunityDiscountsModel = model(
  'CommunityDiscount',
  CommunityDiscountsSchema
);

module.exports = CommunityDiscountsModel;
