const mongoose = require('mongoose');
const history = require('../../middleware/mongoose/history.middleware');

const Community = require('./community.model');
const User = require('../../models/users.model');
const {
  COMMUNITY_POSTS_VISIBILITY_LIST,
  COMMUNITY_POSTS_VISIBILITY_TYPE_MAP,
  COMMUNITY_POSTS_POSTED_BY_LIST,
  COMMUNITY_POSTS_STATUS_LIST,
  COMMUNITY_POSTS_STATUS,
  COMMUNITY_POSTS_POSTED_BY,
} = require('../constants');

const collectionName = 'community_posts';

const ReviewSchema = new mongoose.Schema(
  {
    reviewedDate: { type: Date, required: false },
    reviewedByLearnerObjectId: {
      type: mongoose.Schema.Types.ObjectId,
      required: false,
    },
    rejectedReason: { type: String, required: false },
    reviewedStatus: {
      type: String,
      required: false,
    },
  },
  {
    _id: false,
  }
);

const MentionedProductsSchema = new mongoose.Schema(
  {
    type: { type: String, required: false },
    productObjectId: {
      type: mongoose.Schema.Types.ObjectId,
      required: false,
    },
  },
  {
    _id: false,
  }
);

const ReportedDataSchema = new mongoose.Schema({
  reportedByLearnerObjectId: {
    type: mongoose.Schema.Types.ObjectId,
    required: false,
  },
  reportedDate: {
    type: Date,
    required: false,
  },
  reason: {
    type: String,
    required: false,
  },
});
const PostReportedSchema = new mongoose.Schema(
  {
    areReportsIgnored: { type: Boolean, required: false },
    reportedData: { type: [ReportedDataSchema], required: false },
  },
  {
    _id: false,
  }
);

const CommunityPostSchema = new mongoose.Schema(
  {
    isDefault: { type: Boolean, default: false },
    metadata: { type: Object, required: false },
    communityPostId: { type: Number, required: false },
    title: { type: String, required: true },
    description: { type: String, required: false },
    isAnnouncement: { type: Boolean, default: true },
    isPinned: { type: Boolean, default: false },
    status: {
      type: String,
      required: false,
      enum: COMMUNITY_POSTS_STATUS_LIST,
      default: COMMUNITY_POSTS_STATUS.APPROVED,
    },
    reviewData: {
      type: [ReviewSchema],
      required: false,
    },
    info: { type: String, required: false },
    videoSrc: { type: Object, required: false },
    blurHashString: { type: String, required: false },
    blurImageUrl: { type: String, required: false },
    editor: { type: mongoose.Schema.Types.ObjectId, required: false },
    slug: { type: String },
    imgSrc: { type: String, required: false },
    hideLikeCount: { type: Boolean, required: false, default: false },
    hideImpressions: { type: Boolean, required: false, default: false },
    author: {
      type: mongoose.Schema.Types.ObjectId,
      ref: User,
      required: true,
    },
    communities: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: Community,
        required: false,
      },
    ],
    impressions: { type: Number, required: false, default: 0 },
    lastProcessedDate: { type: Date, required: false },
    isDemo: { type: Boolean, required: false, default: false },
    disabledComments: { type: Boolean, required: false, default: false },
    content: { type: Object, required: false },
    reactionCounts: { type: Object, default: {} }, // each key in this object is reaction type and the value is the reaction count
    commentCount: { type: Number, default: 0 }, // count of comments on this announcement
    postedBy: {
      type: String,
      required: false,
      enum: COMMUNITY_POSTS_POSTED_BY_LIST,
      default: COMMUNITY_POSTS_POSTED_BY.CREATOR,
    },
    mentionedProducts: {
      type: [MentionedProductsSchema],
      required: false,
    },
    postReported: {
      type: PostReportedSchema,
      required: false,
    },
    visibilityType: {
      type: String,
      enum: COMMUNITY_POSTS_VISIBILITY_LIST,
      default: COMMUNITY_POSTS_VISIBILITY_TYPE_MAP.MEMBERS,
    },
    modifiedReason: { type: String, required: false },
  },
  {
    collection: collectionName,
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'lastModifiedTimeStamp',
    },
  }
);

CommunityPostSchema.plugin(history, {
  collection: collectionName + '_history',
  mongoose,
});

const CommunityPostModel = mongoose.model(
  'CommunityPost',
  CommunityPostSchema
);

module.exports = CommunityPostModel;
