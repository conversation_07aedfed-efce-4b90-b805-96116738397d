const { Schema, model } = require('mongoose');
const {
  COMMUNITY_PAYOUT_ACCOUNT_STATUS,
} = require('../../services/payout/constants');

const communityPayoutAccountSchema = new Schema(
  {
    communityObjectId: { type: Schema.Types.ObjectId, required: true },
    communityCode: { type: String, required: true },
    payoutGateway: {
      type: String,
      required: true,
    },
    status: {
      type: String,
      required: true,
      enum: Object.values(COMMUNITY_PAYOUT_ACCOUNT_STATUS),
    },
    payoutGatewayAccountId: { type: String, required: true },
    payoutCurrency: { type: String, required: true },
    email: { type: String, required: false },
    accountDetails: { type: Object },
  },
  {
    collection: 'community_payout_account',
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'updatedAt',
    },
  }
);

module.exports = model(
  'communityPayoutAccountSchema',
  communityPayoutAccountSchema
);
