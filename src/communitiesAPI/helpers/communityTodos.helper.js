const communityVideosService = require('../services/mobile/communityVideos.service');
const communityEventsService = require('../services/mobile/communityEvents.service');

const decorateVideoTodoTask = async (todo, videoObjectId) => {
  const video = await communityVideosService.getCommunityVideo(
    videoObjectId
  );
  // eslint-disable-next-line no-param-reassign
  todo.task = video;
  return todo;
};

const decorateEventTodoTask = async (todo, eventObjectId) => {
  const event = await communityEventsService.getCommunityEventByEventId(
    eventObjectId
  );
  // eslint-disable-next-line no-param-reassign
  todo.task = event;
  return todo;
};

const decoratePlatformTodoTask = async (todo, platforms, platform) => {
  const platformFound = platforms.find(
    (element) => element.name === platform
  );
  // eslint-disable-next-line no-param-reassign
  if (platformFound) todo.task = platformFound;
  return todo;
};

module.exports = {
  decorateVideoTodoTask,
  decorateEventTodoTask,
  decoratePlatformTodoTask,
};
