/* eslint-disable no-unused-vars */
const aws = require('aws-sdk');
const status = require('http-status');
const path = require('path');
const { ObjectId } = require('mongoose').Types;
const uuidv4 = require('uuid');
const Video = require('../../../models/videos.model');

const CommunityFolderItemSignedUrlDetails = require('../../models/communityFolderItemSignedUrlDetails.model');
const {
  communityFolderItemTypesMap,
  SIGNEDURL_UPLOAD_FILE_MIMETYPE,
  SIGNEDURL_UPLOAD_IMAGE_MIMETYPE,
  SIGNEDURL_UPLOAD_VIDEO_MIMETYPE,
  SIGNEDURL_UPLOAD_AUDIO_MIMETYPE,
} = require('../../constants');

const { VIDEO, IMG, AUDIO } = communityFolderItemTypesMap;
const {
  awsRegion,
  awsSecretKey,
  awsAccessKey,
} = require('../../../config');
const {
  FILEASSETBUCKET_CLOUDFRONT_BASE_URL,
  VIDEOASSETBUCKET,
  FILEASSETBUCKET,
} = require('../../../constants/common');
const logger = require('../../../services/logger.service');

const s3BaseUrl = 'https://s3.ap-southeast-1.amazonaws.com';

const nasIOFolderItemFileBucketPath = (communityId, communityFolderId) =>
  `nasIO/portal/folder-item/file/${communityId}/${communityFolderId}`;

const nasIOFolderItemVideoBucketPath = (communityId, communityFolderId) =>
  `nasIO/portal/folder-item/${communityId}/${communityFolderId}`;

const nasIOFolderItemImageBucketPath = (communityId, communityFolderId) =>
  `nasIO/portal/folder-item/image/${communityId}/${communityFolderId}`;

const nasIOFolderItemAudioBucketPath = (communityId, communityFolderId) =>
  `nasIO/portal/folder-item/audio/${communityId}/${communityFolderId}`;

const getBucket = (type) => {
  const buckets = {
    video: VIDEOASSETBUCKET,
    file: FILEASSETBUCKET,
    image: FILEASSETBUCKET,
    audio: FILEASSETBUCKET,
  };
  return buckets[type];
};

const checkMimeType = async (mimetype, type) => {
  let error = '';
  if (
    type === VIDEO &&
    !SIGNEDURL_UPLOAD_VIDEO_MIMETYPE.includes(mimetype)
  ) {
    error = 'Invalid file type. It has to be a mp4, mov file';
  } else if (
    type === IMG &&
    !SIGNEDURL_UPLOAD_IMAGE_MIMETYPE.includes(mimetype)
  ) {
    error = 'Invalid file type. It has to be a jpeg, jpg, png file';
  } else if (
    type === AUDIO &&
    !SIGNEDURL_UPLOAD_AUDIO_MIMETYPE.includes(mimetype)
  ) {
    error = 'Invalid file type. It has to be an mp3 file';
  } else if (
    ![VIDEO, IMG, AUDIO].includes(type) &&
    !SIGNEDURL_UPLOAD_FILE_MIMETYPE.includes(mimetype)
  ) {
    error =
      'Invalid file type. It has to be a pdf, doc, docx, xls, xlsx file';
  } else {
    return true;
  }
  const err = new Error(error);
  err.status = status.BAD_REQUEST;
  throw err;
};

function initializeS3(type) {
  const awsConfig = awsSecretKey
    ? {
        secretAccessKey: awsSecretKey,
        accessKeyId: awsAccessKey,
      }
    : { credentialProvider: new aws.CredentialProviderChain() };

  aws.config.update({
    region: awsRegion,
    ...awsConfig,
  });
  if (type === VIDEO) {
    aws.config.update({ useAccelerateEndpoint: true });
  }
  return new aws.S3();
}

async function getSingleUploadUrl(bucket, key, mimetype, s3) {
  const url = await s3.getSignedUrl('putObject', {
    Bucket: bucket,
    Key: key,
    ContentType: mimetype,
    Expires: 3600,
  });
  return url;
}

async function getSignedUrlForSinglePart(signedUrlId, partNumber) {
  const {
    s3Key: Key,
    bucket: Bucket,
    uploadId: UploadId,
    type,
  } = await CommunityFolderItemSignedUrlDetails.findOne({
    _id: new ObjectId(signedUrlId),
  });
  const s3 = initializeS3(type);
  const s3Url = await s3.getSignedUrlPromise('uploadPart', {
    Bucket,
    Key,
    UploadId,
    Expires: 3600,
    PartNumber: partNumber,
  });
  return s3Url;
}

async function initiateMultipartUpload(bucket, key, mimetype, s3) {
  const res = await s3
    .createMultipartUpload({
      Bucket: bucket,
      Key: key,
      ContentType: mimetype,
      Expires: 3600,
    })
    .promise();
  logger.info(`UploadId : ${res.UploadId}\n`);
  return res.UploadId;
}

async function generatePresignedUrlsParts(
  uploadId,
  parts,
  bucket,
  key,
  s3
) {
  const baseParams = {
    Bucket: bucket,
    Key: key,
    UploadId: uploadId,
    Expires: 3600,
  };

  const promises = [];

  for (let index = 0; index < parts; index++) {
    promises.push(
      s3.getSignedUrlPromise('uploadPart', {
        ...baseParams,
        PartNumber: index + 1,
      })
    );
  }

  const res = await Promise.all(promises);

  const records = res.reduce((map, part, index) => {
    // eslint-disable-next-line no-param-reassign
    map[index] = part;
    return map;
  }, {});

  logger.info(`Records : ${JSON.stringify(records)}\n`);
  return records;
}

async function completeMultiUpload(signedChunkDetails, signedUrlId) {
  try {
    const {
      s3Key: key,
      type,
      uploadId,
      communityFolderObjectId,
      fileName,
    } = await CommunityFolderItemSignedUrlDetails.findOne({
      _id: new ObjectId(signedUrlId),
    });

    logger.info(
      `\n\n${JSON.stringify(
        signedChunkDetails
      )} for the upload id ${uploadId} and folderObjectId ${communityFolderObjectId} and file name ${fileName}`
    );

    const bucket = getBucket(type);
    const params = {
      Bucket: bucket,
      Key: key,
      UploadId: uploadId,
      MultipartUpload: { Parts: signedChunkDetails },
    };

    const s3 = initializeS3(type);
    logger.info(
      params,
      ' params for complete multi upload for upload id',
      uploadId
    );
    const result = await s3.completeMultipartUpload(params).promise();
    logger.info(
      `\n\n${result} for the upload id ${uploadId}, folderObjectId ${communityFolderObjectId} and file name ${fileName}`
    );

    await CommunityFolderItemSignedUrlDetails.findOneAndUpdate(
      { _id: new ObjectId(signedUrlId) },
      { location: result.Location, bucket, eTag: result.ETag },
      {
        new: true,
      }
    ).lean();
    logger.info(
      `updated the signed url details for the upload id ${uploadId}, the communityFolderObjectId ${communityFolderObjectId} and file name ${fileName}`
    );
    return true;
  } catch (error) {
    logger.info(`\n\n Error: ${JSON.stringify(error)}`);
    return false;
  }
}

const multipartUpload = async (
  bucket,
  key,
  mimetype,
  numberOfChunks,
  s3
) => {
  const uploadId = await initiateMultipartUpload(
    bucket,
    key,
    mimetype,
    s3
  );
  const records = await generatePresignedUrlsParts(
    uploadId,
    numberOfChunks,
    bucket,
    key,
    s3
  );
  return { records, uploadId };
};

const getSignedUrlId = async (
  communityObjectId,
  communityFolderObjectId,
  type,
  videoObjectId,
  fileName,
  s3Key,
  uploadId,
  bucket,
  location,
  reserveStorageId = null
) => {
  const { _id: signedUrlId } =
    await CommunityFolderItemSignedUrlDetails.create({
      communityObjectId,
      communityFolderObjectId,
      type,
      videoObjectId,
      fileName,
      s3Key,
      uploadId,
      bucket,
      location,
      reserveStorageId,
    });
  return signedUrlId;
};

const getSignedUrls = async (
  originalname,
  numberOfChunks,
  userObjectId,
  createdBy,
  mimetype,
  type,
  communityId,
  communityFolderId,
  isMultipart,
  reserveStorageId = null
) => {
  let response = {};
  try {
    const fileName = originalname.replace(/\s/g, '-');
    const bucket = getBucket(type);
    let key;
    let videoObjectId = null;
    const s3 = initializeS3(type);
    await checkMimeType(mimetype, type);
    if (type === VIDEO) {
      const { _id: videoId } = await Video.create({
        userObjectId,
        createdBy,
      });
      const s3Path = nasIOFolderItemVideoBucketPath(
        communityId,
        communityFolderId
      );
      key = `${s3Path}/${videoId.toString()}${path.extname(fileName)}`;
      videoObjectId = videoId;
    } else if (type === AUDIO) {
      const s3Path = nasIOFolderItemAudioBucketPath(
        communityId,
        communityFolderId
      );
      key = `${s3Path}/${Date.now().toString()}-${uuidv4.v4()}__community_file.${path.extname(
        fileName
      )}`;
    } else if (type === IMG) {
      const s3Path = nasIOFolderItemImageBucketPath(
        communityId,
        communityFolderId
      );
      key = `${s3Path}/${Date.now().toString()}-${uuidv4.v4()}__community_file`;
    } else {
      const s3Path = nasIOFolderItemFileBucketPath(
        communityId,
        communityFolderId
      );
      key = `${s3Path}/${Date.now().toString()}-${uuidv4.v4()}__community_file`;
    }
    if (isMultipart) {
      const { records, uploadId } = await multipartUpload(
        bucket,
        key,
        mimetype,
        numberOfChunks,
        s3
      );
      const signedUrlId = await getSignedUrlId(
        communityId,
        communityFolderId,
        type,
        videoObjectId,
        originalname,
        key,
        uploadId,
        bucket,
        '',
        reserveStorageId
      );
      response = { records, signedUrlId };
    } else {
      const url = await getSingleUploadUrl(bucket, key, mimetype, s3);
      const [location] = url.split('?');

      let fileUrl = url;
      if (bucket === FILEASSETBUCKET) {
        fileUrl = location.replace(
          `${s3BaseUrl}/${FILEASSETBUCKET}`,
          FILEASSETBUCKET_CLOUDFRONT_BASE_URL
        );
      }
      const signedUrlId = await getSignedUrlId(
        communityId,
        communityFolderId,
        type,
        videoObjectId,
        originalname,
        key,
        '',
        bucket,
        fileUrl,
        reserveStorageId
      );
      response = { url, fileUrl, signedUrlId };
    }
  } catch (err) {
    logger.info(JSON.stringify(err));
    throw err;
  }
  return response;
};

const checkIfVideoFileExists = async (key) => {
  const s3 = initializeS3();
  const params = {
    Bucket: VIDEOASSETBUCKET,
    Key: key,
  };
  try {
    const data = await s3.headObject(params).promise();
    return true;
  } catch (err) {
    return false;
  }
};

module.exports = {
  getSignedUrls,
  completeMultiUpload,
  getSignedUrlForSinglePart,
  checkIfVideoFileExists,
};
