// models
const ObjectId = require('mongoose').Types.ObjectId;
const { DateTime } = require('luxon');
const jwt = require('jsonwebtoken');
const Community = require('../../models/community.model');
const CommunityRole = require('../../models/communityRole.model');
const CommunitySubscription = require('../../models/communitySubscriptions.model');
const CommunityDiscounts = require('../../models/communityDiscounts.model');
const ChatInviteGenerationLogs = require('../../models/chatInviteGenerationLogs.model');
const RecurringPlanLocalCurrencySupported = require('../../models/recurringPlanLocalCurrencySupported.model');
const CommunityEvent = require('../../models/communityEvents.model');
const CommunityFolder = require('../../models/communityFolders.model');
const AffiliateModel = require('../../../models/affiliate/affiliates.model');
const {
  IndiaPaymentWhitelistConfigService,
  AiSummaryConfigService,
  CustomWelcomeMessageConfigService,
} = require('../../../services/config');

const {
  DEFAULT_REVENUE_GOAL,
  PAYMENT_PROVIDER,
} = require('../../../constants/common');

// services
const logger = require('../../../services/logger.service');
const {
  COMMUNITY_SUBSCRIPTION_STATUSES,
  CHAT_PLATFORMS,
} = require('../../../constants/common');
const {
  BOT_STATUSES,
  COMMUNITY_FOLDER_STATUS,
  COMMUNITY_EVENT_ACCESS_TYPES,
  COMMUNITY_FOLDER_PURCHASE_TYPES,
  aclRoles,
  LEGENDS_COMMUNITY_CODE,
  INDIAUPI_COMMUNITY_CODE,
  LATAM_COMMUNITY_CODE,
  latamCountriesArray,
  COUNTRY_CREATED,
  communityFolderTypesMap,
  EVENT_STATUS,
} = require('../../constants');

const { NAS_IO_FRONTEND_URL } = require('../../../config/index');

const {
  getCommunityBotDetails,
  retrieveProductWithPrices,
  getLearnerCommunitySubscriptionDetails,
  getLearnersCommunityDetails,
  getCommunityCodesByIds,
  getCommunityById,
  getCommunitiesByIds,
  updateOneCommunity,
  updateCommunityData,
  linkValidation,
  updateCommunityForPublicApi,
} = require('../common/community.service');
const { getConfigByType } = require('../../../services/config.service');
const {
  isLearnerSubscribedToCommunity,
} = require('../common/communitySubscriptions.service');
const { learnerSubscriptionService } = require('../common/learner');
const axios = require('../../../clients/axios.client');

const {
  DISCORD_URL,
  DISCORD_AUTH,
  TELEGRAM_URL,
  TELEGRAM_AUTH,
} = require('../../../config');

const { CONFIG_TYPES } = require('../../../constants/common');
const {
  performAdditionalTasksForSocialBots,
} = require('../../controllers/web/communities/utils');

const WhatsappGroup = require('../../models/whatsappGroup.model');
const {
  sendMessageToSQSFifoQueue,
} = require('../../../handlers/sqs.handler');
const { CHAT_INVITE_LINK_QUEUE_URL } = require('../../../config');
const communitySubscriptionsService = require('./communitySubscriptions.service');
const memberService = require('../../../services/membership/count.service');

const { retrieveWhatsappGroupInfo } = require('./whatsapp.service');
const whatsappBotNumbers = require('../../models/whatsappBotNumbers.model');

const { ParamError, ToUserError } = require('../../../utils/error.util');
const PaymentProviderUtil = require('../../../utils/paymentProvider.util');
const {
  getLocalCurrencyByCountry,
} = require('../../../services/payout/common.service');
const sessionAttendeesModel = require('../../../models/oneOnOneSessions/sessionAttendees.model');
const FeeService = require('../common/fee.service');
const CommunityPostModel = require('../../models/communityPost.model');
const ProgramModel = require('../../../models/program/program.model');
const { PROGRAM_STATUS } = require('../../../services/program/constants');
const commonConfigService = require('../../../services/config/common.service');
const communityRoleService = require('./communityRole.service');
const { GENERIC_ERROR } = require('../../../constants/errorCode');
const {
  generateCoverMediaItems,
} = require('../../../utils/multipleCoverMediaItems.util');
const {
  COVER_MEDIA_ENTITY_TYPES,
} = require('../../../constants/coverMediaItems.constant');
const {
  retrieveLastPurchasePlanOrder,
} = require('../../../services/plan/planOrder.service');
const MetaAdsIntegration = require('../../../models/magicAudience/metaAdsIntegration.model');

const storeLogsForChatInviteGeneration = async (data) => {
  try {
    logger.info('chat link generation failed data', data);
    const chatInviteGenerationLogs = new ChatInviteGenerationLogs(data);
    await chatInviteGenerationLogs.save();
  } catch (err) {
    logger.error(
      `Error while storing logs for chat invite generation: ${err.message}`
    );
  }
};

const getMetaAdsIntegration = async ({
  communityObjectId,
  communityCode,
}) => {
  const communityId =
    communityObjectId ||
    (
      await Community.findOne({
        code: communityCode,
      })
        .select('_id')
        .lean()
    )._id;

  if (!communityId) {
    throw new Error('Community not found');
  }
  const integration = await MetaAdsIntegration.findOne({
    communityObjectId: communityId,
  }).lean();

  return integration || null;
};

const addBotToCommunity = async (
  communityId,
  type,
  serverKey,
  botStatus = BOT_STATUSES.CONNECTED
) => {
  try {
    const communityWithServerKey = await Community.findOne({
      'bots.serverKey': serverKey,
    });

    if (communityWithServerKey) {
      // send error that same server is connected to another community
      return {
        error: {
          message: 'Server key already connected to another community',
          code: 400,
        },
        data: null,
      };
    }

    let discordAuth = DISCORD_AUTH;
    let telegramAuth = TELEGRAM_AUTH;
    if (!discordAuth || !telegramAuth || (!discordAuth && !telegramAuth)) {
      logger.info(
        'No discordAuth and serverAuth found. Searching from the config collection...'
      );
      const { envVarData = null } = await getConfigByType(
        CONFIG_TYPES.LEARN_PORTAL_BACKEND_ENV_CONFIG_TYPE
      );
      if (envVarData && Object.keys(envVarData).length) {
        logger.info('Setting the env var found in config...');
        telegramAuth = envVarData?.TELEGRAM_AUTH;
        discordAuth = envVarData?.DISCORD_AUTH;
      }
    }
    const pushQuery = {
      bots: {
        type,
        serverKey,
        status: botStatus,
        connectedDate: DateTime.utc(),
      },
    };

    let link;

    if (type === 'Telegram') {
      try {
        logger.info(`Creating new telegram invite link`);
        const res = await axios.get(
          `${TELEGRAM_URL}/telegram/invite?group_id=${serverKey}`,
          {
            headers: {
              'api-key': `${telegramAuth}`,
            },
          }
        );
        link = res?.data?.result?.invite_link;
      } catch (err) {
        logger.error(
          'Sent message to the chat invite link Queue to retry'
        );
        sendMessageToSQSFifoQueue(CHAT_INVITE_LINK_QUEUE_URL, null, {
          inviteData: {
            communityId,
            type: 'Telegram',
            serverKey,
            timesTried: 0,
          },
        });
        await storeLogsForChatInviteGeneration({
          communityId,
          chatType: 'Telegram',
          timesTried: 0,
          serverKey,
          error: err,
          status: 'retrying',
        });
        logger.info('Error occured retrieving link for telegram:', err);
      }
    } else if (type === 'Discord') {
      try {
        logger.info(`Creating new discord invite link`);
        const api = `${DISCORD_URL}/api/v1/invite/${serverKey}`;
        const authHeader = jwt.sign({}, discordAuth);
        const res = await axios.get(api, {
          headers: {
            Authorization: `Bearer ${authHeader}`,
          },
        });
        link = res?.data?.invite;
      } catch (err) {
        // send a sqs message to retry
        logger.error(
          'Sent message to the chat invite link Queue to retry'
        );
        sendMessageToSQSFifoQueue(CHAT_INVITE_LINK_QUEUE_URL, null, {
          inviteData: {
            communityId,
            type: 'Discord',
            serverKey,
            timesTried: 0,
          },
        });
        logger.error('Error occured retrieving link for discord:', err);
        await storeLogsForChatInviteGeneration({
          communityId,
          chatType: 'Discord',
          timesTried: 0,
          error: err,
          serverKey,
          status: 'retrying',
        });
      }
    } else if (type === 'Whatsapp') {
      try {
        const { inviteLink = null } = await WhatsappGroup.findById(
          serverKey
        );
        link = inviteLink;
      } catch (err) {
        logger.error('Error occured retrieving link for whatsapp:', err);
      }
    }

    if (link) {
      logger.info(`Link is present: ${link}`);
      pushQuery.platforms = {
        name: type?.toLowerCase(),
        link,
      };
      logger.info(`New push query for community updates: ${pushQuery}`);
    }

    await Community.findOneAndUpdate(
      {
        _id: communityId,
        'bots.type': { $ne: type },
      },
      {
        $push: {
          ...pushQuery,
        },
      },
      {
        upsert: false,
        new: true,
      }
    );
    // This is handle the addition of the platform links to the community
    await performAdditionalTasksForSocialBots(
      communityId,
      type,
      serverKey,
      botStatus
    );
    // This findOne is to get the updated community data
    const community = await Community.findOne({ _id: communityId });
    return { data: community, error: null };
  } catch (err) {
    logger.error('Error occured adding bot data to community:', err);
    // throw new Error('Error occured adding bot data to community:');
    return {
      error: {
        message: err.message,
      },
      data: null,
    };
  }
};

const setMonetisationModalViewed = async (communityId) => {
  try {
    const community = await updateOneCommunity(
      { _id: communityId },
      {
        hasViewedMonetisationModal: true,
      }
    );
    return community;
  } catch (err) {
    logger.error(
      'Error occured setting monetization modal viewed:',
      err,
      err.stack
    );
    throw new Error('Error occured setting monetization modal viewed:');
  }
};

const setMoneyPageViewed = async (
  communityId,
  hasViewedMoneyPage = false
) => {
  try {
    const community = await updateOneCommunity(
      { _id: communityId },
      {
        hasViewedMoneyPage,
      }
    );
    return community;
  } catch (err) {
    logger.error(
      'Error occured setting money page viewed:',
      err,
      err.stack
    );
    throw new Error('Error occured setting money page viewed:');
  }
};

// eslint-disable-next-line no-unused-vars
const deleteBotFromCommunity = async (communityId, type, serverKey) => {
  try {
    const community = await Community.findOne({ _id: communityId });
    if (!community) {
      throw new ParamError('Community not found');
    }
    const botIndex = community.bots.findIndex((b) => b.type === type);
    if (botIndex >= 0) {
      community.bots.splice(botIndex, 1);
    }
    await community.save();
    return community;
  } catch (err) {
    logger.error(
      'Error occured deleting bot data from community:',
      err,
      err.stack
    );
    const error = new Error(
      'Error occured deleting bot data from community:'
    );
    error.status = err.status || 500;
    throw error;
  }
};

const getWhatsappBotMetadata = async (result) => {
  let whatsappBotMetaData = {};
  if (
    !!result.managedByWhatsappBot &&
    result.isWhatsappExperienceCommunity
  ) {
    logger.info(
      'getCommunities: now querying data from botsNumber collection to get whatsapp bot info for the botNumberId',
      result.managedByWhatsappBot,
      ' and active Community code',
      result.code
    );

    const whatsappBotInfo = await whatsappBotNumbers
      .findOne({
        botWhatsappId: result.managedByWhatsappBot,
      })
      .lean();

    if (whatsappBotInfo) {
      logger.info('whatsappBotInfo found', whatsappBotInfo);

      const { botWhatsappId, botQr, botName, number } =
        whatsappBotInfo || {};
      whatsappBotMetaData = {
        contactName: botName,
        contactQr: botQr,
        numberId: botWhatsappId,
        number,
      };
    }
  }
  return whatsappBotMetaData;
};

const getPricingRelatedInformation = async (
  community,
  countryCreatedIn
) => {
  const [
    payoutCurrency,
    recurringLocalCurrency,
    isEligibleForLocalPayment,
    { localCurrencies, productPricing },
  ] = await Promise.all([
    getLocalCurrencyByCountry(countryCreatedIn),
    RecurringPlanLocalCurrencySupported.findOne().lean(),
    // Return a flag to indicate if this CM is eligible for india payment or not
    IndiaPaymentWhitelistConfigService.isCommunityWhitelisted(
      community._id
    ),
    communitySubscriptionsService.getCommunityProductPricing(
      community.stripeProductId,
      community.payment_methods
    ),
  ]);
  let revenueGoal = community.revenueGoal;
  if (
    isEligibleForLocalPayment &&
    community.revenueGoal?.currency !==
      DEFAULT_REVENUE_GOAL.INR.currency &&
    community.countryCreatedIn === 'India'
  ) {
    revenueGoal = DEFAULT_REVENUE_GOAL.INR;
  }
  const { supported: recurringPlanLocalCurrencySupported = [] } =
    recurringLocalCurrency;

  const subscriptionSupportedCurrenciesSetting =
    recurringPlanLocalCurrencySupported.map(
      (supported) => supported.currency
    );

  const localPriceEnabled = localCurrencies.some((localCurrency) =>
    recurringPlanLocalCurrencySupported.find(
      (currencySupported) =>
        currencySupported.currency === localCurrency.currency
    )
  );

  const {
    paymentDetails,
    defaultPaymentDetails,
    proPaymentDetails,
    paymentDetailsByTiers,
  } = await FeeService.getProAndDefaultFeeRate({ community });

  return {
    payoutCurrency: payoutCurrency.toLowerCase(),
    productPricing,
    isEligibleForLocalPayment,
    revenueGoal,
    localCurrencies,
    subscriptionSupportedCurrenciesSetting,
    localPriceEnabled,
    paymentDetails,
    defaultPaymentDetails,
    proPaymentDetails,
    paymentDetailsByTiers,
  };
};

const getCommunityDetailsByCode = async (
  activeCommunityCode,
  activeCommunityId,
  learnerId,
  ip,
  email = ''
) => {
  try {
    if (!activeCommunityCode) {
      return null;
    }
    const existingManagerRole = await CommunityRole.findOne({
      email,
      communityCode: activeCommunityCode,
      role: aclRoles.MANAGER,
    }).lean();

    const aggregateQuery = [
      {
        $match: {
          code: activeCommunityCode,
        },
      },
      {
        $lookup: {
          from: 'community_web3_discount_methods',
          let: { communityCode: '$code' },
          pipeline: [
            {
              $match: {
                $expr: { $eq: ['$communityCode', '$$communityCode'] },
              },
            },
            {
              $project: {
                address: '$contractAddress',
                collectionName: 1,
              },
            },
          ],
          as: 'contractAddressess',
        },
      },
      {
        $addFields: {
          hasViewedMoneyPage: {
            $ifNull: ['$hasViewedMoneyPage', false],
          },
          'config.memberPostApprovalRequired': {
            $ifNull: ['$config.memberPostApprovalRequired', true],
          },
          'config.allowMembersToPost': {
            $ifNull: ['$config.allowMembersToPost', true],
          },
        },
      },
    ];

    logger.info(
      `getCommunityDetailsByCode: aggregate query ${JSON.stringify(
        aggregateQuery
      )}`
    );

    const results = await Community.aggregate(aggregateQuery);

    const activeCommunities = await Promise.all(
      results.map(async (community) => {
        const result = community;

        const [
          hasFolders,
          hasPaidFolders,
          hasPaidEvents,
          events,
          hasDigitalProducts,
          hasSessions,
          hasChallenges,
          hasEvents,
          affiliate,
          hasAnnouncements,
          coverMediaItems,
        ] = await Promise.all([
          CommunityFolder.findOne({
            status: COMMUNITY_FOLDER_STATUS.PUBLISHED,
            communityObjectId: new ObjectId(community._id),
          })
            .select('_id')
            .lean(),
          CommunityFolder.findOne({
            status: COMMUNITY_FOLDER_STATUS.PUBLISHED,
            communityObjectId: community._id,
            access: COMMUNITY_FOLDER_PURCHASE_TYPES.PAID,
          })
            .select('_id')
            .lean(),
          CommunityEvent.findOne({
            communities: community._id,
            access: COMMUNITY_EVENT_ACCESS_TYPES.PAID,
          })
            .select('_id')
            .lean(),
          CommunityEvent.findOne(
            {
              communities: community._id,
              startTime: { $gte: new Date() },
            },
            { discountsApplied: 0 }
          )
            .sort({ startTime: 1 })
            .limit(1)
            .lean(),
          CommunityFolder.findOne({
            communityObjectId: community._id,
            type: communityFolderTypesMap.DIGITAL_PRODUCT,
            status: COMMUNITY_FOLDER_STATUS.PUBLISHED,
          }).select('_id'),
          CommunityFolder.findOne({
            communityObjectId: community._id,
            type: communityFolderTypesMap.SESSION,
            status: COMMUNITY_FOLDER_STATUS.PUBLISHED,
          }).select('_id'),
          ProgramModel.findOne({
            communityObjectId: community._id,
            status: PROGRAM_STATUS.PUBLISHED,
          }),
          CommunityEvent.findOne({
            communities: community._id,
            status: {
              $in: [EVENT_STATUS.PUBLISHED, 'Active'],
            },
          }),
          AffiliateModel.findOne({
            learnerObjectId: learnerId,
            communityObjectId: community._id,
          }).lean(),
          CommunityPostModel.findOne({
            communities: community._id,
          }),
          generateCoverMediaItems({
            entity: community,
            entityType: COVER_MEDIA_ENTITY_TYPES.COMMUNITY,
            isCommunityManager: existingManagerRole,
          }),
        ]);

        result.hasAffiliate = !!affiliate;
        result.events = events ? [events] : [];
        result.hasFolders = !!hasFolders;
        result.hasPaidFolders = !!hasPaidFolders;
        result.hasPaidEvents = !!hasPaidEvents;
        result.hasDigitalProducts = !!hasDigitalProducts;
        result.hasSessions = !!hasSessions;
        result.hasChallenges = !!hasChallenges;
        result.hasEvents = !!hasEvents;
        result.hasAnnouncements = !!hasAnnouncements;
        result.coverMediaItems = coverMediaItems;

        if (!result.createdBy) {
          const role = await CommunityRole.findOne(
            {
              communityObjectId: new ObjectId(result._id),
              role: aclRoles.OWNER,
            },
            { email: 1 }
          ).lean();
          result.createdBy = role?.email ?? '';
        }

        let builderCommunityCode = LEGENDS_COMMUNITY_CODE;
        if (result.countryCreatedIn === COUNTRY_CREATED.INDIA) {
          builderCommunityCode = INDIAUPI_COMMUNITY_CODE;
        } else if (latamCountriesArray.includes(result.countryCreatedIn)) {
          builderCommunityCode = LATAM_COMMUNITY_CODE;
        }

        const [
          pendingApplications,
          memberCountInfo,
          builderCommunity,
          upcomingSessionEvents = [],
          whatsappBotMetaData,
          pricingData,
        ] = await Promise.all([
          CommunitySubscription.countDocuments({
            communityCode: result?.code,
            status: COMMUNITY_SUBSCRIPTION_STATUSES.PENDING,
          }),
          memberService.countCommunityMembers({
            communityCode: result?.code,
          }),
          Community.findOne({
            code: builderCommunityCode,
          })
            .select('link')
            .lean(),
          sessionAttendeesModel
            .find({
              communityObjectId: result._id,
              sessionEndTime: { $gte: new Date() },
              attendeeLearnerObjectId: learnerId,
              isPending: false,
            })
            .sort({ sessionStartTime: 1 })
            .populate(
              'hostLearnerObjectId sessionObjectId',
              'firstName lastName profileImage title'
            )
            .lean(),
          getWhatsappBotMetadata(result),
          getPricingRelatedInformation(result, community.countryCreatedIn),
        ]);
        const memberCount = memberCountInfo.count;
        const foundWhatsapp = result.bots?.find(
          (bot) => bot?.type === CHAT_PLATFORMS.WHATSAPP
        );

        const whatsappStatus = {};

        if (
          (result.isWhatsappExperienceCommunity ?? false) &&
          foundWhatsapp
        ) {
          const [whatsappGroupInfo] = await Promise.all([
            await retrieveWhatsappGroupInfo(result._id),
          ]);

          whatsappStatus.whatsappGroupName =
            whatsappGroupInfo.whatsappGroupName;
          whatsappStatus.whatsappMemberCount =
            memberCountInfo.statusBreakdown.inWhatsappGroup;
          whatsappStatus.totalMemberCount = memberCount;

          if (existingManagerRole) {
            whatsappStatus.whatsappGroupNumber =
              whatsappGroupInfo.whatsappGroupNumber;
            whatsappStatus.isWhatsappBotAdmin =
              community.whatsappInfo?.isBotWhatsappGroupAdmin;
            whatsappStatus.isWhatsappBotInGroup =
              community.whatsappInfo?.isBotInWhatsappGroup;
            whatsappStatus.isWhatsappGodModeConnected =
              whatsappGroupInfo.isGodModeConnected;
          }
        }

        if (builderCommunity) {
          result.builderCommunityLink = `${NAS_IO_FRONTEND_URL}${builderCommunity.link}`;
        }

        if (existingManagerRole) {
          const discountIds = result.discountsApplied ?? [];

          const [discountsApplied, numberOfManager] = await Promise.all([
            result.isPaidCommunity && discountIds.length
              ? CommunityDiscounts.find({
                  _id: { $in: discountIds },
                  communityCode: result.code,
                  isActive: true,
                }).lean()
              : null,
            CommunityRole.countDocuments({
              communityCode: result.code,
              role: aclRoles.MANAGER,
            }),
          ]);

          if (discountsApplied) {
            logger.info(
              `Found discounts for communities ${
                result._id
              }: ${JSON.stringify(discountsApplied)}`
            );
            result.discountsApplied = discountsApplied;
          } else {
            result.discountsApplied = [];
          }

          result.noOfManagers = numberOfManager;
        } else {
          delete result.discountsApplied;
        }
        let isCommunityWhitelistedForAiSummary = false;
        if (result.isWhatsappExperienceCommunity) {
          const isCommunityBlacklistedForAiSummary =
            await AiSummaryConfigService.isCommunityCodeBlacklisted(
              result.code
            );
          if (!isCommunityBlacklistedForAiSummary) {
            isCommunityWhitelistedForAiSummary =
              await AiSummaryConfigService.isCommunityCodeWhitelisted(
                result.code
              );
          }
        }

        const [
          hasAccessToZeroLink,
          hasAccessToGetInspired,
          isCommunityCodeBlacklistedForCustomMessage,
        ] = await Promise.all([
          commonConfigService.isCommunityIdWhitelisted(
            CONFIG_TYPES.ZERO_LINK_WHITELIST_CONFIG_TYPE,
            result
          ),
          commonConfigService.isCommunityIdWhitelisted(
            CONFIG_TYPES.GET_INSPIRED_WHITELIST_CONFIG_TYPE,
            result
          ),
          CustomWelcomeMessageConfigService.isCommunityCodeBlacklisted(
            result.code
          ),
        ]);
        if (result.config?.planType) {
          const planOrder = await retrieveLastPurchasePlanOrder({
            communityObjectId: community._id,
            entityType: community.config?.planType,
          });
          result.config.hasPlanOrder = !!planOrder;
        }

        return {
          ...result,
          ...whatsappStatus,
          ...pricingData,
          hasAccessToZeroLink,
          hasAccessToGetInspired,
          showCustomWelcomeMessage:
            !isCommunityCodeBlacklistedForCustomMessage,
          showAiSummary: !!isCommunityWhitelistedForAiSummary,
          isManager: !!existingManagerRole,
          roleConfig: existingManagerRole?.config,
          pendingApplications,
          members: memberCount,
          isPricingEditable: memberCountInfo.summary.memberCount > 0,
          whatsappBotMetaData,
          upcomingSessionEvents,
        };
      })
    );
    return activeCommunities;
  } catch (err) {
    logger.error('getCommunityDetailsByCode error', err, err.stack);
    throw err;
  }
};

const retrieveCommunityProductPrices = async (communityId) => {
  const community = await Community.findById(communityId, {
    stripeProductId: 1,
    stripeUsProductId: 1,
    baseCurrency: 1,
    passOnTakeRate: 1,
    passOnPaymentGatewayFee: 1,
    prices: 1,
    payment_methods: 1,
    isFreeCommunity: 1,
  }).lean();

  if (!community) {
    throw new Error('Community not found');
  }
  if (community.isFreeCommunity) {
    return { prices: [], baseCurrency: community.baseCurrency };
  }

  const paymentProvider =
    await PaymentProviderUtil.retrievePaymentProvider(
      community.payment_methods
    );

  const result = await retrieveProductWithPrices(
    paymentProvider === PAYMENT_PROVIDER.STRIPE_US
      ? community.stripeUsProductId
      : community.stripeProductId,
    community.prices
  );

  return { prices: result, baseCurrency: community.baseCurrency };
};

const getActiveCommunity = async ({
  learnerObjectId,
  params = {},
  email,
  ip,
}) => {
  const activeCommunityId = params?.activeCommunityId;
  let activeCommunityCode;

  if (activeCommunityId) {
    const activeCommunityData = await Community.findOne({
      _id: activeCommunityId,
    }).lean();

    if (!activeCommunityData) {
      throw new ParamError('Community not found');
    }
    activeCommunityCode = activeCommunityData.code;
  } else {
    // Fetch user communities and set the 1st one as activeCommunityCode
    const activeCommunityFromSubscription =
      await learnerSubscriptionService.retrieveActiveCommunityFromSubscription(
        {
          learnerObjectId,
          withPendingSubscription: true,
        }
      );

    if (!activeCommunityFromSubscription) {
      throw new ToUserError(
        'No active subscription found',
        GENERIC_ERROR.USER_NO_ACTIVE_SUBSCRIPTION
      );
    }

    activeCommunityCode = activeCommunityFromSubscription.code;
  }

  const [
    activeCommunityDetails,
    activeCommunitySubscriptionDetails,
    metaAdsIntegration,
  ] = await Promise.all([
    getCommunityDetailsByCode(
      activeCommunityCode,
      activeCommunityId,
      learnerObjectId,
      ip,
      email
    ),
    getLearnerCommunitySubscriptionDetails({
      communityCode: activeCommunityCode,
      learnerObjectId,
    }),
    await getMetaAdsIntegration({
      communityCode: activeCommunityCode,
      communityObjectId: activeCommunityId,
    }),
  ]);

  const result = {
    ...(activeCommunityDetails?.[0] || {}),
    subscription: activeCommunitySubscriptionDetails?.[0],
    metaAdsIntegration: metaAdsIntegration || {},
  };

  return result;
};

const getLearnerCommunities = async (
  learnerId,
  learnerObjectId,
  userObjectId,
  params = {},
  ip,
  email = ''
) => {
  try {
    const result = {
      communities: [],
      activeCommunity: [],
    };
    let activeCommunityCode = params?.activeCommunity;
    const activeCommunityId = params?.activeCommunityId;

    const communityDetails = await getLearnersCommunityDetails({
      learnerId,
      userObjectId,
      learnerObjectId,
      activeCommunityId,
    });
    result['communities'] = communityDetails.sortedCommunities;

    if (!activeCommunityId) {
      activeCommunityCode = communityDetails.sortedCommunities?.[0]?.code;
    }

    if (!activeCommunityCode) {
      activeCommunityCode =
        communityDetails.activeCommunitySubscription?.communityCode;
    }

    const subscribedToCommunity = await isLearnerSubscribedToCommunity({
      learnerId,
      activeCommunityCode,
    });

    if (subscribedToCommunity) {
      result.activeCommunity = await getCommunityDetailsByCode(
        activeCommunityCode,
        activeCommunityId,
        learnerObjectId,
        ip,
        email
      );
      if (result.activeCommunity.length > 0) {
        result.activeCommunity[0].subscription =
          communityDetails.activeCommunitySubscription;
      }
    }
    return result;
  } catch (err) {
    logger.error('getLearnerCommunities error', err, err.stack);
    throw err;
  }
};

const getCommunityDataWithSubscriptionAndRole = async ({
  communityObjectId,
  learnerObjectId,
  userObjectId,
}) => {
  let data = {};
  // get community
  const community = await getCommunityById(communityObjectId);

  // get learner subscription
  const communityCode = community?.code;
  const subscription =
    await learnerSubscriptionService.getLearnerSubscription(
      communityCode,
      learnerObjectId,
      'status cancelledAt nextBillingDate paymentProvider recurringPurchase onGracePeriod',
      { checkPendingSubscription: true }
    );

  // get role
  let membersWithRoles = [];
  if (subscription) {
    // only check for role if user has a subscription
    membersWithRoles = await communityRoleService.getUserCommunityRole({
      userObjectId,
      communityCode,
      fields: 'role',
    });
  }

  const isManager = Boolean(
    membersWithRoles.find((communityRoleData) => {
      return (
        Array.isArray(communityRoleData?.role) &&
        communityRoleData?.role?.includes(aclRoles.MANAGER)
      );
    })
  );

  // assemble
  data = {
    ...community,
    isManager,
    subscription,
    membersWithRoles,
  };

  // return
  return data;
};

const getAdminCommunities = async (adminEmail) => {
  try {
    const adminCommunitiesRoles = await CommunityRole.find({
      role: 'admin',
      email: adminEmail,
    });
    const adminCommunities = adminCommunitiesRoles.map((item) => {
      return new ObjectId(item.communityObjectId);
    });
    const result = await Community.aggregate([
      { $match: { _id: { $in: adminCommunities }, isActive: true } },
      {
        $lookup: {
          from: 'community_web3_discount_methods',
          let: { communityCode: '$code' },
          pipeline: [
            {
              $match: {
                $expr: { $eq: ['$communityCode', '$$communityCode'] },
              },
            },
            {
              $project: {
                address: '$contractAddress',
                collectionName: 1,
              },
            },
          ],
          as: 'contractAddressess',
        },
      },
      {
        $lookup: {
          from: 'community_posts',
          let: { communityObjectId: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $in: ['$$communityObjectId', '$communities'],
                },
              },
            },
            {
              $project: {
                announcementObjectId: '$_id',
              },
            },
          ],
          as: 'communityAnnouncements',
        },
      },
      {
        $lookup: {
          from: 'community_events',
          let: { communityObjectId: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $in: ['$$communityObjectId', '$communities'],
                    },
                    { isActive: true },
                  ],
                },
              },
            },
            {
              $project: {
                eventObjectId: '$_id',
                access: 1,
              },
            },
          ],
          as: 'communityEvents',
        },
      },
      {
        $lookup: {
          from: 'community_folders',
          let: { communityObjectId: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ['$communityObjectId', '$$communityObjectId'] },
                    {
                      $eq: ['$status', COMMUNITY_FOLDER_STATUS.PUBLISHED],
                    },
                  ],
                },
              },
            },
            {
              $project: {
                folderObjectId: '$_id',
              },
            },
          ],
          as: 'communityFolders',
        },
      },
      {
        $addFields: {
          hasAnnouncements: {
            $cond: [
              { $gt: [{ $size: '$communityAnnouncements' }, 0] },
              true,
              false,
            ],
          },
          hasEvents: {
            $cond: [
              { $gt: [{ $size: '$communityEvents' }, 0] },
              true,
              false,
            ],
          },
          hasPaidEvents: {
            $cond: [
              {
                $gt: [
                  {
                    $size: {
                      $filter: {
                        input: '$communityEvents',
                        as: 'communityEvent',
                        cond: {
                          $eq: [
                            '$$communityEvent.access',
                            COMMUNITY_EVENT_ACCESS_TYPES.PAID,
                          ],
                        },
                      },
                    },
                  },
                  0,
                ],
              },
              true,
              false,
            ],
          },
          hasFolders: {
            $cond: [
              { $gt: [{ $size: '$communityFolders' }, 0] },
              true,
              false,
            ],
          },
          hasViewedMoneyPage: { $ifNull: ['$hasViewedMoneyPage', false] },
        },
      },
      {
        $unset: [
          'communityAnnouncements',
          'communityEvents',
          'communityFolders',
        ],
      },
    ]);
    return result;
  } catch (err) {
    logger.error(err.message);
    throw err;
  }
};

module.exports = {
  getCommunityBotDetails,
  getCommunityCodesByIds,
  getLearnerCommunities,
  getActiveCommunity,
  getCommunityDataWithSubscriptionAndRole,
  getAdminCommunities,
  addBotToCommunity,
  deleteBotFromCommunity,
  getCommunityById,
  getCommunitiesByIds,
  updateOneCommunity,
  updateCommunityData,
  updateCommunityForPublicApi,
  linkValidation,
  setMonetisationModalViewed,
  setMoneyPageViewed,
  retrieveCommunityProductPrices,
};
