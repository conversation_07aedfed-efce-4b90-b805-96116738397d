const {
  getFolderById,
  getFoldersByCommunityId,
  getFilteredFoldersByCommunityId,
  createOneFolder,
  updateOneFolder,
  patchOneFolder,
  softDeleteFolderById,
  doesCommunityHavePaidFolders,
  autoPurchaseFolder,
  getPublishedFolderCount,
} = require('../common/communityFolders.service');

module.exports = {
  getFolderById,
  getFoldersByCommunityId,
  getFilteredFoldersByCommunityId,
  createOneFolder,
  updateOneFolder,
  patchOneFolder,
  softDeleteFolderById,
  doesCommunityHavePaidFolders,
  autoPurchaseFolder,
  getPublishedFolderCount,
};
