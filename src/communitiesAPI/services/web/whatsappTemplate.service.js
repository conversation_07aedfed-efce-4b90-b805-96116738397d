const ObjectId = require('mongoose').Types.ObjectId;
const WhatsappTemplateModel = require('../../models/whatsappTemplate.model');
const logger = require('../../../services/logger.service');

const getWhatsappTemplates = async (params = {}) => {
  const { name, templateObjectId } = params;

  let query = {};

  try {
    if (templateObjectId) query._id = new ObjectId(templateObjectId);
    if (name) query.name = name;
    logger.info(
      `Fiding whatsapp templates with the following query ${JSON.stringify(
        query
      )}`
    );
    const templates = await WhatsappTemplateModel.find(query)
      .select('_id name components')
      .lean();

    return templates;
  } catch (err) {
    logger.error(err.message);
    throw err;
  }
};

module.exports = {
  getWhatsappTemplates,
};
