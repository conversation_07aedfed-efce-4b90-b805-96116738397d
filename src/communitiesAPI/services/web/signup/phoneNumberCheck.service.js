const ObjectId = require('mongoose').Types.ObjectId;
const httpStatus = require('http-status');
const Community = require('../../../models/community.model');
const WhatsappParticipants = require('../../../models/whatsappParticipants.model');
const User = require('../../../../models/users.model');
const logger = require('../../../../services/logger.service');
const {
  learnerPhoneNumberService,
  learnerSubscriptionService,
} = require('../../common/learner');

const communityPhoneNumberCheck = async (
  phoneNumber,
  communityObjectId
) => {
  if (!phoneNumber || !communityObjectId) {
    const error = new Error(
      'PhoneNumber and communityObjectId need to be specified'
    );
    error.status = httpStatus.BAD_REQUEST;
    throw error;
  }

  try {
    logger.info(`Finding community with ObjectId ${communityObjectId}`);
    const community = await Community.findById(communityObjectId)
      .select('code')
      .lean();

    if (!community) {
      const error = new Error(
        `Community with ObjectId ${communityObjectId} does not exists`
      );
      error.status = httpStatus.BAD_REQUEST;
      throw error;
    }

    const cleanedPhoneNumber =
      learnerPhoneNumberService.getCleanedPhoneNumber(phoneNumber);

    const whatsappGroupMember = await WhatsappParticipants.findOne({
      communityObjectId,
      number: cleanedPhoneNumber.replace('+', ''),
    })
      .select('_id subscriptionObjectId')
      .lean();

    const subscriptionArr =
      await learnerSubscriptionService.getSubscriptionsInCommunityByPhoneNumber(
        cleanedPhoneNumber,
        community.code
      );

    let isActiveUser;
    let subscription;

    if (subscriptionArr && subscriptionArr.length > 0) {
      subscription = subscriptionArr[0];
      const user = await User.findOne({
        $or: [
          { learner: new ObjectId(subscription?.learnerObjectId) },
          { email: subscription.email },
        ],
      })
        .select('_id status')
        .lean();
      if (user?.status === 'ACTIVE') {
        isActiveUser = true;
      } else if (user?.status === 'PENDING') {
        isActiveUser = false;
      }
    }

    if (
      subscription &&
      whatsappGroupMember &&
      subscription?._id !== whatsappGroupMember?.subscriptionObjectId
    ) {
      logger.info(
        `Updating whatsapp participants ${whatsappGroupMember?._id} with subscriptionObjectId ${subscription?._id}`
      );
      const updated = await WhatsappParticipants.findByIdAndUpdate(
        whatsappGroupMember?._id,
        {
          subscriptionObjectId: new ObjectId(subscription?._id),
        },
        {
          new: true,
          upsert: false,
          select: '_id subscriptionObjectId',
        }
      );
      logger.info(
        `Updated whatsapp participants ${whatsappGroupMember?._id} with ${updated}`
      );
    }
    let email = subscription?.email ?? null;

    if (email) {
      const arr = email.split('@');
      email = `${arr[0]?.[0] ?? ''}${arr[0]?.[1] ?? ''}`;
      const length = arr[0].length;
      let minusOff = 4;
      if (length === 2) {
        minusOff = 1;
        email = `${arr[0]?.[0] ?? ''}`;
      } else if (length < 5) {
        minusOff = 2;
      }
      for (let i = 0; i < length - minusOff; i++) {
        email = `${email}*`;
      }
      if (minusOff === 4) {
        email = `${email}*${arr[0]?.charAt(length - 2) ?? ''}${
          arr[0]?.charAt(length - 1) ?? ''
        }`;
      }
      email = `${email}@${arr[1] ?? ''}`;
    }
    return {
      learnerObjectId: subscription?.learnerObjectId ?? null,
      email,
      isSubscriber: !!subscription,
      isActiveUser,
      isWhatsappGroupMember: !!whatsappGroupMember,
      isWhatsappSignupUser: subscription?.isWhatsappSignupUser,
      phoneNumber: cleanedPhoneNumber,
    };
  } catch (error) {
    logger.error(`Community Phone Number Check Failed due to: ${error}`);
    throw error;
  }
};

const updatePhoneNumber = async (phoneNumber, learnerObjectId) => {
  if (!phoneNumber || !learnerObjectId) {
    const error = new Error(
      'PhoneNumber and learnerObjectId need to be specified'
    );
    error.status = httpStatus.BAD_REQUEST;
    throw error;
  }

  try {
    const cleanedPhoneNumber =
      learnerPhoneNumberService.getCleanedPhoneNumber(phoneNumber);

    const learner =
      await learnerPhoneNumberService.updateLearnerPhoneNumber(
        learnerObjectId,
        cleanedPhoneNumber
      );

    return {
      learnerObjectId: learner?._id ?? null,
      email: learner?.email ?? null,
      phoneNumber: learner?.phoneNumber,
    };
  } catch (error) {
    logger.error(`Community Phone Number Check Failed due to: ${error}`);
    throw error;
  }
};

module.exports = { communityPhoneNumberCheck, updatePhoneNumber };
