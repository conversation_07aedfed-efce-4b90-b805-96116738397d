const ObjectId = require('mongoose').Types.ObjectId;
const httpStatus = require('http-status');
const Community = require('../../../models/community.model');
const CommunityRole = require('../../../models/communityRole.model');
const logger = require('../../../../services/logger.service');
const {
  learnerPhoneNumberService,
  learnerSubscriptionService,
} = require('../../common/learner');
const { CHAT_PLATFORMS } = require('../../../../constants/common');

const communityMemberCheck = async (
  userObjectId,
  learnerObjectId,
  communityObjectId
) => {
  if (!learnerObjectId || !communityObjectId) {
    const error = new Error(
      'Both learnerObjectId or communityObjectId need to be specified'
    );
    error.status = httpStatus.BAD_REQUEST;
    throw error;
  }

  try {
    logger.info(`Searching for community with ${communityObjectId}`);
    const community = await Community.findById(communityObjectId)
      .select('code platforms')
      .lean();

    if (!community) {
      const error = new Error(
        `Community with ObjectId ${communityObjectId} does not exists`
      );
      error.status = httpStatus.BAD_REQUEST;
      throw error;
    }

    const subscription =
      await learnerSubscriptionService.getLearnerSubscription(
        community?.code,
        learnerObjectId,
        '_id'
      );

    let whatsappPlatform;
    let chatPlatform;

    let roles = {};
    if (subscription) {
      if (community.platforms) {
        whatsappPlatform = community?.platforms?.filter(
          (platform) =>
            platform?.name === CHAT_PLATFORMS.WHATSAPP.toLocaleLowerCase()
          // TODO: To double confirm what we want to standardise
          // platform?.name === CHAT_PLATFORMS.WHATSAPP
        );
        chatPlatform = community?.platforms;
      }
      const communityRole = await CommunityRole.findOne({
        userObjectId: new ObjectId(userObjectId),
        communityObjectId: new ObjectId(communityObjectId),
      })
        .select('role')
        .lean();
      if (communityRole) {
        roles['member'] = true;
        // eslint-disable-next-line no-unused-expressions
        communityRole?.role?.forEach((role) => {
          roles[role] = true;
        });
      }
    }

    const phoneNumber =
      await learnerPhoneNumberService.getRegisteredPhoneNumberFromLearner(
        learnerObjectId
      );
    return {
      isSubscriber: !!subscription,
      subscriptionObjectId: subscription?._id,
      whatsappInvitationLink: whatsappPlatform?.[0]?.link,
      invitationLink: chatPlatform?.[0]?.link,
      phoneNumber,
      roles,
    };
  } catch (error) {
    logger.error(`Community Member Check Failed due to: ${error}`);
    throw error;
  }
};

module.exports = { communityMemberCheck };
