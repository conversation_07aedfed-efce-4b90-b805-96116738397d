const { DateTime } = require('luxon');

const { getEventRSVPList } = require('../common/eventAttendees.service');
const EventAttendees = require('../../models/eventAttendees.model');
const Event = require('../../models/communityEvents.model');

const logger = require('../../../services/logger.service');
const {
  COMMUNITY_EVENT_ATTENDEE_PURCHASE_TYPES,
} = require('../../constants');
const EventAttendeesModel = require('../../models/eventAttendees.model');

const ObjectId = require('mongoose').Types.ObjectId;

const checkForUpcomingPaidEventsForLearner = async (
  learnerObjectId,
  communityId
) => {
  try {
    const paidEventsAttendees =
      (await EventAttendees.find(
        {
          learnerObjectId,
          purchaseType: COMMUNITY_EVENT_ATTENDEE_PURCHASE_TYPES.PAID,
        },
        'eventObjectId'
      )) || [];
    if (paidEventsAttendees?.length <= 0) {
      logger.info(
        `No Paid Event Attendees found for user ${learnerObjectId} `
      );
      return false;
    }

    for await (const event of paidEventsAttendees) {
      const queryData = {
        _id: new ObjectId(event.eventObjectId),
        communities: { $in: [new ObjectId(communityId)] },
        endTime: { $gt: DateTime.utc() },
      };
      const findEvent = await Event.findOne(queryData);
      if (findEvent) {
        logger.info(
          `Paid Events for ${learnerObjectId} found for community ${communityId} `
        );
        return true;
      }
    }
    logger.info(
      `No Paid Events for ${learnerObjectId} found for community ${communityId} `
    );
    return false;
  } catch (error) {
    logger.error(
      `Unable to get upcoming paid events by learner ${learnerObjectId} due to: `,
      error
    );
    throw error;
    // return false;
  }
};

const checkAttendees = async (attendees, updatedBy) => {
  const checkinOrOutAttendees = attendees.map(async (attendee) => {
    const { _id: attendeeObjectId, isCheckedIn } = attendee;

    const eventAttendee = await EventAttendeesModel.findOne({
      _id: attendeeObjectId,
    });

    const updateFilter = {};

    if (eventAttendee.checkInConfig) {
      updateFilter['checkedInHistory'] = [
        ...eventAttendee.checkedInHistory,
        {
          isCheckedIn: eventAttendee.checkInConfig?.isCheckedIn,
          checkedInAt: eventAttendee.checkInConfig?.checkedInAt,
          updatedDate: new Date(),
        },
      ];
    }

    updateFilter['checkInConfig'] = {
      isCheckedIn,
      checkedInAt: isCheckedIn ? new Date() : null,
      updatedByLearnerObjectId: updatedBy,
    };

    const updatedAttendee = await EventAttendeesModel.findOneAndUpdate(
      {
        _id: attendeeObjectId,
      },
      {
        $set: updateFilter,
      },
      {
        new: true,
      }
    );

    return updatedAttendee;
  });

  const updatedAttendees = await Promise.all(checkinOrOutAttendees);

  return updatedAttendees;
};

module.exports = {
  getEventRSVPList,
  checkForUpcomingPaidEventsForLearner,
  checkAttendees,
};
