/* eslint-disable no-unused-vars */
// models
const ObjectId = require('mongoose').Types.ObjectId;
const Posts = require('../../models/communityPost.model');
const Community = require('../../models/community.model');
const bulkNotificationService = require('../../../services/bulkNotification/bulkNotification.service');
// services
const logger = require('../../../services/logger.service');
const {
  MOBILE_NOTIFICATION_TYPES,
  MOBILE_NOTIFICATION_RECIPIENTS_SCOPES,
  communityEnrolmentStatuses,
  COMMUNITY_SHORT_CODE_LEN,
  MILESTONE_ACTIVITY_TYPES,
  PURCHASE_TYPE,
  ANNOUNCEMENT_LOCALIZATION_KEY,
  ANNOUNCEMENT_WHATSAPP_TEMPLATE_TYPE,
} = require('../../../constants/common');

const {
  getPost,
  getCommunityPosts,
  getCommunityAnnouncementsWithAuthors,
  getCommunityPinnedAnnouncementsWithAuthors,
  getAnnouncementWithAuthor,
} = require('../common/communityPosts.service');

const ActionEventService = require('../../../services/actionEvent');

const {
  sendMobileNotificationToQueue,
} = require('../../../services/notification/mobileNotifications.service');
const { makeUrlShortCode } = require('../../../utils/makeShortCode');
const communityFoldersModel = require('../../models/communityFolders.model');
const CommunityEventsModel = require('../../models/communityEvents.model');
const CommunityPostModel = require('../../models/communityPost.model');

const {
  ResourceNotFoundError,
  ParamError,
  ToUserError,
} = require('../../../utils/error.util');

const ProgramModel = require('../../../models/program/program.model');
const {
  COMMUNITY_POSTS_STATUS,
  COMMUNITY_POSTS_POSTED_BY,
} = require('../../constants');
const { FEED_MAIL_TYPES } = require('../../../services/mail/constants');
const usersModel = require('../../../models/users.model');
const {
  NAS_IO_FRONTEND_URL,
  NAS_IO_APP_DYNAMIC_LINK,
} = require('../../../config');
const { sendEmail } = require('../../../services/notification');
const {
  retrieveCommunityManagerInfo,
} = require('../../../services/communityNotification/email/common.service');
const learnersModel = require('../../../models/learners.model');
const localizationModel = require('../../../models/platform/localization.model');
const { getName } = require('../../../utils/name.util');
const { getPlatformParamsForApp } = require('../../../utils/app.util');
const { buildUrl } = require('../../../utils/url.util');
const fraudService = require('../../../services/fraud');

const BULK_NOTIFICATION_BATCH_SIZE = 500;
const { GENERIC_ERROR } = require('../../../constants/errorCode');
const { PRODUCT_TYPE } = require('../../../services/product/constants');

const generateBulkNotificationPayloadForCreateAnnouncement = async ({
  community,
  post,
  channels,
}) => {
  const existingMemberFilter = {
    $or: [
      { status: communityEnrolmentStatuses.CURRENT },
      {
        $and: [
          { status: communityEnrolmentStatuses.CANCELLED },
          { cancelledAt: { $gte: new Date() } },
        ],
      },
    ],
  };

  const recipientPipeline = [
    {
      $match: {
        communityCode: community.code,
        ...existingMemberFilter,
      },
    },
  ];

  const recipientMatchFilter = {
    communityCode: community.code,
    ...existingMemberFilter,
  };

  const recipientCountPipeline = [
    {
      $match: recipientMatchFilter,
    },
    {
      $count: 'count',
    },
  ];
  const bulkNotificationPayload = {
    channels,
    communityId: community._id,
    entityId: post._id,
    entityCollection: 'community_posts',
    entityMetaData: {
      title: post.title,
    },
    recipientCollection: 'community_subscriptions',
    recipientFilters: {
      recipientPipeline,
      recipientMatchFilter,
      recipientCountPipeline,
      batchSize: BULK_NOTIFICATION_BATCH_SIZE,
    },
  };

  return bulkNotificationPayload;
};

function generateCommentPostLinkForAnnouncements(postInfo, communityInfo) {
  const queryParams = new URLSearchParams();

  const params = {
    link: buildUrl({
      baseUrl: NAS_IO_FRONTEND_URL,
      path: `/mb/communities/${String(communityInfo._id)}/feed/${
        postInfo?._id
      }`,
      searchParams: queryParams,
    }),
    ...getPlatformParamsForApp(),
    ofl: buildUrl({
      baseUrl: NAS_IO_FRONTEND_URL,
      path: `${communityInfo.link}/feed${postInfo.slug}`,
      searchParams: queryParams,
    }),
    ifl: buildUrl({
      baseUrl: NAS_IO_FRONTEND_URL,
      path: `${communityInfo.link}/feed${postInfo.slug}`,
      searchParams: queryParams,
    }),
  };

  return buildUrl({
    baseUrl: NAS_IO_APP_DYNAMIC_LINK,
    path: '/',
    searchParams: new URLSearchParams(params),
  });
}

const sendNotificationToCommunityMembersForNewPostByMemberOrCM = async ({
  community,
  postData,
  isCreator = false,
  isMagicReach = false,
}) => {
  // if its a cm post and if the cm is sending a magic reach then its only gonna send to magic reach
  if (isCreator && !isMagicReach) {
    return;
  }
  // Fetch all required localization data in a single API call
  const localizationData = await localizationModel
    .find({
      key: {
        $in: [
          ANNOUNCEMENT_LOCALIZATION_KEY.ANNOUNCEMENT_ADDED.title,
          ANNOUNCEMENT_LOCALIZATION_KEY.ANNOUNCEMENT_ADDED.body,
        ],
      },
    })
    .lean();

  // Create a map for easier access
  const localizationMap = localizationData.reduce((acc, item) => {
    acc[item.key] = item;
    return acc;
  }, {});

  // Extract title and body directly from the map
  const mobileTitle =
    localizationMap[
      ANNOUNCEMENT_LOCALIZATION_KEY.ANNOUNCEMENT_ADDED.title
    ];
  const mobileBody =
    localizationMap[ANNOUNCEMENT_LOCALIZATION_KEY.ANNOUNCEMENT_ADDED.body];

  const { author } = postData;

  const authorUserInformation = await usersModel
    .findOne({
      _id: author,
    })
    .populate('learner')
    .lean();

  const { learner: authorLearner } = authorUserInformation;

  const authorName = getName(
    authorLearner.firstName,
    authorLearner.lastName,
    authorUserInformation.email
  );
  const communityCode = community.code;
  const communityId = community._id.toString();
  const postId = postData._id.toString();

  const locales = ['en', 'es-mx', 'pt-br', 'ja']; // Add new locales here
  const locale = locales.reduce((acc, loc) => {
    acc[loc] = {
      title: mobileTitle[loc] ?? mobileTitle.en,
      body: mobileBody[loc] ?? mobileBody.en,
    };
    return acc;
  }, {});

  let channels = [
    {
      type: 'mobile',
      templateId: MOBILE_NOTIFICATION_TYPES.ANNOUNCEMENT_ADDED,
      commonVariables: {
        shared: {
          postTitle: postData.title,
          postUrl: `${NAS_IO_FRONTEND_URL}${community.link}/feed${postData.slug}`,
          communityName: community.title,
          communityUrl: `${NAS_IO_FRONTEND_URL}${community.link}`,
          postLink: generateCommentPostLinkForAnnouncements(
            postData,
            community
          ),
          communityCode,
          communityId,
          postId,
          authorName,
        },
        locale,
      },
    },
    {
      type: 'email',
      templateId:
        FEED_MAIL_TYPES.MEMBER_COMMUNITY_POST_CREATED_ANNOUNCEMENT,
      commonVariables: {
        shared: {
          postTitle: postData.title,
          communityName: community.title,
          communityUrl: `${NAS_IO_FRONTEND_URL}${community.link}`,
          postLink: generateCommentPostLinkForAnnouncements(
            postData,
            community
          ),
          authorName,
          communityCode,
          authorProfileImage: authorLearner.profileImage,
          communityThumbnail:
            community?.thumbnailImgData?.mobileImgData?.src,
        },
      },
    },
    // {
    //   type: 'whatsapp',
    //   templateId:
    //     ANNOUNCEMENT_WHATSAPP_TEMPLATE_TYPE.COMMUNITY_POST_ANNOUNCEMENT,
    //   commonVariables: {
    //     shared: {
    //       postTitle: postData.title,
    //       postUrl: `${NAS_IO_FRONTEND_URL}${community.link}/feed${postData.slug}`,
    //       communityName: community.title,
    //       communityUrl: `${NAS_IO_FRONTEND_URL}${community.link}`,
    //       postLink: `${community.link}/feed${postData.slug}`, // no need to add frontend url here, whatsapp handles that automatically
    //       authorName,
    //       communityCode,
    //     },
    //   },
    // },
  ];
  if (isCreator) {
    // remove email channel for cm
    channels = channels.filter((channel) => channel.type !== 'email');
  }
  const payloadForBulkNotificationToMembers =
    await generateBulkNotificationPayloadForCreateAnnouncement({
      community,
      post: postData,
      channels,
    });

  await bulkNotificationService.createBulkNotificationAndSend(
    payloadForBulkNotificationToMembers
  );
};

const generateFeedEmailPayloads = async (postData, community) => {
  const authorUserInformation = await usersModel
    .findOne({ _id: postData.author })
    .populate('learner')
    .lean();
  const { learner } = authorUserInformation;
  let authorsName = '';
  if (learner?.firstName) {
    if (learner.lastName) {
      authorsName = `${learner.firstName} ${learner.lastName}`;
    } else {
      authorsName = learner.firstName;
    }
  } else {
    authorsName = learner?.email?.split('@')[0] || '';
  }
  const payload = {
    communityCode: community.code,
    communityName: community.title,
    communityThumbnail:
      community.thumbnailImgData?.mobileImgData?.src ??
      'https://d2oi1rqwb0pj00.cloudfront.net/na-website/community-product-page/nas-io-homepage/NA+logo.jpeg',
    authorName: authorsName,
    authorEmail: authorUserInformation.email,
    authorProfileImage: authorUserInformation.profileImage,
    postTitle: postData.title,
    postLink: `${NAS_IO_FRONTEND_URL}${community.link}/feed${postData.slug}`,
    authorlearnerId: learner.learnerId,
    authorLanguagePreference: learner.languagePreference || 'en',
    authorPhoneNumber: learner.phoneNumber,
  };

  //TODO: add app link here

  return payload;
};

const getWhatsappBodyBasedOnType = ({
  type,
  community,
  postData,
  authorInfo,
}) => {
  let whatsappData = {
    body: [authorInfo.authorName, postData.title, community.title],
    buttons: [[`${community.link}/feed${postData.slug}`]],
    authorLanguagePreference: 'en',
  };
  switch (type) {
    case ANNOUNCEMENT_WHATSAPP_TEMPLATE_TYPE.MEMBER_COMMUNITY_POST_APPROVED: {
      whatsappData = {
        body: [authorInfo.authorName, postData.title, community.title],
        buttons: [[`${community.link}/feed${postData.slug}`]],
        authorLanguagePreference: authorInfo.languagePreference,
      };
      break;
    }
    case ANNOUNCEMENT_WHATSAPP_TEMPLATE_TYPE.MEMBER_COMMUNITY_POST_REJECTED: {
      whatsappData = {
        body: [authorInfo.authorName, postData.title],
        buttons: [[`${community.link}/feed`]],
        authorLanguagePreference: authorInfo.languagePreference,
      };
      break;
    }
    default:
      return {};
  }
  return whatsappData;
};

const getLocalizedMobileNotificationTitleAndBody = async (
  templateId,
  languagePreference
) => {
  let titleObj = {};
  let bodyObj = {};
  let titleKey = '';
  let bodyKey = '';
  switch (templateId) {
    case MOBILE_NOTIFICATION_TYPES.CM_ANNOUNCEMENT_PENDING_NOTIFY: {
      titleKey =
        ANNOUNCEMENT_LOCALIZATION_KEY.CM_ANNOUNCEMENT_PENDING_NOTIFY.title;
      bodyKey =
        ANNOUNCEMENT_LOCALIZATION_KEY.CM_ANNOUNCEMENT_PENDING_NOTIFY.body;
      break;
    }
    case MOBILE_NOTIFICATION_TYPES.MEMBER_POST_APPROVED_NOTIFY: {
      titleKey =
        ANNOUNCEMENT_LOCALIZATION_KEY.MEMBER_POST_APPROVED_NOTIFY.title;
      bodyKey =
        ANNOUNCEMENT_LOCALIZATION_KEY.MEMBER_POST_APPROVED_NOTIFY.body;
      break;
    }
    case MOBILE_NOTIFICATION_TYPES.MEMBER_POST_REJECTED_NOTIFY: {
      titleKey =
        ANNOUNCEMENT_LOCALIZATION_KEY.MEMBER_POST_REJECTED_NOTIFY.title;
      bodyKey =
        ANNOUNCEMENT_LOCALIZATION_KEY.MEMBER_POST_REJECTED_NOTIFY.body;
      break;
    }
    default:
      return {};
  }

  titleObj = await localizationModel.findOne({ key: titleKey }).lean();
  bodyObj = await localizationModel.findOne({ key: bodyKey }).lean();
  return {
    title: titleObj[languagePreference],
    body: bodyObj[languagePreference],
  };
};

const sendNotificationToMemberForFeedStatus = async ({
  mailType,
  mobileNotificationType,
  whatsappTemplateType,
  community,
  postData,
}) => {
  const feedEmailPayload = await generateFeedEmailPayloads(
    postData,
    community
  );
  const { authorName, authorEmail, authorPhoneNumber } = feedEmailPayload;
  const mailReqBody = {
    mailType,
    mailCourse: community.code,
    mailCourseOffer: 'All',
    toMail: [authorEmail],
    toMailName: [authorName],
    data: feedEmailPayload,
    requesterServiceName: 'Learning Portal Backend',
  };
  await sendEmail(mailReqBody);

  const authorLearnerId = feedEmailPayload.authorlearnerId;
  delete feedEmailPayload.authorlearnerId;
  const { title, body } = await getLocalizedMobileNotificationTitleAndBody(
    mobileNotificationType,
    feedEmailPayload.authorLanguagePreference
  );

  await sendMobileNotificationToQueue(
    mobileNotificationType,
    [authorLearnerId],
    MOBILE_NOTIFICATION_RECIPIENTS_SCOPES.GLOBAL,
    { ...feedEmailPayload, title, body }
  );

  if (authorPhoneNumber) {
    const whatsappData = getWhatsappBodyBasedOnType({
      authorInfo: {
        authorName,
      },
      type: whatsappTemplateType,
      community,
      postData,
    });

    const whatsappMessageBody = {
      phoneNumber: authorPhoneNumber,
      templateType: whatsappTemplateType,
      data: whatsappData,
    };

    const messageGroupId = `${whatsappTemplateType}-${community.code}-${authorPhoneNumber}`;

    try {
      // await sendMessageToSQSQueue({
      //   queueUrl: WHATSAPP_SERVICE_SQS_QUEUE_URL,
      //   messageBody: whatsappMessageBody,
      //   messageGroupId,
      //   isFifo: true,
      // });
    } catch (error) {
      logger.error(
        `sendNotificationToMemberForFeedStatus: error: ${
          error.response?.data ?? error
        }`
      );
    }
  }
};

const sendNotificationToCMForPendingPosts = async ({
  community,
  postData,
}) => {
  const communityManagersMailInfo = await retrieveCommunityManagerInfo(
    community.code,
    FEED_MAIL_TYPES.CM_COMMUNITY_POST_PENDING_APPROVAL_ANNOUNCEMENT
  );

  const { emails, names } = communityManagersMailInfo;

  const feedNotificationPayload = await generateFeedEmailPayloads(
    postData,
    community
  );

  delete feedNotificationPayload.authorlearnerId;

  await Promise.all(
    emails.map((managerEmail, index) => {
      const mailReqBody = {
        mailType:
          FEED_MAIL_TYPES.CM_COMMUNITY_POST_PENDING_APPROVAL_ANNOUNCEMENT,
        mailCourse: community.code,
        mailCourseOffer: 'All',
        toMail: [managerEmail],
        toMailName: [names[index]],
        data: { ...feedNotificationPayload, managerName: names[index] },
        requesterServiceName: 'Learning Portal Backend',
      };
      return sendEmail(mailReqBody);
    })
  );

  const learnerIds = await learnersModel
    .find({ email: { $in: emails } })
    .select('learnerId languagePreference')
    .lean();

  await Promise.all(
    learnerIds.map(async ({ learnerId, languagePreference = 'en' }) => {
      const { title, body } =
        await getLocalizedMobileNotificationTitleAndBody(
          MOBILE_NOTIFICATION_TYPES.CM_ANNOUNCEMENT_PENDING_NOTIFY,
          languagePreference
        );
      await sendMobileNotificationToQueue(
        MOBILE_NOTIFICATION_TYPES.CM_ANNOUNCEMENT_PENDING_NOTIFY,
        [learnerId],
        MOBILE_NOTIFICATION_RECIPIENTS_SCOPES.GLOBAL,
        { ...feedNotificationPayload, title, body }
      );
    })
  );

  return true;
};
/**
 * V2 get community posts for a give query -
 *
 * @param {Object} params
 * @returns {Array}
 */
const getCommunityPostsV2 = async (params = {}) => {
  try {
    const result = await Posts.find(params).sort({
      lastModifiedTimestamp: -1,
      createdAt: -1,
    });
    if (!result) {
      throw new ResourceNotFoundError('No community posts found');
    }
    return result;
  } catch (err) {
    logger.error('getCommunityPostsV2 error', err.message, err.stack);
    throw err;
  }
};

const findResourcesPostsOrEventsWithSlugForPosts = async (
  slug,
  communityId
) => {
  try {
    // check if there is a folder with the same slug
    const folder = await communityFoldersModel.findOne({
      resourceSlug: slug,
      communityObjectId: communityId,
    });

    if (folder) {
      return true;
    }

    // check if there is an event with the same slug
    const event = await CommunityEventsModel.findOne({
      slug,
      communities: [communityId],
    });

    if (event) {
      return true;
    }

    // check if there is a post with the same slug
    const post = await CommunityPostModel.findOne({
      communities: [communityId],
      slug,
    });

    if (post) {
      return true;
    }
    return false;
  } catch (error) {
    logger.error(
      'error in finding resource, posts or events with slug',
      error,
      error.stack
    );
    throw new Error(error);
  }
};
const generateSlugForCommunityResources = async (
  communityLink,
  communityId
) => {
  try {
    // generate the a random 4 letter url
    const urlShortCodeRand = makeUrlShortCode(COMMUNITY_SHORT_CODE_LEN);
    // check if there is any event, folder or resource with the same url
    const isThereASameSlugAlready =
      await findResourcesPostsOrEventsWithSlugForPosts(
        urlShortCodeRand,
        communityId
      );
    // if there is then we will call the function again (recursive attack)
    if (isThereASameSlugAlready) {
      return generateSlugForCommunityResources(communityLink, communityId);
    }
    // if there is no same things then we will return the slug
    return urlShortCodeRand;
  } catch (error) {
    logger.error(
      'Error generating slug for Community Resources (Events, Folders and Posts)',
      error,
      error.stack
    );
  }
};

/**
 * Gets the announcements for a given author and given community.
 *
 * @param {String} author
 * @param {String} communityId
 * @returns {Array}
 */
const getAnnouncementPosts = async (communityId) => {
  try {
    const query = {
      communities: { $in: [communityId] },
      isAnnouncement: true,
    };
    const announcements = await getCommunityPostsV2(query);
    return announcements;
  } catch (err) {
    logger.error('getAnnouncementPosts error', err.message, err.stack);
    throw new Error(
      'Error occursed while trying to get community anouncements',
      communityId
    );
  }
};

/**
 * Gets the pinned announcements for a given author and given community.
 *
 * @param {String} author
 * @param {String} communityId
 * @returns {Array}
 */
const getPinnedAnnouncementPosts = async (communityId) => {
  try {
    const query = {
      communities: { $in: [communityId] },
      isAnnouncement: true,
      isPinned: true,
    };
    const announcements = await getCommunityPostsV2(query);
    return announcements;
  } catch (err) {
    logger.error(
      'getPinnedAnnouncementPosts error',
      err.message,
      err.stack
    );
    throw new Error(
      'Error occursed while trying to get community anouncements',
      communityId
    );
  }
};

async function checkPostForFraud({ community, updatedData, postId }) {
  const contentList = [];
  const contentSourceList = [];
  if (updatedData.title) {
    contentList.push(updatedData.title);
    contentSourceList.push('title');
  }
  if (updatedData.content) {
    contentList.push(
      fraudService.extractLexicalTextForFraud(updatedData.content.root)
    );
    contentSourceList.push('content');
  }

  if (contentList.length > 0) {
    const fraudEngine = new fraudService.FraudEngine({
      communityId: community._id,
      eventName: fraudService.INTERESTED_EVENTS.UPDATE_CONTENT,
      entityType: 'post',
      entityId: postId,
      data: {
        content: contentList.join(', '),
        contentSource: contentSourceList.join(' & '),
      },
      checksToPerform: [fraudService.COMMON_FRAUD_CHECKS.FREE_INPUT],
      autoConsequencesToApply: [
        fraudService.COMMON_CONSEQUENCES.RESTRICT_CHECKOUT,
        fraudService.COMMON_CONSEQUENCES.RESTRICT_CUSTOM_EMAIL,
        fraudService.COMMON_CONSEQUENCES.RESTRICT_MAGIC_REACH,
        fraudService.COMMON_CONSEQUENCES.NOT_INDEXABLE,
        fraudService.COMMON_CONSEQUENCES.RECOMMEND_DEACTIVATE,
      ],
    });
    try {
      const fraudResult = await fraudEngine.performCheck();
      return fraudResult.overallOutcome === fraudService.OUTCOMES.DETECTED;
    } catch (error) {
      logger.error('Error in fraud check:', error.message, error.stack);
    }
    return false;
  }
}

/**
 * Creates post for given data
 *
 * @param {Object} params
 * @returns {Object}
 */
const createPost = async (data = {}) => {
  try {
    const document = await Posts.create(data);
    return document;
  } catch (err) {
    logger.error('createPost error', err.message, err.stack);
    throw new Error(`Error occursed while creating a post: ${err}`);
  }
};

/**
 * Updates an existing post
 *
 * @param {Object} params
 * @param {String} id
 * @returns {Object}
 */
const updatePost = async (id, data = {}, learnerObjectId = null) => {
  try {
    let reviewData = {};

    let updateQuery = {
      ...data,
    };
    let community;
    if (data.communityId) {
      community = await Community.findById(data.communityId).lean();
    }

    if (data.status && data.status !== COMMUNITY_POSTS_STATUS.PENDING) {
      reviewData = {
        reviewedDate: new Date(),
        reviewedByLearnerObjectId: learnerObjectId,
        reviewedStatus: data.status,
      };

      if (data.status === COMMUNITY_POSTS_STATUS.REJECTED) {
        reviewData.rejectedReason = data.rejectedReason ?? '';
      }

      updateQuery = {
        ...updateQuery,
        $push: {
          reviewData,
        },
      };
    }

    const updatedDocument = await Posts.findOneAndUpdate(
      { _id: id },
      {
        ...updateQuery,
      },
      { new: true }
    );

    const isFraudDetected = await checkPostForFraud({
      community,
      updatedData: data,
      postId: id,
    });

    // if there was data.status then that means the post has been either rejected or has been approved if it is not in a pending state
    if (data.status && data.status !== COMMUNITY_POSTS_STATUS.PENDING) {
      if (data.status === COMMUNITY_POSTS_STATUS.APPROVED) {
        await sendNotificationToMemberForFeedStatus({
          mailType:
            FEED_MAIL_TYPES.MEMBER_COMMUNITY_POST_APPROVED_ANNOUNCEMENT,
          mobileNotificationType:
            MOBILE_NOTIFICATION_TYPES.MEMBER_POST_APPROVED_NOTIFY,
          whatsappTemplateType:
            ANNOUNCEMENT_WHATSAPP_TEMPLATE_TYPE.MEMBER_COMMUNITY_POST_APPROVED,
          community,
          postData: updatedDocument,
        });
        if (!isFraudDetected) {
          await sendNotificationToCommunityMembersForNewPostByMemberOrCM({
            community,
            postData: updatedDocument,
            isCreator: data.postedBy === COMMUNITY_POSTS_POSTED_BY.CREATOR,
            isMagicReach: false,
          });
        }
      }

      if (data.status === COMMUNITY_POSTS_STATUS.REJECTED) {
        await sendNotificationToMemberForFeedStatus({
          mailType:
            FEED_MAIL_TYPES.MEMBER_COMMUNITY_POST_REJECTED_ANNOUNCEMENT,
          mobileNotificationType:
            MOBILE_NOTIFICATION_TYPES.MEMBER_POST_REJECTED_NOTIFY,
          whatsappTemplateType:
            ANNOUNCEMENT_WHATSAPP_TEMPLATE_TYPE.MEMBER_COMMUNITY_POST_REJECTED,
          community,
          postData: updatedDocument,
        });
      }
    }
    return updatedDocument;
  } catch (err) {
    logger.error(err.message);
    throw err;
  }
};

/**
 * Deltes an existing post
 *
 * @param {String} id
 * @returns {Object}
 */
const deletePost = async (id, deleter) => {
  try {
    const result = await Posts.deleteOne(
      { _id: id },
      { _deletion: { _deleter: deleter } }
    );
    return result;
  } catch (error) {
    logger.error(
      'Error deleting community announcement',
      error,
      error.stack
    );
    throw new Error(`Error deleting community announcement: ${error}`);
  }
};

/**
 * Creates a post of type announcement for given data, and sends mobile notification to commmunity members
 *
 * @param {String} data
 * @returns {Object}
 */
const createAnnouncementPost = async (data = {}, learnerObjectId) => {
  try {
    // Create post
    const postData = { isAnnouncement: true, ...data };

    const communityId = postData.communities?.[0];
    let community;
    if (communityId) {
      community = await Community.findById(communityId).lean();
    }

    const slug = await generateSlugForCommunityResources(
      community.link,
      communityId
    );
    postData.slug = `/${slug}`;

    const response = await createPost(postData);
    const isFraudDetected = await checkPostForFraud({
      community,
      updatedData: data,
      postId: response._id,
    });

    // notify all the members if a post has been created by a member
    if (
      data.status === COMMUNITY_POSTS_STATUS.APPROVED &&
      !isFraudDetected
    ) {
      await sendNotificationToCommunityMembersForNewPostByMemberOrCM({
        community,
        postData: response,
        isCreator: data.postedBy === COMMUNITY_POSTS_POSTED_BY.CREATOR,
        isMagicReach: data.notifyAll,
      });
    }

    if (
      data.postedBy === COMMUNITY_POSTS_POSTED_BY.MEMBER &&
      data.status === COMMUNITY_POSTS_STATUS.PENDING
    ) {
      await sendNotificationToCMForPendingPosts({
        community,
        postData,
      });
    }

    await ActionEventService.sendMilestoneEvent({
      actionEventType: MILESTONE_ACTIVITY_TYPES.MILESTONE_FIRST_POST,
      communityCode: community?.code,
      communityObjectId: community?._id,
      learnerObjectId,
    });

    // Send notification to community members
    let mobileNotification;

    // if (community) {
    //   if (
    //     !community?.isDemo &&
    //     data.postedBy !== COMMUNITY_POSTS_POSTED_BY.MEMBER
    //   ) {
    //     try {
    //       const subscriptions = await CommunitySubscriptions.find({
    //         communityCode,
    //         status: communityEnrolmentStatuses.CURRENT,
    //       });

    //       const userIds = subscriptions.map(
    //         (subscription) => subscription.learnerId
    //       );
    //       mobileNotification = await sendMobileNotification(
    //         MOBILE_NOTIFICATION_TYPES.ANNOUNCEMENT_ADDED,
    //         userIds,
    //         MOBILE_NOTIFICATION_RECIPIENTS_SCOPES.GLOBAL,
    //         {
    //           communityCode,
    //           title: `New announcement in ${community?.title}!`,
    //           body: response?.title,
    //           name: '',
    //           community_name: community?.title,
    //         }
    //       );
    //     } catch (error) {
    //       // TODO suspecting if userIds too long, it fails
    //       // to take a look later
    //       logger.error(
    //         'Failed to send mobile notification after posting announcement',
    //         `communityId=${communityId}`,
    //         error,
    //         error.stack
    //       );
    //     }
    //   } else {
    //     logger.info('This is a demo community');
    //   }
    // } else {
    //   logger.info(
    //     'No Mobile notification were sent - community Id not found, something went wrong'
    //   );
    // }

    const { mentionedProducts } = response.toObject();

    const populatedMentionedProducts = [];

    if (mentionedProducts && mentionedProducts.length > 0) {
      await Promise.all(
        mentionedProducts.map(async (product) => {
          const { productObjectId, type } = product;
          let productInfo = {};
          switch (type) {
            case PURCHASE_TYPE.FOLDER:
            case PURCHASE_TYPE.SESSION:
            case PRODUCT_TYPE.DIGITAL_FILES:
            case PRODUCT_TYPE.COURSE: {
              productInfo = await communityFoldersModel
                .findOne({
                  _id: new ObjectId(productObjectId),
                })
                .lean();
              break;
            }
            case PURCHASE_TYPE.EVENT: {
              productInfo = await CommunityEventsModel.findOne({
                _id: new ObjectId(productObjectId),
              }).lean();
              break;
            }
            case PURCHASE_TYPE.CHALLENGE: {
              productInfo = await ProgramModel.findOne({
                _id: new ObjectId(productObjectId),
              }).lean();
              break;
            }
            default:
              break;
          }
          populatedMentionedProducts.push({
            type,
            productObjectId: productInfo,
          });
        })
      );
    }

    const responseObject = response.toObject(); // Convert to plain object
    responseObject.mentionedProducts = populatedMentionedProducts;
    return {
      announcement: responseObject,
      mobileNotification,
    };
  } catch (err) {
    logger.error('createAnnouncementPost error', err, err.stack);
    throw new Error('Error occured while creator announcement for author');
  }
};

const createDefaultAnnouncementPost = async (
  community,
  author,
  languagePreference = 'en'
) => {
  try {
    const defaultPost = await Posts.findOne({ isDefault: true }).lean();
    if (!defaultPost) {
      return;
    }

    const {
      title,
      content,
      visibilityType,
      blurImageUrl,
      localizationDetails,
    } = defaultPost;

    const slug = await generateSlugForCommunityResources(
      community.link,
      community._id
    );
    const postData = {
      title,
      content,
      visibilityType,
      blurImageUrl,
      isAnnouncement: true,
      communities: [community._id],
      slug: `/${slug}`,
      author,
      metadata: { isGeneratedByDefault: true },
    };

    if (languagePreference !== 'en') {
      postData.content =
        localizationDetails.content[languagePreference] ?? content;
      postData.title =
        localizationDetails.subject[languagePreference] ?? title;
    }

    const response = await createPost(postData);

    return {
      announcement: response,
    };
  } catch (err) {
    logger.error('createDefaultAnnouncementPost error', err, err.stack);
    throw new Error('Error occured while creator announcement for author');
  }
};

/**
 * Update the announcement post with the reported data
 * @param {ObjectId} postId
 * @param {ObjectId} reportedByLearnerObjectId
 * @param {string} reason
 * @returns {Post}
 */
const reportPost = async ({
  postId,
  reportedByLearnerObjectId,
  reason,
}) => {
  const post = await Posts.findOneAndUpdate(
    {
      _id: new ObjectId(postId),
    },
    {
      $push: {
        'postReported.reportedData': {
          reportedByLearnerObjectId,
          reportedDate: new Date(),
          reason,
        },
      },
    }
  );

  return post;
};

module.exports = {
  getCommunityPosts,
  getPost,
  updatePost,
  deletePost,
  getAnnouncementPosts,
  getPinnedAnnouncementPosts,
  createAnnouncementPost,
  getCommunityAnnouncementsWithAuthors,
  getCommunityPinnedAnnouncementsWithAuthors,
  getAnnouncementWithAuthor,
  createDefaultAnnouncementPost,
  generateSlugForCommunityResources,
  reportPost,
};
