const Community = require('../../models/community.model');
const CommunityVideos = require('../../models/communityVideos.model');
const CommunityResources = require('../../models/communityResources.model');
const Events = require('../../models/communityEvents.model');
const { makeUrlShortCode } = require('../../../utils/makeShortCode');
const axios = require('../../../clients/axios.client');
const logger = require('../../../services/logger.service');
const ObjectId = require('mongoose').Types.ObjectId;
const { COMMUNITY_SHORT_CODE_LEN } = require('../../../constants/common');

const {
  URL_SHORTENER_LINK,
  NAS_IO_FRONTEND_URL,
  FRONTEND_APP_LINK,
} = require('../../../config');
const {
  getFolderItemsByCommunityId,
  updateOneFolderItem,
} = require('../common/communityFolderItems.service');
const { isValidString } = require('../../../utils/string_handling');
const {
  getFoldersByCommunityId,
  updateOneFolder,
  getFolderById,
} = require('./communityFolders.service');

// TODO: Need to adjust link if this function becomes relevant again
const LEARN_PORTAL_BASE_URL = FRONTEND_APP_LINK;
const SHORTURL_AUTH_KEY = process.env.SHORTURL_AUTH_KEY;
const NAS_IO_BASE_URL = NAS_IO_FRONTEND_URL;

const addShortUrl = async (name, code, destination) => {
  try {
    logger.info(
      'Adding ShortUrl with the given params: ',
      name,
      code,
      destination
    );
    const response = await axios({
      method: 'post',
      url: URL_SHORTENER_LINK + '/short-url/shortenUrl',
      headers: {
        'auth-token': SHORTURL_AUTH_KEY,
      },
      data: { name: name, longUrl: destination, urlCode: code },
    });
    return response.data;
  } catch (err) {
    logger.error(
      'Error occured when adding short url:',
      err.message,
      err.stack
    );
    const error = new Error(err.response.data.message);
    error.status = err.response.status;
    throw error;
  }
};

const specificTypeShortUrl = async (communityObjectId, type, typeId) => {
  try {
    let typeData;
    if (type === 'events') {
      typeData = Events;
    }
    if (type === 'resources') {
      typeData = CommunityResources;
    }
    if (type === 'videos') {
      typeData = CommunityVideos;
    }
    const nasioBaseURL = NAS_IO_BASE_URL;
    const learnPortalBaseUrL = LEARN_PORTAL_BASE_URL;
    const longUrl = `${learnPortalBaseUrL}/communities/${communityObjectId}/library?type=${type}&id=${typeId}`;
    const urlShortCode = makeUrlShortCode(COMMUNITY_SHORT_CODE_LEN);
    const titleData = typeData
      .findOne({ _id: typeId }, { title: 1, _id: 0 })
      .lean();
    const shortUrlData = await addShortUrl(
      titleData.title,
      urlShortCode,
      longUrl
    );
    const shortUrlDb = `${nasioBaseURL}/${urlShortCode}`;
    await typeData.findOneAndUpdate(
      { _id: typeId },
      { shortUrl: shortUrlDb }
    );
    return typeData;
  } catch (err) {
    console.log(err.message);
    logger.error(err.message);
    return err;
  }
};

const getVideoShortUrls = async (communityObjectId, communityCode) => {
  const communityVideoData = await CommunityVideos.find(
    { communityObjectId: new ObjectId(communityObjectId) },
    { _id: 1, video: 1, shortUrl: 1, title: 1 }
  ).lean();
  if (communityVideoData.length !== 0) {
    const communityVideoDataFiltered = communityVideoData.filter(
      (value) => Object.keys(value).length !== 0
    );
    const videoShortUrls = {};
    for (const videoId of communityVideoDataFiltered) {
      // eslint-disable-next-line no-prototype-builtins
      if (videoId.hasOwnProperty('video')) {
        if (
          videoId?.shortUrl &&
          (videoId?.shortUrl !== '' || videoId?.shortUrl !== null)
        ) {
          // console.log('Video short url exists');
          videoShortUrls[videoId.video] = videoId.shortUrl;
        } else {
          const nasioBaseURL = NAS_IO_BASE_URL;
          const learnPortalBaseUrL = LEARN_PORTAL_BASE_URL;
          const longUrl = `${learnPortalBaseUrL}/communities/${communityObjectId}/library?type=videos&id=${videoId._id}`;
          const urlShortCodeRand = makeUrlShortCode(
            COMMUNITY_SHORT_CODE_LEN
          );
          const urlShortCode = `${communityCode}/${urlShortCodeRand}`;
          // eslint-disable-next-line no-await-in-loop
          const shortUrlData = await addShortUrl(
            videoId.title,
            urlShortCode,
            longUrl
          );
          const shortUrlDb = `${nasioBaseURL}/${shortUrlData.urlCode}`;
          // eslint-disable-next-line no-await-in-loop
          await CommunityVideos.findOneAndUpdate(
            { _id: videoId._id },
            { shortUrl: shortUrlDb }
          );
          videoShortUrls[videoId.video] = shortUrlDb;
        }
      }
    }
    return videoShortUrls;
  }
};

const getResourceShortUrls = async (communityObjectId, communityCode) => {
  const communityResourcesData = await CommunityResources.find(
    { communityObjectId: new ObjectId(communityObjectId) },
    { _id: 1, video: 1, shortUrl: 1, title: 1 }
  ).lean();
  if (communityResourcesData.length !== 0) {
    const communityResourcesDataFiltered = communityResourcesData.filter(
      (value) => Object.keys(value).length !== 0
    );
    const resourceShortUrls = {};
    for (const resources of communityResourcesDataFiltered) {
      if (
        resources?.shortUrl &&
        (resources?.shortUrl !== '' || resources?.shortUrl !== null)
      ) {
        // console.log('Resource short url exists');
        resourceShortUrls[resources._id] = resources.shortUrl;
      } else {
        const nasioBaseURL = NAS_IO_BASE_URL;
        const learnPortalBaseUrL = LEARN_PORTAL_BASE_URL;
        const longUrl = `${learnPortalBaseUrL}/communities/${communityObjectId}/library?type=resources&id=${resources._id}`;
        const urlShortCodeRand = makeUrlShortCode(
          COMMUNITY_SHORT_CODE_LEN
        );
        const urlShortCode = `${communityCode}/${urlShortCodeRand}`;
        // eslint-disable-next-line no-await-in-loop
        const shortUrlData = await addShortUrl(
          resources.title,
          urlShortCode,
          longUrl
        );
        const shortUrlDb = `${nasioBaseURL}/${shortUrlData.urlCode}`;
        // eslint-disable-next-line no-await-in-loop
        await CommunityResources.findOneAndUpdate(
          { _id: resources._id },
          { shortUrl: shortUrlDb }
        );
        resourceShortUrls[resources._id] = shortUrlDb;
      }
    }
    return resourceShortUrls;
  }
};

const getEventShortUrls = async (communityObjectId, communityCode) => {
  const communityEventsData = await Events.find({
    communities: { $in: [new ObjectId(communityObjectId)] },
  }).lean();
  if (communityEventsData.length !== 0) {
    const communityEventsDataFiltered = communityEventsData.filter(
      (value) => Object.keys(value).length !== 0
    );
    const eventShortUrls = {};
    for (const events of communityEventsDataFiltered) {
      if (
        events?.shortUrl &&
        (events?.shortUrl !== '' || events?.shortUrl !== null)
      ) {
        // console.log('Event short url exists');
        eventShortUrls[events._id] = events.shortUrl;
      } else {
        const nasioBaseURL = NAS_IO_BASE_URL;
        const learnPortalBaseUrL = LEARN_PORTAL_BASE_URL;
        const longUrl = `${learnPortalBaseUrL}/communities/${communityObjectId}/events?type=events&id=${events._id}`;
        const urlShortCodeRand = makeUrlShortCode(
          COMMUNITY_SHORT_CODE_LEN
        );
        const urlShortCode = `${communityCode}/${urlShortCodeRand}`;
        // eslint-disable-next-line no-await-in-loop
        const shortUrlData = await addShortUrl(
          events.title,
          urlShortCode,
          longUrl
        );
        const shortUrlDb = `${nasioBaseURL}/${shortUrlData.urlCode}`;
        // eslint-disable-next-line no-await-in-loop
        await Events.findOneAndUpdate(
          { _id: events._id },
          { shortUrl: shortUrlDb }
        );
        eventShortUrls[events._id] = shortUrlDb;
      }
    }
    return eventShortUrls;
  }
};
const getAllShortUrl = async (params = {}) => {
  try {
    logger.info('Finding all ShortUrl with the given params');
    const response = await axios.get(URL_SHORTENER_LINK + '/short-url/', {
      headers: {
        'auth-token': SHORTURL_AUTH_KEY,
      },
      params: params,
    });
    return response.data;
  } catch (err) {
    logger.error(
      'Error on axios get request to get all shorturl',
      err,
      err.stack
    );
    throw new Error('Error on axios get request to get all shorturl');
  }
};

const generateShortUrl = async (longUrl, communityCode, name) => {
  try {
    const urlShortCodeRand = makeUrlShortCode(COMMUNITY_SHORT_CODE_LEN);
    const urlShortCode = `${communityCode}/${urlShortCodeRand}`;
    const shortUrlData = await addShortUrl(name, urlShortCode, longUrl);
    return `${NAS_IO_BASE_URL}/${shortUrlData.urlCode}`;
  } catch (error) {
    logger.info('Error occured when generating short url:', error.message);
    throw error;
  }
};

const getCommunityFolderItemsShortUrl = async (
  communityObjectId,
  communityCode
) => {
  const folderItems = await getFolderItemsByCommunityId(
    communityObjectId,
    {
      status: 'Published',
    }
  );
  if (folderItems.length === 0) return null;

  const promises = folderItems.map(async (itemDoc) => {
    const { _id: id, shortUrl, title, communityFolderObjectId } = itemDoc;
    const folderData = await getFolderById(communityFolderObjectId);
    if (!folderData) {
      return null;
    }
    const { type } = folderData;
    if (isValidString(shortUrl)) return { [id]: shortUrl };

    const longUrl = `${LEARN_PORTAL_BASE_URL}/communities/${communityObjectId}/library?folderType=${type}&folderId=${communityFolderObjectId}&folderItemId=${id}`;
    const shortUrlDb = await generateShortUrl(
      longUrl,
      communityCode,
      title
    );
    const result = await updateOneFolderItem(
      { _id: id },
      { shortUrl: shortUrlDb, longUrl: longUrl }
    );
    return { [id]: shortUrlDb };
  });
  const resolvedPromises = await Promise.allSettled(promises);
  return resolvedPromises.reduce((accumulator, currentObj) => {
    const { status, value } = currentObj;
    if (status !== 'fulfilled') return accumulator;
    if (!value) return accumulator;
    return { ...accumulator, ...value };
  }, {});
};

const getCommunityFolderShortUrl = async (
  communityObjectId,
  communityCode
) => {
  const folders = await getFoldersByCommunityId(communityObjectId);
  if (folders.length === 0) return null;
  const promises = folders.map(async (folder) => {
    const { _id: id, shortUrl, title, type } = folder;
    if (isValidString(shortUrl)) return { [id]: shortUrl };
    logger.info(`${id} folder for ${communityObjectId} = ${folder}`);
    const longUrl = `${LEARN_PORTAL_BASE_URL}/communities/${communityObjectId}/library?folderType=${type}&folderId=${id}`;
    logger.info(
      `communityId: ${communityObjectId} ${id} longUrl = ${longUrl}`
    );
    const shortUrlDb = await generateShortUrl(
      longUrl,
      communityCode,
      title
    );
    logger.info(
      `communityId: ${communityObjectId} ${id} shortUrlDb = ${shortUrlDb}`
    );
    const updatedFolder = await updateOneFolder(
      { _id: id },
      { shortUrl: shortUrlDb, longUrl: longUrl }
    );
    logger.info(
      `communityId: ${communityObjectId} ${id} updatedFolder = ${JSON.stringify(
        updatedFolder
      )}`
    );

    return { [id]: shortUrlDb };
  });
  const resolvedPromises = await Promise.allSettled(promises);
  return resolvedPromises.reduce((accumulator, currentObj) => {
    const { status, value } = currentObj;
    if (status !== 'fulfilled') return accumulator;
    return { ...accumulator, ...value };
  }, {});
};

const specificCommunityShortUrls = async (communityObjectId) => {
  try {
    const activeCommunity = await Community.findOne({
      _id: new ObjectId(communityObjectId),
    }).lean();
    const communityCode =
      activeCommunity.communityShortCode || activeCommunity.code;
    const communityCodeLowerCase = communityCode.toLowerCase();
    /* Video short urls starts */
    const videoShortUrls = await getVideoShortUrls(
      communityObjectId,
      communityCodeLowerCase
    );
    /* Video short urls ends */

    /* Resource short urls starts */
    const resourceShortUrls = await getResourceShortUrls(
      communityObjectId,
      communityCodeLowerCase
    );
    /* Resource short urls ends */

    /* Events short urls ends */
    const eventShortUrls = await getEventShortUrls(
      communityObjectId,
      communityCodeLowerCase
    );
    /* Events short urls ends */

    /* Community folder items short urls starts */
    const folderItemsShortUrls = await getCommunityFolderItemsShortUrl(
      communityObjectId,
      communityCodeLowerCase
    );
    /* Community folder items short urls ends */

    /* Community folder short urls starts */
    const folderShortUrls = await getCommunityFolderShortUrl(
      communityObjectId,
      communityCodeLowerCase
    );
    /* Community folder short urls ends */

    const allShortUrlsList = {};
    if (videoShortUrls) {
      if (allShortUrlsList?.videoUrls) {
        allShortUrlsList.videoUrls = videoShortUrls;
      } else {
        allShortUrlsList['videoUrls'] = videoShortUrls;
      }
    }
    if (resourceShortUrls) {
      if (allShortUrlsList?.resourceUrls) {
        allShortUrlsList.resourceUrls = resourceShortUrls;
      } else {
        allShortUrlsList['resourceUrls'] = resourceShortUrls;
      }
    }

    if (eventShortUrls) {
      if (allShortUrlsList?.eventUrls) {
        allShortUrlsList.eventUrls = eventShortUrls;
      } else {
        allShortUrlsList['eventUrls'] = eventShortUrls;
      }
    }

    if (folderItemsShortUrls) {
      allShortUrlsList.folderItemUrls = {
        ...allShortUrlsList.folderItemUrls,
        ...folderItemsShortUrls,
      };
    }

    if (folderShortUrls) {
      allShortUrlsList.folderUrls = {
        ...allShortUrlsList.folderUrls,
        ...folderShortUrls,
      };
    }
    return allShortUrlsList;
  } catch (err) {
    logger.error(err.message);
  }
};

const allCommunityShortUrls = async () => {
  try {
    const activeCommunities = await Community.find(
      {
        isActive: true,
      },
      { _id: 1, code: 1, communityShortCode: 1 }
    ).lean();
    const allShortUrlsList = [];
    for await (const community of activeCommunities) {
      const communityObjectId = community._id;
      const communityShortUrls = await specificCommunityShortUrls(
        communityObjectId
      );
      allShortUrlsList.push(communityShortUrls);
    }
    // console.log('allShortUrlsList ', allShortUrlsList);
    return allShortUrlsList;
  } catch (err) {
    logger.error(err.message);
    // throw err;
  }
};

module.exports = {
  getAllShortUrl,
  addShortUrl,
  allCommunityShortUrls,
  specificCommunityShortUrls,
  specificTypeShortUrl,
  getEventShortUrls,
};
