const CommunityEventsModel = require('../../models/communityEvents.model');
const communityFoldersModel = require('../../models/communityFolders.model');
const CommunityPostModel = require('../../models/communityPost.model');
const slugUtils = require('../../../utils/slug.util');
const { PAYMENT_PROVIDER } = require('../../../constants/common');

const checkIfResourcesPostsOrEventsWithSlugExists = async (
  slug,
  communityId,
  doNotCheckForType = []
) => {
  slugUtils.validateSlug(slug);

  if (!doNotCheckForType.includes('folder')) {
    const folder = await communityFoldersModel.findOne({
      resourceSlug: slug,
      communityObjectId: communityId,
    });

    if (folder) {
      return true;
    }
  }

  if (!doNotCheckForType.includes('event')) {
    // check if there is an event with the same slug
    const event = await CommunityEventsModel.findOne({
      slug,
      communities: [communityId],
    });

    if (event) {
      return true;
    }
  }

  if (!doNotCheckForType.includes('post')) {
    // check if there is a post with the same slug
    const post = await CommunityPostModel.findOne({
      communities: [communityId],
      slug,
    });

    if (post) {
      return true;
    }
  }
  return false;
};

// To determine stripe product is under which payment provider (stripe/stripe-us/stripe-india)
const getStripeProductProvider = (existingSubscription) => {
  if (!existingSubscription) {
    return;
  }

  if (existingSubscription?.stripeProductProvider) {
    return existingSubscription.stripeProductProvider;
  }

  switch (existingSubscription?.paymentProvider) {
    case PAYMENT_PROVIDER.STRIPE_INDIA:
    case PAYMENT_PROVIDER.RAZORPAY:
      // for stripe-india or razorpay member, the product will only be stripe-india
      return PAYMENT_PROVIDER.STRIPE_INDIA;
    case PAYMENT_PROVIDER.PAYPAL:
      // For paypal resubscription case, we will always return stripe us product
      return PAYMENT_PROVIDER.STRIPE_US;
    default:
      // For all subscription that didnt have stripeProductProvider are all legacy subscription under stripe (sg) account
      return PAYMENT_PROVIDER.STRIPE;
  }
};

const isCommunityIndexable = (communityData) => {
  if (communityData.indexable === false) {
    return false;
  }
  if (communityData.indexable === true) {
    return true;
  }
  const currentDate = new Date();
  const createdAtDate = new Date(communityData?.createdAt);
  const dateDifference = currentDate - createdAtDate;
  const daysDifference = dateDifference / (1000 * 60 * 60 * 24);

  return daysDifference > 3;
};

module.exports = {
  checkIfResourcesPostsOrEventsWithSlugExists,
  getStripeProductProvider,
  isCommunityIndexable,
};
