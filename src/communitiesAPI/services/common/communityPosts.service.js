const ObjectId = require('mongoose').Types.ObjectId;
const Posts = require('../../models/communityPost.model');

// services
const logger = require('../../../services/logger.service');
const regexUtils = require('../../../utils/regex.util');
const { replacePrivateLinksInVideos } = require('../../utils');

//constants
const {
  defaultPostUserParams,
  COMMUNITY_POSTS_VISIBILITY_TYPE_MAP,
  COMMUNITY_POSTS_STATUS,
  COMMUNITY_POSTS_POSTED_BY,
} = require('../../constants');
const {
  REACTION_ENTITY_COLLECTIONS,
  REACTION_STATES,
  communityEnrolmentStatuses,
  PURCHASE_TYPE,
} = require('../../../constants/common');

// utils
const {
  defaultPaginatedResponse,
  getNextAndPrevious,
} = require('../../../utils/pagination.util');
const CommunityModel = require('../../models/community.model');
const productService = require('../../../services/common/communityProducts.service');
const { PRODUCT_TYPE } = require('../../../services/product/constants');

/**
 * gets posts for given community object Id
 *
 * @param {String} communityObjectId
 * @returns {Object}
 */
const getCommunityPosts = async (communityObjectId) => {
  try {
    const result = await Posts.find({
      communities: new ObjectId(communityObjectId),
    });
    result.forEach((item) => {
      // Convert the video links here

      // eslint-disable-next-line no-param-reassign
      item.videoSrc = replacePrivateLinksInVideos(item.videoSrc);
    });
    if (!result) {
      throw new Error('No posts found');
    }
    return result;
  } catch (err) {
    logger.error(err.message, err.stack);
    throw err;
  }
};

/**
 * gets posts populated with author for given community object Id
 *
 * @param {String} communityObjectId
 * @returns {Object}
 */
const getCommunityPostsWithAuthorsByQuery = async (
  queryInfo,
  learnerObjectId = null,
  paginate = 0,
  pageNum = null,
  pageSize = null,
  searchQuery = null
) => {
  const query = queryInfo;
  const isUserPartOfCommunity = query.isUserPartOfCommunity;
  let searchWithEscapedRegexSign;
  delete query.isUserPartOfCommunity;
  const { communities = [] } = query;

  let communityInfo = {};
  if (communities.length > 0) {
    communityInfo = await CommunityModel.findOne({
      _id: communities[0],
    });
  }

  const communityPostAggregationPipeline = [
    {
      $match: query,
    },
    { $sort: { createdAt: -1 } },
  ];

  if (searchQuery) {
    searchWithEscapedRegexSign = regexUtils.escapeRegExp(searchQuery);
  }
  if (searchWithEscapedRegexSign && searchWithEscapedRegexSign !== '') {
    communityPostAggregationPipeline.push({
      $match: {
        title: { $regex: `${searchWithEscapedRegexSign}`, $options: 'i' },
      },
    });
  }

  if (paginate === 1) {
    communityPostAggregationPipeline.push(
      { $skip: pageNum ? (pageNum - 1) * pageSize : 0 },
      { $limit: pageSize }
    );
  }

  communityPostAggregationPipeline.push(
    // get author details
    {
      $lookup: {
        from: 'users',
        let: { author: '$author' },
        pipeline: [
          { $match: { $expr: { $eq: ['$_id', '$$author'] } } },
          {
            $lookup: {
              from: 'learners',
              let: { learnerObjectId: '$learner' },
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $eq: ['$_id', { $toObjectId: '$$learnerObjectId' }],
                    },
                  },
                },
                {
                  $project: {
                    firstName: 1,
                    lastName: 1,
                    profileImage: 1,
                    description: 1,
                    socialMedia: 1,
                    countryId: 1,
                  },
                },
              ],
              as: 'learner',
            },
          },
          {
            $unwind: {
              path: '$learner',
              preserveNullAndEmptyArrays: true,
            },
          },
          {
            $project: {
              learner_role: 1,
              firstName: {
                $ifNull: [
                  '$learner.firstName',
                  defaultPostUserParams.firstName,
                ],
              },
              lastName: {
                $ifNull: ['$learner.lastName', ''],
              },
              profileImage: {
                $ifNull: [
                  '$learner.profileImage',
                  defaultPostUserParams.profileImage,
                ],
              },
              description: '$learner.description',
              socialMedia: '$learner.socialMedia',
              learnerObjectId: '$learner._id',
              countryId: '$learner.countryId',
            },
          },
        ],
        as: 'author',
      },
    },
    {
      $unwind: {
        path: '$author',
        preserveNullAndEmptyArrays: true,
      },
    },
    // get info whether loggedin user has reacted to this announcement
    {
      $lookup: {
        from: 'reactions',
        let: { learnerObjectId, entityObjectId: '$_id' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  {
                    $eq: [
                      '$learnerObjectId',
                      { $toObjectId: '$$learnerObjectId' },
                    ],
                  },
                  {
                    $eq: [
                      '$entityCollection',
                      REACTION_ENTITY_COLLECTIONS.COMMUNITY_POST,
                    ],
                  },
                  {
                    $eq: ['$state', REACTION_STATES.REACTED],
                  },
                  {
                    $eq: [
                      '$entityObjectId',
                      { $toObjectId: '$$entityObjectId' },
                    ],
                  },
                ],
              },
            },
          },
          {
            $project: {
              type: 1,
            },
          },
        ],
        as: 'currentUserReactionData',
      },
    },
    {
      $unwind: {
        path: '$currentUserReactionData',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: 'community_magic_reach_email',
        localField: '_id',
        foreignField: 'sentResults.AnnouncementV2.announcementObjectId',
        as: 'emailSentData',
      },
    },
    {
      $unwind: {
        path: '$emailSentData',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $addFields: {
        totalImpressions: {
          $add: [
            { $ifNull: ['$impressions', 0] },
            {
              $ifNull: [
                '$emailSentData.analyticsData.Email.totalOpens',
                0,
              ],
            },
          ],
        },
      },
    },
    {
      $lookup: {
        from: 'community_subscriptions',
        let: {
          communityCode: communityInfo.code,
          learnerObjectId: '$author.learnerObjectId',
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ['$communityCode', '$$communityCode'] },
                  { $eq: ['$learnerObjectId', '$$learnerObjectId'] },
                  {
                    $or: [
                      {
                        $eq: [
                          '$status',
                          communityEnrolmentStatuses.CURRENT,
                        ],
                      },
                      {
                        $and: [
                          {
                            $eq: [
                              '$status',
                              communityEnrolmentStatuses.CANCELLED,
                            ],
                          },
                          { $gte: ['$cancelledAt', new Date()] },
                        ],
                      },
                    ],
                  },
                ],
              },
            },
          },
        ],
        as: 'author.subscriptionInfo',
      },
    },
    {
      $addFields: {
        postedBy: {
          $ifNull: ['$postedBy', COMMUNITY_POSTS_POSTED_BY.CREATOR],
        },
      },
    }
  );

  const posts = await Posts.aggregate(communityPostAggregationPipeline);

  const subscriptionObjectIds = [];
  const eventObjectIds = [];
  const productObjectIds = [];
  const programObjectIds = [];

  posts.forEach((post) => {
    const mentionedProducts = post.mentionedProducts ?? [];
    mentionedProducts.forEach((product) => {
      switch (product.type) {
        case PURCHASE_TYPE.SUBSCRIPTION:
          subscriptionObjectIds.push(product.productObjectId);
          break;
        case PURCHASE_TYPE.CHALLENGE:
          programObjectIds.push(product.productObjectId);
          break;
        case PURCHASE_TYPE.EVENT:
          eventObjectIds.push(product.productObjectId);
          break;
        case PURCHASE_TYPE.SESSION:
        case PURCHASE_TYPE.FOLDER:
        case PRODUCT_TYPE.DIGITAL_FILES:
        case PRODUCT_TYPE.COURSE:
          productObjectIds.push(product.productObjectId);
          break;
        default:
          break;
      }
    });
  });

  const existingProductCache = await productService.retrieveEntitiesCache({
    subscriptionObjectIds,
    eventObjectIds,
    productObjectIds,
    programObjectIds,
    source: productService.SOURCE_TYPES.MAGIC_REACH,
  });

  let result = posts.map((post) => {
    const mentionedProducts = post.mentionedProducts ?? [];
    mentionedProducts.forEach((product, index) => {
      const { type, productObjectId } = product;
      const key = `${type}-${productObjectId}`;
      const productInfo = existingProductCache.get(key);
      mentionedProducts[index].productInfo = productInfo ?? {};
    });
    return {
      ...post,
      mentionedProducts,
    };
  });

  if (paginate === 1) {
    if (searchWithEscapedRegexSign && searchWithEscapedRegexSign !== '') {
      query.title = {
        $regex: `^${searchWithEscapedRegexSign}`,
        $options: 'i',
      };
    }

    const totalDocuments = await Posts.countDocuments(query);
    result = [
      {
        data: result,
        metadata: {
          total: totalDocuments,
        },
      },
    ];
  }
  if (paginate === 1) {
    result = result?.[0] || null;
    if (!result) {
      result = defaultPaginatedResponse;
    }
    const { next = null, previous = null } = getNextAndPrevious(
      pageNum,
      pageSize,
      result?.metadata?.total
    );
    result.metadata.next = next;
    result.metadata.previous = previous;
  }

  const isPaginated = result.data;

  let resultsArray = [];
  if (isPaginated) {
    resultsArray = result.data;
  } else {
    resultsArray = result;
  }

  const updateResultInfo = await resultsArray.map((communityPost) => {
    // if the communities visibility doesn't exist OR communityPost.visibilityType is Members AND user is not part of the community then we hide the fields
    if (
      (!communityPost?.visibilityType ||
        communityPost?.visibilityType ===
          COMMUNITY_POSTS_VISIBILITY_TYPE_MAP.MEMBERS) &&
      !isUserPartOfCommunity
    ) {
      const { content, ...rest } = communityPost;

      return { ...rest, isLocked: true };
    }

    return communityPost;
  });

  const updatedResult = await Promise.all(updateResultInfo);

  if (isPaginated) {
    result.data = updatedResult;
  } else {
    result = updatedResult;
  }
  return result;
};

/**
 * gets posts populated with author for given community object Id
 *
 * @param {String} communityObjectId
 * @returns {Object}
 */
const getCommunityPostsWithAuthors = async (communityObjectId) => {
  const query = {
    communities: [new ObjectId(communityObjectId)],
  };

  const result = await getCommunityPostsWithAuthorsByQuery(query);

  return result;
};

const getUserCommunityAnnouncementsPosts = async (
  communityObjectId,
  user,
  paginate,
  pageNum,
  pageSize,
  status = COMMUNITY_POSTS_STATUS.APPROVED
) => {
  const statusArray = status.split(',');
  const query = {
    communities: [new ObjectId(communityObjectId)],
    isPinned: false,
    author: new ObjectId(user._id),
    isUserPartOfCommunity: true,
  };

  if (statusArray.includes(COMMUNITY_POSTS_STATUS.APPROVED)) {
    query.$or = [
      { status: { $exists: false } },
      { status: { $in: statusArray } },
    ];
  } else {
    query.status = { $in: statusArray };
  }

  const result = await getCommunityPostsWithAuthorsByQuery(
    query,
    user.learner._id,
    paginate,
    pageNum,
    pageSize
  );

  return result;
};

/**
 * gets posts populated with author for given community object Id that is NOT pinned
 *
 * @param {String} communityObjectId
 * @returns {Object}
 */
const getCommunityAnnouncementsWithAuthors = async (
  communityObjectId,
  learnerObjectId,
  paginate,
  pageNum,
  pageSize,
  isUserPartOfCommunity = true,
  searchQuery,
  status = COMMUNITY_POSTS_STATUS.APPROVED,
  postedBy = null
) => {
  const query = {
    communities: [new ObjectId(communityObjectId)],
    isPinned: false,
    isAnnouncement: true,
  };

  if (status === COMMUNITY_POSTS_STATUS.APPROVED) {
    query.$or = [
      { status: COMMUNITY_POSTS_STATUS.APPROVED },
      { status: { $exists: false } },
    ];
  } else {
    query.status = status; // Directly match if other statuses need exact matches
  }

  if (postedBy === COMMUNITY_POSTS_POSTED_BY.CREATOR) {
    query.$or = [
      { postedBy: COMMUNITY_POSTS_POSTED_BY.CREATOR },
      { postedBy: { $exists: false } },
    ];
  } else if (postedBy === COMMUNITY_POSTS_POSTED_BY.MEMBER) {
    query.postedBy = COMMUNITY_POSTS_POSTED_BY.MEMBER;
  }

  query['isUserPartOfCommunity'] = isUserPartOfCommunity;

  const result = await getCommunityPostsWithAuthorsByQuery(
    query,
    learnerObjectId,
    paginate,
    pageNum,
    pageSize,
    searchQuery
  );

  return result;
};

/**
 * gets Pinned announcements populated with author for given community object Id
 *
 * @param {String} communityObjectId
 * @returns {Object}
 */
const getCommunityPinnedAnnouncementsWithAuthors = async (
  communityObjectId,
  learnerObjectId,
  paginate,
  pageNum,
  pageSize,
  isUserPartOfCommunity = true,
  searchQuery,
  status = COMMUNITY_POSTS_STATUS.APPROVED,
  postedBy = null
) => {
  const query = {
    communities: [new ObjectId(communityObjectId)],
    isPinned: true,
    isAnnouncement: true,
  };

  if (status === COMMUNITY_POSTS_STATUS.APPROVED) {
    query.$or = [
      { status: COMMUNITY_POSTS_STATUS.APPROVED },
      { status: { $exists: false } },
    ];
  } else {
    query.status = status; // Directly match if other statuses need exact matches
  }

  if (postedBy === COMMUNITY_POSTS_POSTED_BY.CREATOR) {
    query.$or = [
      { postedBy: COMMUNITY_POSTS_POSTED_BY.CREATOR },
      { postedBy: { $exists: false } },
    ];
  } else if (postedBy === COMMUNITY_POSTS_POSTED_BY.MEMBER) {
    query.postedBy = COMMUNITY_POSTS_POSTED_BY.MEMBER;
  }

  query['isUserPartOfCommunity'] = isUserPartOfCommunity;

  const result = await getCommunityPostsWithAuthorsByQuery(
    query,
    learnerObjectId,
    paginate,
    pageNum,
    pageSize,
    searchQuery
  );

  return result;
};

/**
 * gets post for given post Id
 *
 * @param {String} postId
 * @returns {Object}
 */
const getPost = async (postId) => {
  try {
    const document = await Posts.findById(postId);
    return document;
  } catch (err) {
    logger.error(err, err.stack);
    throw new Error(`Error occursed while getting a post: ${err}`);
  }
};

/**
 * gets post for given post Id - populated with author
 *
 * @param {String} postId
 * @returns {Object}
 */
const getPostWithAuthor = async (postId) => {
  try {
    const query = { _id: new ObjectId(postId) };
    const result = await getCommunityPostsWithAuthorsByQuery(query);

    if (!result?.length) {
      throw new Error(
        'No matching community post with given post Id found for given todo'
      );
    }
    return result[0];
  } catch (err) {
    logger.error(err, err.stack);
    throw new Error(`Error occursed while getting a post: ${err}`);
  }
};

/**
 * gets announcement for given post Id - populated with author
 *
 * @param {String} postId
 * @returns {Object}
 */
const getAnnouncementWithAuthor = async (
  postId,
  isUserPartOfCommunity = false,
  learnerObjectId = null
) => {
  try {
    const query = {
      _id: new ObjectId(postId),
      isAnnouncement: true,
    };

    query.isUserPartOfCommunity = isUserPartOfCommunity;

    const result = await getCommunityPostsWithAuthorsByQuery(
      query,
      learnerObjectId
    );

    if (!result?.length) {
      throw new Error('no matching community post found ');
    }
    return result[0];
  } catch (err) {
    logger.error(err, err.stack);
    throw new Error(
      'Error occured while getting announcement with author detals'
    );
  }
};

/**
 * gets announcement reaction counts for given announcement Id
 *
 * @param {String} postId
 * @returns {Object}
 */
const getPostReactionCount = async (postId) => {
  try {
    const { reactionCounts = null } = await Posts.findById(
      postId,
      'reactionCounts'
    ).exec();
    return reactionCounts;
  } catch (err) {
    logger.error(err, err.stack);
    throw new Error(`Error occursed while getting a post: ${err}`);
  }
};

module.exports = {
  getPost,
  getPostWithAuthor,
  getCommunityPosts,
  getCommunityPostsWithAuthors,
  getCommunityAnnouncementsWithAuthors,
  getCommunityPinnedAnnouncementsWithAuthors,
  getAnnouncementWithAuthor,
  getPostReactionCount,
  getUserCommunityAnnouncementsPosts,
};
