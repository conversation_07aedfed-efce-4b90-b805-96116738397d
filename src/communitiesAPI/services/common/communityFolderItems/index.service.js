const httpContext = require('express-http-context');
const ObjectId = require('mongoose').Types.ObjectId;

// models
const CommunityFolderItems = require('../../../models/communityFolderItems.model');
const VideoModel = require('../../../../models/videos.model');

// utils
const {
  commonFilters,
  fixFolderItemLinks,
  fixVideoDataLinks,
} = require('../../../../utils/communityFolders/folderItem.util');
const { communityLibraryTypesMap } = require('../../../constants');

const logger = require('../../../../services/logger.service');

exports.getFolderItemById = async (
  communityFolderItemObjectId,
  isCommunityManager = false,
  filter = {}
) => {
  const permissionFilter = isCommunityManager
    ? commonFilters.MANAGER_VIEW
    : commonFilters.NON_MANAGER_VIEW;
  try {
    const filters = {
      _id: new ObjectId(communityFolderItemObjectId),
      ...permissionFilter,
      ...filter,
    };

    let folderItem = await CommunityFolderItems.findOne(filters).lean();

    if (
      folderItem?.type === communityLibraryTypesMap.VIDEO.toLowerCase()
    ) {
      folderItem = fixFolderItemLinks(folderItem);
      if (!isCommunityManager) {
        delete folderItem.mp4Link;
      }

      let video = await VideoModel.findOne({
        _id: folderItem.videoObjectId,
      }).lean();
      if (video?.thumbnailLink) {
        video = fixVideoDataLinks(video);
        folderItem.isDefaultThumbnail =
          video?.thumbnailLink === folderItem?.thumbnail;
      }

      folderItem.video = video;
    }

    return folderItem;
  } catch (error) {
    logger.error(
      `Error getting CommunityFolderItem for communityObjectId given`,
      error,
      error.stack
    );
    throw new Error(
      `Error getting CommunityFolderItem for communityObjectId given`
    );
  }
};

exports.getFolderItemByIdWithVideoInfo = async (
  communityFolderItemObjectId,
  isCommunityManager = false
) => {
  const permissionFilter = isCommunityManager
    ? commonFilters.MANAGER_VIEW
    : commonFilters.NON_MANAGER_VIEW;
  try {
    let folderItem = await CommunityFolderItems.findOne({
      _id: new ObjectId(communityFolderItemObjectId),
      ...permissionFilter,
    })
      .populate('videoObjectId')
      .lean();

    if (folderItem.videoObjectId) {
      folderItem = fixFolderItemLinks(folderItem);

      const video = folderItem.videoObjectId;
      folderItem.video = fixVideoDataLinks(video);
      folderItem.isDefaultThumbnail =
        video?.thumbnailLink === folderItem?.thumbnail;
      delete folderItem.videoObjectId;

      if (folderItem?.mp4Link) {
        const requestPlatform = httpContext.get('requestPlatform');

        // if device is not Android and user is not community manager, then delete mp4 link
        if (!isCommunityManager && requestPlatform !== 'Android') {
          delete folderItem.mp4Link;
        }
      }

      return folderItem;
    }
  } catch (error) {
    logger.error(
      `Error getting CommunityFolderItem for communityObjectId given`,
      communityFolderItemObjectId,
      error,
      error.stack
    );
    throw new Error(
      `Error getting CommunityFolderItem for communityObjectId given`
    );
  }
};

exports.getFolderItemsWithFilter = async (filter = {}) => {
  try {
    // validate filters is object and is not empty
    if (!filter || !Object.keys(filter).length) {
      return [];
    }

    const result = await CommunityFolderItems.find(filter).lean();
    return result;
  } catch (error) {
    logger.error(
      `Error getting CommunityFolderItems for communityObjectId given`,
      error,
      error.stack
    );
    throw new Error(
      `Error getting CommunityFolderItems for communityObjectId given`
    );
  }
};

exports.updateManyFolderItems = async (filter, update, options = {}) => {
  try {
    const updatedData = await CommunityFolderItems.updateMany(
      filter,
      update,
      options
    );
    return updatedData;
  } catch (error) {
    logger.error('Error updating folder items:', error, error.stack);
    throw new Error('Cannot update folder items');
  }
};
