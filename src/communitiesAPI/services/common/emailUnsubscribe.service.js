const logger = require('../../../services/logger.service');
const EmailUnsubscribeModel = require('../../models/emailUnsubscribe.model');

const createEmailUnsubscribers = async (params = {}) => {
  try {
    const document = await EmailUnsubscribeModel.create(params);
    return document;
  } catch (err) {
    logger.error(err.message);
    throw err;
  }
};

const findOneEmailUnsubscriber = async (params = {}) => {
  try {
    const document = await EmailUnsubscribeModel.findOne(params);
    return document;
  } catch (err) {
    logger.error(err.message);
    throw err;
  }
};

const findEmailUnsubscribers = async (params = {}) => {
  try {
    const document = await EmailUnsubscribeModel.find(params);
    return document;
  } catch (err) {
    logger.error(err.message);
    throw err;
  }
};

const findOneandUpdateEmailUnsubscriber = async (
  params = {},
  payload = {}
) => {
  try {
    const document = await EmailUnsubscribeModel.findOneAndUpdate(
      params,
      payload,
      {
        upsert: false,
        new: true,
      }
    );
    return document;
  } catch (err) {
    logger.error(err.message);
    throw err;
  }
};

module.exports = {
  createEmailUnsubscribers,
  findOneEmailUnsubscriber,
  findEmailUnsubscribers,
  findOneandUpdateEmailUnsubscriber,
};
