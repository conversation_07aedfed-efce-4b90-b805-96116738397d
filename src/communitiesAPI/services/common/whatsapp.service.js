const axios = require('../../../clients/axios.client');
const logger = require('../../../services/logger.service');
const status = require('http-status');
const { phone } = require('phone');
const { Readable } = require('stream');
const { DateTime } = require('luxon');
const {
  WHATSAPP_BEARER_TOKEN,
  WHATSAPP_PHONE_ID,
  WHATSAPP_SERVICE_QUEUE_BOT_URL,
  WHATSAPP_BOT_URL,
} = require('../../../config');
const { WHATSAPP_QUEUE_EVENTS, aclRoles } = require('../../constants');
const {
  CHAT_PLATFORMS,
  communityEnrolmentStatuses,
  COMMUNITY_APPLICATION_STATUS,
} = require('../../../constants/common');
const {
  getWhatsappFooterObject,
  FOOTER_TEXT,
} = require('../../utils/whatsappUtils');
const CommunityMagicReachEmailModel = require('../../../models/magicReach/communityMagicReachEmail.model');
const MagicReachNodeWrapper = require('../../../services/magicReach/contentFormatter/MagicReachNodeWrapper');
const ObjectId = require('mongoose').Types.ObjectId;
const WhatsappParticipantsModel = require('../../models/whatsappParticipants.model');
const WhatsappConnectedGroupModel = require('../../models/whatsappConnectedGroup.model');
const CommunityRoleModel = require('../../models/communityRole.model');
const CommunityModel = require('../../models/community.model');
const CommunityApplicationsModel = require('../../models/communityApplications.model');
const CommunityPurchaseTransactionModel = require('../../models/communityPurchaseTransactions.model');
const {
  sendMessageToSQSFifoQueue,
} = require('../../../handlers/sqs.handler');
const {
  whatsappAnalyticsModel,
} = require('../../models/whatsappAnalytics.model');
const usersModel = require('../../../models/users.model');
const { whatsappSession } = require('../../models/whatsappSessions.model');
const LearnersModel = require('../../../models/learners.model');

const MongoDbUtils = require('../../../utils/mongodb.util');
const whatsappConnectedGroupModel = require('../../models/whatsappConnectedGroup.model');

const WHATSAPP_VERSION = 'v15.0';
const WHATSAPP_PHONE_URL = `https://graph.facebook.com/${WHATSAPP_VERSION}`;

const userExportUtils = require('../../../services/membership/utils/export.utils');
const userDisplayUtils = require('../../../services/membership/utils/display.utils');

const {
  getCommunityUIConfigService,
} = require('../../../communitiesAPI/services/web/communityUIConfig.service');

const {
  COMMUNITY_SUBSCRIPTION_STATUSES,
} = require('../../../constants/common');

const sendWhatsappTemplateMessage = async ({
  templateId,
  phoneNumber,
  phoneNumberId,
  ...params
}) => {
  const url = `${WHATSAPP_PHONE_URL}/${
    phoneNumberId ?? WHATSAPP_PHONE_ID
  }/messages`;
  const body = {
    messaging_product: 'whatsapp',
    to: phoneNumber,
    type: 'template',
    template: {
      name: templateId,
      language: {
        code: params?.languageCode ?? 'en_US',
      },
    },
  };
  const response = await axios.post(url, body);
  return response?.data;
};

const sendReplyToMessage = async ({
  messagePayload,
  phoneNumber,
  phoneNumberId,
  ...params
}) => {
  const url = `${WHATSAPP_PHONE_URL}/${
    phoneNumberId ?? WHATSAPP_PHONE_ID
  }/messages`;
  const body = {
    messaging_product: 'whatsapp',
    to: phoneNumber,
    ...messagePayload,
  };
  try {
    const response = await axios.post(url, body, {
      headers: {
        Authorization: `Bearer ${WHATSAPP_BEARER_TOKEN}`,
      },
    });
    return response?.data;
  } catch (err) {
    logger.error(
      'Error occured sending whatsapp reply|',
      `error=${err}|`,
      `trace=${err.stack}`
    );
    return { success: false };
  }
};

const sendWhatsappMagicReachMessage = async ({
  draftId,
  phoneNumber,
  phoneNumberId = null,
}) => {
  try {
    const magicReachDraft = await CommunityMagicReachEmailModel.findOne({
      _id: new ObjectId(draftId),
    });
    if (!magicReachDraft) {
      const error = new Error(`Draft not found for ${draftId}`);
      error.status = status.BAD_REQUEST;
      throw error;
    }
    const content = magicReachDraft.content;
    if (!content) {
      const error = new Error(`Draft content not found for ${draftId}`);
      error.status = status.BAD_REQUEST;
      throw error;
    }
    const rootPayload = {
      ...content?.root,
      title: magicReachDraft?.title,
    };
    const rootWrapper = new MagicReachNodeWrapper(rootPayload);
    const whatsappMessages = rootWrapper.getOptimizedWhatsappMessage();

    const responseData = [];

    // Comment out footer: PM request

    // if (!magicReachDraft.removeFooter) {
    //   const footerObject = getWhatsappFooterObject({
    //     footerText: FOOTER_TEXT,
    //   });
    //   if (footerObject) {
    //     const response = await sendReplyToMessage({
    //       messagePayload: footerObject,
    //       phoneNumber,
    //       phoneNumberId,
    //     });
    //     responseData.push(response);
    //   }
    // }

    for await (const whatsappMessage of whatsappMessages) {
      const response = await sendReplyToMessage({
        messagePayload: whatsappMessage,
        phoneNumber,
        phoneNumberId,
      });
      responseData.push(response);
    }
    logger.info(
      `Sending whatsapp magic Reach message for ${draftId} response: ${JSON.stringify(
        responseData
      )}`
    );
    return responseData;
  } catch (err) {
    logger.error(
      `Error occured sending whatsapp magic Reach message for ${draftId}:`,
      err
    );
    throw err;
  }
};

const retrieveWhatsappCommunityMembersCountNonSubscriber = async (
  communityId
) => {
  const filter = {
    communityObjectId: communityId,
    isInWhatsApp: true,
    subscriptionObjectId: null,
  };

  const membersCount = await WhatsappParticipantsModel.countDocuments(
    filter
  );

  return membersCount;
};

const retrieveWhatsappCommunityMembersCount = async (
  communityId,
  isInWhatsApp = true
) => {
  const membersCount = await WhatsappParticipantsModel.countDocuments({
    communityObjectId: communityId,
    isInWhatsApp,
  });

  return membersCount;
};

const getWhatsappConnectionStatus = async (email) => {
  try {
    const userInfo = await usersModel
      .findOne(
        {
          email,
        },
        { _id: 1 }
      )
      .lean();

    const godModeConnected = await whatsappSession
      .findOne({
        id: userInfo?._id,
        isBot: false,
      })
      .lean();

    const isGodModeConnected = godModeConnected?.status === 'connected';

    return {
      isBotAdmin: true,
      isBotInGroup: true,
      isGodModeConnected,
    };
  } catch (error) {
    return {
      isBotAdmin: false,
      isBotInGroup: false,
      isGodModeConnected: false,
    };
  }
};

const retrieveWhatsappGroupInfo = async (communityId) => {
  const communityData = await CommunityModel.findOne(
    {
      _id: new ObjectId(communityId),
    },
    { bots: 1, createdBy: 1 }
  ).lean();

  if (communityData == null) {
    throw new Error('Invalid community id');
  }

  const bots = communityData.bots;
  const createdBy = communityData.createdBy;

  const foundWhatsapp = bots?.find(
    (bot) => bot?.type === CHAT_PLATFORMS.WHATSAPP
  );

  if (foundWhatsapp?.serverKey == null) {
    throw new Error('No whatsapp group');
  }

  const { serverKey: groupId } = foundWhatsapp;

  const whatsappConnectedGroup = await WhatsappConnectedGroupModel.findOne(
    {
      whatsappGroupId: groupId,
    },
    { name: 1 }
  ).lean();

  const learner = await LearnersModel.findOne(
    { email: createdBy },
    { phoneNumber: 1 }
  ).lean();

  const phoneNumber = learner?.phoneNumber ?? '';

  const { countryCode: phoneCountryCode = '' } = phone(phoneNumber ?? '');

  const displayPhoneNumber =
    phoneNumber.replace(phoneCountryCode, `${phoneCountryCode} `) ?? '';

  const whatsappConnectedData = await getWhatsappConnectionStatus(
    createdBy
  );

  return {
    whatsappGroupName: whatsappConnectedGroup?.name ?? '',
    whatsappGroupNumber: displayPhoneNumber,
    ...whatsappConnectedData,
  };
};

const retrieveWhatsappCommunityMembersCountByCode = async (
  communityCode,
  isInWhatsApp = true
) => {
  const communityData = await CommunityModel.findOne(
    {
      code: communityCode,
    },
    { _id: 1 }
  );

  if (communityData == null) {
    throw new Error('Invalid community code');
  }

  return retrieveWhatsappCommunityMembersCount(
    communityData._id,
    isInWhatsApp
  );
};

const retrieveWhatsappCommunityStatus = async (
  communityId,
  isInWhatsApp = true
) => {
  const [groupInfo, membersCount] = await Promise.all([
    retrieveWhatsappGroupInfo(communityId),
    retrieveWhatsappCommunityMembersCount(communityId, isInWhatsApp),
  ]);

  const result = {
    membersCount,
    ...groupInfo,
  };

  return result;
};

const retrieveWhatsappCommunityCode = async (communityId) => {
  const { code } = await CommunityModel.findOne(
    {
      _id: new ObjectId(communityId),
    },
    { code: 1 }
  ).lean();

  if (code == null) {
    throw new Error('Invalid community id');
  }

  return code;
};

function projectWhatsappCommunityMembersRequiredData() {
  return {
    $project: {
      _id: 0,
      whatsappId: 1,
      isInWhatsApp: 1,
      profilePicUrl: 1,
      communityObjectId: 1,
      subscriptionObjectId: 1,
      countryCodeNumber: 1,
      country: 1,
      number: 1,
      name: 1,
      shortName: 1,
      pushname: 1,
      joinedAt: 1,
      fullName: 1,
      'countryData.country': 1,
      'subscriptionData.email': 1,
      'subscriptionData.status': 1,
      'subscriptionData.createdAt': 1,
      'subscriptionData.updatedAt': 1,
      'subscriptionData.nextBillingDate': 1,
      'subscriptionData.applicationReviewDate': 1,
      'subscriptionData.cancelledAt': 1,
      'subscriptionData.memberType': 1,
      'subscriptionData.paymentProvider': 1,
      'subscriptionData.amount': 1,
      'subscriptionData.unsubscribedAt': 1,
      'learnerData.firstName': 1,
      'learnerData.lastName': 1,
      'learnerData.profileImage': 1,
      'learnerData.description': 1,
      'learnerData.fullName': {
        $concat: ['$learnerData.firstName', ' ', '$learnerData.lastName'],
      },
    },
  };
}

function postProcessWhatsappCommunityMemberData(
  participant,
  communityRolesCache
) {
  const {
    fullName,
    name,
    shortName,
    pushname,
    country,
    countryCodeNumber,
    number,
    learnerData,
    subscriptionData,
    countryData,
    profilePicUrl,
  } = participant;

  const finalFullName =
    learnerData.fullName ??
    fullName ??
    name ??
    shortName ??
    pushname ??
    '';

  const newParticipant = {
    ...participant,
  };

  newParticipant.displayPhoneNumber = `+${number?.replace(
    countryCodeNumber,
    `${countryCodeNumber} `
  )}`;

  newParticipant.fullName = finalFullName;

  newParticipant.isManager =
    communityRolesCache
      .get(subscriptionData?.email ?? '')
      ?.includes(aclRoles.ADMIN) ?? false;

  newParticipant.country =
    countryData?.country ?? subscriptionData?.country ?? country;

  newParticipant.profileImage = learnerData?.profileImage ?? profilePicUrl;

  const now = DateTime.utc();

  newParticipant.displayStatus =
    subscriptionData?.status === communityEnrolmentStatuses.CURRENT ||
    (subscriptionData?.status === communityEnrolmentStatuses.CANCELLED &&
      subscriptionData?.cancelledAt > now)
      ? 'Subscriber'
      : 'Not on Nas.io';

  delete newParticipant.countryData;
  delete newParticipant.learnerData;
  delete newParticipant.name;
  delete newParticipant.shortName;
  delete newParticipant.pushname;
  delete newParticipant.profilePicUrl;

  return newParticipant;
}

function postProcessWhatsappCommunityMemberFullData(
  participant,
  communityRolesCache
) {
  const {
    fullName,
    name,
    shortName,
    pushname,
    country,
    countryCodeNumber,
    number,
    learnerData,
    subscriptionData,
    countryData,
    profilePicUrl,
  } = participant;

  const learnerFullName = [learnerData?.firstName, learnerData?.lastName]
    .filter((value) => value != null)
    .join(' ');

  const finalFullName =
    learnerFullName.length !== 0
      ? learnerFullName
      : fullName ?? name ?? shortName ?? pushname ?? '';

  let newParticipant = {
    ...participant,
    ...participant.subscriptionData,
  };

  newParticipant.displayPhoneNumber = `+${number.replace(
    countryCodeNumber,
    `${countryCodeNumber} `
  )}`;

  newParticipant.fullName = finalFullName;

  newParticipant.isManager =
    communityRolesCache
      .get(subscriptionData?.email ?? '')
      ?.includes(aclRoles.ADMIN) ?? false;

  newParticipant.country =
    countryData?.country ?? subscriptionData?.country ?? country;

  newParticipant.profileImage = learnerData?.profileImage ?? profilePicUrl;

  const now = DateTime.utc();

  const activityCopy = {
    lastReacted: 'Reacted to a message',
    lastMessageSent: 'Sent a message in chat',
    //TODO: add lastRead when it is available
  };

  const activity = {
    lastReacted: newParticipant.lastReacted ?? null,
    lastMessageSent: newParticipant.lastMessageSent ?? null,
    //TODO: add lastRead when it is available
  };

  const sortedActivity = Object.entries(activity)
    .sort(([, a], [, b]) => {
      return b - a;
    })
    .reduce((acc, [key]) => [...acc, key], []);

  if (sortedActivity?.[0] && activity[sortedActivity?.[0]]) {
    newParticipant.lastAction = activityCopy[sortedActivity[0]];
    newParticipant.lastActionDate = activity[sortedActivity[0]];
  }

  if (subscriptionData) {
    newParticipant.displayStatus =
      subscriptionData.status === communityEnrolmentStatuses.CURRENT ||
      (subscriptionData.status === communityEnrolmentStatuses.CANCELLED &&
        subscriptionData.cancelledAt > now)
        ? 'Subscriber'
        : 'Not on Nas.io';
    newParticipant.subscriptionStartDate =
      subscriptionData.createdAt !== ''
        ? subscriptionData.createdAt.toISOString().split('T')[0]
        : '';
    if (subscriptionData.paymentProvider === 'stripe') {
      newParticipant.paymentMethod = 'Card';
    } else if (subscriptionData.paymentProvider === 'xendit') {
      toReturnEntry.paymentMethod = 'Local Payment';
    }
    newParticipant =
      userDisplayUtils.checkAndFillUnsubscribingFields(newParticipant);
  }

  delete newParticipant.name;
  delete newParticipant.shortName;
  delete newParticipant.pushname;
  delete newParticipant.profilePicUrl;

  return newParticipant;
}

function getSearchWithoutSpaceAndPlusSign(search) {
  return search.replace(/ /g, '').replace(/\+/g, '');
}

async function retrieveWhatsappCommunityMembersPipelineCount(matchFilter) {
  const membersCount = await WhatsappParticipantsModel.countDocuments(
    matchFilter
  );

  return membersCount;
}

function generateWhatsappMemberMatchStage(communityId, search = '') {
  const matchQuery = {
    communityObjectId: new ObjectId(communityId),
    isInWhatsApp: true,
  };

  const searchWithoutSpaceAndPlusSign =
    getSearchWithoutSpaceAndPlusSign(search);

  if (searchWithoutSpaceAndPlusSign !== '') {
    //if (/^([0-9]+-)*([0-9]+)$/.test(searchWithoutSpaceAndPlusSign)) {
    matchQuery.number = {
      $regex: `${searchWithoutSpaceAndPlusSign}`,
    };
  }

  return { $match: matchQuery };
}

const generateWhatsappMembersPipelineQuery = (
  communityId,
  skip,
  limit,
  search = ''
) => {
  const matchStage = generateWhatsappMemberMatchStage(communityId, search);

  const pipelineQuery = [
    matchStage,
    {
      $sort: {
        subscriptionObjectId: -1,
        number: 1,
      },
    },
    {
      $skip: limit * skip,
    },
    {
      $limit: limit,
    },
    ...MongoDbUtils.lookupAndUnwind(
      'community_subscriptions',
      'subscriptionObjectId',
      '_id',
      'subscriptionData'
    ),
    {
      $addFields: {
        whatsappId: '$_id',
        learnerId: {
          $ifNull: ['$subscriptionData.learnerId', ''],
        },
      },
    },
    ...MongoDbUtils.lookupAndUnwind(
      'learners',
      'learnerId',
      'learnerId',
      'learnerData'
    ),
    ...MongoDbUtils.lookupAndUnwind(
      'country_currency_mapping',
      'learnerData.countryId',
      'countryId',
      'countryData'
    ),
    projectWhatsappCommunityMembersRequiredData(),
  ];

  return pipelineQuery;
};

async function populateApplicationAndPurchaseInfo(whatsappParticipants) {
  const subscriptionObjectIds = [];
  whatsappParticipants.forEach((participant) => {
    if (participant.subscriptionObjectId) {
      subscriptionObjectIds.push(participant.subscriptionObjectId);
    }
  });
  const applications = await CommunityApplicationsModel.find({
    subscriptionObjectId: { $in: subscriptionObjectIds },
  }).lean();
  const applicationMap = applications.reduce((acc, application) => {
    acc[application.subscriptionObjectId] = application;
    return acc;
  }, {});
  const purchaseTransactions =
    await CommunityPurchaseTransactionModel.aggregate([
      {
        $match: {
          subscriptionObjectId: { $in: subscriptionObjectIds },
        },
      },
      ...MongoDbUtils.lookupAndUnwind(
        'community_discount_transactions',
        '_id',
        'purchaseTransactionObjectId',
        'communityDiscountTransactionsData'
      ),
      ...MongoDbUtils.lookupAndUnwind(
        'community_discount',
        'communityDiscountTransactionsData.communityDiscountObjectId',
        '_id',
        'communityDiscountTransactionsData.communityDiscountData'
      ),
    ]);
  const purchaseTransactionMap = purchaseTransactions.reduce(
    (acc, purchaseTransaction) => {
      acc[purchaseTransaction.subscriptionObjectId] = purchaseTransaction;
      return acc;
    },
    {}
  );

  const results = whatsappParticipants.map((participant) => {
    if (participant.subscriptionObjectId) {
      const communityApplicationData =
        applicationMap[participant.subscriptionObjectId];
      const communityPurchaseTransaction =
        purchaseTransactionMap[participant.subscriptionObjectId];
      return {
        ...participant,
        communityApplicationData,
        communityPurchaseTransaction,
      };
    }
    return participant;
  });
  return results;
}

const retrieveWhatsappCommunityMembers = async (
  communityId,
  skip,
  limit,
  search
) => {
  const communityInfo = await CommunityModel.findOne({
    _id: new ObjectId(communityId),
  }).lean();

  const isBotWhatsappGroupAdmin =
    communityInfo.whatsappInfo?.isBotWhatsappGroupAdmin;

  if (!isBotWhatsappGroupAdmin) {
    return {
      data: [],
      meta: {
        total: 0,
        limit,
        page: 0,
        pages: 0,
      },
    };
  }
  const pipelineQuery = generateWhatsappMembersPipelineQuery(
    communityId,
    skip - 1,
    limit,
    search
  );

  const matchFilterQuery = pipelineQuery[0].$match;

  logger.info(
    `retrieveWhatsappCommunityMembers: Pipeline ${JSON.stringify(
      pipelineQuery
    )}`
  );

  let [totalMembersCount, whatsappParticipants, communityRoles] =
    await Promise.all([
      retrieveWhatsappCommunityMembersPipelineCount(matchFilterQuery),
      WhatsappParticipantsModel.aggregate(pipelineQuery),
      CommunityRoleModel.find({ communityObjectId: communityId }),
    ]);

  const communityRolesCache = communityRoles.reduce(
    (acc, communityRole) => {
      acc.set(communityRole.email, communityRole.role);
      return acc;
    },
    new Map()
  );

  const postProcessWhatsappParticipants = whatsappParticipants.map(
    (participant) =>
      postProcessWhatsappCommunityMemberData(
        participant,
        communityRolesCache
      )
  );

  const result = {
    data: postProcessWhatsappParticipants,
    meta: {
      total: totalMembersCount,
      limit,
      page: skip,
      pages: Math.ceil(totalMembersCount / limit),
    },
  };

  return result;
};

const retrieveWhatsappCommunityMember = async (
  whatsappId,
  communityId
) => {
  const whatsappMember = await WhatsappParticipantsModel.findOne(
    {
      _id: new ObjectId(whatsappId),
    },
    {
      _id: 0,
      isInWhatsApp: 1,
      profilePicUrl: 1,
      communityObjectId: 1,
      subscriptionObjectId: 1,
      countryCodeNumber: 1,
      country: 1,
      number: 1,
      name: 1,
      shortName: 1,
      pushname: 1,
      joinedAt: 1,
      fullName: 1,
      lastReacted: 1,
      lastMessageSent: 1,
    }
  ).lean();

  if (whatsappMember == null) {
    throw new Error('Invalid whatsappId');
  }

  const communityRoles = await CommunityRoleModel.find({
    communityObjectId: communityId,
  });

  const communityRolesCache = communityRoles.reduce(
    (acc, communityRole) => {
      acc.set(communityRole.email, communityRole.role);
      return acc;
    },
    new Map()
  );

  let nonSubscriberWhatsappMember;

  if (whatsappMember.subscriptionObjectId == null) {
    whatsappMember.whatsappId = whatsappId;

    nonSubscriberWhatsappMember =
      postProcessWhatsappCommunityMemberFullData(
        whatsappMember,
        communityRolesCache
      );

    return nonSubscriberWhatsappMember;
  }

  const currentDate = new Date().toUTCString();

  const pipelineQuery = [
    {
      $match: {
        _id: new ObjectId(whatsappId),
        communityObjectId: new ObjectId(communityId),
      },
    },
    ...MongoDbUtils.lookupAndUnwind(
      'community_subscriptions',
      'subscriptionObjectId',
      '_id',
      'subscriptionData'
    ),
    {
      $match: {
        $or: [
          {
            'subscriptionData.status': communityEnrolmentStatuses.CURRENT,
          },
          {
            $and: [
              {
                'subscriptionData.status':
                  communityEnrolmentStatuses.CANCELLED,
              },
              { cancelledAt: { $gte: currentDate } },
            ],
          },
        ],
      },
    },
    {
      $addFields: {
        whatsappId: '$_id',
        learnerId: {
          $ifNull: ['$subscriptionData.learnerId', ''],
        },
      },
    },
    ...MongoDbUtils.lookupAndUnwind(
      'learners',
      'learnerId',
      'learnerId',
      'learnerData'
    ),
    ...MongoDbUtils.lookupAndUnwind(
      'country_currency_mapping',
      'learnerData.countryId',
      'countryId',
      'countryData'
    ),
    ...MongoDbUtils.lookupAndUnwind(
      'community_application',
      'subscriptionObjectId',
      'subscriptionObjectId',
      'communityApplicationData'
    ),
    ...MongoDbUtils.lookupAndUnwind(
      'community_purchase_transactions',
      'subscriptionObjectId',
      'subscriptionObjectId',
      'purchaseTransactionData'
    ),
    {
      $project: {
        _id: 0,
        whatsappId: 1,
        isInWhatsApp: 1,
        profilePicUrl: 1,
        communityObjectId: 1,
        subscriptionObjectId: 1,
        countryCodeNumber: 1,
        country: 1,
        number: 1,
        name: 1,
        shortName: 1,
        pushname: 1,
        joinedAt: 1,
        fullName: 1,
        countryData: 1,
        subscriptionData: 1,
        learnerData: 1,
        communityRoleData: 1,
        purchaseTransactionData: 1,
        lastReacted: 1,
        lastMessageSent: 1,
      },
    },
  ];

  logger.info(
    `retrieveWhatsappCommunityMember: Pipeline ${JSON.stringify(
      pipelineQuery
    )}`
  );

  const whatsappParticipant = await WhatsappParticipantsModel.aggregate(
    pipelineQuery
  );

  if (whatsappParticipant.length === 0) {
    logger.error('Invalid subscriber whatsappId');
    return nonSubscriberWhatsappMember;
  }

  const postProcessWhatsappParticipant =
    postProcessWhatsappCommunityMemberFullData(
      whatsappParticipant[0],
      communityRolesCache
    );

  return postProcessWhatsappParticipant;
};

const removeMemberFromWhatsappViaQueue = async (groupId, contactId) => {
  const messageBody = {
    event: WHATSAPP_QUEUE_EVENTS.REMOVE,
    groupId,
    contactId,
    timestamp: new Date().toISOString(),
    requestor: 'LPBE',
  };

  logger.info(
    `removeMemberFromWhatsappViaQueue: ${JSON.stringify(messageBody)}`
  );

  sendMessageToSQSFifoQueue(
    WHATSAPP_SERVICE_QUEUE_BOT_URL,
    `whatsapp_${groupId}_${contactId}`,
    messageBody
  );
};

const removeWhatsappMemberBySubscription = async (
  communityObjectId,
  subscriptionObjectId
) => {
  try {
    const whatsappParticipants = await WhatsappParticipantsModel.find(
      {
        communityObjectId: new ObjectId(communityObjectId),
        isInWhatsApp: true,
        subscriptionObjectId: new ObjectId(subscriptionObjectId),
      },
      { groupId: 1, contactId: 1 }
    ).lean();

    if (whatsappParticipants.length === 0) {
      logger.info(
        `removeWhatsappMemberBySubscription: No whatsapp participant is found communityObjectId: ${communityObjectId}, subscriptionObjectId: ${subscriptionObjectId}`
      );
      return;
    }

    await Promise.all([
      whatsappParticipants.map(async (whatsappParticipant) => {
        const {
          _id: whatsappId,
          groupId,
          contactId,
        } = whatsappParticipant;

        await WhatsappParticipantsModel.findOneAndUpdate(
          { _id: new ObjectId(whatsappId) },
          { subscriptionObjectId: null, learnerId: null }
        );

        return removeMemberFromWhatsappViaQueue(groupId, contactId);
      }),
    ]);
  } catch (err) {
    logger.error(
      `removeWhatsappMemberBySubscription: Unable to remove member from the chat: ${err}`
    );
  }
};

const removeWhatsappMemberByWhatsappId = async (whatsappId) => {
  try {
    const whatsappParticipant = await WhatsappParticipantsModel.findOne(
      {
        _id: new ObjectId(whatsappId),
      },
      { _id: 0, groupId: 1, contactId: 1 }
    ).lean();

    if (whatsappParticipant == null) {
      logger.info(
        `removeWhatsappMemberByWhatsappId: No whatsapp participant is found ${whatsappId}`
      );
      return;
    }

    const { groupId, contactId } = whatsappParticipant;

    return removeMemberFromWhatsappViaQueue(groupId, contactId);
  } catch (err) {
    logger.error(
      `removeWhatsappMemberByWhatsappId: Unable to remove member from the chat: ${err}`
    );
  }
};

const generateWhatsappMembersCsvStream = async (
  communityId,
  limit,
  search
) => {
  try {
    const [communityRoles, community, communityUiConfig] =
      await Promise.all([
        CommunityRoleModel.find({ communityObjectId: communityId }).lean(),
        CommunityModel.findOne({ _id: communityId }).lean(),
        getCommunityUIConfigService(communityId),
      ]);

    const communityRolesCache = communityRoles.reduce(
      (acc, communityRole) => {
        acc.set(communityRole.email, communityRole.role);
        return acc;
      },
      new Map()
    );

    const csvStream = new Readable({
      objectMode: true,
      read() {},
    });

    // hardcode the tab id here, in the future should be derived from current tab when we migrate csv export to BE entirely
    const uiConfig = communityUiConfig?.config;
    const csvHeaders = userExportUtils.getMemberCsvHeaders({
      uiConfig,
      activeTabId: 'whatsapp',
    });
    csvStream.push(
      `${csvHeaders.map((entry) => entry.label).join(',')}\n`
    );

    let pageNo = 1;
    let hasNextPage = true;
    const getNextPage = async () => {
      const pipelineQuery = generateWhatsappMembersPipelineQuery(
        communityId,
        pageNo - 1,
        limit,
        search
      );

      let whatsappParticipants = await WhatsappParticipantsModel.aggregate(
        pipelineQuery
      );
      whatsappParticipants = await populateApplicationAndPurchaseInfo(
        whatsappParticipants
      );

      const currentPageSize = whatsappParticipants.length;
      whatsappParticipants.forEach((participant) => {
        const postProcessWhatsappParticipant =
          postProcessWhatsappCommunityMemberFullData(
            participant,
            communityRolesCache
          );
        const csvObject = userExportUtils.formatMemberCsvEntry(
          postProcessWhatsappParticipant,
          community.applicationConfigDataFields
        );

        const csvEntry = [];
        for (const header of csvHeaders) {
          const headerComponents = header.key.split('.');
          let value = csvObject;
          for (const component of headerComponents) {
            value = value[component];
            if (value == null) {
              break;
            }
          }
          if (value == null) {
            value = '';
          }
          csvEntry.push(value);
        }
        csvStream.push(`${csvEntry.join(',')}\n`);
      });

      pageNo += 1;
      hasNextPage = currentPageSize === limit;
      if (!hasNextPage) {
        csvStream.push(null);
      }
    };

    csvStream._read = () => {
      if (hasNextPage) {
        getNextPage();
      } else {
        csvStream.push(null);
      }
    };

    return csvStream;
  } catch (err) {
    logger.error(`generateCsvStream: ${err}`, err.stack);
    throw new Error('Error generating CSV stream');
  }
};

const updateWhatsappMemberSubscription = async (
  communityObjectId,
  subscriptionObjectId,
  newSubscriptionObjectid
) => {
  return WhatsappParticipantsModel.updateMany(
    {
      communityObjectId: new ObjectId(communityObjectId),
      isInWhatsApp: true,
      subscriptionObjectId: new ObjectId(subscriptionObjectId),
    },
    {
      $set: {
        subscriptionObjectId: new ObjectId(newSubscriptionObjectid),
      },
    },
    {
      lean: true,
      new: true,
    }
  );
};

const getWhatsappAnalytics = async ({ communityId, duration }) => {
  try {
    let result = {};
    const dateInUTC = DateTime.utc();
    logger.info(
      `getWhatsappAnalytics: dateInUTC: ${dateInUTC} for community ${communityId} and duration ${duration}`
    );
    // the variables for memberGrowth and analytics for the current duration
    let memberGrowthInfo = 0;
    let previousMemberGrowthInfo = 0;
    let linkVisits = 0;
    let previousLinkVisits = 0;
    const currentAnalyticsArr = [];
    const previousAnalyticsArr = [];

    const community = await CommunityModel.findOne({
      _id: new ObjectId(communityId),
    });

    const groupId = community.bots?.[0]?.serverKey;
    logger.info(
      'the groupId for the community id',
      communityId,
      ' groupId',
      groupId
    );

    const totalWhatsappMembers =
      await WhatsappParticipantsModel.countDocuments({
        communityObjectId: new ObjectId(communityId),
        isInWhatsApp: true,
      }).lean();

    if (duration !== 'all') {
      // the date that we want calculate the analytics for
      const currentAnalyticsDurationDate = dateInUTC.minus({
        days: duration,
      });
      logger.info(
        `getWhatsappAnalytics: currentAnalyticsDurationDate: ${currentAnalyticsDurationDate}`
      );
      // the date that we want to calculate the analytics for the previous duration
      const previousAnalyticsDurationDate = dateInUTC.minus({
        days: duration * 2,
      });
      // if duration 30 - we are getting the data for 60 days
      const allWhatsappAnalytics = await whatsappAnalyticsModel
        .find({
          communityObjectId: new ObjectId(communityId),
          startDate: { $gte: previousAnalyticsDurationDate },
        })
        .lean();

      const whatsappGroup = await whatsappConnectedGroupModel.findOne({
        whatsappGroupId: groupId,
      });

      const isAnnouncement = whatsappGroup?.groupMetadata?.announce;

      allWhatsappAnalytics.forEach((singleAnalyticsData) => {
        // if the date is greater than the duration date then we will push it to the current array else previous array
        if (
          singleAnalyticsData.startDate >= currentAnalyticsDurationDate
        ) {
          currentAnalyticsArr.push(singleAnalyticsData);
        } else {
          previousAnalyticsArr.push(singleAnalyticsData);
        }
      });

      // we create a map of the users who are engaged in the group
      // flatMap() flattens the returned arrays from each element into a single array
      let allUsersEngagedId = currentAnalyticsArr.flatMap(
        (singleAnalytics) => [
          ...(singleAnalytics?.messagesSent || []), // Add all elements of 'messagesSent' array to the result
          ...(singleAnalytics?.reactions || []), // Add all elements of 'reactions' array to the result
          ...(singleAnalytics?.messageRead || []), // Add all elements of 'messageRead' array to the result
        ]
      );

      // Sum the 'membersAddedToday' property of all elements in the 'currentAnalyticsArr' array
      memberGrowthInfo += currentAnalyticsArr.reduce(
        (acc, curr) => acc + (curr?.membersAddedToday || 0),
        0
      );

      // Sum the 'linkVisits' property of all elements in the 'currentAnalyticsArr' array
      linkVisits += currentAnalyticsArr.reduce(
        (acc, curr) => acc + (curr?.linkVisits || 0),
        0
      );

      // Remove duplicate values from the 'allUsersEngagedId' array using the spread operator and the Set object
      allUsersEngagedId = [...new Set(allUsersEngagedId)];

      // flatMap() flattens the returned arrays from each element into a single array
      let allUserPreviousEngagedId = previousAnalyticsArr.flatMap(
        (singleAnalytics) => [
          ...(singleAnalytics?.messagesSent || []), // Add all elements of 'messagesSent' array to the result
          ...(singleAnalytics?.reactions || []), // Add all elements of 'reactions' array to the result
          ...(singleAnalytics?.messageRead || []), // Add all elements of 'messageRead' array to the result
        ]
      );

      // Sum the 'membersAddedToday' property of all elements in the 'oldAnalyticsArray' array
      previousMemberGrowthInfo += previousAnalyticsArr.reduce(
        (acc, curr) => acc + (curr?.membersAddedToday || 0),
        0
      );

      // Sum the 'linkVisits' property of all elements in the 'oldAnalyticsArray' array
      previousLinkVisits += previousAnalyticsArr.reduce(
        (acc, curr) => acc + (curr?.linkVisits || 0),
        0
      );

      // Remove duplicate values from the 'allUserPreviousEngagedId' array using the spread operator and the Set object
      allUserPreviousEngagedId = [...new Set(allUserPreviousEngagedId)];

      const engagedMembers = allUsersEngagedId?.length;

      const linkVisitsGrowth = linkVisits - previousLinkVisits;

      logger.info(
        `getWhatsappAnalytics: linkVisitsGrowth: ${linkVisitsGrowth} and previousLinkVisits: ${previousLinkVisits}`
      );
      const linkVisitsGrowthPercentage =
        (linkVisitsGrowth / previousLinkVisits) * 100;
      // now we calculate the engagement percentage of the members = percentage of the members who are engaged in the group from total members]
      const engagementPercentage =
        (engagedMembers / totalWhatsappMembers) * 100;

      logger.info(
        `getWhatsappAnalytics: memberGrowthInfo: ${memberGrowthInfo} and previousMemberGrowthInfo: ${previousMemberGrowthInfo}`
      );
      // now we calculate the members growth
      const memberGrowth = memberGrowthInfo - previousMemberGrowthInfo;
      logger.info(
        `getWhatsappAnalytics: memberGrowth: ${memberGrowth} and linkVisitsGrowthPercentage: ${linkVisitsGrowthPercentage}`
      );

      // eslint-disable-next-line no-restricted-globals
      const totalEngagementData = isNaN(engagementPercentage)
        ? 0
        : engagementPercentage;
      result = {
        engagedMembers: isAnnouncement
          ? null
          : {
              growth: 0,
              total: Math.round(
                (engagedMembers / totalWhatsappMembers) * 100
              ),
            },
        memberGrowth: {
          growth: 0,
          total: memberGrowthInfo,
        },
        lastReadMessage: {},
        linkVisits: { growth: 0, total: linkVisits },
      };
    } else {
      const allParticipants =
        await WhatsappParticipantsModel.countDocuments({
          communityObjectId: communityId,
          isInWhatsApp: true,
        });
      const allWhatsappAnalytics = await whatsappAnalyticsModel
        .find({
          communityObjectId: new ObjectId(communityId),
        })
        .lean();
      linkVisits += allWhatsappAnalytics.reduce(
        (acc, curr) => acc + (curr?.linkVisits || 0),
        0
      );
      result = {
        engagedMembers: null,
        memberGrowth: {
          growth: 0,
          total: allParticipants,
        },
        lastReadMessage: {},
        linkVisits: { growth: 0, total: linkVisits },
      };
    }
    return result;
  } catch (err) {
    logger.error(`getWhatsappAnalytics: ${err}`, err.stack);
    throw new Error('Error getting whatsapp analytics');
  }
};
module.exports = {
  sendWhatsappTemplateMessage,
  sendReplyToMessage,
  sendWhatsappMagicReachMessage,
  retrieveWhatsappCommunityCode,
  retrieveWhatsappCommunityMembersCountByCode,
  retrieveWhatsappCommunityMembersCountNonSubscriber,
  retrieveWhatsappCommunityMembersCount,
  retrieveWhatsappCommunityStatus,
  retrieveWhatsappCommunityMembers,
  retrieveWhatsappCommunityMember,
  removeWhatsappMemberBySubscription,
  removeWhatsappMemberByWhatsappId,
  retrieveWhatsappGroupInfo,
  generateWhatsappMembersCsvStream,
  updateWhatsappMemberSubscription,
  getWhatsappAnalytics,
  removeMemberFromWhatsappViaQueue,
};
