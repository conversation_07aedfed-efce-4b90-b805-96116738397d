const { DateTime } = require('luxon');
const httpStatus = require('http-status');
const mongoose = require('mongoose');

const axios = require('../../../clients/axios.client');

const { ObjectId } = mongoose.Types;
const {
  MAIN_PAYMENT_BACKEND_URL,
  MAIN_WEBSITE_AUTH_KEY,
} = require('../../../config');
const {
  PURCHASE_TYPE,
  PRODUCT_PURCHASE_TYPES,
  PAYABLE_PURCHASE_TYPES,
  DISCOUNT_STATUS_TYPE,
  DISCOUNT_VALUE_TYPE,
  DISCOUNT_CATEGORY,
  PAYMENT_PROVIDER,
} = require('../../../constants/common');

const PrimaryMongooseConnection = require('../../../rpc/primaryMongooseConnection');
const CommunityDiscountModel = require('../../models/communityDiscounts.model');
const CommunityFolderModel = require('../../models/communityFolders.model');
const CommunityEventModel = require('../../models/communityEvents.model');
const ProgramModel = require('../../../models/program/program.model');
const CommunityModel = require('../../models/community.model');
const AddonTransactionModel = require('../../models/communityAddonTransactions.model');
const PurchaseTransactionModel = require('../../models/communityPurchaseTransactions.model');

const communityProductService = require('../../../services/common/communityProducts.service');

const logger = require('../../../services/logger.service');

const {
  ToUserError,
  ParamError,
  InternalError,
  ResourceNotFoundError,
} = require('../../../utils/error.util');
const { DISCOUNT_ERROR } = require('../../../constants/errorCode');
const regexUtils = require('../../../utils/regex.util');
const PaymentBackendRpc = require('../../../rpc/paymentBackend');
const PaymentProviderUtils = require('../../../utils/paymentProvider.util');
const { PRODUCT_TYPE } = require('../../../services/product/constants');

const validateCommunityById = async (communityObjectId, select) => {
  logger.info(`Checking if communityObjectId ${communityObjectId} exists`);
  const community = await CommunityModel.findById(communityObjectId)
    .select(select)
    .lean();

  if (!community) {
    throw new ParamError(
      `Community ${communityObjectId} does not exists!`
    );
  }

  return community;
};

const validateDiscountCode = async (code, communityCode) => {
  const discount = await CommunityDiscountModel.findOne({
    code,
    communityCode,
  })
    .select('_id isActive code')
    .lean();

  if (discount) {
    const error = new ToUserError(
      'Discount code already exists',
      DISCOUNT_ERROR.DISCOUNT_CODE_EXISTS,
      {
        discountCode: discount.code,
        isActive: discount.isActive ? 'active' : 'disabled',
      }
    );
    logger.error(`validateDiscountCode error|error=${error.message}`);
    throw error;
  }

  return true;
};

const validateDiscountValue = async (payload) => {
  if (
    payload.type === DISCOUNT_VALUE_TYPE.PERCENTAGE &&
    payload.value <= 0
  ) {
    throw new ParamError(`Invalid discount value: ${payload.value}`);
  }
};

const retrieveAllDiscountsRelatedToEntity = async (
  entity,
  communityCode
) => {
  const { discountsApplied = [] } = entity;

  const discounts = await CommunityDiscountModel.find(
    {
      $or: [
        {
          _id: { $in: discountsApplied },
        },
        { linkedEntities: { $exists: false } },
        { linkedEntities: { $size: 0 } },
      ],
      isActive: true,
      communityCode,
      discountCategory: {
        $ne: DISCOUNT_CATEGORY.UPSELL,
      },
    },
    {
      code: 1,
      effectiveTimeStart: 1,
      effectiveTimeEnd: 1,
      maxRedemptions: 1,
      type: 1,
      isActive: 1,
      totalRedemptions: 1,
      value: 1,
      trialDays: 1,
      intervalCount: 1,
      linkedEntities: 1,
      createdAt: 1,
    }
  ).lean();

  return discounts;
};

const filterAndObtainLinkedEntities = async (
  linkedEntities,
  communityObjectId
) => {
  let needStripeDiscount = linkedEntities.length === 0;
  const updatedEntityArr = [];
  await Promise.all(
    linkedEntities.map(async (entity) => {
      if (
        [PRODUCT_TYPE.COURSE, PRODUCT_TYPE.DIGITAL_FILES].includes(
          entity.type.toUpperCase()
        )
      ) {
        // eslint-disable-next-line no-param-reassign
        entity.type = PURCHASE_TYPE.FOLDER;
      }

      const entityType = entity.type.toUpperCase();
      if (PRODUCT_PURCHASE_TYPES.includes(entityType)) {
        const folder = await CommunityFolderModel.findOne({
          _id: entity.entityObjectId,
          communityObjectId: new ObjectId(communityObjectId),
        })
          .select('_id title shortUrl resourceSlug')
          .lean();
        if (folder) {
          updatedEntityArr.push({
            type: entityType,
            entityObjectId: new ObjectId(folder?._id),
            title: folder?.title,
            slug: folder?.resourceSlug,
          });
        }
        logger.info(
          `Product ${entity.entityObjectId} does not exists for community ${communityObjectId}`
        );
      } else {
        switch (entityType) {
          case PURCHASE_TYPE.SUBSCRIPTION:
            needStripeDiscount = true;
            updatedEntityArr.push({
              type: PURCHASE_TYPE.SUBSCRIPTION,
              entityObjectId: new ObjectId(communityObjectId),
              title: 'Membership',
            });
            break;

          case PURCHASE_TYPE.EVENT: {
            const event = await CommunityEventModel.findOne({
              _id: entity.entityObjectId,
              communities: new ObjectId(communityObjectId),
            })
              .select('_id title slug')
              .lean();
            if (event) {
              updatedEntityArr.push({
                type: PURCHASE_TYPE.EVENT,
                entityObjectId: new ObjectId(event._id),
                title: event.title,
                slug: event.slug,
              });
            }
            logger.info(
              `Event ${entity.entityObjectId} does not exists for community ${communityObjectId}`
            );
            break;
          }

          case PURCHASE_TYPE.CHALLENGE: {
            const program = await ProgramModel.findOne({
              _id: entity.entityObjectId,
              communityObjectId: new ObjectId(communityObjectId),
            })
              .select('_id title slug')
              .lean();

            if (program) {
              updatedEntityArr.push({
                type: PURCHASE_TYPE.CHALLENGE,
                entityObjectId: new ObjectId(program._id),
                title: program.title,
                slug: program.slug,
              });
            }
            logger.info(
              `Challenge ${entity.entityObjectId} does not exists for community ${communityObjectId}`
            );
            break;
          }

          default:
            throw new InternalError(`Invalid entity type ${entityType}`);
        }
      }
    })
  );
  logger.info(
    `Filtered linkedEntities for the requested discount creation ${JSON.stringify(
      updatedEntityArr
    )}`
  );
  return { filtered: updatedEntityArr, needStripeDiscount };
};

function retrieveModel(entityType) {
  let model;

  if (PRODUCT_PURCHASE_TYPES.includes(entityType)) {
    model = CommunityFolderModel;
  } else {
    switch (entityType) {
      case PURCHASE_TYPE.SUBSCRIPTION:
        model = CommunityModel;
        break;

      case PURCHASE_TYPE.EVENT:
        model = CommunityEventModel;
        break;

      case PURCHASE_TYPE.CHALLENGE:
        model = ProgramModel;
        break;
      default:
        throw new InternalError(
          `Entity type ${entityType} is not supported`
        );
    }
  }

  return model;
}

const updateLinkedEntitiesWithDiscountId = async (
  linkedEntities,
  discountObjectIds,
  session = undefined
) => {
  const idsToDB = discountObjectIds.map((id) => new ObjectId(id));

  await Promise.all(
    linkedEntities.map(async (entity) => {
      const { type: entityType } = entity;

      const model = retrieveModel(entityType);

      await model.findByIdAndUpdate(
        entity.entityObjectId,
        {
          $addToSet: {
            discountsApplied: { $each: idsToDB },
          },
        },
        { session }
      );
    })
  );

  logger.info(
    `Updated entities with the new discount ${discountObjectIds}`
  );
};

// eslint-disable-next-line no-unused-vars
const removeDiscountFromEntities = async (
  linkedEntities,
  discountObjectId
) => {
  await Promise.all(
    linkedEntities.map(async (entity) => {
      const { type: entityType } = entity;

      const model = retrieveModel(entityType);

      await model.findByIdAndUpdate(entity.entityObjectId, {
        $pullAll: {
          discountsApplied: [new ObjectId(discountObjectId)],
        },
      });
    })
  );
  logger.info(`Updated entities by removing discount ${discountObjectId}`);
};

const discountValidationForEntities = async (
  communityCode,
  newDiscountsToApply = [],
  discountsToAdd = []
) => {
  // Adding Existing Discounts to be applied
  if (discountsToAdd.length !== 0) {
    const numberOfDiscountsExisting =
      await CommunityDiscountModel.countDocuments({
        _id: { $in: discountsToAdd },
      });

    if (numberOfDiscountsExisting !== discountsToAdd.length) {
      throw new ParamError(
        'There is an non-existing discount specified in discountsToAdd'
      );
    }
  }

  if (newDiscountsToApply.length !== 0) {
    // Creating New Discounts to be applied
    const newDiscountCodes = newDiscountsToApply.map((discount) => {
      const isValid = /^[A-Z0-9]*$/.test(discount.code);
      if (!isValid) {
        throw new ToUserError(
          'Discount code should only have uppercase letters and numbers.',
          DISCOUNT_ERROR.INVALID_DISCOUNT_CODE_FORMAT
        );
      }
      return discount.code;
    });

    const existingDiscount = await CommunityDiscountModel.exists({
      code: { $in: newDiscountCodes },
      communityCode,
      'linkedEntities.0': { $exists: false },
    });

    if (existingDiscount) {
      throw new ToUserError(
        'Discount code already exists. Please specify a different code.',
        DISCOUNT_ERROR.DISCOUNT_CODE_EXISTS
      );
    }
  }
};

const discountCreationForEntities = async (
  communityCode,
  oldEntity,
  entity,
  entityType,
  createdBy,
  entityModel,
  newDiscountsToApply = [],
  discountsToRemove = [],
  discountsToAdd = [],
  discountsToDisable = [],
  databaseSession = null
) => {
  let session;

  if (!databaseSession) {
    const primaryMongooseConnection =
      await PrimaryMongooseConnection.connect();

    session = await primaryMongooseConnection.startSession();
    session.startTransaction();
  } else {
    session = databaseSession;
  }

  let result;
  try {
    const linkedEntity = {
      type: entityType,
      entityObjectId: entity._id,
      title: entity.title,
      slug: entity.slug,
    };

    let discountsApplied = entity.discountsApplied ?? [];

    const discountsRelevant = new Set(
      discountsApplied.map((id) => id.toString())
    );

    if (
      (oldEntity &&
        (oldEntity.title !== entity.title ||
          oldEntity.slug !== entity.slug)) ||
      discountsToRemove.length !== 0 ||
      discountsToDisable.length !== 0 ||
      discountsToAdd.length !== 0
    ) {
      logger.info(
        `Updating discounts cause either title/slug has changed or there's discounts to unlink`
      );

      discountsToRemove.forEach((id) => discountsRelevant.delete(id));

      const bulkOperations = [];

      // Remove linked entities
      if (discountsApplied.length > 0) {
        bulkOperations.push({
          updateMany: {
            filter: { _id: { $in: discountsApplied } },
            update: {
              $pull: {
                linkedEntities: {
                  type: entityType,
                  entityObjectId: entity._id,
                },
              },
            },
          },
        });
      }

      // Disable discounts
      if (discountsToDisable.length > 0) {
        bulkOperations.push({
          updateMany: {
            filter: { _id: { $in: discountsToDisable } },
            update: { $set: { isActive: false } },
          },
        });
      }

      // Execute bulk operations if any exist
      if (bulkOperations.length > 0) {
        await CommunityDiscountModel.bulkWrite(bulkOperations, {
          session,
        });
      }

      const discountsToUpdate = Array.from(discountsRelevant)
        .concat(discountsToAdd)
        .map((id) => new ObjectId(id));
      // Adding Existing Discounts to be applied

      logger.info(
        `Updating Community Discounts with ObjectId ${JSON.stringify(
          discountsToUpdate
        )} with linkedEntities : ${JSON.stringify(linkedEntity)}`
      );

      await CommunityDiscountModel.updateMany(
        { _id: { $in: discountsToUpdate } },
        {
          $addToSet: { linkedEntities: linkedEntity },
        },
        {
          session,
        }
      );
    }

    // Creating New Discounts to be applied
    if (newDiscountsToApply.length > 0) {
      logger.info(
        `Creating Community discounts with the given params:
        ${JSON.stringify(newDiscountsToApply)}`
      );

      for await (const discount of newDiscountsToApply) {
        const discountDetails = {
          ...discount,
          effectiveTimeStart:
            discount.effectiveTimeStart ?? DateTime.utc(),
          effectiveTimeEnd: discount.effectiveTimeEnd ?? null,
          communityCode,
          createdBy,
          linkedEntities: [linkedEntity],
        };

        // Max redemptions does not allow negative number
        if (discount.maxRedemptions === -1) {
          discountDetails.maxRedemptions = null;
        }

        const newDiscount = await CommunityDiscountModel.findOneAndUpdate(
          { code: discount.code, communityCode },
          discountDetails,
          { new: true, upsert: true, session }
        ).lean();

        discountsRelevant.add(newDiscount._id.toString());
        logger.info(`Created Community discount:  ${newDiscount._id}`);
      }
    }

    discountsApplied = Array.from(discountsRelevant)
      .concat(discountsToAdd)
      .map((id) => new ObjectId(id));

    result = await entityModel
      .findByIdAndUpdate(
        entity._id,
        { discountsApplied },
        { upsert: false, new: true, session }
      )
      .lean();

    if (!databaseSession) {
      await session.commitTransaction();
    }
  } catch (error) {
    if (!databaseSession) {
      await session.abortTransaction();
    }

    logger.error(
      `discountCreationForEntities error ${error.message}, ${error.stack}`
    );
    throw error;
  } finally {
    if (!databaseSession) {
      await session.endSession();
    }
  }

  const discountsAppliedArr = await retrieveAllDiscountsRelatedToEntity(
    result,
    communityCode
  );

  if (discountsAppliedArr) {
    logger.info(
      `Found discounts for entity ${result._id}: ${JSON.stringify(
        discountsAppliedArr
      )}`
    );
    result.discountsApplied = discountsAppliedArr;
  }

  return result;
};

function retrieveDiscountStatus(discount) {
  const { maxRedemptions, totalRedemptions, effectiveTimeEnd, isActive } =
    discount;

  const currentDate = new Date();

  let status = DISCOUNT_STATUS_TYPE.ACTIVE;

  if (maxRedemptions && totalRedemptions >= maxRedemptions) {
    status = DISCOUNT_STATUS_TYPE.FULLY_REDEEMED;
  } else if (!isActive) {
    status = DISCOUNT_STATUS_TYPE.INACTIVE;
  } else if (
    effectiveTimeEnd &&
    effectiveTimeEnd.toISOString() < currentDate.toISOString()
  ) {
    status = DISCOUNT_STATUS_TYPE.EXPIRED;
  }

  return status;
}

const getDiscountsForCommunity = async ({
  communityObjectId,
  pageNo = 1,
  pageSize = 50,
  sortBy,
  sortOrder,
  excludeEmptyEntities = false,
  entityType = 'ALL',
  search = '',
  status,
}) => {
  logger.info(`Checking if communityObjectId ${communityObjectId} exists`);
  const community = await validateCommunityById(
    communityObjectId,
    '_id code isFreeCommunity'
  );

  logger.info(
    `Searching discounts for community ${communityObjectId} - ${community.code}`
  );
  const filter = {
    communityCode: community.code,
    discountCategory: { $ne: DISCOUNT_CATEGORY.UPSELL },
  };

  const searchWithEscapedRegexSign = regexUtils.escapeRegExp(search);

  if (searchWithEscapedRegexSign !== '') {
    const regexPattern = new RegExp(searchWithEscapedRegexSign, 'i');

    filter.code = regexPattern;
  }

  if (
    entityType !== 'ALL' &&
    PAYABLE_PURCHASE_TYPES.includes(entityType)
  ) {
    filter['linkedEntities.type'] = entityType;
  }

  if (excludeEmptyEntities) {
    filter['linkedEntities.0'] = { $exists: true };
  }

  switch (status) {
    case DISCOUNT_STATUS_TYPE.FULLY_REDEEMED:
      filter.maxRedemptions = { $ne: null };
      filter.totalRedemptions = { $ne: null };
      filter.$expr = { $gte: ['$totalRedemptions', '$maxRedemptions'] };
      break;
    case DISCOUNT_STATUS_TYPE.INACTIVE:
      filter.$or = [
        { maxRedemptions: null },
        { totalRedemptions: null },
        {
          maxRedemptions: { $ne: null },
          totalRedemptions: { $ne: null },
          $expr: { $lt: ['$totalRedemptions', '$maxRedemptions'] },
        },
      ];
      filter.isActive = false;
      break;
    case DISCOUNT_STATUS_TYPE.EXPIRED:
      filter.$or = [
        { maxRedemptions: null },
        { totalRedemptions: null },
        {
          maxRedemptions: { $ne: null },
          totalRedemptions: { $ne: null },
          $expr: { $lt: ['$totalRedemptions', '$maxRedemptions'] },
        },
      ];
      filter.effectiveTimeEnd = { $ne: null, $lt: new Date() };
      filter.isActive = true;
      break;
    case DISCOUNT_STATUS_TYPE.ACTIVE:
      filter.$and = [
        {
          $or: [
            { maxRedemptions: null },
            { totalRedemptions: null },
            {
              maxRedemptions: { $ne: null },
              totalRedemptions: { $ne: null },
              $expr: { $lt: ['$totalRedemptions', '$maxRedemptions'] },
            },
          ],
        },
        {
          $or: [
            { effectiveTimeEnd: null },
            { effectiveTimeEnd: { $ne: null, $gte: new Date() } },
          ],
        },
      ];
      filter.isActive = true;
      break;
    case null:
    case undefined:
    case '':
      break;
    default:
      throw new ParamError(`Invalid discount status ${status}`);
  }

  const projection = {
    _id: 1,
    type: 1,
    value: 1,
    code: 1,
    isActive: 1,
    maxRedemptions: 1,
    totalRedemptions: {
      $ifNull: ['$totalRedemptions', 0],
    },
    createdAt: 1,
    linkedEntities: 1,
    trialDays: 1,
    intervalCount: 1,
    effectiveTimeEnd: 1,
    effectiveTimeStart: 1,
    timezone: 1,
  };

  const discountAggregationQuery = [
    {
      $match: filter,
    },
    {
      $sort: {
        [sortBy]: sortOrder,
      },
    },
    {
      $project: projection,
    },
  ];

  if (pageSize !== -1) {
    discountAggregationQuery.push({
      $skip: (pageNo - 1) * pageSize,
    });

    discountAggregationQuery.push({
      $limit: pageSize,
    });
  }

  try {
    const [data, totalCount, allProducts] = await Promise.all([
      CommunityDiscountModel.aggregate(discountAggregationQuery),
      CommunityDiscountModel.countDocuments(filter),
      communityProductService.retrieveAllProducts({ communityObjectId }),
    ]);

    const { products: entities } = allProducts;

    if (!community.isFreeCommunity) {
      entities.unshift({
        entityObjectId: communityObjectId,
        title: 'Membership',
        type: PURCHASE_TYPE.SUBSCRIPTION,
      });
    }

    const entityMap = {};

    entities.forEach((entity) => {
      if (
        [PRODUCT_TYPE.COURSE, PRODUCT_TYPE.DIGITAL_FILES].includes(
          entity.type
        )
      ) {
        entityMap[entity.entityObjectId] = entity;
      }
    });

    const discounts = data.map((discount) => {
      const discountStatus = retrieveDiscountStatus(discount);

      discount.linkedEntities.forEach((linked) => {
        if (linked.type === PURCHASE_TYPE.FOLDER) {
          const match = entityMap[linked.entityObjectId];

          if (match) {
            linked.type = match.type;
          }
        }
      });

      const discountWithStatus = {
        ...discount,
        status: discountStatus,
      };

      return discountWithStatus;
    });

    return {
      discounts,
      entities,
      metadata: {
        total: totalCount,
        limit: pageSize,
        page: pageNo,
        pages: Math.ceil(totalCount / pageSize),
      },
    };
  } catch (error) {
    logger.error(`getDiscountsForCommunity error|error=${error}`);
    throw error;
  }
};

const getOneDiscountById = async (id) => {
  const discountData = await CommunityDiscountModel.findById(id)
    .select(
      '_id type value code isActive maxRedemptions totalRedemptions linkedEntities createdAt'
    )
    .lean();

  if (!discountData) {
    const error = new Error(`Discount ${id} does not exists!`);
    error.status = httpStatus.BAD_REQUEST;
    logger.error(`getOneDiscountById error|error=${error.message}`);
    throw error;
  }
  return discountData;
};

const updateCommunityDiscount = async (params = {}) => {
  logger.info(
    `Checking if discountObjectId ${params.discountObjectId} exists`
  );

  const [discount, community] = await Promise.all([
    CommunityDiscountModel.findById(params.discountObjectId)
      .select(
        '_id code value type maxRedemptions linkedEntities promoCodeStripeId discountCouponStripeId isActive communityCode stripePromoCodeDetails'
      )
      .lean(),
    validateCommunityById(
      params.communityObjectId,
      '_id code stripeProductId stripeUsProductId isPaidCommunity payment_methods'
    ),
  ]);

  if (!discount) {
    throw new ParamError(
      `Discount ${params.discountObjectId} does not exists!`
    );
  }

  const paymentProvider =
    await PaymentProviderUtils.retrievePaymentProvider(
      community.payment_methods
    );

  const toBeUpdatedPayload = {
    value: params.value,
    effectiveTimeEnd: params.effectiveTimeEnd,
  };
  let isChangeActive = false;

  // Currently we only allow CM to toggle on/off for discount code, cannot change other value
  if (params.isActive !== null && params.isActive !== discount.isActive) {
    toBeUpdatedPayload.isActive = params.isActive;
    isChangeActive = true;
  }

  try {
    if (discount.promoCodeStripeId && isChangeActive) {
      try {
        const paymentBackendRpc = new PaymentBackendRpc();
        await paymentBackendRpc.init();

        switch (paymentProvider) {
          case PAYMENT_PROVIDER.STRIPE:
          case PAYMENT_PROVIDER.STRIPE_US:
            // double write to both stripe sg and us account
            await paymentBackendRpc.updateStripeDiscount({
              stripePromotionCodeId: discount.promoCodeStripeId,
              isActive: params.isActive,
              paymentProvider: PAYMENT_PROVIDER.STRIPE,
            });

            if (discount.stripePromoCodeDetails) {
              await paymentBackendRpc.updateStripeDiscount({
                stripePromotionCodeId:
                  discount.stripePromoCodeDetails.promotionCodeId,
                isActive: params.isActive,
                paymentProvider: PAYMENT_PROVIDER.STRIPE_US,
              });
            }

            break;
          case PAYMENT_PROVIDER.STRIPE_INDIA:
            await paymentBackendRpc.updateStripeDiscount({
              stripePromotionCodeId: discount.promoCodeStripeId,
              isActive: params.isActive,
              paymentProvider,
            });
            break;
          default:
        }
      } catch (error) {
        const errorData = {};
        errorData.message =
          error?.response?.data?.info ||
          new Error('Error in update stripe community discount');
        errorData.status =
          error?.response?.data?.status || httpStatus.BAD_REQUEST;
        logger.error(
          `updateCommunityDiscount error|error=${error.message}`
        );
        throw errorData;
      }
    }
    logger.info(
      `Updating discounts ${params.discountObjectId} with ${JSON.stringify(
        toBeUpdatedPayload
      )}`
    );

    const newDiscount = await CommunityDiscountModel.findByIdAndUpdate(
      params.discountObjectId,
      toBeUpdatedPayload,
      {
        new: true,
        upsert: false,
        session: params.session,
      }
    ).lean();

    const discountStatus = retrieveDiscountStatus(newDiscount);
    const discountWithStatus = {
      ...newDiscount,
      status: discountStatus,
    };

    return discountWithStatus;
  } catch (error) {
    logger.error(`updateCommunityDiscount error|error=${error}`);
    throw error;
  }
};

function initDiscountDetails({ community, updatedEntityArr, params }) {
  const {
    isActive,
    code,
    value,
    type,
    maxRedemptions,
    maxRedemptionsPerPerson,
    effectiveTimeStart,
    effectiveTimeEnd,
    intervalCount = 0,
    trialDays = 0,
    prefix,
    timezone,
    discountCategory = DISCOUNT_CATEGORY.GENERAL,
    expiredDuration,
  } = params;

  if (
    community.baseCurrency === 'INR' &&
    (trialDays > 0 || intervalCount)
  ) {
    throw new ParamError(
      'INR community cannot have trial days or interval count'
    );
  }

  const discountDetails = {
    isActive,
    value,
    type: type.toLowerCase(),
    maxRedemptions,
    maxRedemptionsPerPerson,
    effectiveTimeStart: effectiveTimeStart ?? DateTime.utc(),
    effectiveTimeEnd: effectiveTimeEnd ?? null,
    code,
    communityCode: community.code,
    linkedEntities: updatedEntityArr,
    discountCategory,
    expiredDuration,
  };

  if (timezone) {
    discountDetails.timezone = timezone;
  }

  if (prefix) {
    discountDetails.prefix = prefix;
  }

  if (intervalCount) {
    discountDetails.durationInMonths = intervalCount;
    discountDetails.duration = 'repeating';
  }

  if (trialDays > 0) {
    discountDetails.trialDays = trialDays;
  }

  return discountDetails;
}

async function createStripeDiscount({ community, code, discountDetails }) {
  if (!community.stripeProductId && !community.stripeUsProductId) {
    const error = new Error(
      `Community ${community.code} does not have a stripe product id!`
    );
    error.status = httpStatus.BAD_REQUEST;
    logger.error(`createCommunityDiscount error|error=${error.message}`);
    throw error;
  }

  const paymentProvider =
    await PaymentProviderUtils.retrievePaymentProvider(
      community.payment_methods
    );
  logger.info(
    `Creating discount on stripe for discount ${code} and community ${community.code}`
  );
  try {
    const paymentBackendRpc = new PaymentBackendRpc();
    await paymentBackendRpc.init();

    let stripeDiscount = {};
    let stripeUsDiscount = {};
    let stripePromoCodeDetails;
    switch (paymentProvider) {
      case PAYMENT_PROVIDER.STRIPE:
      case PAYMENT_PROVIDER.STRIPE_US:
        // double write to both stripe sg and us account
        stripeDiscount = await paymentBackendRpc.createStripeDiscount({
          ...discountDetails,
          stripeProductId: community.stripeProductId,
          paymentProvider: PAYMENT_PROVIDER.STRIPE,
        });

        if (community.stripeUsProductId) {
          stripeUsDiscount = await paymentBackendRpc.createStripeDiscount({
            ...discountDetails,
            stripeProductId: community.stripeUsProductId,
            paymentProvider: PAYMENT_PROVIDER.STRIPE_US,
          });

          stripePromoCodeDetails = {
            promotionCode: stripeUsDiscount.stripePromotionCode,
            couponId: stripeUsDiscount.stripeCouponId,
            promotionCodeId: stripeUsDiscount.stripePromotionCodeId,
            stripeProductId: community.stripeUsProductId,
          };
        }
        break;
      case PAYMENT_PROVIDER.STRIPE_INDIA:
        stripeDiscount = await paymentBackendRpc.createStripeDiscount({
          ...discountDetails,
          stripeProductId: community.stripeProductId,
          paymentProvider,
        });
        break;
      default:
    }

    return {
      discountPlatform: paymentProvider,
      discountCouponStripeId: stripeDiscount.stripeCouponId,
      communityStripeProductId: community.stripeProductId,
      promoCodeStripeId: stripeDiscount.stripePromotionCodeId,
      promoCodeStripe: stripeDiscount.stripePromotionCode,
      stripePromoCodeDetails,
    };
  } catch (error) {
    const errorData = {};
    errorData.message =
      error?.response?.data?.info ||
      new Error('Error in stripe community discount api call');
    errorData.status =
      error?.response?.data?.status || httpStatus.BAD_REQUEST;

    logger.error(
      `createCommunityDiscount error|error=${errorData.message}`
    );
    throw errorData;
  }
}

async function generateStripeDiscountForCommunitySwitchToPaid({
  community,
  stripeProductId,
  stripeUsProductId,
}) {
  const discounts = await CommunityDiscountModel.find({
    promoCodeStripeId: { $exists: false },
    linkedEntities: { $size: 0 },
    communityCode: community.code,
  }).lean();

  const filteredDiscounts = discounts.filter((discount) => {
    const isNotFreeTrial = !discount.trialDays;
    const isNot100PercentForever =
      discount.value !== 100 || discount.intervalCount;

    return isNotFreeTrial || isNot100PercentForever;
  });

  const communityWithStripeProductId = {
    ...community,
    stripeProductId,
    stripeUsProductId,
  };

  await Promise.all([
    filteredDiscounts.map(async (discount) => {
      const { _id: discountObjectId, code } = discount;

      const discountDetails = {
        ...discount,
      };

      // Main website backend cannot include these fields
      delete discountDetails.maxRedemptions;
      delete discountDetails.maxRedemptionsPerPerson;

      const stripeResponse = await createStripeDiscount({
        community: communityWithStripeProductId,
        code,
        discountDetails,
      });

      await CommunityDiscountModel.findByIdAndUpdate(
        discountObjectId,
        stripeResponse,
        {
          new: true,
        }
      ).lean();
    }),
  ]);
}

function getFinalDiscountPayload({
  discountDetails,
  intervalCount,
  trialDays,
}) {
  const creationPayload = {
    ...discountDetails,
  };
  if (intervalCount > 0) {
    creationPayload.intervalCount = intervalCount;
  }

  if (trialDays > 0) {
    delete creationPayload.intervalCount;
    creationPayload.value = '100';
  }

  return creationPayload;
}

const createCommunityDiscount = async (params = {}) => {
  const {
    communityObjectId,
    code,
    linkedEntities = [],
    intervalCount = 0,
    trialDays = 0,
    bypassEntityUpdate = false,
    session,
  } = params;

  logger.info(`Checking if communityObjectId ${communityObjectId} exists`);
  const community = await validateCommunityById(
    communityObjectId,
    '_id code stripeProductId stripeUsProductId isPaidCommunity payment_methods baseCurrency'
  );

  await validateDiscountCode(code, community.code);

  await validateDiscountValue(params);

  logger.info(
    `Evaluating linkedEntities for the requested discount creation ${JSON.stringify(
      linkedEntities
    )}`
  );

  const { filtered: updatedEntityArr, needStripeDiscount } =
    await filterAndObtainLinkedEntities(linkedEntities, communityObjectId);

  logger.info(
    `Creating discounts for community ${communityObjectId} - ${community.code}`
  );

  let discountDetails = initDiscountDetails({
    community,
    updatedEntityArr,
    params,
  });

  if (needStripeDiscount && trialDays === 0 && community.isPaidCommunity) {
    const stripeResponse = await createStripeDiscount({
      community,
      code,
      discountDetails,
    });
    discountDetails = {
      ...discountDetails,
      ...stripeResponse,
      linkedEntities: updatedEntityArr,
    };
  }

  const creationPayload = getFinalDiscountPayload({
    discountDetails,
    intervalCount,
    trialDays,
  });

  logger.info(
    `Creating new discount for community ${
      community.code
    } with ${JSON.stringify(creationPayload)}`
  );

  const [newDiscount] = await CommunityDiscountModel.create(
    [creationPayload],
    {
      session,
    }
  );

  if (!bypassEntityUpdate) {
    await updateLinkedEntitiesWithDiscountId(
      newDiscount.linkedEntities,
      [newDiscount._id],
      session
    );
  }

  return newDiscount;
};

function generateRandomString(length) {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';

  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    result += characters.charAt(randomIndex);
  }

  return result;
}

const bulkGenerateDiscountCodes = async (params = {}) => {
  const {
    communityObjectId,
    linkedEntities = [],
    intervalCount = 0,
    trialDays = 0,
    numberOfCodes,
    codeLengthWithoutPrefix = 6,
  } = params;

  const community = await CommunityModel.findById(
    communityObjectId
  ).lean();
  if (!community) {
    throw new ParamError('Invalid communityObjectId');
  }

  let prefix = params.prefix ?? '';
  if (!prefix) {
    prefix = generateRandomString(4);
  }

  const deduplicateSet = new Set();
  do {
    const code = generateRandomString(codeLengthWithoutPrefix);
    if (!deduplicateSet.has(`${prefix}${code}`)) {
      deduplicateSet.add(`${prefix}${code}`);
    }
  } while (deduplicateSet.size < numberOfCodes);

  const { filtered: updatedEntityArr, needStripeDiscount } =
    await filterAndObtainLinkedEntities(linkedEntities, communityObjectId);

  const promises = [];
  const discountDocuments = [];
  for (const code of deduplicateSet) {
    // eslint-disable-next-line no-await-in-loop
    promises.push(async () => {
      const innerParams = { ...params };
      innerParams.code = code;
      let discountDetails = initDiscountDetails({
        community,
        updatedEntityArr,
        params: innerParams,
      });

      if (needStripeDiscount && trialDays === 0) {
        const stripeResponse = await createStripeDiscount({
          community,
          code,
          discountDetails,
        });
        discountDetails = {
          ...discountDetails,
          ...stripeResponse,
          linkedEntities: updatedEntityArr,
        };
      }

      const creationPayload = getFinalDiscountPayload({
        discountDetails,
        intervalCount,
        trialDays,
      });
      creationPayload.code = code;

      discountDocuments.push(creationPayload);
    });
  }
  await Promise.all(promises.map((promise) => promise()));
  const discounts = await CommunityDiscountModel.insertMany(
    discountDocuments
  );
  if (discounts.length > 0) {
    await updateLinkedEntitiesWithDiscountId(
      discounts[0].linkedEntities,
      discounts.map((discount) => discount._id)
    );
  }
  return discounts.map((discount) => discount.code);
};

const checkIfDiscountInUsed = async (discount, community) => {
  const [addonTransactions, purchaseTransactions] = await Promise.all([
    AddonTransactionModel.findOne({
      communityObjectId: community._id,
      'payment_details.status': { $ne: 'incomplete' },
      discount: discount._id.toString(),
    })
      .select('_id')
      .lean(),
    PurchaseTransactionModel.findOne({
      community_code: community.code,
      'payment_details.complete_payment': 1,
      discountTransactionId: discount._id,
    })
      .select('_id')
      .lean(),
  ]);

  return !!addonTransactions || !!purchaseTransactions;
};

const removeCommunityDiscount = async (discountId, communityId) => {
  const discount = await CommunityDiscountModel.findById(
    discountId
  ).lean();

  if (!discount) {
    throw new ResourceNotFoundError('Discount not found');
  }

  const community = await CommunityModel.findOne({
    _id: communityId,
    code: discount.communityCode,
    isActive: true,
  })
    .select('_id code isActive')
    .lean();

  if (!community) {
    throw new ResourceNotFoundError('Community not found');
  }

  const isDiscountInUsed = await checkIfDiscountInUsed(
    discount,
    community
  );

  if (isDiscountInUsed) {
    throw new ParamError('Discount is still in used! Cannot be removed!');
  }

  const query = { discountsApplied: new ObjectId(discountId) };
  const pullDiscountAppliedQuery = {
    $pullAll: {
      discountsApplied: [new ObjectId(discountId)],
    },
  };

  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  try {
    const [deletedDiscount, newCommunity, folders, events, programs] =
      await Promise.all([
        CommunityDiscountModel.deleteOne(
          {
            _id: new ObjectId(discountId),
          },
          { session }
        ),
        CommunityModel.updateMany(
          { _id: community._id, ...query },
          pullDiscountAppliedQuery,
          { session }
        ),
        CommunityFolderModel.updateMany(query, pullDiscountAppliedQuery, {
          session,
        }),
        CommunityEventModel.updateMany(query, pullDiscountAppliedQuery, {
          session,
        }),
        ProgramModel.updateMany(query, pullDiscountAppliedQuery, {
          session,
        }),
      ]);

    await session.commitTransaction();
    return {
      deletedDiscount,
      community: newCommunity,
      folders,
      events,
      programs,
    };
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }
};

module.exports = {
  validateDiscountCode,
  validateCommunityById,
  discountValidationForEntities,
  discountCreationForEntities,
  getDiscountsForCommunity,
  getOneDiscountById,
  createCommunityDiscount,
  updateCommunityDiscount,
  bulkGenerateDiscountCodes,
  retrieveAllDiscountsRelatedToEntity,
  generateStripeDiscountForCommunitySwitchToPaid,
  removeCommunityDiscount,
};
