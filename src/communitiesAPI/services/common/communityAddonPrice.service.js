const logger = require('../../../services/logger.service');
const {
  getCountryFromIP,
} = require('../../../services/countryFromIP/countryFromIP.service');
const {
  DEFAULT_COUNTRY,
  DEFAULT_CURRENCY,
  PRICE_TYPE,
  PURCHASE_TYPE,
} = require('../../../constants/common');
const FeeService = require('./fee.service');
const {
  retrievePaymentFeeStructure,
} = require('../../../services/config/paymentFeeStructureConfig.service');
const { PassOnConfigService } = require('../../../services/config/index');

const {
  latamCountriesArray,
  COUNTRY_CREATED,
} = require('../../constants');

const {
  getCountryInfoByCountryFromDB,
} = require('../../../services/countryInfoMapping/countryInfoMapping.service');
const CommunityModel = require('../../models/community.model');
const PaymentBackendRpc = require('../../../rpc/paymentBackend');
const {
  normalizeAmountByCurrency,
  normalizeAndRoundAmountByCurrency,
} = require('../../../utils/currency.util');
const {
  ResourceNotFoundError,
  ParamError,
  ToUserError,
  InternalError,
} = require('../../../utils/error.util');
const { GENERIC_ERROR } = require('../../../constants/errorCode');
const {
  USA_CUSTOM_BASE_PAYOUT_FEE_START_DATE,
} = require('../../../config');

function calculateDiscountedAmount(amount, currency, discount) {
  if (!discount) {
    return amount;
  }

  const { value, type } = discount;
  let discountAmount = 0;
  if (type === 'percentage') {
    discountAmount = Math.ceil((value / 100) * amount);
  } else if (type === 'amount') {
    discountAmount = value;
  }
  const discountedAmount = normalizeAmountByCurrency(
    amount - discountAmount,
    currency
  );

  return discountedAmount;
}

async function retrieveConversionRate(fromCurrency, toCurrency) {
  // Get conversion rate from entity currency to local currency
  const paymentBackendRpc = new PaymentBackendRpc();
  await paymentBackendRpc.init();
  const conversionRateData = await paymentBackendRpc.getConversionRate(
    fromCurrency,
    toCurrency
  );

  const conversionRate = conversionRateData.conversionRate;

  return conversionRate;
}

function validateFlexiblePricing(
  addon,
  selectedAmount,
  minAmountInLocalCurrency
) {
  const isFlexiblePricing =
    addon.pricingConfig?.priceType === PRICE_TYPE.FLEXIBLE;

  if (!isFlexiblePricing) {
    if (selectedAmount != null) {
      throw new ParamError(
        'Selected amount not available for fixed pricing'
      );
    }

    return;
  }

  if (addon.pricingConfig.minAmount == null) {
    throw new InternalError(
      'Flexible pricing config do not have min amount'
    );
  }

  if (selectedAmount == null) {
    throw new ParamError('Selected amount needed for flexible pricing');
  }

  if (!Number.isInteger(selectedAmount)) {
    throw new ParamError('Selected amount need to be a valid number');
  }

  if (selectedAmount < minAmountInLocalCurrency) {
    throw new ToUserError(
      'Selected amount is below the minimum allowed. Please choose a higher amount.',
      GENERIC_ERROR.SELECTED_AMOUNT_LESS_THAN_MIN_AMOUNT
    );
  }
}

async function getLocalizePrice(
  addon,
  community,
  localCurrency,
  quantity,
  selectedAmount
) {
  let itemPrice;
  let itemCurrency;
  let toLocalCurrencyConversionRate;
  let toLocalCurrencyConversionRateBeforeMarkup;
  let localItemPrice;
  let toBaseCurrencyConversionRate;
  // For the item that has different currency than community base currency (eq. ZERO LINK)
  // We will need to convert the amount back to base currency
  if (addon.currency === community.baseCurrency) {
    // For zero link, if the CM set amount is in base currency
    // baseCurrency = USD
    // addon.currency = USD
    // itemCurrency = USD
    // localCurrency = USD (because zero link doesnt do localize conversion)

    // For other product (folder/event/challenges)
    // baseCurrency = USD
    // addon.currency = USD
    // itemCurrency = USD
    // localCurrency = SGD (if member is in SG)

    // Example: addon currency = USD, local currency = SGD
    // USD to SGD
    // toLocalCurrencyConversionRateBeforeMarkup = 1.296200133
    // toLocalCurrencyConversionRate = 1.296200133 * 1.03 = 1.335086136
    toLocalCurrencyConversionRateBeforeMarkup =
      await retrieveConversionRate(addon.currency, localCurrency);

    toLocalCurrencyConversionRate =
      toLocalCurrencyConversionRateBeforeMarkup;
    if (toLocalCurrencyConversionRate !== 1) {
      toLocalCurrencyConversionRate *= 1.03;
    }

    // If a selected amount exists, convert it back to the base currency since it is already in the local currency.
    if (selectedAmount != null) {
      // toBaseCurrencyConversionRate (SGD to USD) = 1 / 1.335086136
      toBaseCurrencyConversionRate = 1 / toLocalCurrencyConversionRate;

      localItemPrice = selectedAmount * quantity;

      itemPrice =
        normalizeAndRoundAmountByCurrency(
          selectedAmount * toBaseCurrencyConversionRate,
          addon.currency
        ) * quantity;
      itemCurrency = addon.currency;
    } else {
      itemPrice = addon.amount * quantity;
      itemCurrency = addon.currency;

      localItemPrice =
        normalizeAndRoundAmountByCurrency(
          addon.amount * toLocalCurrencyConversionRate,
          localCurrency
        ) * quantity;
    }
  } else {
    // Only for zero link, if the CM set amount is NOT in base currency
    // baseCurrency = USD
    // addon.currency = SGD -> zero link currency
    // localCurrency = SGD
    // ---> need to convert itemPrice back to USD (for revenue calculation)

    [toBaseCurrencyConversionRate, toLocalCurrencyConversionRate] =
      await Promise.all([
        retrieveConversionRate(addon.currency, community.baseCurrency),
        retrieveConversionRate(community.baseCurrency, addon.currency),
      ]);

    itemPrice =
      normalizeAndRoundAmountByCurrency(
        (selectedAmount ?? addon.amount) * toBaseCurrencyConversionRate,
        localCurrency
      ) * quantity;
    itemCurrency = community.baseCurrency;
    localItemPrice = (selectedAmount ?? addon.amount) * quantity;
  }
  return {
    itemPrice,
    itemCurrency,
    toLocalCurrencyConversionRate,
    toLocalCurrencyConversionRateBeforeMarkup,
    localItemPrice,
    toBaseCurrencyConversionRate,
  };
}

async function retrieveFlexiblePricing({
  addon,
  localCurrency,
  quantity,
  selectedAmount,
  community,
}) {
  const toLocalCurrencyConversionRateBeforeMarkup =
    await retrieveConversionRate(addon.currency, localCurrency);
  let toLocalCurrencyConversionRate =
    toLocalCurrencyConversionRateBeforeMarkup;
  if (toLocalCurrencyConversionRate !== 1) {
    toLocalCurrencyConversionRate *= 1.03;
  }

  const minAmount = addon.pricingConfig.minAmount;
  const suggestedAmount = addon.pricingConfig.suggestedAmount;

  const minAmountInLocalCurrency = normalizeAndRoundAmountByCurrency(
    minAmount * toLocalCurrencyConversionRate,
    localCurrency
  );
  const suggestAmountInLocalCurrency = normalizeAndRoundAmountByCurrency(
    suggestedAmount * toLocalCurrencyConversionRate,
    localCurrency
  );

  if (selectedAmount != null) {
    validateFlexiblePricing(
      addon,
      selectedAmount,
      minAmountInLocalCurrency
    );

    const {
      itemPrice,
      itemCurrency,
      toBaseCurrencyConversionRate,
      localItemPrice,
    } = await getLocalizePrice(
      addon,
      community,
      localCurrency,
      quantity,
      selectedAmount
    );

    return {
      amount: itemPrice,
      currency: itemCurrency,
      localAmount: localItemPrice,
      localCurrency,
      quantity,
      toLocalCurrencyConversionRate,
      toLocalCurrencyConversionRateBeforeMarkup,
      toBaseCurrencyConversionRate,
      minAmount: minAmountInLocalCurrency,
    };
  }

  const {
    itemPrice,
    itemCurrency,
    toBaseCurrencyConversionRate,
    localItemPrice,
  } = await getLocalizePrice(
    addon,
    community,
    localCurrency,
    quantity,
    Math.max(suggestAmountInLocalCurrency, minAmountInLocalCurrency)
  );

  return {
    amount: itemPrice,
    currency: itemCurrency,
    localAmount: localItemPrice,
    localCurrency,
    quantity,
    toLocalCurrencyConversionRate,
    toLocalCurrencyConversionRateBeforeMarkup,
    toBaseCurrencyConversionRate,
    minAmount: minAmountInLocalCurrency,
  };
}

async function retrieveCountryWisePricing({
  addon,
  localCurrency,
  quantity,
}) {
  const countryWisePrice = addon.countryWisePrice;

  const localPrice = countryWisePrice?.find(
    (item) =>
      item.currency === localCurrency &&
      !item.localiseBasePrice &&
      item.amount != null
  );

  if (localPrice?.amount == null) {
    return;
  }

  const localItemPrice = localPrice.amount * quantity;

  const [toLocalCurrencyConversionRate, toAddonCurrencyConversionRate] =
    await Promise.all([
      retrieveConversionRate(addon.currency, localCurrency),
      retrieveConversionRate(localCurrency, addon.currency),
    ]);

  const itemPrice =
    normalizeAndRoundAmountByCurrency(
      localPrice.amount * toAddonCurrencyConversionRate,
      addon.currency
    ) * quantity;

  return {
    amount: itemPrice,
    currency: addon.currency,
    localAmount: localItemPrice,
    localCurrency,
    quantity,
    toLocalCurrencyConversionRate,
  };
}

async function retrieveFixedPricing({
  addon,
  localCurrency,
  quantity,
  community,
}) {
  const {
    itemPrice,
    itemCurrency,
    toLocalCurrencyConversionRate,
    toLocalCurrencyConversionRateBeforeMarkup,
    toBaseCurrencyConversionRate,
    localItemPrice,
  } = await getLocalizePrice(addon, community, localCurrency, quantity);

  return {
    amount: itemPrice,
    currency: itemCurrency,
    localAmount: localItemPrice,
    localCurrency,
    quantity,
    toLocalCurrencyConversionRate,
    toLocalCurrencyConversionRateBeforeMarkup,
    toBaseCurrencyConversionRate,
  };
}

async function retrieveAddonPrice({
  addon,
  community,
  localCurrency,
  quantity,
  selectedAmount,
  purchaseType,
  countryInfo,
}) {
  const { pricingConfig } = addon;

  // Disable the localized addon price when
  // 1. Community config has toggle off the localizeAddonPrice
  // 2. Zerolink
  // 3. Community's base currency is INR
  // 4. Member's local currency is not supported by us
  let localizeAddonPrice = community?.config?.localizeAddonPrice ?? true;
  switch (purchaseType) {
    case PURCHASE_TYPE.ZERO_LINK:
      // No localize pricing for zerolink
      localizeAddonPrice = false;
      break;
    default:
  }
  if (addon.currency === 'INR') {
    localizeAddonPrice = false;
  } else if (!countryInfo?.localisePrice) {
    localizeAddonPrice = false;
  }

  const selectedCurrency = localizeAddonPrice
    ? localCurrency
    : addon.currency;

  const isFlexiblePricing =
    pricingConfig?.priceType === PRICE_TYPE.FLEXIBLE;

  if (isFlexiblePricing) {
    const pricing = await retrieveFlexiblePricing({
      addon,
      localCurrency: selectedCurrency,
      quantity,
      selectedAmount,
      community,
    });

    return pricing;
  }

  const countryWisePricing = await retrieveCountryWisePricing({
    addon,
    localCurrency,
    quantity,
  });

  if (countryWisePricing) {
    return countryWisePricing;
  }

  const pricing = await retrieveFixedPricing({
    addon,
    localCurrency: selectedCurrency,
    quantity,
    community,
  });

  return pricing;
}

const getAddonPriceInLocalCurrency = async (params = {}) => {
  const {
    ip = null,
    addon = null,
    communityObjectId,
    discount,
    quantity = 1,
    selectedAmount,
    purchaseType,
    paymentMethodCountryCode = null,
    paymentProvider = null,
  } = params;
  const country = (await getCountryFromIP({ ip })) || DEFAULT_COUNTRY;

  const [countryInfo, community] = await Promise.all([
    getCountryInfoByCountryFromDB(country),
    CommunityModel.findById(communityObjectId, {
      createdAt: 1,
      passOnPaymentGatewayFee: 1,
      passOnTakeRate: 1,
      payoutFeeConfigs: 1,
      baseCurrency: 1,
      config: 1,
      basePayoutFeeConfigs: 1,
      countryCreatedIn: 1,
    }).lean(),
  ]);

  if (!community) {
    throw new ResourceNotFoundError('Community not found');
  }

  // Default to USD if no localize price
  const localCurrency = countryInfo?.localisePrice
    ? countryInfo.currencyCode
    : DEFAULT_CURRENCY;

  const baseCurrency =
    community.baseCurrency?.toUpperCase() ?? DEFAULT_CURRENCY;

  const [paymentFeeStructure] = await Promise.all([
    retrievePaymentFeeStructure({
      baseCurrency,
      purchaseType,
      planType: community.config?.planType,
    }),
  ]);

  // Use the pass on fee toggle from product level before community level
  // Lets say [zero link] has own toggle for pass on, then we should use product level one
  const passOnTakeRate =
    addon.passOnTakeRate ?? community.passOnTakeRate ?? false;
  const passOnPaymentGatewayFee =
    addon.passOnPaymentGatewayFee ??
    community.passOnPaymentGatewayFee ??
    false;

  const effectivePayoutFeeConfig =
    FeeService.retrieveEffectivePayoutFeeConfig(
      community.payoutFeeConfigs,
      new Date(),
      purchaseType
    );

  const effectiveBasePayoutFeeConfig =
    FeeService.retrieveEffectivePayoutFeeConfig(
      community.basePayoutFeeConfigs,
      new Date(),
      purchaseType
    );

  // Check if its latam country
  if (!effectiveBasePayoutFeeConfig) {
    if (
      latamCountriesArray.includes(community.countryCreatedIn) &&
      ['USD', 'EUR'].includes(baseCurrency)
    ) {
      throw new Error('No base fee config found');
    } else if (
      community.countryCreatedIn === COUNTRY_CREATED.UNITED_STATES &&
      ['USD'].includes(baseCurrency)
    ) {
      throw new Error('No base fee config found');
    }
  }

  if (addon.currency && addon.currency !== baseCurrency) {
    if (purchaseType !== PURCHASE_TYPE.ZERO_LINK) {
      throw new Error('Addon Currency should be in baseCurrency!');
    }
  }

  const addonPrice = await retrieveAddonPrice({
    addon,
    community,
    localCurrency,
    quantity: parseInt(quantity, 10),
    selectedAmount:
      selectedAmount != null ? parseInt(selectedAmount, 10) : null,
    purchaseType,
    countryInfo,
  });

  const {
    checkoutAmount: checkoutAmountInLocalCurrency,
    internationalFee,
    feeCalculationVersion,
    hasInternationalFee,
  } = FeeService.getCheckoutAmountBreakdown({
    itemPrice: addonPrice.localAmount,
    currency: addonPrice.localCurrency,
    communityCreatedAt: community.createdAt,
    isAddonPayment: true,
    paymentFeeStructure,
    basePayoutFeeConfig: effectiveBasePayoutFeeConfig,
    customPayoutFeeConfig: effectivePayoutFeeConfig,
    passOnTakeRate,
    passOnPaymentGatewayFee,
    toCurrencyConversionRate: addonPrice.toLocalCurrencyConversionRate,
    paymentMethodCountryCode,
    paymentProvider,
    communityCountry: community.countryCreatedIn,
    baseCurrency,
    itemPriceInBaseCurrency: addonPrice.amount,
  });

  const priceDetail = {
    quantity: addonPrice.quantity,
    amount: addonPrice.amount, // Item price
    currency: addonPrice.currency, // Item price currency
    localAmount: addonPrice.localAmount,
    localCurrency: addonPrice.localCurrency,
    discountAmount: 0, // Discount applied to item price
    localDiscountAmount: 0,
    originalCheckoutAmount: checkoutAmountInLocalCurrency, // Member Pay before discount
    checkoutAmount: checkoutAmountInLocalCurrency, // Member Pay
    checkoutCurrency: addonPrice.localCurrency,
    feeDetails: {
      processingFee:
        checkoutAmountInLocalCurrency - addonPrice.localAmount,
      originalProcessingFee:
        checkoutAmountInLocalCurrency - addonPrice.localAmount, // Fee before discount
      internationalFee,
    },
    minAmount: addonPrice.minAmount,
    feeCalculationVersion,
    hasInternationalFee,
    paymentMethodCountryCode,
    conversionRateBeforeMarkup:
      addonPrice.toLocalCurrencyConversionRateBeforeMarkup,
    conversionRate: addonPrice.toLocalCurrencyConversionRate,
  };

  if (discount) {
    const discountedItemPrice = calculateDiscountedAmount(
      addonPrice.amount,
      addonPrice.currency,
      discount
    );

    const discountedItemPriceInLocalCurrency = calculateDiscountedAmount(
      addonPrice.localAmount,
      addonPrice.localCurrency,
      discount
    );

    const {
      checkoutAmount: discountedCheckoutAmountInLocalCurrency,
      internationalFee: discountedInternationalFee,
    } = FeeService.getCheckoutAmountBreakdown({
      itemPrice: discountedItemPriceInLocalCurrency,
      currency: addonPrice.localCurrency,
      communityCreatedAt: community.createdAt,
      isAddonPayment: true,
      paymentFeeStructure,
      basePayoutFeeConfig: effectiveBasePayoutFeeConfig,
      customPayoutFeeConfig: effectivePayoutFeeConfig,
      passOnTakeRate,
      passOnPaymentGatewayFee,
      toCurrencyConversionRate: addonPrice.toLocalCurrencyConversionRate,
      communityCountry: community.countryCreatedIn,
      baseCurrency,
    });

    priceDetail.discountAmount = addonPrice.amount - discountedItemPrice;
    priceDetail.localDiscountAmount =
      addonPrice.localAmount - discountedItemPriceInLocalCurrency;
    priceDetail.checkoutAmount = discountedCheckoutAmountInLocalCurrency;
    priceDetail.feeDetails.processingFee =
      discountedCheckoutAmountInLocalCurrency -
      discountedItemPriceInLocalCurrency;
    priceDetail.feeDetails.internationalFee = discountedInternationalFee;
  }

  logger.info(`Processed priceDetail: ${JSON.stringify(priceDetail)}`);
  return priceDetail;
};

module.exports = { getAddonPriceInLocalCurrency };
