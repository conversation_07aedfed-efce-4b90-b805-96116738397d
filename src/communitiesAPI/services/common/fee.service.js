const {
  DEFAULT_PAYMENT_STRUCTURE,
  END_OF_BETA_DATE,
  FEE_CALCULATION_VERSION,
  DEFAULT_COUNTRY_CODE,
  DEFAULT_COUNTRY,
  DEFAULT_CURRENCY,
} = require('../../../constants/common');
const {
  normalizeAndRoundAmountByCurrency,
} = require('../../../utils/currency.util');
const {
  latamCountriesArray,
  COUNTRY_CREATED,
} = require('../../constants');
const logger = require('../../../services/logger.service');
const {
  USA_CUSTOM_BASE_PAYOUT_FEE_START_DATE,
} = require('../../../config');
const {
  retrievePaymentFeeStructure,
} = require('../../../services/config/paymentFeeStructureConfig.service');
const {
  ENTITY_TYPE: PLAN_ENTITY_TYPE,
} = require('../../../services/plan/constants');

const EFFECTIVE_TIME_END = new Date(Date.UTC(3000, 0, 1));

const FEE_PERCENTAGE_FIELD_NAME = {
  REVENUE_SHARE: 'revenueShareInPercentage',
  GST_ON_REVENUE_SHARE: 'gstOnRevenueShareInPercentage',
  GATEWAY_FEE: 'gatewayFeeInPercentage',
  GST_ON_GATEWAY_FEE: 'gstOnGatewayFeeInPercentage',
  WHT_FEE: 'whtFeeInPercentage',
  MIN_GATEWAY_FEE: 'minGatewayFee',
  PROCESSING_FEE: 'processingFee',
  REFUND_PROCESSING_FEE: 'refundProcessingFee',
  INTERNATIONAL_FEE: 'internationalFeeInPercentage',
};

const CUSTOM_BASE_FEE_TYPE = {
  LATAM: 'latam',
  USA: 'usa',
};

function getProductConfigByCurrency(purchaseTypeConfigs, baseCurrency) {
  const purchaseTypeConfigsOfBaseCurrency = {};
  for (const key in purchaseTypeConfigs) {
    if (purchaseTypeConfigs[key]?.[baseCurrency]) {
      purchaseTypeConfigsOfBaseCurrency[key] = {
        ...purchaseTypeConfigs[key]?.[baseCurrency],
      };
    }
  }
  return purchaseTypeConfigsOfBaseCurrency;
}

function retrieveCustomBasePayoutFeeConfig(
  customBaseFeeType,
  baseCurrency,
  paymentFeeStructure,
  targetEffectiveStartTime = null
) {
  const { customLogicFee, internationalFeePercentageByPaymentProvider } =
    paymentFeeStructure;

  const customBaseFeeStructure = customLogicFee?.[customBaseFeeType];

  if (!customBaseFeeStructure) {
    throw new Error('Fee is not found');
  }

  const minGatewayFee =
    customBaseFeeStructure.minGatewayFeeByBaseCurrency[baseCurrency] ?? 0;

  const processingFee =
    customBaseFeeStructure.processingFeeByBaseCurrency[baseCurrency] ?? 0;

  const refundProcessingFee =
    customBaseFeeStructure.refundProcessingFeeByBaseCurrency[
      baseCurrency
    ] ?? 0;

  const purchaseTypeConfigsOfBaseCurrency = getProductConfigByCurrency(
    customBaseFeeStructure.purchaseTypeConfigs,
    baseCurrency
  );

  let effectiveTimeStart = new Date();
  if (targetEffectiveStartTime) {
    effectiveTimeStart = targetEffectiveStartTime;
  }
  const baseFeeStructure = {
    effectiveTimeStart,
    effectiveTimeEnd: EFFECTIVE_TIME_END,
    revenueShareInPercentage:
      customBaseFeeStructure.revenueShareInPercentage,
    gatewayFeeInPercentage: customBaseFeeStructure.gatewayFeeInPercentage,
    minGatewayFee,
    processingFee,
    refundProcessingFee,
    currency: baseCurrency,
    purchaseTypeConfigs: purchaseTypeConfigsOfBaseCurrency,
    internationalFeePercentageByPaymentProvider,
  };

  return baseFeeStructure;
}

exports.retrieveBasePayoutFeeConfigAndOtherSettings = async ({
  community,
  paymentFeeStructure,
  targetEffectiveStartTime = null,
}) => {
  if (!community || !paymentFeeStructure) {
    throw new Error('Community and payment fee structure need to exist');
  }

  const result = {
    feeConfig: {},
    passOnTakeRate: false,
    passOnPaymentGatewayFee: false,
  };

  const { baseCurrency, countryCreatedIn } = community;

  const {
    fee,
    minGatewayFeeByBaseCurrency,
    refundProcessingFeeByBaseCurrency,
    purchaseTypeConfigs,
    internationalFeePercentageByPaymentProvider,
    processingFeeByBaseCurrency,
  } = paymentFeeStructure;

  // For latam community + (USD or EUR)
  if (
    latamCountriesArray.includes(countryCreatedIn) &&
    ['USD', 'EUR'].includes(baseCurrency)
  ) {
    const latamBasePayoutFeeConfig = retrieveCustomBasePayoutFeeConfig(
      CUSTOM_BASE_FEE_TYPE.LATAM,
      baseCurrency,
      paymentFeeStructure,
      targetEffectiveStartTime
    );

    result.feeConfig = latamBasePayoutFeeConfig;

    return result;
  }

  // For US community + USD
  const currentDate = new Date();
  if (
    countryCreatedIn === COUNTRY_CREATED.UNITED_STATES &&
    ['USD'].includes(baseCurrency) &&
    currentDate >= new Date(USA_CUSTOM_BASE_PAYOUT_FEE_START_DATE)
  ) {
    const usaBasePayoutFeeConfig = retrieveCustomBasePayoutFeeConfig(
      CUSTOM_BASE_FEE_TYPE.USA,
      baseCurrency,
      paymentFeeStructure,
      targetEffectiveStartTime
    );

    result.feeConfig = usaBasePayoutFeeConfig;
    result.passOnTakeRate = true;
    result.passOnPaymentGatewayFee = true;

    return result;
  }

  const purchaseTypeConfigsOfBaseCurrency = getProductConfigByCurrency(
    purchaseTypeConfigs,
    baseCurrency
  );

  const basePayoutFeeConfig = {
    effectiveTimeStart: targetEffectiveStartTime ?? new Date(),
    effectiveTimeEnd: EFFECTIVE_TIME_END,
    ...fee,
    processingFee: processingFeeByBaseCurrency,
    minGatewayFee: minGatewayFeeByBaseCurrency,
    refundProcessingFee: refundProcessingFeeByBaseCurrency,
    currency: baseCurrency,
    purchaseTypeConfigs: purchaseTypeConfigsOfBaseCurrency,
    internationalFeePercentageByPaymentProvider,
  };

  result.feeConfig = basePayoutFeeConfig;

  return result;
};

// "volumeTiers":[
//   {"volumeMin":0,"volumeMax":50000,"revenueShareInPercentage":3},
//   {"volumeMin":50000,"volumeMax":150000,"revenueShareInPercentage":2},
//   {"volumeMin":150000,"volumeMax":99999999999,"revenueShareInPercentage":1}
// ]
function getTieredFee(config, itemPrice, feeType) {
  if (config?.volumeTiers) {
    const matchingTier = config.volumeTiers.find(
      (tier) => itemPrice > tier.volumeMin && itemPrice <= tier.volumeMax
    );
    if (matchingTier) {
      return matchingTier[feeType];
    }
  }
  return null;
}

function getFee(
  basePayoutFeeConfig,
  paymentFeeStructure,
  fieldName,
  defaultValue,
  itemPrice,
  paymentProvider = null
) {
  const feeConfigMapper = {
    [FEE_PERCENTAGE_FIELD_NAME.REVENUE_SHARE]: {
      base: basePayoutFeeConfig?.revenueShareInPercentage,
      payment: paymentFeeStructure?.fee?.revenueShareInPercentage,
    },
    [FEE_PERCENTAGE_FIELD_NAME.GST_ON_REVENUE_SHARE]: {
      base: basePayoutFeeConfig?.gstOnRevenueShareInPercentage,
      payment: paymentFeeStructure?.fee?.gstOnRevenueShareInPercentage,
    },
    [FEE_PERCENTAGE_FIELD_NAME.GATEWAY_FEE]: {
      base: basePayoutFeeConfig?.gatewayFeeInPercentage,
      payment: paymentFeeStructure?.fee?.gatewayFeeInPercentage,
    },
    [FEE_PERCENTAGE_FIELD_NAME.GST_ON_GATEWAY_FEE]: {
      base: basePayoutFeeConfig?.gstOnGatewayFeeInPercentage,
      payment: paymentFeeStructure?.fee?.gstOnGatewayFeeInPercentage,
    },
    [FEE_PERCENTAGE_FIELD_NAME.WHT_FEE]: {
      base: basePayoutFeeConfig?.whtFeeInPercentage,
      payment: paymentFeeStructure?.fee?.whtFeeInPercentage,
    },
    [FEE_PERCENTAGE_FIELD_NAME.MIN_GATEWAY_FEE]: {
      base: basePayoutFeeConfig?.minGatewayFee,
      payment: paymentFeeStructure?.minGatewayFeeByBaseCurrency,
    },
    [FEE_PERCENTAGE_FIELD_NAME.PROCESSING_FEE]: {
      base: basePayoutFeeConfig?.processingFee,
      payment: paymentFeeStructure?.processingFeeByBaseCurrency,
    },
    [FEE_PERCENTAGE_FIELD_NAME.REFUND_PROCESSING_FEE]: {
      base: basePayoutFeeConfig?.refundProcessingFee,
      payment: paymentFeeStructure?.refundProcessingFeeByBaseCurrency,
    },
    [FEE_PERCENTAGE_FIELD_NAME.INTERNATIONAL_FEE]: {
      base: basePayoutFeeConfig
        ?.internationalFeePercentageByPaymentProvider?.[
        paymentProvider ?? ''
      ],
      payment:
        paymentFeeStructure?.fee
          ?.internationalFeePercentageByPaymentProvider?.[
          paymentProvider ?? ''
        ],
    },
  };

  const feeConfig = feeConfigMapper[fieldName];

  if (!feeConfig) {
    logger.error(
      `retrieveFee: error: ${fieldName} is not a valid field name`
    );
    throw new Error('Invalid field name for fee config');
  }

  const hasBasePayoutFeeConfig =
    basePayoutFeeConfig && Object.keys(basePayoutFeeConfig).length > 0;
  if (hasBasePayoutFeeConfig) {
    const tieredFee = getTieredFee(
      basePayoutFeeConfig,
      itemPrice,
      fieldName
    );
    return tieredFee ?? feeConfig.base ?? defaultValue;
  }

  const tieredFee = getTieredFee(
    paymentFeeStructure?.fee,
    itemPrice,
    fieldName
  );
  return tieredFee ?? feeConfig.payment ?? defaultValue;
}

exports.getPaymentFeeDetailsBasedOnPassonFields = ({
  currency,
  communityCreatedAt,
  isAddonPayment = false,
  paymentFeeStructure = {},
  basePayoutFeeConfig,
  customPayoutFeeConfig = null,
  passOnTakeRate = false,
  passOnPaymentGatewayFee = false,
  itemPrice = null,
  itemPriceInBaseCurrency = null, // for zerolink case, CM can set any currency, and item price might not be in base currency
  ignorePassOnCondition = false, // For CMP price breakdown calculation, we need to display fee breakdown
  paymentProvider = null, // For international fee calculation
  paymentMethodCountryCode = null, // For international fee calculation
  communityCountry,
  baseCurrency,
}) => {
  const retrieveFee = (fieldName, defaultValue) =>
    getFee(
      basePayoutFeeConfig,
      paymentFeeStructure,
      fieldName,
      defaultValue,
      itemPrice,
      paymentProvider
    );

  const tieredRevenueShare = getTieredFee(
    customPayoutFeeConfig,
    itemPriceInBaseCurrency ?? itemPrice,
    FEE_PERCENTAGE_FIELD_NAME.REVENUE_SHARE
  );
  let revenueShareInPercentage = parseFloat(
    passOnTakeRate || ignorePassOnCondition
      ? tieredRevenueShare ??
          customPayoutFeeConfig?.revenueShareInPercentage ??
          retrieveFee(
            FEE_PERCENTAGE_FIELD_NAME.REVENUE_SHARE,
            DEFAULT_PAYMENT_STRUCTURE.REVENUE_SHARE
          )
      : 0
  );

  const customForAllPaymentProviderFee =
    customPayoutFeeConfig?.paymentProviderFee?.all?.ALL ??
    customPayoutFeeConfig;

  if (!isAddonPayment) {
    const communityCreatedAtDateString = communityCreatedAt
      .toISOString()
      .split('T')[0];

    if (communityCreatedAtDateString < END_OF_BETA_DATE) {
      revenueShareInPercentage = 0;
    }
  }

  const isUsMarket =
    communityCountry === DEFAULT_COUNTRY &&
    baseCurrency === DEFAULT_CURRENCY;

  const hasInternationalFee =
    paymentMethodCountryCode != null
      ? paymentMethodCountryCode !== DEFAULT_COUNTRY_CODE && isUsMarket
      : false;

  const enableInternationalFee =
    passOnPaymentGatewayFee && hasInternationalFee;

  const internationalFeeInPercentage = parseFloat(
    enableInternationalFee
      ? retrieveFee(FEE_PERCENTAGE_FIELD_NAME.INTERNATIONAL_FEE, 0)
      : 0
  );

  if (Object.keys(paymentFeeStructure).length === 0) {
    return {
      revenueShareInPercentage,
      gstOnRevenueShareInPercentage: 0,
      gatewayFeeInPercentage: 0,
      gstOnGatewayFeeInPercentage: 0,
      whtFeeInPercentage: 0,
      minGatewayFee: 0,
      processingFee: 0,
      internationalFeeInPercentage,
      hasInternationalFee,
      currency,
    };
  }

  const gstOnRevenueShareInPercentage = parseFloat(
    passOnTakeRate || ignorePassOnCondition
      ? customForAllPaymentProviderFee?.gstOnRevenueShareInPercentage ??
          retrieveFee(
            FEE_PERCENTAGE_FIELD_NAME.GST_ON_REVENUE_SHARE,
            DEFAULT_PAYMENT_STRUCTURE.GST_ON_REVENUE_SHARE
          )
      : 0
  );

  const tieredGatewayFee = getTieredFee(
    customPayoutFeeConfig,
    itemPriceInBaseCurrency ?? itemPrice,
    FEE_PERCENTAGE_FIELD_NAME.GATEWAY_FEE
  );
  const gatewayFeeInPercentage = parseFloat(
    passOnPaymentGatewayFee || ignorePassOnCondition
      ? tieredGatewayFee ??
          customForAllPaymentProviderFee?.gatewayFeeInPercentage ??
          retrieveFee(
            FEE_PERCENTAGE_FIELD_NAME.GATEWAY_FEE,
            DEFAULT_PAYMENT_STRUCTURE.GATEWAY_FEE
          )
      : 0
  );

  const gstOnGatewayFeeInPercentage = parseFloat(
    passOnPaymentGatewayFee || ignorePassOnCondition
      ? customForAllPaymentProviderFee?.gstFeeInPercentage ??
          retrieveFee(
            FEE_PERCENTAGE_FIELD_NAME.GST_ON_GATEWAY_FEE,
            DEFAULT_PAYMENT_STRUCTURE.GST_ON_GATEWAY_FEE
          )
      : 0
  );

  const whtFeeInPercentage = parseFloat(
    customForAllPaymentProviderFee?.whtFeeInPercentage ??
      retrieveFee(FEE_PERCENTAGE_FIELD_NAME.WHT_FEE, 0)
  );

  const minGatewayFee = parseFloat(
    customForAllPaymentProviderFee?.minGatewayFee ??
      retrieveFee(FEE_PERCENTAGE_FIELD_NAME.MIN_GATEWAY_FEE, 0)
  );

  const processingFee = parseFloat(
    passOnPaymentGatewayFee || ignorePassOnCondition
      ? customForAllPaymentProviderFee?.processingFee ??
          retrieveFee(FEE_PERCENTAGE_FIELD_NAME.PROCESSING_FEE, 0)
      : 0
  );

  // Every community should have base payout fee config to be configured
  // If not, we should pop error, and we do the data fix for it
  const purchaseTypeConfigs =
    customPayoutFeeConfig?.purchaseTypeConfigs ??
    basePayoutFeeConfig?.purchaseTypeConfigs;

  return {
    revenueShareInPercentage,
    gstOnRevenueShareInPercentage,
    gatewayFeeInPercentage,
    gstOnGatewayFeeInPercentage,
    whtFeeInPercentage,
    minGatewayFee,
    processingFee,
    internationalFeeInPercentage,
    hasInternationalFee,
    currency,
    purchaseTypeConfigs,
  };
};

exports.getCommunityPaymentFeeDetails = ({
  currency,
  communityCreatedAt,
  paymentFeeStructure = {},
  customPayoutFeeConfig = null,
  basePayoutFeeConfig = {},
  communityCountry,
  baseCurrency,
}) => {
  return this.getPaymentFeeDetailsBasedOnPassonFields({
    currency,
    communityCreatedAt,
    isAddonPayment: false,
    paymentFeeStructure,
    customPayoutFeeConfig,
    passOnTakeRate: true,
    passOnPaymentGatewayFee: true,
    basePayoutFeeConfig,
    communityCountry,
    baseCurrency,
  });
};

function calculateFeeWithMinGatewayFee({
  itemPrice,
  gstOnGatewayFeeInPercentage,
  revenueShareInPercentage,
  gstOnRevenueShareInPercentage,
  internationalFeeInPercentage,
  passOnPaymentGatewayFee,
  processingFeeInCurrency,
  minGatewayFeeInCurrency,
  currency,
}) {
  // eg. 7.9% + (1 + 0%) = 7.9%
  const revenueShareInPercentageWithGst =
    revenueShareInPercentage * (1 + gstOnRevenueShareInPercentage / 100);

  // eg. 0.6
  const gatewayFeeAmount = minGatewayFeeInCurrency;

  // eg. 0% * 0.6 = 0
  const gstOnGatewayFeeAmount = normalizeAndRoundAmountByCurrency(
    (gstOnGatewayFeeInPercentage / 100) * gatewayFeeAmount,
    currency
  );

  // if pass on, 7.9% + 0% = 7.9%
  // else, 0%
  const totalGatewayFeeInPercentage = passOnPaymentGatewayFee
    ? revenueShareInPercentageWithGst + internationalFeeInPercentage
    : 0;

  // if pass on, 100 + 0.3 + 0.6 + 0 = 100.9
  // else, 100
  const amountBeforeGatewayFee = passOnPaymentGatewayFee
    ? itemPrice +
      processingFeeInCurrency +
      gatewayFeeAmount +
      gstOnGatewayFeeAmount
    : itemPrice;

  // if pass on, 100.9 / (1 - 7.9%) = 100.9 / 0.9211 = 109.56
  // else, 100 / (1 - 0%) = 100 / 1 = 100
  const checkoutAmount = normalizeAndRoundAmountByCurrency(
    amountBeforeGatewayFee / (1 - totalGatewayFeeInPercentage / 100),
    currency
  );

  // if pass on, 109.56 * 7.9% = 8.66
  // else, 100 * 7.9% = 7.9
  const revenueShareAmount = normalizeAndRoundAmountByCurrency(
    (revenueShareInPercentage / 100) * checkoutAmount,
    currency
  );

  // if pass on, 8.66 * 0% = 0
  // else, 7.9 * 0% = 0
  const gstOnRevenueShareAmount = normalizeAndRoundAmountByCurrency(
    (gstOnRevenueShareInPercentage / 100) * revenueShareAmount,
    currency
  );

  // if pass on, 109.56 * 0% = 0
  // else, 100 * 0% = 0
  const internationalFeeAmount = normalizeAndRoundAmountByCurrency(
    (internationalFeeInPercentage / 100) * checkoutAmount,
    currency
  );

  // if pass on, 100 + 8.66 + 0 + 0.6 + 0 + 0.3 + 0 = 109.56
  // else, 100
  const finalCheckoutAmount = passOnPaymentGatewayFee
    ? itemPrice +
      revenueShareAmount +
      gstOnRevenueShareAmount +
      gatewayFeeAmount +
      gstOnGatewayFeeAmount +
      processingFeeInCurrency +
      internationalFeeAmount
    : checkoutAmount;

  return {
    revenueShareAmount, // pass on: 8.66, non pass on: 7.9
    gstOnRevenueShareAmount, // pass on: 0, non pass on: 0
    gatewayFee: gatewayFeeAmount, // pass on: 0.6, non pass on: 0.6
    gstOnGatewayFee: gstOnGatewayFeeAmount, // pass on: 0, non pass on: 0
    processingFee: processingFeeInCurrency, // pass on: 0.3, non pass on: 0.3
    internationalFee: internationalFeeAmount, // pass on: 0, non pass on: 0
    checkoutAmount: finalCheckoutAmount, // pass on: 109.56, non pass on: 100
  };
}

function calculateFee({
  itemPrice,
  gatewayFeeInPercentage,
  gstOnGatewayFeeInPercentage,
  revenueShareInPercentage,
  gstOnRevenueShareInPercentage,
  internationalFeeInPercentage,
  passOnPaymentGatewayFee,
  processingFeeInCurrency,
  currency,
}) {
  // eg. 7.9% + (1 + 0%) = 7.9%
  const revenueShareInPercentageWithGst =
    revenueShareInPercentage * (1 + gstOnRevenueShareInPercentage / 100);

  // eg. 4.4% * (1 + 0%) = 4.4%
  const gatewayFeeInPercentageWithGst =
    gatewayFeeInPercentage * (1 + gstOnGatewayFeeInPercentage / 100);

  // if pass on, 7.9% + 4.4% + 0% = 12.3%
  // else, 0%
  const totalGatewayFeeInPercentage = passOnPaymentGatewayFee
    ? revenueShareInPercentageWithGst +
      gatewayFeeInPercentageWithGst +
      internationalFeeInPercentage
    : 0;

  // if pass on, 100 + 0.3 = 100.3
  // else, 100
  const amountBeforeGatewayFee = passOnPaymentGatewayFee
    ? itemPrice + processingFeeInCurrency
    : itemPrice;

  // if pass on, 100.3 / (1 - 12.3%) = 100.3 / 0.877 = 114.37
  // else, 100
  const checkoutAmount = normalizeAndRoundAmountByCurrency(
    amountBeforeGatewayFee / (1 - totalGatewayFeeInPercentage / 100),
    currency
  );

  // if pass on, 114.37 * 7.9% = 9.04
  // else, 100 * 7.9% = 7.9
  const revenueShareAmount = normalizeAndRoundAmountByCurrency(
    (revenueShareInPercentage / 100) * checkoutAmount,
    currency
  );

  // if pass on, 9.04 * 0% = 0
  // else, 7.9 * 0% = 0
  const gstOnRevenueShareAmount = normalizeAndRoundAmountByCurrency(
    (gstOnRevenueShareInPercentage / 100) * revenueShareAmount,
    currency
  );

  // if pass on, 114.37 * 0% = 0
  // else, 100 * 0% = 0
  const internationalFeeAmount = normalizeAndRoundAmountByCurrency(
    (internationalFeeInPercentage / 100) * checkoutAmount,
    currency
  );

  // if pass on, 114.37 * 4.4% = 5.04
  // else, 100 * 4.4% = 4.4
  const gatewayFeeAmount = normalizeAndRoundAmountByCurrency(
    (gatewayFeeInPercentage / 100) * checkoutAmount,
    currency
  );

  // if pass on, 5.04 * 0% = 0
  // else, 4.4 * 0% = 0
  const gstOnGatewayFeeAmount = normalizeAndRoundAmountByCurrency(
    (gstOnGatewayFeeInPercentage / 100) * gatewayFeeAmount,
    currency
  );

  // if pass on, 100 + 9.04 + 5.04 + 0 + 0 + 0.3 + 0 = 114.38
  // else, 100
  const finalCheckoutAmount = passOnPaymentGatewayFee
    ? itemPrice +
      revenueShareAmount +
      gstOnRevenueShareAmount +
      gatewayFeeAmount +
      gstOnGatewayFeeAmount +
      processingFeeInCurrency +
      internationalFeeAmount
    : checkoutAmount;

  return {
    revenueShareAmount, // pass on: 9.04, no pass on: 7.9
    gstOnRevenueShareAmount, // pass on: 0, no pass on: 0
    gatewayFee: gatewayFeeAmount, // pass on: 5.04, no pass on: 4.4
    gstOnGatewayFee: gstOnGatewayFeeAmount, // pass on: 0, no pass on: 0
    processingFee: processingFeeInCurrency, // pass on: 0.3, no pass on: 0.3
    internationalFee: internationalFeeAmount, // pass on: 0, no pass on: 0
    checkoutAmount: finalCheckoutAmount, // pass on: 114.38, no pass on: 100
  };
}

exports.getCheckoutAmountBreakdown = ({
  itemPrice,
  currency,
  communityCreatedAt,
  isAddonPayment = false,
  paymentFeeStructure = {},
  basePayoutFeeConfig,
  customPayoutFeeConfig = null,
  passOnTakeRate = false,
  passOnPaymentGatewayFee = false,
  toCurrencyConversionRate = 1,
  ignorePassOnCondition = false, // For CMP price breakdown calculation, we need to display fee breakdown
  paymentProvider = null, // For international fee calculation
  paymentMethodCountryCode = null, // For international fee calculation
  communityCountry,
  baseCurrency,
  itemPriceInBaseCurrency, // this is for zerolink tiered fee calculation
}) => {
  const feeCalculationVersion = FEE_CALCULATION_VERSION.V2;

  if (itemPrice === 0) {
    return {
      itemPrice: 0,
      checkoutAmount: 0,
      revenueShare: 0,
      gstOnRevenue: 0,
      gatewayFee: 0,
      gst: 0,
      processingFee: 0,
      feeCalculationVersion,
    };
  }

  const {
    revenueShareInPercentage,
    gstOnRevenueShareInPercentage,
    gatewayFeeInPercentage,
    gstOnGatewayFeeInPercentage,
    minGatewayFee,
    processingFee,
    internationalFeeInPercentage,
    hasInternationalFee,
  } = this.getPaymentFeeDetailsBasedOnPassonFields({
    currency,
    communityCreatedAt,
    isAddonPayment,
    paymentFeeStructure,
    basePayoutFeeConfig,
    customPayoutFeeConfig,
    passOnTakeRate,
    passOnPaymentGatewayFee,
    itemPrice,
    itemPriceInBaseCurrency,
    ignorePassOnCondition,
    paymentProvider,
    paymentMethodCountryCode,
    communityCountry,
    baseCurrency,
  });

  const normalizedItemPrice = normalizeAndRoundAmountByCurrency(
    itemPrice,
    currency
  );

  const processingFeeInCurrency = normalizeAndRoundAmountByCurrency(
    processingFee * toCurrencyConversionRate,
    currency
  );

  const minGatewayFeeInCurrency = normalizeAndRoundAmountByCurrency(
    minGatewayFee * toCurrencyConversionRate,
    currency
  );

  let fee = calculateFee({
    itemPrice: normalizedItemPrice,
    gatewayFeeInPercentage,
    gstOnGatewayFeeInPercentage,
    revenueShareInPercentage,
    gstOnRevenueShareInPercentage,
    internationalFeeInPercentage,
    passOnPaymentGatewayFee,
    processingFeeInCurrency,
    currency,
  });

  if (fee.gatewayFee < minGatewayFeeInCurrency) {
    fee = calculateFeeWithMinGatewayFee({
      itemPrice: normalizedItemPrice,
      gstOnGatewayFeeInPercentage,
      revenueShareInPercentage,
      gstOnRevenueShareInPercentage,
      internationalFeeInPercentage,
      passOnPaymentGatewayFee,
      processingFeeInCurrency,
      minGatewayFeeInCurrency,
      currency,
    });
  }

  const amountBreakdown = {
    itemPrice: normalizedItemPrice,
    checkoutAmount: fee.checkoutAmount,
    revenueShare: fee.revenueShareAmount,
    gstOnRevenue: fee.gstOnRevenueShareAmount,
    gatewayFee: fee.gatewayFee,
    gst: fee.gstOnGatewayFee,
    processingFee: fee.processingFee,
    internationalFee: fee.internationalFee,
    feeCalculationVersion,
    hasInternationalFee,
  };

  return amountBreakdown;
};

exports.retrieveEffectivePayoutFeeConfig = (
  payoutFeeConfigs,
  selectedDate = new Date(),
  purchaseType = ''
) => {
  const selectedDateString = selectedDate.toISOString();

  const effectivePayoutFeeConfig = payoutFeeConfigs?.find(
    (payoutFeeConfig) => {
      const { effectiveTimeStart, effectiveTimeEnd } = payoutFeeConfig;

      return (
        selectedDateString >= effectiveTimeStart.toISOString() &&
        selectedDateString < effectiveTimeEnd.toISOString()
      );
    }
  );

  const feeForProduct =
    effectivePayoutFeeConfig?.purchaseTypeConfigs?.[purchaseType];

  // assign to purchase type config as international fee config is in the root level
  if (feeForProduct) {
    feeForProduct.internationalFeePercentageByPaymentProvider =
      effectivePayoutFeeConfig.internationalFeePercentageByPaymentProvider;
  }

  return feeForProduct ?? effectivePayoutFeeConfig;
};

exports.getProAndDefaultFeeRate = async ({ community }) => {
  if (!community) {
    return {};
  }

  const [
    paymentFeeStructure,
    defaultPaymentFeeStructure,
    proPaymentFeeStructure,
    platinumPaymentFeeStructure,
  ] = await Promise.all([
    retrievePaymentFeeStructure({
      baseCurrency: community.baseCurrency,
      planType: community.config?.planType,
    }),
    retrievePaymentFeeStructure({
      baseCurrency: community.baseCurrency,
      planType: null,
    }),
    retrievePaymentFeeStructure({
      baseCurrency: community.baseCurrency,
      planType: PLAN_ENTITY_TYPE.PRO,
    }),
    retrievePaymentFeeStructure({
      baseCurrency: community.baseCurrency,
      planType: PLAN_ENTITY_TYPE.PLATINUM,
    }),
  ]);
  const effectivePayoutFeeConfig = this.retrieveEffectivePayoutFeeConfig(
    community.payoutFeeConfigs
  );

  const effectiveBasePayoutFeeConfig =
    this.retrieveEffectivePayoutFeeConfig(community.basePayoutFeeConfigs);

  const paymentDetails = this.getCommunityPaymentFeeDetails({
    currency: community.baseCurrency,
    communityCreatedAt: community.createdAt,
    paymentFeeStructure,
    customPayoutFeeConfig: effectivePayoutFeeConfig,
    basePayoutFeeConfig: effectiveBasePayoutFeeConfig,
    communityCountry: community.countryCreatedIn,
    baseCurrency: community.baseCurrency,
  });

  const { feeConfig: basePayoutFeeConfigForNonPro } =
    await this.retrieveBasePayoutFeeConfigAndOtherSettings({
      community,
      paymentFeeStructure: defaultPaymentFeeStructure,
    });
  const { feeConfig: basePayoutFeeConfigForPro } =
    await this.retrieveBasePayoutFeeConfigAndOtherSettings({
      community,
      paymentFeeStructure: proPaymentFeeStructure,
    });
  const { feeConfig: basePayoutFeeConfigForPlatinum } =
    await this.retrieveBasePayoutFeeConfigAndOtherSettings({
      community,
      paymentFeeStructure: platinumPaymentFeeStructure,
    });

  const defaultPaymentDetails = this.getCommunityPaymentFeeDetails({
    currency: community.baseCurrency,
    communityCreatedAt: community.createdAt,
    paymentFeeStructure: defaultPaymentFeeStructure,
    communityCountry: community.countryCreatedIn,
    baseCurrency: community.baseCurrency,
    basePayoutFeeConfig: basePayoutFeeConfigForNonPro,
    customPayoutFeeConfig: effectivePayoutFeeConfig,
  });

  const proPaymentDetails = this.getCommunityPaymentFeeDetails({
    currency: community.baseCurrency,
    communityCreatedAt: community.createdAt,
    paymentFeeStructure: proPaymentFeeStructure,
    communityCountry: community.countryCreatedIn,
    baseCurrency: community.baseCurrency,
    basePayoutFeeConfig: basePayoutFeeConfigForPro,
    customPayoutFeeConfig: effectivePayoutFeeConfig,
  });

  const platinumPaymentDetails = this.getCommunityPaymentFeeDetails({
    currency: community.baseCurrency,
    communityCreatedAt: community.createdAt,
    paymentFeeStructure: platinumPaymentFeeStructure,
    communityCountry: community.countryCreatedIn,
    baseCurrency: community.baseCurrency,
    basePayoutFeeConfig: basePayoutFeeConfigForPlatinum,
    customPayoutFeeConfig: effectivePayoutFeeConfig,
  });

  const paymentDetailsByTiers = {
    [`${PLAN_ENTITY_TYPE.PLATINUM.toLowerCase()}`]: platinumPaymentDetails,
    [`${PLAN_ENTITY_TYPE.PRO.toLowerCase()}`]: proPaymentDetails,
    default: defaultPaymentDetails,
  };

  return {
    paymentDetails,
    defaultPaymentDetails,
    proPaymentDetails,
    paymentDetailsByTiers,
  };
};
