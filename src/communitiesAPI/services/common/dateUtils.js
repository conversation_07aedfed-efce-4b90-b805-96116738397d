const { DateTime } = require('luxon');
const {
  getIntervalDurationInMS,
  getNextWeekdayStartDateTimestamp,
} = require('./timeIntervalUtils');

const getSessionTimeIntervals = function (
  startDate,
  endDate,
  frequency = null
) {
  const sessionTimeIntervals = [];
  const dayStrings = ['Sun', 'Mon', 'Tues', 'Wed', 'Thurs', 'Fri', 'Sat'];
  const days = {
    Mon: 1,
    Tues: 2,
    Wed: 3,
    Thurs: 4,
    Fri: 5,
    Sat: 6,
    Sun: 0,
  };

  const sessionDuration = getIntervalDurationInMS(startDate, endDate);

  // get starting time points for the week --> write util getCurrentWeekdayTimestamp
  // loop through the week and calculate weekly session timestamp intervals

  const startTimestamp = new Date(startDate).getTime();
  const endTimestamp = new Date(endDate).getTime();

  // if it is a one-day course
  if (!frequency) {
    sessionTimeIntervals.push({
      start: startTimestamp,
      end: endTimestamp,
    });
    return sessionTimeIntervals;
  }

  const numericFrequency = frequency.map((item) => days[item]);
  const lastFrequencyIndex = numericFrequency.length - 1;

  const firstDayInt = new Date(startDate).getDay();
  const firstDayString = dayStrings[firstDayInt];
  let currentDayIndex = frequency.findIndex(
    (item) => item === firstDayString
  );

  let currentInterval = {
    start: startTimestamp,
    end: startTimestamp + sessionDuration,
  };

  // TODO: remove temp safeguard
  const maxSessions = 50; // SAFEGUARD FOR TESTING
  let sessionCount = 0;

  while (
    currentInterval.start < endTimestamp &&
    sessionCount < maxSessions
  ) {
    sessionTimeIntervals.push(currentInterval);

    // update next day when read from freq
    if (currentDayIndex === lastFrequencyIndex) {
      currentDayIndex = 0;
    } else {
      currentDayIndex++;
    }

    // calculate next start and end
    const nextStart = getNextWeekdayStartDateTimestamp(
      currentInterval.start,
      frequency[currentDayIndex],
      frequency.length < 2
    );
    const nextEnd = nextStart + sessionDuration;
    currentInterval = { start: nextStart, end: nextEnd };
    sessionCount++; // TO DELETE
  }

  return sessionTimeIntervals;
};

const getCurrentUTCTime = () => DateTime.local().setZone('UTC');

// time = Date and = Mon Feb 05 2024 17:01:55 GMT+0530 (India Standard Time)
const convertToUTC = (time, timezone) => {
  return DateTime.fromJSDate(time, { zone: timezone }).setZone('UTC');
};

const convertToUTCForAnyTimezone = (time) => {
  return DateTime.fromJSDate(time).setZone('UTC');
};
module.exports = {
  getSessionTimeIntervals,
  getCurrentUTCTime,
  convertToUTC,
  convertToUTCForAnyTimezone,
};
