// models
const CommunityVideos = require('../../models/communityVideos.model');

// services
const logger = require('../../../services/logger.service');

const yup = require('yup');
const { replacePrivateLinksInVideos } = require('../../utils');
const ObjectId = require('mongoose').Types.ObjectId;

const getCommunityVideos = async (communityObjectId) => {
  try {
    const result = await CommunityVideos.aggregate([
      {
        $match: {
          communityObjectId: new ObjectId(communityObjectId),
          isActive: true,
        },
      },
      {
        $lookup: {
          from: 'videos',
          localField: 'video',
          foreignField: '_id',
          as: 'videos',
        },
      },
      {
        $unwind: {
          path: '$videos',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          tag: 1,
          topicIndex: 1,
          subIndex: 1,
          title: 1,
          topic: 1,
          createdAt: 1,
          shortUrl: 1,
          link: '$videos.link',
          hlsLink: '$videos.hlsLink',
          thumbnail: '$videos.thumbnailLink',
          duration: '$videos.duration',
          subtitles: '$videos.subtitles',
        },
      },
      {
        $sort: {
          topicIndex: 1,
          subIndex: 1,
        },
      },
      {
        $group: {
          _id: '$topicIndex',
          topic: { $first: '$topic' },
          topicIndex: { $first: '$topicIndex' },
          items: { $push: '$$ROOT' },
        },
      },
      { $project: { _id: 1, topic: 1, items: 1, topicIndex: 1 } },
      {
        $sort: {
          topicIndex: 1,
        },
      },
    ]);
    if (!result) {
      throw new Error('No Videos found');
    }
    result.forEach((item) => {
      // eslint-disable-next-line no-param-reassign
      item.items = replacePrivateLinksInVideos(item.items);
    });
    return result;
  } catch (err) {
    logger.error(err.message, err.stack);
    throw err;
  }
};

module.exports = { getCommunityVideos };
