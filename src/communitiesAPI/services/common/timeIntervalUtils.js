const getTimeOffset = (date) => {
  const h = date.getHours();
  const m = date.getMinutes();

  const timeOffset = new Date(0, 0, 0, h, m);

  return timeOffset;
};
const getIntervalDurationInMS = (startDate, endDate) => {
  const startTimestamp = new Date(startDate);
  const endTimestamp = new Date(endDate);

  const startTime = getTimeOffset(startTimestamp).getTime();
  const endTime = getTimeOffset(endTimestamp).getTime();

  const duration = endTime - startTime;

  return duration;
};

const compareIntervalsOverlapping = (a, b) => {
  const [earlierInterval, laterInterval] =
    a.start < b.start ? [a, b] : [b, a];
  return laterInterval.start < earlierInterval.end;
};

const areTimeIntervalsOverlapping = (
  newTimeIntervals,
  existingTimeIntervals
) => {
  const newLength = newTimeIntervals.length;
  const existingLength = existingTimeIntervals.length;
  let overlapping = false;

  for (let i = 0; i < newLength; i++) {
    for (let y = 0; y < existingLength; y++) {
      overlapping = compareIntervalsOverlapping(
        newTimeIntervals[i],
        existingTimeIntervals[y]
      );
      if (overlapping) break;
    }
    if (overlapping) break;
  }

  return overlapping;
};

const daysToAdd = (curr, next) => {
  if (next < curr) {
    return 7 - curr + next;
  }
  return next - curr;
};

const getNextWeekdayStartDateTimestamp = (
  currentDate,
  nextDay,
  isOneSessionPerWeek
) => {
  const days = {
    Mon: 1,
    Tues: 2,
    Wed: 3,
    Thurs: 4,
    Fri: 5,
    Sat: 6,
    Sun: 0,
  };

  const ONE_DAY = 1000 * 60 * 60 * 24;

  const formattedCurrentDate = new Date(currentDate);
  const currentDay = formattedCurrentDate.getDay();
  const nextDayStartTimestamp = isOneSessionPerWeek
    ? currentDate + 7 * ONE_DAY
    : currentDate + daysToAdd(currentDay, days[nextDay]) * ONE_DAY;

  return nextDayStartTimestamp;
};

const getFormattedTimeFromSeconds = (seconds) => {
  // Hours, minutes and seconds
  const hrs = parseInt(seconds / 3600, 10);
  const mins = parseInt((seconds % 3600) / 60, 10);
  const secs = parseInt(seconds % 60, 10);

  // Output like "1:01" or "4:03:59" or "123:03:59"
  let ret = '';

  if (hrs > 0) {
    ret += `${hrs}:${mins < 10 ? '0' : ''}`;
  }

  ret += `${mins}:${secs < 10 ? '0' : ''}`;
  ret += `${secs}`;
  return ret;
};

const getFormattedTimeFromMs = (ms) => {
  const sec = ms ? parseInt(ms / 1000, 10) : 0; // parseInt = Bitwise operator for Math.floor. Is Faster.
  return getFormattedTimeFromSeconds(sec);
};

module.exports = {
  areTimeIntervalsOverlapping,
  getIntervalDurationInMS,
  getNextWeekdayStartDateTimestamp,
  getFormattedTimeFromSeconds,
  getFormattedTimeFromMs,
};
