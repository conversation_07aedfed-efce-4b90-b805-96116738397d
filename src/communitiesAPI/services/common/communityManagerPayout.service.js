const logger = require('../../../services/logger.service');
const CommunityManagerPayoutModel = require('../../models/communityManagerPayout.model');
const CountryCurrencyMappingModel = require('../../../models/countryInfoMapping.model');
const PayoutDetailsLarkWebhookService = require('./payoutDetailsLarkWebhook.service');
const { ParamError } = require('../../../utils/error.util');
const CountryInfoService = require('../../../services/countryInfoMapping.service');

const handleError = (functionName, err) => {
  logger.error(
    `Error: communityManagerPayout.service.js -> ${functionName}: `,
    err,
    err.stack
  );
  throw new Error(err);
};

exports.getPayoutBankAccount = async (params) => {
  try {
    const payoutData = await CommunityManagerPayoutModel.findOne(
      params
    ).lean();
    if (payoutData) {
      payoutData.bankCountryCode =
        await CountryInfoService.getCountryCodeByCountryName(
          payoutData.bankCountry
        );
    }
    return payoutData;
  } catch (error) {
    handleError('getPayout', error);
  }
};

exports.createPayoutBankAccount = async (params = {}) => {
  try {
    // eslint-disable-next-line no-param-reassign
    params.bankCountry =
      await CountryInfoService.getCountryNameByCountryCode(
        params.bankCountryCode
      );

    if (!params.phoneNumber) {
      if (!params.dialCode || !params.phoneNumberWithoutDialCode) {
        throw new ParamError(`Phone number is required`);
      }

      // eslint-disable-next-line no-param-reassign
      params.phoneNumber = `${params.dialCode}${params.phoneNumberWithoutDialCode}`;
    }

    const payoutData = await CommunityManagerPayoutModel.create(params);
    payoutData._doc.bankCountryCode = params.bankCountryCode;

    const { communityId, updatedAt } = payoutData;
    await PayoutDetailsLarkWebhookService.sendNotification(
      communityId,
      updatedAt,
      'CREATED'
    );

    return payoutData;
  } catch (err) {
    if (err.code === 11000) {
      err.message =
        'Payout Information already exists for this community ID';
    }
    handleError('createPayout', err);
  }
};

exports.updatePayoutBankAccount = async (params, payload) => {
  try {
    // eslint-disable-next-line no-param-reassign
    payload.bankCountry =
      await CountryInfoService.getCountryNameByCountryCode(
        payload.bankCountryCode
      );
    const updatedData = await CommunityManagerPayoutModel.findOneAndUpdate(
      params,
      payload,
      {
        new: true,
      }
    ).lean();
    updatedData.bankCountryCode = params.bankCountryCode;

    const { communityId, updatedAt } = updatedData;
    await PayoutDetailsLarkWebhookService.sendNotification(
      communityId,
      updatedAt,
      'UPDATED'
    );

    return updatedData;
  } catch (error) {
    handleError('updatePayout', error);
  }
};

exports.deletePayoutBankAccount = async (params) => {
  try {
    const updatedData = await CommunityManagerPayoutModel.deleteOne(
      params
    ).lean();

    const { communityId } = params;
    await PayoutDetailsLarkWebhookService.sendNotification(
      communityId,
      new Date().toISOString(),
      'DELETED'
    );

    return updatedData;
  } catch (error) {
    handleError('deletePayout', error);
  }
};
