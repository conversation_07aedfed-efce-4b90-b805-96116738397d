const axios = require('../../../clients/axios.client');
const {
  LARK_PRODUCT_FEEDBACK_ASSISTANT_ID,
  LARK_PRODUCT_FEEDBACK_ASSISTANT_SECRET_KEY,
} = require('../../../config');

exports.LARK_SUCCESS_CODE = 0;

exports.generateMessageTemplate = (header) => {
  const template = {
    msg_type: 'post',
    content: {
      post: {
        en_us: {
          title: header,
          content: [],
        },
      },
    },
  };

  return template;
};

exports.insertContentToTemplate = (template, content) => {
  const newTemplate = { ...template };
  newTemplate.content.post.en_us.content = content;
  return newTemplate;
};

exports.generateEmptyPayload = () => [];

exports.generateTextPayload = (title, value = '') => [
  {
    tag: 'text',
    text: title,
  },
  {
    tag: 'text',
    text: value,
  },
];

exports.generateLinkPayload = (title, value, href) => [
  {
    tag: 'text',
    text: title,
  },
  {
    tag: 'a',
    text: value,
    href,
  },
];

exports.sendPushNotificationToLark = async (url, payload) =>
  axios.post(url, payload);

/**
 * Description
 * @returns {Promise<string>}
 */
exports.generateTenantAccessToken = async () => {
  const data = await axios.post(
    'https://open.larksuite.com/open-apis/auth/v3/tenant_access_token/internal',
    {
      app_id: LARK_PRODUCT_FEEDBACK_ASSISTANT_ID,
      app_secret: LARK_PRODUCT_FEEDBACK_ASSISTANT_SECRET_KEY,
    }
  );

  const accessToken = data.data.tenant_access_token;

  return accessToken;
};

/**
 * Description
 * @param {Object} payload
 * @param {string} tenantAccessToken
 * @param {string} receiveIdType
 * @param {string} messageId
 * @returns {null}
 */
exports.replyToMessageOnLark = async ({
  payload,
  tenantAccessToken,
  receiveIdType,
  messageId,
}) => {
  const response = await axios.post(
    `https://open.larksuite.com/open-apis/im/v1/messages/${messageId}/reply`,
    payload,
    {
      headers: {
        Authorization: `Bearer ${tenantAccessToken}`,
      },
      params: {
        receive_id_type: receiveIdType,
      },
    }
  );

  return response.data;
};

exports.getDocsData = async ({ tenantAccessToken, docId }) => {
  const response = await axios.get(
    `https://open.larksuite.com/open-apis/docx/v1/documents/${docId}/raw_content`,
    {
      headers: {
        Authorization: `Bearer ${tenantAccessToken}`,
      },
    }
  );

  return response.data;
};

exports.getChatMessages = async ({
  tenantAccessToken,
  chatId,
  startTime,
}) => {
  const queryParams = {
    container_id: chatId,
    container_id_type: 'chat',
    start_time: startTime,
  };
  const response = await axios.get(
    'https://open.larksuite.com/open-apis/im/v1/messages',
    {
      headers: {
        Authorization: `Bearer ${tenantAccessToken}`,
      },
      params: queryParams,
    }
  );

  return response.data;
};

exports.getSectionsOfTaskList = async ({
  tenantAccessToken,
  taskListId,
}) => {
  const queryParams = {
    page_size: 1,
    resource_type: 'tasklist',
    resource_id: taskListId,
  };
  const response = await axios.get(
    `https://open.larksuite.com/open-apis/task/v2/sections`,
    {
      headers: {
        Authorization: `Bearer ${tenantAccessToken}`,
      },
      params: queryParams,
    }
  );

  return response.data.data;
};

exports.getSectionDetails = async ({ tenantAccessToken, sectionId }) => {
  const response = await axios.get(
    `	https://open.larksuite.com/open-apis/task/v2/sections/${sectionId}`,
    {
      headers: {
        Authorization: `Bearer ${tenantAccessToken}`,
      },
    }
  );

  return response.data?.data?.section;
};

exports.createSection = async ({
  name,
  tenantAccessToken,
  taskListId,
  insertBefore,
}) => {
  const response = await axios.post(
    'https://open.larksuite.com/open-apis/task/v2/sections',
    {
      insert_before: insertBefore,
      name,
      resource_id: taskListId,
      resource_type: 'tasklist',
    },
    {
      headers: {
        Authorization: `Bearer ${tenantAccessToken}`,
      },
    }
  );

  return response.data.data;
};

exports.createTask = async ({
  tenantAccessToken,
  taskListId,
  sectionId,
  summary,
  due,
  origin,
}) => {
  const response = await axios.post(
    'https://open.larksuite.com/open-apis/task/v2/tasks',
    {
      summary,
      due,
      tasklists: [{ tasklist_guid: taskListId, section_guid: sectionId }],
      origin,
    },
    {
      headers: {
        Authorization: `Bearer ${tenantAccessToken}`,
      },
    }
  );

  return response.data.data;
};

exports.createBaseRecord = async ({
  tenantAccessToken,
  baseId,
  tableId,
  record,
}) => {
  const response = await axios.post(
    `https://open.larksuite.com/open-apis/bitable/v1/apps/${baseId}/tables/${tableId}/records`,
    record,
    {
      headers: {
        Authorization: `Bearer ${tenantAccessToken}`,
      },
    }
  );

  return response.data;
};

exports.updateBaseRecord = async ({
  tenantAccessToken,
  baseId,
  tableId,
  recordId,
  updatedFields,
}) => {
  const response = await axios.put(
    `https://open.larksuite.com/open-apis/bitable/v1/apps/${baseId}/tables/${tableId}/records/${recordId}`,
    updatedFields,
    {
      headers: {
        Authorization: `Bearer ${tenantAccessToken}`,
      },
    }
  );

  return response.data;
};

exports.queryBaseRecords = async ({
  tenantAccessToken,
  baseId,
  tableId,
  filter,
}) => {
  const response = await axios.get(
    `https://open.larksuite.com/open-apis/bitable/v1/apps/${baseId}/tables/${tableId}/records`,
    {
      params: {
        filter,
      },
      headers: {
        Authorization: `Bearer ${tenantAccessToken}`,
      },
    }
  );

  return response.data;
};

/**
 * Forwards a thread to a specific group chat
 * @param {Object} params
 * @param {string} params.tenantAccessToken - The tenant access token
 * @param {string} params.threadId - The ID of the thread to forward
 * @param {string} params.chatId - The ID of the group chat to receive the forwarded thread
 * @returns {Promise<Object>} The response from the Lark API
 */
exports.forwardThreadToGroupChat = async ({
  tenantAccessToken,
  threadId,
  chatId,
}) => {
  const response = await axios.post(
    `https://open.larksuite.com/open-apis/im/v1/threads/${threadId}/forward`,
    { receive_id: chatId },
    {
      headers: {
        Authorization: `Bearer ${tenantAccessToken}`,
        'Content-Type': 'application/json',
      },
      params: {
        receive_id_type: 'chat_id',
      },
    }
  );

  return response.data;
};
