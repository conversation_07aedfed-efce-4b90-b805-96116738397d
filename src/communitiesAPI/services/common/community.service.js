/* eslint-disable no-param-reassign */
/* eslint-disable no-unused-expressions */
const ObjectId = require('mongoose').Types.ObjectId;
const status = require('http-status');
const { isValidObjectId } = require('mongoose');
const axios = require('../../../clients/axios.client');
// models
const Learner = require('../../../models/learners.model');
const DiscordServer = require('../../models/discordServer.model');
const Community = require('../../models/community.model');
const CommunityLandingPageTemplate = require('../../models/communityLandingPageTemplate.model');
const CommunityWeb3DiscountMethods = require('../../models/communityWeb3DiscountMethods.model');
const CommunitySubscriptions = require('../../models/communitySubscriptions.model');
const DiscordRolesModel = require('../../models/discordRoles.model');
const DiscordChannelsModel = require('../../models/discordChannel.model');
const CommunityRoles = require('../../models/communityRole.model');
const RecurringPlanLocalCurrencySupported = require('../../models/recurringPlanLocalCurrencySupported.model');
const RevenueTransactionModel = require('../../../models/revenueTransaction.model');
const {
  CONFIG_TYPES,
  COMMUNITY_SUBSCRIPTION_STATUSES,
  communityEnrolmentStatuses,
  CHAT_PLATFORMS,
  DEFAULT_COMMUNITY_PROFILE_IMAGE,
  DEFAULT_CURRENCY,
  MILESTONE_ACTIVITY_TYPES,
  PAYMENT_PROVIDER,
  CURRENCY_WITH_NON_DECIMAL_POINTS,
} = require('../../../constants/common');
const { CENTS_PER_DOLLAR, aclRoles } = require('../../constants');
const {
  ToUserError,
  InternalError,
  ParamError,
  ResourceNotFoundError,
} = require('../../../utils/error.util');
const { COMMUNITY_ERROR } = require('../../../constants/errorCode');

const {
  createDefaultAnnouncementPost,
} = require('../web/communityPosts.service');

const ActionEventService = require('../../../services/actionEvent');

const {
  sendZapierMails,
  communityCreatedEmails,
  communityCreatedKlaviyoEvents,
  communityMemberAccessChangeKlaviyoEvents,
  communityCreatedReferralAlertEmails,
  communityCreatedReferralEmails,
  communityManagerLanguagePreferenceChangeEvent,
} = require('./communityTriggers.service');
const { NAS_IO_FRONTEND_URL } = require('../../../config');

const {
  AiSummaryConfigService,
  CustomWelcomeMessageConfigService,
  IndiaPaymentWhitelistConfigService,
} = require('../../../services/config');

const logger = require('../../../services/logger.service');
const urlUtils = require('../../../utils/url.util');
const { URL_SHORTENER_LINK, NODE_ENV } = require('../../../config');
const SocialChatConnectionModel = require('../../models/socialChatConnection.model');
const {
  MEMBERSHIP_TYPE,
} = require('../../../services/membership/constants');
const whatsappBotNumbers = require('../../models/whatsappBotNumbers.model');
const { retrieveWhatsappGroupInfo } = require('./whatsapp.service');
const PaymentProviderUtils = require('../../../utils/paymentProvider.util');
const PaymentBackendRpc = require('../../../rpc/paymentBackend');
const FraudService = require('../../../services/fraud');
const FeeService = require('./fee.service');
const {
  retrievePaymentFeeStructure,
} = require('../../../services/config/paymentFeeStructureConfig.service');
const { PassOnConfigService } = require('../../../services/config/index');
const {
  normalizeAmountByCurrency,
} = require('../../../utils/currency.util');
const CommunityEventsModel = require('../../models/communityEvents.model');

const priceUtils = require('../../../utils/price.util');
const slugUtils = require('../../../utils/slug.util');
const {
  generateStripeDiscountForCommunitySwitchToPaid,
} = require('./communityDiscounts.service');
const {
  createOrUpdateMembershipPricing,
} = require('./communityMemershipPrice.service');
const {
  getExistingMemberParams,
} = require('../../../utils/communitySubscription.util');
const commonConfigService = require('../../../services/config/common.service');
const {
  getUpdateDataForMultipleCoverMediaItems,
  generateCoverMediaItems,
  hasVideoCoverMediaItems,
} = require('../../../utils/multipleCoverMediaItems.util');
const {
  COVER_MEDIA_ENTITY_TYPES,
} = require('../../../constants/coverMediaItems.constant');
const {
  deleteRemovedVideoCoverMediaItems,
} = require('../../../services/coverMediaItems/common.service');

const getCommunityBotDetails = async (communityId) => {
  try {
    const result = [];
    const community = await Community.findById(communityId);
    if (community?.bots?.length) {
      await Promise.all(
        community.bots.map(async (bot) => {
          if (bot?.type === CHAT_PLATFORMS.DISCORD) {
            const botDetails = await DiscordServer.aggregate([
              {
                $match: { id: bot.serverKey },
              },
              {
                $lookup: {
                  from: 'discord_users',
                  let: { ownerId: '$ownerId', id: '$id' },
                  pipeline: [
                    {
                      $match: {
                        $and: [
                          { $expr: { $eq: ['$id', '$$ownerId'] } },
                          { $expr: { $eq: ['$guild', '$$id'] } },
                        ],
                      },
                    },
                  ],
                  as: 'details',
                },
              },
              {
                $project: {
                  type: CHAT_PLATFORMS.DISCORD,
                  serverName: '$name',
                  serverId: '$id',
                  ownerId: 1,
                  ownerUsername: {
                    $arrayElemAt: ['$details.username', 0],
                  },
                  ownerAvatar: {
                    $arrayElemAt: ['$details.userAvatar', 0],
                  },
                },
              },
            ]);
            result.push(botDetails[0]);
          }
          // if (bot?.type === CHAT_PLATFORMS.TELEGRAM) {
          //   const botDetails = await TelegramGroups.aggregate([
          //     {
          //       $match: { group_id: bot.serverKey },
          //     },
          //     {
          //       $lookup: {
          //         from: 'telegram_users',
          //         let: { user_id: '$admin_user_id' },
          //         pipeline: [
          //           {
          //             $match: {
          //               $expr: { $eq: ['$user_id', '$$user_id'] },
          //             },
          //           },
          //         ],
          //         as: 'details',
          //       },
          //     },
          //     {
          //       $project: {
          //         type: CHAT_PLATFORMS.TELEGRAM,
          //         serverName: '$group_title',
          //         serverId: '$group_id',
          //         ownerId: '$admin_user_id',
          //         ownerUsername: {
          //           $arrayElemAt: ['$details.user_name', 0],
          //         },
          //         phoneNumber: '$phone_number',
          //       },
          //     },
          //   ]);
          //   result.push(botDetails[0]);
          // }

          if (CHAT_PLATFORMS.TELEGRAM) {
            let botDetails;
            const telegramType = CHAT_PLATFORMS.TELEGRAM.toLowerCase();
            // If serverKey is not a valid ObjectId, build a simple object from community.platforms (this is to handle legacy data)
            if (!isValidObjectId(bot?.serverKey)) {
              const platformData = community.platforms.find(
                (platform) => platform?.name === telegramType
              );
              botDetails = {
                type: telegramType,
                inviteLink: platformData?.link || '',
                communityObjectId: community._id,
                status: 'connected',
              };
            } else {
              // Otherwise, look up the record in the database
              botDetails = await SocialChatConnectionModel.findById(
                bot?.serverKey
              ).lean();
            }
            result.push(botDetails);
          }

          if (
            [
              CHAT_PLATFORMS.SLACK,
              CHAT_PLATFORMS.FACEBOOK,
              CHAT_PLATFORMS.LINKEDIN,
              CHAT_PLATFORMS.LINE,
            ].includes(bot?.type)
          ) {
            const botDetails = await SocialChatConnectionModel.findById(
              bot?.serverKey
            );
            const processedBotDetails = botDetails.toObject() || null;
            processedBotDetails.type = bot?.type;
            result.push(processedBotDetails);
          }
        })
      );
    }

    return result;
  } catch (err) {
    logger.error(err.message);
    throw err;
  }
};

const getCommunitySubscriptionDetailsPipielineQuery = ({
  learnerId,
  learnerObjectId,
  communityCode,
}) => {
  if (!learnerId && !learnerObjectId && !communityCode) {
    logger.error(
      '[Fn:getCommunitySubscriptionDetailsPipielineQuery] Insufficient params to get subscriptions'
    );
    throw new ParamError('Insufficient params to get subscriptions');
  }

  const existingMemberParams = getExistingMemberParams({
    withPendingSubscription: true,
  });

  const match = {
    ...existingMemberParams,
  };

  if (communityCode) {
    match.communityCode = communityCode;
  }

  if (learnerObjectId) {
    match.learnerObjectId = learnerObjectId;
  }

  if (learnerId) {
    match.learnerId = learnerId;
  }

  const pipelineQuery = [
    {
      $match: match,
    },
    {
      $addFields: {
        purchaseTransactionObjectId: {
          $toObjectId: '$communitySignupId',
        },
      },
    },
    {
      $lookup: {
        from: 'community_purchase_transactions',
        localField: 'purchaseTransactionObjectId',
        foreignField: '_id',
        as: 'discountdetails',
      },
    },
    {
      $unwind: {
        path: '$discountdetails',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: 'community_discount_transactions',
        localField: 'discountdetails.discountTransactionId',
        foreignField: '_id',
        as: 'discountdetails.discountTransactionDetails',
      },
    },
    {
      $unwind: {
        path: '$discountdetails.discountTransactionDetails',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: 'community_discount',
        localField:
          'discountdetails.discountTransactionDetails.communityDiscountObjectId',
        foreignField: '_id',
        as: 'discountdetails.discountTransactionDetails.promoDetails',
      },
    },
    {
      $unwind: {
        path: '$discountdetails.discountTransactionDetails.promoDetails',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $project: {
        root: {
          $mergeObjects: [
            '$$ROOT',
            {
              discountdetails: {
                _id: '$discountdetails._id',
                promoDetails:
                  '$discountdetails.discountTransactionDetails.promoDetails',
              },
              recurringPurchase:
                '$discountdetails.payment_details.recurringPurchase',
              fullAmount: {
                $ifNull: [
                  '$discountdetails.full_local_amount',
                  '$discountdetails.local_amount',
                ],
              },
            },
          ],
        },
      },
    },
    {
      $replaceRoot: {
        newRoot: '$root',
      },
    },
  ];

  return pipelineQuery;
};

const getLearnerCommunitySubscriptionDetails = async ({
  communityCode,
  learnerObjectId,
}) => {
  const pipelineQuery = getCommunitySubscriptionDetailsPipielineQuery({
    learnerObjectId,
    communityCode,
  });

  const userCommunitySubscription = await CommunitySubscriptions.aggregate(
    pipelineQuery
  );

  return userCommunitySubscription;
};

const getLearnersCommunityDetails = async (params = {}) => {
  try {
    const { learnerId, userObjectId, activeCommunityId } = params;

    const pipelineQuery = getCommunitySubscriptionDetailsPipielineQuery({
      learnerId,
    });

    const userCommunitySubscriptions =
      await CommunitySubscriptions.aggregate(pipelineQuery);

    const communityCodeData = new Map();
    userCommunitySubscriptions.forEach((subscription) => {
      if (
        subscription.memberType !== MEMBERSHIP_TYPE.PAID &&
        subscription.status === COMMUNITY_SUBSCRIPTION_STATUSES.CANCELLED
      ) {
        return;
      }

      if (
        communityCodeData.get(subscription.communityCode)?.status ===
        communityEnrolmentStatuses.CURRENT
      ) {
        return;
      }
      communityCodeData.set(subscription.communityCode, subscription);
    });

    const communityCodes = communityCodeData.keys();
    const communityCodesArr = [...communityCodes];

    const communitiesQuery = [
      {
        $match: {
          isActive: true,
          code: {
            $in: communityCodesArr,
          },
        },
      },
      {
        $lookup: {
          from: 'community_role',
          localField: 'code',
          foreignField: 'communityCode',
          as: 'membersWithRoles',
        },
      },
      {
        $project: {
          _id: 1,
          code: 1,
          title: 1,
          thumbnailImgData: 1,
          isFreeCommunity: 1,
          isPaidCommunity: 1,
          isTokenGated: 1,
          link: 1,
          By: 1,
          platforms: 1,
          membersWithRoles: 1,
          isDemo: 1,
          isWhatsappExperienceCommunity: 1,
          applicationConfig: {
            $ifNull: ['$applicationConfig', { autoApproval: false }],
          },
          profileImage: {
            $ifNull: [
              '$communityCheckoutCardData.imgData.mobileImgProps.src',
              DEFAULT_COMMUNITY_PROFILE_IMAGE,
            ],
          },
          request_approval: 1,
          applicationConfigDataFields: 1,
          countryCreatedIn: 1,
          bots: 1,
          managedByWhatsappBot: 1,
          whatsappInfo: 1,
          restrictedInfo: 1,
          config: 1,
        },
      },
    ];

    const communities = await Community.aggregate(communitiesQuery);

    const communitiesCurrentlySubscribed = [];
    const communitiesPendingApproval = [];
    const otherCommunities = [];
    let newActiveCommunityId = activeCommunityId;

    if (!newActiveCommunityId) {
      newActiveCommunityId = communities?.[0]?._id?.toString();
    }

    let activeCommunitySubscription;
    await Promise.all(
      communities.map(async (community) => {
        // Return a flag to indicate if this CM is eligible for india payment or not
        community.isEligibleForLocalPayment =
          await IndiaPaymentWhitelistConfigService.isCommunityWhitelisted(
            community._id
          );

        if (community.membersWithRoles) {
          community.membersWithRoles = community.membersWithRoles.filter(
            (member) => member.userObjectId.equals(userObjectId)
          );
        } else {
          community.membersWithRoles = [];
          logger.info(
            'No membersWithRoles for community|',
            `community=${community._id}`
          );
        }

        const existingManager = Boolean(
          community.membersWithRoles.find((userCommunityRole) => {
            return (
              Array.isArray(userCommunityRole?.role) &&
              userCommunityRole?.role.includes(aclRoles.MANAGER)
            );
          })
        );

        let whatsappBotMetaData = {};
        let whatsappGroupName;
        let isCommunityWhitelistedForAiSummary = false;

        const [
          hasAccessToZeroLink,
          hasAccessToGetInspired,
          isCommunityCodeBlacklistedForCustomMessage,
          coverMediaItems,
        ] = await Promise.all([
          commonConfigService.isCommunityIdWhitelisted(
            CONFIG_TYPES.ZERO_LINK_WHITELIST_CONFIG_TYPE,
            community
          ),
          commonConfigService.isCommunityIdWhitelisted(
            CONFIG_TYPES.GET_INSPIRED_WHITELIST_CONFIG_TYPE,
            community
          ),
          CustomWelcomeMessageConfigService.isCommunityCodeBlacklisted(
            community.code
          ),
          generateCoverMediaItems({
            entity: community,
            entityType: COVER_MEDIA_ENTITY_TYPES.COMMUNITY,
            isCommunityManager: existingManager,
          }),
        ]);

        if (community.isWhatsappExperienceCommunity) {
          if (community?.managedByWhatsappBot) {
            const whatsappBotInfo = await whatsappBotNumbers
              .findOne({
                botWhatsappId: community.managedByWhatsappBot,
              })
              .lean();
            const { botWhatsappId, botQr, botName, number } =
              whatsappBotInfo || {};
            whatsappBotMetaData = {
              contactName: botName,
              contactQr: botQr,
              numberId: botWhatsappId,
              number,
            };
          }

          community.isWhatsappBotInGroup =
            community?.whatsappInfo?.isBotInWhatsappGroup ?? false;
          community.isWhatsappBotAdmin =
            community?.whatsappInfo?.isBotWhatsappGroupAdmin ?? false;

          // this means that the bot is connected
          if (
            community?.bots?.length > 0 &&
            community?.bots?.[0].type === 'Whatsapp'
          ) {
            const whatsappGroupInfo = await retrieveWhatsappGroupInfo(
              community._id
            );

            if (whatsappGroupInfo) {
              whatsappGroupName = whatsappGroupInfo.whatsappGroupName;
            }
          }
          const isCommunityBlacklistedForAiSummary =
            await AiSummaryConfigService.isCommunityCodeBlacklisted(
              community.code
            );
          if (!isCommunityBlacklistedForAiSummary) {
            isCommunityWhitelistedForAiSummary =
              await AiSummaryConfigService.isCommunityCodeWhitelisted(
                community.code
              );
          }
        }

        if (Object.keys(whatsappBotMetaData).length > 0) {
          community.whatsappBotMetaData = whatsappBotMetaData;
        }

        if (whatsappGroupName) {
          community.whatsappGroupName = whatsappGroupName;
        }

        community.coverMediaItems = coverMediaItems;
        community.hasAccessToZeroLink = hasAccessToZeroLink;
        community.hasAccessToGetInspired = hasAccessToGetInspired;
        community.showCustomWelcomeMessage =
          !isCommunityCodeBlacklistedForCustomMessage;
        community.showAiSummary = !!isCommunityWhitelistedForAiSummary;

        community.isManager = !!existingManager;
        community.roleConfig = existingManager?.config;
        community.subscriptions = [communityCodeData.get(community.code)];
        const selfSubscription =
          communityCodeData.get(community?.code) || null;
        if (selfSubscription?.status === 'Current') {
          communitiesCurrentlySubscribed.push(community);
        } else if (selfSubscription?.status === 'Pending') {
          communitiesPendingApproval.push(community);
        } else {
          otherCommunities.push(community);
        }
        if (community._id.toString() === newActiveCommunityId) {
          activeCommunitySubscription = communityCodeData.get(
            community.code
          );
        }
      })
    );

    // Sort in descending order based on isManager (true comes first)
    communitiesCurrentlySubscribed.sort((a, b) => {
      return b.isManager - a.isManager;
    });

    communitiesPendingApproval.forEach((community) => {
      delete community.platforms;
      // delete community.subscriptions;
      delete community.membersWithRoles;
    });
    const sortedCommunities = [
      ...communitiesCurrentlySubscribed,
      ...communitiesPendingApproval,
      ...otherCommunities,
    ];

    return {
      sortedCommunities,
      activeCommunitySubscription,
    };
  } catch (err) {
    logger.error('getLearnersCommunityDetails error', err, err.stack);
    throw err;
  }
};

const getCommunityById = async (communityObjectId) => {
  try {
    const community = await Community.findById(
      communityObjectId,
      '_id code link title thumbnailImgData By platforms bots trackingPixels baseCurrency'
    ).lean();
    if (!community) {
      logger.error(`Community not exist in DB for ${communityObjectId}`);
      throw new Error(
        `Community not exist in DB for ${communityObjectId}`
      );
    }
    if (!community.applicationConfig) {
      community.applicationConfig = {
        autoApproval: false,
      };
    }
    return community;
  } catch (error) {
    logger.error(
      `Unable to find community details for ${communityObjectId} due to: `,
      error
    );
    throw error;
  }
};

const getCommunitiesByIds = async (arrayOfIds) => {
  if (arrayOfIds.length === 0) return [];
  const communities = await Community.find({
    _id: { $in: arrayOfIds },
  });
  return communities;
};

const getCommunityCodesByIds = async (arrayOfIds) => {
  if (arrayOfIds.length === 0) return [];
  const communities = await getCommunitiesByIds(arrayOfIds);
  const communityIds = communities.map((community) => community.code);
  return communityIds;
};

const updateOneCommunity = async (params, payload) => {
  try {
    const updatedData = await Community.findOneAndUpdate(params, payload, {
      new: true,
    }).lean();
    return updatedData;
  } catch (error) {
    logger.error('Error updating community:', error, error.stack);
    throw new Error('Cannot update community');
  }
};

const updateCommunityWeb3DiscountMethods = async (params = {}) => {
  try {
    const { contract, communityCode } = params;
    const collectionName = params?.collectionName;
    const findOne = await CommunityWeb3DiscountMethods.findOne({
      contractAddress: contract,
      communityCode,
    }).lean();
    if (!findOne) {
      const url = `https://api.etherscan.io/api?module=contract&action=getabi&address=${contract}&tag=latest&apikey=H2AURDNC5EGG35N81MGYXIKHT7M2XQP9EG`;
      const minABI = await axios.get(url);
      if (minABI?.data?.status === '1') {
        const contractAbi = minABI?.data?.result;
        const tenYearsFromNow = new Date();
        tenYearsFromNow.setFullYear(tenYearsFromNow.getFullYear() + 10);
        const insertData = {
          contractAddress: contract,
          contractAbi,
          isActive: true,
          startDate: new Date(),
          endDate: tenYearsFromNow,
          collectionName,
          communityCode,
          type: 'project_collaboration',
          discountDetails: {
            type: 'percent_off',
            value: 100,
            currency: 'USD',
            allowInAllCountries: true,
            // countryWiseDiscounts: [
            //   {
            //     value: 100,
            //     country: 'India',
            //     currency: 'INR',
            //   },
            //   {
            //     value: 100,
            //     country: 'Philippines',
            //     currency: 'PHP',
            //   },
            // ],
          },
          collectionData: {
            name: collectionName,
          },
        };
        Object.keys(insertData).forEach(
          (k) => insertData[k] == null && delete insertData[k]
        );
        await CommunityWeb3DiscountMethods.create(insertData);
      } else {
        throw new Error(
          'Cannot find the contract address details. Contract Address not valid'
        );
      }
    } else if (collectionName) {
      await CommunityWeb3DiscountMethods.findOneAndUpdate(
        {
          contractAddress: contract,
          communityCode,
        },
        { collectionName, isActive: true }
      );
    }
  } catch (error) {
    logger.error('Error update Community Web3 Discount Methods:', error);
    throw (
      error || new Error('Cannot update Community Web3 Discount Methods')
    );
  }
};

// eslint-disable-next-line no-unused-vars
function transformAmountToCents(item) {
  const newItem = { ...item };

  // Stripe only return dollar for non decimal points currencies
  if (
    CURRENCY_WITH_NON_DECIMAL_POINTS.includes(item.currency.toUpperCase())
  ) {
    newItem.amount *= CENTS_PER_DOLLAR;
  }

  return newItem;
}

const retrieveProductWithPrices = async (
  stripeProductId,
  communityPrices
) => {
  if (!stripeProductId) {
    return [];
  }

  if (!communityPrices?.length) {
    return [];
  }

  const prices = communityPrices
    .filter(({ isHidden }) => !isHidden)
    .map(({ cmSetPrice, currency, interval, intervalCount }) => {
      const priceId = priceUtils.generatePriceId({
        currency,
        interval,
        intervalCount,
        cmSetPrice,
      });

      return {
        id: priceId,
        currency,
        amount: cmSetPrice / CENTS_PER_DOLLAR,
        recurring: {
          interval,
          intervalCount,
        },
      };
    });

  return prices;
};

function generateAmountInCents(
  amount,
  currency,
  allowDecimal,
  minInCents,
  limitInCents
) {
  const amountInDollar = allowDecimal ? amount : Math.ceil(amount);

  const unitAmountInCents = Math.ceil(amountInDollar * CENTS_PER_DOLLAR);

  if (unitAmountInCents < minInCents || unitAmountInCents > limitInCents) {
    throw new ToUserError('Invalid price', COMMUNITY_ERROR.INVALID_PRICE, {
      unitAmountInCents: unitAmountInCents / CENTS_PER_DOLLAR,
      currency,
      minInCents: minInCents / CENTS_PER_DOLLAR,
      limitInCents: limitInCents / CENTS_PER_DOLLAR,
    });
  }

  return unitAmountInCents;
}

async function updatePaymentFeeStructureForItemCurrencyViaBaseCurrencyUsd(
  paymentFeeStructure,
  itemCurrency,
  baseCurrency
) {
  const paymentBackendRpc = new PaymentBackendRpc();
  await paymentBackendRpc.init();

  const { minGatewayFeeByBaseCurrency } = paymentFeeStructure;

  if (itemCurrency !== baseCurrency) {
    // Get conversion rate from base currency to item currency
    const conversionRateData = await paymentBackendRpc.getConversionRate(
      baseCurrency,
      itemCurrency
    );

    const exchangeRateToItemCurrency = conversionRateData.conversionRate;
    logger.info(
      `Conversion rate from ${baseCurrency} to ${itemCurrency} is ${exchangeRateToItemCurrency}`
    );

    const newMinGatewayFeeByBaseCurrency = normalizeAmountByCurrency(
      Math.ceil(minGatewayFeeByBaseCurrency * exchangeRateToItemCurrency)
    );

    const newPaymentFeeStructure = {
      ...paymentFeeStructure,
      minGatewayFeeByBaseCurrency: newMinGatewayFeeByBaseCurrency,
    };

    return newPaymentFeeStructure;
  }

  return paymentFeeStructure;
}

const updatePricesForToggleChanges = async (
  stripeProductId,
  communityCreatedAt,
  prices,
  baseCurrency = DEFAULT_CURRENCY,
  passOnTakeRate = false,
  payoutFeeConfigs = null,
  basePayoutFeeConfigs = null,
  paymentProvider = null,
  passOnPaymentGatewayFee = false,
  stripeUsProductId = null,
  communityCountry = null,
  planType = null
) => {
  const [
    { supported: recurringPlanLocalCurrencySupported = [] },
    paymentFeeStructure,
  ] = await Promise.all([
    RecurringPlanLocalCurrencySupported.findOne().lean(),
    retrievePaymentFeeStructure({ baseCurrency, planType }),
  ]);

  const effectivePayoutFeeConfig =
    FeeService.retrieveEffectivePayoutFeeConfig(payoutFeeConfigs);

  const effectiveBasePayoutFeeConfig =
    FeeService.retrieveEffectivePayoutFeeConfig(basePayoutFeeConfigs);

  const communityPrices = [];

  const pricePromises = prices.map(
    async ({ amount, currency, interval, intervalCount }) => {
      const { allowDecimal, minInCents, limitInCents } =
        recurringPlanLocalCurrencySupported.find(
          (currencySupported) => currencySupported.currency === currency
        ) ?? {};

      if (
        (interval !== 'month' && interval !== 'year') ||
        intervalCount > 12 ||
        intervalCount < 1
      ) {
        throw new ToUserError(
          'Invalid interval details',
          COMMUNITY_ERROR.INVALID_INTERVAL_DETAILS,
          {
            interval,
            intervalCount,
          }
        );
      }

      const cmSetPrice = generateAmountInCents(
        parseFloat(amount),
        currency,
        allowDecimal,
        minInCents,
        limitInCents
      );

      let newPaymentFeeStructure = paymentFeeStructure;

      // Only base currency USD can allow to have multiple currencies
      if (baseCurrency === DEFAULT_CURRENCY && currency !== baseCurrency) {
        newPaymentFeeStructure =
          await updatePaymentFeeStructureForItemCurrencyViaBaseCurrencyUsd(
            paymentFeeStructure,
            currency,
            baseCurrency
          );
      } else if (currency !== baseCurrency) {
        throw new InternalError(
          `Item currency (${currency}) not the same as base currency (${baseCurrency})`
        );
      }

      const checkoutAmountBreakdown =
        FeeService.getCheckoutAmountBreakdown({
          itemPrice: cmSetPrice,
          currency,
          communityCreatedAt,
          isAddonPayment: false,
          paymentFeeStructure: newPaymentFeeStructure,
          basePayoutFeeConfig: effectiveBasePayoutFeeConfig,
          customPayoutFeeConfig: effectivePayoutFeeConfig,
          passOnTakeRate,
          passOnPaymentGatewayFee,
          communityCountry,
          baseCurrency,
        });

      const convertedBreakdown = {
        itemPrice: checkoutAmountBreakdown.itemPrice,
        checkoutAmount: checkoutAmountBreakdown.checkoutAmount,
        revenueShare: checkoutAmountBreakdown.revenueShare,
        gstOnRevenue: checkoutAmountBreakdown.gstOnRevenue,
        gatewayFee: checkoutAmountBreakdown.gatewayFee,
        gst: checkoutAmountBreakdown.gst,
        processingFee: checkoutAmountBreakdown.processingFee,
      };

      const stripePrice = convertedBreakdown.checkoutAmount;
      const processingFee = stripePrice - cmSetPrice;

      communityPrices.push({
        cmSetPrice,
        stripePrice,
        feeDetails: {
          ...convertedBreakdown,
          processingFee, // total fee
        },
        currency,
        interval,
        intervalCount,
      });

      return {
        currency,
        unit_amount: stripePrice,
        recurring: {
          interval,
          interval_count: intervalCount,
        },
      };
    }
  );
  const newPrices = await Promise.all(pricePromises);

  try {
    const paymentBackendRpc = new PaymentBackendRpc();
    await paymentBackendRpc.init();

    let result;
    if (
      [PAYMENT_PROVIDER.STRIPE, PAYMENT_PROVIDER.STRIPE_US].includes(
        paymentProvider
      )
    ) {
      [result] = await Promise.all([
        paymentBackendRpc.updateStripeProductPricing({
          stripeProductId,
          prices: newPrices,
          baseCurrency,
          paymentProvider: PAYMENT_PROVIDER.STRIPE,
        }),
        paymentBackendRpc.updateStripeProductPricing({
          stripeProductId: stripeUsProductId,
          prices: newPrices,
          baseCurrency,
          paymentProvider: PAYMENT_PROVIDER.STRIPE_US,
        }),
      ]);
    } else {
      // For stripe india pricing
      result = await paymentBackendRpc.updateStripeProductPricing({
        stripeProductId,
        prices: newPrices,
        baseCurrency,
        paymentProvider,
      });
    }

    logger.info(`updatePrices: ${JSON.stringify(result)}`);

    return communityPrices;
  } catch (error) {
    logger.error('Error in updating prices:', error.message, error.stack);
    throw new Error('Unable to update prices');
  }
};

const archiveAllPrices = async (
  stripeProductId,
  baseCurrency,
  paymentProvider,
  stripeUsProductId
) => {
  try {
    const paymentBackendRpc = new PaymentBackendRpc();
    await paymentBackendRpc.init();
    switch (paymentProvider) {
      case PAYMENT_PROVIDER.STRIPE:
      case PAYMENT_PROVIDER.STRIPE_US:
        await paymentBackendRpc.archiveStripeProductPricing({
          stripeProductId,
          paymentProvider: PAYMENT_PROVIDER.STRIPE,
          archiveAllPrice: true,
        });
        if (stripeUsProductId) {
          await paymentBackendRpc.archiveStripeProductPricing({
            stripeProductId: stripeUsProductId,
            paymentProvider: PAYMENT_PROVIDER.STRIPE_US,
            archiveAllPrice: true,
          });
        }
        break;
      case PAYMENT_PROVIDER.STRIPE_INDIA:
        await paymentBackendRpc.archiveStripeProductPricing({
          stripeProductId,
          paymentProvider,
          archiveAllPrice: true,
        });
        break;
      default:
        throw new ParamError(
          `Payment provider ${paymentProvider} not supported`
        );
    }
  } catch (error) {
    logger.error('Error in archiving prices:', error.message, error.stack);
    throw new Error('Unable to archiving prices');
  }
};

const updateProductName = async (
  paymentProvider,
  stripeProductId,
  stripeUsProductId,
  title
) => {
  try {
    const paymentBackendRpc = new PaymentBackendRpc();
    await paymentBackendRpc.init();

    if (paymentProvider === PAYMENT_PROVIDER.STRIPE_INDIA) {
      await paymentBackendRpc.updateStripeIndiaProduct(
        stripeProductId,
        title
      );
    } else {
      await paymentBackendRpc.updateStripeProduct(
        stripeProductId,
        title,
        PAYMENT_PROVIDER.STRIPE
      );
      await paymentBackendRpc.updateStripeProduct(
        stripeUsProductId,
        title,
        PAYMENT_PROVIDER.STRIPE_US
      );
    }
  } catch (error) {
    logger.error('Error in updating product:', error.message, error.stack);
    throw new Error('Unable to update product');
  }
};

const linkValidation = async (link, communityId, communityCode = null) => {
  slugUtils.validateSlug(link);

  const community = await Community.findById(communityId, {
    link: 1,
    code: 1,
  }).lean();

  if (!community) {
    throw new ResourceNotFoundError('Community Not found');
  }

  if (link === community.link) {
    return link;
  }

  const linkRegex = { $regex: new RegExp(`^${link}$`, 'i') };
  const matchFilter = {
    link: linkRegex,
    _id: { $ne: new ObjectId(communityId) },
  };
  const TAKEN_LINK_ERROR_MSG = 'Link is already taken';
  const doesCommunityExist = await Community.exists(matchFilter);
  if (doesCommunityExist) {
    throw new ToUserError(
      TAKEN_LINK_ERROR_MSG,
      COMMUNITY_ERROR.TAKEN_LINK
    );
  }
  const template = await CommunityLandingPageTemplate.findOne({
    slug: linkRegex,
  });

  if (template) {
    if (template.communityCode !== (communityCode ?? community.code)) {
      logger.error('Template check: Link belongs to another community.');
      throw new ToUserError(
        TAKEN_LINK_ERROR_MSG,
        COMMUNITY_ERROR.TAKEN_LINK
      );
    }
    logger.info('Template check: Community already has this link.');
  }
  try {
    const res = await axios.head(`${URL_SHORTENER_LINK}/short-url${link}`);

    if (res.status === status.OK) {
      logger.error('Axios check: Link belongs to another short-url.');
      throw new ToUserError(
        TAKEN_LINK_ERROR_MSG,
        COMMUNITY_ERROR.TAKEN_LINK
      );
    }
  } catch (error) {
    if (error.message === TAKEN_LINK_ERROR_MSG) {
      throw error;
    }
    logger.info(`Status: ${`${URL_SHORTENER_LINK}/short-url${link}`}`);
    logger.info(
      'Axios check: Link does not belong to another community. So allowing link update'
    );
    return link;
  }
  return false;
};

const checkCommunityForFraud = async ({
  communityId,
  communityData,
  eventName = FraudService.INTERESTED_EVENTS.UPDATE_CONTENT,
}) => {
  const checksToPerform = [];
  const autoConsequencesToApply = [];
  const data = {};
  let alertThreshold = 70;
  if (eventName === FraudService.INTERESTED_EVENTS.ENABLE_COMMUNITY) {
    if (NODE_ENV !== 'production') {
      return;
    }
    checksToPerform.push(FraudService.COMMON_FRAUD_CHECKS.RISKY_MARKET);
    checksToPerform.push(FraudService.COMMON_FRAUD_CHECKS.FAKE_IDENTITY);
    data.content = communityData.timezone;
    data.contentSource = 'community timezone';
    autoConsequencesToApply.push(
      FraudService.COMMON_CONSEQUENCES.RESTRICT_CHECKOUT,
      FraudService.COMMON_CONSEQUENCES.RESTRICT_MAGIC_REACH,
      FraudService.COMMON_CONSEQUENCES.RESTRICT_INVITE,
      FraudService.COMMON_CONSEQUENCES.RESTRICT_UPLOAD
    );
    alertThreshold = 100;
  } else if (communityData.description || communityData.title) {
    checksToPerform.push(FraudService.COMMON_FRAUD_CHECKS.FREE_INPUT);
    data.content = `${communityData.title}, ${communityData.description}`;
    data.contentSource = 'community description & title';
    autoConsequencesToApply.push(
      FraudService.COMMON_CONSEQUENCES.RESTRICT_CHECKOUT,
      FraudService.COMMON_CONSEQUENCES.RESTRICT_CUSTOM_EMAIL,
      FraudService.COMMON_CONSEQUENCES.RESTRICT_FREE_SUBSCRIPTION,
      FraudService.COMMON_CONSEQUENCES.RESTRICT_MAGIC_REACH,
      FraudService.COMMON_CONSEQUENCES.RESTRICT_INVITE,
      FraudService.COMMON_CONSEQUENCES.NOT_INDEXABLE,
      FraudService.COMMON_CONSEQUENCES.RESTRICT_UPLOAD,
      FraudService.COMMON_CONSEQUENCES.RECOMMEND_DEACTIVATE
    );
  } else {
    return;
  }

  const fraudEngine = new FraudService.FraudEngine({
    communityId,
    eventName,
    entityType: 'community',
    entityId: communityId,
    data,
    checksToPerform,
    autoConsequencesToApply,
    alertThreshold,
  });
  try {
    await fraudEngine.performCheck();
  } catch (error) {
    logger.error('Error in fraud check:', error.message, error.stack);
  }
};

const getCommunityUpdateBaseCurrencyFields = async (
  communityData,
  newBaseCurrency
) => {
  if (!newBaseCurrency || newBaseCurrency === communityData.baseCurrency) {
    return {};
  }
  const existsRevenueTransaction = await RevenueTransactionModel.exists({
    communityObjectId: communityData._id,
  });
  if (existsRevenueTransaction) {
    throw new InternalError(
      'Cannot change currency if it exists transactions'
    );
  }
  const newPaymentMethods = communityData.payment_methods.map(
    (paymentMethod) => {
      const newPaymentMethod = { ...paymentMethod };

      if (paymentMethod.label === PAYMENT_PROVIDER.STRIPE) {
        if (newBaseCurrency === 'INR') {
          newPaymentMethod.value = PAYMENT_PROVIDER.STRIPE_INDIA;
        } else {
          newPaymentMethod.value = PAYMENT_PROVIDER.STRIPE;
        }
      }
      return newPaymentMethod;
    }
  );

  const newCommunity = {
    ...communityData,
    baseCurrency: newBaseCurrency,
  };

  const paymentFeeStructure = await retrievePaymentFeeStructure({
    baseCurrency: newCommunity.baseCurrency,
    planType: newCommunity.config?.planType,
  });

  const {
    feeConfig: basePayoutFeeConfig,
    passOnTakeRate: passOnTakeRateSetting,
    passOnPaymentGatewayFee: passOnPaymentGatewayFeeSetting,
  } = await FeeService.retrieveBasePayoutFeeConfigAndOtherSettings({
    community: newCommunity,
    paymentFeeStructure,
  });

  const basePayoutFeeConfigs = [basePayoutFeeConfig];

  return {
    passOnTakeRate: passOnTakeRateSetting,
    passOnPaymentGatewayFee: passOnPaymentGatewayFeeSetting,
    basePayoutFeeConfigs,
    baseCurrency: newBaseCurrency,
    payment_methods: newPaymentMethods,
  };
};

const updateCommunityForPublicApi = async (communityId, payload = {}) => {
  if (payload.isActive != null) {
    throw new ParamError('Invalid api call');
  }

  if (!payload.baseCurrency && !payload.link) {
    throw new ParamError('Invalid call for update');
  }
  const communityData = await Community.findOne({
    _id: new ObjectId(communityId),
  }).lean();
  if (!communityData) {
    throw new ResourceNotFoundError('Community Not found');
  }
  if (communityData.isActive && !communityData.isDraft) {
    throw new ParamError('Community is already active. Login to update!');
  }
  const communityCode = communityData.code;
  const communitySlug = communityData.link;

  let updateData = {};

  if (
    payload.baseCurrency &&
    payload.baseCurrency !== communityData.baseCurrency
  ) {
    const baseCurrencyDataToUpdate =
      await getCommunityUpdateBaseCurrencyFields(
        communityData,
        payload.baseCurrency
      );
    updateData = {
      ...updateData,
      ...baseCurrencyDataToUpdate,
    };
  }

  if (payload?.link) {
    const link = await linkValidation(
      payload?.link,
      communityId,
      communityCode
    );
    updateData.link = link;
  }

  if (!Object.keys(updateData).length) {
    return communityData;
  }

  const communityUpdate = await updateOneCommunity(
    { _id: new ObjectId(communityId) },
    updateData
  );

  if (updateData.link) {
    logger.info('Updating Community Landing Page with new link');
    await CommunityLandingPageTemplate.findOneAndUpdate(
      {
        slug: communitySlug,
        communityCode,
      },
      { slug: updateData.link }
    );
  }

  // get pro and non-pro fee rate
  const {
    paymentDetails,
    defaultPaymentDetails,
    proPaymentDetails,
    paymentDetailsByTiers,
  } = await FeeService.getProAndDefaultFeeRate({
    community: communityUpdate,
  });

  return {
    ...(communityUpdate ?? {}),
    paymentDetails,
    defaultPaymentDetails,
    proPaymentDetails,
    paymentDetailsByTiers,
  };
};

const updateCommunityData = async (
  communityId,
  payload = {},
  currentUser = {},
  userAgent = null
) => {
  try {
    // ensure that communityReferralCode does not get updated
    delete payload.communityReferralCode;

    const communityData = await Community.findOne({
      _id: new ObjectId(communityId),
    }).lean();
    if (!communityData) {
      throw new Error('Community Not found');
    }
    const communityCode = communityData.code;
    const communitySlug = communityData.link;
    const communityName = communityData?.name;
    const communityCountry = communityData?.countryCreatedIn;

    const enablePassonPaymentGateway =
      await PassOnConfigService.isPassOnPaymentGatewayEnabled(
        communityCode
      );
    const passOnTakeRate =
      payload?.passOnTakeRate ?? communityData.passOnTakeRate ?? false;
    const passOnPaymentGatewayFee = enablePassonPaymentGateway
      ? passOnTakeRate
      : false;

    const updateData = {
      title: payload?.title,
      request_approval: payload?.request_approval,
      isActive: payload?.isActive,
      isDraft: payload?.isDraft,
      applicationConfig: payload?.applicationConfig,
      waitListConfig: payload?.waitListConfig,
      passOnTakeRate,
      passOnPaymentGatewayFee,
      config: {
        ...communityData.config,
      },
    };

    if (payload?.templateLibraryId) {
      updateData.templateLibraryId = payload.templateLibraryId;
    }

    if (
      payload.baseCurrency &&
      payload.baseCurrency !== communityData.baseCurrency
    ) {
      const existsRevenueTransaction =
        await RevenueTransactionModel.exists({
          communityObjectId: communityId,
        });

      if (existsRevenueTransaction) {
        throw new InternalError(
          'Cannot change currency if it exists transactions'
        );
      }

      const newPaymentMethods = communityData.payment_methods.map(
        (paymentMethod) => {
          const newPaymentMethod = { ...paymentMethod };

          if (paymentMethod.label === PAYMENT_PROVIDER.STRIPE) {
            if (payload.baseCurrency === 'INR') {
              newPaymentMethod.value = PAYMENT_PROVIDER.STRIPE_INDIA;
            } else {
              newPaymentMethod.value = PAYMENT_PROVIDER.STRIPE;
            }
          }

          return newPaymentMethod;
        }
      );

      const newCommunity = {
        ...communityData,
        baseCurrency: payload.baseCurrency,
      };

      const paymentFeeStructure = await retrievePaymentFeeStructure({
        baseCurrency: newCommunity.baseCurrency,
        planType: newCommunity.config?.planType,
      });

      const {
        feeConfig: basePayoutFeeConfig,
        passOnTakeRate: passOnTakeRateSetting,
        passOnPaymentGatewayFee: passOnPaymentGatewayFeeSetting,
      } = await FeeService.retrieveBasePayoutFeeConfigAndOtherSettings({
        community: newCommunity,
        paymentFeeStructure,
      });

      updateData.passOnTakeRate = passOnTakeRateSetting;
      updateData.passOnPaymentGatewayFee = passOnPaymentGatewayFeeSetting;

      updateData.basePayoutFeeConfigs = [basePayoutFeeConfig];
      updateData.baseCurrency = payload.baseCurrency;
      updateData.payment_methods = newPaymentMethods;
    }

    if (payload?.showCommunityReferralLink != null) {
      updateData.config.showCommunityReferralLink =
        payload.showCommunityReferralLink;
    }

    if (payload?.hideSignUpOverlay != null) {
      updateData.config.hideSignUpOverlay = payload.hideSignUpOverlay;
    }

    if (payload?.allowMembersToPost != null) {
      updateData.config.allowMembersToPost = payload.allowMembersToPost;
    }

    if (payload?.hideLikesOnPost != null) {
      updateData.config.hideLikesOnPost = payload.hideLikesOnPost;
    }

    if (payload?.hideViewsOnPost != null) {
      updateData.config.hideViewsOnPost = payload.hideViewsOnPost;
    }

    if (payload?.disableCommentsOnPost != null) {
      updateData.config.disableCommentsOnPost =
        payload.disableCommentsOnPost;
    }

    if (payload?.disableLikesOnPost != null) {
      updateData.config.disableLikesOnPost = payload.disableLikesOnPost;
    }

    if (payload?.memberPostApprovalRequired != null) {
      updateData.config.memberPostApprovalRequired =
        payload.memberPostApprovalRequired;
    }

    if (payload?.primarySocialLink) {
      updateData.primarySocialLink = payload?.primarySocialLink;
    }

    if (payload?.thumbnailImgData) {
      updateData.thumbnailImgData = {
        mobileImgData: {
          src: payload.thumbnailImgData,
          meta: { width: 88, height: 88 },
        },
        desktopImgData: {
          src: payload.thumbnailImgData,
          meta: { width: 88, height: 88 },
        },
      };
      updateData.thumbnailImgData = {
        imgData: {
          mobileImgData: {
            src: payload.thumbnailImgData,
          },
          desktopImgData: {
            src: payload.thumbnailImgData,
          },
        },
      };
    }

    if (payload?.communityCategory) {
      updateData.communityGoal = payload.communityCategory;
    }

    if (payload?.communityGoal) {
      updateData.communityGoal = payload.communityGoal;
    }

    if (payload?.fullScreenBannerImgData && !payload?.coverMediaItems) {
      updateData.fullScreenBannerImgData = {
        mobileImgProps: { src: payload.fullScreenBannerImgData },
        desktopImgProps: { src: payload.fullScreenBannerImgData },
      };
    }

    if (payload?.coverMediaItems && payload?.coverMediaItems?.length > 0) {
      const coverMediaItemsUpdateData =
        await getUpdateDataForMultipleCoverMediaItems({
          communityId,
          entityType: COVER_MEDIA_ENTITY_TYPES.COMMUNITY,
          coverMediaItems: payload.coverMediaItems,
        });

      Object.assign(updateData, coverMediaItemsUpdateData);
    }

    if (payload?.mobileApplicationConfigDataFields) {
      updateData.lastEditedApplicationForm = new Date();
      updateData.mobileApplicationConfigDataFields =
        payload?.mobileApplicationConfigDataFields;
    }
    if (payload?.applicationConfigDataFields) {
      updateData.lastEditedApplicationForm = new Date();

      updateData.applicationConfigDataFields =
        payload.applicationConfigDataFields.map((dataField) => {
          const newDataField = { ...dataField };

          // Assign data type to prevent Web or App passing the wrong data type
          if (dataField.fieldName === 'fullName') {
            newDataField.fieldDataType = 'text';
            newDataField.inputSectionKey = 'text';
          } else if (dataField.fieldName === 'phoneNumber') {
            newDataField.fieldDataType = 'phone';
            newDataField.inputSectionKey = 'phone';
          } else if (dataField.fieldName === 'socialMediaLink') {
            newDataField.fieldDataType = 'url';
            newDataField.inputSectionKey = 'url';
          }

          return newDataField;
        });
    }

    if (payload?.request_approval) {
      // check if there are ongoing events with applicationConfig fields if yes then throw an error
      const ongoingApplicationEvents = await CommunityEventsModel.findOne({
        communities: communityId,
        requiresApproval: true,
        endTime: { $gte: new Date() },
        isActive: true,
      });

      if (ongoingApplicationEvents) {
        throw new ToUserError(
          'Cannot update approval fields as there are ongoing events that requires approval',
          COMMUNITY_ERROR.CANNOT_ADD_COMMUNITY_APPROVAL
        );
      }

      updateData.request_approval = payload?.request_approval;
    }

    const learnerObjectId =
      currentUser?.learner?._id ?? currentUser?.learner;

    if (currentUser && currentUser?.learner) {
      const learnerData = await Learner.findById(learnerObjectId).lean();
      currentUser.learner = learnerData;
      if (payload?.referralCode && payload?.isActive) {
        const existingManager = await CommunityRoles.exists({
          userObjectId: new ObjectId(currentUser?._id),
          communityObjectId: { $ne: new ObjectId(communityId) },
          role: 'admin',
        });
        if (existingManager) {
          logger.error(
            `ReferralCode cannot be applied to User ${currentUser?._id} who is already an existing manager`
          );
        } else if (payload?.referralCode) {
          const referralCode = payload?.referralCode?.toLocaleUpperCase();
          logger.info(
            `User ${currentUser?._id} is not an existing manager. Applying referralCode  ${referralCode} to community ${communityId}.`
          );
          const referrer = await Learner.findOne({ referralCode })
            .select('_id email referralCode')
            .lean();
          if (!referrer) {
            logger.error(`Invalid referralCode ${referralCode}`);
            throw new Error('Invalid referralCode');
          }
          updateData.referralCodeUsed = referralCode;
          updateData.referrerEmail = referrer.email;
          updateData.referrerObjectId = new ObjectId(referrer._id);
        }
      }
    }

    if (payload?.link) {
      const link = await linkValidation(
        payload?.link,
        communityId,
        communityCode
      );
      updateData.link = link;
    }

    if (payload?.hideGetStarted != null) {
      updateData.config.hideGetStarted = payload.hideGetStarted;
    }

    const communityRoles = await CommunityRoles.find(
      {
        communityObjectId: new ObjectId(communityId),
        role: 'admin',
      },
      { email: 1, _id: 0 }
    );
    const communityAdminEmailList =
      communityRoles.map((role) => role?.email) || [];
    let communitySubsQuery = {};
    if (communityAdminEmailList.length !== 0) {
      communitySubsQuery = {
        email: {
          $nin: [...communityAdminEmailList],
        },
      };
    }

    const findSubscriptions = await CommunitySubscriptions.findOne({
      communityCode,
      ...communitySubsQuery,
    }).lean();

    // if subscriptions exist and they want it to change to Token Gated (In the future we remove this as well), then pricing model cannot be changed
    if (findSubscriptions && payload?.isTokenGated === true) {
      throw new Error(
        'Community already has subscriptions. Pricing model cannot be changed.'
      );
    }
    const mailDataPayloadCommon = {
      community_url: NAS_IO_FRONTEND_URL + communitySlug,
      community_code: communityCode,
      community_name: communityName,
      cm_email: communityAdminEmailList[0],
    };

    if (payload?.description) {
      const aboutArray = payload?.description?.split('\n\n');
      const about = aboutArray.map((text) => {
        return { text, isNewParagraph: true };
      });
      updateData.about = about;
      updateData.description = payload?.description;
    }

    if (payload?.profileImageLink) {
      if (!urlUtils.isURL(payload.profileImageLink)) {
        throw new ParamError('Invalid image link');
      }

      const props = { src: payload?.profileImageLink };

      let thumbnailImgData = communityData.thumbnailImgData ?? {};
      let mobileImgData =
        communityData.thumbnailImgData?.mobileImgData ?? {};
      let desktopImgData =
        communityData.thumbnailImgData?.desktopImgData ?? {};
      mobileImgData = { ...mobileImgData, ...props };
      desktopImgData = { ...desktopImgData, ...props };
      thumbnailImgData = {
        ...thumbnailImgData,
        mobileImgData,
        desktopImgData,
      };

      updateData.thumbnailImgData = thumbnailImgData;

      const communityCheckoutCardData =
        communityData.communityCheckoutCardData ?? {};
      let imgData = communityData.communityCheckoutCardData?.imgData ?? {};
      let mobileImgProps =
        communityData.communityCheckoutCardData?.imgData?.mobileImgProps ??
        {};
      let desktopImgProps =
        communityData.communityCheckoutCardData?.imgData
          ?.desktopImgProps ?? {};
      mobileImgProps = { ...mobileImgProps, ...props };
      desktopImgProps = { ...desktopImgProps, ...props };
      imgData = { ...imgData, mobileImgProps, desktopImgProps };
      communityCheckoutCardData.imgData = imgData;

      updateData.communityCheckoutCardData = communityCheckoutCardData;
    }
    Object.keys(updateData).forEach(
      (k) => updateData[k] == null && delete updateData[k]
    );

    const paymentProvider =
      await PaymentProviderUtils.retrievePaymentProvider(
        communityData.payment_methods
      );

    let mailDataPricing = {};
    const memberAccess = {
      type: '',
      price: '',
      currency: '',
      contractInfo: '',
    };

    // Check if detachment of discord channel is needed when there's a change in member access
    if (
      (payload?.isTokenGated && !communityData.isTokenGated) ||
      (communityData.isTokenGated &&
        (payload?.isPaidCommunity || payload?.isFreeCommunity))
    ) {
      if (communityData?.verificationChannelId) {
        logger.info(
          `There is a change iin member access, finding channel with id ${communityData?.verificationChannelId}`
        );
        const channel = await DiscordChannelsModel.findOne({
          channel_id: communityData?.verificationChannelId,
        });
        if (channel) {
          logger.info(
            `Channel ${communityData?.verificationChannelId} is valid. Deleting all configured roles in guild ${channel.guildId}`
          );
          await DiscordRolesModel.updateMany(
            { guildID: channel.guildId },
            { $set: { isConfigured: false, config: null } }
          );
          logger.info(
            `Configured roles in guild ${channel.guildId} has been deleted`
          );
          updateData.verificationChannelId = null;
        } else {
          logger.info(
            `Channel ${communityData?.verificationChannelId} cannot be found`
          );
        }
      }
    }

    // Token Gated (Legacy communities)
    if (
      payload?.isTokenGated === true &&
      payload?.contractAddress.length !== 0
    ) {
      if (communityData?.stripeProductId) {
        await updateOneCommunity(
          { _id: new ObjectId(communityId) },
          { $unset: { stripeProductId: 1 } }
        );
      }
      for (const contract of payload?.contractAddress) {
        // eslint-disable-next-line no-await-in-loop
        await updateCommunityWeb3DiscountMethods({
          contract: contract?.address,
          communityCode,
          collectionName: contract?.collectionName,
        });
      }
      updateData.isFreeCommunity = false;
      updateData.isPaidCommunity = false;
      updateData.isTokenGated = true;
      updateData.payment_methods = [
        {
          value: 'web3',
          label: 'web3',
          icon: 'https://d2oi1rqwb0pj00.cloudfront.net/na-website/Payment/svg/ewallet.svg',
        },
      ];
      memberAccess.type = 'Token Gated';
      memberAccess.contractInfo = JSON.stringify(payload?.contractAddress);
      mailDataPricing = {
        ...mailDataPayloadCommon,
        community_price_type: memberAccess.type,
        community_price: memberAccess.price,
        community_price_currency: memberAccess.currency,
        contract_address: memberAccess.contractInfo,
      };
    }
    // Paid Community
    if (payload?.isPaidCommunity === true) {
      if (communityData?.isTokenGated) {
        await CommunityWeb3DiscountMethods.updateMany(
          { communityCode },
          { isActive: false }
        );
      }

      const {
        updatePrice = false,
        prices,
        price,
        interval,
        intervalCount,
        currency,
      } = payload;

      if (updatePrice && (!prices || prices.length === 0)) {
        throw new ParamError(`Empty price array while updating price`);
      }

      const legacyPriceSetting = [
        {
          currency,
          amount: parseFloat(price),
          interval,
          intervalCount,
        },
      ];

      const updatedPriceData = await createOrUpdateMembershipPricing({
        communityData,
        paymentProvider,
        prices,
        legacyPriceSetting,
        updatePrice,
      });

      if (updatedPriceData.prices) {
        updateData.prices = updatedPriceData.prices;
      }

      if (updatedPriceData.stripeProductId) {
        updateData.stripeProductId = updatedPriceData.stripeProductId;
      }

      if (updatedPriceData.stripeUsProductId) {
        updateData.stripeUsProductId = updatedPriceData.stripeUsProductId;
      }

      updateData.isFreeCommunity = false;
      updateData.isTokenGated = false;
      updateData.isPaidCommunity = true;

      await generateStripeDiscountForCommunitySwitchToPaid({
        community: communityData,
        stripeProductId: updateData.stripeProductId,
        stripeUsProductId: updateData.stripeUsProductId,
      });

      memberAccess.type = 'Paid';
      memberAccess.price = `${prices?.[0]?.amount}`;
      memberAccess.currency = prices?.[0]?.currency;

      mailDataPricing = {
        ...mailDataPayloadCommon,
        community_price_type: memberAccess.type,
        community_price: memberAccess.price,
        community_price_currency: memberAccess.currency,
        contract_address: '',
      };
    }

    // Update the name of stripe product
    if (payload.title && communityData.stripeProductId) {
      await updateProductName(
        paymentProvider,
        communityData.stripeProductId,
        communityData.stripeUsProductId,
        payload.title
      );
    }
    // Free Community
    if (payload?.isFreeCommunity === true) {
      if (communityData.stripeProductId) {
        await Promise.all([
          archiveAllPrices(
            communityData.stripeProductId,
            communityData.baseCurrency,
            paymentProvider,
            communityData.stripeUsProductId
          ),
        ]);
      }

      if (communityData.isTokenGated) {
        await CommunityWeb3DiscountMethods.updateMany(
          { communityCode },
          { isActive: false }
        );
      }
      updateData.isFreeCommunity = true;
      updateData.isTokenGated = false;
      updateData.isPaidCommunity = false;
      updateData.prices = []; // remove all price in the db
      memberAccess.type = 'Free';
      mailDataPricing = {
        ...mailDataPayloadCommon,
        community_price_type: memberAccess.type,
        community_price: '',
        community_price_currency: '',
        contract_address: '',
      };
    }
    if (payload?.By) {
      updateData.By = payload?.By?.trim();
    }

    // Move to POST /community-manager
    // if (communityData?.isActive && updateData?.isActive) {
    //   if (!communityData?.By && !payload?.By) {
    //     updateData.By = currentUser?.fullName ?? communityBy;
    //   }
    //   if (!communityData?.createdBy) {
    //     updateData.createdBy = currentUser?.email;
    //   }

    //   const batchMetadataEnabledType = Object.keys(
    //     BATCH_METADATA_MODEL_TYPE
    //   ).reduce((acc, modelType) => {
    //     acc[modelType] = true;
    //     return acc;
    //   }, {});

    //   updateData.config.batchMetadataEnabledType =
    //     batchMetadataEnabledType;

    //   updateData.config.messageSettings =
    //     chatService.settingService.retrieveDefaultMessageSettings();

    //   const paymentFeeStructure = await retrievePaymentFeeStructure(
    //     communityData.baseCurrency
    //   );

    //   const {
    //     feeConfig: basePayoutFeeConfig,
    //     passOnTakeRate: passOnTakeRateSetting,
    //     passOnPaymentGatewayFee: passOnPaymentGatewayFeeSetting,
    //   } = await FeeService.retrieveBasePayoutFeeConfigAndOtherSettings({
    //     community: communityData,
    //     paymentFeeStructure,
    //   });

    //   updateData.passOnTakeRate = passOnTakeRateSetting;
    //   updateData.passOnPaymentGatewayFee = passOnPaymentGatewayFeeSetting;

    //   updateData.basePayoutFeeConfigs = [basePayoutFeeConfig];

    //   const subscriptionDoc = {
    //     communityCode: LEGENDS_COMMUNITY_CODE,
    //     email: currentUser?.email,
    //     learnerId: currentUser?.learner?.learnerId,
    //     learnerObjectId: currentUser?.learner?._id,
    //   };

    //   if (communityCountry === COUNTRY_CREATED.INDIA) {
    //     subscriptionDoc.communityCode = INDIAUPI_COMMUNITY_CODE;
    //   }

    //   if (latamCountriesArray.includes(communityCountry)) {
    //     subscriptionDoc.communityCode = LATAM_COMMUNITY_CODE;
    //   }
    //   // subscribe creator to legends community or indiaUpi community based on country
    //   await findOneOrCreateSubscription(subscriptionDoc, communityData);
    // }
    const updateCommunityDataObject = updateData;
    delete updateCommunityDataObject.isDraft;
    delete updateCommunityDataObject.isActive;
    if (currentUser) {
      updateCommunityDataObject.updatedBy = currentUser?.email;
    }

    const communityUpdate = await updateOneCommunity(
      { _id: new ObjectId(communityId) },
      updateCommunityDataObject
    );

    if (
      (communityUpdate?.isTokenGated && !communityData?.isTokenGated) ||
      (communityUpdate?.isPaidCommunity &&
        !communityData?.isPaidCommunity) ||
      (communityUpdate?.isFreeCommunity && !communityData?.isFreeCommunity)
    ) {
      await sendZapierMails({
        mailType: 'COMMUNITY_PRICE_UPDATE',
        mailCourse: communityCode,
        mailDataPayload: mailDataPricing,
      });
      await communityMemberAccessChangeKlaviyoEvents({
        country: communityCountry,
        email: communityAdminEmailList?.[0],
        chatConnected: communityData?.bots?.[0]?.type ? 'Yes' : 'No',
        chatPlatform: communityData?.bots?.[0]?.type,
        communityCreateDate: communityData?.createdAt.toString(),
        communityApplicationId: communityData?.communityApplicationId,
        communityName,
        communityCode,
        ...memberAccess,
      });
    }
    if (communityUpdate?.isActive && payload?.isActive) {
      if (communityUpdate?.referralCodeUsed && payload?.referralCode) {
        const referrer = await Learner.findOne({
          email: communityUpdate.referrerEmail,
        })
          .select('firstName')
          .lean();
        await communityCreatedReferralAlertEmails(
          communityUpdate.referrerEmail,
          referrer?.firstName ?? communityUpdate.referrerEmail,
          {
            community_url: NAS_IO_FRONTEND_URL + communitySlug,
            community_name: communityUpdate?.title,
            community_host: communityUpdate?.By,
            community_profile_image:
              communityUpdate?.thumbnailImgData?.mobileImgData?.src ??
              DEFAULT_COMMUNITY_PROFILE_IMAGE,
          }
        );
        await communityCreatedReferralEmails(
          communityUpdate.createdBy,
          communityUpdate.By,
          {
            cm_portal_url: `${NAS_IO_FRONTEND_URL}/portal/money?activeCommunityId=${communityUpdate?._id}`,
            referral_link: `${NAS_IO_FRONTEND_URL}?referralCode=${communityUpdate?.referralCodeUsed}`,
          }
        );
      } else {
        await communityCreatedEmails(
          communityUpdate.createdBy,
          communityUpdate.By,
          {
            community_code: communityData?.code,
            student_header_name: `Congratulations, ${communityUpdate?.By}!`,
            community_url: NAS_IO_FRONTEND_URL + communitySlug,
            cm_portal_url: `${NAS_IO_FRONTEND_URL}/portal?activeCommunityId=${communityData?._id}`,
            community_name: communityData?.title,
            community_profile_image:
              communityData?.thumbnailImgData?.mobileImgData?.src ??
              DEFAULT_COMMUNITY_PROFILE_IMAGE,
            isWhatsappExperienceCommunity:
              communityData?.isWhatsappExperienceCommunity,
          }
        );
      }

      await ActionEventService.sendMilestoneEvent({
        actionEventType:
          MILESTONE_ACTIVITY_TYPES.MILESTONE_COMMUNITY_CREATED,
        communityCode,
        communityObjectId: communityId,
        learnerObjectId,
      });
      // await CampaignService.claimFiveDollarBonus({
      //   communityData: communityUpdate,
      //   communityId,
      // });
      const languagePreference =
        currentUser?.learner?.languagePreference || 'en';
      await createDefaultAnnouncementPost(
        communityData,
        currentUser?._id,
        languagePreference
      );

      await sendZapierMails({
        mailType: 'ADMIN_COMMUNITY_ACTIVE',
        mailCourse: communityCode,
        mailDataPayload: mailDataPayloadCommon,
      });
      const deviceCategory =
        userAgent.includes('Android') || userAgent.includes('iPhone')
          ? 'Mobile'
          : 'Web';
      await communityCreatedKlaviyoEvents({
        country: communityCountry,
        email: communityAdminEmailList?.[0],
        chatConnected: communityData?.bots?.[0]?.type ? 'Yes' : 'No',
        chatPlatform: communityData?.bots?.[0]?.type,
        communityCreateDate: communityData?.createdAt.toString(),
        communityApplicationId: communityData?.communityApplicationId,
        timezone: communityData?.timezone,
        communityName,
        communityCode,
        userAgent,
        deviceCategory,
        languagePreference,
      });
    }

    if (updateData.link) {
      logger.info('Updating Community Landing Page with new link');
      await CommunityLandingPageTemplate.findOneAndUpdate(
        {
          slug: communitySlug,
          communityCode,
        },
        { slug: updateData.link },
        { new: true }
      );
    }

    if (payload?.coverMediaItems) {
      // delete removed video cover media items by marking folder item status as deleted.
      const oldCoverMediaItems = communityData?.coverMediaItems ?? [];
      const newCoverMediaItems = communityUpdate?.coverMediaItems ?? [];
      if (
        Array.isArray(oldCoverMediaItems) &&
        hasVideoCoverMediaItems(oldCoverMediaItems)
      ) {
        await deleteRemovedVideoCoverMediaItems({
          oldCoverMediaItems,
          newCoverMediaItems,
        });
      }
    }

    const isCommunityCodeBlacklistedForCustomMessage =
      await CustomWelcomeMessageConfigService.isCommunityCodeBlacklisted(
        communityCode
      );

    // to move to worker pool
    checkCommunityForFraud({
      communityId,
      communityData: updateData,
    });

    return {
      ...(communityUpdate ?? {}),
      showCustomWelcomeMessage:
        !isCommunityCodeBlacklistedForCustomMessage,
    };
  } catch (error) {
    logger.error('Error updating community:', error, error.stack);
    throw error || new Error('Cannot update community');
  }
};

const isDemoCommunity = async (communityId = null) => {
  let isDemo = false;
  try {
    if (!communityId) {
      return isDemo;
    }
    const demoCommunity =
      (await Community.findOne({
        _id: new ObjectId(communityId),
        isDemo: true,
      })) || null;
    if (demoCommunity && Object.keys(demoCommunity)?.length) {
      isDemo = true;
    }
    logger.info(`Community id ${communityId} has isDemo value: ${isDemo}`);
  } catch (error) {
    logger.error('Error checking for demo community:', error);
    isDemo = false;
  }
  return isDemo;
};

const updateCommunityLanguagePreference = async ({
  learner,
  languagePreference,
}) => {
  const communities = await Community.find({
    createdBy: learner.email,
  });

  if (communities.length > 0) {
    await communityManagerLanguagePreferenceChangeEvent({
      email: learner.email,
      languagePreference,
    });
  }
};

module.exports = {
  updateCommunityForPublicApi,
  getCommunityBotDetails,
  getLearnerCommunitySubscriptionDetails,
  getLearnersCommunityDetails,
  getCommunityCodesByIds,
  getCommunityById,
  getCommunitiesByIds,
  updateOneCommunity,
  updateCommunityData,
  linkValidation,
  isDemoCommunity,
  retrieveProductWithPrices,
  updateCommunityLanguagePreference,
  updatePricesForToggleChanges,
  checkCommunityForFraud,
  archiveAllPrices,
};
