/* eslint-disable no-param-reassign */
const ObjectId = require('mongoose').Types.ObjectId;

// models
const CommunityFolderItems = require('../../models/communityFolderItems.model');
const CommunityFolder = require('../../models/communityFolders.model');
const Community = require('../../models/community.model');
const WhatsappConnectedGroup = require('../../models/whatsappConnectedGroup.model');
const CommunityFolderPurchasesModel = require('../../models/communityFolderPurchases.model');
const CommunityFolderAccessLogs = require('../../models/communityFolderAccessLogs.model');

// services
const logger = require('../../../services/logger.service');
const {
  communityLibraryTypesMap,
  communityFolderItemTypesMap,
  communityLibraryStatusMap,
  COMMUNITY_FOLDER_PURCHASE_TYPES,
  FOLDER_ITEM_STATUS,
} = require('../../constants');
const {
  NASG_CLOUDFRONT_BASE_URL,
  NASG_VIDEO_LINK_REGEX,
  VIDEO_LINK_REGEX,
  DEFAULT_CURRENCY,
  EVENT_TYPES,
  PURCHASE_TYPE,
} = require('../../../constants/common');

const { NAS_IO_FRONTEND_URL } = require('../../../config');

const { ParamError } = require('../../../utils/error.util');

const {
  defaultPaginatedResponse,
  getPaginationDataAggregatePipelineStage,
  getNextAndPrevious,
} = require('../../../utils/pagination.util');
const { regexReplace } = require('../../../utils/string_handling');
const { PRIVATE_VIDEO_BASE_URL } = require('../../../config');
const {
  getAddonPriceInLocalCurrency,
} = require('./communityAddonPrice.service');
const membershipService = require('../../../services/membership');
const communityService = require('../../../services/community');

const communityFolderItemsModel = require('../../models/communityFolderItems.model');
const videoModel = require('../../../models/videos.model');
const communityFolderItemSignedUrlDetailsModel = require('../../models/communityFolderItemSignedUrlDetails.model');

const { ResourceNotFoundError } = require('../../../utils/error.util');
const CommunityEventsModel = require('../../models/communityEvents.model');
const learnersModel = require('../../../models/learners.model');
const ProgramModel = require('../../../models/program/program.model');
const { affiliateService } = require('../../../services/affiliate');
const { isCommunityIndexable } = require('./utils');
const {
  FOLDER_ITEM_COVER_MEDIA_FOLDER_TYPES,
  COVER_MEDIA_ENTITY_TYPES,
} = require('../../../constants/coverMediaItems.constant');

const {
  commonFilters,
} = require('../../../utils/communityFolders/folderItem.util');
const {
  getFolderItemById,
  getFolderItemByIdWithVideoInfo,
} = require('./communityFolderItems/index.service');
const {
  generateCoverMediaItems,
} = require('../../../utils/multipleCoverMediaItems.util');
const {
  purgeEntityLandingPageCache,
  ENTITY_LANDING_PAGE,
} = require('@/src/utils/memberPortalLinks.utils');

const nonManagerViewFilter = commonFilters.NON_MANAGER_VIEW;
const managerViewFilter = commonFilters.MANAGER_VIEW;

const getCorrectLinkUponRegexReplace = (link) => {
  if (link) {
    let newLink = regexReplace(
      link,
      NASG_VIDEO_LINK_REGEX,
      NASG_CLOUDFRONT_BASE_URL
    ).replace(/\s/g, '');
    newLink = regexReplace(
      newLink,
      VIDEO_LINK_REGEX,
      PRIVATE_VIDEO_BASE_URL
    ).replace(/\s/g, '');

    return newLink;
  }

  return null;
};

const getPopulatedFolderItemsInfo = async (
  communityFolderObjectId,
  filter,
  permissionFilter,
  parentSectionId,
  pageNum = null,
  pageSize = null,
  paginate = 0,
  isCommunityManager
) => {
  try {
    const params = {
      communityFolderObjectId: new ObjectId(communityFolderObjectId),
      ...filter,
      ...permissionFilter,
      parentSectionId,
    };

    const communityFolderItemAggregationPipeline = [
      {
        $match: params,
      },
      {
        $sort: {
          index: 1,
        },
      },
      {
        $lookup: {
          from: 'videos',
          localField: 'videoObjectId',
          foreignField: '_id',
          as: 'video',
        },
      },
      {
        $unwind: {
          path: '$video',
          preserveNullAndEmptyArrays: true,
        },
      },
    ];

    let result = await CommunityFolderItems.aggregate(
      communityFolderItemAggregationPipeline
    );
    if (paginate === 1) {
      result = result?.[0] || null;
      if (!result) {
        result = defaultPaginatedResponse;
      }
      const { next = null, previous = null } = getNextAndPrevious(
        pageNum,
        pageSize,
        result?.metadata?.total
      );
      result.metadata.next = next;
      result.metadata.previous = previous;
    }
    const resultData = paginate === 1 ? result?.data : result;

    resultData.forEach((folder, i) => {
      if (
        folder.type === communityFolderItemTypesMap.VIDEO &&
        folder.video
      ) {
        resultData[i].thumbnail = getCorrectLinkUponRegexReplace(
          resultData[i].thumbnail
        );

        if (resultData[i]?.mp4Link) {
          resultData[i].mp4Link = isCommunityManager
            ? getCorrectLinkUponRegexReplace(resultData[i].mp4Link)
            : '';
        }
        resultData[i].link = getCorrectLinkUponRegexReplace(
          resultData[i].link
        );
        resultData[i].video.thumbnailLink = getCorrectLinkUponRegexReplace(
          resultData[i].video.thumbnailLink
        );
        resultData[i].video.link = getCorrectLinkUponRegexReplace(
          resultData[i].video.link
        );
        resultData[i].video.hlsLink = getCorrectLinkUponRegexReplace(
          resultData[i].video.hlsLink
        );
      }
    });

    return resultData;
  } catch (error) {
    logger.error('error getting folder items ', error, error.stack);
    throw new Error('Error getting folder items for the section');
  }
};
const getFolderItemsByFolderId = async (
  communityFolderObjectId,
  isCommunityManager = false,
  filter = {},
  paginate = 0,
  pageNum = null,
  pageSize = null
) => {
  const permissionFilter = isCommunityManager
    ? managerViewFilter
    : nonManagerViewFilter;

  const params = {
    communityFolderObjectId: new ObjectId(communityFolderObjectId),
    ...filter,
    ...permissionFilter,
    parentSectionId: { $eq: null },
  };

  // If the filter has filter by _id, then no need to use communityFolderObjectId
  if ('_id' in filter) {
    delete params.communityFolderObjectId;
  }
  try {
    const communityFolderItemAggregationPipeline = [
      {
        $match: params,
      },
      {
        $sort: {
          index: 1,
        },
      },
      {
        $lookup: {
          from: 'videos',
          localField: 'videoObjectId',
          foreignField: '_id',
          as: 'video',
        },
      },
      {
        $unwind: {
          path: '$video',
          preserveNullAndEmptyArrays: true,
        },
      },
    ];
    if (paginate === 1) {
      const paginationDataAggregatePipelineStage =
        getPaginationDataAggregatePipelineStage(pageNum, pageSize);
      const paginationPipeline = {
        $facet: {
          metadata: [
            {
              $count: 'total',
            },
          ],
          data: paginationDataAggregatePipelineStage,
        },
      };
      communityFolderItemAggregationPipeline.push(paginationPipeline);
      communityFolderItemAggregationPipeline.push({
        $unwind: '$metadata',
      });
    }
    let result = await CommunityFolderItems.aggregate(
      communityFolderItemAggregationPipeline
    );

    const sectionsIndexAndId = {};
    const sections = result.filter((folderItem, index) => {
      if (folderItem.type === 'section') {
        sectionsIndexAndId[folderItem._id] = {
          index,
          folderItem,
        };
        return folderItem;
      }
      return false;
    });

    const fetchAllSections = sections.map(async (sectionInfo) => {
      const sectionIndexOnParentLevel =
        sectionsIndexAndId[sectionInfo._id].index;

      const folderItems = await getPopulatedFolderItemsInfo(
        communityFolderObjectId,
        filter,
        permissionFilter,
        sectionInfo._id, // parentSectionId
        null, // pageNum
        null, // pageSize
        0, // paginate
        isCommunityManager
      );
      result[sectionIndexOnParentLevel].items = folderItems;
      return true;
    });

    if (sections.length > 0) {
      await Promise.all(fetchAllSections);
    }

    if (paginate === 1) {
      result = result?.[0] || null;
      if (!result) {
        result = defaultPaginatedResponse;
      }
      const { next = null, previous = null } = getNextAndPrevious(
        pageNum,
        pageSize,
        result?.metadata?.total
      );
      result.metadata.next = next;
      result.metadata.previous = previous;
    }
    const resultData = paginate === 1 ? result?.data : result;
    resultData.forEach((folder, i) => {
      if (
        folder.type === communityFolderItemTypesMap.VIDEO &&
        folder.video
      ) {
        resultData[i].thumbnail = getCorrectLinkUponRegexReplace(
          resultData[i].thumbnail
        );

        if (resultData[i]?.mp4Link) {
          resultData[i].mp4Link = isCommunityManager
            ? getCorrectLinkUponRegexReplace(resultData[i].mp4Link)
            : '';
        }
        resultData[i].link = getCorrectLinkUponRegexReplace(
          resultData[i].link
        );
        resultData[i].video.thumbnailLink = getCorrectLinkUponRegexReplace(
          resultData[i].video.thumbnailLink
        );
        resultData[i].video.link = getCorrectLinkUponRegexReplace(
          resultData[i].video.link
        );
        resultData[i].video.hlsLink = getCorrectLinkUponRegexReplace(
          resultData[i].video.hlsLink
        );
      }
    });
    if (paginate === 1) {
      result.data = resultData;
    } else {
      result = resultData;
    }
    return result;
  } catch (error) {
    logger.error(
      `Error getting CommunityFolderItems for communityFolderObjectId ${communityFolderObjectId}`,
      error,
      error.stack
    );
    throw new Error(
      `Error getting CommunityFolderItems for communityFolderObjectId ${communityFolderObjectId}`
    );
  }
};

const getCurrentVideoItemsSizeByCommunityId = async (
  communityId,
  fileSizeLimitFromCommunityInMB,
  addExtraSizeInBytes = 0
) => {
  try {
    logger.info(
      `getCurrentVideoItemsSizeByCommunityId: ${communityId} - ${fileSizeLimitFromCommunityInMB}`
    );
    const defaultResponse = {
      canProceedToUploadTheFile: true,
      currentMemoryAvailable: fileSizeLimitFromCommunityInMB / 1000,
    };

    // Get folders for which storage should be excluded
    const deletedCommunityFoldersFilters = {
      communityObjectId: new ObjectId(communityId),
      status: communityLibraryStatusMap.DELETED,
    };

    const deletedCommunityFolders = await CommunityFolder.find(
      deletedCommunityFoldersFilters
    )
      .select('_id')
      .lean();

    const deletedCommunityFolderObjIds = deletedCommunityFolders.map(
      (communityFolder) => communityFolder._id
    );

    const videoItemsFileSizeInfo = await CommunityFolderItems.aggregate([
      {
        $match: {
          communityObjectId: new ObjectId(communityId),
          communityFolderObjectId: {
            $nin: deletedCommunityFolderObjIds,
          }, // exclude deleted folders
          type: communityFolderItemTypesMap.VIDEO,
          status: {
            $in: [
              FOLDER_ITEM_STATUS.PUBLISHED,
              FOLDER_ITEM_STATUS.UNPUBLISHED,
              FOLDER_ITEM_STATUS.PROCESSING,
            ],
          },
          folderType: {
            $exists: false,
          }, // exclude cover videos ( entity and checkpoint cover )
        },
      },
      {
        $group: {
          _id: '$videoObjectId',
          size: {
            $first: '$size',
          },
        },
      },
      {
        $group: {
          _id: 1,
          totalVideoSizeInBytes: {
            $sum: {
              $toLong: '$size',
            },
          },
        },
      },
    ]);

    if (!videoItemsFileSizeInfo || videoItemsFileSizeInfo.length === 0) {
      return defaultResponse;
    }

    const totalVideoSizeInBytes =
      videoItemsFileSizeInfo[0]?.totalVideoSizeInBytes +
      addExtraSizeInBytes;
    const totalVideoSizeInMB = totalVideoSizeInBytes / 1000 / 1000;

    const currentAvailableMemoryInMb =
      fileSizeLimitFromCommunityInMB -
      videoItemsFileSizeInfo[0]?.totalVideoSizeInBytes / 1000 / 1000;

    logger.info(
      `getCurrentVideoItemsSizeByCommunityId: current video file size in MB is ${totalVideoSizeInMB} for the communityId ${communityId}`
    );
    if (
      totalVideoSizeInMB &&
      totalVideoSizeInMB >= fileSizeLimitFromCommunityInMB
    ) {
      logger.info(
        `getCurrentVideoItemsSizeByCommunityId: exceeded limit ${totalVideoSizeInMB} >= ${fileSizeLimitFromCommunityInMB} for the communityId ${communityId}`
      );
      return {
        canProceedToUploadTheFile: false,
        currentMemoryAvailable:
          currentAvailableMemoryInMb >= 0
            ? currentAvailableMemoryInMb / 1000
            : 0,
      };
    }
    return {
      canProceedToUploadTheFile: true,
      currentMemoryAvailable:
        currentAvailableMemoryInMb >= 0
          ? currentAvailableMemoryInMb / 1000
          : 0,
    };
  } catch (error) {
    logger.error(
      `Error getting CommunityFolderItems for communityId ${communityId}`,
      error,
      error.stack
    );
    throw new Error(
      `Error getting CommunityFolderItems for communityId ${communityId}`
    );
  }
};

const getFilteredFolderItemsByCommunityId = async (
  communityId,
  isCommunityManager = false,
  filter
) => {
  const permissionFilter = isCommunityManager
    ? managerViewFilter
    : nonManagerViewFilter;
  const params =
    filter === '' || !filter
      ? {
          $and: [
            {
              communityObjectId: new ObjectId(communityId),
              ...permissionFilter,
            },
          ],
        }
      : {
          $and: [
            {
              communityObjectId: new ObjectId(communityId),
              ...permissionFilter,
            },
            {
              $or: [{ title: { $regex: filter, $options: 'i' } }],
            },
          ],
        };

  try {
    const pipeline = [
      { $match: params },
      {
        $lookup: {
          from: 'community_folders',
          localField: 'communityFolderObjectId',
          foreignField: '_id',
          as: 'folder',
        },
      },
      {
        $unwind: {
          path: '$folder',
          includeArrayIndex: 'string',
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $project: {
          _id: 1,
          communityObjectId: 1,
          communityFolderObjectId: 1,
          type: 1,
          title: 1,
          thumbnail: 1,
          shortUrl: 1,
          description: 1,
          icon: 1,
          folderType: '$folder.type',
        },
      },
      {
        $addFields: {
          type: {
            $cond: [
              { $eq: ['$type', communityFolderItemTypesMap.LINK] },
              communityLibraryTypesMap.LINK,
              {
                $cond: [
                  { $eq: ['$type', communityFolderItemTypesMap.VIDEO] },
                  communityLibraryTypesMap.VIDEO,
                  communityLibraryTypesMap.RESOURCE,
                ],
              },
            ],
          },
        },
      },
    ];
    const result = await CommunityFolderItems.aggregate(pipeline);
    logger.info('Community Folder items found', result);
    result.forEach((folder, i) => {
      if (folder.type === communityLibraryTypesMap.VIDEO) {
        result[i].thumbnail = getCorrectLinkUponRegexReplace(
          result[i].thumbnail
        );
      }
    });
    return result;
  } catch (err) {
    logger.error(
      'Error filtering folder items from the community id: ',
      err,
      err.stack
    );
    throw new Error('Error filtering folder items from the community id');
  }
};

const getFolderItemsByCommunityId = async (
  communityObjectId,
  filters = {}
) => {
  try {
    const result = await CommunityFolderItems.find({
      communityObjectId: new ObjectId(communityObjectId),
      folderType: { $ne: 'challenge_checkpoint' },
      ...filters,
    });
    return result;
  } catch (error) {
    logger.error(
      `Error getting CommunityFolderItems for communityObjectId ${communityObjectId}`,
      error,
      error.stack
    );
    throw new Error(
      `Error getting CommunityFolderItems for communityObjectId ${communityObjectId}`
    );
  }
};

const createOneFolderItem = async (params) => {
  if (Object.keys(params).length === 0 && params.constructor === Object) {
    throw new ParamError('Empty Params');
  }

  logger.info(
    'Creating community folder item with the given params:',
    params
  );
  const doc = {};
  const keys = Object.keys(params);
  for (let i = 0; i < keys.length; i++) {
    doc[keys[i]] = params[keys[i]];
  }

  if (doc.type === communityFolderItemTypesMap.VIDEO) {
    doc.status = communityLibraryStatusMap.PROCESSING;
  }

  const folderType = doc.folderType?.toLowerCase();
  const entityCoverVideoMediaTypes = Object.values(
    FOLDER_ITEM_COVER_MEDIA_FOLDER_TYPES
  );
  if (
    entityCoverVideoMediaTypes.includes(folderType) && // if cover video media type
    folderType !== FOLDER_ITEM_COVER_MEDIA_FOLDER_TYPES.COMMUNITY // exception if cover video is for community. then can directly mark as valid.
  ) {
    doc.hasValidFolderObjectId = false;
  } else {
    doc.hasValidFolderObjectId = true;
  }

  let communityFolderItem = new CommunityFolderItems(doc);

  communityFolderItem = await communityFolderItem.save();
  logger.info('Created Community Folder Item: ', communityFolderItem);

  return communityFolderItem;
};

const duplicateFolderItem = async ({
  folderItemObjectId,
  associatedCommunityFolderObjectIds,
  session,
}) => {
  const folderItem = await CommunityFolderItems.findOne({
    _id: new ObjectId(folderItemObjectId),
  }).lean();
  if (!folderItem) {
    return [];
  }
  const quantity = associatedCommunityFolderObjectIds.length;
  const newFolderItems = [];
  for (let i = 0; i < quantity; i++) {
    const newFolderItem = { ...folderItem };
    newFolderItem.communityFolderObjectId = new ObjectId(
      associatedCommunityFolderObjectIds[i]
    );
    delete newFolderItem.__v;
    delete newFolderItem.createdAt;
    delete newFolderItem.updatedAt;
    delete newFolderItem._id;
    newFolderItems.push(newFolderItem);
  }
  const result = await CommunityFolderItems.insertMany(newFolderItems, {
    session,
  });
  return result;
};

const updateOneFolderItem = async (filter, update) => {
  const updatedData = await CommunityFolderItems.findOneAndUpdate(
    filter,
    update,
    { new: true }
  );
  return updatedData;
};

const softDeleteFolderItemById = async (id) => {
  try {
    const result = await CommunityFolderItems.findByIdAndUpdate(
      new ObjectId(id),
      { status: communityLibraryStatusMap.DELETED },
      {
        new: true,
      }
    );

    if (result.type === communityFolderItemTypesMap.SECTION) {
      // delete all the folder items related to the section
      await CommunityFolderItems.updateMany(
        {
          parentSectionId: new ObjectId(id),
        },
        {
          status: communityLibraryStatusMap.DELETED,
        }
      );
    }
    return result;
  } catch (error) {
    logger.error(
      `Error soft deleting CommunityFolderItem by id ${id}`,
      error,
      error.stack
    );
    throw new Error(`Error soft deleting CommunityFolderItem by id ${id}`);
  }
};

const patchManyFolderItemsByFolderId = async (id, payload, condition) => {
  try {
    const updatedData = await CommunityFolderItems.updateMany(
      {
        communityFolderObjectId: new ObjectId(id),
        ...condition,
      },
      payload,
      { new: true }
    );
    return updatedData;
  } catch (error) {
    logger.error('Error patching folder items:', error, error.stack);
    throw new Error('Cannot patch folder items');
  }
};

const softDeleteManyFolderItemsByFolderId = async (id) => {
  try {
    const result = await CommunityFolderItems.updateMany(
      { communityFolderObjectId: new ObjectId(id) },
      { status: communityLibraryStatusMap.DELETED },
      { new: true }
    );
    return result;
  } catch (error) {
    logger.error(
      `Error soft deleting CommunityFolderItems for communityFolderObjectId ${id}`,
      error,
      error.stack
    );
    throw new Error(
      `Error soft deleting CommunityFolderItems for communityFolderObjectId ${id}`
    );
  }
};

const getFolderItemsByIdWithMetaData = async (
  communityFolderId,
  isCommunityManager
) => {
  const folderItems = await getFolderItemsByFolderId(
    communityFolderId,
    isCommunityManager
  );
  const videoCount = folderItems.filter(
    (i) => i.type === communityFolderItemTypesMap.VIDEO
  ).length;

  let totalItemsCount = 0;
  folderItems.forEach((folderItem) => {
    if (folderItem.type === 'section') {
      totalItemsCount += folderItem.items.length;
    } else {
      totalItemsCount += 1;
    }
  });
  return {
    videoCount,
    totalItemsCount,
    otherFolderItemCount: folderItems.length - videoCount,
    folderItems,
  };
};

const getFilteredFolderItems = async (folderItems) => {
  try {
    const filteredFolderItems = folderItems.map((folderItem) => {
      const {
        _id: folderItemId,
        description,
        status,
        index,
        title,
        thumbnail,
        type,
        format,
        size,
        duration,
        platform,
      } = folderItem;

      const filteredFolderItemsObj = {
        _id: folderItemId,
        description,
        status,
        index,
        title,
        thumbnail,
        type,
        format,
        size,
        duration,
        platform,
      };

      if (folderItem?.parentSectionId) {
        filteredFolderItemsObj.parentSectionId =
          folderItem?.parentSectionId;
      }
      return filteredFolderItemsObj;
    });
    return filteredFolderItems;
  } catch (error) {
    logger.error(
      'error in getting the folder items for the folder',
      error,
      error.stack
    );
    throw new Error('error in getting folder items for the folder');
  }
};

const getCommunityResourcePage = async (
  community,
  resourceSlug,
  ip,
  shortUrl,
  learnerObjectId,
  selectedAmount = null,
  affiliateCode = null,
  paymentMethodCountryCode = null,
  paymentProvider = null
) => {
  try {
    let filter = {};

    if (shortUrl) {
      filter = { shortUrl, status: 'Published' };
    } else {
      shortUrl = `${NAS_IO_FRONTEND_URL}${community.link}/${resourceSlug}`;

      filter = {
        $or: [
          { resourceSlug: `/${resourceSlug}`, status: 'Published' },
          {
            shortUrl,
            status: 'Published',
          },
        ],
        communityObjectId: community?._id,
      };
    }

    const resourceFolder = await CommunityFolder.findOne({
      ...filter,
    }).lean();

    if (!resourceFolder) {
      const errorMessage = `No published resource found with slug: ${shortUrl}`;
      logger.error(errorMessage);
      throw new ResourceNotFoundError(errorMessage);
    }

    const resource = resourceFolder;

    // Assign default currency if it does not exists
    resource.currency = resource.currency ?? DEFAULT_CURRENCY;

    if (resource.access === COMMUNITY_FOLDER_PURCHASE_TYPES.PAID) {
      const priceDetails = await getAddonPriceInLocalCurrency({
        ip,
        addon: resource,
        communityObjectId: community._id,
        selectedAmount,
        paymentMethodCountryCode,
        paymentProvider,
      });
      logger.info('Price in local currency: ', priceDetails);
      resource.priceDetails = priceDetails;
    }

    const priceFieldsToRemove = [
      'localiseForAllCountries',
      'countryWisePrice',
    ];

    priceFieldsToRemove.forEach(
      (priceFieldToRemove) => delete resource[priceFieldToRemove]
    );

    if (learnerObjectId) {
      const isPurchased = await CommunityFolderPurchasesModel.findOne({
        learnerObjectId,
        folderObjectId: resource._id,
      }).lean();

      resource.isPurchased = !!isPurchased;
    }

    resource.coverMediaItems = await generateCoverMediaItems({
      entity: resource,
      entityType: COVER_MEDIA_ENTITY_TYPES.FOLDER,
    });

    if (resourceFolder) {
      const [folderItems, folderViewCount] = await Promise.all([
        getFolderItemsByFolderId(resourceFolder?._id, false, {
          status: 'Published',
        }),
        CommunityFolderAccessLogs.countDocuments({
          communityFolderObjectId: new ObjectId(resourceFolder?._id),
        }),
      ]);

      let totalItemsCount = 0;
      const filteredOutFolderItems = folderItems.map(
        async (folderItem) => {
          const {
            _id: folderItemId,
            description,
            status,
            index,
            title,
            thumbnail,
            type,
            format,
            size,
            duration,
            platform,
          } = folderItem;

          const filteredFolderItemsObj = {
            _id: folderItemId,
            platform,
            description,
            status,
            index,
            title,
            thumbnail,
            type,
            format,
            size,
            duration,
          };

          if (folderItem?.type === communityFolderItemTypesMap.SECTION) {
            filteredFolderItemsObj.items = await getFilteredFolderItems(
              folderItem?.items
            );
            totalItemsCount += folderItem.items.length;
          } else {
            totalItemsCount += 1;
          }

          return filteredFolderItemsObj;
        }
      );

      const filteredFolderItems = await Promise.all(
        filteredOutFolderItems
      );

      const videoCount = filteredFolderItems.filter(
        (i) => i?.type === communityFolderItemTypesMap?.VIDEO
      )?.length;
      const otherFolderItemCount = totalItemsCount - videoCount;

      resource.folderItems = filteredFolderItems;
      resource.folderViewCount = folderViewCount;
      resource.totalItemsCount = totalItemsCount;
      resource.videoCount = videoCount;
      resource.otherFolderItemCount = otherFolderItemCount;
    }

    const affiliateInfo = await affiliateService.retrieveAffiliateInfo({
      communityObjectId: community._id,
      affiliateCode,
      entityObjectId: resource._id,
      entityType:
        resource.type === communityLibraryTypesMap.SESSION
          ? PURCHASE_TYPE.SESSION
          : PURCHASE_TYPE.FOLDER,
    });

    const resourceInfo = {
      ...resource,
      parentResourceType: resourceFolder?.type || null,
      parentThumbnail: resourceFolder?.thumbnail || null,
      parentEmoji: resourceFolder?.emoji || null,
      affiliateInfo,
    };

    if (resourceInfo.location) {
      if (!resourceInfo.showLocation) {
        resourceInfo.location = {
          type: resourceInfo.location?.type,
        };
      }
    }

    if (resourceInfo.hostInfo) {
      const { hostLearnerObjectId } = resourceInfo.hostInfo;

      const hostLearnerInfo = await learnersModel.findOne({
        _id: hostLearnerObjectId,
      });

      if (hostLearnerInfo) {
        resourceInfo.hostInfo = {
          profileImage: hostLearnerInfo.profileImage,
          hostName: `${hostLearnerInfo.firstName} ${
            hostLearnerInfo?.lastName ?? ''
          }`,
          hostBio: resourceInfo.hostInfo.hostBio,
          hostTitle: resourceInfo.hostInfo.hostTitle,
        };

        if (
          !resourceInfo.hostInfo.hostBio &&
          !resourceInfo.hostInfo.hostTitle
        ) {
          const differentHostSession = await CommunityFolder.findOne({
            'hostInfo.hostLearnerObjectId': hostLearnerObjectId,
            status: 'Published',
            'hostInfo.hostBio': { $exists: true },
            'hostInfo.hostTitle': { $exists: true },
            _id: { $ne: resourceInfo._id },
          })
            .sort('_id')
            .select('hostInfo')
            .lean();

          if (differentHostSession) {
            resourceInfo.hostInfo.hostBio =
              differentHostSession.hostInfo?.hostBio ?? '';
            resourceInfo.hostInfo.hostTitle =
              differentHostSession.hostInfo?.hostTitle ?? '';
          }
        }
      }
    }
    const communityInfo = community;

    logger.info(
      `Maping profileImages of learners to event with slug: ${resourceSlug}`
    );

    // const memberCount =
    //   await communitySubscriptionsService.getMemberCountByCommunityCode(
    //     community?.code
    //   );
    const memberCountInfo =
      await membershipService.countService.countCommunityMembers({
        communityCode: community?.code,
      });

    const upcomingEventsCount = await CommunityEventsModel.countDocuments({
      communities: community._id,
      type: EVENT_TYPES.LIVE,
    });

    communityInfo.members = memberCountInfo?.count;
    communityInfo.totalMemberCount = memberCountInfo?.count;
    communityInfo.upcomingEventsCount = upcomingEventsCount;
    if (communityInfo?.isWhatsappExperienceCommunity) {
      const communityBots = communityInfo?.bots ?? [];
      const whatsappBot = communityBots.filter(
        (bot) => bot.type === 'Whatsapp'
      );

      if (whatsappBot.length > 0 && whatsappBot[0]?.serverKey) {
        const whatsappGroup = await WhatsappConnectedGroup.findOne({
          whatsappGroupId: whatsappBot[0]?.serverKey,
        }).select('name');
        if (whatsappGroup) {
          // const numberOfNonSubscriberWhatsappMembers =
          //   await WhatsappParticipants.count({
          //     communityObjectId: community?._id,
          //     isInWhatsApp: true,
          //     subscriptionObjectId: null,
          //   });
          // const totalWhatsappMembers = await WhatsappParticipants.count({
          //   communityObjectId: community?._id,
          //   isInWhatsApp: true,
          // });
          communityInfo.whatsappGroupName = whatsappGroup.name;
          communityInfo.whatsappMemberCount =
            memberCountInfo?.statusBreakdown?.inWhatsappGroup;
          // communityInfo.totalMemberCount +=
          //   numberOfNonSubscriberWhatsappMembers;
        }
      }
    }
    communityInfo.profileImage =
      communityInfo?.thumbnailImgData?.desktopImgData?.src;

    return { resourceInfo, communityInfo };
  } catch (err) {
    logger.error('getCommunityResourcePage error', err, err.stack);
    throw err;
  }
};

const getResourceBySlug = async (
  communityLink,
  slug,
  ip,
  learnerObjectId,
  selectedAmount = null,
  affiliateCode = null,
  paymentMethodCountryCode = null,
  paymentProvider = null
) => {
  let community = {};

  let shortUrl;

  community =
    await communityService.getCommunityService.getCommunityByLink({
      link: `/${communityLink}`,
      projection: {
        $project: {
          createdBy: 0,
          'platforms.link': 0,
          platformPreviousData: 0,
          taskMetaData: 0,
          botsPreviousData: 0,
        },
      },
    });

  if (!community) {
    community = await Community.findOne({
      communityShortCode: communityLink,
    });

    if (community) {
      shortUrl = `${NAS_IO_FRONTEND_URL}/${communityLink}/${slug}`;
    } else {
      throw new ResourceNotFoundError(
        `Community doesnt exist with short code: ${communityLink}`
      );
    }
  }

  community.indexable = isCommunityIndexable(community);

  const resourceSlug = `${slug}`;

  const resource = await getCommunityResourcePage(
    community,
    resourceSlug,
    ip,
    shortUrl,
    learnerObjectId,
    selectedAmount,
    affiliateCode,
    paymentMethodCountryCode,
    paymentProvider
  );

  const resourceType = resource?.resourceInfo?.type;

  switch (resourceType) {
    case 'external_link':
    case 'file':
      delete resource?.resourceInfo?.link;
      break;
    default:
      logger.info(
        'Nothing to delete for this type of resource',
        resourceType
      );
      break;
  }

  return resource;
};

const reorderFoldersItems = async (folderId, folderItems) => {
  try {
    const updateOperations = folderItems.map((folderItem) => {
      const update = {
        $set: {
          index: folderItem.index,
        },
      };
      // eslint-disable-next-line no-prototype-builtins
      if (folderItem.hasOwnProperty('parentSectionId')) {
        if (folderItem.parentSectionId) {
          update.$set.parentSectionId = folderItem.parentSectionId;
        } else {
          update.$unset = { parentSectionId: 1 };
        }
      }
      return {
        updateOne: {
          filter: { _id: folderItem._id },
          update: { ...update },
        },
      };
    });
    logger.info(
      'reorderFoldersItemService: now updating the folder Items for the folderId',
      folderId,
      'with the following updateOperations',
      updateOperations
    );
    await communityFolderItemsModel.bulkWrite(updateOperations);
    return {
      data: folderItems,
      message: 'Folder items reordered successfully',
    };
  } catch (error) {
    logger.error(
      'Error occurred while changing the order of the folder items ',
      error.message,
      error.stack
    );
    throw error;
  }
};

// this function is going to be used a template to update the progress of the video upload status it can be an error or just progress update
const updateStatusOfVideo = async (srcVideo, updateInfoObj) => {
  logger.info(
    'updateVideoUploadStatus: srcVideo and updateInfoObj',
    srcVideo,
    JSON.stringify(updateInfoObj)
  );
  // get the communityFolderItemObjectId from the communityFolderItemSignedUrlDetailsModel
  const communitySignedUrlItem =
    await communityFolderItemSignedUrlDetailsModel.findOne(
      {
        s3Key: srcVideo,
      },
      {
        communityFolderItemObjectId: 1,
        _id: 0,
      }
    );
  logger.info(
    'updateVideoUploadStatus: communityFolderItemId',
    communitySignedUrlItem
  );
  const { communityFolderItemObjectId } = communitySignedUrlItem || {};

  // update the communityFolderItemProgressModel with the updateInfoObj
  const updatedCommunityFolderItemProgress =
    await communityFolderItemsModel.findOneAndUpdate(
      {
        _id: communityFolderItemObjectId,
      },
      {
        $set: {
          ...updateInfoObj,
        },
      },
      {
        new: true,
      }
    );
  logger.info(
    'updateVideoUploadStatus: updatedCommunityFolderItemProgress',
    updatedCommunityFolderItemProgress
  );
  return true;
};

const updateFolderItemForVideoLinks = async ({
  videoObjectId,
  thumbnailLink,
  hlsLink,
  mpdLink,
  mp4Link,
}) => {
  try {
    // GET THE VIDEO OBJECT ID AND UPDATE THE VIDEO OBJECT WITH THE LINKS

    await videoModel.findOneAndUpdate(
      {
        _id: videoObjectId,
      },
      {
        $set: {
          thumbnailLink,
          hlsLink,
          link: mpdLink,
        },
      },
      {
        new: true,
      }
    );
    // UPDATE THE MP4 link on the communityFolderItem

    await communityFolderItemsModel.updateMany(
      {
        videoObjectId,
        status: {
          $ne: communityLibraryStatusMap.DELETED,
        },
      },
      {
        $set: {
          thumbnail: thumbnailLink,
          mp4Link,
          status: 'Published',
        },
      }
    );

    const updatedCommunityFolderItems = await communityFolderItemsModel
      .find({
        videoObjectId,
      })
      .read('primary')
      .lean();

    const challengeCoverVideoItems = updatedCommunityFolderItems.filter(
      (item) => item.folderType === 'challenge_cover_video'
    );

    if (challengeCoverVideoItems.length > 0) {
      const communityObjectId =
        updatedCommunityFolderItems?.[0]?.communityObjectId;

      const community = await Community.findOne({
        _id: new ObjectId(communityObjectId),
      }).select('link');

      const programIds = challengeCoverVideoItems.map(
        (item) => new ObjectId(item.communityFolderObjectId)
      );

      const programs = await ProgramModel.find({
        _id: { $in: programIds },
      }).select('type slug');

      const programMap = new Map();
      programs.forEach((program) => {
        programMap.set(program._id.toString(), program);
      });

      await Promise.all(
        challengeCoverVideoItems.map(async (item) => {
          const program = programMap.get(
            item.communityFolderObjectId.toString()
          );
          if (community && program) {
            await purgeEntityLandingPageCache({
              community,
              entityType: ENTITY_LANDING_PAGE.CHALLENGE,
              entitySlug: program.slug,
            });
          }
          return true;
        })
      );
    }

    return updatedCommunityFolderItems;
  } catch (error) {
    logger.error(
      'Error occurred while updating the video file status ',
      error,
      error.stack
    );
    throw error;
  }
};

const updateVideoFileStatus = async ({ detail }) => {
  try {
    if (detail.status === 'STATUS_UPDATE') {
      const { userMetadata } = detail || {};
      const { srcVideo } = userMetadata || {};
      if (srcVideo) {
        await updateStatusOfVideo(srcVideo, {
          processingPercentComplete:
            detail?.jobProgress?.jobPercentComplete,
          currentProcessingPhase: detail?.jobProgress?.currentPhase,
        });
        return true;
      }
    } else if (detail.status === 'ERROR') {
      const { userMetadata } = detail || {};
      const { srcVideo } = userMetadata || {};
      await updateStatusOfVideo(srcVideo, {
        currentProcessingPhase: 'ERROR',
        status: communityLibraryStatusMap.ERROR,
        processingError: detail?.errorMessage,
      });
    }
  } catch (error) {
    logger.error(
      'Error occurred while updating the video file status ',
      error,
      error.stack
    );
    return false;
  }
};

module.exports = {
  getFolderItemById,
  getFolderItemsByFolderId,
  getFilteredFolderItemsByCommunityId,
  createOneFolderItem,
  duplicateFolderItem,
  updateOneFolderItem,
  softDeleteFolderItemById,
  getFolderItemsByCommunityId,
  patchManyFolderItemsByFolderId,
  softDeleteManyFolderItemsByFolderId,
  getFolderItemsByIdWithMetaData,
  getResourceBySlug,
  reorderFoldersItems,
  getCurrentVideoItemsSizeByCommunityId,
  updateVideoFileStatus,
  updateFolderItemForVideoLinks,
  getFolderItemByIdWithVideoInfo,
};
