// models
const CommunityFolderAccessLogs = require('../../models/communityFolderAccessLogs.model');
// services
const logger = require('../../../services/logger.service');

const createOneFolderAccessLog = async (params) => {
  if (Object.keys(params).length === 0 && params.constructor === Object) {
    throw new Error('Empty Params');
  }

  try {
    const folderAccessLog = await CommunityFolderAccessLogs.create(params);
    return folderAccessLog.toObject();
  } catch (error) {
    logger.error('Error creating a folder access log:', error);
    throw new Error(`Error creating a folder access log`);
  }
};

module.exports = {
  createOneFolderAccessLog,
};
