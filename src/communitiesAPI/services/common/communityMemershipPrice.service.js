const {
  DEFAULT_CURRENCY,
  PAYMENT_PROVIDER,
} = require('../../../constants/common');
const RecurringPlanLocalCurrencySupported = require('../../models/recurringPlanLocalCurrencySupported.model');
const {
  retrievePaymentFeeStructure,
} = require('../../../services/config/paymentFeeStructureConfig.service');
const FeeService = require('./fee.service');
const { CENTS_PER_DOLLAR } = require('../../constants');
const { ToUserError } = require('../../../utils/error.util');
const { COMMUNITY_ERROR } = require('../../../constants/errorCode');
const PaymentBackendRpc = require('../../../rpc/paymentBackend');
const logger = require('../../../services/logger.service');

function generateAmountInCents(
  amount,
  currency,
  allowDecimal,
  minInCents,
  limitInCents
) {
  const amountInDollar = allowDecimal ? amount : Math.ceil(amount);

  const unitAmountInCents = Math.ceil(amountInDollar * CENTS_PER_DOLLAR);

  if (unitAmountInCents < minInCents || unitAmountInCents > limitInCents) {
    throw new ToUserError('Invalid price', COMMUNITY_ERROR.INVALID_PRICE, {
      unitAmountInCents: unitAmountInCents / CENTS_PER_DOLLAR,
      currency,
      minInCents: minInCents / CENTS_PER_DOLLAR,
      limitInCents: limitInCents / CENTS_PER_DOLLAR,
    });
  }

  return unitAmountInCents;
}

function addOtherCurrencyPricesForBaseCurrencyUsd(
  baseCurrency,
  communityPrices,
  existingCommunityPrices = []
) {
  if (baseCurrency !== DEFAULT_CURRENCY) {
    return communityPrices;
  }

  const existingNonBaseCurrencyPrices = existingCommunityPrices.filter(
    (price) => price.currency !== baseCurrency
  );

  return [...communityPrices, ...existingNonBaseCurrencyPrices];
}

exports.createProductWithPrices = async (
  communityTitle,
  prices,
  baseCurrency = DEFAULT_CURRENCY,
  paymentProvider = PAYMENT_PROVIDER.STRIPE,
  priceAmountUnitInCents = false
) => {
  const { supported: recurringPlanLocalCurrencySupported = [] } =
    await RecurringPlanLocalCurrencySupported.findOne().lean();

  const communityPrices = prices.map(
    ({ amount, currency, interval, intervalCount }) => {
      const { allowDecimal, minInCents, limitInCents } =
        recurringPlanLocalCurrencySupported.find(
          (currencySupported) => currencySupported.currency === currency
        ) ?? {};

      if (currency !== baseCurrency) {
        throw Error('Currency should be in baseCurrency!');
      }

      const cmSetPrice = generateAmountInCents(
        priceAmountUnitInCents
          ? amount / CENTS_PER_DOLLAR
          : parseFloat(amount),
        currency,
        allowDecimal,
        minInCents,
        limitInCents
      );

      return {
        cmSetPrice,
        currency,
        interval,
        intervalCount,
      };
    }
  );

  const data = {
    name: communityTitle,
    description: communityTitle,
    prices: [],
    baseCurrency,
    paymentProvider,
  };

  try {
    const paymentBackendRpc = new PaymentBackendRpc();
    await paymentBackendRpc.init();
    const result = await paymentBackendRpc.createStripeProduct(data);

    return {
      productId: result?.product?.id,
      communityPrices,
    };
  } catch (error) {
    logger.error(
      'Error in creating product with prices:',
      error,
      error.stack
    );
    throw new Error('Unable to create product with prices');
  }
};

exports.updatePrices = async (
  prices,
  baseCurrency = DEFAULT_CURRENCY,
  existingCommunityPrices = [],
  priceAmountUnitInCents = false
) => {
  const { supported: recurringPlanLocalCurrencySupported = [] } =
    await RecurringPlanLocalCurrencySupported.findOne().lean();

  const communityPrices = prices.map(
    ({ amount, currency, interval, intervalCount }) => {
      const { allowDecimal, minInCents, limitInCents } =
        recurringPlanLocalCurrencySupported.find(
          (currencySupported) => currencySupported.currency === currency
        ) ?? {};

      if (currency !== baseCurrency) {
        throw Error('Currency should be in baseCurrency!');
      }

      if (
        (interval !== 'month' && interval !== 'year') ||
        intervalCount > 12 ||
        intervalCount < 1
      ) {
        throw new ToUserError(
          'Invalid interval details',
          COMMUNITY_ERROR.INVALID_INTERVAL_DETAILS,
          {
            interval,
            intervalCount,
          }
        );
      }

      const cmSetPrice = generateAmountInCents(
        priceAmountUnitInCents
          ? amount / CENTS_PER_DOLLAR
          : parseFloat(amount),
        currency,
        allowDecimal,
        minInCents,
        limitInCents
      );

      return {
        cmSetPrice,
        currency,
        interval,
        intervalCount,
      };
    }
  );

  const finalCommunityPrices = addOtherCurrencyPricesForBaseCurrencyUsd(
    baseCurrency,
    communityPrices,
    existingCommunityPrices
  );

  return finalCommunityPrices;
};

const createOrUpdatePricing = async ({
  communityData,
  paymentProvider,
  existingStripeProductId,
  updatePrice,
  prices,
  legacyPriceSetting,
}) => {
  let updatedPrice;
  let stripeProductId;
  if (!existingStripeProductId) {
    const { productId, communityPrices } =
      await this.createProductWithPrices(
        communityData.title,
        prices ?? legacyPriceSetting,
        communityData.baseCurrency,
        paymentProvider
      );

    updatedPrice = communityPrices;
    stripeProductId = productId;
  } else if (updatePrice) {
    stripeProductId = existingStripeProductId;
    const existingCommunityPrices = communityData.prices ?? [];

    const updatedPricesDetail = await this.updatePrices(
      prices ?? legacyPriceSetting,
      communityData.baseCurrency,
      existingCommunityPrices
    );

    updatedPrice = updatedPricesDetail;
  }

  return {
    updatedPrice,
    stripeProductId,
  };
};
exports.createOrUpdateMembershipPricing = async ({
  communityData,
  paymentProvider,
  prices,
  legacyPriceSetting,
  updatePrice,
}) => {
  const updateData = {};
  if (
    [PAYMENT_PROVIDER.STRIPE, PAYMENT_PROVIDER.STRIPE_US].includes(
      paymentProvider
    )
  ) {
    const { stripeProductId, updatedPrice } = await createOrUpdatePricing({
      communityData,
      paymentProvider: PAYMENT_PROVIDER.STRIPE,
      existingStripeProductId: communityData.stripeProductId,
      updatePrice,
      prices,
      legacyPriceSetting,
    });
    updateData.prices = updatedPrice;
    updateData.stripeProductId = stripeProductId;

    const { stripeProductId: stripeUsProductId } =
      await createOrUpdatePricing({
        communityData,
        paymentProvider: PAYMENT_PROVIDER.STRIPE_US,
        existingStripeProductId: communityData.stripeUsProductId,
        updatePrice,
        prices,
        legacyPriceSetting,
      });
    updateData.stripeUsProductId = stripeUsProductId;
  } else {
    const { stripeProductId, updatedPrice } = await createOrUpdatePricing({
      communityData,
      paymentProvider,
      existingStripeProductId: communityData.stripeProductId,
      updatePrice,
      prices,
      legacyPriceSetting,
    });
    updateData.prices = updatedPrice;
    updateData.stripeProductId = stripeProductId;
  }

  return updateData;
};
