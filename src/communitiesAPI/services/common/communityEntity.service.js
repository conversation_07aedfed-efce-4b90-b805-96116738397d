const { ObjectId } = require('mongoose').Types;

// constants
const { NAS_IO_FRONTEND_URL } = require('../../../config');
const {
  CONFIG_TYPES,
  PAYMENT_PROVIDER,
  VERIFY_PAYMENT_ACCESS_TYPE,
} = require('../../../constants/common');

/* eslint-disable no-case-declarations */
/* eslint-disable no-await-in-loop */
const logger = require('../../../services/logger.service');
const Event = require('../../models/communityEvents.model');
const CommunityFolder = require('../../models/communityFolders.model');
const Community = require('../../models/community.model');
const CommunityAddonTransaction = require('../../models/communityAddonTransactions.model');
const EventAttendeesModal = require('../../models/eventAttendees.model');
const EventService = require('../../../services/event');
const { getConfigByType } = require('../../../services/config.service');
const { sleepForSeconds } = require('../../utils/timeOutRoutes');
const {
  COMMUNITY_ENTITY_ONE_TIME_PAYMENT_STATUSES,
  COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES,
} = require('../../constants');
const {
  ToUserError,
  ResourceNotFoundError,
  ParamError,
} = require('../../../utils/error.util');
const { GENERIC_ERROR } = require('../../../constants/errorCode');
const ProgramModel = require('../../../models/program/program.model');
const PaymentBackendRpc = require('../../../rpc/paymentBackend');
const { mapFailureCodeToPaymentError } = require('./paymentErrorUtils');
const UserModel = require('../../../models/users.model');
const AuthServiceRpc = require('../../../rpc/authService.rpc');

const verifyPaidEntityPayment = async (entitySignupId, isLoginToken) => {
  if (!entitySignupId) {
    throw new ParamError('Missing entity signup id');
  }

  if (!ObjectId.isValid(entitySignupId)) {
    throw new ParamError('Invalid entity signup id');
  }

  try {
    let isValid = false;
    const { envVarData = null } = await getConfigByType(
      CONFIG_TYPES.LEARN_PORTAL_BACKEND_ENV_CONFIG_TYPE
    );
    const { MAX_RETRIES = 8, TIMEOUT_SECONDS = 0.75 } = envVarData;
    let attempts = 0;
    let addonTransaction = null;

    const isPaymentSuccessful = (transaction) =>
      transaction?.payment_details?.status ===
      COMMUNITY_ENTITY_ONE_TIME_PAYMENT_STATUSES.SUCCESS;

    const isPaymentFailed = (transaction) =>
      transaction?.payment_details?.status ===
      COMMUNITY_ENTITY_ONE_TIME_PAYMENT_STATUSES.FAILED;

    const paymentBackendRpc = new PaymentBackendRpc();
    await paymentBackendRpc.init();

    do {
      addonTransaction = await CommunityAddonTransaction.findById(
        entitySignupId
      ).lean();

      if (!addonTransaction) {
        throw new ResourceNotFoundError('Transaction not found');
      }

      // For payment webhook that took very long to reach our system
      if (addonTransaction.payment_details.isDirectCharge) {
        switch (addonTransaction.payment_details.paymentProvider) {
          case PAYMENT_PROVIDER.EBANX:
            // eslint-disable-next-line no-await-in-loop
            await paymentBackendRpc.ebanxFetchAndUpdatePaymentStatus(
              addonTransaction.payment_details.paymentIntentId
            );
            break;
          default:
            break;
        }
      }

      if (addonTransaction.payment_details?.paymentGatewayTransactionId) {
        switch (addonTransaction.payment_details.paymentProvider) {
          case PAYMENT_PROVIDER.PAYPAL:
            // eslint-disable-next-line no-await-in-loop
            await paymentBackendRpc.paypalFetchPaymentAndUpdate(
              addonTransaction.payment_details?.purchaseType,
              addonTransaction._id,
              addonTransaction.payment_details?.paymentGatewayTransactionId
            );
            break;
          default:
            break;
        }
      }

      isValid = isPaymentSuccessful(addonTransaction);

      if (isPaymentFailed(addonTransaction)) {
        const failureCode = addonTransaction.payment_details.failureCode;

        if (!failureCode) {
          throw new ToUserError(
            'Payment is failed',
            GENERIC_ERROR.PAYMENT_FAILURE
          );
        }

        const paymentError = mapFailureCodeToPaymentError(failureCode);

        return {
          isValid,
          isFailedPayment: true,
          paymentError,
          error: {
            message:
              addonTransaction.payment_details.failureReason ??
              'Payment is failed',
          },
        };
      }

      if (!isValid && attempts < MAX_RETRIES) {
        await sleepForSeconds(parseFloat(TIMEOUT_SECONDS));
      }

      ++attempts;
    } while (!isValid && attempts <= MAX_RETRIES);

    logger.info(entitySignupId, ' payment validity: ', isValid);

    if (!isValid) {
      return {
        isValid,
        error: {
          message:
            'Unable to verify community entity payment due to Timeout!',
        },
      };
    }

    const {
      email,
      entityCollection = null,
      entityObjectId = null,
      amount = null,
      currency = null,
      local_amount = null,
      local_currency = null,
    } = addonTransaction;

    let entityDetails = null;
    let communityDetails = null;
    let data = null;
    switch (entityCollection) {
      case 'community_events':
        entityDetails = await Event.findOne(
          {
            _id: entityObjectId,
          },
          '_id communities slug title icsFileLink access description descriptionContent startTime endTime type liveLink inPersonLocation inPersonLocationMetadata chatGroupLink'
        );
        communityDetails = await Community.findOne({
          _id: entityDetails?.communities?.[0],
        }).select('link title platforms isWhatsappExperienceCommunity');
        const eventLink = communityDetails?.link
          ? `${NAS_IO_FRONTEND_URL}${communityDetails?.link}${entityDetails?.slug}`
          : NAS_IO_FRONTEND_URL;
        data = {
          email,
          access: entityDetails?.access,
          amount,
          currency,
          localAmount: local_amount,
          localCurrency: local_currency,
          addToCalendarLink: entityDetails?.icsFileLink,
          eventLink,
          eventTitle: entityDetails?.title,
          eventDescription:
            entityDetails?.descriptionContent ||
            entityDetails?.description,
          communityLink: communityDetails?.link,
          communityTitle: communityDetails?.title,
          startTime: entityDetails?.startTime,
          endTime: entityDetails?.endTime,
          type: entityDetails?.type,
          liveLink: entityDetails?.liveLink,
          chatGroupLink: entityDetails?.chatGroupLink,
          inPersonLocation: entityDetails?.inPersonLocation,
          inPersonLocationMetadata:
            entityDetails?.inPersonLocationMetadata,
        };

        // Send notification to BC/CM that a new event attendee application is pending.
        if (addonTransaction?.metadata?.requiresApproval) {
          // get event attendee
          const eventAttendee = await EventAttendeesModal.findOne({
            eventObjectId: entityObjectId,
            learnerObjectId: addonTransaction.learnerObjectId,
            status: COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.PENDING,
          }).lean();

          // send pending event attendee notification
          if (eventAttendee) {
            await EventService.sendPendingEventAttendeeMobileNotification({
              event: entityDetails,
              eventAttendee,
            });
          }
        }

        break;
      case 'community_folders':
        entityDetails = await CommunityFolder.findOne({
          _id: entityObjectId,
        });
        communityDetails = await Community.findOne({
          _id: entityDetails?.communityObjectId,
        }).select('platforms isWhatsappExperienceCommunity');
        data = {
          ...entityDetails?._doc,
          email,
          amount,
          currency,
          localAmount: local_amount,
          localCurrency: local_currency,
        };
        break;
      case 'program':
        entityDetails = await ProgramModel.findOne({
          _id: entityObjectId,
        });
        communityDetails = await Community.findOne({
          _id: entityDetails?.communityObjectId,
        });
        data = {
          ...entityDetails?._doc,
          email,
          amount,
          currency,
          localAmount: local_amount,
          localCurrency: local_currency,
        };
        break;

      default:
        break;
    }

    if (
      communityDetails?.isWhatsappExperienceCommunity &&
      communityDetails?.platforms
    ) {
      const whatsappPlatform = communityDetails.platforms.filter(
        (platform) => platform.name === 'whatsapp'
      );

      if (whatsappPlatform.length > 0) {
        data['whatsappInvitationLink'] = whatsappPlatform[0]?.link;
      }
    }

    // Only update if user is first purchase to prevent
    // sending the access token for next purchase
    let updatedUser = await UserModel.findOneAndUpdate(
      { email, isActive: true, isFirstPurchase: true },
      { isFirstPurchase: false },
      { new: true }
    ).lean();

    if (!updatedUser) {
      // For the case that
      // - Backend update the user and return success response with DIRECT token to FE
      // - But FE didnt get BE response because of 500 error
      // - FE will trigger second api call, but the user has alr updated,
      // - Hence BE return EMAIL token to FE because user is alr updated (which is wrong)

      // Fix here: just fetch user again, and verify if first purchase item has same signup id as current signup
      updatedUser = await UserModel.findOne({
        email,
        isActive: true,
      }).lean();
    }

    const isFirstPurchaseItem =
      updatedUser?.firstPurchaseInfo?.signupId?.toString() ===
      entitySignupId.toString();

    const access =
      isLoginToken || isFirstPurchaseItem
        ? VERIFY_PAYMENT_ACCESS_TYPE.DIRECT
        : VERIFY_PAYMENT_ACCESS_TYPE.EMAIL;

    const result = {
      isValid,
      data,
      access,
    };

    if (!isLoginToken && isFirstPurchaseItem) {
      const authServiceRpc = new AuthServiceRpc();

      const { token, refreshToken } =
        await authServiceRpc.generateLoginAccessAndRefreshToken(email);

      result.token = token;
      result.refreshToken = refreshToken;
    }

    return result;
  } catch (error) {
    logger.error(
      'Unable to verify paid community entity payment due to: ',
      error
    );
    throw error;
  }
};

module.exports = { verifyPaidEntityPayment };
