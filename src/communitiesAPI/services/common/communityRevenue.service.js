const Community = require('../../models/community.model');
const CommunityRole = require('../../models/communityRole.model');
const logger = require('../../../services/logger.service');
const { sendEmail } = require('../../../services/notification');
const { getConfigByType } = require('../../../services/config.service');
const { CONFIG_TYPES } = require('../../../constants/common');
const service = require('../../../services/transaction');
const { DateTime } = require('luxon');

const getDateRangeInString = (month, year, monthOnly = false) => {
  const firstDayLastMonth = DateTime.utc(year, month, 1);
  if (monthOnly) {
    return `${firstDayLastMonth.toFormat('LLL yyyy')}`;
  }
  const lastDayLastMonth = firstDayLastMonth.endOf('month');
  return `${firstDayLastMonth.toFormat(
    'dd LLL yyyy'
  )} - ${lastDayLastMonth.toFormat('dd LLL yyyy')}`;
};

const calculateRevenueOfCommunity = async (
  month,
  year,
  filter = {},
  justRevenue = false,
  sendEmailToManagers = false,
  testEmail = true
) => {
  const monthToFilter = month;
  const firstDay = DateTime.utc(year, monthToFilter, 1);
  const lastDay = firstDay.endOf('month');
  const dateRange = getDateRangeInString(monthToFilter, year);
  const dateRangeMonth = getDateRangeInString(monthToFilter, year, true);

  const codes = filter?.code?.$in;
  const response = await Promise.all(
    codes.map(async (code) => {
      const community = await Community.findOne({ code })
        .select('_id title code thumbnailImgData')
        .lean();
      const owner = await CommunityRole.findOne({
        communityCode: code,
        role: 'owner',
      });
      const res =
        await service.RevenueService.retrieveRevenueBreakdownPayouts({
          startDate: firstDay,
          endDate: lastDay,
          query: { communityObjectId: community?._id },
        });

      const emailInfo = {
        _id: community?._id,
        code,
        month: dateRangeMonth,
        dateRange,
        ...res,
        community_profile_pic:
          community.thumbnailImgData?.desktopImgData?.src,
        community_name: community.title,
        community_code: community.code,
      };

      const payoutDetails = [];

      const backPaymentInUsd = {
        // THE_LOSER_CIRCLE: 1067.56,
        // NUNCA_MADRES: 4.34,
        // FOUNDERS_COMMUNITY_2: 5.2,
        // ARTIFICIAL_INTELLIGENCE_4: 8.49,
        // LIFE_ON_TOUR: 9.53,
        // PROFITING_PODCASTERS_HUB_6: 8.66,
        // ALGORITMO_UMANO_2: 5.19 + 5.19,
      };

      const backPaymentInInr = {
        // NIKIST_HUSTLERS_CLUB: -111692.07,
        // THESWINGTRADER_2: 11888,
        // BUILD_ACADEMY: -3492.35,
      };

      const conversionInInr = {
        // MONEYSECRETS: 76635.29,
      };

      if (res?.totalNetAmountInUsdWithoutOriginalCurrency > 0) {
        const revenue =
          res.totalNetAmountInUsdWithoutOriginalCurrency +
          (backPaymentInUsd[code] ?? 0);
        if (revenue > 10) {
          payoutDetails.push({
            revenue: revenue.toFixed(2),
            currency: 'USD',
          });
        }
      }

      if (res?.totalNetAmountInLocalCurrencyForOriginalCurrency > 0) {
        let revenue = 0;

        if (conversionInInr[code]) {
          revenue = conversionInInr[code];
        } else {
          revenue =
            res.totalNetAmountInLocalCurrencyForOriginalCurrency +
            (backPaymentInInr[code] ?? 0);
        }
        if (revenue > 10) {
          payoutDetails.push({
            revenue: revenue.toFixed(2),
            currency: 'INR',
          });
        }
      }

      emailInfo.payoutDetails = payoutDetails;

      if (sendEmailToManagers) {
        let toMail = [];
        let toMailName = [];
        const { envVarData = null } = await getConfigByType(
          CONFIG_TYPES.LEARN_PORTAL_BACKEND_ENV_CONFIG_TYPE
        );
        if (envVarData && Object.keys(envVarData).length) {
          logger.info('Setting the env var found in config...');
          toMail = envVarData?.TEST_TO_MAIL;
          toMailName = envVarData?.TEST_TO_MAIL_NAME;
        }

        if (!testEmail) {
          toMail.push(owner?.email);
          toMailName.push(owner?.email);
        }
        await Promise.all(
          emailInfo.payoutDetails.map(async (details) => {
            const mailReqBody = {
              mailType: 'COMMUNITY_REVENUE_SUMMARY',
              mailSubject: `Hey congrats, you have earned!`,
              mailCourse: 'All',
              mailCourseOffer: 'All',
              toMail,
              toMailName,
              data: { ...emailInfo, ...details },
            };

            try {
              logger.info('Sending revenue summary email');
              const message = await sendEmail(mailReqBody);
              return message;
            } catch (err) {
              logger.error('Error revenue summary email: ', err);
              throw err;
            }
          })
        );
      }
      return emailInfo;
    })
  );
  return response;
};

module.exports = { calculateRevenueOfCommunity };
