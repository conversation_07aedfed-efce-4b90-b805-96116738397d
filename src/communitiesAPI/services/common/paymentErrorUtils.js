const { GENERIC_ERROR } = require('../../../constants/errorCode');

exports.mapFailureCodeToPaymentError = (failureCode) => {
  let paymentError = GENERIC_ERROR.PAYMENT_FAILURE;

  switch (failureCode) {
    case GENERIC_ERROR.PAYMENT_FAILURE_3DS_FAILED.name:
      paymentError = GENERIC_ERROR.PAYMENT_FAILURE_3DS_FAILED;
      break;
    case GENERIC_ERROR.PAYMENT_FAILURE_BANK_DECLINE.name:
      paymentError = GENERIC_ERROR.PAYMENT_FAILURE_BANK_DECLINE;
      break;
    case GENERIC_ERROR.PAYMENT_FAILURE_NOT_ACCEPTED.name:
      paymentError = GENERIC_ERROR.PAYMENT_FAILURE_NOT_ACCEPTED;
      break;
    case GENERIC_ERROR.PAYMENT_FAILURE_INSUFFICIENT_FUNDS.name:
      paymentError = GENERIC_ERROR.PAYMENT_FAILURE_INSUFFICIENT_FUNDS;
      break;
    case GENERIC_ERROR.PAYMENT_FAILURE_HIGH_RISK_TRANSACTION.name:
      paymentError = GENERIC_ERROR.PAYMENT_FAILURE_HIGH_RISK_TRANSACTION;
      break;
    case GENERIC_ERROR.PAYMENT_FAILURE_INCORRECT_CUSTOMER_DATA.name:
      paymentError = GENERIC_ERROR.PAYMENT_FAILURE_INCORRECT_CUSTOMER_DATA;
      break;
    case GENERIC_ERROR.PAYMENT_FAILURE_INVALID_CARD_OR_CARD_TYPE.name:
      paymentError =
        GENERIC_ERROR.PAYMENT_FAILURE_INVALID_CARD_OR_CARD_TYPE;
      break;
    case GENERIC_ERROR.PAYMENT_FAILURE_TIMEOUT.name:
      paymentError = GENERIC_ERROR.PAYMENT_FAILURE_TIMEOUT;
      break;
    case GENERIC_ERROR.PAYMENT_FAILURE_NO_RESPONSE_FROM_ACQUIRER.name:
      paymentError =
        GENERIC_ERROR.PAYMENT_FAILURE_NO_RESPONSE_FROM_ACQUIRER;
      break;
    case GENERIC_ERROR.PAYMENT_FAILURE_ACQUIRER_RESPONSE_ERROR.name:
      paymentError = GENERIC_ERROR.PAYMENT_FAILURE_ACQUIRER_RESPONSE_ERROR;
      break;
    case GENERIC_ERROR.PAYMENT_FAILURE_BROKEN_COMMUNICATION.name:
      paymentError = GENERIC_ERROR.PAYMENT_FAILURE_BROKEN_COMMUNICATION;
      break;
    case GENERIC_ERROR.PAYMENT_FAILURE_COMMUNICATION_MISMATCH.name:
      paymentError = GENERIC_ERROR.PAYMENT_FAILURE_COMMUNICATION_MISMATCH;
      break;
    case GENERIC_ERROR.PAYMENT_FAILURE_UNKNOWN_ACQUIRER_ERROR.name:
      paymentError = GENERIC_ERROR.PAYMENT_FAILURE_UNKNOWN_ACQUIRER_ERROR;
      break;
    case GENERIC_ERROR
      .PAYMENT_FAILURE_CANNOT_PROCESS_TRANSACTION_AT_THIS_MOMENT.name:
      paymentError =
        GENERIC_ERROR.PAYMENT_FAILURE_CANNOT_PROCESS_TRANSACTION_AT_THIS_MOMENT;
      break;
    default:
      break;
  }
  return paymentError;
};
