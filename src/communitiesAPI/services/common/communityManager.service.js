const jwt = require('jsonwebtoken');
const { DateTime } = require('luxon');
const ObjectId = require('mongoose').Types.ObjectId;
const axios = require('../../../clients/axios.client');
const logger = require('../../../services/logger.service');
const CommunityRoles = require('../../models/communityRole.model');
const Users = require('../../../models/users.model');
const Community = require('../../models/community.model');
const Learner = require('../../../models/learners.model');
const CommunityPurchaseTransaction = require('../../models/communityPurchaseTransactions.model');

const { createLearner } = require('../../../services/learner.service');
const {
  createUserAndLearnerAndTokensWithSubscription,
  getCommunityManagerToken,
} = require('../../../services/user.service');
const CommunitySubscription = require('../../models/communitySubscriptions.model');
const formatVariableDataService = require('../../../services/mail/formatVariableData.service');
const { DEFAULT_IMAGES } = require('../../../services/mail/constants');
const commonService = require('../../../services/communityNotification/email/common.service');
const fraudService = require('../../../services/fraud');

const {
  findOneOrCreateSubscription,
  convertPaidToPromotedSubscription,
  convertFreeToPromotedSubscription,
} = require('./communitySubscriptions.service');
const {
  saveManagerInvite,
  cancelManagerInvite,
  sendManagerInviteEmail,
  sendManagerRevokeEmail,
} = require('../web/inviteMembers.service');
const membershipServices = require('../../../services/membership');
const {
  retrievePaymentFeeStructure,
} = require('../../../services/config/paymentFeeStructureConfig.service');
const {
  createDefaultAnnouncementPost,
} = require('../web/communityPosts.service');

const {
  communityEnrolmentStatuses,
  COMMUNITY_SUBSCRIPTION_STATUSES,
  COMMUNITY_INVITED_USERS_STATUSES,
  CHAT_PLATFORMS,
  DEFAULT_COMMUNITY_PROFILE_IMAGE,
  MEMBERSHIP_ACTION_EVENT_TYPES,
  BATCH_METADATA_MODEL_TYPE,
  MILESTONE_ACTIVITY_TYPES,
} = require('../../../constants/common');
const {
  COMMUNITY_ADMIN_LIMIT,
  COMMUNITY_USER_STATUSES,
  aclRoles,
  LEGENDS_COMMUNITY_CODE,
  COUNTRY_CREATED,
  latamCountriesArray,
  INDIAUPI_COMMUNITY_CODE,
  LATAM_COMMUNITY_CODE,
} = require('../../constants');
const {
  NAS_IO_FRONTEND_URL,
  DISCORD_URL,
  DISCORD_AUTH,
  NOTIFICATION_URL,
  NOTIFICATION_AUTH,
} = require('../../../config');

const {
  communityCreatedEmails,
  communityCreatedReferralAlertEmails,
  communityCreatedReferralEmails,
} = require('./communityTriggers.service');
const {
  removeWhatsappMemberBySubscription,
  updateWhatsappMemberSubscription,
} = require('./whatsapp.service');
const {
  cancelSubscriptionService,
} = require('../../../services/communitySubscription');
const { checkCommunityForFraud } = require('./community.service');

const { ToUserError } = require('../../../utils/error.util');
const nameUtils = require('../../../utils/name.util');
const {
  MEMBERSHIP_ERROR,
  FEATURE_PERMISSION_ERROR,
} = require('../../../constants/errorCode');
const actionEventService = require('../../../services/actionEvent');
const chatService = require('../../../services/chat');
const feeService = require('./fee.service');
const membershipUsageService = require('../../../services/featurePermissions/membershipUsage.service');
const {
  hasInternalDomain,
} = require('@/src/utils/checkInternalMember.util');

const sendEmailToRemovedMember = async (emailData) => {
  try {
    const data = {
      mailType: 'COMMUNITY_MEMBER_REMOVED',
      mailCourse: emailData?.communityCode,
      mailCourseOffer: '',
      toMail: [emailData?.removeMemberEmail],
      toMailName: [emailData?.memberName],
      requesterServiceName: 'App.Nas.io',
      data: emailData,
    };
    const response = await axios.post(
      `${NOTIFICATION_URL}/api/v1/send-email`,
      data,
      {
        headers: {
          Authorization: `Bearer ${NOTIFICATION_AUTH}`,
        },
      }
    );
    return response?.data;
  } catch (error) {
    logger.error(
      'Error on axios post request to send remove member email: ',
      error
    );
    return false;
  }
};

const createManagerRole = async (params = {}) => {
  try {
    logger.info('Create manager role');
    const { email, communityId, referralCode } = params;

    const findUser = await Users.findOne({ email, isActive: true });
    if (!findUser) {
      throw new Error('User not found');
    }

    const community = await Community.findById(communityId, {
      isDraft: 1,
      isActive: 1,
      code: 1,
      By: 1,
      createdBy: 1,
      timezone: 1,
      countryCreatedIn: 1,
      config: 1,
      baseCurrency: 1,
      link: 1,
      thumbnailImgData: 1,
      title: 1,
      isWhatsappExperienceCommunity: 1,
    })
      .read('primary')
      .lean();

    if (!community) {
      throw new Error('Community not found');
    }

    if (!community.isDraft && community.isActive) {
      return {
        roleCreated: true,
        message:
          'Manager role created for this user for the given community',
      };
    }

    // assumption: if assign community manager request is made while community is in draft state, its by owner
    const insertManager = {
      userObjectId: new ObjectId(findUser._id),
      email,
      communityObjectId: new ObjectId(communityId),
      communityCode: community.code,
    };
    let createManager = await CommunityRoles.findOne(insertManager);
    if (!createManager) {
      insertManager.role = [
        aclRoles.OWNER,
        aclRoles.ADMIN,
        aclRoles.MANAGER,
        aclRoles.MEMBER,
      ];
      createManager = await CommunityRoles.create(insertManager);
    }

    let learner;
    if (createManager) {
      findUser.community_admin = true;
      await findUser.save();

      learner = await Learner.findOne({ email })
        .select('_id learnerId firstName lastName languagePreference')
        .lean();

      if (!learner) {
        learner = await createLearner({
          email,
          isActive: true,
        });
      }

      const {
        _id: learnerObjectId,
        learnerId,
        firstName,
        lastName,
      } = learner;
      await findOneOrCreateSubscription(
        {
          email,
          learnerId,
          communityCode: community.code,
          learnerObjectId,
        },
        community
      );

      const batchMetadataEnabledType = Object.keys(
        BATCH_METADATA_MODEL_TYPE
      ).reduce((acc, modelType) => {
        acc[modelType] = true;
        return acc;
      }, {});

      const messageSettings =
        chatService.settingService.retrieveDefaultMessageSettings();

      const paymentFeeStructure = await retrievePaymentFeeStructure({
        baseCurrency: community.baseCurrency,
        planType: community.config?.planType,
      });

      const {
        feeConfig: basePayoutFeeConfig,
        passOnTakeRate: passOnTakeRateSetting,
        passOnPaymentGatewayFee: passOnPaymentGatewayFeeSetting,
      } = await feeService.retrieveBasePayoutFeeConfigAndOtherSettings({
        community,
        paymentFeeStructure,
      });

      const basePayoutFeeConfigs = [basePayoutFeeConfig];

      const communityUpdateFields = {
        isDraft: false,
        isActive: true,
        config: {
          ...community.config,
          batchMetadataEnabledType,
          messageSettings,
        },
        passOnTakeRate: passOnTakeRateSetting,
        passOnPaymentGatewayFee: passOnPaymentGatewayFeeSetting,
        basePayoutFeeConfigs,
      };

      if (
        ((createManager.role ?? []).includes(aclRoles.OWNER) &&
          !community.By) ||
        !community.createdBy
      ) {
        communityUpdateFields.By = nameUtils.getName(firstName, lastName);
        communityUpdateFields.createdBy = email;
      }

      if (referralCode) {
        const existingManager = await CommunityRoles.findOne({
          email,
          communityObjectId: { $ne: communityId },
          role: aclRoles.MANAGER,
        });

        const referrer = await Learner.findOne(
          {
            referralCode,
            isActive: true,
          },
          { email: 1, firstName: 1 }
        ).lean();

        if (referrer && !existingManager) {
          communityUpdateFields.referralCodeUsed = referralCode;
          communityUpdateFields.referrerEmail = referrer.email;
          communityUpdateFields.referrerObjectId = referrer._id;

          await communityCreatedReferralAlertEmails(
            referrer.email,
            referrer.firstName ?? referrer.email,
            {
              community_url: NAS_IO_FRONTEND_URL + community.link,
              community_name: community.title,
              community_host: communityUpdateFields.By,
              community_profile_image:
                community.thumbnailImgData?.mobileImgData?.src ??
                DEFAULT_COMMUNITY_PROFILE_IMAGE,
            }
          );

          await communityCreatedReferralEmails(
            communityUpdateFields.createdBy,
            communityUpdateFields.By,
            {
              cm_portal_url: `${NAS_IO_FRONTEND_URL}/portal/money?activeCommunityId=${community._id}`,
              referral_link: `${NAS_IO_FRONTEND_URL}?referral=${communityUpdateFields.referralCodeUsed}`,
            }
          );
        }
      } else {
        await communityCreatedEmails(
          communityUpdateFields.createdBy,
          communityUpdateFields.By,
          {
            community_code: community.code,
            student_header_name: `Congratulations, ${communityUpdateFields.By}!`,
            community_url: NAS_IO_FRONTEND_URL + community.link,
            cm_portal_url: `${NAS_IO_FRONTEND_URL}/portal?activeCommunityId=${community._id}`,
            community_name: community.title,
            community_profile_image:
              community.thumbnailImgData?.mobileImgData?.src ??
              DEFAULT_COMMUNITY_PROFILE_IMAGE,
            isWhatsappExperienceCommunity:
              community.isWhatsappExperienceCommunity,
          }
        );
      }

      await actionEventService.sendMilestoneEvent({
        actionEventType:
          MILESTONE_ACTIVITY_TYPES.MILESTONE_COMMUNITY_CREATED,
        communityCode: community.code,
        communityObjectId: community._id,
        learnerObjectId,
      });

      const languagePreference = learner.languagePreference || 'en';
      await createDefaultAnnouncementPost(
        community,
        findUser._id,
        languagePreference
      );

      await checkCommunityForFraud({
        communityId: community._id,
        communityData: community,
        eventName: fraudService.INTERESTED_EVENTS.ENABLE_COMMUNITY,
      });

      const subscriptionDoc = {
        communityCode: LEGENDS_COMMUNITY_CODE,
        email,
        learnerId,
        learnerObjectId,
      };

      const communityCountry = community.countryCreatedIn;

      if (communityCountry === COUNTRY_CREATED.INDIA) {
        subscriptionDoc.communityCode = INDIAUPI_COMMUNITY_CODE;
      }

      if (latamCountriesArray.includes(communityCountry)) {
        subscriptionDoc.communityCode = LATAM_COMMUNITY_CODE;
      }
      // subscribe creator to legends community or indiaUpi community based on country
      await findOneOrCreateSubscription(subscriptionDoc, community);

      const communityUpdate = await Community.findOneAndUpdate(
        {
          _id: new ObjectId(communityId),
          isDraft: true,
        },
        communityUpdateFields,
        { new: true }
      ).lean();

      if (communityUpdate) {
        logger.info(
          'Communtity has been made active. Community Code: ',
          community?.code
        );
      }

      return {
        roleCreated: true,
        updatedCommunityData: {
          code: communityUpdate.code,
          _id: communityUpdate._id,
          By: communityUpdate.By,
          isActive: communityUpdate.isActive,
          isDraft: communityUpdate.isDraft,
          createdBy: communityUpdate.createdBy,
          restrictedInfo: communityUpdate.restrictedInfo,
        },
        message:
          'Manager role created for this user for the given community',
      };
    }
    throw new Error('Error while creating the manager role');
  } catch (err) {
    logger.error(err.message, err.stack);
    throw err;
  }
};

const getAdminCountWithLimit = async (params = {}) => {
  try {
    const { communityId = null } = params;
    if (!communityId) {
      const errorMessage = 'No Community Specified!';
      throw new Error(errorMessage);
    }
    const communityAdminCount =
      (await CommunityRoles.countDocuments({
        role: aclRoles.ADMIN,
        communityObjectId: new ObjectId(communityId),
      })) || 0;
    return {
      currentCount: communityAdminCount || 0,
      maxLimit: COMMUNITY_ADMIN_LIMIT,
    };
  } catch (error) {
    logger.error('Unable to get admin count with limit due to: ', error);
    throw error;
  }
};

const checkEmailStatuses = async (
  communityId,
  communityCode,
  emails = []
) => {
  try {
    const result = [];
    const statusCheckPromises = emails.map(async (email) => {
      const existingUser = await Users.exists({ email });
      const isInternalEmail = hasInternalDomain(email);
      if (!existingUser) {
        result.push({
          email,
          status: COMMUNITY_USER_STATUSES.NEW_USER,
          isInternalEmail,
        });
      } else {
        const existingAdminRole = await CommunityRoles.exists({
          role: aclRoles.ADMIN,
          email,
          communityObjectId: new ObjectId(communityId),
        });
        if (existingAdminRole) {
          result.push({
            email,
            status: COMMUNITY_USER_STATUSES.ALREADY_ADMIN,
            isInternalEmail,
          });
        } else {
          const existingSubscription = await CommunitySubscription.exists({
            email,
            communityCode,
            status: COMMUNITY_SUBSCRIPTION_STATUSES.CURRENT,
          });
          if (existingSubscription) {
            result.push({
              email,
              status: COMMUNITY_USER_STATUSES.ALREADY_MEMBER,
              isInternalEmail,
            });
          } else {
            result.push({
              email,
              status: COMMUNITY_USER_STATUSES.EXISTING_USER,
              isInternalEmail,
            });
          }
        }
      }
    });
    await Promise.all(statusCheckPromises);
    return result;
  } catch (error) {
    logger.error('Unable to check admin status due to: ', error);
    throw error;
  }
};

const getAdminStatus = async (params = {}) => {
  try {
    const { communityId = null, emails = [] } = params;
    if (!communityId) {
      const errorMessage = 'No Community Specified!';
      throw new Error(errorMessage);
    }
    if (!emails.length) {
      const errorMessage = 'No Emails To Check!';
      throw new Error(errorMessage);
    }
    const community = await Community.findById(communityId);
    const adminStatuses = await checkEmailStatuses(
      communityId,
      community?.code,
      emails
    );
    return adminStatuses;
  } catch (error) {
    logger.error('Unable to get admin status due to: ', error);
    throw error;
  }
};

const createCommunityAdmin = async (community, email, userObjectId) => {
  const communityAdmin = await CommunityRoles.create({
    role: [aclRoles.ADMIN, aclRoles.MANAGER, aclRoles.MEMBER],
    communityObjectId: community._id,
    communityCode: community?.code,
    email,
    userObjectId,
  });
  return communityAdmin;
};

const addAdmins = async (params = {}) => {
  try {
    logger.info('Received params for adding community managers: ', params);
    const {
      communityId = null,
      emails = [],
      managerEmail = null,
      managerUserObjectId = null,
    } = params;
    if (!communityId) {
      const errorMessage = 'No Community Specified!';
      throw new Error(errorMessage);
    }
    if (!emails.length) {
      const errorMessage = 'No Emails To Check!';
      throw new Error(errorMessage);
    }
    const community = await Community.findById(communityId).lean();
    const adminStatuses = await checkEmailStatuses(
      communityId,
      community?.code,
      emails
    );
    logger.info('admin statuses: ', JSON.stringify(adminStatuses));

    const newAdmins = adminStatuses.filter(
      (item) =>
        item.status !== COMMUNITY_USER_STATUSES.ALREADY_ADMIN &&
        !item.isInternalEmail
    );
    const limitCheck = await membershipUsageService.checkAddManagerLimit(
      communityId,
      newAdmins.length
    );
    if (!limitCheck.allowed) {
      throw new ToUserError(
        limitCheck.message,
        FEATURE_PERMISSION_ERROR[limitCheck.error]
      );
    }

    const author = await Learner.findOne({
      email: community?.createdBy,
      isActive: true,
    });

    const result = [];
    const statusPromises = adminStatuses.map(async (adminStatusData) => {
      const { email, status } = adminStatusData;
      // Shift the limit check outside of loop. Aka as long as total bulk managers exceeds we dont save any at all.
      const isAllowed = true;
      // const isAllowed = await isAllowedToAddManager(communityId);
      const existingUser = await Users.findOne({
        email,
        isActive: true,
      }).lean();
      const existingLearner = (await Learner.findOne({ email })) || null;
      let createdUserAndLearnerWithSubscription = null;
      let subscriptionData = null;
      let createdCommunitySubscription = null;
      let promotedCommunitySubscription = null;
      let createdManagerInvite = null;
      let createdCommunityAdmin = null;
      let ctaLink = null;
      let mailData = null;
      let mailResponse = null;
      switch (status) {
        case COMMUNITY_USER_STATUSES.NEW_USER:
          if (!isAllowed) {
            result.push({
              email,
              added: false,
              reason: 'Could not add as Manager. Max Limit Reached!',
            });
            break;
          }

          // create learner and user account, free subscription to given community, admin record, then trigger email
          createdUserAndLearnerWithSubscription =
            await createUserAndLearnerAndTokensWithSubscription(
              email,
              community?.code,
              community
            );
          logger.info(
            'Created User and Learner With Subscription: ',
            createdUserAndLearnerWithSubscription
          );

          createdCommunitySubscription =
            createdUserAndLearnerWithSubscription?.communitySubscription;

          createdManagerInvite = await saveManagerInvite({
            email,
            communityObjectId: new ObjectId(communityId),
            communityCode: community?.code,
            invitedByEmail: managerEmail,
            invitedByUserObjectId: new ObjectId(managerUserObjectId),
            status: COMMUNITY_INVITED_USERS_STATUSES.INVITED,
            type: aclRoles.MANAGER,
          });
          logger.info('Created Community Invite: ', createdManagerInvite);

          createdCommunityAdmin = await createCommunityAdmin(
            community,
            email,
            createdUserAndLearnerWithSubscription?.user?._id
          );
          if (createdCommunityAdmin) {
            result.push({ email, added: true });
          }

          ctaLink = await getCommunityManagerToken(
            createdUserAndLearnerWithSubscription?.user,
            'portal'
          );

          mailData = {
            communityCode: community?.code,
            communityName: community?.title,
            invitedByEmail: managerEmail,
            communityLink: ctaLink,
            email,
            name: email.split('@'),
            communityDescription: community?.description,
          };
          mailResponse = await sendManagerInviteEmail(
            mailData,
            author?.languagePreference
          );

          break;
        case COMMUNITY_USER_STATUSES.EXISTING_USER:
          if (!isAllowed) {
            result.push({
              email,
              added: false,
              reason: 'Could not add as Manager. Max Limit Reached!',
            });
            break;
          }
          // create free subscription to given community, and admin record, then trigger email
          createdCommunitySubscription = await findOneOrCreateSubscription(
            {
              email,
              learnerId: existingLearner.learnerId,
              communityCode: community?.code,
              learnerObjectId: existingLearner?._id,
            },
            community
          );

          createdCommunityAdmin = await createCommunityAdmin(
            community,
            email,
            existingUser?._id
          );
          if (createdCommunityAdmin) {
            result.push({ email, added: true });
          }

          ctaLink = await getCommunityManagerToken(existingUser, 'portal');

          mailData = {
            communityName: community?.title,
            invitedByEmail: managerEmail,
            communityLink: ctaLink,
            email,
            name: email.split('@'),
            communityDescription: community?.description,
          };
          mailResponse = await sendManagerInviteEmail(
            mailData,
            author?.languagePreference
          );

          break;
        case COMMUNITY_USER_STATUSES.ALREADY_MEMBER:
          if (!isAllowed) {
            result.push({
              email,
              added: false,
              reason: 'Could not add as Manager. Max Limit Reached!',
            });
            break;
          }
          // if they are on stripe subscription we cancel that and convert to free, if in app then we throw error saying CM needs to email user to cancel paid subscription before becoming admin
          subscriptionData = await CommunitySubscription.findOne({
            email,
            learnerId: existingLearner?.learnerId,
            communityCode: community?.code,
            $or: [
              {
                status: communityEnrolmentStatuses.CURRENT,
              },
              {
                $and: [
                  {
                    status: communityEnrolmentStatuses.CANCELLED,
                  },
                  { cancelledAt: { $gte: DateTime.utc() } },
                ],
              },
            ],
          }).lean();
          logger.info('existing subscription data ', subscriptionData);
          if (subscriptionData?.subscriptionId) {
            if (subscriptionData?.inAppSubscriptionId) {
              // in app subscription
              result.push({
                email,
                added: false,
                reason: 'User needs to cancel in app subscription',
              });
              break;
            } else if (subscriptionData?.stripeSubscriptionId) {
              if (subscriptionData?.amount > 0) {
                // paid subscription
                promotedCommunitySubscription =
                  await convertPaidToPromotedSubscription(
                    subscriptionData
                  );
                createdCommunitySubscription =
                  await findOneOrCreateSubscription(
                    {
                      promotedSubscription: promotedCommunitySubscription,
                      email,
                      learnerId: existingLearner.learnerId,
                      communityCode: community?.code,
                      learnerObjectId: existingLearner?._id,
                    },
                    community
                  );
              } else {
                promotedCommunitySubscription =
                  await convertFreeToPromotedSubscription(
                    subscriptionData
                  );
                createdCommunitySubscription =
                  await findOneOrCreateSubscription(
                    {
                      promotedSubscription: promotedCommunitySubscription,
                      email,
                      learnerId: existingLearner.learnerId,
                      communityCode: community?.code,
                      learnerObjectId: existingLearner?._id,
                    },
                    community
                  );
              }
            } else {
              // free subscription
              promotedCommunitySubscription =
                await convertFreeToPromotedSubscription(subscriptionData);
              createdCommunitySubscription =
                await findOneOrCreateSubscription(
                  {
                    promotedSubscription: promotedCommunitySubscription,
                    email,
                    learnerId: existingLearner.learnerId,
                    communityCode: community?.code,
                    learnerObjectId: existingLearner?._id,
                  },
                  community
                );
            }

            createdCommunityAdmin = await createCommunityAdmin(
              community,
              email,
              existingUser?._id
            );
            if (createdCommunityAdmin) {
              result.push({ email, added: true });

              if (community?.isWhatsappExperienceCommunity) {
                await updateWhatsappMemberSubscription(
                  communityId,
                  subscriptionData?._id,
                  createdCommunitySubscription?._id
                );
              }
            }
            ctaLink = await getCommunityManagerToken(
              existingUser,
              'portal'
            );
            mailData = {
              communityName: community?.title,
              invitedByEmail: managerEmail,
              communityLink: ctaLink,
              email,
              name: email.split('@'),
              communityDescription: community?.description,
            };
            mailResponse = await sendManagerInviteEmail(
              mailData,
              author?.languagePreference
            );
          }

          break;
        case COMMUNITY_USER_STATUSES.ALREADY_ADMIN:
          // do nothing, skip
          result.push({
            email,
            added: false,
            reason: 'User is already admin',
          });
          break;
        default:
          break;
      }
      logger.info(
        'Community Subscription: ',
        createdCommunitySubscription
      );
      logger.info('Cta Link for Invite Email: ', ctaLink);
      logger.info('Invite Mail sent response: ', mailResponse);

      if (createdCommunityAdmin) {
        logger.info('Created community admin as: ', createdCommunityAdmin);
      }
    });
    await Promise.allSettled(statusPromises);
    logger.info('Result of added admins: ', result);
    return result;
  } catch (error) {
    logger.error('Unable to add admins due to: ', error);
    throw error;
  }
};

const removeAdmin = async (params = {}) => {
  try {
    const {
      communityId = null,
      email = null,
      managerEmail = null,
    } = params;
    if (!communityId) {
      const errorMessage = 'No Community Specified!';
      throw new Error(errorMessage);
    }
    if (!email) {
      const errorMessage = 'No Email Specified!';
      throw new Error(errorMessage);
    }
    const {
      createdBy = null,
      title = null,
      code = null,
    } = await Community.findById(communityId);

    if (email === createdBy) {
      const errorMessage = `Can't remove original community admin!`;
      throw new Error(errorMessage);
    }

    if (email === managerEmail) {
      throw new Error('Cannot remove ownself');
    }

    const cancelledInvite = await cancelManagerInvite({
      email,
      communityObjectId: new ObjectId(communityId),
      communityCode: code,
      invitedByEmail: managerEmail,
      type: aclRoles.MANAGER,
    });
    logger.info('Cancelled community manager invite: ', cancelledInvite);
    const removedAdmin = await CommunityRoles.findOneAndDelete({
      role: aclRoles.ADMIN,
      communityObjectId: new ObjectId(communityId),
      email,
    });
    logger.info('Removed community manager role: ', removedAdmin);
    if (!removedAdmin) {
      const errorMessage = 'Unable to find admin to remove!';
      throw new Error(errorMessage);
    }
    const mailData = {
      communityName: title,
      invitedByEmail: managerEmail,
      communityLink: `${NAS_IO_FRONTEND_URL}/login`,
      email,
      name: email.split('@'),
    };
    const author = Learner.findOne({
      email: createdBy,
      isActive: true,
    });
    const mailResponse = await sendManagerRevokeEmail(
      mailData,
      author?.languagePreference
    );
    logger.info('Revoke Mail sent response: ', mailResponse);
    return removedAdmin;
  } catch (error) {
    // console.log('Unable to remove admin due to: ', error);
    logger.error('Unable to remove admin due to: ', error);
    throw error;
  }
};

const resendCommunityCreatedMail = async (params = {}) => {
  try {
    const { communityId } = params;
    const community = await Community.findOne({
      _id: new ObjectId(communityId),
    });
    if (!community) {
      const errorMessage =
        'No Community found with the given communityId!';
      throw new Error(errorMessage);
    }
    await communityCreatedEmails(community?.createdBy, community?.By, {
      student_header_name: `Congratulations, ${community?.By}!`,
      community_code: community?.code,
      community_url: NAS_IO_FRONTEND_URL + community?.link,
      cm_portal_url: `${NAS_IO_FRONTEND_URL}/portal?activeCommunityId=${community?._id}`,
      community_name: community?.title,
      community_profile_image:
        community?.thumbnailImgData?.mobileImgData?.src ??
        DEFAULT_COMMUNITY_PROFILE_IMAGE,
      isWhatsappExperienceCommunity:
        community?.isWhatsappExperienceCommunity,
    });
    return true;
  } catch (error) {
    logger.error('Unable to resend Community Created Mail: ', error);
    throw error;
  }
};

const removeMemberFromDiscord = async (bots, discordUserId) => {
  const foundDiscord = bots?.find(
    (element) => element?.type === CHAT_PLATFORMS.DISCORD
  );

  const isCommunityConnectedToDiscord = foundDiscord != null;
  let removeChatStatus = false;

  if (foundDiscord) {
    try {
      // Remove the user from the chat
      const requestUrl = `${DISCORD_URL}/api/v1/kickMember/${foundDiscord?.serverKey}/${discordUserId}`;
      const authHeader = jwt.sign({}, DISCORD_AUTH);
      const removeChat = await axios.post(
        requestUrl,
        {},
        {
          headers: {
            Authorization: `Bearer ${authHeader}`,
          },
        }
      );
      if (removeChat.status === 200) {
        removeChatStatus = true;
      }
    } catch (err) {
      logger.error('Unable to remove member from the chat: ', err);
    }
  }

  return {
    isCommunityConnectedToDiscord,
    removeChatStatus,
  };
};

const removeMemberFromWhatsapp = async (
  bots,
  communityObjectId,
  subscriptionObjectId
) => {
  const foundWhatsapp = bots?.find(
    (element) => element?.type === CHAT_PLATFORMS.WHATSAPP
  );

  const isCommunityConnectedToWhatsapp = foundWhatsapp != null;
  let removeWhatsappChatStatus = false;

  if (foundWhatsapp) {
    await removeWhatsappMemberBySubscription(
      communityObjectId,
      subscriptionObjectId
    );

    removeWhatsappChatStatus = true;
  }

  return {
    isCommunityConnectedToWhatsapp,
    removeWhatsappChatStatus,
  };
};

const removeMember = async (params = {}, ignoreManager = false) => {
  try {
    const {
      communityId,
      learnerId,
      removalReason,
      currentManagerlearnerId,
      community = null,
      learner = null,
    } = params;

    const communityData =
      community ?? (await Community.findById(communityId).lean());

    if (!communityData) {
      throw new Error('Community not found');
    }

    const learnerData =
      learner ??
      (await Learner.findOne({
        learnerId,
      }).lean());

    if (!learnerData) {
      throw new Error('No learner found');
    }

    const removeMemberEmail = learnerData.email;

    let currentManagerlearnerData;

    if (!ignoreManager) {
      currentManagerlearnerData = await Learner.findOne({
        _id: new ObjectId(currentManagerlearnerId),
      }).lean();

      if (currentManagerlearnerData == null) {
        throw new Error('No current manager learner found');
      }

      if (currentManagerlearnerData.learnerId === learnerData.learnerId) {
        throw new Error('Cannot remove ownself');
      }

      const createdByEmail = communityData.createdBy;

      // Community Owner check
      if (removeMemberEmail === createdByEmail) {
        throw new Error(`Can't remove original community admin!`);
      }
    }

    const communityCode = communityData.code;

    const findUserSubscription = await CommunitySubscription.findOne({
      email: removeMemberEmail,
      communityCode,
      $or: [
        { status: communityEnrolmentStatuses.CURRENT },
        {
          status: communityEnrolmentStatuses.CANCELLED,
          cancelledAt: { $gte: new Date() },
        },
      ],
    }).lean();

    if (!findUserSubscription) {
      throw new Error(
        `No active subscription by ${removeMemberEmail} for community code ${communityCode} found`
      );
    }

    if (
      communityData.isPaidCommunity &&
      findUserSubscription.inAppSubscriptionId
    ) {
      // In app subscription so it cannot be removed
      throw new ToUserError(
        'Cannot remove in-app subscription',
        MEMBERSHIP_ERROR.MOBILE_SUBSCRIPTION_NOT_ABLE_TO_REMOVE
      );
    }

    const findPurchaseTransactionData =
      await CommunityPurchaseTransaction.findOne({
        community_code: communityCode,
        email: removeMemberEmail,
        subscriptionObjectId: new ObjectId(findUserSubscription._id),
        'payment_details.complete_payment': 1,
      }).lean();

    const memberName =
      learnerData.firstName || learnerData.lastName || 'Member';

    const removedBy =
      currentManagerlearnerData?.email || `${communityData.title} admin`;

    let removeSubscription;

    const stripeSubscriptionId =
      findUserSubscription.stripeSubscriptionId ??
      findPurchaseTransactionData?.stripeSubscriptionId;

    if (!stripeSubscriptionId) {
      removeSubscription = await CommunitySubscription.findOneAndUpdate(
        {
          email: removeMemberEmail,
          communityCode,
          status: communityEnrolmentStatuses.CURRENT,
        },
        {
          removedBy,
          removalReason,
          removedAt: Date.now(),
          status: communityEnrolmentStatuses.REMOVED,
        },
        { new: true }
      ).lean();

      if (removeSubscription) {
        await actionEventService.sendFreeMembershipActionEvent({
          actionEventType:
            MEMBERSHIP_ACTION_EVENT_TYPES.SUBSCRIPTION_REMOVED,
          actionEventCreatedAt: removeSubscription.removedAt,
          subscription: removeSubscription,
          community: communityData,
        });
      }
    } else {
      try {
        removeSubscription =
          await cancelSubscriptionService.removeSubscriptionProcess({
            subscription: findUserSubscription,
            stripeSubscriptionId,
            removalReason,
            removedBy,
          });
      } catch (apiError) {
        logger.error(
          'Failed to remove subscription from main website api call: ',
          apiError
        );
        removeSubscription = false;
      }
    }

    let removeSubscriptionStatus = false;
    let removeChatStatus = false;
    let removalMailSent = false;
    let isMemberCommunityAdmin = false;
    let isMemberCommunityAdminRemoved = false;
    let isMemberChatConnected = false;
    let isCommunityConnectedToDiscord = false;
    let removeWhatsappChatStatus = false;
    let isCommunityConnectedToWhatsapp = false;

    if (removeSubscription) {
      await membershipServices.updateService.removeMembership({
        filters: { subscriptionObjectId: removeSubscription._id },
        removedBy,
        removalReason,
      });

      removeSubscriptionStatus = true;
      // Check if Member is the admin for this community
      const checkAdminAccess = await CommunityRoles.findOne({
        role: aclRoles.ADMIN,
        communityObjectId: new ObjectId(communityId),
        email: removeMemberEmail,
      });
      if (checkAdminAccess) {
        isMemberCommunityAdmin = true;
        // remove member from community admin records
        const removedAdmin = await CommunityRoles.findOneAndDelete({
          role: aclRoles.ADMIN,
          communityObjectId: new ObjectId(communityId),
          email: removeMemberEmail,
        });
        if (removedAdmin) {
          isMemberCommunityAdminRemoved = true;
        }
      }

      const communityVariables =
        formatVariableDataService.formatCommunityData({
          community: communityData,
        });

      const owner = await commonService.retrieveCommunityOwnerInfo(
        communityData.code
      );
      const mailData = {
        // Remove this in the future
        communityName: communityData.title,
        removedByEmail: removedBy,
        removedByName:
          currentManagerlearnerData?.firstName || 'Community Manager',
        memberName,
        removeMemberEmail,
        removalReason,
        // Remove above in the future
        communityCode,
        community_name: communityData.title,
        removed_by_email: removedBy,
        removed_by_name:
          currentManagerlearnerData?.firstName || 'Community Manager',
        removal_reason: removalReason,
        ...communityVariables,
        community_host: owner.name,
        host_profile_image:
          owner.profileImage ??
          DEFAULT_IMAGES.COMMUNITY_HOST_PROFILE_IMAGE,
      };

      const mailResponse = await sendEmailToRemovedMember(mailData);
      if (mailResponse) {
        removalMailSent = true;
      }

      if (findUserSubscription?.userInChat) {
        isMemberChatConnected = true;

        const discordResult = await removeMemberFromDiscord(
          communityData.bots,
          learnerData.discordUserId
        );

        isCommunityConnectedToDiscord =
          discordResult.isCommunityConnectedToDiscord;

        removeChatStatus = discordResult.removeChatStatus;
      }
    }

    if (communityData.isWhatsappExperienceCommunity) {
      const whatsappResult = await removeMemberFromWhatsapp(
        communityData.bots,
        communityData._id,
        findUserSubscription._id
      );

      isCommunityConnectedToWhatsapp =
        whatsappResult.isCommunityConnectedToWhatsapp;

      removeWhatsappChatStatus = whatsappResult.removeWhatsappChatStatus;
      removeChatStatus = whatsappResult.removeWhatsappChatStatus;
    }

    return {
      removeSubscriptionStatus,
      removeChatStatus,
      removalMailSent,
      isMemberCommunityAdmin,
      isMemberCommunityAdminRemoved,
      isMemberChatConnected,
      isCommunityConnectedToDiscord,
      removeWhatsappChatStatus,
      isCommunityConnectedToWhatsapp,
    };
  } catch (error) {
    logger.error('Unable to remove member: ', error);
    if (!ignoreManager) {
      throw error;
    } else {
      return {
        removeSubscriptionStatus: false,
      };
    }
  }
};

module.exports = {
  createManagerRole,
  getAdminCountWithLimit,
  getAdminStatus,
  addAdmins,
  removeAdmin,
  resendCommunityCreatedMail,
  removeMember,
};
