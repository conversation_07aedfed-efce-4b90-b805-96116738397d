const { ObjectId } = require('mongoose').Types;
const {
  FINANCE_PAYOUT_DETAILS_LARK_WEBHOOK,
  WALLET_ERROR_ALERT_LARK_WEBHOOK,
  PAYOUT_FAILED_ALERT_LARK_WEBHOOK,
} = require('../../../config');
const {
  generateMessageTemplate,
  insertContentToTemplate,
  generateTextPayload,
  sendPushNotificationToLark,
} = require('./lark.service');
const Community = require('../../models/community.model');

exports.sendNotification = async (communityId, updatedAt, actionType) => {
  if (!FINANCE_PAYOUT_DETAILS_LARK_WEBHOOK) {
    return;
  }

  const { code, name } = await Community.findOne(
    {
      _id: new ObjectId(communityId),
    },
    {
      code: 1,
      name: 1,
    }
  );

  const timestamp = new Date(updatedAt);

  const template = generateMessageTemplate(
    `Payout Details ${actionType}: ${code} (${timestamp.toISOString()})`
  );
  const content = [
    generateTextPayload('Action Type: ', actionType),
    generateTextPayload('Community Code: ', code),
    generateTextPayload('Community Name: ', name),
    generateTextPayload(
      `${actionType} Timestamp: `,
      timestamp.toISOString()
    ),
  ];

  const payload = await insertContentToTemplate(template, content);

  await sendPushNotificationToLark(
    FINANCE_PAYOUT_DETAILS_LARK_WEBHOOK,
    payload
  );
};

exports.sendErrorNotification = async (header, messages) => {
  if (!WALLET_ERROR_ALERT_LARK_WEBHOOK) {
    return;
  }

  const template = generateMessageTemplate(header);
  const content = [];

  Object.entries(messages).forEach(([key, value]) => {
    const textPayload = generateTextPayload(key, value);
    content.push(textPayload);
  });

  const payload = await insertContentToTemplate(template, content);

  await sendPushNotificationToLark(
    WALLET_ERROR_ALERT_LARK_WEBHOOK,
    payload
  );
};

exports.sendPayoutFailedNotification = async (header, messages) => {
  if (!PAYOUT_FAILED_ALERT_LARK_WEBHOOK) {
    return;
  }

  const template = generateMessageTemplate(header);
  const content = [];

  Object.entries(messages).forEach(([key, value]) => {
    const textPayload = generateTextPayload(key, value);
    content.push(textPayload);
  });

  const payload = await insertContentToTemplate(template, content);

  await sendPushNotificationToLark(
    PAYOUT_FAILED_ALERT_LARK_WEBHOOK,
    payload
  );
};
