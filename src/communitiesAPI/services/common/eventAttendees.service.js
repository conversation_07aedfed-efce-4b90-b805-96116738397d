const ObjectId = require('mongoose').Types.ObjectId;
const logger = require('../../../services/logger.service');
const EventAttendees = require('../../models/eventAttendees.model');
const Event = require('../../models/communityEvents.model');
const Community = require('../../models/community.model');
const CommunitySubscriptions = require('../../models/communitySubscriptions.model');
const Learners = require('../../../models/learners.model');
const mongodbUtils = require('../../../utils/mongodb.util');

const {
  COMMUNITY_EVENT_ATTENDEE_PURCHASE_TYPES,
  COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES,
} = require('../../constants');

const getEventAttendeesCount = async ({
  eventObjectId,
  status = COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING,
}) => {
  const pipeline = [
    {
      $match: {
        eventObjectId: mongodbUtils.toObjectId(eventObjectId),
        status,
      },
    },
    {
      $group: {
        _id: null,
        total: { $sum: { $ifNull: ['$quantity', 1] } },
      },
    },
  ];

  const result = await EventAttendees.aggregate(pipeline);
  const count = result.length > 0 ? result[0].total : 0;

  return count;
};

const getEventAttendeesProfileImage = async (eventObjectId, dpLimit) => {
  try {
    const learners = await EventAttendees.aggregate([
      {
        $match: {
          eventObjectId: new ObjectId(eventObjectId),
          status: COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING,
        },
      },
      { $limit: dpLimit },
      {
        $lookup: {
          from: 'learners',
          localField: 'learnerObjectId',
          foreignField: '_id',
          as: 'learner',
        },
      },
      { $unwind: '$learner' },
      {
        $project: {
          _id: 0,
          learnerProfileImage: '$learner.profileImage',
        },
      },
    ]);
    return learners;
  } catch (err) {
    logger.error(err.message);
    throw err;
  }
};

const getPaidEventIdsForLearner = async (learnerObjectId) => {
  try {
    const paidEvents =
      (await EventAttendees.find(
        {
          learnerObjectId,
          purchaseType: COMMUNITY_EVENT_ATTENDEE_PURCHASE_TYPES.PAID,
        },
        'eventObjectId'
      )) || [];
    logger.info(`Paid Events for ${learnerObjectId}: `, paidEvents);
    return paidEvents;
  } catch (error) {
    logger.error(
      `Unable to get paid events by learner ${learnerObjectId} due to: `,
      error
    );
    throw error;
  }
};

const getEventRSVPList = async (communityObjectId, eventObjectId) => {
  try {
    const { code = null } = await Community.findById(
      communityObjectId,
      'code'
    );
    const {
      title = null,
      startTime = null,
      endTime = null,
    } = await Event.findById(eventObjectId, 'title startTime endTime');
    const communityCode = code;
    const eventName = title;
    const eventStartTime = startTime;
    const eventEndTime = endTime;
    const eventAttendees = await EventAttendees.aggregate([
      {
        $match: {
          eventObjectId: new ObjectId(eventObjectId),
        },
      },
      {
        $project: {
          learnerObjectId: 1,
          eventObjectId: 1,
          eventName,
          eventStartTime,
          eventEndTime,
          purchaseType: {
            $ifNull: ['$purchaseType', 'free'],
          },
          amount: {
            $ifNull: [
              {
                $divide: ['$amount', 100],
              },
              null,
            ],
          },
          currency: {
            $ifNull: ['$currency', null],
          },
          localAmount: {
            $ifNull: [
              {
                $divide: ['$local_amount', 100],
              },
              null,
            ],
          },
          localCurrency: {
            $ifNull: ['$local_currency', null],
          },
          rsvpDate: {
            $ifNull: ['$createdAt', null],
          },
          ticketReference: 1,
        },
      },
    ]);

    const learnersObjectIdList = eventAttendees.map(
      (eventAttendee) => eventAttendee.learnerObjectId
    );

    const [subscriptions, learners] = await Promise.all([
      CommunitySubscriptions.find(
        {
          communityCode,
          status: 'Current',
          learnerObjectId: {
            $in: learnersObjectIdList,
          },
        },
        'learnerObjectId createdAt'
      ),
      Learners.find(
        {
          _id: {
            $in: learnersObjectIdList,
          },
        },
        '_id email firstName lastName'
      ),
    ]);

    const subscriptionsCache = subscriptions.reduce(
      (acc, subscription) => {
        return acc.set(
          subscription.learnerObjectId.toString(),
          subscription.createdAt
        );
      },
      new Map()
    );

    const learnersCache = learners.reduce((acc, learner) => {
      return acc.set(learner._id.toString(), {
        email: learner.email,
        firstName: learner.firstName,
        lastName: learner.lastName,
      });
    }, new Map());

    const eventAttendeesList = eventAttendees.map((eventAttendee) => {
      const learnerCache = learnersCache.get(
        eventAttendee.learnerObjectId.toString()
      );

      const newEventAttendee = {
        ...eventAttendee,
        email: learnerCache?.email ?? null,
        firstName: learnerCache?.firstName ?? null,
        lastName: learnerCache?.lastName ?? null,
        communitySignupDate:
          subscriptionsCache.get(
            eventAttendee.learnerObjectId.toString()
          ) ?? null,
      };

      delete newEventAttendee.learnerObjectId;
      delete newEventAttendee.eventObjectId;

      return newEventAttendee;
    });

    logger.info(
      `Event id ${eventObjectId} RSVP List is: `,
      eventAttendeesList
    );
    return eventAttendeesList;
  } catch (err) {
    logger.error(err.message);
    throw err;
  }
};

module.exports = {
  getEventAttendeesCount,
  getEventAttendeesProfileImage,
  getPaidEventIdsForLearner,
  getEventRSVPList,
};
