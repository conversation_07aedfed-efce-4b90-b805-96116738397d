const {
  DEFAULT_CURRENCY,
  CONFIG_TYPES,
} = require('../../../constants/common');
const logger = require('../../../services/logger.service');
const { getConfigByType } = require('../../../services/config.service');
const { ParamError } = require('../../../utils/error.util');
const {
  normalizeAndRoundAmountByCurrency,
} = require('../../../utils/currency.util');
const PaymentBackendRpc = require('../../../rpc/paymentBackend');

exports.checkIfValidPriceOfEntity = async (
  amt,
  currency = DEFAULT_CURRENCY
) => {
  const paymentBackendRpc = new PaymentBackendRpc();
  await paymentBackendRpc.init();

  let amount = amt;
  if (Number.isNaN(amount)) {
    // If amount is string, convert it to int
    amount = parseInt(amount, 10);
  }

  if (amount < 0) {
    throw new ParamError('Amount cannot be less than 0');
  }

  // Get price config from db
  const { value = null } = await getConfigByType(
    CONFIG_TYPES.ONETIME_PAYMENT_PRICE_BOUND
  );

  let priceBound = value[currency.toUpperCase()];

  const priceBoundCurrency = priceBound
    ? currency.toUpperCase()
    : DEFAULT_CURRENCY;

  // Use USD price bound to compare if it does not exists
  // Convert amount to USD in order to compare for USD price bound
  if (!priceBound) {
    priceBound = value[DEFAULT_CURRENCY];

    const conversionRateData = await paymentBackendRpc.getConversionRate(
      currency,
      DEFAULT_CURRENCY
    );

    const exchangeRateToItemCurrency = conversionRateData.conversionRate;

    const newAmount = normalizeAndRoundAmountByCurrency(
      amount * exchangeRateToItemCurrency,
      DEFAULT_CURRENCY
    );

    amount = newAmount;
  }

  // Amount 0 refers to free
  if (amount === 0) {
    return true;
  }

  const isValidPrice =
    amount >= priceBound.minPrice && amount <= priceBound.maxPrice;

  if (!isValidPrice) {
    throw new ParamError(
      `Invalid price: ${amount / 100} in ${priceBoundCurrency}`
    );
  }

  return true;
};
