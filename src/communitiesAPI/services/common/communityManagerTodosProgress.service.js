// models
const CommunityManagerTodosProgress = require('../../models/communityManagerTodosProgress.model');
const CommunityManagerTodos = require('../../models/communityManagerTodos.model');
const Learners = require('../../../models/learners.model');
const Config = require('../../../models/config.model');
const {
  communityManagerTodoTypesMap,
  communityManagerTodoCodeMap,
  communityCompletedStepsMap,
} = require('../../constants');
const {
  CHAT_PLATFORMS,
  CONFIG_TYPES,
} = require('../../../constants/common');
//services
const logger = require('../../../services/logger.service');
const CommunityMagicReachEmailService = require('../../services/common/communityMagicReachEmails.service');
const {
  getMemberCountByCommunityId,
} = require('./communitySubscriptions.service');
const { getCommunitiesByIds } = require('./community.service');
const { getFoldersByCommunityId } = require('./communityFolders.service');
const {
  getCommunityEvents,
  countCommunityEvents,
} = require('./communityEvents.service');
const {
  getCommunityAnnouncementsWithAuthors,
} = require('./communityPosts.service');
const CommunityModel = require('../../models/community.model');

const getManagerTodoProgress = async (params = {}) => {
  let members;
  let filters = { ...params };
  try {
    if (
      params.todoType === communityManagerTodoTypesMap.COMMUNITY_SETUP &&
      params.communityObjectId
    ) {
      members = await getMemberCountByCommunityId(
        params.communityObjectId
      );
    }
    const community = await CommunityModel.findOne({
      _id: params.communityObjectId,
    });

    if (!community) {
      logger.error('Community does not exists');
      throw new Error('Community does not exists');
    }

    let todoCodeToExclude = [];
    if (community.bots?.[0]?.type !== CHAT_PLATFORMS.DISCORD) {
      todoCodeToExclude = [
        communityManagerTodoCodeMap.COMMUNITY_DISCORD_VERIFICATION_GENERAL,
        communityManagerTodoCodeMap.COMMUNITY_DISCORD_VERIFICATION_NFT_HOLDERS,
      ];
    } else if (community?.isTokenGated) {
      todoCodeToExclude = [
        communityManagerTodoCodeMap.COMMUNITY_DISCORD_VERIFICATION_GENERAL,
      ];
    } else {
      todoCodeToExclude = [
        communityManagerTodoCodeMap.COMMUNITY_DISCORD_VERIFICATION_NFT_HOLDERS,
      ];
    }

    if (!community.isWhatsappExperienceCommunity) {
      todoCodeToExclude.push(
        communityManagerTodoCodeMap.COMMUNITY_WHATSAPP_BOT_SETUP
      );
    }
    const results = await CommunityManagerTodosProgress.aggregate([
      {
        $match: {
          ...filters,
          isActive: true,
          todoCode: {
            $nin: todoCodeToExclude,
          },
        },
      },
      {
        $lookup: {
          from: 'community_manager_todos',
          localField: 'communityManagerTodoObjectId',
          foreignField: '_id',
          as: 'details',
        },
      },
      {
        $unwind: {
          path: '$details',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          todoCode: 1,
          completed: 1,
          details: 1,
          members: {
            $cond: {
              if: {
                $eq: [
                  '$todoCode',
                  communityManagerTodoCodeMap.COMMUNITY_SETUP_INVITE,
                ],
              },
              then: members,
              else: null,
            },
          },
        },
      },
      {
        $sort: {
          'details.index': 1,
        },
      },
    ]);

    return results;
  } catch (err) {
    logger.error('Error getting communityManagerTodosProgress:', err);
    throw new Error('Cannot get communityManagerTodosProgress');
  }
};

const updateManagerTodoProgress = async (params, payload) => {
  try {
    const updatedData =
      await CommunityManagerTodosProgress.findOneAndUpdate(
        params,
        payload,
        {
          new: true,
        }
      ).lean();
    return updatedData;
  } catch (error) {
    logger.error('Error updating communityManagerTodosProgress:', error);
    throw new Error('Cannot update communityManagerTodosProgress');
  }
};

const getAdditionalManagerTodoProgress = async (todoCode, user) => {
  try {
    const todo = await CommunityManagerTodos.findOne({ todoCode });

    if (
      todoCode ===
      communityManagerTodoCodeMap.COMMUNITY_SETUP_OWNER_PROFILE_PIC
    ) {
      const learner = await Learners.findOne({ email: user?.email });
      const config = await Config.findOne({
        type: CONFIG_TYPES.RANDOM_PROFILE_IMAGE,
        imageLink: learner?.profileImage,
      });

      return {
        todoCode: todo.todoCode,
        details: todo,
        members: null,
        completed: !config,
      };
    }

    return;
  } catch (err) {
    logger.error('Error getting communityManagerTodos:', err);
    throw new Error('Cannot get communityManagerTodos');
  }
};

const validateCompletion = async (todoCode, communityObjectId, mode) => {
  switch (todoCode) {
    case communityManagerTodoCodeMap.COMMUNITY_SETUP_NAME: {
      const community = await getCommunitiesByIds([communityObjectId]);
      return !!community?.[0]?.title;
    }
    case communityManagerTodoCodeMap.COMMUNITY_SETUP_DESCRIPTION: {
      const community = await getCommunitiesByIds([communityObjectId]);
      return !!community?.[0]?.description;
    }
    case communityManagerTodoCodeMap.COMMUNITY_SETUP_CHAT: {
      const community = await getCommunitiesByIds([communityObjectId]);
      return community?.[0]?.bots.length > 0;
    }
    case communityManagerTodoCodeMap.COMMUNITY_SETUP_MEMBER_ACCESS: {
      const community = await getCommunitiesByIds([communityObjectId]);

      if (community?.[0]?.completed_steps) {
        const result = community?.[0]?.completed_steps.some((step) => {
          return step.step === communityCompletedStepsMap.PRICE_SELECTION;
        });
        if (result) return true;
      }

      // isFreeCommunity: True => FREE community
      // stripeProductId not null is paid community => PAID community
      // paymentMethods has a value for web3 => NFT Gated
      let paymentMethodExists = false;
      if (community?.[0]?.payment_methods) {
        paymentMethodExists = community?.[0]?.payment_methods.length > 0;
      }
      return (
        community?.[0]?.isFreeCommunity ||
        (!community?.[0]?.isFreeCommunity &&
          !!community?.[0]?.stripeProductId) ||
        (!community?.[0]?.isFreeCommunity && paymentMethodExists)
      );
    }
    case communityManagerTodoCodeMap.COMMUNITY_SETUP_BANNER: {
      const community = await getCommunitiesByIds([communityObjectId]);
      // eslint-disable-next-line no-nested-ternary
      return !community?.[0]?.fullScreenBannerImgData?.desktopImgProps
        ? false
        : mode === 'mobile'
        ? !!community?.[0]?.fullScreenBannerImgData?.mobileImgProps
        : !!community?.[0]?.fullScreenBannerImgData?.desktopImgProps;
    }
    case communityManagerTodoCodeMap.COMMUNITY_SETUP_LIBRARY: {
      const folders = await getFoldersByCommunityId(
        communityObjectId,
        false
      );
      return folders.length > 0;
    }
    case communityManagerTodoCodeMap.COMMUNITY_SETUP_EVENT: {
      const eventsCount = await countCommunityEvents({
        communities: communityObjectId,
      });
      return eventsCount > 0;
    }
    case communityManagerTodoCodeMap.COMMUNITY_SETUP_ANNOUNCEMENT: {
      const posts = await getCommunityAnnouncementsWithAuthors(
        communityObjectId
      );
      return posts.length > 0;
    }
    case communityManagerTodoCodeMap.COMMUNITY_SETUP_INVITE: {
      const members = await getMemberCountByCommunityId(communityObjectId);
      return members >= 3;
    }
    case communityManagerTodoCodeMap.COMMUNITY_DISCORD_VERIFICATION_GENERAL: {
      const community = await getCommunitiesByIds([communityObjectId]);
      return (
        community?.[0]?.bots?.[0]?.type === CHAT_PLATFORMS.DISCORD &&
        !!community?.[0]?.verificationChannelId &&
        !community?.[0]?.isTokenGated
      );
    }
    case communityManagerTodoCodeMap.COMMUNITY_DISCORD_VERIFICATION_NFT_HOLDERS: {
      const community = await getCommunitiesByIds([communityObjectId]);
      return (
        community?.[0]?.bots?.[0]?.type === CHAT_PLATFORMS.DISCORD &&
        !!community?.[0]?.verificationChannelId &&
        !!community?.[0]?.isTokenGated
      );
    }
    case communityManagerTodoCodeMap.COMMUNITY_SETUP_MAGIC_REACH: {
      const result =
        await CommunityMagicReachEmailService.getAllSentMagicReachEmails({
          communityId: communityObjectId,
        });
      return result.length > 0;
    }
    default: {
      return false;
    }
  }
};

module.exports = {
  getAdditionalManagerTodoProgress,
  getManagerTodoProgress,
  updateManagerTodoProgress,
  validateCompletion,
};
