const ObjectId = require('mongoose').Types.ObjectId;
const SubscriptionModel = require('../../../models/communitySubscriptions.model');
const CommunityModel = require('../../../models/community.model');
const LearnerModel = require('../../../../models/learners.model');
const logger = require('../../../../services/logger.service');
const {
  getExistingMemberParams,
} = require('../../../../utils/communitySubscription.util');

const getLearnerSubscription = async (
  communityCode,
  learnerObjectId,
  fields = '',
  config = {
    checkPendingSubscription: false,
  }
) => {
  try {
    logger.info(
      `Finding Learner's Subscription for ${communityCode} and learner ${learnerObjectId}`
    );
    const withPendingSubscription = config?.checkPendingSubscription;
    const existingMemberParams = getExistingMemberParams({
      withPendingSubscription,
    });

    const subscription = await SubscriptionModel.findOne({
      communityCode,
      learnerObjectId: new ObjectId(learnerObjectId),
      ...existingMemberParams,
    })
      .select(fields)
      .lean();
    return subscription;
  } catch (error) {
    logger.error(
      "Finding Learner's Subscription Failed due to",
      error,
      error.stack
    );
    throw new Error("Finding Learner's Subscription Failed");
  }
};

const retrieveActiveCommunityFromSubscription = async ({
  learnerObjectId,
  withPendingSubscription = false,
}) => {
  const existingMemberParams = getExistingMemberParams({
    withPendingSubscription,
  });

  const communityCodes = await SubscriptionModel.distinct(
    'communityCode',
    {
      learnerObjectId,
      ...existingMemberParams,
    }
  );

  const community = await CommunityModel.findOne({
    code: { $in: communityCodes },
    isActive: true,
  }).lean();

  return community;
};

const getSubscriptionsByLearnerId = async ({
  learnerObjectId,
  fields = '',
  withPendingSubscription = false,
  limit = null, // no limit
}) => {
  const existingMemberParams = getExistingMemberParams({
    withPendingSubscription,
  });

  const getSubscriptionsQuery = SubscriptionModel.find({
    learnerObjectId: new ObjectId(learnerObjectId),
    ...existingMemberParams,
  }).select(fields);

  if (limit) {
    getSubscriptionsQuery.limit(limit);
  }

  const subscriptions = await getSubscriptionsQuery.lean();

  return subscriptions;
};

const isLearnerSubscribedToCommunity = async (
  communityCode,
  learnerObjectId
) => {
  try {
    logger.info(
      `Finding Community Subscription for ${communityCode} and learner ${learnerObjectId}`
    );
    const existingMemberParams = getExistingMemberParams({});
    const existingSubscription = await SubscriptionModel.exists({
      communityCode,
      learnerObjectId: new ObjectId(learnerObjectId),
      ...existingMemberParams,
    });
    return !!existingSubscription;
  } catch (error) {
    logger.error(
      'Is Learner Subscribed To Community check Failed due to',
      error,
      error.stack
    );
    throw new Error('Is Learner Subscribed To Community check Failed');
  }
};

const getSubscriptionsInCommunityByPhoneNumber = async (
  phoneNumber,
  communityCode,
  limit = 1
) => {
  const learners = await LearnerModel.find({ phoneNumber });
  if (!learners || learners.length === 0) {
    return [];
  }
  const learnerIds = learners.map((learner) => learner.learnerId);
  const learnerMap = learners.reduce((acc, learner) => {
    acc[learner.learnerId] = learner;
    return acc;
  }, {});
  const pipeline = [
    {
      $match: {
        communityCode,
        learnerId: { $in: learnerIds },
      },
    },
    { $limit: limit },
    {
      $project: {
        _id: 1,
        email: 1,
        learnerId: 1,
      },
    },
  ];

  const subscriptions = await SubscriptionModel.aggregate(pipeline);

  const results = subscriptions.map((subscription) => {
    const learner = learnerMap[subscription.learnerId];
    return {
      ...subscription,
      learnerObjectId: learner._id,
      phoneNumber,
      isWhatsappSignupUser: learner.isWhatsappSignupUser,
    };
  });

  return results;
};

module.exports = {
  getLearnerSubscription,
  getSubscriptionsByLearnerId,
  isLearnerSubscribedToCommunity,
  getSubscriptionsInCommunityByPhoneNumber,
  retrieveActiveCommunityFromSubscription,
};
