const ObjectId = require('mongoose').Types.ObjectId;
const httpStatus = require('http-status');
const Learner = require('../../../../models/learners.model');
const WhatsappParticipants = require('../../../models/whatsappParticipants.model');
const logger = require('../../../../services/logger.service');
const {
  checkPhoneNumber,
  getFormattedNumber,
} = require('../../../../utils/phoneNumber.util');

const { ToUserError } = require('../../../../utils/error.util');
const { GENERIC_ERROR } = require('../../../../constants/errorCode');

const getCleanedPhoneNumber = (phoneNumber) => {
  let phoneNumberInput = phoneNumber;
  if (!phoneNumber.startsWith('+')) {
    phoneNumberInput = '+' + phoneNumber;
  }

  let isValidNumber;
  try {
    isValidNumber = checkPhoneNumber(phoneNumberInput);
  } catch (err) {
    throw new ToUserError(
      'Invalid Phone Number',
      GENERIC_ERROR.INVALID_PHONE_NUMBER,
      {
        phoneNumber,
      }
    );
  }

  if (!isValidNumber) {
    const error = new ToUserError(
      'Invalid Phone Number',
      GENERIC_ERROR.INVALID_PHONE_NUMBER,
      {
        phoneNumber,
      }
    );
    throw error;
  }

  const cleanedPhoneNumber = getFormattedNumber(phoneNumberInput);
  return cleanedPhoneNumber;
};

const getRegisteredPhoneNumberFromLearner = async (learnerObjectId) => {
  if (!learnerObjectId) {
    const error = new Error('learnerObjectId need to be specified');
    error.status = httpStatus.BAD_REQUEST;
    throw error;
  }

  try {
    const learner = await Learner.findById(learnerObjectId)
      .select('isWhatsappNumberRegistered phoneNumber')
      .lean();

    if (!learner) {
      const error = new Error(
        `Learner with ObjectId ${learnerObjectId} does not exists`
      );
      error.status = httpStatus.BAD_REQUEST;
      throw error;
    }

    if (!learner?.phoneNumber || !learner?.phoneNumber === '') {
      logger.info(
        `Learner with ObjectId ${learnerObjectId} does not have a phoneNumber`
      );
      return null;
    }

    if (!learner?.isWhatsappNumberRegistered) {
      logger.info(
        `Learner with ObjectId ${learnerObjectId} phoneNumber ${learner?.phoneNumber} is not registered`
      );
      return null;
    }

    return learner?.phoneNumber;
  } catch (error) {
    logger.error(
      `Get Learner Registered PhoneNumber Failed due to: ${error}`,
      error.stack
    );
    throw error;
  }
};

const getLearnerWithRegisteredPhoneNumber = async (
  phoneNumber,
  extraFilter = {},
  fields = ''
) => {
  if (!phoneNumber) {
    const error = new Error('phoneNumber need to be specified');
    error.status = httpStatus.BAD_REQUEST;
    throw error;
  }

  try {
    logger.info(
      `Finding Learner with Registered PhoneNumber ${phoneNumber}`
    );
    const learner = await Learner.findOne({
      ...extraFilter,
      phoneNumber,
      isWhatsappNumberRegistered: true,
    })
      .select(fields)
      .lean();
    logger.info(
      `Found Learner with Registered PhoneNumber ${phoneNumber}`
    );
    return learner;
  } catch (error) {
    logger.error(
      `Get Learner with Registered PhoneNumber ${phoneNumber} Failed due to: ${error}`
    );
    throw error;
  }
};

const getPhoneNumberStatus = async (
  phoneNumber,
  filter = {},
  whatsappCheck = false
) => {
  if (!phoneNumber) {
    const error = new Error('phoneNumber need to be specified');
    error.status = httpStatus.BAD_REQUEST;
    throw error;
  }

  try {
    const isPhoneNumberRegistered =
      await getLearnerWithRegisteredPhoneNumber(
        phoneNumber,
        filter,
        '_id'
      );

    const result = {
      registeredLearner: isPhoneNumberRegistered?._id,
      isRegistered: !!isPhoneNumberRegistered,
      phoneNumber,
    };
    if (!whatsappCheck) {
      return result;
    }
    const existingParticipant = await WhatsappParticipants.exists({
      number: phoneNumber.replace('+', ''),
    });
    const whatsappNumberInDB = !!existingParticipant;

    return {
      ...result,
      whatsappNumberInDB,
    };
  } catch (error) {
    logger.error(
      `Getting PhoneNumber Status Failed due to: ${error}`,
      error.stack
    );
    throw error;
  }
};

const updateLearnerPhoneNumber = async (learnerObjectId, phoneNumber) => {
  if (!learnerObjectId && !phoneNumber) {
    const error = new Error(
      'Both learnerObjectId and phoneNumber need to be specified'
    );
    error.status = httpStatus.BAD_REQUEST;
    throw error;
  }

  try {
    const { isRegistered, registeredLearner } = await getPhoneNumberStatus(
      phoneNumber
    );

    let isWhatsappNumberRegistered = false;
    if (!isRegistered || learnerObjectId === registeredLearner) {
      isWhatsappNumberRegistered = true;
    }

    logger.info(`Updating learner ${learnerObjectId} with ${phoneNumber}`);

    const learner = await Learner.findByIdAndUpdate(
      learnerObjectId,
      {
        phoneNumber,
        isWhatsappNumberRegistered,
      },
      {
        new: true,
        upsert: false,
        select: '_id phoneNumber isWhatsappNumberRegistered email',
      }
    );

    logger.info(`Updated learner ${learnerObjectId} with ${learner}`);

    return learner;
  } catch (error) {
    logger.error(`Update Learner PhoneNumber Failed due to: ${error}`);
    throw error;
  }
};

module.exports = {
  getCleanedPhoneNumber,
  getRegisteredPhoneNumberFromLearner,
  getLearnerWithRegisteredPhoneNumber,
  getPhoneNumberStatus,
  updateLearnerPhoneNumber,
};
