const logger = require('../../../services/logger.service');

const { sendEmail } = require('../../../services/notification/index');

const {
  identifyUser,
  trackAction,
  subscribeToList,
} = require('../../../handlers/klaviyo.handler');

const sendZapierMails = async (payload = {}) => {
  try {
    const { mailType, mailCourse, mailDataPayload } = payload;
    Object.keys(mailDataPayload).forEach(
      (k) => mailDataPayload[k] == null && delete mailDataPayload[k]
    );
    const mailReqBody = {
      mailType,
      mailCourse,
      mailCourseOffer: 'All',
      toMail: [],
      toMailName: [],
      data: mailDataPayload,
    };
    await sendEmail(mailReqBody);
  } catch (error) {
    logger.error('Error in sending zapier mails', error);
  }
};

const communityCreatedReferralAlertEmails = async (
  toMail,
  toMailName,
  params
) => {
  const mailDataCommon = {
    mailCourse: 'All',
    mailCourseOffer: 'All',
    toMail: [toMail],
    toMailName: [toMailName],
    data: params,
  };

  const communitySummaryEmailParams = {
    ...mailDataCommon,
    mailType: 'COMMUNITY_CREATION_REFERRAL_USAGE_ALERT_TO_REFERRER',
  };

  try {
    logger.info(
      'Sending Community Created Referral Usage Alert to Referrer email'
    );
    await sendEmail(communitySummaryEmailParams);
  } catch (error) {
    logger.error(
      'Unable to send Community Created Referral Usage Alert to Referrer email'
    );
  }
};

const communityCreatedReferralEmails = async (
  toMail,
  toMailName,
  params
) => {
  const mailDataCommon = {
    mailCourse: 'All',
    mailCourseOffer: 'All',
    toMail: [toMail],
    toMailName: [toMailName],
    data: params,
  };

  const communitySummaryEmailParams = {
    ...mailDataCommon,
    mailType: 'COMMUNITY_CREATION_REFERRAL_USAGE_TO_REFEREE',
  };

  try {
    logger.info(
      'Sending Community Created Referral Usage to Referee email'
    );
    await sendEmail(communitySummaryEmailParams);
  } catch (error) {
    logger.error(
      'Unable to send Community Created Referral Usage to Referee email'
    );
  }
};

const communityCreatedEmails = async (toMail, toMailName, params) => {
  const mailDataCommon = {
    mailCourse: params?.community_code ?? 'All',
    mailCourseOffer: 'All',
    toMail: [toMail],
    toMailName: [toMailName],
    data: params,
  };
  // const welcomeCommunityEmailParams = {
  //   ...mailDataCommon,
  //   mailType: 'COMMUNITY_CREATION_WELCOME',
  // };
  // try {
  //   logger.info('Sending Community Created Welcome email');
  //   await sendEmail(welcomeCommunityEmailParams);
  // } catch (error) {
  //   // console.log(error);
  //   logger.error('Unable to send Community Created Welcome email');
  // }

  const communitySummaryEmailParams = {
    ...mailDataCommon,
    mailType: 'COMMUNITY_CREATION_WELCOME_SUMMARY',
  };
  if (params?.isWhatsappExperienceCommunity) {
    communitySummaryEmailParams.mailType =
      'WHATSAPP_COMMUNITY_CREATION_WELCOME_SUMMARY';
  }
  try {
    logger.info('Sending Community Created Welcome Summary email');
    await sendEmail(communitySummaryEmailParams);
  } catch (error) {
    // console.log(error);
    logger.error('Unable to send Community Created Welcome Summary email');
  }
};

const communityCreatedKlaviyoEvents = async (params = {}) => {
  const { email, languagePreference } = params;
  const payload = {
    email,
    properties: {
      languagePreference,
    },
    post: true,
  };
  await identifyUser(payload);
  await trackAction(email, 'CommunityCreated', params);
  await subscribeToList({ email });
};

const communityMemberAccessChangeKlaviyoEvents = async (params = {}) => {
  const { email } = params;
  const customerProperties = { email };
  await identifyUser(customerProperties);
  await trackAction(email, 'CommunityMemberAccessChange', params);
  await subscribeToList(customerProperties);
};

const communityManagerLanguagePreferenceChangeEvent = async (
  params = {}
) => {
  const { email, languagePreference } = params;
  const payload = {
    email,
    properties: {
      languagePreference,
    },
    post: true,
  };
  await identifyUser(payload);
  // trackAction(email, 'CommunityLanguagePreferenceChange', params);
  // subscribeToList(customerProperties);
};

const communityMemberRemoved = async (params = {}) => {
  const mailReqBody = {
    mailType,
    mailCourse,
    mailCourseOffer: 'All',
    toMail: [],
    toMailName: [],
    data: mailDataPayload,
  };
  await sendEmail(mailReqBody);
};

module.exports = {
  sendZapierMails,
  communityCreatedEmails,
  communityCreatedReferralAlertEmails,
  communityCreatedReferralEmails,
  communityCreatedKlaviyoEvents,
  communityMemberAccessChangeKlaviyoEvents,
  communityManagerLanguagePreferenceChangeEvent,
};
