/* eslint-disable no-param-reassign */
// models
const CommunityResources = require('../../models/communityResources.model');

// services
const logger = require('../../../services/logger.service');

const ObjectId = require('mongoose').Types.ObjectId;
const { DateTime } = require('luxon');

const getCommunityResourcesPreview = async (communityObjectId) => {
  try {
    const result = await CommunityResources.aggregate([
      {
        $match: {
          communityObjectId: new ObjectId(communityObjectId),
        },
      },
      {
        $addFields: {
          createdAt: { $toDate: '$_id' },
        },
      },
      {
        $group: {
          _id: '$topicIndex',
          topic: { $first: '$topic' },
          topicIndex: { $first: '$topicIndex' },
          emoji: { $first: '$emoji' },
          items: { $push: '$$ROOT' },
        },
      },
      {
        $project: {
          _id: 0,
          topicIndex: 1,
          topic: 1,
          emoji: 1,
          items: 1,
          resourceCount: { $size: '$items' },
          shortUrl: 1,
        },
      },
      {
        $sort: {
          topicIndex: 1,
        },
      },
    ]);
    if (!result) {
      throw new Error('No Resources found');
    }

    result.forEach((item) => {
      const lastWeekDate = DateTime.utc().minus({ days: 7 });
      const isNew = item?.items.some(
        (resource) => resource?.createdAt > lastWeekDate
      );
      item.isNew = isNew;
      delete item.items;
    });

    return result;
  } catch (err) {
    logger.error(err.message);
    throw err;
  }
};

const getCommunityResources = async (communityObjectId) => {
  try {
    const result = await CommunityResources.aggregate([
      {
        $match: {
          communityObjectId: new ObjectId(communityObjectId),
        },
      },
      {
        $addFields: {
          createdAt: { $toDate: '$_id' },
        },
      },
      {
        $group: {
          _id: '$topicIndex',
          topic: { $first: '$topic' },
          topicIndex: { $first: '$topicIndex' },
          items: { $push: '$$ROOT' },
        },
      },
      {
        $project: {
          _id: 0,
          topicIndex: 1,
          topic: 1,
          items: 1,
          shortUrl: 1,
        },
      },
      {
        $sort: {
          topicIndex: 1,
        },
      },
    ]);
    if (!result) {
      throw new Error('No Resources found');
    }

    return result;
  } catch (err) {
    logger.error(err.message);
    throw err;
  }
};

module.exports = { getCommunityResourcesPreview, getCommunityResources };
