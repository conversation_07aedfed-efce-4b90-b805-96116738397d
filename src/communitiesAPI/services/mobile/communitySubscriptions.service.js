const ObjectId = require('mongoose').Types.ObjectId;

// models
const Learners = require('../../../models/learners.model');
const Community = require('../../models/community.model');
const CommunitySubscriptions = require('../../models/communitySubscriptions.model');
const CommunityRole = require('../../models/communityRole.model');

// services
const {
  getCurrentSubscription,
  getMemberCountByCommunityCode,
  getMemberCountByCommunityId,
} = require('../common/communitySubscriptions.service');
const logger = require('../../../services/logger.service');

const {
  communityEnrolmentStatuses,
  ANONYMOUS_COMMUNITY_USER_NAME,
} = require('../../../constants/common');
const { getQueryBasedOnSingleFilter } = require('../../utils');
const {
  defaultPaginatedResponse,
  getPaginationDataAggregatePipelineStage,
  getNextAndPrevious,
} = require('../../../utils/pagination.util');
const {
  getCountryInfoByIdFromMemoryCache,
} = require('../../../services/countryInfoMapping/countryInfoMapping.service');

const getFeaturedCommunityMembers = async (learnerIds = []) => {
  try {
    const result = [];
    return result;
  } catch (err) {
    logger.error(err.message);
    throw err;
  }
};

function postDBProcessLearner(learner, learnerIdToSubscriptionMap) {
  const toReturnLearner = learner;
  let countryFromCache = getCountryInfoByIdFromMemoryCache(
    learner.countryId
  );
  if (countryFromCache === undefined) {
    logger.error(
      `Cannot get from cache for CountryInfoMapping [${learner.countryId}]`
    );
    countryFromCache = {
      country: '',
    };
  }
  toReturnLearner.country = countryFromCache.country;

  toReturnLearner.subscriptions =
    learnerIdToSubscriptionMap[learner.learnerId].reverse();
  toReturnLearner.joinedCommunityDate = learner.subscriptions.map(
    (subscription) => subscription.createdAt
  );

  return toReturnLearner;
}

function reorderLearnersBySubscription(
  learnersMap,
  sortedUniqueByLearnerIdSubscription
) {
  const results = [];
  for (subscription of sortedUniqueByLearnerIdSubscription) {
    if (subscription.learnerId in learnersMap) {
      results.push(learnersMap[subscription.learnerId]);
    }
  }
  return results;
}

function sortLearnersByJoinedDate(
  sortOrder,
  learners,
  sortedUniqueByLearnerIdSubscription
) {
  const learnersMap = learners.reduce((acc, learner) => {
    acc[learner.learnerId] = learner;
    return acc;
  }, {});
  if (sortOrder === 1) {
    return reorderLearnersBySubscription(
      learnersMap,
      sortedUniqueByLearnerIdSubscription.reverse()
    );
  }
  return reorderLearnersBySubscription(
    learnersMap,
    sortedUniqueByLearnerIdSubscription
  );
}

const getCommunityMembers = async ({
  communityId,
  paginate = 0,
  pageNum = null,
  pageSize = null,
  sortBy = null,
  sortOrder = null,
  filterQuery,
  countryId,
  isManager = false,
}) => {
  try {
    const community = await Community.findById(communityId);
    if (!community) {
      throw new Error('Invalid communityId!');
    }

    // get from subscription and sorted by last joined for easier data processing
    const communitySubsciptions = await CommunitySubscriptions.find(
      {
        communityCode: community.code,
        status: communityEnrolmentStatuses.CURRENT,
      },
      { learnerId: 1, createdAt: 1 }
    ).sort({ createdAt: 'desc' });

    const learnerIdToSubscriptionMap = {};
    const sortedUniqueByLearnerIdSubscription = [];
    const learnerIds = [];

    communitySubsciptions.forEach((subscription) => {
      if (subscription.learnerId in learnerIdToSubscriptionMap) {
        learnerIdToSubscriptionMap[subscription.learnerId].push(
          subscription
        );
      } else {
        learnerIds.push(subscription.learnerId);
        sortedUniqueByLearnerIdSubscription.push(subscription);
        learnerIdToSubscriptionMap[subscription.learnerId] = [
          subscription,
        ];
      }
    });

    const pipeline = [
      {
        $match: {
          learnerId: { $in: learnerIds },
          ...getQueryBasedOnSingleFilter(filterQuery, countryId),
        },
      },
    ];

    // remove the support in app for fullName default sorting
    // change it to sorting by joined date asc (old -> new)
    if (sortBy === 'fullName') {
      pipeline.push({ $sort: { joinedCommunityDate: 1 } });
    }
    if (
      sortBy &&
      sortOrder &&
      sortBy !== 'joinedCommunityDate' &&
      sortBy !== 'fullName'
    ) {
      pipeline.push({ $sort: { sortyBy: sortOrder } });
    }

    const contactUsernamesProjection = isManager
      ? { $ifNull: ['$contactUsernames', []] }
      : [];

    const displayEmail = isManager ? { email: 1 } : {};

    pipeline.push(
      ...[
        {
          $project: {
            ...displayEmail,
            countryId: 1,
            bio: { $ifNull: ['$bio', null] },
            skills: { $ifNull: ['$skills', []] },
            interests: { $ifNull: ['$interests', []] },
            spotlights: { $ifNull: ['$spotlights', []] },
            contactUsernames: contactUsernamesProjection,
            followersCount: { $ifNull: ['$followersCount', 0] },
            primaryContact: { $ifNull: ['$primaryContact', null] },
            isActive: 1,
            firstName: 1,
            lastName: 1,
            learnerId: 1,
            profileImage: 1,
            socialMedia: 1,
            creations: 1,
            subtitlePreference: 1,
            lastModifiedTimeStamp: 1,
            createdAt: 1,
            fullName: {
              $switch: {
                branches: [
                  {
                    case: {
                      $and: [
                        { $gt: ['$firstName', null] },
                        { $gt: ['$lastName', null] },
                      ],
                    },
                    then: { $concat: ['$firstName', ' ', '$lastName'] },
                  },
                  {
                    case: {
                      $and: [
                        { $gt: ['$firstName', null] },
                        { $lte: ['$lastName', null] },
                      ],
                    },
                    then: '$firstName',
                  },
                  {
                    case: {
                      $and: [
                        { $lte: ['$firstName', null] },
                        { $gt: ['$lastName', null] },
                      ],
                    },
                    then: '$lastName',
                  },
                  {
                    case: {
                      $and: [
                        { $lte: ['$firstName', null] },
                        { $lte: ['$lastName', null] },
                      ],
                    },
                    then: ANONYMOUS_COMMUNITY_USER_NAME,
                  },
                ],
              },
            },
          },
        },
      ]
    );

    if (paginate === 1) {
      const paginationDataAggregatePipelineStage =
        getPaginationDataAggregatePipelineStage(pageNum, pageSize);

      const paginationPipeline = {
        $facet: {
          metadata: [
            {
              $count: 'total',
            },
          ],
          data: paginationDataAggregatePipelineStage,
        },
      };
      pipeline.push(paginationPipeline);
      pipeline.push({ $unwind: '$metadata' });
    }

    let learners = await Learners.aggregate(pipeline);
    if (paginate === 1) {
      learners = learners?.[0] || null;
      if (!learners) {
        learners = defaultPaginatedResponse;
      }
      const { next = null, previous = null } = getNextAndPrevious(
        pageNum,
        pageSize,
        learners?.metadata?.total
      );
      learners.metadata.next = next;
      learners.metadata.previous = previous;

      if (learners?.data?.length) {
        learners.data = learners.data.map((learner) => {
          return postDBProcessLearner(learner, learnerIdToSubscriptionMap);
        });

        if (sortBy === 'joinedCommunityDate') {
          learners.data = sortLearnersByJoinedDate(
            sortOrder,
            learners.data,
            sortedUniqueByLearnerIdSubscription
          );
        }
      }
    } else if (learners?.length) {
      learners = learners.map((learner) => {
        return postDBProcessLearner(learner, learnerIdToSubscriptionMap);
      });

      if (sortBy === 'joinedCommunityDate') {
        learners = sortLearnersByJoinedDate(
          sortOrder,
          learners,
          sortedUniqueByLearnerIdSubscription
        );
      }
    }

    // community creator -> as of now we just have the By key in community collection
    // community moderator -> who can create posts in community
    // community manager -> who can manage community
    const communityManagerUsers = await CommunityRole.aggregate([
      {
        $match: {
          role: 'admin',
          communityObjectId: new ObjectId(communityId),
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'userObjectId',
          foreignField: '_id',
          as: 'user',
        },
      },
      {
        $unwind: {
          path: '$user',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: 'learners',
          localField: 'user.learner',
          foreignField: '_id',
          as: 'user.learner',
        },
      },
      {
        $unwind: {
          path: '$user.learner',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: 'country_currency_mapping',
          localField: 'user.learner.countryId',
          foreignField: 'countryId',
          as: 'user.learner.countryData',
        },
      },
      {
        $unwind: {
          path: '$user.learner.countryData',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: 'trainers',
          localField: 'user.trainer',
          foreignField: '_id',
          as: 'user.trainer',
        },
      },
      {
        $unwind: {
          path: '$user.trainer',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: 'country_currency_mapping',
          localField: 'user.learner.countryId',
          foreignField: 'countryId',
          as: 'user.trainer.countryData',
        },
      },
      {
        $unwind: {
          path: '$user.trainer.countryData',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          firstName: {
            $ifNull: [
              '$user.learner.firstName',
              '$user.trainer.firstName',
            ],
          },
          lastName: {
            $ifNull: ['$user.learner.lastName', '$user.trainer.lastName'],
          },
          profileImage: {
            $ifNull: [
              '$user.learner.profileImage',
              '$user.trainer.profileImage',
            ],
          },
          countryId: {
            $ifNull: [
              '$user.learner.countryId',
              '$user.trainer.countryId',
            ],
          },
          country: {
            $ifNull: [
              '$user.learner.countryData.country',
              '$user.trainer.countryData.country',
            ],
          },
          description: {
            $ifNull: [
              '$user.learner.description',
              '$user.trainer.description',
            ],
          },
          longDescription: {
            $ifNull: [
              '$user.learner.longDescription',
              '$user.trainer.longDescription',
            ],
          },
          socialMedia: {
            $ifNull: [
              '$user.learner.socialMedia',
              '$user.trainer.socialMedia',
            ],
          },
        },
      },
    ]);
    return { data: learners, communityManagers: communityManagerUsers };
  } catch (err) {
    logger.error(err.message);
    throw err;
  }
};

module.exports = {
  getCurrentSubscription,
  getFeaturedCommunityMembers,
  getCommunityMembers,
  getMemberCountByCommunityCode,
  getMemberCountByCommunityId,
};
