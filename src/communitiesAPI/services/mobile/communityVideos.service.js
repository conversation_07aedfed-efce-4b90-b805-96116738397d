/* eslint-disable no-param-reassign */
// models
const CommunityVideos = require('../../models/communityVideos.model');

// services
const logger = require('../../../services/logger.service');
const {
  getCorrectVideoLink,
  replacePrivateLinksInVideos,
} = require('../../utils');
const ObjectId = require('mongoose').Types.ObjectId;
const { DateTime } = require('luxon');

const getCommunityVideosAggregated = async (params = {}) => {
  try {
    const result = await CommunityVideos.aggregate([
      {
        $match: params,
      },
      {
        $lookup: {
          from: 'videos',
          localField: 'video',
          foreignField: '_id',
          as: 'videos',
        },
      },
      {
        $unwind: {
          path: '$videos',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          tag: 1,
          topicIndex: 1,
          subIndex: 1,
          title: 1,
          topic: 1,
          shortUrl: 1,
          link: '$videos.link',
          hlsLink: '$videos.hlsLink',
          duration: '$videos.duration',
          thumbnail: '$videos.thumbnailLink',
          createdAt: { $toDate: '$videos._id' },
        },
      },
      {
        $sort: {
          topicIndex: 1,
          subIndex: 1,
        },
      },
      {
        $group: {
          _id: '$topicIndex',
          objectId: { $first: '$_id' },
          thumbnail: { $first: '$thumbnail' },
          link: { $first: '$link' },
          hlsLink: { $first: '$hlsLink' },
          duration: { $first: '$duration' },
          topic: { $first: '$topic' },
          topicIndex: { $first: '$topicIndex' },
          items: { $push: '$$ROOT' },
        },
      },
      {
        $project: {
          _id: 1,
          topic: 1,
          objectId: 1,
          link: 1,
          hlsLink: 1,
          duration: 1,
          thumbnail: 1,
          topicIndex: 1,
        },
      },
      {
        $sort: {
          topicIndex: 1,
        },
      },
    ]);
    if (!result) {
      throw new Error('No Videos found');
    }
    result.forEach((item) => {
      item.thumbnail = getCorrectVideoLink(item.thumbnail);
      item.link = getCorrectVideoLink(item.link);
      item.hlsLink = getCorrectVideoLink(item.hlsLink);
    });

    return result;
  } catch (err) {
    logger.error(err.message);
    throw err;
  }
};

const getCommunityVideoPreviews = async (communityObjectId) => {
  try {
    const params = { communityObjectId: new ObjectId(communityObjectId) };
    const results = await getCommunityVideosAggregated(params);
    return results;
  } catch (err) {
    logger.error(
      `something went wrong while getting video perviews ${err}`
    );
    throw err;
  }
};

const getCommunityVideo = async (communityVideoObjectId) => {
  try {
    const params = { _id: new ObjectId(communityVideoObjectId) };
    const result = await getCommunityVideosAggregated(params);
    if (!result?.length) {
      throw new Error('No matching community video found for given todo');
    }
    return result[0];
  } catch (err) {
    logger.error(
      `something went wrong while getting video perviews ${err}`,
      err.stack
    );
    throw err;
  }
};

const getCommunityClassVideoPreview = async (communityObjectId) => {
  try {
    const result = await CommunityVideos.aggregate([
      {
        $match: { communityObjectId: new ObjectId(communityObjectId) },
      },
      {
        $lookup: {
          from: 'videos',
          localField: 'video',
          foreignField: '_id',
          as: 'videos',
        },
      },
      {
        $unwind: {
          path: '$videos',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          tag: 1,
          topicIndex: 1,
          subIndex: 1,
          title: 1,
          topic: 1,
          shortUrl: 1,
          thumbnail: '$videos.thumbnailLink',
          createdAt: { $toDate: '$videos._id' },
        },
      },
      {
        $sort: {
          topicIndex: 1,
          subIndex: 1,
        },
      },
      {
        $group: {
          _id: '$topicIndex',
          objectId: { $first: '$_id' },
          thumbnail: { $first: '$thumbnail' },
          topic: { $first: '$topic' },
          topicIndex: { $first: '$topicIndex' },
          items: { $push: '$$ROOT' },
        },
      },
      {
        $project: {
          _id: 1,
          topic: 1,
          objectId: 1,
          thumbnail: 1,
          topicIndex: 1,
          videoCount: { $size: '$items' },
          items: '$items',
        },
      },
      {
        $sort: {
          topicIndex: 1,
        },
      },
    ]);
    if (!result) {
      throw new Error('No Videos found');
    }
    result.forEach((item) => {
      item.thumbnail = getCorrectVideoLink(item.thumbnail);
      const lastWeekDate = DateTime.utc().minus({ days: 7 });
      const isNew = item?.items.some(
        (video) => video?.createdAt > lastWeekDate
      );
      item.isNew = isNew;
      delete item.items;
    });

    return result;
  } catch (err) {
    // console.log(err);
    logger.error(err.message);
    throw err;
  }
};

const getCommunityVideos = async (communityObjectId) => {
  try {
    const result = await CommunityVideos.aggregate([
      {
        $match: {
          communityObjectId: new ObjectId(communityObjectId),
          isActive: true,
        },
      },
      {
        $lookup: {
          from: 'videos',
          localField: 'video',
          foreignField: '_id',
          as: 'videos',
        },
      },
      {
        $unwind: {
          path: '$videos',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          tag: 1,
          topicIndex: 1,
          subIndex: 1,
          title: 1,
          topic: 1,
          createdAt: { $toDate: '$videos._id' },
          shortUrl: 1,
          link: '$videos.link',
          hlsLink: '$videos.hlsLink',
          thumbnail: '$videos.thumbnailLink',
          duration: '$videos.duration',
          subtitles: '$videos.subtitles',
        },
      },
      {
        $sort: {
          topicIndex: 1,
          subIndex: 1,
        },
      },
      {
        $group: {
          _id: '$topicIndex',
          topic: { $first: '$topic' },
          topicIndex: { $first: '$topicIndex' },
          items: { $push: '$$ROOT' },
        },
      },
      { $project: { _id: 1, topic: 1, items: 1, topicIndex: 1 } },
      {
        $sort: {
          topicIndex: 1,
        },
      },
    ]);
    if (!result) {
      throw new Error('No Videos found');
    }
    result.forEach((item) => {
      // eslint-disable-next-line no-param-reassign
      item.items = replacePrivateLinksInVideos(item.items);
    });
    return result;
  } catch (err) {
    // console.log(err);
    logger.error(err.message);
    throw err;
  }
};

module.exports = {
  getCommunityVideoPreviews,
  getCommunityVideo,
  getCommunityClassVideoPreview,
  getCommunityVideos,
};
