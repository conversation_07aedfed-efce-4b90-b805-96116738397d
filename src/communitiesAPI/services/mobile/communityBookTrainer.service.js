/* eslint-disable camelcase */
const CommunityBookTrainerModel = require('../../models/communityBookTrainer.model');
const axios = require('../../../clients/axios.client');
const CommunityModel = require('../../models/community.model');
const ObjectId = require('mongoose').Types.ObjectId;
const logger = require('../../../services/logger.service');
const {
  CONFIG_TYPES,
  CALENDLY_EVENT_TYPES,
} = require('../../../constants/common');
const { getConfigByType } = require('../../../services/config.service');

const getCalendlyToken = async () => {
  try {
    const { envVarData = null } = await getConfigByType(
      CONFIG_TYPES.LEARN_PORTAL_BACKEND_ENV_CONFIG_TYPE
    );
    const { CALENDLY_TOKEN = null } = envVarData;
    logger.info('Got calendly token');
    return CALENDLY_TOKEN;
  } catch (error) {
    logger.error('Error getting calendly token', error);
    throw error;
  }
};

const getCalendyData = async (uri, method = 'get', body = {}) => {
  try {
    const token = await getCalendlyToken();
    const axiosConfig = {
      method,
      url: uri,
      body: JSON.stringify(body),
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    };
    logger.info(
      'Calendly api request config from mobile app: ',
      axiosConfig
    );
    const response = await axios(axiosConfig);
    if (response?.status === 200 || response?.status === 201) {
      const calendlyData = response?.data;
      return calendlyData?.resource;
    }
    if (response?.status === 403) {
      if (response?.data?.message === 'Event is already canceled') {
        throw new Error('Event is already canceled');
      }
    }
  } catch (error) {
    logger.error(
      'Unable to get calendly data from mobile app due to: ',
      error
    );
    throw error;
  }
};

const getCommunityTrainer = async (communityId) => {
  try {
    const { trainerId = null } = await CommunityModel.findById(
      communityId,
      'trainerId'
    );
    logger.info(
      'trainerId in getCommunityTrainer for mobile app: ',
      trainerId
    );
    return trainerId;
  } catch (error) {
    logger.error('Error getting community trainer for mobile app', error);
    return null;
  }
};

const getLatestBookingForLearner = async (
  communityId,
  learnerId,
  trainerID = null
) => {
  try {
    let trainerId = trainerID || (await getCommunityTrainer(communityId));
    logger.info(
      'trainerId in getLatestBookingForLearner for mobile app: ',
      trainerId
    );
    const booking = await CommunityBookTrainerModel.find({
      communityId: new ObjectId(communityId),
      learnerId: new ObjectId(learnerId),
      trainerId: new ObjectId(trainerId),
      status: 'active',
      endTime: { $gt: new Date() },
    })
      .sort({ startTime: -1 })
      .limit(1);
    logger.info(
      'booking in getLatestBookingForLearner for mobile app: ',
      booking
    );
    return booking?.length ? booking?.[0] : null;
  } catch (error) {
    logger.error(
      'Error getting latest booking for learner for mobile app',
      error
    );
    throw error;
  }
};

const getOrCreateBooking = async (params) => {
  try {
    let booking = null;
    const { communityId, learnerId, trainerId = null } = params;
    const oldBooking = await getLatestBookingForLearner(
      communityId,
      learnerId,
      trainerId
    );
    logger.info('old Booking found for mobile app', oldBooking);
    if (oldBooking) {
      booking = oldBooking;
    }
    logger.info('No booking found for mobile app. Creating new booking');
    const newBooking = await CommunityBookTrainerModel.create(params);
    logger.info('New booking created for mobile app', newBooking);
    if (newBooking) {
      booking = newBooking;
    }
    return booking;
  } catch (error) {
    logger.error(
      'Error getting or creating booking for mobile app',
      error
    );
    throw error;
  }
};

const cancelBookingEvent = async (params) => {
  const { learnerObjectId, communityId, cancelReason = null } = params;

  try {
    const booking = await getLatestBookingForLearner(
      communityId,
      learnerObjectId
    );

    if (!booking) throw new Error('No booking found');
    // https://calendly.stoplight.io/docs/api-docs/afb2e9fe3a0a0-cancel-event
    // Api Reference for cancelling an event
    const { bookingLink } = booking;

    const response = await getCalendyData(
      `${bookingLink}/cancellation`,
      'post',
      {
        reason: cancelReason,
      }
    );
    if (response) {
      // Fix up the mongo db record
      booking.status = 'cancelled';
      await booking.save();
      return booking;
    }
  } catch (err) {
    if (err.message === 'No booking found') {
      throw new Error('No booking found');
    }
    if (err?.response?.data?.message === 'Event is already canceled') {
      throw new Error('Event is already canceled');
    }
    logger.error('Error cancelling booking', err);
    throw new Error('Error cancelling booking');
  }
};

const registerBookingEvent = async (params) => {
  const {
    email,
    eventUri,
    inviteeUri,
    communityId,
    learnerObjectId,
    learnerId,
  } = params;

  // Get Calendly Data from Calendly API
  try {
    const [eventData, inviteeData, trainerId = null] = await Promise.all([
      getCalendyData(eventUri),
      getCalendyData(inviteeUri),
      getCommunityTrainer(communityId),
    ]);
    const {
      name,
      start_time: startTime,
      end_time: endTime,
      status,
    } = eventData;
    const {
      cancel_url: cancelLink,
      reschedule_url: rescheduleLink,
      rescheduled,
    } = inviteeData;

    const eventDetails = {
      trainerId,
      learnerId: learnerObjectId,
      communityId,
      status,
      startTime,
      endTime,
      bookingLink: eventUri,
      rescheduleLink,
      cancelLink,
      inviteeLink: inviteeUri,
    };

    const booking = await getOrCreateBooking(eventDetails);
    logger.info(
      'booking in registerBookingEvent for mobile app: ',
      booking
    );
    return booking;
  } catch (err) {
    logger.error('Error registering booking for mobile app', err);
    throw err;
  }
};

const cancelBookingEventMobile = async (params) => {
  const { learnerObjectId, communityId, cancelReason } = params;

  try {
    let booking = await getLatestBookingForLearner(
      communityId,
      learnerObjectId
    );

    if (!booking) throw new Error('No booking found');

    // Fix up the mongo db record
    booking.status = 'cancelled';
    booking = await booking.save();
    logger.info('Cancelled booking: ', booking);
    return booking;
  } catch (err) {
    logger.error('Error cancelling booking', err);
    throw new Error('Error cancelling booking');
  }
};

const calendlyWebhookListener = async (params) => {
  logger.info('calendly webhook payload ', params);
  try {
    const {
      event = null,
      created_at = null,
      created_by = null,
      payload = null,
    } = params;
    logger.info(`${event} created at ${created_at} by ${created_by}`);
    const {
      uri: inviteeUri,
      email,
      event: eventUri,
      tracking,
      cancellation,
      rescheduled,
      new_invitee,
      ...otherDetails
    } = payload;
    const { utm_content = null } = tracking;
    const utmData = utm_content ? JSON.parse(utm_content) : {};
    const { learnerObjectId = null, communityObjectId = null } = utmData;
    logger.info(
      'learnerObjectId from tracking.utm_content',
      learnerObjectId
    );
    logger.info(
      'communityObjectId from tracking.utm_content',
      communityObjectId
    );

    let booking = null;
    if (learnerObjectId && communityObjectId) {
      switch (event) {
        case CALENDLY_EVENT_TYPES.CREATED:
          if (rescheduled) {
            booking = await registerBookingEvent({
              email,
              eventUri,
              inviteeUri: new_invitee,
              communityId: communityObjectId,
              learnerObjectId,
              learnerId: null,
            });
          } else {
            booking = await registerBookingEvent({
              email,
              eventUri,
              inviteeUri,
              communityId: communityObjectId,
              learnerObjectId,
              learnerId: null,
            });
          }
          break;
        case CALENDLY_EVENT_TYPES.CANCELED:
          booking = await cancelBookingEventMobile({
            learnerObjectId,
            communityId: communityObjectId,
            cancelReason: cancellation?.reason,
          });
          break;
        default:
          logger.error('Invalid calendly webhook payload!');
          break;
      }
      logger.info('booking in calendly webhook: ', booking);
      return booking;
    }
  } catch (err) {
    // console.log(err);
    logger.error('Error in calendly webhook due to: ', err);
    return null;
  }
};

module.exports = {
  registerBookingEvent,
  cancelBookingEvent,
  calendlyWebhookListener,
  getLatestBookingForLearner,
};
