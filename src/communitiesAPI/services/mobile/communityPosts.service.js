const {
  getPostWithAuthor,
  getCommunityAnnouncementsWithAuthors,
  getCommunityPinnedAnnouncementsWithAuthors,
} = require('../common/communityPosts.service');
const logger = require('../../../services/logger.service');

const MobileAnnouncementsNodeWrapper = require('../../utils/mobileHTMLnodes/mobileHTMLnodes');

const getCommunityPostsHtml = async (
  communityId,
  learnerObjectId,
  paginate,
  pageNum,
  pageSize
) => {
  let results;
  try {
    results = await getCommunityAnnouncementsWithAuthors(
      communityId,
      learnerObjectId,
      paginate,
      pageNum,
      pageSize
    );
  } catch (err) {
    logger.error('getCommunityPostsHtml error', err, err.stack);
    throw new Error(
      `Something went wrong while getting posts popualted with authors: ${err}`
    );
  }
  const resultData = paginate === 1 ? results?.data : results;
  try {
    resultData.forEach((post) => {
      if (post.content) {
        const rootContent = {
          ...post?.content?.root,
        };
        const htmlGen = new MobileAnnouncementsNodeWrapper(rootContent);
        const htmlData = htmlGen.getHTML();
        // eslint-disable-next-line no-param-reassign
        post.html = htmlData;
      }
    });
  } catch (err) {
    logger.error('getCommunityPostsHtml error', err, err.stack);
    throw new Error(
      `Something went wrong while generated HTML for post content ${err}`
    );
  }
  if (paginate === 1) {
    results.data = resultData;
  } else {
    results = resultData;
  }
  return results;
};

const getPinnedCommunityPostsHtml = async (
  communityId,
  learnerObjectId,
  paginate,
  pageNum,
  pageSize
) => {
  let results;
  try {
    results = await getCommunityPinnedAnnouncementsWithAuthors(
      communityId,
      learnerObjectId,
      paginate,
      pageNum,
      pageSize
    );
  } catch (err) {
    logger.error('getPinnedCommunityPostsHtml error', err, err.stack);
    throw new Error(
      `Something went wrong while getting posts popualted with authors: ${err}`
    );
  }
  const resultData = paginate === 1 ? results?.data : results;
  try {
    resultData.forEach((post) => {
      if (post.content) {
        const rootContent = {
          ...post?.content?.root,
        };
        const htmlGen = new MobileAnnouncementsNodeWrapper(rootContent);
        const htmlData = htmlGen.getHTML();
        // eslint-disable-next-line no-param-reassign
        post.html = htmlData;
      }
    });
  } catch (err) {
    logger.error('getPinnedCommunityPostsHtml error', err, err.stack);
    throw new Error(
      `Something went wrong while generated HTML for post content ${err}`
    );
  }
  if (paginate === 1) {
    results.data = resultData;
  } else {
    results = resultData;
  }
  return results;
};

const getCommunityPostHtml = async (postId) => {
  let result;
  try {
    result = await getPostWithAuthor(postId);
  } catch (err) {
    logger.error('getCommunityPostHtml error', err, err.stack);
    throw new Error(
      `Something went wrong while getting post(${postId})popualted with author: ${err}`
    );
  }
  try {
    if (result?.content) {
      const rootContent = {
        ...result?.content?.root,
      };
      const htmlGen = new MobileAnnouncementsNodeWrapper(rootContent);
      const htmlData = htmlGen.getHTML();
      result.html = htmlData;
    }
  } catch (err) {
    logger.error('getCommunityPostHtml error', err, err.stack);
    throw new Error(
      `Something went wrong while generated HTML for post content ${err}`
    );
  }

  return result;
};

/**
 * Updates an existing post
 *
 * @param {Object} params
 * @param {String} id
 * @returns {Object}
 */
const updatePost = async (id, data = {}) => {
  try {
    const updatedDocument = await Posts.findOneAndUpdate(
      { _id: id },
      { ...data },
      { new: true }
    );
    logger.info('Updated Announcement: ', updatedDocument);
    return updatedDocument;
  } catch (err) {
    logger.error(err.message);
    throw err;
  }
};

module.exports = {
  getCommunityPostHtml,
  getCommunityPostsHtml,
  getPinnedCommunityPostsHtml,
  updatePost,
};
