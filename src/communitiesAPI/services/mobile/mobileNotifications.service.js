// models
const {
  getMobileNotifications,
  bulkUpdateNotifications,
  updateScheduledNotification,
  sendMobileNotification,
  scheduleMobileNotification,
} = require('../../../services/notification/mobileNotifications.service');

const { DateTime } = require('luxon');
const {
  MOBILE_NOTIFICATION_TYPES,
  MO<PERSON>LE_NOTIFICATION_RECIPIENTS_SCOPES,
} = require('../../../constants/common');

const logger = require('../../../services/logger.service');

const getUserMobileNotifications = async (
  userId,
  paginate = 0,
  pageNum = null,
  pageSize = null,
  communityCode = null,
  communityId = null
) => {
  try {
    const users = await getMobileNotifications(
      userId,
      paginate,
      pageNum,
      pageSize,
      communityCode,
      communityId
    );
    return users;
  } catch (err) {
    logger.error(
      'Something went wrong while getting user mobile notifications: ',
      err
    );
    throw err;
  }
};

const formatNotifications = (userNotifications) => {
  const response = userNotifications.map((notification) => {
    const schedule = notification?.schedule;
    if (schedule) schedule.scheduleId = schedule?._id;
    delete schedule?._id;
    delete schedule?.notification?._id;
    const template = notification?.template;
    if (template) template.templateId = template?._id;
    delete template?._id;

    return {
      _id: notification?._id,
      seen: notification?.seen,
      status: notification?.status,
      sentDate: notification?.sentDate,
      userId: notification?.userId,
      data: notification?.data,

      // Schedule fields (possible dynamic or user-customized) will override template fields
      ...template,
      ...schedule?.notification,
    };
  });
  return response;
};

const setNotificationsSeen = async (userId, notificationIds) => {
  try {
    const response = await bulkUpdateNotifications(
      userId,
      notificationIds,
      {
        seen: true,
      }
    );
    return response;
  } catch (err) {
    logger.error(
      `Something went wrong while setting notifications as seen for user: ${err}`
    );
    throw err;
  }
};

const updateEventScheduledNotifications = async (
  eventObjectId,
  startTime
) => {
  try {
    const filterObject = {
      'notification.eventObjectId': eventObjectId,
      status: 'Pending',
    };
    const startTimeDateTime = DateTime.fromISO(startTime.toISOString());

    await updateScheduledNotification(
      {
        ...filterObject,
        'notification.type': MOBILE_NOTIFICATION_TYPES.EVENT_STARTING_NOW,
      },
      {
        dueDate: startTimeDateTime,
      }
    );

    await updateScheduledNotification(
      {
        ...filterObject,
        'notification.type':
          MOBILE_NOTIFICATION_TYPES.EVENT_STARTING_10_MINUTES,
      },
      {
        dueDate: startTimeDateTime.minus({ minutes: 10 }),
      }
    );

    await updateScheduledNotification(
      {
        ...filterObject,
        'notification.type':
          MOBILE_NOTIFICATION_TYPES.EVENT_STARTING_3_HOURS,
      },
      {
        dueDate: startTimeDateTime.minus({ hours: 3 }),
      }
    );
  } catch (err) {
    logger.error(
      `Something went wrong while updated scheduled notification: ${err}`
    );
    throw err;
  }
};

// Helper function to send/schedule all event-related notifcations
const createEventNotifications = async (params) => {
  const {
    communityTitle,
    communityCode,
    eventObjectId,
    userIds,
    startTimeDate,
    customNotificationTitle,
    customNotificationBody,
    title,
  } = params;

  const eventStartTimeFormatted = startTimeDate?.toFormat('cccc, LLLL d');
  // Sending immediate mobile push notification
  await sendMobileNotification(
    MOBILE_NOTIFICATION_TYPES.EVENT_ADDED,
    userIds,
    MOBILE_NOTIFICATION_RECIPIENTS_SCOPES.GLOBAL,
    {
      eventObjectId,
      communityCode,
      title: `New event on ${eventStartTimeFormatted}!`,
      body: `Join ${title} with ${communityTitle}! Find out more.`,
      ...(customNotificationTitle && { title: customNotificationTitle }),
      ...(customNotificationBody && { body: customNotificationBody }),
    }
  );

  // Schedule event starting now, in 10 minutes and in 3 hours notifications
  await scheduleMobileNotification({
    dueDate: startTimeDate,
    recurring: false,
    notification: {
      type: MOBILE_NOTIFICATION_TYPES.EVENT_STARTING_NOW,
      title: `We're starting! Join now`,
      body: `Join us for ${title} - click for details.`,
      communityCode,
      eventObjectId,
    },
    recipients: { type: MOBILE_NOTIFICATION_RECIPIENTS_SCOPES.GLOBAL },
  });

  await scheduleMobileNotification({
    dueDate: startTimeDate.minus({ minutes: 10 }),
    recurring: false,
    notification: {
      type: MOBILE_NOTIFICATION_TYPES.EVENT_STARTING_10_MINUTES,
      title: `Reminder: We're starting in 10 minutes!`,
      body: `Get ready to join ${title} with ${communityTitle}`,
      communityCode,
      eventObjectId,
    },
    recipients: { type: MOBILE_NOTIFICATION_RECIPIENTS_SCOPES.GLOBAL },
  });

  await scheduleMobileNotification({
    dueDate: startTimeDate.minus({ hours: 3 }),
    recurring: false,
    notification: {
      type: MOBILE_NOTIFICATION_TYPES.EVENT_STARTING_3_HOURS,
      title: `Reminder: We're starting in 3 hours!`,
      body: `${communityTitle} is hosting ${title} soon! Learn more.`,
      communityCode,
      eventObjectId,
    },
    recipients: { type: MOBILE_NOTIFICATION_RECIPIENTS_SCOPES.GLOBAL },
  });
};

module.exports = {
  getUserMobileNotifications,
  formatNotifications,
  setNotificationsSeen,
  updateEventScheduledNotifications,
  createEventNotifications,
};
