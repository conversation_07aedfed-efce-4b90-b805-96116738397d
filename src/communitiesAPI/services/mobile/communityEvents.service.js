// models
const Events = require('../../models/communityEvents.model');
const EventAttendees = require('../../models/eventAttendees.model');
const Learner = require('../../../models/learners.model');

// services
const logger = require('../../../services/logger.service');
const { sendEmail } = require('../../../services/notification/index');
const { DateTime } = require('luxon');
const {
  getUpcomingEventsByCommunityId,
  getCommunityEvents,
  getPastEventsByCommunityId,
  registerForEvent,
  getCommunityEventByEventId,
  getEventById,
  getEventByIdForAdmin,
} = require('../common/communityEvents.service');
const { dateTimesAreOnSameDay } = require('../../utils');
const ObjectId = require('mongoose').Types.ObjectId;

const getAttendingCommunityEvents = (upcomingEvents) => {
  return upcomingEvents.filter((event) => event.registered === true);
};

// This parses the events of how mobile FE expects
// Special Titles are not the month of the event, for example (Today, Tomorrow)
const getTitledCommunityEvents = (events, specialTitles = {}) => {
  let titleToEventsMap = {};
  const now = DateTime.utc();
  const tomorrow = now.plus({ days: 1 });
  events.forEach((event) => {
    const startDate = DateTime.fromJSDate(event.startTime);
    const month = startDate.monthLong;
    if (specialTitles.today && dateTimesAreOnSameDay(now, startDate)) {
      if (!titleToEventsMap['Today']) titleToEventsMap['Today'] = [];
      titleToEventsMap['Today'].push(event);
    } else if (
      specialTitles.tomorrow &&
      dateTimesAreOnSameDay(tomorrow, startDate)
    ) {
      if (!titleToEventsMap['Tomorrow']) titleToEventsMap['Tomorrow'] = [];
      titleToEventsMap['Tomorrow'].push(event);
    } else {
      if (!titleToEventsMap[month]) titleToEventsMap[month] = [];
      titleToEventsMap[month].push(event);
    }
  });
  let result = [];
  for (const [key, value] of Object.entries(titleToEventsMap)) {
    result.push({ title: key, sessions: value });
  }
  return result;
};

module.exports = {
  getCommunityEvents,
  getUpcomingEventsByCommunityId,
  getPastEventsByCommunityId,
  registerForEvent,
  getAttendingCommunityEvents,
  getTitledCommunityEvents,
  getCommunityEventByEventId,
  getEventById,
  getEventByIdForAdmin,
};
