// models
const Community = require('../../models/community.model');
const CommunitySubscription = require('../../models/communitySubscriptions.model');

// services
const logger = require('../../../services/logger.service');
const {
  communityEnrolmentStatuses,
  COMMUNITY_SUBSCRIPTION_STATUSES,
} = require('../../../constants/common');

const {
  getFeaturedCommunityMembers,
} = require('./communitySubscriptions.service');

// common service functions
const {
  getLearnersCommunityDetails,
  getCommunityCodesByIds,
  getCommunitiesByIds,
  updateOneCommunity,
  getCommunityBotDetails,
  getCommunityById,
} = require('../common/community.service');
const {
  isLearnerSubscribedToCommunity,
} = require('../common/communitySubscriptions.service');
const { getCommunityVideoPreviews } = require('./communityVideos.service');

const {
  getUpcomingEventsByCommunityId,
} = require('../common/communityEvents.service');
const {
  communityEventsService,
  communityFoldersService,
} = require('../web');

const getCommunityDetailsByCodeMobile = async (
  activeCommunityCode,
  learnerObjectId
) => {
  try {
    const communityLimitMobile = 5; // number of items for mobile community display.
    if (activeCommunityCode) {
      const communityQueryResponse = await Community.aggregate([
        {
          $match: {
            code: activeCommunityCode,
          },
        },
        {
          $lookup: {
            from: 'trainers',
            localField: 'trainerId',
            foreignField: '_id',
            as: 'trainer',
          },
        },
        {
          $unwind: {
            path: '$trainer',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $project: {
            title: 1,
            By: 1,
            thumbnailImgData: 1,
            featuredMemberIds: 1,
            platforms: 1,
            trainer: 1,
            link: 1,
            fullScreenBannerImgData: 1,
            isWhatsappExperienceCommunity: 1,
            restrictedInfo: 1,
          },
        },
      ]);

      const community = communityQueryResponse[0];

      if (!community) {
        throw new Error(
          `something went wrong, active community code ${activeCommunityCode} maybe incorrect`
        );
      }

      const { _id: communityObjectId, featuredMemberIds } = community;

      const [
        pendingApplications,
        upcomingEvents,
        communityVideos,
        featuredMembers,
      ] = await Promise.all([
        CommunitySubscription.countDocuments({
          communityCode: activeCommunityCode,
          status: COMMUNITY_SUBSCRIPTION_STATUSES.PENDING,
        }),
        getUpcomingEventsByCommunityId(learnerObjectId, communityObjectId),
        getCommunityVideoPreviews(communityObjectId),
        getFeaturedCommunityMembers(featuredMemberIds),
      ]);

      const result = {
        ...community,
        pendingApplications,
        upcomingEvents: upcomingEvents.slice(0, communityLimitMobile),
        featuredMembers,
        communityVideos: communityVideos.slice(0, communityLimitMobile),
      };

      return result;
    }
    return null;
  } catch (err) {
    logger.error(err.message);
    throw err;
  }
};

const getLearnerCommunities = async (
  learnerId,
  learnerObjectId,
  userObjectId,
  params = {}
) => {
  try {
    const result = {
      communities: [],
      activeCommunity: [],
    };
    let activeCommunityCode = params?.activeCommunity;
    const activeCommunityId = params?.activeCommunityId;

    const communityDetails = await getLearnersCommunityDetails({
      learnerId,
      communityEnrolmentStatuses,
      learnerObjectId,
      activeCommunityCode,
      activeCommunityId,
      userObjectId,
    });

    const storeExtraMetadataInCommunity =
      communityDetails.sortedCommunities.map(async (community) => {
        try {
          const { _id: communityId } = community;
          const [hasPaidEvents, hasPaidFolders] = await Promise.all([
            await communityEventsService.doesCommunityHavePaidEvents(
              communityId
            ),
            await communityFoldersService.doesCommunityHavePaidFolders(
              communityId
            ),
          ]);
          return {
            ...community,
            hasPaidEvents,
            hasPaidFolders,
          };
        } catch (error) {
          // no error in here as we don't want to stop the execution of the loop
          return community;
        }
      });

    communityDetails.sortedCommunities = await Promise.all(
      storeExtraMetadataInCommunity
    );
    result['communities'] = communityDetails.sortedCommunities;

    if (!activeCommunityId) {
      activeCommunityCode = communityDetails.sortedCommunities?.[0]?.code;
    }

    if (!activeCommunityCode) {
      activeCommunityCode = communityDetails.sortedCommunities?.find(
        ({ _id }) => _id.toString() === activeCommunityId
      )?.code;
    }

    const subscribedToCommunity = await isLearnerSubscribedToCommunity({
      learnerId,
      activeCommunityCode,
    });
    if (subscribedToCommunity) {
      result['activeCommunity'] = await getCommunityDetailsByCodeMobile(
        activeCommunityCode,
        learnerObjectId
      );
    }
    return result;
  } catch (err) {
    logger.error('getLearnerCommunities error', err, err.stack);
    throw err;
  }
};

module.exports = {
  getCommunityBotDetails,
  getLearnerCommunities,
  getCommunityCodesByIds,
  getCommunitiesByIds,
  updateOneCommunity,
  getCommunityById,
};
