// models
const communityTodos = require('../../models/communityTodos.model');
const communityTodosProgress = require('../../models/communityTodosProgress.model');
const Community = require('../../models/community.model');

const ObjectId = require('mongoose').Types.ObjectId;
const {
  communityTodoModesMap,
  communityTodoTypesMap,
} = require('../../constants');
const {
  decorateVideoTodoTask,
  decorateEventTodoTask,
  decoratePlatformTodoTask,
} = require('../../helpers/communityTodos.helper');
//services
const logger = require('../../../services/logger.service');
const { DateTime } = require('luxon');

const {
  scheduleMobileNotification,
} = require('../../../services/notification/mobileNotifications.service');

const getOrientation = async (communityId, learnerId) => {
  try {
    const results = await communityTodos.aggregate([
      {
        $match: {
          communityId: new ObjectId(communityId),
          mode: communityTodoModesMap.OVERALL,
          isActive: true,
        },
      },
      {
        $lookup: {
          from: 'communities',
          localField: 'communityId',
          foreignField: '_id',
          as: 'community',
        },
      },
      {
        $unwind: {
          path: '$community',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: 'community_todos_progress',
          let: {
            todo: '$_id',
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ['$todo', '$$todo'],
                    },
                    {
                      $eq: ['$learnerId', learnerId],
                    },
                  ],
                },
              },
            },
          ],
          as: 'userProgress',
        },
      },
      {
        $unwind: {
          path: '$userProgress',
          preserveNullAndEmptyArrays: true,
        },
      },
    ]);

    const orientation = await Promise.all(
      results.map(async (result) => {
        let todo = { ...result };
        // compute subunitOfferType
        let task = todo?.task;
        const type = result?.type;
        switch (type) {
          case communityTodoTypesMap.VIDEO:
            todo = await decorateVideoTodoTask(todo, task?.videoObjectId);
            break;
          case communityTodoTypesMap.EVENT:
            // specific event ID is given
            if (task.eventObjectId)
              todo = await decorateEventTodoTask(
                todo,
                task?.eventObjectId
              );

            break;
          case communityTodoTypesMap.PLATFORM:
            if (todo.community?.platforms)
              todo = await decoratePlatformTodoTask(
                todo,
                todo?.community?.platforms,
                task?.platform
              );
            break;

          // Cases with defiend front end behavior and no specific task data.
          case communityTodoTypesMap.PROFILE:
            break;
          case communityTodoTypesMap.MEMBERS:
            break;
          case communityTodoTypesMap.LIBRARY:
            break;
          default:
            logger.error('Invalid Todo Type');
            break;
        }

        todo.completed = todo?.userProgress?.completed ?? false;
        delete todo.community;
        return todo;
      })
    );

    return orientation;
  } catch (err) {
    logger.error(
      `Something went wrong while getting orientation for learner ${learnerId} for community ${communityId}: ${err}`
    );
  }
};

const updateOrCreateProgress = async (todoId, learnerId, params = {}) => {
  try {
    const previousProgress = await communityTodosProgress.findOne({
      todo: todoId,
      learnerId,
    });
    if (!previousProgress) {
      const result = await communityTodosProgress.create({
        todo: todoId,
        learnerId,
        ...params,
      });
      return result;
    }

    const keys = Object.keys({ ...params });
    for (i = 0; i < keys.length; i++) {
      previousProgress[keys[i]] = params[keys[i]];
    }

    await previousProgress.save();
    return previousProgress;
  } catch (err) {
    logger.error(
      `Something went wrong while updating or ceating todo progress for a user: ${err}`
    );
    throw err;
  }
};

const scheduleTodoMobileNotification = async (userId, communityId) => {
  const community = await Community.findOne({ _id: communityId });
  const communityCode = community.code;
  const recurringDetails = { hours: 48 };
  const dueDate = DateTime.utc().plus(recurringDetails);
  const doc = {
    dueDate,
    recurring: true,
    recurringDetails,
    notification: {
      type: 'completeTodos',
      communityCode,
    },
    recipients: { type: 'single', userId },
  };

  const response = await scheduleMobileNotification(doc);
  return response;
};

module.exports = {
  getOrientation,
  updateOrCreateProgress,
  scheduleTodoMobileNotification,
};
