const yup = require('yup');

const {
  EMBEDDED_VIDEO_PLATFORMS,
  communityFolderItemTypesMap,
} = require('../constants');

const createFolderItemSchema = yup.object().shape({
  index: yup.number().notRequired(),
  communityObjectId: yup.string().trim().required(),
  type: yup.string().trim().required(),
  title: yup.string().trim().required(),
  description: yup.string().trim().notRequired().nullable(),
  link: yup.string().when('type', {
    is: (val) =>
      [
        communityFolderItemTypesMap.EMBEDDED_VIDEO,
        communityFolderItemTypesMap.LINK,
      ].includes(val),
    then: yup.string().trim().required(),
    otherwise: yup.string().trim().notRequired().nullable(),
  }),
  icon: yup.string().trim().notRequired().nullable(),
  thumbnail: yup.string().trim().notRequired().nullable(),
  communityFolderObjectId: yup.string().trim().required(),
  videoObjectId: yup.string().trim().notRequired().nullable(),
  parentSectionId: yup.string().trim().notRequired(),
  folderType: yup.string().trim().notRequired(),
  isCoverVideo: yup.boolean().notRequired(),
  platform: yup
    .string()
    .oneOf(Object.values(EMBEDDED_VIDEO_PLATFORMS))
    .when('type', {
      is: (val) => val === communityFolderItemTypesMap.EMBEDDED_VIDEO,
      then: yup.string().trim().required(),
      otherwise: yup.string().trim().notRequired(),
    }),
  duration: yup.number().notRequired(),
});

const updateFolderItemSchema = yup.object().shape({
  index: yup.number().notRequired(),
  communityObjectId: yup.string().trim().required(),
  type: yup.string().trim().required(),
  title: yup.string().trim().required(),
  description: yup.string().trim().notRequired().nullable(),
  link: yup.string().when('type', {
    is: (val) =>
      [
        communityFolderItemTypesMap.EMBEDDED_VIDEO,
        communityFolderItemTypesMap.LINK,
      ].includes(val),
    then: yup.string().trim().required(),
    otherwise: yup.string().trim().notRequired().nullable(),
  }),
  icon: yup.string().trim().notRequired().nullable(),
  thumbnail: yup.string().trim().notRequired().nullable(),
  videoObjectId: yup.string().trim().notRequired().nullable(),
  folderType: yup.string().trim().notRequired(),
  platform: yup
    .string()
    .oneOf(Object.values(EMBEDDED_VIDEO_PLATFORMS))
    .when('type', {
      is: (val) => val === communityFolderItemTypesMap.EMBEDDED_VIDEO,
      then: yup.string().trim().required(),
      otherwise: yup.string().trim().notRequired(),
    }),
  duration: yup.number().notRequired(),
});

module.exports = {
  createFolderItemSchema,
  updateFolderItemSchema,
};
