const yup = require('yup');
const { communityLibraryStatusMap } = require('../constants');
const {
  coverMediaItemsSchema,
} = require('../../validations/coverMediaItems.validation');

const createFolderSchema = yup.object().shape({
  templateLibraryId: yup.string().notRequired(),
  index: yup.number().notRequired(),
  communityObjectId: yup.string().trim().required(),
  type: yup.string().trim().required(),
  title: yup.string().trim().required(),
  description: yup.string().trim().notRequired(),
  tags: yup.array().of(yup.string()).notRequired(),
  thumbnail: yup.string().trim().notRequired(), // legacy
  coverMediaItems: coverMediaItemsSchema,
  emoji: yup.string().trim().notRequired(),
  videoCount: yup.number().notRequired(),
  otherFolderItemCount: yup.number().notRequired(),
  isDraft: yup.string().trim().notRequired(),
  status: yup.string().trim().notRequired(),
  discountsToAdd: yup.array().default([]),
  discountsToRemove: yup.array().default([]),
  discountsToDisable: yup.array().default([]),
  newDiscountsToApply: yup.array().default([]),
  templateId: yup.string().trim().notRequired(),
});

const patchFolderSchema = yup.object().shape({
  status: yup
    .string()
    .trim()
    .required()
    .oneOf([
      communityLibraryStatusMap.PUBLISHED,
      communityLibraryStatusMap.UNPUBLISHED,
    ]),
});

module.exports = {
  createFolderSchema,
  patchFolderSchema,
};
