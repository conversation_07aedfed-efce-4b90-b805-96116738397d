const aws = require('aws-sdk');
const multer = require('multer');
const multerS3 = require('multer-s3');
const status = require('http-status');
const path = require('path');
const logger = require('../../services/logger.service');

const {
  awsRegion,
  awsSecretKey,
  awsAccessKey,
  awsAccessLevel,
} = require('../../config');
const {
  VIDEOASSETBUCKET,
  FILEASSETBUCKET,
} = require('../../constants/common');
const { communityFolderItemTypesMap } = require('../constants');
const Video = require('../../models/videos.model');
const {
  createFolderItemSchema,
} = require('../apiSchemas/communityFolderItems.schema');

const nasIOFolderItemFileBucketPath = (communityId, communityFolderId) =>
  `nasIO/portal/folder-item/file/${communityId}/${communityFolderId}`;

const nasIOFolderItemImageBucketPath = (communityId, communityFolderId) =>
  `nasIO/portal/folder-item/image/${communityId}/${communityFolderId}`;

const nasIOFolderItemAudioBucketPath = (communityId, communityFolderId) =>
  `nasIO/portal/folder-item/audio/${communityId}/${communityFolderId}`;

const nasIOFolderItemVideoBucketPath = (communityId, communityFolderId) =>
  `nasIO/portal/folder-item/${communityId}/${communityFolderId}`;

const config = awsSecretKey
  ? {
      secretAccessKey: awsSecretKey,
      accessKeyId: awsAccessKey,
    }
  : { credentialProvider: new aws.CredentialProviderChain() };

aws.config.update({
  region: awsRegion,
  ...config,
});

const s3 = new aws.S3();

const folderItemValidationFilter = async (req, file, cb) => {
  const input = { ...req.body };
  const { communityId, communityFolderId } = req.params;
  const mimeTypeVideo = ['video/mp4'];
  const mimeTypeFile = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  ];
  const { mimetype, originalname } = file;
  input.communityObjectId = communityId;
  input.communityFolderObjectId = communityFolderId;
  const isQueryValid = await createFolderItemSchema.isValid(input);
  const fileName = originalname.replace(/\s/g, '-');

  if (!isQueryValid) {
    logger.info('Error creating folder item due to Invalid Parameters');
    const error = new Error('Invalid Parameters');
    error.status = status.BAD_REQUEST;
    return cb(error);
  }

  const body = createFolderItemSchema.cast(input);

  if (body.type === communityFolderItemTypesMap.VIDEO)
    if (mimeTypeVideo.indexOf(mimetype) !== false) {
      const { _id: videoId } = await Video.create({
        userObjectId: req.user._id,
        createdBy: req.user.email,
      });
      const s3Path = nasIOFolderItemVideoBucketPath(
        communityId,
        communityFolderId
      );
      req.body = {
        ...body,
        path: s3Path,
        bucket: VIDEOASSETBUCKET,
        fileName,
        key: `${s3Path}/${videoId.toString()}${path.extname(fileName)}`,
        videoObjectId: videoId,
      };
      cb(null, true);
    } else {
      const err = new Error(
        'Invalid file type. It has to be a mp4, mov, avchd, mkv, avi, wmv file'
      );
      err.status = status.BAD_REQUEST;
      cb(err, false);
    }
  else if (body.type === communityFolderItemTypesMap.IMAGE) {
    if (mimeTypeFile.indexOf(mimetype) !== false) {
      const s3Path = nasIOFolderItemImageBucketPath(
        communityId,
        communityFolderId
      );
      req.body = {
        ...body,
        path: s3Path,
        bucket: FILEASSETBUCKET,
        fileName,
        key: `${s3Path}/${Date.now().toString()}-${fileName}`,
      };
      cb(null, true);
    } else {
      const err = new Error(
        'Invalid file type. It has to be a jpeg, jpg, png file'
      );
      err.status = status.BAD_REQUEST;
      cb(err, false);
    }
  } else if (body.type === communityFolderItemTypesMap.AUDIO) {
    if (mimeTypeFile.indexOf(mimetype) !== false) {
      const s3Path = nasIOFolderItemAudioBucketPath(
        communityId,
        communityFolderId
      );
      req.body = {
        ...body,
        path: s3Path,
        bucket: FILEASSETBUCKET,
        fileName,
        key: `${s3Path}/${Date.now().toString()}-${fileName}`,
      };
      cb(null, true);
    } else {
      const err = new Error('Invalid file type. It has to be an mp3 file');
      err.status = status.BAD_REQUEST;
      cb(err, false);
    }
  } else if (
    ![
      communityFolderItemTypesMap.AUDIO,
      communityFolderItemTypesMap.IMG,
      communityFolderItemTypesMap.VIDEO,
    ].includes(body.type)
  ) {
    if (mimeTypeFile.indexOf(mimetype) !== false) {
      const s3Path = nasIOFolderItemFileBucketPath(
        communityId,
        communityFolderId
      );
      req.body = {
        ...body,
        path: s3Path,
        bucket: FILEASSETBUCKET,
        fileName,
        key: `${s3Path}/${Date.now().toString()}-${fileName}`,
      };
      cb(null, true);
    } else {
      const err = new Error(
        'Invalid file type. It has to be a pdf, doc, docx, xls, xlsx file'
      );
      err.status = status.BAD_REQUEST;
      cb(err, false);
    }
  }
};
const MAX_FILE_UPLOAD_SIZE = 2 * 1024 * 1024 * 1024; // 2Gb

const uploadCommunityFolderItem = multer({
  limits: { fileSize: MAX_FILE_UPLOAD_SIZE },
  fileFilter: folderItemValidationFilter,
  onError(err, next) {
    logger.info(`Multer S3 upload info : ${JSON.stringify(err)}`);
    // eslint-disable-next-line no-param-reassign
    err.status = status.BAD_REQUEST;
    next(err);
  },
  storage: multerS3({
    s3,
    bucket: (req, file, cb) => {
      cb(null, req.body.bucket);
    },
    contentType: (req, file, cb) => {
      cb(null, file.mimetype);
    },
    metadata: (req, file, cb) => {
      cb(null, {
        fileOriginalName: String(file.originalname),
        ...(req.body.videoObjectId
          ? {
              videoObjectId: req.body.videoObjectId.toString(),
            }
          : {}),
      });
    },
    key: (req, file, cb) => {
      cb(null, req.body.key);
    },
  }),
});

module.exports = {
  uploadCommunityFolderItem,
};
