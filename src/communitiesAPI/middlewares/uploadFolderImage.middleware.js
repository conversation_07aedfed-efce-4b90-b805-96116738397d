const aws = require('aws-sdk');
const multer = require('multer');
const multerS3 = require('multer-s3');
const status = require('http-status');
const logger = require('../../services/logger.service');
const {
  awsRegion,
  awsSecretKey,
  awsAccessKey,
  awsAccessLevel,
} = require('../../config');
const { IMAGEASSETBUCKET } = require('../../constants/common');
const { communityFolderTypesMap } = require('../constants');
const {
  createFolderSchema,
} = require('../apiSchemas/communityFolders.schema');

const nasIOBucketPath = (communityId) =>
  `nasIO/portal/folder/image/${communityId}`;

const config = awsSecretKey
  ? {
      secretAccessKey: awsSecretKey,
      accessKeyId: awsAccessKey,
    }
  : { credentialProvider: new aws.CredentialProviderChain() };

aws.config.update({
  region: awsRegion,
  ...config,
});

const s3 = new aws.S3();

const requestValidation = async (req, file, cb) => {
  const input = req.body;
  const { communityId } = req.params;
  input.communityObjectId = communityId;
  const isQueryValid = await createFolderSchema.isValid(input);

  if (!isQueryValid) {
    logger.info('Error creating folder due to Invalid Parameters');
    const error = new Error('Invalid Parameters');
    error.status = status.BAD_REQUEST;
    return cb(error);
  }

  const body = createFolderSchema.cast(input);

  if (body.type === communityFolderTypesMap.COLLECTION && !body.emoji) {
    logger.info(
      'Error creating folder due to Invalid Parameters. Folder of type = collection needs to have an emoji specified'
    );
    const error = new Error(
      'Invalid Parameters. Emoji has to be provided for type = collection'
    );
    error.status = status.BAD_REQUEST;
    return cb(error);
  }

  if (
    body.type === communityFolderTypesMap.DIGITAL_PRODUCT &&
    !body.thumbnail &&
    !file
  ) {
    logger.info(
      'Error creating folder due to Invalid Parameters. Folder of type = class needs to have either thumbnail or image specified'
    );
    const error = new Error(
      'Invalid Parameters. Either thumbnail or image has to be provided for type = class'
    );
    error.status = status.BAD_REQUEST;
    return cb(error);
  }

  if (
    file.mimetype === 'image/jpeg' ||
    file.mimetype === 'image/png' ||
    file.mimetype === 'image/webp'
  ) {
    body.path = nasIOBucketPath(req.params.communityId);
    body.bucket = IMAGEASSETBUCKET;
    body.fileName = file.originalname.replace(/\s/g, '-');
    body.key = `${body.path}/${Date.now().toString()}-${body.fileName}`;
    req.body = body;
    cb(null, true);
  } else {
    const err = new Error(
      'Invalid file type. It has to be a jpeg,png or webp file'
    );
    err.status = status.BAD_REQUEST;
    cb(err, false);
  }
};

const uploadCommunityFolderImage = multer({
  // limits: { fileSize: thresholdFileSize },
  fileFilter: requestValidation,

  onError(err, next) {
    // eslint-disable-next-line no-param-reassign
    err.status = status.BAD_REQUEST;
    next(err);
  },
  storage: multerS3({
    s3,
    bucket: (req, file, cb) => {
      cb(null, req.body.bucket);
    },
    contentType: (req, file, cb) => {
      cb(null, file.mimetype);
    },
    dest: (req, file, cb) => {
      cb(null, req.body.path);
    },
    acl: awsAccessLevel,
    metadata: (req, file, cb) => {
      cb(null, { fileOriginalName: String(file.originalname) });
    },
    key: (req, file, cb) => {
      cb(null, req.body.key);
    },
  }),
});

module.exports = {
  uploadCommunityFolderImage,
};
