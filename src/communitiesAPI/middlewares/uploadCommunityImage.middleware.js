const aws = require('aws-sdk');
const multer = require('multer');
const multerS3 = require('multer-s3');
const status = require('http-status');
const logger = require('../../services/logger.service');
const uuidv4 = require('uuid');
const {
  awsRegion,
  awsSecretKey,
  awsAccessKey,
  awsAccessLevel,
  env,
} = require('../../config');
const { IMAGEASSETBUCKET } = require('../../constants/common');
const {
  uploadCommunityImageAssetSchema,
} = require('../apiSchemas/community.schema');

const nasIOBucketPath = (communityLink) =>
  `nasIO/community-product-page/${communityLink}/${env}`;

const config = awsSecretKey
  ? {
      secretAccessKey: awsSecretKey,
      accessKeyId: awsAccessKey,
    }
  : { credentialProvider: new aws.CredentialProviderChain() };

aws.config.update({
  region: awsRegion,
  ...config,
});

const s3 = new aws.S3();

const requestValidation = async (req, file, cb) => {
  const input = req.body;
  const { communityId } = req.params;
  input.communityObjectId = communityId;
  const isQueryValid = await uploadCommunityImageAssetSchema.isValid(
    input
  );

  if (!isQueryValid) {
    logger.info(
      'Error uploading community image asset due to Invalid Parameters'
    );
    const error = new Error('Invalid Parameters');
    error.status = status.BAD_REQUEST;
    return cb(error);
  }

  input.link = input.link.replace('/', '');
  const body = uploadCommunityImageAssetSchema.cast(input);
  if (
    file.mimetype === 'image/jpeg' ||
    file.mimetype === 'image/png' ||
    file.mimetype === 'image/webp'
  ) {
    body.path = nasIOBucketPath(req.body.link);
    body.bucket = IMAGEASSETBUCKET;
    body.fileName = file.originalname.replace(/[^a-zA-Z0-9_-]+/, '-');
    body.key = `${body.path}/${Date.now().toString()}-${body.fileName}`;
    req.body = body;
    cb(null, true);
  } else {
    const err = new Error(
      'Invalid file type. It has to be a jpeg, png or webp file'
    );
    err.status = status.BAD_REQUEST;
    cb(err, false);
  }
};

const uploadCommunityImage = multer({
  // limits: { fileSize: thresholdFileSize },
  fileFilter: requestValidation,

  onError(err, next) {
    // eslint-disable-next-line no-param-reassign
    err.status = status.BAD_REQUEST;
    next(err);
  },
  storage: multerS3({
    s3,
    bucket: (req, file, cb) => {
      cb(null, req.body.bucket);
    },
    contentType: (req, file, cb) => {
      cb(null, file.mimetype);
    },
    dest: (req, file, cb) => {
      cb(null, req.body.path);
    },
    acl: awsAccessLevel,
    metadata: (req, file, cb) => {
      cb(null, {
        fileOriginalName: String(`${uuidv4.v4()}_community_image`),
      });
    },
    key: (req, file, cb) => {
      cb(null, req.body.key);
    },
  }),
});

module.exports = {
  uploadCommunityImage,
};
