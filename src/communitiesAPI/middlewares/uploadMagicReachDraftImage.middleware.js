const aws = require('aws-sdk');
const multer = require('multer');
const multerS3 = require('multer-s3');
const status = require('http-status');
const {
  awsRegion,
  awsSecretKey,
  awsAccessKey,
  awsAccessLevel,
} = require('../../config');
const { IMAGEASSETBUCKET } = require('../../constants/common');

const thresholdImageSize = 20000000;
const nasIOBucketPath = (communityId, draftId) =>
  `nasIO/portal/draft-email-images/${communityId}/${draftId}`;

const config = awsSecretKey
  ? {
      secretAccessKey: awsSecretKey,
      accessKeyId: awsAccessKey,
    }
  : { credentialProvider: new aws.CredentialProviderChain() };

aws.config.update({
  region: awsRegion,
  ...config,
});

const s3 = new aws.S3();

const fileFilter = (req, file, cb) => {
  if (
    file.mimetype === 'image/jpg' ||
    file.mimetype === 'image/jpeg' ||
    file.mimetype === 'image/png' ||
    file.mimetype === 'image/webp'
  ) {
    cb(null, true);
  } else {
    const err = new Error(
      'Invalid file type. It has to be a jpg, jpeg, png or webp file'
    );
    err.status = status.BAD_REQUEST;
    cb(err, false);
  }
};

const upload = () => {
  return multer({
    limits: { fileSize: thresholdImageSize },
    fileFilter: fileFilter,
    onError: function (err, next) {
      // eslint-disable-next-line no-param-reassign
      err.status = status.BAD_REQUEST;
      next(err);
    },
    storage: multerS3({
      s3: s3,
      bucket: IMAGEASSETBUCKET,
      contentType: (req, file, cb) => {
        cb(null, file.mimetype);
      },
      dest: (req, file, cb) => {
        cb(null, nasIOBucketPath(req.params.communityId, req.body.id));
      },
      acl: awsAccessLevel,
      metadata: (req, file, cb) => {
        cb(null, { name: String(file.originalname) });
      },
      key: (req, file, cb) => {
        cb(
          null,
          `${nasIOBucketPath(
            req.params.communityId,
            req.body.id
          )}/${Date.now().toString()}-${file.originalname}`
        );
      },
    }),
  });
};

const uploadMagicReachDraftImage = upload();

module.exports = {
  uploadMagicReachDraftImage,
};
