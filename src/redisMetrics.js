const httpContext = require('express-http-context');
const { performance } = require('perf_hooks');
const logger = require('./services/logger.service');
const {
  REDIS_REQUEST_DIMENSIONS,
  REDIS_REQUEST_METRICS,
} = require('./monitoring/constants');
const { getMetric } = require('./monitoring/metricRegistry');

function metricsCollectionWrapper(func, cluster, command) {
  return async (...args) => {
    try {
      // apiRoute only exists when postRoutePreHandlerMiddleware is provided.
      const requestApiPath =
        httpContext.get('apiRoute') ||
        httpContext.get('fullApiPath') ||
        'unknown';
      const preCommandLabels = {
        [REDIS_REQUEST_DIMENSIONS.COMMAND]: command,
        [REDIS_REQUEST_DIMENSIONS.REQUEST_API_PATH]: requestApiPath,
        [REDIS_REQUEST_DIMENSIONS.CLUSTER]: cluster,
      };

      const incomingCounter = getMetric(
        REDIS_REQUEST_METRICS.INCOMING_COUNTER
      );
      const processedCounter = getMetric(
        REDIS_REQUEST_METRICS.PROCESSED_COUNTER
      );
      const latencyHistogram = getMetric(
        REDIS_REQUEST_METRICS.REQUEST_LATENCY_HISTOGRAM
      );

      if (incomingCounter) {
        incomingCounter.labels(preCommandLabels).inc();
      }

      let redisErr;
      let result;
      const startTime = performance.now();

      try {
        result = await func(...args);
      } catch (err) {
        redisErr = err;
      }

      const duration = performance.now() - startTime;
      const postCommandLabels = {
        [REDIS_REQUEST_DIMENSIONS.COMMAND]: command,
        [REDIS_REQUEST_DIMENSIONS.REQUEST_API_PATH]: requestApiPath,
        [REDIS_REQUEST_DIMENSIONS.STATUS]: redisErr ? 'failed' : 'success',
        [REDIS_REQUEST_DIMENSIONS.CLUSTER]: cluster,
      };

      if (processedCounter) {
        processedCounter.labels(postCommandLabels).inc();
      }
      if (latencyHistogram) {
        latencyHistogram.labels(postCommandLabels).observe(duration);
      }
      if (redisErr) {
        throw redisErr;
      }

      return result;
    } catch (err) {
      logger.error('redis client record metrics error', err, err.stack);
      throw err;
    }
  };
}

module.exports = {
  metricsCollectionWrapper,
};
