const EbanxWhitelist = require('../../models/ebanxWhitelist.model');
const CommunityModel = require('../../communitiesAPI/models/community.model');
const {
  PURCHASE_TYPE,
  ADDON_PURCHASE_TYPES,
} = require('../../constants/common');
const { ENTITY_TYPE: PLAN_ENTITY_TYPE } = require('../plan/constants');
const logger = require('../logger.service');

// Simple constant flag to enable/disable feature
const EBANX_WHITELIST_ENABLED = true; // Change to false to disable

// Status constants
const WHITELIST_STATUS = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
};

/**
 * Extract productId from transaction based on purchase type
 * @param {Object} transaction - Transaction object (addon, subscription, or plan)
 * @param {String} purchaseType - Type of purchase (SUBSCRIPTION, EVENT, COURSE, etc.)
 * @returns {String|null} productId - The product identifier for whitelist check, null if error
 */
async function getProductIdFromTransaction(transaction, purchaseType) {
  try {
    if (!transaction) {
      logger.error('EBANX Whitelist: Transaction is required');
      return null;
    }

    switch (purchaseType) {
      // Community subscription - lookup community by code to get ObjectId
      case PURCHASE_TYPE.SUBSCRIPTION: {
        if (!transaction.community_code) {
          logger.error(
            'EBANX Whitelist: community_code not found in subscription transaction'
          );
          return null;
        }

        const community = await CommunityModel.findOne({
          code: transaction.community_code,
        }).lean();

        if (!community) {
          logger.error(
            `EBANX Whitelist: Community not found for code: ${transaction.community_code}`
          );
          return null;
        }

        return community._id;
      }
      // Individual products (addon transactions) - use entityObjectId
      case ADDON_PURCHASE_TYPES.EVENT:
      case ADDON_PURCHASE_TYPES.DIGITAL_FILES:
      case ADDON_PURCHASE_TYPES.COURSE:
      case ADDON_PURCHASE_TYPES.CHALLENGE:
      case ADDON_PURCHASE_TYPES.SESSION:
        if (!transaction.entityObjectId) {
          logger.error(
            'EBANX Whitelist: entityObjectId not found in addon transaction'
          );
          return null;
        }
        return transaction.entityObjectId;

      // Plan orders - use planObjectId
      default:
        if (Object.values(PLAN_ENTITY_TYPE).includes(purchaseType)) {
          if (!transaction.planObjectId) {
            logger.error(
              'EBANX Whitelist: planObjectId not found in plan transaction'
            );
            return null;
          }
          return transaction.planObjectId;
        }

        logger.error(
          `EBANX Whitelist: Unsupported purchase type: ${purchaseType}`
        );
        return null;
    }
  } catch (error) {
    logger.error(
      'EBANX Whitelist: Error in getProductIdFromTransaction:',
      error
    );
    return null;
  }
}

async function getWhitelistEntry(productId) {
  try {
    if (!productId) {
      return null;
    }
    return await EbanxWhitelist.findOne({ productId }).lean();
  } catch (error) {
    logger.error('EBANX Whitelist: Error getting whitelist entry:', error);
    return null;
  }
}

async function isProductWhitelisted(productId) {
  try {
    if (!EBANX_WHITELIST_ENABLED) {
      return false; // If disabled, no fraud bypass
    }

    if (!productId) {
      return false;
    }

    const entry = await getWhitelistEntry(productId);
    return !!(entry && entry.status === WHITELIST_STATUS.ACTIVE);
  } catch (error) {
    logger.error(
      'EBANX Whitelist: Error checking if product is whitelisted:',
      error
    );
    return false; // Default to not whitelisted on error
  }
}

module.exports = {
  getWhitelistEntry,
  isProductWhitelisted,
  getProductIdFromTransaction,
  WHITELIST_STATUS,
};
