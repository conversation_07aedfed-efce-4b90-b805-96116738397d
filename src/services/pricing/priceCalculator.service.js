const CommunityModel = require('../../communitiesAPI/models/community.model');
const { ParamError } = require('../../utils/error.util');
const {
  retrievePaymentFeeStructure,
} = require('../config/paymentFeeStructureConfig.service');
const feeService = require('../../communitiesAPI/services/common/fee.service');
const { PURCHASE_TYPE } = require('../../constants/common');

exports.calculatePrice = async ({
  communityObjectId,
  itemPrice,
  isPassOn = null,
  entityType,
}) => {
  const community = await CommunityModel.findOne({
    _id: communityObjectId,
    isActive: true,
  }).lean();

  if (!community) {
    throw new ParamError('Community not found');
  }

  const finalIsPassOn = isPassOn ?? community.passOnTakeRate;

  const isAddon = entityType !== PURCHASE_TYPE.SUBSCRIPTION;

  const localCurrency = community.baseCurrency;

  const paymentFeeStructure = await retrievePaymentFeeStructure({
    baseCurrency: community.baseCurrency,
    purchaseType: entityType,
    planType: community.config?.planType,
  });

  const effectivePayoutFeeConfig =
    feeService.retrieveEffectivePayoutFeeConfig(
      community.payoutFeeConfigs
    );

  const effectiveBasePayoutFeeConfig =
    feeService.retrieveEffectivePayoutFeeConfig(
      community.basePayoutFeeConfigs
    );

  const toCurrencyConversionRate = 1;

  const checkoutAmountBreakdown = feeService.getCheckoutAmountBreakdown({
    itemPrice,
    currency: localCurrency,
    communityCreatedAt: community.createdAt,
    isAddonPayment: isAddon,
    paymentFeeStructure,
    basePayoutFeeConfig: effectiveBasePayoutFeeConfig,
    customPayoutFeeConfig: effectivePayoutFeeConfig,
    passOnTakeRate: finalIsPassOn,
    passOnPaymentGatewayFee: finalIsPassOn,
    toCurrencyConversionRate,
    ignorePassOnCondition: true,
    communityCountry: community.countryCreatedIn,
    baseCurrency: community.baseCurrency,
  });

  const totalNasFeeAmount =
    checkoutAmountBreakdown.revenueShare +
    checkoutAmountBreakdown.gstOnRevenue;

  const totalPaymentProcessingFee =
    checkoutAmountBreakdown.gatewayFee +
    checkoutAmountBreakdown.gst +
    checkoutAmountBreakdown.processingFee;

  const receiveAmount =
    checkoutAmountBreakdown.checkoutAmount -
    totalNasFeeAmount -
    totalPaymentProcessingFee;

  const price = {
    itemPrice,
    currency: localCurrency,
    nasFee: {
      isPassOn: finalIsPassOn,
      amount: totalNasFeeAmount,
      gstAmount: checkoutAmountBreakdown.gstOnRevenue,
    },
    paymentProcessingFee: {
      isPassOn: finalIsPassOn,
      amount: totalPaymentProcessingFee,
      gstAmount: checkoutAmountBreakdown.gst,
    },
    checkoutAmount: checkoutAmountBreakdown.checkoutAmount,
    receiveAmount,
  };

  return price;
};
