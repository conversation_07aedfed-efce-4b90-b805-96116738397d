const { ObjectId } = require('mongoose').Types;
const { PURCHASE_TYPE, PRICE_TYPE } = require('../../constants/common');
const {
  communityFolderTypesMap,
  COMMUNITY_FOLDER_STATUS,
  COMMUNITY_EVENT_ACCESS_TYPES,
  COMMUNITY_FOLDER_PURCHASE_TYPES,
  EVENT_STATUS,
} = require('../../communitiesAPI/constants');
const {
  ACCESS_TYPE,
  PROGRAM_TYPE,
  PROGRAM_STATUS,
  PROGRAM_CHALLENGE_TYPE,
} = require('../program/constants');
const commonService = require('../communityNotification/email/common.service');
const logger = require('../logger.service');

const CommunityModel = require('../../communitiesAPI/models/community.model');
const FolderModel = require('../../communitiesAPI/models/communityFolders.model');
const EventModel = require('../../communitiesAPI/models/communityEvents.model');
const ProgramModel = require('../../models/program/program.model');
const LearnerModel = require('../../models/learners.model');

const regexUtils = require('../../utils/regex.util');
const { PRODUCT_TYPE } = require('../product/constants');

const SOURCE_TYPES = {
  MAGIC_REACH: 'magicReach',
  CHAT: 'chat',
};

function appendSearchIntoMatchQuery(matchFilter, search = '') {
  if (!search) {
    return;
  }

  const searchWithEscapedRegexSign = regexUtils.escapeRegExp(search);

  if (searchWithEscapedRegexSign !== '') {
    const regexPattern = new RegExp(searchWithEscapedRegexSign, 'i');

    // eslint-disable-next-line no-param-reassign
    matchFilter.title = regexPattern;
  }
}

function retrieveSubscriptionFilter(
  communityObjectId,
  onlyPaid,
  withAffiliateEarnings,
  search
) {
  const matchFilter = {
    _id: new ObjectId(communityObjectId),
  };

  appendSearchIntoMatchQuery(matchFilter, search);

  const orFilters = [];

  const commonFilters = [{ isActive: true }];

  if (onlyPaid) {
    commonFilters.push({ isPaidCommunity: true });
  }

  if (withAffiliateEarnings) {
    orFilters.push({ 'affiliateEarningAnalytics.quantity': { $gt: 0 } });
  }

  orFilters.push({ $and: commonFilters });

  matchFilter.$or = orFilters;

  const projectionFilters = commonFilters;

  return { matchFilter, projectionFilters };
}

function retrieveEventFilter(
  communityObjectId,
  currentDate,
  search,
  withAffiliateEarnings,
  onlyPaid = true,
  onlyPublished = false
) {
  const matchFilter = {
    communities: new ObjectId(communityObjectId),
  };

  appendSearchIntoMatchQuery(matchFilter, search);

  const orFilters = [];

  // TODO: Event Draft - To test flow here
  const commonFilters = [{ isActive: true }];

  if (onlyPaid) {
    commonFilters.push({ access: COMMUNITY_EVENT_ACCESS_TYPES.PAID });
  }

  const endTimeMatchFilter = { endTime: { $gt: currentDate } };
  const endTimeProjectionFilter = { $gt: ['$endTime', currentDate] };

  const isSoldOutMatchFilter = { isSoldOut: { $ne: true } };
  const isSoldOutProjectionFilter = { $ne: ['$isSoldOut', true] };

  const statusMatchFilters = {
    status: {
      $in: [EVENT_STATUS.DRAFT, EVENT_STATUS.PUBLISHED, 'Active'],
    },
  };
  const statusProjectionFilters = { status: 1 };

  if (onlyPublished) {
    statusMatchFilters.status = {
      $in: [EVENT_STATUS.PUBLISHED, 'Active'],
    };
  }

  if (withAffiliateEarnings) {
    orFilters.push({ 'affiliateEarningAnalytics.quantity': { $gt: 0 } });
  }

  orFilters.push({
    $and: [
      ...commonFilters,
      statusMatchFilters,
      endTimeMatchFilter,
      isSoldOutMatchFilter,
    ],
  });

  matchFilter.$or = orFilters;

  const projectionFilters = [
    ...commonFilters,
    statusProjectionFilters,
    endTimeProjectionFilter,
    isSoldOutProjectionFilter,
  ];

  return { matchFilter, projectionFilters };
}

function retrieveProductFilter(
  types,
  communityObjectId,
  onlyPublished,
  search,
  withAffiliateEarnings,
  onlyPaid = true
) {
  const matchFilter = {
    communityObjectId: new ObjectId(communityObjectId),
  };

  if (types) {
    matchFilter.type = { $in: types };
  }

  appendSearchIntoMatchQuery(matchFilter, search);

  const orFilters = [];

  const commonFilters = [];

  if (onlyPaid) {
    commonFilters.push({ access: COMMUNITY_FOLDER_PURCHASE_TYPES.PAID });
  }

  if (onlyPublished) {
    commonFilters.push({ status: COMMUNITY_FOLDER_STATUS.PUBLISHED });
  } else {
    commonFilters.push({
      status: {
        $in: [
          COMMUNITY_FOLDER_STATUS.UNPUBLISHED,
          COMMUNITY_FOLDER_STATUS.PUBLISHED,
        ],
      },
    });
  }

  const isSoldOutMatchFilter = { isSoldOut: { $ne: true } };
  const isSoldOutProjectionFilter = { $ne: ['$isSoldOut', true] };

  const stopAcceptingBookingsMatchFilter = {
    stopAcceptingBookings: { $ne: true },
  };
  const stopAcceptingBookingsProjectionFilter = {
    $ne: ['$stopAcceptingBookings', true],
  };

  if (withAffiliateEarnings) {
    orFilters.push({ 'affiliateEarningAnalytics.quantity': { $gt: 0 } });
  }

  orFilters.push({
    $and: [
      ...commonFilters,
      isSoldOutMatchFilter,
      stopAcceptingBookingsMatchFilter,
    ],
  });

  matchFilter.$or = orFilters;

  const projectionFilters = [
    ...commonFilters,
    isSoldOutProjectionFilter,
    stopAcceptingBookingsProjectionFilter,
  ];

  return { matchFilter, projectionFilters };
}

function retrieveProgramFilter(
  communityObjectId,
  currentDate,
  onlyPublished,
  search,
  withAffiliateEarnings,
  onlyPaid = true
) {
  const matchFilter = {
    communityObjectId: new ObjectId(communityObjectId),
  };

  appendSearchIntoMatchQuery(matchFilter, search);

  const orFilters = [];

  const commonFilters = [];

  if (onlyPaid) {
    commonFilters.push({ access: ACCESS_TYPE.PAID });
  }

  if (onlyPublished) {
    commonFilters.push({ status: PROGRAM_STATUS.PUBLISHED });
  } else {
    commonFilters.push({
      status: {
        $in: [PROGRAM_STATUS.DRAFT, PROGRAM_STATUS.PUBLISHED],
      },
    });
  }

  const isRegistrationClosedMatchFilter = {
    isRegistrationClosed: { $ne: true },
  };
  const isRegistrationClosedProjectionFilter = {
    $ne: ['$isRegistrationClosed', true],
  };

  const endTimeMatchFilter = { endTime: { $gt: currentDate } };
  const endTimeProjectionFilter = { $gt: ['$endTime', currentDate] };

  const startTimeMatchFilter = { startTime: { $gt: currentDate } };
  const startTimeProjectionFilter = { $gt: ['$startTime', currentDate] };

  const canJoinAfterStartFilter = { canJoinAfterStart: true };

  const isAlwaysOnChallengeMatchFilter = {
    challengeType: PROGRAM_CHALLENGE_TYPE.ALWAYS_ON,
  };

  if (withAffiliateEarnings) {
    orFilters.push({ 'affiliateEarningAnalytics.quantity': { $gt: 0 } });
  }

  orFilters.push({
    $and: [
      ...commonFilters,
      {
        // Return challenge that either
        // 1. Can join after start
        // or 2. Havent start yet
        $or: [
          canJoinAfterStartFilter,
          startTimeMatchFilter,
          isAlwaysOnChallengeMatchFilter,
        ],
      },
      isRegistrationClosedMatchFilter,
      {
        $or: [endTimeMatchFilter, isAlwaysOnChallengeMatchFilter],
      },
    ],
  });

  matchFilter.$or = orFilters;

  const projectionFilters = [
    ...commonFilters,
    {
      $or: [
        canJoinAfterStartFilter,
        startTimeProjectionFilter,
        isAlwaysOnChallengeMatchFilter,
      ],
    },
    isRegistrationClosedProjectionFilter,
    { $or: [endTimeProjectionFilter, isAlwaysOnChallengeMatchFilter] },
  ];

  return { matchFilter, projectionFilters };
}

function subscriptionProjection(
  source,
  withAffiliateEarnings,
  projectionFilters
) {
  const projection = {
    _id: 0,
    code: 1,
    entityObjectId: '$_id',
    title: 'Community Membership',
    slug: '$link',
    type: PURCHASE_TYPE.SUBSCRIPTION,
    thumbnail: '$fullScreenBannerImgData.mobileImgProps.src',
    profileImage: '$thumbnailImgData.mobileImgData.src',
    sales: withAffiliateEarnings
      ? '$affiliateEarningAnalytics.quantity'
      : '$earningAnalytics.quantity',
    earnings: withAffiliateEarnings
      ? '$affiliateEarningAnalytics.revenueInLocalCurrency'
      : '$earningAnalytics.revenueInLocalCurrency',
    prices: 1,
    priceType: PRICE_TYPE.FIXED,
    baseCurrency: 1,
    isPaidCommunity: 1,
  };

  if (withAffiliateEarnings) {
    projection.canToggle = {
      $cond: {
        if: { $and: projectionFilters },
        then: true,
        else: false,
      },
    };
  }

  if (source === SOURCE_TYPES.MAGIC_REACH) {
    projection.title = 1;
    projection.communityObjectId = '$_id';
  }

  return projection;
}

function eventProjection(
  source,
  withAffiliateEarnings,
  projectionFilters
) {
  const projection = {
    _id: 0,
    entityObjectId: '$_id',
    title: 1,
    slug: 1,
    entityLink: { $concat: ['/events', '$slug'] },
    type: PURCHASE_TYPE.EVENT,
    thumbnail: '$bannerImg',
    sales: withAffiliateEarnings
      ? '$affiliateEarningAnalytics.quantity'
      : '$earningAnalytics.quantity',
    earnings: withAffiliateEarnings
      ? '$affiliateEarningAnalytics.revenueInLocalCurrency'
      : '$earningAnalytics.revenueInLocalCurrency',
    price: {
      $cond: {
        if: {
          $eq: ['$pricingConfig.priceType', PRICE_TYPE.FLEXIBLE],
        },
        then: '$pricingConfig.suggestedAmount',
        else: '$amount',
      },
    },
    currency: 1,
    priceType: {
      $ifNull: ['$pricingConfig.priceType', PRICE_TYPE.FIXED],
    },
  };

  if (withAffiliateEarnings) {
    projection.canToggle = {
      $cond: {
        if: { $and: projectionFilters },
        then: true,
        else: false,
      },
    };
  }

  if ([SOURCE_TYPES.MAGIC_REACH, SOURCE_TYPES.CHAT].includes(source)) {
    projection.access = 1;
    projection.startTime = 1;
    projection.endTime = 1;
    projection.communityObjectId = { $first: '$communities' };
    // for backward compatibility
    projection.bannerImg = 1;
  }

  return projection;
}

function productProjection(
  source,
  withAffiliateEarnings,
  projectionFilters
) {
  const projection = {
    _id: 0,
    entityObjectId: '$_id',
    title: 1,
    slug: '$resourceSlug',
    entityLink: {
      $concat: ['/products', '$resourceSlug'],
    },
    type: {
      $cond: {
        if: { $eq: ['$type', communityFolderTypesMap.SESSION] },
        then: PURCHASE_TYPE.SESSION,
        else: {
          $cond: {
            if: { $eq: ['$type', communityFolderTypesMap.COURSE] },
            then: PRODUCT_TYPE.COURSE,
            else: PRODUCT_TYPE.DIGITAL_FILES,
          },
        },
      },
    },
    thumbnail: 1,
    sales: withAffiliateEarnings
      ? '$affiliateEarningAnalytics.quantity'
      : '$earningAnalytics.quantity',
    earnings: withAffiliateEarnings
      ? '$affiliateEarningAnalytics.revenueInLocalCurrency'
      : '$earningAnalytics.revenueInLocalCurrency',
    price: {
      $cond: {
        if: {
          $eq: ['$pricingConfig.priceType', PRICE_TYPE.FLEXIBLE],
        },
        then: '$pricingConfig.suggestedAmount',
        else: '$amount',
      },
    },
    currency: 1,
    priceType: {
      $ifNull: ['$pricingConfig.priceType', PRICE_TYPE.FIXED],
    },
  };

  if (withAffiliateEarnings) {
    projection.canToggle = {
      $cond: {
        if: { $and: projectionFilters },
        then: true,
        else: false,
      },
    };
  }

  if ([SOURCE_TYPES.MAGIC_REACH, SOURCE_TYPES.CHAT].includes(source)) {
    projection.access = 1;
    projection.startTime = 1;
    projection.endTime = 1;
    projection.durationIntervalInMinutes = 1;
    projection.hostInfo = 1;
    projection.communityObjectId = 1;
  }

  return projection;
}

function programProjection(
  source,
  withAffiliateEarnings,
  projectionFilters
) {
  const projection = {
    _id: 0,
    entityObjectId: '$_id',
    title: 1,
    slug: 1,
    entityLink: {
      $concat: ['/challenges', '$slug'],
    },
    type: {
      $cond: {
        if: { $eq: ['$type', PROGRAM_TYPE.CHALLENGE] },
        then: PURCHASE_TYPE.CHALLENGE,
        else: 'COURSE', // Does not exists yet
      },
    },
    thumbnail: '$cover',
    sales: withAffiliateEarnings
      ? '$affiliateEarningAnalytics.quantity'
      : '$earningAnalytics.quantity',
    earnings: withAffiliateEarnings
      ? '$affiliateEarningAnalytics.revenueInLocalCurrency'
      : '$earningAnalytics.revenueInLocalCurrency',
    price: {
      $cond: {
        if: {
          $eq: ['$pricingConfig.priceType', PRICE_TYPE.FLEXIBLE],
        },
        then: '$pricingConfig.suggestedAmount',
        else: '$pricingConfig.amount',
      },
    },
    currency: '$pricingConfig.currency',
    priceType: {
      $ifNull: ['$pricingConfig.priceType', PRICE_TYPE.FIXED],
    },
  };

  if (withAffiliateEarnings) {
    projection.canToggle = {
      $cond: {
        if: { $and: projectionFilters },
        then: true,
        else: false,
      },
    };
  }
  if ([SOURCE_TYPES.MAGIC_REACH, SOURCE_TYPES.CHAT].includes(source)) {
    projection.access = 1;
    projection.startTime = 1;
    projection.endTime = 1;
    projection.durationInDays = 1;
    projection.communityObjectId = 1;
    projection.challengeType = 1;
    // for backward compatibility
    projection.cover = 1;
  }

  return projection;
}

async function retrieveEntity(
  entityType,
  matchFilter,
  projection,
  pageSize = 0
) {
  if (!matchFilter) {
    return [];
  }

  let model;

  switch (entityType) {
    case PURCHASE_TYPE.SUBSCRIPTION:
      model = CommunityModel;
      break;
    case PURCHASE_TYPE.EVENT:
      model = EventModel;
      break;
    case PURCHASE_TYPE.FOLDER:
    case PURCHASE_TYPE.SESSION:
      model = FolderModel;
      break;
    case PURCHASE_TYPE.CHALLENGE:
      model = ProgramModel;
      break;
    default:
      throw new Error(`${entityType} not supported`);
  }

  const result = await model
    .find(matchFilter, projection)
    .limit(pageSize)
    .lean();

  return result;
}

const reformatDiscountPrice = (discountedPrice, price) => {
  const totalMonthsForDiscountedPrice =
    discountedPrice.intervalCount *
    (discountedPrice.interval === 'year' ? 12 : 1);
  const totalMonths =
    price.intervalCount * (price.interval === 'year' ? 12 : 1);

  const discountedPricePerMonth =
    discountedPrice.cmSetPrice / totalMonthsForDiscountedPrice;
  const pricePerMonth = price.cmSetPrice / totalMonths;

  const percentage = (
    ((pricePerMonth - discountedPricePerMonth) * 100) /
    pricePerMonth
  ).toFixed(0);
  return {
    discountPerc: percentage,
    discountIntervalCount: discountedPrice.intervalCount,
    discountInterval: discountedPrice.interval,
  };
};

async function formatSubscriptionPrice(subscriptions) {
  const communityCodes = subscriptions.map(
    (subscription) => subscription.code
  );
  const ownerCache = await commonService.retrieveCommunityOwnerCacheInfo(
    communityCodes
  );
  const newSubscriptions = subscriptions.map((subscription) => {
    const newSubscription = { ...subscription };
    newSubscription.ownerName =
      ownerCache.get(subscription.code)?.name ?? '';
    const subscriptionPrices = subscription.isPaidCommunity
      ? subscription.prices
      : [];
    delete newSubscription.prices;
    const prices = subscriptionPrices
      .filter((price) => price.currency === subscription.baseCurrency)
      .sort((a, b) => {
        if (a.interval === b.interval) {
          return a.intervalCount - b.intervalCount;
        }
        return a.interval === 'month' ? -1 : 1;
      });
    const priceDetails = prices[0];
    const newPriceDetails = {
      localCurrency: priceDetails?.currency ?? subscription.baseCurrency,
      localAmount: priceDetails?.cmSetPrice ?? 0,
      interval: priceDetails?.interval ?? 'month',
      intervalCount: priceDetails?.intervalCount ?? 1,
    };

    let discountPrice = {};
    if (prices.length > 1) {
      discountPrice = reformatDiscountPrice(prices[1], prices[0]);
    }
    newSubscription.priceDetails = {
      ...newPriceDetails,
      ...discountPrice,
    };

    delete newSubscription.prices;

    return newSubscription;
  });
  return newSubscriptions;
}

async function formatSessionHostName(source, sessions) {
  if (![SOURCE_TYPES.MAGIC_REACH, SOURCE_TYPES.CHAT].includes(source)) {
    return sessions;
  }

  const learnerObjectIds = [];
  sessions.forEach((session) => {
    if (
      session.hostInfo?.hostLearnerObjectId &&
      (session.hostInfo?.hostTitle === '' || !session.hostInfo?.hostTitle)
    ) {
      learnerObjectIds.push(session.hostInfo.hostLearnerObjectId);
    }
  });
  const learners = await LearnerModel.find({ _id: learnerObjectIds })
    .select('_id firstName lastName')
    .lean();

  const learnerCache = new Map();
  learners.forEach((learner) => {
    learnerCache.set(learner._id.toString(), learner);
  });

  const newSessions = sessions.map((session) => {
    const productInfo = { ...session };
    if (session.hostInfo?.hostLearnerObjectId) {
      if (
        session.hostInfo?.hostTitle === '' ||
        !session.hostInfo?.hostTitle
      ) {
        const learner = learnerCache.get(
          session.hostInfo.hostLearnerObjectId.toString()
        );
        productInfo.hostName = `${learner?.firstName ?? ''} ${
          learner?.lastName ?? ''
        }`.trim();
      } else {
        productInfo.hostName = session.hostInfo?.hostTitle;
      }
      delete productInfo.hostInfo;
    }

    return productInfo;
  });
  return newSessions;
}

const retrieveAllProducts = async ({
  search = '',
  communityObjectId,
  withSubscriptionType = false,
  onlyPublished = false,
  withAffiliateEarnings = false,
  onlyPaid = true,
  pageSize = 0,
  source = null,
}) => {
  const currentDate = new Date();

  const subscriptionFilter = retrieveSubscriptionFilter(
    communityObjectId,
    onlyPaid,
    withAffiliateEarnings,
    search
  );

  const eventFilter = retrieveEventFilter(
    communityObjectId,
    currentDate,
    search,
    withAffiliateEarnings,
    onlyPaid,
    onlyPublished
  );

  const productFilter = retrieveProductFilter(
    [
      communityFolderTypesMap.DIGITAL_PRODUCT,
      communityFolderTypesMap.COURSE,
    ],
    communityObjectId,
    onlyPublished,
    search,
    withAffiliateEarnings,
    onlyPaid
  );

  const sessionFilter = retrieveProductFilter(
    [communityFolderTypesMap.SESSION],
    communityObjectId,
    onlyPublished,
    search,
    withAffiliateEarnings,
    onlyPaid
  );

  const programFilter = retrieveProgramFilter(
    communityObjectId,
    currentDate,
    onlyPublished,
    search,
    withAffiliateEarnings,
    onlyPaid
  );

  const [subscriptions, events, folders, sessions, programs] =
    await Promise.all([
      withSubscriptionType
        ? retrieveEntity(
            PURCHASE_TYPE.SUBSCRIPTION,
            subscriptionFilter.matchFilter,
            subscriptionProjection(
              source,
              withAffiliateEarnings,
              subscriptionFilter.projectionFilters
            ),
            pageSize
          )
        : [],
      retrieveEntity(
        PURCHASE_TYPE.EVENT,
        eventFilter.matchFilter,
        eventProjection(
          source,
          withAffiliateEarnings,
          eventFilter.projectionFilters
        ),
        pageSize
      ),
      retrieveEntity(
        PURCHASE_TYPE.FOLDER,
        productFilter.matchFilter,
        productProjection(
          source,
          withAffiliateEarnings,
          productFilter.projectionFilters
        ),
        pageSize
      ),
      retrieveEntity(
        PURCHASE_TYPE.SESSION,
        sessionFilter.matchFilter,
        productProjection(
          source,
          withAffiliateEarnings,
          sessionFilter.projectionFilters
        ),
        pageSize
      ),
      retrieveEntity(
        PURCHASE_TYPE.CHALLENGE,
        programFilter.matchFilter,
        programProjection(
          source,
          withAffiliateEarnings,
          programFilter.projectionFilters
        ),
        pageSize
      ),
    ]);

  const newSubscriptions = await formatSubscriptionPrice(subscriptions);
  const newSessions = await formatSessionHostName(source, sessions);

  const allProducts = [
    ...newSubscriptions,
    ...programs.sort((a, b) => a.title - b.title),
    ...events.sort((a, b) => a.title - b.title),
    ...folders.sort((a, b) => a.title - b.title),
    ...newSessions.sort((a, b) => a.title - b.title),
  ];

  return {
    products:
      pageSize === 0
        ? allProducts
        : allProducts.slice(
            0,
            pageSize > allProducts.length ? allProducts.length : pageSize
          ),
  };
};

const retrieveTotalEntitiesSize = async ({
  communityObjectId,
  withSubscriptionType = false,
  onlyPublished = false,
  search = null,
  withAffiliateEarnings = false,
  onlyPaid = true,
}) => {
  const currentDate = new Date();

  const subscriptionFilter = retrieveSubscriptionFilter(
    communityObjectId,
    onlyPaid,
    withAffiliateEarnings,
    search
  );

  const eventFilter = retrieveEventFilter(
    communityObjectId,
    currentDate,
    search,
    withAffiliateEarnings,
    onlyPaid,
    onlyPublished
  );

  const productFilter = retrieveProductFilter(
    null,
    communityObjectId,
    onlyPublished,
    search,
    withAffiliateEarnings,
    onlyPaid
  );
  const programFilter = retrieveProgramFilter(
    communityObjectId,
    currentDate,
    onlyPublished,
    search,
    withAffiliateEarnings,
    onlyPaid
  );

  const result = await Promise.all([
    withSubscriptionType
      ? CommunityModel.countDocuments(subscriptionFilter.matchFilter)
      : 0,
    EventModel.countDocuments(eventFilter.matchFilter),
    FolderModel.countDocuments(productFilter.matchFilter),
    ProgramModel.countDocuments(programFilter.matchFilter),
  ]);

  return result.reduce((acc, data) => acc + data, 0);
};

const aggregateAffiliateEarningsAndSales = async (communityObjectId) => {
  const currentDate = new Date();

  const subscriptionFilter = retrieveSubscriptionFilter(
    communityObjectId,
    false,
    true
  );

  const eventFilter = retrieveEventFilter(
    communityObjectId,
    currentDate,
    null,
    true
  );

  const productFilter = retrieveProductFilter(
    null,
    communityObjectId,
    false,
    null,
    true
  );

  const programFilter = retrieveProgramFilter(
    communityObjectId,
    currentDate,
    false,
    null,
    true
  );

  const groupQuery = {
    $group: {
      _id: null,
      sales: {
        $sum: '$affiliateEarningAnalytics.quantity',
      },
      earnings: {
        $sum: '$affiliateEarningAnalytics.revenueInLocalCurrency',
      },
    },
  };

  const result = await Promise.all([
    CommunityModel.aggregate([
      { $match: subscriptionFilter.matchFilter },
      groupQuery,
    ]),
    EventModel.aggregate([
      { $match: eventFilter.matchFilter },
      groupQuery,
    ]),
    FolderModel.aggregate([
      { $match: productFilter.matchFilter },
      groupQuery,
    ]),
    ProgramModel.aggregate([
      { $match: programFilter.matchFilter },
      groupQuery,
    ]),
  ]);

  return result.reduce(
    (acc, data) => {
      const { sales = 0, earnings = 0 } = data[0] ?? {};
      acc.sales += sales;
      acc.earnings += earnings;

      return acc;
    },
    { sales: 0, earnings: 0 }
  );
};

const retrieveEntitiesCache = async ({
  subscriptionObjectIds = [],
  eventObjectIds = [],
  productObjectIds = [],
  programObjectIds = [],
  source = null,
}) => {
  let subscriptionFilter;
  let eventFilter;
  let productFilter;
  let programFilter;

  if (subscriptionObjectIds.length > 0) {
    subscriptionFilter = {
      _id: { $in: subscriptionObjectIds },
    };
  }

  if (eventObjectIds.length > 0) {
    eventFilter = {
      _id: { $in: eventObjectIds },
    };
  }

  if (productObjectIds.length > 0) {
    productFilter = {
      _id: { $in: productObjectIds },
    };
  }

  if (programObjectIds.length > 0) {
    programFilter = {
      _id: { $in: programObjectIds },
    };
  }

  const [subscriptions, events, products, programs] = await Promise.all([
    retrieveEntity(
      PURCHASE_TYPE.SUBSCRIPTION,
      subscriptionFilter,
      subscriptionProjection(source)
    ),
    retrieveEntity(
      PURCHASE_TYPE.EVENT,
      eventFilter,
      eventProjection(source)
    ),
    retrieveEntity(
      PURCHASE_TYPE.FOLDER,
      productFilter,
      productProjection(source)
    ),
    retrieveEntity(
      PURCHASE_TYPE.CHALLENGE,
      programFilter,
      programProjection(source)
    ),
  ]);

  let newSubscriptions = subscriptions;

  if ([SOURCE_TYPES.MAGIC_REACH, SOURCE_TYPES.CHAT].includes(source)) {
    newSubscriptions = await formatSubscriptionPrice(subscriptions);
  }

  const newProducts = await formatSessionHostName(source, products);

  const entitiesCache = [
    ...newSubscriptions,
    ...events,
    ...newProducts,
    ...programs,
  ].reduce((acc, entity) => {
    const { type, entityObjectId } = entity;
    let key = `${type}-${entityObjectId}`;
    acc.set(key, entity);

    // Add value for folder for backward compatibility
    // When there is same product id in the product list, but have different type
    // - FOLDER: for old posts
    // - DIGITAL_FILES, COURSE: for new posts

    // For the case that have same product id in different posts
    // in old post, the mentioned product is: FOLDER_6866055c45ae05b901999cea
    // in new post, the same mentioned product is: DIGITAL_FILES_6866055c45ae05b901999cea
    if ([PRODUCT_TYPE.COURSE, PRODUCT_TYPE.DIGITAL_FILES].includes(type)) {
      key = `${PURCHASE_TYPE.FOLDER}-${entityObjectId}`;
      acc.set(key, entity);
    }
    return acc;
  }, new Map());

  return entitiesCache;
};

module.exports = {
  SOURCE_TYPES,
  retrieveAllProducts,
  retrieveTotalEntitiesSize,
  aggregateAffiliateEarningsAndSales,
  retrieveEntitiesCache,
};
