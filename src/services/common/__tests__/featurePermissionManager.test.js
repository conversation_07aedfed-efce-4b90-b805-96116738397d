/* eslint-disable no-undef */
const FeaturePermissionManager = require('../featurePermissionManager.service');

const {
  FEATURE_LIST_ID,
  FEATURE_LIST_NAME,
} = require('../../../constants/common');

describe('FeaturePermissionManager', () => {
  const grandfatherFeatures = [
    { featureId: FEATURE_LIST_ID.STORAGE, allowed: true, limit: 10 },
    { featureId: FEATURE_LIST_ID.MANAGER, allowed: true, limit: 10 },
  ];

  it('should default to FREE plan if an invalid plan type is provided', () => {
    const manager = new FeaturePermissionManager('INVALID_PLAN', []);
    expect(manager.getFeaturePermType()).toBe('FREE');
  });

  it('should correctly set the feature permission type based on the plan', () => {
    const manager = new FeaturePermissionManager('PRO', []);
    expect(manager.getFeaturePermType()).toBe('PRO');
  });

  it('should reformat grandfather features correctly', () => {
    const manager = new FeaturePermissionManager(
      'FREE',
      grandfatherFeatures
    );
    expect(manager.grandfatherFeatures).toEqual({
      [FEATURE_LIST_ID.STORAGE]: {
        featureId: FEATURE_LIST_ID.STORAGE,
        allowed: true,
        limit: 10,
      },
      [FEATURE_LIST_ID.MANAGER]: {
        featureId: FEATURE_LIST_ID.MANAGER,
        allowed: true,
        limit: 10,
      },
    });
  });

  it('should compute final features correctly with grandfathered features for FREE', () => {
    const manager = new FeaturePermissionManager(
      'FREE',
      grandfatherFeatures
    );
    const finalFeatures = manager.getAllFeaturesDetailed();

    const storageFeature = finalFeatures.find(
      (feature) => feature.featureId === FEATURE_LIST_ID.STORAGE
    );
    const managerFeature = finalFeatures.find(
      (feature) => feature.featureId === FEATURE_LIST_ID.MANAGER
    );
    const memberFeature = finalFeatures.find(
      (feature) => feature.featureId === FEATURE_LIST_ID.MEMBER
    );

    expect(storageFeature.isGrandfathered).toBe(true);
    expect(storageFeature.limit).toBe(10);

    expect(managerFeature.isGrandfathered).toBe(true);
    expect(managerFeature.limit).toBe(10);

    expect(memberFeature.isGrandfathered).toBe(false);
    expect(memberFeature.limit).toBe(500);
  });

  it('should compute final features correctly with grandfathered features for PRO', () => {
    const manager = new FeaturePermissionManager(
      'PRO',
      grandfatherFeatures
    );
    const finalFeatures = manager.getAllFeaturesDetailed();

    const storageFeature = finalFeatures.find(
      (feature) => feature.featureId === FEATURE_LIST_ID.STORAGE
    );
    const managerFeature = finalFeatures.find(
      (feature) => feature.featureId === FEATURE_LIST_ID.MANAGER
    );
    const memberFeature = finalFeatures.find(
      (feature) => feature.featureId === FEATURE_LIST_ID.MEMBER
    );

    expect(storageFeature.isGrandfathered).toBe(false);
    expect(storageFeature.limit).toBe(50);

    expect(managerFeature.isGrandfathered).toBe(true);
    expect(managerFeature.limit).toBe(10);

    expect(memberFeature.isGrandfathered).toBe(false);
    expect(memberFeature.limit).toBe(10000);
  });

  it('should correctly determine if a feature is grandfathered for PRO', () => {
    const manager = new FeaturePermissionManager(
      'PRO',
      grandfatherFeatures
    );
    expect(manager.isFeatureGrandfathered(FEATURE_LIST_ID.STORAGE)).toBe(
      false
    );
    expect(manager.isFeatureGrandfathered(FEATURE_LIST_ID.MANAGER)).toBe(
      true
    );
    expect(manager.isFeatureGrandfathered(FEATURE_LIST_ID.MEMBER)).toBe(
      false
    );
  });

  it('should correctly determine if a feature is allowed', () => {
    const manager = new FeaturePermissionManager('PRO', []);
    expect(manager.isFeatureAllowed(FEATURE_LIST_ID.STORAGE)).toBe(true);
    expect(manager.isFeatureAllowed(FEATURE_LIST_ID.MAGIC_ADS)).toBe(
      false
    );
  });

  it('should correctly determine if a feature is allowed based on base currency', () => {
    const manager = new FeaturePermissionManager('PRO', []);
    expect(
      manager.isFeatureAllowed(FEATURE_LIST_ID.ZERO_LINK, 'INR')
    ).toBe(false);
    expect(
      manager.isFeatureAllowed(FEATURE_LIST_ID.ZERO_LINK, 'USD')
    ).toBe(true);
  });

  it('should correctly return the feature limit', () => {
    const manager = new FeaturePermissionManager('PRO', []);
    expect(manager.getFeatureLimit(FEATURE_LIST_ID.STORAGE)).toBe(
      50 * 1024 * 1024 * 1024
    ); // 50 GB in bytes
    expect(manager.getFeatureLimit(FEATURE_LIST_ID.MEMBER)).toBe(10000);
  });

  it('should return the correct feature configuration', () => {
    const manager = new FeaturePermissionManager('PRO', []);
    const feature = manager.getFeature(FEATURE_LIST_ID.STORAGE);
    expect(feature).toEqual({
      featureName: FEATURE_LIST_NAME.STORAGE,
      allowed: true,
      limit: 50,
      interval: null,
      intervalCount: null,
      isGrandfathered: false,
    });
  });

  it('should return null for a non-existent feature configuration', () => {
    const manager = new FeaturePermissionManager('PRO', []);
    const feature = manager.getFeature(9999); // Non-existent feature ID
    expect(feature).toBeNull();
  });
});
