const CommunityRoleModel = require('../../communitiesAPI/models/communityRole.model');
const { MEMBERSHIP_COMMUNITY_ROLE } = require('../membership/constants');
const UserModel = require('../../models/users.model');
const LearnerModel = require('../../models/learners.model');

exports.getCommunityOwnerInfo = async (communityObjectId) => {
  const ownerRole = await CommunityRoleModel.findOne({
    communityObjectId,
    role: MEMBERSHIP_COMMUNITY_ROLE.OWNER,
  }).lean();
  const user = await UserModel.findById(ownerRole.userObjectId).lean();
  ownerRole.user = user;

  const learner = await LearnerModel.findById(user.learner).lean();
  ownerRole.learner = learner;

  return ownerRole;
};
