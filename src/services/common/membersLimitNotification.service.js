const FeaturePermissionManager = require('@services/common/featurePermissionManager.service');
const { FEATURE_LIST_ID } = require('@constants/common');
const MembershipUsageService = require('../featurePermissions/membershipUsage.service');
const { ENTITY_TYPE: PLAN_TYPE } = require('../plan/constants');
const communityNotificationService = require('../communityNotification');

// To reduce the chance of missing notification due to race condition
const REACHING_LIMIT_FREE_THRESHOLD = [401, 402];
const REACHING_LIMIT_PRO_THRESHOLD = [9001, 9002];

function shouldSendLimitReachingNotification(
  planType,
  currentUsage,
  limit
) {
  const featurePermissionManager = new FeaturePermissionManager(planType);
  const featureLimit = featurePermissionManager.getFeatureLimit(
    FEATURE_LIST_ID.MEMBER
  );

  if (!planType && featureLimit === limit) {
    return REACHING_LIMIT_FREE_THRESHOLD.includes(currentUsage);
  }

  if (planType === PLAN_TYPE.PRO && featureLimit === limit) {
    return REACHING_LIMIT_PRO_THRESHOLD.includes(currentUsage);
  }

  return false;
}

exports.notifyMembersLimit = async ({
  community,
  additionalEmails = 0,
}) => {
  const { currentUsage, remaining, limit } =
    await MembershipUsageService.checkAddMemberLimit(
      community._id,
      additionalEmails,
      true
    );

  const planType = community.config?.planType;

  if (remaining - additionalEmails === 0) {
    return communityNotificationService.sendMembersLimitReachedNotification(
      {
        communityObjectId: community._id,
      }
    );
  }

  const totalUsageAfterAdding = currentUsage + additionalEmails;

  if (
    shouldSendLimitReachingNotification(
      planType,
      totalUsageAfterAdding,
      limit
    )
  ) {
    return communityNotificationService.sendMembersLimitReachingNotification(
      {
        communityObjectId: community._id,
      }
    );
  }
};
