const { createApi } = require('unsplash-js');
const config = require('../../config');
const { InternalError } = require('../../utils/error.util');
const logger = require('../logger.service');

const unsplash = createApi({ accessKey: config.unsplashApiKey });

/**
 * Minifies the Unsplash image data to include only essential fields
 * @param {Object} imageData - Raw image data from Unsplash
 * @returns {Object} - Minified image data
 */
const minifyImageData = (imageData) => ({
  id: imageData?.id,
  urls: {
    regular: imageData?.urls?.regular,
  },
  user: {
    id: imageData?.user?.id,
    name: imageData?.user?.name,
    links: {
      html: imageData?.user?.links?.html,
    },
  },
  links: {
    html: imageData?.links?.html,
    download: imageData?.links?.download,
    download_location: imageData?.links?.download_location,
  },
});

/**
 * Fetch images from Unsplash based on the search query.
 * @param {string} query - Search term for the images.
 * @param {number} page - Page number for pagination.
 * @param {number} perPage - Number of images per page.
 * @param {string} orientation - Image orientation (landscape or portrait).
 * @returns {Promise<Array>} - List of images.
 */
exports.getUnsplashImages = async ({
  query = '',
  page = 1,
  perPage = 10,
  orientation = 'landscape',
}) => {
  try {
    const response = await unsplash.search.getPhotos({
      query,
      page,
      perPage,
      orientation,
    });

    if (response.errors) {
      const errorMsg = Array.isArray(response.errors)
        ? response.errors.join(', ')
        : response.errors;

      throw new InternalError(errorMsg);
    }

    const data = {
      ...response.response,
      results: response.response.results.map(minifyImageData),
    };

    return data;
  } catch (error) {
    logger.error('Error fetching images from Unsplash:', error);
    throw new InternalError('Error fetching images from Unsplash');
  }
};

exports.trackDownload = async (downloadLocation) => {
  try {
    const result = await unsplash.photos.trackDownload({
      downloadLocation,
    });
    return result;
  } catch (error) {
    logger.error('Error tracking download:', error);
    throw new InternalError('Error tracking download');
  }
};
