const { PRODUCT_TYPE } = require('../product/constants');
const CommunityProduct = require('../../models/product/communityProduct.model');
const Community = require('../../communitiesAPI/models/community.model');
const { ToUserError } = require('../../utils/error.util');

// URL path to PRODUCT_TYPE mapping
const URL_PRODUCT_TYPE_MAP = {
  products: PRODUCT_TYPE.DIGITAL_FILES,
  'digital-files': PRODUCT_TYPE.DIGITAL_FILES,
  events: PRODUCT_TYPE.EVENT,
  sessions: PRODUCT_TYPE.SESSION,
  challenges: PRODUCT_TYPE.CHALLENGE,
  courses: PRODUCT_TYPE.COURSE,
};

const parseProductUrl = async (productLink) => {
  // Remove leading/trailing slashes and filter out empty segments
  const cleanUrl = productLink.replace(/^\/+|\/+$/g, '');

  // Split URL into segments and filter out empty segments
  const segments = cleanUrl.split('/').filter((segment) => segment !== '');

  if (segments.length !== 3) {
    throw new ToUserError(
      'Invalid product URL format. Expected format: /{communityCode}/{productType}/{productSlug}'
    );
  }

  const [communityLink, productTypePath, productSlug] = segments;

  // Map URL path to PRODUCT_TYPE
  const productType = URL_PRODUCT_TYPE_MAP[productTypePath];

  if (!productType) {
    throw new ToUserError(`Invalid product type path: ${productTypePath}`);
  }

  // First, find the community by its link (add leading slash if not present)
  const communityLinkWithSlash = communityLink.startsWith('/')
    ? communityLink
    : `/${communityLink}`;
  const community = await Community.findOne({
    link: communityLinkWithSlash,
  }).select('_id code');

  if (!community) {
    throw new ToUserError('Community not found with the provided link');
  }

  // Query the database for the product by communityObjectId and slug
  // Add leading slash to slug if not present (database stores slugs with leading slash)
  const productSlugWithSlash = productSlug.startsWith('/')
    ? productSlug
    : `/${productSlug}`;
  const product = await CommunityProduct.findOne({
    communityObjectId: community._id,
    slug: productSlugWithSlash,
  }).select('_id entityObjectId');

  if (!product) {
    throw new ToUserError('Product not found with the provided URL');
  }

  return {
    productId: product.entityObjectId.toString(),
    communityCode: community.code,
    productType,
  };
};

module.exports = {
  parseProductUrl,
};
