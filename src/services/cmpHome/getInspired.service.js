const TemplateLibraryModel = require('../../models/getInspired/templateLibrary.model');

const {
  retrieveDefaultCommunityTemplate,
  retrieveTemplateVersionForQuerying,
} = require('../getInspired/common.service');

const {
  TEMPLATE_LIBRARY_TYPES,
  TEMPLATE_SOURCE_TYPE,
} = require('../../constants/common');

exports.getInspiredEarningsBanner = async (community) => {
  const latestTemplate = await TemplateLibraryModel.findOne({
    communityObjectId: community._id,
    isAIGenerated: true,
    source: TEMPLATE_SOURCE_TYPE.GET_INSPIRED,
  })
    .sort({ version: -1 })
    .select('version')
    .lean();
  const { aiTemplateGenerationConfig = {} } = community;
  const { generationStatus, generationVersion } =
    aiTemplateGenerationConfig;
  const { isDefault, version } = retrieveTemplateVersionForQuerying(
    generationStatus,
    generationVersion,
    latestTemplate?.version
  );
  const projection =
    'predictedEarningsInUSD predictedEarningsInLocalCurrency localCurrency metadata';
  let template;
  if (isDefault) {
    template = await retrieveDefaultCommunityTemplate(community);
  } else {
    template = await TemplateLibraryModel.findOne({
      isAIGenerated: true,
      version,
      communityObjectId: community._id,
      type: TEMPLATE_LIBRARY_TYPES.COMMUNITY,
      source: TEMPLATE_SOURCE_TYPE.GET_INSPIRED,
    })
      .select(projection)
      .lean();
  }
  return template;
};
