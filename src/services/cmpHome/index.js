const nextThingsToDoService = require('./nextThingsToDo.service');
const getInspiredService = require('./getInspired.service');
const recentService = require('./recent.service');
const CommunityModel = require('../../communitiesAPI/models/community.model');

exports.getCmpHomeDetails = async (communityId, learnerObjectId) => {
  const community = await CommunityModel.findById(communityId).lean();

  const [communityTemplate, todos, recents] = await Promise.all([
    getInspiredService.getInspiredEarningsBanner(community),
    nextThingsToDoService.getNextThingsToDoDetails(
      community,
      learnerObjectId
    ),
    recentService.getRecentProducts(community),
  ]);
  const todoList = todos.sortedResults;
  delete todos.sortedResults;
  return {
    recents,
    communityTemplate,
    todos,
    todoList,
  };
};
