const { ObjectId } = require('mongoose').Types;
const { DateTime } = require('luxon');

const Community = require('../../communitiesAPI/models/community.model');
const Subscription = require('../../communitiesAPI/models/communitySubscriptions.model');
const Learner = require('../../models/learners.model');
const CommunityFolder = require('../../communitiesAPI/models/communityFolders.model');
const CommunityEvent = require('../../communitiesAPI/models/communityEvents.model');
const Program = require('../../models/program/program.model');
const CommunityFolderPurchase = require('../../communitiesAPI/models/communityFolderPurchases.model');
const SessionAttendee = require('../../models/oneOnOneSessions/sessionAttendees.model');
const CommunityPost = require('../../communitiesAPI/models/communityPost.model');
const Config = require('../../models/config.model');
const CountryCurrencyMappingModel = require('../../models/countryInfoMapping.model');
const CommunityRoleModel = require('../../communitiesAPI/models/communityRole.model');

const productService = require('../common/communityProducts.service');
const communityService = require('../community');
const programService = require('../program');
const eventService = require('../../communitiesAPI/services/common/communityEvents.service');
const sessionService = require('../oneOnOneSession/manageOneOnOneSession.service');
const membershipService = require('../membership');
const subscriptionCommonService = require('../common/subscription.service');
const { affiliateService } = require('../affiliate');

const {
  ParamError,
  DBError,
  ResourceNotFoundError,
} = require('../../utils/error.util');
const { getMongoServerTimeoutConfig } = require('../config.service');

const programConstants = require('../program/constants');
const legacyCommunityConstants = require('../../communitiesAPI/constants');
const commonConstants = require('../../constants/common');
const membershipConstants = require('../membership/constants');

const CommunityPostModel = require('../../communitiesAPI/models/communityPost.model');
const MembershipModel = require('../../models/membership/membership.model');
const { lookupAndUnwind } = require('../../utils/mongodb.util');
const CommunityModel = require('../../communitiesAPI/models/community.model');
const {
  aclRoles,
  EVENT_STATUS,
} = require('../../communitiesAPI/constants');
const mongodbUtils = require('../../utils/mongodb.util');
const {
  generateCoverMediaItems,
} = require('../../utils/multipleCoverMediaItems.util');
const {
  isCommunityIndexable,
} = require('../../communitiesAPI/services/common/utils');
const {
  COVER_MEDIA_ENTITY_TYPES,
} = require('../../constants/coverMediaItems.constant');
const FeaturePermissionManager = require('../common/featurePermissionManager.service');

const DEFAULT_COMMUNITY_PROFILE_IMAGE =
  'https://d2oi1rqwb0pj00.cloudfront.net/nasIO/community-landing-page/png/community-profile-picture-placeholder.png';
const DEFAULT_COMMUNITY_META_IMAGE =
  'https://d2oi1rqwb0pj00.cloudfront.net/na-website/communities-page/png/community-metaPreviewImage.png';

const getLearner = async (email) => {
  if (!email) {
    return undefined;
  }
  const learner = await Learner.findOne(
    {
      email,
      isActive: true,
    },
    {
      _id: 1,
      email: 1,
      firstName: 1,
      lastName: 1,
      language: 1,
      profileImage: 1,
    }
  ).lean();
  return learner;
};

async function getActiveSubscriptionFromEmail({ email, communityCode }) {
  const existingMemberParams = {
    $or: [
      { status: commonConstants.COMMUNITY_SUBSCRIPTION_STATUSES.CURRENT },
      {
        status: commonConstants.COMMUNITY_SUBSCRIPTION_STATUSES.CANCELLED,
        cancelledAt: { $gte: new Date() },
      },
    ],
  };
  const existingSubscription = await Subscription.findOne({
    communityCode,
    email,
    ...existingMemberParams,
  });

  return existingSubscription;
}

async function getCommunityBrief({ communityId }) {
  const communityBrief = await Community.findOne(
    {
      _id: communityId,
    },
    {
      _id: 1,
      code: 1,
      createdBy: 1,
      config: 1,
    }
  );
  if (!communityBrief) {
    throw new ParamError('Community not found with given id');
  }
  return communityBrief;
}

const getCommunityUpcoming = async ({ communityId, userFromRequest }) => {
  const learner = await getLearner(userFromRequest?.email);
  const community = await getCommunityBrief({ communityId });
  const [upcomingChallenges, upcomingEvents, upcomingSessions] =
    await Promise.all([
      programService.getProgramsService.getUpcomingChallenges({
        community,
        learner,
        limit: 10,
        skip: 0,
      }),
      eventService.getUpcomingEventsByCommunityId(
        learner?._id,
        userFromRequest?._id,
        communityId,
        1,
        1,
        10
      ),
      sessionService.getCommunityUpcomingBookings({
        userFromRequest,
        communityObjectId: community._id,
      }),
    ]);

  const upcomingList = [];
  if (upcomingChallenges?.results) {
    upcomingChallenges.results.forEach((challenge) => {
      upcomingList.push({
        ...challenge,
        itemType: 'challenge',
      });
    });
  }
  if (upcomingEvents?.data) {
    upcomingEvents.data.forEach((event) => {
      upcomingList.push({
        ...event,
        itemType: 'event',
      });
    });
  }
  upcomingSessions.forEach((session) => {
    upcomingList.push({
      ...session,
      itemType: 'session',
    });
  });

  const getStartTime = (item) => {
    if (item.startTime !== undefined) {
      return item.startTime;
    }
    return item.sessionStartTime;
  };

  upcomingList.sort((a, b) => {
    const startTimeA = getStartTime(a);
    const startTimeB = getStartTime(b);
    return startTimeA - startTimeB;
  });
  return upcomingList.slice(0, 10);
};

async function getCommunityPricingDataWrapper({ community, ip }) {
  if (community.isPaidCommunity === false) {
    return undefined;
  }

  return subscriptionCommonService.getSubscriptionDisplayPrices({
    communityId: community._id,
    ip,
  });
}

const getCommunityLandingPageData = async ({
  communitySlug,
  userFromRequest,
  ip,
  affiliateCode = null,
}) => {
  const community =
    await communityService.getCommunityService.getCommunityByLink({
      link: communitySlug,
      projection: {
        $project: {
          isActive: 1,
          isDraft: 1,
          code: 1,
          link: 1,
          slug: '$link',
          createdBy: 1,
          bots: 1,
          isWhatsappExperienceCommunity: 1,
          countryCreatedIn: 1,
          payment_methods: 1,
          baseCurrency: 1,
          title: 1,
          description: 1,
          stripeProductId: 1,
          aiAssistantInfo: 1,
          applicationConfigDataFields: 1,
          applicationConfig: {
            requestApproval: { $ifNull: ['$request_approval', false] },
            autoApproval: {
              //FE should not be using autoApproval field after launch
              $ifNull: ['$applicationConfig.autoApproval', false],
            },
          },
          additionalConfig: {
            hideSignUpOverlay: {
              $ifNull: ['$config.hideSignUpOverlay', false],
            },
          },
          trackingPixels: 1,
          fullScreenBannerImgData: 1,
          coverMediaItems: 1,
          backgroundImage: '$fullScreenBannerImgData.mobileImgProps.src', // legacy. https://nasdaily.sg.larksuite.com/wiki/TFmrwqNG8iY23YkP0FiujZEMshY
          platform: { $arrayElemAt: ['$platforms', 0] },
          profileImage: {
            $ifNull: [
              '$communityCheckoutCardData.imgData.mobileImgProps.src',
              DEFAULT_COMMUNITY_PROFILE_IMAGE,
            ],
          },
          isPaidCommunity: { $ifNull: ['$isPaidCommunity', false] },
          metadata: {
            title: { $concat: ['$title', ' | Nas.io Communities'] },
            description: {
              $concat: ['Join ', '$title', ', hosted by ', '$By'],
            },
            imageUrl: {
              $ifNull: [
                '$fullScreenBannerImgData.mobileImgProps.src',
                DEFAULT_COMMUNITY_META_IMAGE,
              ],
            },
          },
          featurePermissions: 1,
          communityReferralCode: 1,
          config: 1,
          createdAt: 1,
          indexable: 1,
          host: {
            name: '$By',
          },
          restrictedInfo: 1,
        },
      },
    });

  if (!community) {
    throw new ParamError('Community not found with given slug');
  }

  if (!userFromRequest) {
    delete community.platform?.link;
  } else {
    const activeSubscription = await getActiveSubscriptionFromEmail({
      email: userFromRequest.email,
      communityCode: community.code,
    });
    if (!activeSubscription) {
      delete community.platform?.link;
    }
  }

  const existApplicationQuestion =
    community.applicationConfigDataFields?.find(
      (element) => !element.isDeleted
    );
  community.applicationConfig.hasApplication = !!existApplicationQuestion;
  delete community.applicationConfigDataFields;

  const now = DateTime.utc();

  const [
    affiliateInfo,
    memberCountInfo,
    pricingData,
    upcomingEventCount,
    pastEventCount,
    digitalProductCount,
    sessionCount,
    challengeCount,
    annoucementCount,
    coverMediaItems,
  ] = await Promise.all([
    affiliateService.retrieveAffiliateInfo({
      communityObjectId: community._id,
      affiliateCode,
      entityObjectId: community._id,
      entityType: commonConstants.PURCHASE_TYPE.SUBSCRIPTION,
    }),
    membershipService.countService.countCommunityMembers({
      communityId: community._id,
    }),
    getCommunityPricingDataWrapper({ community, ip }),
    // TODO: Event Draft - To test flow here
    CommunityEvent.countDocuments({
      communities: community._id,
      status: {
        $in: [EVENT_STATUS.PUBLISHED, 'Active'],
      },
      endTime: { $gt: now },
    }),
    CommunityEvent.countDocuments({
      communities: community._id,
      status: {
        $in: [EVENT_STATUS.PUBLISHED, 'Active'],
      },
      endTime: { $lte: now },
    }),
    CommunityFolder.countDocuments({
      communityObjectId: community._id,
      status: legacyCommunityConstants.COMMUNITY_FOLDER_STATUS.PUBLISHED,
      type: {
        $in: [
          legacyCommunityConstants.communityFolderTypesMap.DIGITAL_PRODUCT,
          legacyCommunityConstants.communityFolderTypesMap.COURSE,
        ],
      },
    }),
    CommunityFolder.countDocuments({
      communityObjectId: community._id,
      status: legacyCommunityConstants.COMMUNITY_FOLDER_STATUS.PUBLISHED,
      type: legacyCommunityConstants.communityFolderTypesMap.SESSION,
    }),
    Program.countDocuments({
      communityObjectId: community._id,
      status: programConstants.PROGRAM_STATUS.PUBLISHED,
      type: programConstants.PROGRAM_TYPE.CHALLENGE,
    }),
    CommunityPost.countDocuments({
      communities: community._id,
      isAnnouncement: true,
    }),
    generateCoverMediaItems({
      entity: community,
      entityType: COVER_MEDIA_ENTITY_TYPES.COMMUNITY,
      isCommunityManager: false,
    }),
  ]);

  const hideProducts = (value) => {
    if (!community.config?.hideProducts) {
      return value;
    }

    if (typeof value === 'boolean') {
      return false;
    }

    if (typeof value === 'number') {
      return 0;
    }

    return value;
  };

  const planType = community.config?.planType;
  const featurePermissions = community.featurePermissions || [];

  const featurePermissionManager = new FeaturePermissionManager(
    planType,
    featurePermissions
  );
  const memberOnlyCount = memberCountInfo.summary.memberCount ?? 0;
  const memberOnlyLimit = featurePermissionManager.getFeatureLimit(
    commonConstants.FEATURE_LIST_ID.MEMBER
  );
  community.totalMemberCount = memberCountInfo.count;
  community.memberCount = memberCountInfo.count;
  community.hasMaxedMemberLimit = memberOnlyCount >= memberOnlyLimit;
  community.affiliateInfo = affiliateInfo;

  if (pricingData) {
    community.pricingData = pricingData;
  }
  community.hasEvents = hideProducts(
    upcomingEventCount > 0 || pastEventCount > 0
  );
  community.hasUpcomingEvents = hideProducts(upcomingEventCount > 0);
  community.hasPastEvents = hideProducts(pastEventCount > 0);
  community.hasProducts = hideProducts(
    digitalProductCount > 0 || sessionCount > 0
  );
  community.hasDigitalProducts = hideProducts(digitalProductCount > 0);
  community.hasSessions = hideProducts(sessionCount > 0);
  community.hasChallenges = hideProducts(challengeCount > 0);
  community.hasChat = !!community.platform;
  community.detailsCount = {
    upcomingEvents: hideProducts(upcomingEventCount),
    pastEvents: hideProducts(pastEventCount),
    digitalProducts: hideProducts(digitalProductCount),
    sessions: hideProducts(sessionCount),
    challenges: hideProducts(challengeCount),
    annoucements: hideProducts(annoucementCount),
  };
  community.coverMediaItems = coverMediaItems;
  community.indexable = isCommunityIndexable(community);

  return community;
};

const getLatestProduct = (
  communityPost,
  communitySession,
  communityFolder
) => {
  const items = [
    { type: 'communityPost', data: communityPost },
    { type: 'communitySession', data: communitySession },
    { type: 'communityFolder', data: communityFolder },
  ];

  const sortedItems = items
    .filter((item) => item.data && item.data.createdAt) // Exclude if data is undefined or createdAt is missing
    .sort(
      (a, b) => new Date(b.data.createdAt) - new Date(a.data.createdAt)
    );

  return sortedItems.reduce((acc, item, index) => {
    acc[item.type] = index;
    return acc;
  }, {});
};

const getCommunityEntitiesInfo = async (communityId, learnerObjectId) => {
  const config = {
    order: {
      post: 0,
      session: 0,
      folder: 0,
    },
    totals: {
      communityPost: 0,
      communitySession: 0,
      communityFolder: 0,
    },
  };

  const communityInfo = await CommunityModel.findOne({
    _id: new ObjectId(communityId),
  })
    .select({
      code: 1,
    })
    .lean();

  const postAggregatePipeline = [
    {
      $match: {
        communities: new ObjectId(communityId),
        $or: [{ status: { $exists: false } }, { status: 'approved' }],
      },
    },
    { $sort: { isPinned: -1, createdAt: -1 } },
    { $limit: 3 },
    {
      $lookup: {
        from: 'users',
        localField: 'author',
        foreignField: '_id',
        as: 'author',
      },
    },
    {
      $unwind: {
        path: '$author',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: 'learners',
        localField: 'author.learner',
        foreignField: '_id',
        as: 'learner',
      },
    },
    {
      $unwind: {
        path: '$learner',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $addFields: {
        profileImage: '$learner.profileImage',
        authorName: {
          $concat: [
            '$learner.firstName',
            { $cond: [{ $eq: ['$learner.lastName', ''] }, '', ' '] },
            '$learner.lastName',
          ],
        },
      },
    },
    {
      $lookup: {
        from: 'community_magic_reach_email',
        localField: '_id',
        foreignField: 'sentResults.AnnouncementV2.announcementObjectId',
        as: 'emailSentData',
      },
    },
    {
      $unwind: {
        path: '$emailSentData',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $addFields: {
        totalImpressions: {
          $add: [
            { $ifNull: ['$impressions', 0] },
            {
              $ifNull: [
                '$emailSentData.analyticsData.Email.totalOpens',
                0,
              ],
            },
          ],
        },
      },
    },
    ...lookupAndUnwind(
      'program',
      'mentionedProducts.productObjectId',
      '_id',
      'programInfo'
    ),
    ...lookupAndUnwind(
      'community_folders',
      'mentionedProducts.productObjectId',
      '_id',
      'foldersInfo'
    ),
    ...lookupAndUnwind(
      'community_events',
      'mentionedProducts.productObjectId',
      '_id',
      'eventsInfo'
    ),
    {
      $lookup: {
        from: 'community_subscriptions',
        let: {
          communityCode: communityInfo.code,
          learnerObjectId: '$author.learnerObjectId',
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ['$communityCode', '$$communityCode'] },
                  { $eq: ['$learnerObjectId', '$$learnerObjectId'] },
                  {
                    $or: [
                      {
                        $eq: [
                          '$status',
                          commonConstants.communityEnrolmentStatuses
                            .CURRENT,
                        ],
                      },
                      {
                        $and: [
                          {
                            $eq: [
                              '$status',
                              commonConstants.communityEnrolmentStatuses
                                .CANCELLED,
                            ],
                          },
                          { $gte: ['$cancelledAt', new Date()] },
                        ],
                      },
                    ],
                  },
                ],
              },
            },
          },
        ],
        as: 'author.subscriptionInfo',
      },
    },
    {
      $addFields: {
        'mentionedProducts.productInfo': {
          $mergeObjects: ['$programInfo', '$foldersInfo', '$eventsInfo'],
        },
        postedBy: {
          $ifNull: [
            '$postedBy',
            legacyCommunityConstants.COMMUNITY_POSTS_POSTED_BY.CREATOR,
          ],
        },
        programInfo: 0,
        foldersInfo: 0,
        eventsInfo: 0,
      },
    },
    {
      $addFields: {
        mentionedProducts: {
          $cond: {
            if: {
              $or: [
                {
                  $eq: ['$mentionedProducts.productInfo', {}],
                },
                {
                  $not: '$mentionedProducts',
                },
              ],
            },
            then: [],
            else: '$mentionedProducts',
          },
        },
      },
    },
  ];

  const folderAggregatePipeline = [
    {
      $match: {
        communityObjectId: new ObjectId(communityId),
        status: legacyCommunityConstants.COMMUNITY_FOLDER_STATUS.PUBLISHED,
        type: legacyCommunityConstants.communityFolderTypesMap
          .DIGITAL_PRODUCT,
      },
    },
    {
      $sort: { createdAt: -1 },
    },
    {
      $limit: 3,
    },
    {
      $lookup: {
        from: 'community_folder_purchases',
        let: { id: '$_id' },
        pipeline: [
          {
            $match: {
              $and: [
                { $expr: { $eq: ['$folderObjectId', '$$id'] } },
                {
                  $expr: {
                    $eq: [
                      '$learnerObjectId',
                      new ObjectId(learnerObjectId),
                    ],
                  },
                },
              ],
            },
          },
        ],
        as: 'purchases',
      },
    },
    {
      $addFields: {
        isPurchased: {
          $cond: {
            if: { $eq: ['$access', 'free'] },
            then: true,
            else: { $toBool: { $size: '$purchases' } },
          },
        },
      },
    },
  ];
  const [communityPosts, communityFolders, communitySessions] =
    await Promise.all([
      CommunityPostModel.aggregate(postAggregatePipeline),
      CommunityFolder.aggregate(folderAggregatePipeline),
      CommunityFolder.find({
        communityObjectId: communityId,
        status: legacyCommunityConstants.COMMUNITY_FOLDER_STATUS.PUBLISHED,
        type: legacyCommunityConstants.communityFolderTypesMap.SESSION,
      })
        .populate('hostInfo.hostLearnerObjectId')
        .sort({ createdAt: -1 })
        .limit(3)
        .lean(),
    ]);

  [
    config.totals.communityPost,
    config.totals.communitySession,
    config.totals.communityFolder,
  ] = await Promise.all([
    CommunityPostModel.countDocuments({ communities: communityId }),
    CommunityFolder.countDocuments({
      communityObjectId: communityId,
      status: legacyCommunityConstants.COMMUNITY_FOLDER_STATUS.PUBLISHED,
      type: legacyCommunityConstants.communityFolderTypesMap.SESSION,
    }),
    CommunityFolder.countDocuments({
      communityObjectId: communityId,
      status: legacyCommunityConstants.COMMUNITY_FOLDER_STATUS.PUBLISHED,
      type: legacyCommunityConstants.communityFolderTypesMap
        .DIGITAL_PRODUCT,
    }),
  ]);

  config.order = getLatestProduct(
    communityPosts?.[0],
    communitySessions?.[0],
    communityFolders?.[0]
  );

  const subscriptionObjectIds = [];
  const eventObjectIds = [];
  const productObjectIds = [];
  const programObjectIds = [];

  communityPosts.forEach((post) => {
    const mentionedProducts = post.mentionedProducts ?? [];
    mentionedProducts.forEach((product) => {
      switch (product.type) {
        case commonConstants.PURCHASE_TYPE.SUBSCRIPTION:
          subscriptionObjectIds.push(product.productObjectId);
          break;
        case commonConstants.PURCHASE_TYPE.CHALLENGE:
          programObjectIds.push(product.productObjectId);
          break;
        case commonConstants.PURCHASE_TYPE.EVENT:
          eventObjectIds.push(product.productObjectId);
          break;
        case commonConstants.PURCHASE_TYPE.SESSION:
        case commonConstants.PURCHASE_TYPE.FOLDER:
          productObjectIds.push(product.productObjectId);
          break;
        default:
          break;
      }
    });
  });

  const existingProductCache = await productService.retrieveEntitiesCache({
    subscriptionObjectIds,
    eventObjectIds,
    productObjectIds,
    programObjectIds,
    source: productService.SOURCE_TYPES.MAGIC_REACH,
  });

  const newPosts = communityPosts.map((post) => {
    const mentionedProducts = post.mentionedProducts ?? [];
    mentionedProducts.forEach((product, index) => {
      const { type, productObjectId } = product;
      const key = `${type}-${productObjectId}`;
      const productInfo = existingProductCache.get(key);
      mentionedProducts[index].productInfo = productInfo ?? {};
    });
    return {
      ...post,
      mentionedProducts,
    };
  });

  return {
    entityData: {
      communityPosts: newPosts,
      communitySessions,
      communityFolders,
    },
    config,
  };
};

const getCommunityMembers = async ({
  searchString,
  currentUser,
  communityId,
  otherFilters,
  skip,
  limit,
}) => {
  // Get community owner and manager
  const owner = await CommunityRoleModel.findOne({
    communityObjectId: communityId,
    role: aclRoles.OWNER,
  })
    .select('role email')
    .lean();

  // Query the Learner model using the array of emails
  const ownerLearner = await Learner.findOne({
    email: owner.email,
  })
    .select('_id socialMedia description email')
    .lean();

  const excludeOwnerAndCurrentUserQuery = {
    mustNot: [
      {
        in: {
          path: 'learnerObjectId',
          value: [
            ownerLearner._id,
            mongodbUtils.toObjectId(currentUser.learner._id),
          ],
        },
      },
    ],
  };

  const membershipProjection = {
    profileImage: 1,
    name: 1,
    email: 1,
    learnerObjectId: 1,
    countryInfo: 1,
    'subscriptionInfo.signUpDate': 1,
    communityRole: 1,
  };

  // Result here will always exclude owner and current user
  const results =
    await membershipService.getService.getCommunityMembersForLandingPage({
      communityId,
      searchString,
      otherFilters,
      skip,
      limit,
      getBufferData: true,
      status: [
        membershipConstants.MEMBERSHIP_STATUS.SUBSCRIBED,
        membershipConstants.MEMBERSHIP_STATUS.NOT_ON_NASIO,
      ],
      projection: membershipProjection,
      extraConditionsParam: excludeOwnerAndCurrentUserQuery,
    });

  const learnerIds = results.members.map(
    (member) => member.learnerObjectId
  );

  if (skip === 0) {
    learnerIds.push(mongodbUtils.toObjectId(currentUser.learner._id));
  }
  const learners = await Learner.find(
    { _id: { $in: learnerIds } },
    {
      _id: 1,
      socialMedia: 1,
      description: 1,
      email: 1,
    }
  ).lean();

  const learnerMap = learners.reduce((acc, learner) => {
    acc[learner._id.toString()] = learner;
    return acc;
  }, {});

  const processedMembers = results.members.map((member) => {
    const { email, ...rest } = member;

    if (!member.learnerObjectId) {
      return rest;
    }
    const socialMedia =
      learnerMap[member.learnerObjectId.toString()]?.socialMedia;
    const bio = learnerMap[member.learnerObjectId.toString()]?.description;

    return {
      ...rest,
      socialMedia,
      bio,
    };
  });

  if (skip === 0) {
    if (searchString) {
      // When the search string is given, will check if owner/current user has same pattern in search string
      const ownerMemberData =
        await membershipService.getService.getCommunityMembersBySearchBasic(
          {
            community: { _id: mongodbUtils.toObjectId(communityId) },
            searchString,
            extraConditions: {
              filter: [
                {
                  equals: {
                    path: 'learnerObjectId',
                    value: ownerLearner._id,
                  },
                },
              ],
            },
            limit: 1,
            status: [
              membershipConstants.MEMBERSHIP_STATUS.SUBSCRIBED,
              membershipConstants.MEMBERSHIP_STATUS.NOT_ON_NASIO,
            ],
            projection: membershipProjection,
          }
        );
      if (ownerMemberData && ownerMemberData.length > 0) {
        const { email, ...rest } = ownerMemberData[0];

        results.owner = {
          ...rest,
          socialMedia: ownerLearner?.socialMedia,
          bio: ownerLearner?.description,
        };
      }

      const currentMemberData =
        await membershipService.getService.getCommunityMembersBySearchBasic(
          {
            community: { _id: mongodbUtils.toObjectId(communityId) },
            searchString,
            extraConditions: {
              filter: [
                {
                  equals: {
                    path: 'learnerObjectId',
                    value: mongodbUtils.toObjectId(
                      currentUser.learner._id
                    ),
                  },
                },
              ],
            },
            limit: 1,
            status: [
              membershipConstants.MEMBERSHIP_STATUS.SUBSCRIBED,
              membershipConstants.MEMBERSHIP_STATUS.NOT_ON_NASIO,
            ],
            projection: membershipProjection,
          }
        );
      if (currentMemberData && currentMemberData.length > 0) {
        const currentUserMember = currentMemberData[0];
        results.currentUser = {
          ...currentUserMember,
          socialMedia:
            learnerMap[currentUserMember.learnerObjectId.toString()]
              ?.socialMedia,
          bio: learnerMap[currentUserMember.learnerObjectId.toString()]
            ?.description,
        };
      }
    } else {
      // Get owner data
      const ownerMemberData = await MembershipModel.findOne({
        email: owner.email,
        communityObjectId: mongodbUtils.toObjectId(communityId),
        primaryIdentifier: 'email',
        ...otherFilters,
      })
        .select(membershipProjection)
        .lean();

      if (ownerMemberData) {
        const { email, ...rest } = ownerMemberData;

        results.owner = {
          ...rest,
          socialMedia: ownerLearner?.socialMedia,
          bio: ownerLearner?.description,
        };
      }

      // Get current user data
      const currentUserMember = await MembershipModel.findOne({
        email: currentUser.email,
        communityObjectId: mongodbUtils.toObjectId(communityId),
        primaryIdentifier: 'email',
        ...otherFilters,
      })
        .select(membershipProjection)
        .lean();

      if (currentUserMember) {
        const { email, ...rest } = currentUserMember;

        results.currentUser = {
          ...rest,
          socialMedia:
            learnerMap[currentUserMember.learnerObjectId.toString()]
              ?.socialMedia,
          bio: learnerMap[currentUserMember.learnerObjectId.toString()]
            ?.description,
        };
      }
    }
    // for backward compatibility that always return current user in first item
    if (
      results.currentUser &&
      results.owner &&
      results.currentUser.learnerObjectId.toString() ===
        results.owner.learnerObjectId.toString()
    ) {
      results.members = [results.currentUser, ...processedMembers];
    } else {
      results.members = [
        ...(results.currentUser ? [results.currentUser] : []),
        ...(results.owner ? [results.owner] : []),
        ...processedMembers,
      ];
    }
  }

  return results;
};

const getCommunityMember = async ({
  communityObjectId,
  learnerObjectId,
}) => {
  const member =
    await membershipService.getService.getCommunityMemberForLandingPage({
      communityObjectId,
      learnerObjectId,
      projection: {
        profileImage: 1,
        name: 1,
        email: 1,
        learnerObjectId: 1,
        countryInfo: 1,
        'subscriptionInfo.signUpDate': 1,
      },
    });

  if (!member) {
    throw new ResourceNotFoundError('Member not found');
  }

  const learner = await Learner.findOne(
    { _id: member.learnerObjectId },
    {
      _id: 1,
      socialMedia: 1,
      description: 1,
      email: 1,
    }
  ).lean();

  member.socialMedia = learner?.socialMedia;
  member.bio = learner?.description;

  return member;
};

function buildProductSearchStage({ communityId, type }) {
  const pipeline = [];

  const matchStage = {
    $match: {
      communityObjectId: new ObjectId(communityId),
      status: legacyCommunityConstants.COMMUNITY_FOLDER_STATUS.PUBLISHED,
    },
  };

  // If `type` is specified, add it to $match
  if (type) {
    matchStage.$match.type = type;
  }

  pipeline.push(matchStage);

  // Sorting
  pipeline.push({
    $sort: {
      index: 1,
      createdAt: -1,
    },
  });

  return pipeline;
}

function buildProductSearchPipeline({
  communityId,
  type,
  projection = {
    title: 1,
    type: 1,
    index: 1,
    description: 1,
    communityObjectId: 1,
    hostInfo: 1,
    status: 1,
    access: 1,
    resourceSlug: 1,
    countryWisePrice: 1,
    amount: 1,
    currency: 1,
    thumbnail: 1,
    location: 1,
    durationIntervalInMinutes: 1,
    pricingConfig: 1,
  },
  skip,
  limit,
}) {
  const searchStage = buildProductSearchStage({
    communityId,
    type,
  });
  const pipeline = [
    ...searchStage,
    {
      $project: projection,
    },
    {
      $skip: skip,
    },
    {
      $limit: limit,
    },
  ];
  return pipeline;
}

function buildProductSearchMetaPipeline({ communityId, type }) {
  const searchStage = buildProductSearchStage({
    communityId,
    type,
  });
  const pipeline = [
    ...searchStage,
    {
      $count: 'total',
    },
  ];
  return pipeline;
}

async function processProductsForUser({ products, userFromRequest }) {
  const processedProducts = [];
  const learner = await getLearner(userFromRequest?.email);
  const digitalProducts = products.filter(
    (product) =>
      product.type ===
      legacyCommunityConstants.communityFolderTypesMap.DIGITAL_PRODUCT
  );
  const sessions = products.filter(
    (product) =>
      product.type ===
      legacyCommunityConstants.communityFolderTypesMap.SESSION
  );

  const purchasedDigitalProducts = await CommunityFolderPurchase.find(
    {
      learnerObjectId: learner._id,
      folderObjectId: {
        $in: digitalProducts.map((product) => product._id),
      },
    },
    { folderObjectId: 1 }
  ).lean();
  const purchasedDigitalProductsSet = new Set(
    purchasedDigitalProducts.map((product) =>
      product.folderObjectId.toString()
    )
  );

  const sessionAttendees = await SessionAttendee.find(
    {
      sessionObjectId: {
        $in: sessions.map((session) => session._id),
      },
      attendeeLearnerObjectId: learner._id,
    },
    {
      createdAt: 0,
      updatedAt: 0,
      statusHistory: 0,
      applicationInfo: 0,
      reasonForRefund: 0,
      checkoutId: 0,
    }
  );
  const sessionAttendeesMap = sessionAttendees.reduce((acc, session) => {
    acc[session.sessionObjectId] = session;
    return acc;
  }, {});

  products.forEach((product) => {
    const processedProduct = {
      ...product,
    };
    switch (product.type) {
      case legacyCommunityConstants.communityFolderTypesMap.SESSION: {
        const attendeeBooking = sessionAttendeesMap[product._id];
        if (attendeeBooking) {
          processedProduct.attendeeBooking = attendeeBooking;
        }
        break;
      }
      case legacyCommunityConstants.communityFolderTypesMap
        .DIGITAL_PRODUCT:
        processedProduct.isPurchased = purchasedDigitalProductsSet.has(
          product._id.toString()
        );
        break;
      default:
        break;
    }
    processedProducts.push(processedProduct);
  });
  return processedProducts;
}

const getCommunityProducts = async ({
  communityId,
  userFromRequest,
  type,
  skip,
  limit,
}) => {
  if (!communityId) {
    throw new ParamError('Community ID is required');
  }

  const [products, meta] = await Promise.all([
    CommunityFolder.aggregate(
      buildProductSearchPipeline({
        communityId,
        type,
        skip,
        limit,
      })
    ),
    CommunityFolder.aggregate(
      buildProductSearchMetaPipeline({
        communityId,
        type,
      })
    ),
  ]);
  if (!meta?.length) {
    return {
      products: [],
      meta: {
        total: 0,
      },
    };
  }
  const metaToReturn = {
    total: meta[0].total,
    limit,
    page: skip / limit + 1,
    pages: Math.ceil(meta[0].total / limit),
  };

  if (userFromRequest) {
    const processedProducts = await processProductsForUser({
      products,
      userFromRequest,
      communityId,
    });
    return {
      products: processedProducts,
      meta: metaToReturn,
    };
  }

  return {
    products,
    meta: metaToReturn,
  };
};

const getCommunityLandingPageDynamicData = async ({
  communityId,
  userFromRequest,
}) => {
  const learner = await getLearner(userFromRequest?.email);
  const community = await getCommunityBrief({ communityId });

  if (community.config?.hideProducts) {
    return {
      upcomingItems: [],
      ongoingItems: [],
    };
  }

  const [
    upcomingChallenges,
    upcomingEvents,
    upcomingSessions,
    ongoingChallenges,
  ] = await Promise.all([
    programService.getProgramsService.getUpcomingChallenges({
      community,
      learner,
      limit: 10,
      skip: 0,
    }),
    eventService.getUpcomingEventsByCommunityId(
      learner?._id,
      userFromRequest?._id,
      communityId,
      1,
      1,
      10
    ),
    sessionService.getCommunityUpcomingBookings({
      userFromRequest,
      communityObjectId: community._id,
    }),
    programService.getProgramsService.getOngoingChallenges({
      community,
      learner,
      limit: 10,
      skip: 0,
    }),
  ]);

  const upcomingList = [];
  if (upcomingChallenges?.results) {
    upcomingChallenges.results.forEach((challenge) => {
      upcomingList.push({
        ...challenge,
        itemType: 'challenge',
      });
    });
  }
  if (upcomingEvents?.data) {
    upcomingEvents.data.forEach((event) => {
      upcomingList.push({
        ...event,
        itemType: 'event',
      });
    });
  }
  upcomingSessions.forEach((session) => {
    upcomingList.push({
      ...session,
      itemType: 'session',
    });
  });

  const ongoingList = [];
  if (ongoingChallenges?.results) {
    ongoingChallenges.results.forEach((challenge) => {
      ongoingList.push({
        ...challenge,
        itemType: 'challenge',
      });
    });
  }

  const getStartTime = (item) => {
    if (item.startTime !== undefined) {
      return item.startTime;
    }
    return item.sessionStartTime;
  };

  upcomingList.sort((a, b) => {
    const startTimeA = getStartTime(a);
    const startTimeB = getStartTime(b);
    return startTimeA - startTimeB;
  });
  ongoingList.sort((a, b) => {
    const startTimeA = getStartTime(a);
    const startTimeB = getStartTime(b);
    return startTimeA - startTimeB;
  });

  return {
    upcomingItems: upcomingList.slice(0, 10),
    ongoingItems: ongoingList.slice(0, 10),
  };
};

const getCountriesInCommunity = async ({
  community,
  status,
  maxTimeMS,
}) => {
  let countryMapping = community.memberCountries;
  if (!countryMapping || countryMapping?.length === 0) {
    countryMapping = await CountryCurrencyMappingModel.find({}).lean();
    countryMapping = countryMapping.map((country) => {
      return {
        id: country.countryId,
        code: country.countryCode,
        name: country.country,
      };
    });
  }
  const countries = [];
  const countriesWithZeroCount = [];
  await Promise.all(
    countryMapping.map(async (country) => {
      const pipeline =
        membershipService.membershipSearchUtils.buildSearchMetaPipeline({
          community,
          status,
          otherFilters: {
            'countryInfo.id': country.id,
          },
          returnStoredSource: true,
        });
      const countryMeta = await MembershipModel.aggregate(pipeline)
        .option({ maxTimeMS })
        .read('sp');

      if (countryMeta[0].count.total > 0) {
        countries.push({
          ...country,
          count: countryMeta[0].count.total,
        });
      } else if (
        community.memberCountries &&
        community.memberCountries.length !== 0
      ) {
        countriesWithZeroCount.push(country);
      }
    })
  );
  return { countries, countriesWithZeroCount };
};

const getTopFourCountries = (sortedCountries, totalMembers) => {
  const topFourCountries = [];
  let totalTopThree = 0;

  const rounds = sortedCountries.length > 4 ? 4 : sortedCountries.length;

  for (let i = 0; i < rounds; i++) {
    let count = sortedCountries[i].count;
    let name = sortedCountries[i].name;
    let code = sortedCountries[i].code;
    if (i !== 3) {
      totalTopThree += sortedCountries[i].count;
    } else if (sortedCountries.length !== 4) {
      count = totalMembers - totalTopThree;
      name = 'Rest of the world';
      code = null;
    }
    topFourCountries.push({
      code,
      name,
      percentage: Math.round((count / totalMembers) * 100),
    });
  }

  return topFourCountries;
};

const getTopFourProfileImage = async ({
  randomProfileImages,
  community,
  status,
  maxTimeMS,
}) => {
  const randomProfileImagesSet = new Set();
  randomProfileImages.forEach((i) =>
    randomProfileImagesSet.add(i.imageLink)
  );
  const searchPipeline =
    membershipService.membershipSearchUtils.buildSearchPipeline({
      community,
      status,
      projection: { profileImage: 1 },
      skip: 0,
      limit: 10,
      returnStoredSource: true,
    });
  const members = await MembershipModel.aggregate(searchPipeline)
    .option({ maxTimeMS })
    .read('sp');

  const topFourProfileImage = [];
  const topFourDefaultProfileImage = [];
  members.forEach((member) => {
    if (topFourProfileImage.length < 4) {
      if (!randomProfileImagesSet.has(member.profileImage)) {
        topFourProfileImage.push(member.profileImage);
      } else {
        topFourDefaultProfileImage.push(member.profileImage);
      }
    }
  });

  return [...topFourProfileImage, ...topFourDefaultProfileImage].slice(
    0,
    4
  );
};

const getCommunityMemberSummary = async ({ communityId }) => {
  const community = await Community.findOne({ _id: communityId }).lean();
  if (!community) {
    throw new ParamError('Community not found');
  }

  const status = [
    membershipConstants.MEMBERSHIP_STATUS.SUBSCRIBED,
    membershipConstants.MEMBERSHIP_STATUS.NOT_ON_NASIO,
  ];

  const searchMetaPipeline =
    membershipService.membershipSearchUtils.buildSearchMetaPipeline({
      community,
      status,
    });
  const maxTimeMS = getMongoServerTimeoutConfig({
    collectionName: 'membership',
    operation: 'aggregate',
  });

  const [meta, randomProfileImages] = await Promise.all([
    MembershipModel.aggregate(searchMetaPipeline)
      .option({ maxTimeMS })
      .read('sp'),
    Config.find({
      type: 'randomProfileImage',
    }).lean(),
  ]);

  if (!meta?.length) {
    throw DBError('Search members meta invalid');
  }
  let countries = [];

  countries = (community.memberCountries ?? []).filter(
    (country) => country.count > 0
  );

  countries.sort((a, b) => b.count - a.count);
  const totalCountries = countries.length;
  const totalMembers = meta[0].count.total;

  const topFourCountries = getTopFourCountries(countries, totalMembers);
  const topFourProfileImage = await getTopFourProfileImage({
    randomProfileImages,
    community,
    status,
    maxTimeMS,
  });

  return {
    summary: {
      totalMembers,
      totalCountries,
      topFourCountries,
      topFourProfileImage,
    },
    countries,
  };
};

module.exports = {
  getCommunityUpcoming,
  getCommunityLandingPageData,
  getCommunityMembers,
  getCommunityProducts,
  getCommunityLandingPageDynamicData,
  getCommunityEntitiesInfo,
  getCommunityMemberSummary,
  getCommunityMember,
  getCountriesInCommunity,
};
