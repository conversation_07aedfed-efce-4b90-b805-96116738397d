const PrimaryMongooseConnection = require('@src/rpc/primaryMongooseConnection');

const CommunityModel = require('../../communitiesAPI/models/community.model');
const FeaturePermissionManager = require('../common/featurePermissionManager.service');

const storageUsageService = require('./storageUsage.service');
const magicReachUsageService = require('./magicReachUsage.service');
const membershipCountService = require('../membership/count.service');
const aiCofounderMessageService = require('../aiCofounder/message.service');
const grandFatherService = require('./grandfather.service');

const { ParamError } = require('../../utils/error.util');
const {
  FEATURE_LIST_ID,
  FEATURE_LIST_NAME,
  FEATURE_LIST_ID_TO_NAME,
} = require('../../constants/common');

async function retrieveFeatureUsage({
  limit,
  featureId,
  community,
  membershipUsageSummary,
}) {
  let usage = 0;
  switch (featureId) {
    case FEATURE_LIST_ID.MANAGER: {
      if (membershipUsageSummary) {
        usage = membershipUsageSummary.managerUsage;
      } else {
        usage = await membershipCountService.retrieveManagerUsage({
          communityId: community._id,
        });
      }
      break;
    }
    case FEATURE_LIST_ID.MEMBER: {
      if (membershipUsageSummary) {
        usage = membershipUsageSummary.memberUsage;
      } else {
        usage = await membershipCountService.retrieveMemberUsage({
          communityId: community._id,
        });
      }
      break;
    }
    case FEATURE_LIST_ID.STORAGE: {
      usage = community.currentStorageUsageInBytes;
      if (!community.currentStorageUsageInBytes) {
        usage = await storageUsageService.getCommunityStorageUsage(
          community._id
        );
      }
      usage = storageUsageService.convertBytesToGB(usage);
      break;
    }
    case FEATURE_LIST_ID.MAGIC_REACH_WHATSAPP_MSG: {
      usage = await magicReachUsageService.getCommunityMagicReachUsage(
        community._id
      );
      break;
    }
    case FEATURE_LIST_ID.AI_COFOUNDER_MESSAGE: {
      usage = await aiCofounderMessageService.retrieveMessageUsage({
        communityObjectId: community._id,
        limit,
      });
      break;
    }
    default:
      break;
  }
  return usage;
}

/**
 * Get the feature permissions configurations with usage
 * @param {Array} features // retrieved from featurePermissionManager.getAllFeaturesDetailed()
 * @param {Object} community community object. Should include currentStorageUsageInBytes field
 * @returns
 */
async function retrieveFeaturePermissionsWithUsage(
  features = [],
  community
) {
  const membershipUsageSummary =
    await membershipCountService.retrieveMembershipUsageSummary({
      communityId: community._id,
    });
  const updatedFeatures = await Promise.all(
    features.map(async (feature) => {
      if (!feature.allowed) {
        return feature;
      }
      const usage = await retrieveFeatureUsage({
        limit: feature.limit ?? 0,
        featureId: feature.featureId,
        community,
        membershipUsageSummary,
      });
      return { ...feature, usage };
    })
  );
  return updatedFeatures;
}

/**
 * Get ALL feature permissions Type configurations
 * @returns {Object} ALL feature permissions Type configurations
 */
exports.getFeaturePlanPermissions = async () => {
  const featurePermissionSettings =
    FeaturePermissionManager.retrieveFeaturePermissionDefaultConfigs();
  Object.keys(featurePermissionSettings).forEach(
    (featurePermissionType) => {
      const finalConfigurations = {};
      const configurations =
        featurePermissionSettings[featurePermissionType];

      Object.keys(configurations).forEach((featureId) => {
        const featureName = FEATURE_LIST_ID_TO_NAME[featureId];
        finalConfigurations[featureName] = {
          featureName,
          ...configurations[featureId],
        };
      });

      featurePermissionSettings[featurePermissionType] =
        finalConfigurations;
    }
  );

  return featurePermissionSettings;
};

/**
 * Update grandfather feature permissions.
 * @param {String} communityId
 * @param {Array} payload.features array of updated grandfather feature permissions.
 * @returns {Object} An object containing the feature permission type, features, and plan type.
 */
exports.updateGrandfatherFeaturePermissions = async (
  communityId,
  payload = {}
) => {
  const community = await CommunityModel.findById(communityId, {
    _id: 1,
    config: 1,
    featurePermissions: 1,
    currentStorageUsageInBytes: 1,
  }).lean();

  if (!community) {
    throw new ParamError('Community not found');
  }
  const { features = [] } = payload;

  const existingGrandfatherFeatureMap = new Map();
  community.featurePermissions.forEach((permission) => {
    existingGrandfatherFeatureMap.set(permission.featureName, permission);
  });

  const featurePermissionManager = new FeaturePermissionManager(
    community.config.planType
  );
  const updatedFeatures = [];
  let updatedCommunity;

  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();
  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  try {
    await Promise.all(
      features.map(async (feature) => {
        let featureNeedsUpdate = false;
        const { featureName } = feature;
        const existingGrandfatherFeature =
          existingGrandfatherFeatureMap.get(featureName);
        if (!existingGrandfatherFeature) {
          featureNeedsUpdate = true;
        } else if (
          feature.allowed !== existingGrandfatherFeature.allowed
        ) {
          featureNeedsUpdate = true;
          existingGrandfatherFeatureMap.delete(featureName);
        }

        if (featureNeedsUpdate) {
          switch (featureName) {
            case FEATURE_LIST_NAME.AFFILIATES: {
              await grandFatherService.handleUpdateAffiliate({
                community,
                currentAllowed: featurePermissionManager.isFeatureAllowed(
                  FEATURE_LIST_ID.AFFILIATES
                ),
                allowed: feature.allowed,
                session,
              });
              break;
            }
            default:
              break;
          }
        }

        if (FEATURE_LIST_ID[featureName]) {
          updatedFeatures.push({
            featureId: FEATURE_LIST_ID[featureName],
            ...feature,
          });
        }
      })
    );

    updatedCommunity = await CommunityModel.updateOne(
      { _id: communityId },
      {
        featurePermissions: updatedFeatures,
      },
      {
        new: true,
        session,
      }
    );
    await session.commitTransaction();
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }

  return {
    updated: !!updatedCommunity.modifiedCount,
  };
};

/**
 * Get ALL feature permissions for a community based on its plan type and existing grandfather permissions.
 * @param {String} communityId
 * @returns {Object} An object containing the feature permission type, features, and plan type.
 */
exports.getCommunityPermissions = async (
  communityId,
  includeUsage = false
) => {
  const community = await CommunityModel.findById(communityId, {
    config: 1,
    featurePermissions: 1,
    currentStorageUsageInBytes: 1,
  }).lean();

  if (!community) {
    throw new ParamError('Community not found');
  }
  const planType = community.config?.planType;
  const featurePermissions = community.featurePermissions || [];

  const featurePermissionManager = new FeaturePermissionManager(
    planType,
    featurePermissions
  );

  const featurePermissionSettings =
    featurePermissionManager.getAllFeaturesDetailed();
  let features = featurePermissionSettings;
  if (includeUsage) {
    features = await retrieveFeaturePermissionsWithUsage(
      featurePermissionSettings,
      community
    );
  }

  return {
    featurePermissionType: featurePermissionManager.getFeaturePermType(),
    planType: community.config?.planType ?? 'FREE',
    features,
  };
};
