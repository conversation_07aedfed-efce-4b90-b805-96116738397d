const { ObjectId } = require('mongoose').Types;
const moment = require('moment');

const FeaturePermissionManager = require('@services/common/featurePermissionManager.service');
const { FEATURE_LIST_ID } = require('@constants/common');
const { FEATURE_PERMISSION_ERROR } = require('@constants/errorCode');
const logger = require('@services/logger.service.js');
const CommunityModel = require('@/src/communitiesAPI/models/community.model');
const CommunityMagicReachStats = require('@/src/models/magicReach/communityMagicReachStats.model');
const {
  COMMUNITY_STATS_WINDOW,
  PLATFORMS,
} = require('@/src/services/magicReach/constants');

class MagicReachUsageService {
  /**
   * Get current Magic Reach WhatsApp message usage for a community
   * @param {string} communityObjectId - Community ID
   * @returns {Promise<number>} Current usage count
   */
  static async getCommunityMagicReachUsage(communityObjectId) {
    try {
      const currentMonth = moment.utc().format('YYYY-MM');

      const stats = await CommunityMagicReachStats.findOne({
        communityId: new ObjectId(communityObjectId),
        platform: PLATFORMS.WHATSAPP,
        window: COMMUNITY_STATS_WINDOW.MONTHLY,
        currentMonth,
      }).lean();
      return stats?.totalSent || 0;
    } catch (error) {
      logger.error('Failed to get community Magic Reach usage', {
        error: error.message,
        communityId: communityObjectId,
      });
      throw error;
    }
  }

  /**
   * Check if community can send additional Magic Reach WhatsApp messages
   * @param {string} communityObjectId - Community ID
   * @param {number} additionalMessages - Number of additional messages to send
   * @returns {Promise<Object>} Check result with allowed status and limits
   */
  static async checkSendLimit(communityObjectId, additionalMessages = 0) {
    try {
      const community = await CommunityModel.findById(
        communityObjectId
      ).lean();
      if (!community) {
        throw new Error('Community not found');
      }

      const currentUsage = await this.getCommunityMagicReachUsage(
        communityObjectId
      );

      const featurePermissionManager = new FeaturePermissionManager(
        community.config?.planType,
        community.featurePermissions
      );

      const sendLimit = featurePermissionManager.getFeatureLimit(
        FEATURE_LIST_ID.MAGIC_REACH_WHATSAPP_MSG
      );
      const isSendAllowed = featurePermissionManager.isFeatureAllowed(
        FEATURE_LIST_ID.MAGIC_REACH_WHATSAPP_MSG
      );

      if (!isSendAllowed) {
        return {
          allowed: false,
          error:
            FEATURE_PERMISSION_ERROR.MAGIC_REACH_WHATSAPP_NOT_ALLOWED.name,
          message:
            'Magic Reach WhatsApp messaging feature not allowed for current plan',
          currentUsage,
          limit: 0,
        };
      }

      const totalUsageAfterSend = currentUsage + additionalMessages;

      // Check if limit is exceeded
      if (totalUsageAfterSend > sendLimit) {
        return {
          allowed: false,
          error:
            FEATURE_PERMISSION_ERROR.MAGIC_REACH_WHATSAPP_LIMIT_EXCEEDED
              .name,
          message: `Magic Reach WhatsApp message limit exceeded. You can send ${sendLimit} messages per month.`,
          currentUsage,
          limit: sendLimit,
          wouldExceedBy: totalUsageAfterSend - sendLimit,
        };
      }

      return {
        allowed: true,
        currentUsage,
        limit: sendLimit,
        remaining: sendLimit - currentUsage,
      };
    } catch (error) {
      logger.error('Failed to check Magic Reach send limit', {
        error: error.message,
        communityId: communityObjectId,
        additionalMessages,
      });
      throw error;
    }
  }

  /**
   * Get Magic Reach usage summary for a community
   * @param {string} communityObjectId - Community ID
   * @returns {Promise<Object>} Usage summary with current usage and limits
   */
  static async getUsageSummary(communityObjectId) {
    try {
      const community = await CommunityModel.findById(
        communityObjectId
      ).lean();
      if (!community) {
        throw new Error('Community not found');
      }

      const currentUsage = await this.getCommunityMagicReachUsage(
        communityObjectId
      );

      const featurePermissionManager = new FeaturePermissionManager(
        community.config?.planType,
        community.featurePermissions
      );

      const sendLimit = featurePermissionManager.getFeatureLimit(
        FEATURE_LIST_ID.MAGIC_REACH_WHATSAPP_MSG
      );
      const isSendAllowed = featurePermissionManager.isFeatureAllowed(
        FEATURE_LIST_ID.MAGIC_REACH_WHATSAPP_MSG
      );

      return {
        allowed: isSendAllowed,
        currentUsage,
        limit: sendLimit,
        remaining: Math.max(0, sendLimit - currentUsage),
      };
    } catch (error) {
      logger.error('Failed to get Magic Reach usage summary', {
        error: error.message,
        communityId: communityObjectId,
      });
      throw error;
    }
  }
}

module.exports = MagicReachUsageService;
