const { ObjectId } = require('mongoose').Types;

const FeaturePermissionManager = require('@services/common/featurePermissionManager.service');
const { FEATURE_LIST_ID } = require('@constants/common');
const {
  FOLDER_ITEM_COVER_MEDIA_FOLDER_TYPES,
} = require('@constants/coverMediaItems.constant');
const { FEATURE_PERMISSION_ERROR } = require('@constants/errorCode');
const logger = require('@services/logger.service.js');
const CommunityModel = require('@/src/communitiesAPI/models/community.model');
const CommunityFolderItems = require('@/src/communitiesAPI/models/communityFolderItems.model');
const CommunityFolders = require('@/src/communitiesAPI/models/communityFolders.model');
const {
  FOLDER_ITEM_STATUS,
  COMMUNITY_FOLDER_STATUS,
  communityFolderItemTypesMap,
} = require('@/src/communitiesAPI/constants');
const StorageReservationService = require('./storageReservation.service');

class StorageUsageService {
  static async getCommunityStorageUsage(communityObjectId) {
    try {
      const community = await CommunityModel.findById(
        communityObjectId
      ).lean();
      if (!community) {
        throw new Error('Community not found');
      }

      if (
        community.currentStorageUsageInBytes !== null &&
        community.currentStorageUsageInBytes !== undefined
      ) {
        return community.currentStorageUsageInBytes;
      }

      const calculatedUsage = await this.calculateStorageUsageFromFiles(
        communityObjectId
      );

      await CommunityModel.updateOne(
        { _id: communityObjectId },
        { currentStorageUsageInBytes: calculatedUsage }
      );

      return calculatedUsage;
    } catch (error) {
      logger.error('Failed to get community storage usage', {
        error: error.message,
        communityId: communityObjectId,
      });
      throw error;
    }
  }

  static async calculateStorageUsageFromFiles(communityObjectId) {
    try {
      // Get deleted folders to exclude them
      const deletedCommunityFolders = await CommunityFolders.find(
        {
          communityObjectId: new ObjectId(communityObjectId),
          status: COMMUNITY_FOLDER_STATUS.DELETED,
        },
        { _id: 1 }
      ).lean();

      const deletedCommunityFolderObjIds = deletedCommunityFolders.map(
        (folder) => folder._id
      );

      const coverVideoFolderTypes = Object.values(
        FOLDER_ITEM_COVER_MEDIA_FOLDER_TYPES
      );

      const aggregationPipeline = [
        {
          $match: {
            communityObjectId: new ObjectId(communityObjectId),
            communityFolderObjectId: {
              $nin: deletedCommunityFolderObjIds,
            },
            status: {
              $in: [
                FOLDER_ITEM_STATUS.PUBLISHED,
                FOLDER_ITEM_STATUS.UNPUBLISHED,
                FOLDER_ITEM_STATUS.PROCESSING,
              ],
            },
            size: { $exists: true, $nin: [null, ''] },
            $or: [
              // Include cover videos
              {
                folderType: { $in: coverVideoFolderTypes },
              },
              // Include checkpoint featured videos
              {
                folderType: 'challenge_checkpoint',
              },
              // Include regular folder items (attachments in digital products and checkpoints)
              {
                folderType: { $exists: false },
                type: {
                  $in: [
                    communityFolderItemTypesMap.VIDEO,
                    communityFolderItemTypesMap.IMG,
                    communityFolderItemTypesMap.FILE,
                    communityFolderItemTypesMap.AUDIO,
                  ],
                },
              },
            ],
          },
        },
        {
          // Group by unique identifier to avoid counting duplicates
          $group: {
            _id: {
              $cond: {
                if: {
                  $and: [
                    { $eq: ['$type', communityFolderItemTypesMap.VIDEO] },
                    { $ne: ['$videoObjectId', null] },
                  ],
                },
                then: '$videoObjectId', // For videos, use videoObjectId
                else: '$link', // For images, files, audio use link
              },
            },
            size: { $first: '$size' },
            type: { $first: '$type' },
          },
        },
        {
          // Sum up unique files
          $group: {
            _id: null,
            totalSizeInBytes: {
              $sum: {
                $toLong: '$size',
              },
            },
            uniqueFileCount: { $sum: 1 },
          },
        },
      ];

      const result = await CommunityFolderItems.aggregate(
        aggregationPipeline
      );

      if (!result || result.length === 0) {
        return 0;
      }

      logger.info('Calculated storage usage', {
        communityId: communityObjectId,
        totalSizeInBytes: result[0].totalSizeInBytes,
        uniqueFileCount: result[0].uniqueFileCount,
      });

      return result[0].totalSizeInBytes || 0;
    } catch (error) {
      logger.error('Failed to calculate storage usage from files', {
        error: error.message,
        communityId: communityObjectId,
      });
      throw error;
    }
  }

  static async checkStorageLimit(
    communityObjectId,
    additionalSizeInBytes = 0
  ) {
    try {
      const community = await CommunityModel.findById(
        communityObjectId
      ).lean();
      if (!community) {
        throw new Error('Community not found');
      }

      const currentUsage = await this.getCommunityStorageUsage(
        communityObjectId
      );

      const reservedUsage =
        await StorageReservationService.getTotalReservedStorage(
          communityObjectId
        );

      const featurePermissionManager = new FeaturePermissionManager(
        community.config?.planType,
        community.featurePermissions
      );

      const storageLimit = featurePermissionManager.getFeatureLimit(
        FEATURE_LIST_ID.STORAGE
      );
      const isStorageAllowed = featurePermissionManager.isFeatureAllowed(
        FEATURE_LIST_ID.STORAGE
      );

      if (!isStorageAllowed) {
        return {
          allowed: false,
          error: FEATURE_PERMISSION_ERROR.STORAGE_NOT_ALLOWED.name,
          message: 'Storage feature not allowed for current plan',
          currentUsageInBytes: currentUsage,
          reservedUsageInBytes: reservedUsage,
          limitInBytes: 0,
        };
      }

      const totalUsageAfterUpload =
        currentUsage + reservedUsage + additionalSizeInBytes;

      if (totalUsageAfterUpload > storageLimit) {
        return {
          allowed: false,
          error: FEATURE_PERMISSION_ERROR.STORAGE_LIMIT_EXCEEDED.name,
          message: 'Storage limit exceeded',
          currentUsageInBytes: currentUsage,
          reservedUsageInBytes: reservedUsage,
          limitInBytes: storageLimit,
          wouldExceedBy: totalUsageAfterUpload - storageLimit,
        };
      }

      return {
        allowed: true,
        currentUsageInBytes: currentUsage,
        reservedUsageInBytes: reservedUsage,
        limitInBytes: storageLimit,
        remainingInBytes: storageLimit - currentUsage - reservedUsage,
      };
    } catch (error) {
      logger.error('Failed to check storage limit', {
        error: error.message,
        communityId: communityObjectId,
        additionalSize: additionalSizeInBytes,
      });
      throw error;
    }
  }

  static async incrementStorageUsage(communityObjectId, sizeInBytes) {
    try {
      if (!sizeInBytes || sizeInBytes <= 0) {
        return;
      }

      const currentUsage = await this.getCommunityStorageUsage(
        communityObjectId
      );
      const newUsage = currentUsage + sizeInBytes;

      await CommunityModel.updateOne(
        { _id: communityObjectId },
        { currentStorageUsageInBytes: newUsage }
      );

      logger.info('Storage usage incremented', {
        communityId: communityObjectId,
        sizeInBytes,
        previousUsage: currentUsage,
        newUsage,
        action: 'increment',
      });
    } catch (error) {
      logger.error('Failed to increment storage usage', {
        error: error.message,
        communityId: communityObjectId,
        sizeInBytes,
      });
      throw error;
    }
  }

  static async decrementStorageUsage(communityObjectId, sizeInBytes) {
    try {
      if (!sizeInBytes || sizeInBytes <= 0) {
        return;
      }

      const currentUsage = await this.getCommunityStorageUsage(
        communityObjectId
      );
      const newUsage = Math.max(0, currentUsage - sizeInBytes);

      await CommunityModel.updateOne(
        { _id: communityObjectId },
        { currentStorageUsageInBytes: newUsage }
      );

      logger.info('Storage usage decremented', {
        communityId: communityObjectId,
        sizeInBytes,
        previousUsage: currentUsage,
        newUsage,
        action: 'decrement',
      });
    } catch (error) {
      logger.error('Failed to decrement storage usage', {
        error: error.message,
        communityId: communityObjectId,
        sizeInBytes,
      });
      throw error;
    }
  }

  static async recalculateStorageUsage(communityObjectId) {
    try {
      const calculatedUsage = await this.calculateStorageUsageFromFiles(
        communityObjectId
      );

      await CommunityModel.updateOne(
        { _id: communityObjectId },
        { currentStorageUsageInBytes: calculatedUsage }
      );

      logger.info('Storage usage recalculated', {
        communityId: communityObjectId,
        calculatedUsage,
        action: 'recalculate',
      });

      return calculatedUsage;
    } catch (error) {
      logger.error('Failed to recalculate storage usage', {
        error: error.message,
        communityId: communityObjectId,
      });
      throw error;
    }
  }

  static convertBytesToGB(bytes) {
    const num = Number(bytes);
    if (!num || Number.isNaN(num)) return 0;
    return +(num / (1024 * 1024 * 1024)).toFixed(5);
  }

  /**
   * Clear a storage reservation after storage has been incremented
   * @param {string} reservationId - Reservation ID
   * @param {string} communityObjectId - Community ID
   * @returns {Promise<Object|null>} Cleared reservation data or null if not found
   */
  static async clearStorageReservation(reservationId, communityObjectId) {
    try {
      if (!reservationId) {
        return null;
      }

      // Release the reservation
      const reservation =
        await StorageReservationService.releaseReservation(
          reservationId,
          communityObjectId
        );

      if (reservation) {
        logger.info('Storage reservation cleared', {
          reservationId,
          communityId: communityObjectId,
          reservedSizeInBytes: reservation.reservedSizeInBytes,
          reservedSizeInGB: this.convertBytesToGB(
            reservation.reservedSizeInBytes
          ),
        });
      }

      return reservation;
    } catch (error) {
      logger.error('Failed to clear storage reservation', {
        error: error.message,
        reservationId,
        communityId: communityObjectId,
      });
      // Don't throw - this is a cleanup operation
      return null;
    }
  }
}

module.exports = StorageUsageService;
