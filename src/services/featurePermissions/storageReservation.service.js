const { v4: uuidv4 } = require('uuid');
const {
  hsetAsync,
  hgetAsync,
  hdelAsync,
  redisClient,
} = require('../../redisClient');
const logger = require('../logger.service');

const COMMUNITY_RESERVATIONS_PREFIX = 'community_reservations';
const DEFAULT_RESERVATION_TTL_MINUTES = 15;

class StorageReservationService {
  /**
   * Create a new storage reservation
   * @param {Object} params - Reservation parameters
   * @param {string} params.communityObjectId - Community ID
   * @param {string} params.communityFolderObjectId - Folder ID
   * @param {number} params.reservedSizeInBytes - Size to reserve in bytes
   * @param {string} [params.createdByLearnerObjectId] - Learner ID who created the reservation
   * @param {number} [params.ttlMinutes=15] - TTL in minutes
   * @returns {Promise<Object>} Reservation details with reservationId and expiresAt
   */
  static async createReservation({
    communityObjectId,
    communityFolderObjectId,
    reservedSizeInBytes,
    createdByLearnerObjectId,
    ttlMinutes = DEFAULT_RESERVATION_TTL_MINUTES,
  }) {
    try {
      const reservationId = uuidv4();
      const expiresAt = new Date(Date.now() + ttlMinutes * 60 * 1000);
      const communityKey = `${COMMUNITY_RESERVATIONS_PREFIX}:${communityObjectId}`;

      // Store as "size|expiryTimestamp"
      const value = `${reservedSizeInBytes}|${expiresAt.getTime()}`;
      await hsetAsync(communityKey, reservationId, value);

      const reservationData = {
        reservationId,
        communityObjectId,
        communityFolderObjectId,
        reservedSizeInBytes,
        createdByLearnerObjectId,
        createdAt: new Date().toISOString(),
        expiresAt: expiresAt.toISOString(),
      };

      logger.info('Storage reservation created', {
        reservationId,
        communityId: communityObjectId,
        folderId: communityFolderObjectId,
        sizeInBytes: reservedSizeInBytes,
        ttlMinutes,
        createdBy: createdByLearnerObjectId,
      });

      return reservationData;
    } catch (error) {
      logger.error('Failed to create storage reservation', {
        error: error.message,
        communityId: communityObjectId,
        folderId: communityFolderObjectId,
        sizeInBytes: reservedSizeInBytes,
      });
      throw error;
    }
  }

  /**
   * Get reservation details by ID
   * @param {string} reservationId - Reservation ID
   * @param {string} communityObjectId - Community ID
   * @returns {Promise<Object|null>} Reservation details or null if not found/expired
   */
  static async getReservation(reservationId, communityObjectId) {
    try {
      const communityKey = `${COMMUNITY_RESERVATIONS_PREFIX}:${communityObjectId}`;
      const value = await hgetAsync(communityKey, reservationId);

      if (!value) {
        return null;
      }

      const [sizeStr, expiryStr] = value.split('|');
      const expiryTimestamp = parseInt(expiryStr, 10);

      // Check if expired
      if (Date.now() > expiryTimestamp) {
        // Clean up expired reservation
        await hdelAsync(communityKey, reservationId);
        return null;
      }

      return {
        reservationId,
        communityObjectId,
        reservedSizeInBytes: parseInt(sizeStr, 10),
        expiresAt: new Date(expiryTimestamp).toISOString(),
      };
    } catch (error) {
      logger.error('Failed to get storage reservation', {
        error: error.message,
        reservationId,
        communityId: communityObjectId,
      });
      throw error;
    }
  }

  /**
   * Release a reservation manually
   * @param {string} reservationId - Reservation ID
   * @param {string} communityObjectId - Community ID
   * @returns {Promise<Object|null>} Released reservation data or null if not found
   */
  static async releaseReservation(reservationId, communityObjectId) {
    try {
      const communityKey = `${COMMUNITY_RESERVATIONS_PREFIX}:${communityObjectId}`;
      const value = await hgetAsync(communityKey, reservationId);

      if (!value) {
        return null;
      }

      // Parse the stored value
      const [sizeStr, expiryStr] = value.split('|');
      const reservedSizeInBytes = parseInt(sizeStr, 10);
      const expiresAt = new Date(parseInt(expiryStr, 10)).toISOString();

      // Delete the reservation
      await hdelAsync(communityKey, reservationId);

      const reservation = {
        reservationId,
        communityObjectId,
        reservedSizeInBytes,
        expiresAt,
      };

      logger.info('Storage reservation released', {
        reservationId,
        communityId: communityObjectId,
        sizeInBytes: reservedSizeInBytes,
      });

      return reservation;
    } catch (error) {
      logger.error('Failed to release storage reservation', {
        error: error.message,
        reservationId,
        communityId: communityObjectId,
      });
      throw error;
    }
  }

  /**
   * Get total reserved storage for a community
   * @param {string} communityObjectId - Community ID
   * @returns {Promise<number>} Total reserved storage in bytes
   */
  static async getTotalReservedStorage(communityObjectId) {
    try {
      const communityKey = `${COMMUNITY_RESERVATIONS_PREFIX}:${communityObjectId}`;
      const reservations = await redisClient.hGetAll(communityKey);

      let totalReserved = 0;
      const now = Date.now();
      const expiredKeys = [];

      // Process each reservation
      for (const [reservationId, value] of Object.entries(reservations)) {
        const [sizeStr, expiryStr] = value.split('|');
        const expiryTimestamp = parseInt(expiryStr, 10);

        if (now > expiryTimestamp) {
          // Mark for cleanup
          expiredKeys.push(reservationId);
        } else {
          // Add to total if not expired
          const size = parseInt(sizeStr, 10);
          if (!Number.isNaN(size)) {
            totalReserved += size;
          }
        }
      }

      // Clean up expired reservations
      if (expiredKeys.length > 0) {
        await redisClient.hDel(communityKey, ...expiredKeys);

        logger.info('Cleaned up expired reservations', {
          communityId: communityObjectId,
          expiredCount: expiredKeys.length,
        });
      }

      const activeReservationCount =
        Object.keys(reservations).length - expiredKeys.length;

      // Delete empty hash to prevent memory bloat
      if (
        activeReservationCount === 0 &&
        Object.keys(reservations).length > 0
      ) {
        await redisClient.del(communityKey);
        logger.info('Deleted empty reservation hash', {
          communityId: communityObjectId,
        });
      }

      logger.info('Calculated total reserved storage', {
        communityId: communityObjectId,
        totalReservedBytes: totalReserved,
        activeReservationCount,
        expiredCount: expiredKeys.length,
      });

      return totalReserved;
    } catch (error) {
      logger.error('Failed to get total reserved storage', {
        error: error.message,
        communityId: communityObjectId,
      });
      return 0; // Return 0 on error to not block operations
    }
  }
}

module.exports = StorageReservationService;
