const affiliateService = require('../affiliate/affiliate.service');
const affiliateProductService = require('../affiliate/affiliateProduct.service');

exports.handleUpdateAffiliate = async ({
  community,
  planAllowed = true,
  allowed = true,
  session,
}) => {
  const communityObjectId = community._id;

  // Proceed only if both planAllowed and allowed are false
  if (planAllowed || allowed) {
    return;
  }

  await affiliateService.disableAffiliates({ communityObjectId, session });
  await affiliateProductService.disableAffiliateProducts({
    communityObjectId,
    session,
  });
};
