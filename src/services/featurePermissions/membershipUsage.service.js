const FeaturePermissionManager = require('@services/common/featurePermissionManager.service');
const { FEATURE_LIST_ID } = require('@constants/common');
const { FEATURE_PERMISSION_ERROR } = require('@constants/errorCode');
const logger = require('@services/logger.service.js');
const CommunityModel = require('@/src/communitiesAPI/models/community.model');
const membershipCountService = require('../membership/count.service');

class MembershipUsageService {
  /**
   * Check if community can add additional members
   * @param {string} communityId - Community ID
   * @param {number} additionalEmails - Number of additional emals to add
   * @returns {Promise<Object>} Check result with allowed status and limits
   */
  static async checkAddMemberLimit(
    communityId,
    additionalEmails = 0,
    toMember = false
  ) {
    try {
      const community = await CommunityModel.findById(communityId).lean();
      if (!community) {
        throw new Error('Community not found');
      }

      const currentUsage =
        await membershipCountService.retrieveMemberUsage({ community });

      const featurePermissionManager = new FeaturePermissionManager(
        community.config?.planType,
        community.featurePermissions
      );

      const limit = featurePermissionManager.getFeatureLimit(
        FEATURE_LIST_ID.MEMBER
      );
      const isAllowed = featurePermissionManager.isFeatureAllowed(
        FEATURE_LIST_ID.MEMBER
      );
      const totalUsageAfterAdding = currentUsage + additionalEmails;

      if (toMember && (!isAllowed || totalUsageAfterAdding > limit)) {
        return {
          allowed: false,
          error:
            FEATURE_PERMISSION_ERROR.MEMBER_LIMIT_EXCEEDED_SIGNUP.name,
          message:
            'Looks like community is full! Drop the admin a message — they might need to upgrade',
          currentUsage,
          limit: 0,
          wouldExceedBy: totalUsageAfterAdding - limit,
        };
      }

      if (!isAllowed) {
        return {
          allowed: false,
          error: FEATURE_PERMISSION_ERROR.FEATURE_NOT_ALLOWED.name,
          message: 'Feature not allowed for current plan',
          currentUsage,
          limit: 0,
        };
      }

      // Check if limit is exceeded
      if (totalUsageAfterAdding > limit) {
        return {
          allowed: false,
          error: FEATURE_PERMISSION_ERROR.MEMBER_LIMIT_EXCEEDED.name,
          message: `You’ve reached your member limit. Upgrade your plan to increase your limit.`,
          currentUsage,
          limit,
          wouldExceedBy: totalUsageAfterAdding - limit,
        };
      }

      return {
        allowed: true,
        currentUsage,
        limit,
        remaining: limit - currentUsage,
      };
    } catch (error) {
      logger.error('Failed to check Add Member limit', {
        error: error.message,
        communityId,
        additionalEmails,
      });
      throw error;
    }
  }

  /**
   * Check if community can add additional manager
   * @param {string} communityId - Community ID
   * @param {number} additionalEmails - Number of additional emals to add
   * @returns {Promise<Object>} Check result with allowed status and limits
   */
  static async checkAddManagerLimit(communityId, additionalEmails = 0) {
    try {
      const community = await CommunityModel.findById(communityId).lean();
      if (!community) {
        throw new Error('Community not found');
      }

      const currentUsage =
        await membershipCountService.retrieveManagerUsage({ community });

      const featurePermissionManager = new FeaturePermissionManager(
        community.config?.planType,
        community.featurePermissions
      );

      const limit = featurePermissionManager.getFeatureLimit(
        FEATURE_LIST_ID.MANAGER
      );
      const isAllowed = featurePermissionManager.isFeatureAllowed(
        FEATURE_LIST_ID.MANAGER
      );

      if (!isAllowed) {
        return {
          allowed: false,
          error: FEATURE_PERMISSION_ERROR.FEATURE_NOT_ALLOWED.name,
          message: 'Feature not allowed for current plan',
          currentUsage,
          limit: 0,
        };
      }

      const totalUsageAfterAdding = currentUsage + additionalEmails;

      // Check if limit is exceeded
      if (totalUsageAfterAdding > limit) {
        return {
          allowed: false,
          error: FEATURE_PERMISSION_ERROR.MANAGER_LIMIT_EXCEEDED.name,
          message: `You’ve reached your manager limit. Upgrade your plan to increase your limit.`,
          currentUsage,
          limit,
          wouldExceedBy: totalUsageAfterAdding - limit,
        };
      }

      return {
        allowed: true,
        currentUsage,
        limit,
        remaining: limit - currentUsage,
      };
    } catch (error) {
      logger.error('Failed to check Add Manager limit', {
        error: error.message,
        communityId,
        additionalEmails,
      });
      throw error;
    }
  }
}

module.exports = MembershipUsageService;
