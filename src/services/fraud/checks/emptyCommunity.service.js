const checkEmptyCommunity = async ({ community }) => {
  // Community is missing thumbnail
  const thumbnail = community.thumbnailImgData?.mobileImgData?.src;
  const invalidThumbnail =
    !thumbnail || thumbnail.includes('nasIO/onboarding');
  if (invalidThumbnail) {
    return {
      probability: 100,
      reason: 'Community is missing thumbnail',
    };
  }

  // Community has no description
  if (!community.description) {
    return {
      probability: 100,
      reason: 'Community has no description',
    };
  }

  return {
    probability: 0,
  };
};

const getLarkAlertDetails = ({ result }) => {
  return [
    {
      title: 'Empty community reason: ',
      value: `${result.reason}`,
    },
  ];
};

module.exports = {
  checkEmptyCommunity,
  getLarkAlertDetails,
};
