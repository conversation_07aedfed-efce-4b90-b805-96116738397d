const { DateTime } = require('luxon');
const emptyCommunityService = require('./emptyCommunity.service');
const ZeroLinkModel = require('../../../models/zerolink/zerolink.model');
const CommunityFraudModel = require('../../../models/fraud/communityFraud.model');
const { PRICE_TYPE } = require('../../../constants/common');
const rawTransactionModel = require('../../../models/rawTransaction.model');
const requestedRefundModel = require('../../../models/requestedRefund.model');
const PaymentBackendRpc = require('../../../rpc/paymentBackend');
const CurrencyService = require('../../communitySignup/signup/payloadInformation/currency.service');
const logger = require('../../logger.service');

const FRAUD_THRESHOLDS = {
  HIGH_AMOUNT_USD: 150,
  RISKY_PRICE_CHANGE_PERCENTAGE: 150,
  PROBABILITY_HIGH: 85,
  PROBABILITY_CRITICAL: 100,
  NEW_COMMUNITY_THRESHOLD_IN_DAYS: 7,
  RECENT_TXNS_DAYS: 7,
  RECENT_TXNS_COUNT: 5,
  RECENT_REFUND_DAYS: 14,
};

/**
 * @description Check if community is new
 * @param {Object} params
 * @param {Object} params.community
 * @param {number} params.daysThreshold
 * @returns {boolean}
 */
const checkIsNewCommunity = ({ community, daysThreshold }) => {
  const createdAt = community.createdAt;
  const now = DateTime.utc();
  const communityCreationDate = DateTime.fromJSDate(
    new Date(createdAt)
  ).toUTC();

  // Calculate the difference in days
  const daysDifference = now.diff(communityCreationDate, 'days').days;

  return daysDifference <= daysThreshold;
};

/**
 * @description Check if ops approved community in the last 7 days
 * @param {Object} params
 * @param {string} params.communityId
 * @returns {Promise<boolean>}
 */
const checkIfOpsApprovedCommunityInLast7Days = async ({ communityId }) => {
  const approvedRecord = await CommunityFraudModel.findOne({
    communityId,
    status: 'approved',
    updatedAt: { $gte: DateTime.utc().minus({ days: 7 }).toJSDate() },
  }).lean();

  return !!approvedRecord; // returns true if ops approved community it in the last 7 days
};

const getUsdAmount = async ({ amount, currency }) => {
  try {
    // Initialize the RPC client
    const paymentBackendRpc = new PaymentBackendRpc();
    await paymentBackendRpc.init();

    // Convert the amount to USD
    const amountInUsd = await CurrencyService.convertAmountToUsd(
      amount,
      currency,
      paymentBackendRpc
    );

    return amountInUsd;
  } catch (err) {
    logger.error(
      `[Fn:getUsdAmount]|amount=${amount}|currency=${currency}|`,
      err.message
    );
    // Return original amount if conversion fails
    return amount;
  }
};

/**
 * @description Check if amount is greater than USD 150
 * @param {Object} params
 * @param {string} params.currency
 * @param {number} params.amount
 * @returns {Promise<boolean>}
 */
const checkIsAmountHigh = async ({ currency, amount }) => {
  let amountInUSD = amount / 100;
  if (currency.toUpperCase() !== 'USD') {
    amountInUSD = await getUsdAmount({
      amount: amount / 100,
      currency,
    });
  }

  // if new amount is gt than USD 150, then amount is high
  return amountInUSD > FRAUD_THRESHOLDS.HIGH_AMOUNT_USD;
};

/**
 * @description Check if new amount is greater than USD 150 and percentage change in price is more than 150%
 * @param {Object} params
 * @param {Object} params.zeroLink
 * @param {number} params.oldAmount
 * @returns {Promise<boolean>}
 */
const checkPriceChangeDeltaIsHigh = async ({ zeroLink, oldAmount }) => {
  if (zeroLink.pricingConfig.priceType !== PRICE_TYPE.FIXED) {
    return false;
  }

  const newAmount = zeroLink.amount;

  if (!newAmount) {
    return false;
  }

  // get new amount in USD
  const currency = zeroLink.currency;
  const newAmountIsHigh = await checkIsAmountHigh({
    currency,
    amount: newAmount,
  });

  if (!newAmountIsHigh) {
    // if new amount is not high i.e less than USD 150, return false
    return false;
  }

  const amountDelta = Math.abs(newAmount - oldAmount);
  const amountDeltaPercentage = (amountDelta / oldAmount) * 100;

  // If amount delta is more than 150%, return fraud result
  if (
    amountDeltaPercentage > FRAUD_THRESHOLDS.RISKY_PRICE_CHANGE_PERCENTAGE
  ) {
    return true;
  }

  return false;
};

/**
 * @description Check if user has made more than 5 txns in the last 7 days
 * @param {Object} params
 * @param {string} params.communityId
 * @param {string} params.email
 * @returns {Promise<boolean>}
 */
const checkIfUserHasHighNumberOfRecentTxnsInComm = async ({
  communityId,
  email,
}) => {
  const recentTxnCount = await rawTransactionModel
    .find({
      email,
      communityObjectId: communityId,
      transactionCreatedAt: {
        $gte: DateTime.utc()
          .minus({ days: FRAUD_THRESHOLDS.RECENT_TXNS_DAYS })
          .toJSDate(),
      },
    })
    .limit(FRAUD_THRESHOLDS.RECENT_TXNS_COUNT)
    .lean();

  return recentTxnCount.length >= FRAUD_THRESHOLDS.RECENT_TXNS_COUNT;
};

/**
 * @description Check if user has requested for a refund in the last 14 days
 * @param {Object} params
 * @param {string} params.communityId
 * @param {string} params.email
 * @returns {Promise<boolean>}
 */
const checkIfUserHasRecentlyRequestedRefund = async ({
  communityId,
  email,
}) => {
  const recentRefundRequest = await requestedRefundModel
    .findOne({
      communityObjectId: communityId,
      transactionEmail: email,
      createdAt: {
        $gte: DateTime.utc()
          .minus({ days: FRAUD_THRESHOLDS.RECENT_REFUND_DAYS })
          .toJSDate(),
      },
    })
    .lean();

  return !!recentRefundRequest;
};

const checkCreateZeroLink = async ({
  community,
  zeroLinkObjectId,
  existingZeroLink,
  updatePayload = {},
}) => {
  try {
    // CHECK: if community is empty
    const emptyCommunityResult =
      await emptyCommunityService.checkEmptyCommunity({
        community,
      });

    // if is empty community, return immediately
    if (emptyCommunityResult.probability) {
      return emptyCommunityResult;
    }

    // CHECK: Check if community is new & has not been approved by ops in the last 7 days
    // Communities created within last 7 days are considered new communities
    const isNewCommunity = checkIsNewCommunity({
      community,
      daysThreshold: FRAUD_THRESHOLDS.NEW_COMMUNITY_THRESHOLD_IN_DAYS,
    });

    if (isNewCommunity) {
      // if community was created within last 7 days, check if ops approved it in the last 7d
      const hasOpsApprovedCommunityInLast7Days =
        await checkIfOpsApprovedCommunityInLast7Days({
          communityId: community._id,
        });

      if (!hasOpsApprovedCommunityInLast7Days) {
        // if ops did not approve it, then return fraud result
        return {
          probability: FRAUD_THRESHOLDS.PROBABILITY_HIGH,
          reason:
            'Community was created within last 7 days and not approved by ops in the last 7 days',
          createdAt: community.createdAt,
        };
      }
    }

    const isEditFlow = !!existingZeroLink;
    if (isEditFlow) {
      const zeroLink = await ZeroLinkModel.findById(
        zeroLinkObjectId
      ).lean();
      if (!zeroLink) {
        return {
          probability: FRAUD_THRESHOLDS.PROBABILITY_CRITICAL,
          reason: `Zero link with id ${zeroLinkObjectId} not found`,
        };
      }

      // CHECK: if pricingConfig.priceType is changed, return fraud result
      if (
        zeroLink.pricingConfig.priceType !==
        updatePayload.pricingConfig.priceType
      ) {
        return {
          probability: FRAUD_THRESHOLDS.PROBABILITY_HIGH,
          reason: `Price type changed from ${zeroLink.pricingConfig.priceType} to ${updatePayload.pricingConfig.priceType}`,
        };
      }

      // CHECK: If editing, check if price change is too high for fixed price
      if (updatePayload.pricingConfig.priceType === PRICE_TYPE.FIXED) {
        const isPriceChangeDeltaHigh = await checkPriceChangeDeltaIsHigh({
          zeroLink,
          oldAmount: existingZeroLink.amount,
        });

        if (isPriceChangeDeltaHigh) {
          return {
            probability: FRAUD_THRESHOLDS.PROBABILITY_HIGH,
            reason: `Price change is too high. Old price: ${
              existingZeroLink.currency
            } ${existingZeroLink.amount / 100}, new price: ${
              zeroLink.currency
            } ${zeroLink.amount / 100}`,
          };
        }
      }
    }

    return {
      probability: 0,
    };
  } catch (err) {
    logger.error(
      `[FraudCheck][Fn:checkCreateZeroLink]|communityId=${community._id}|zeroLinkObjectId=${zeroLinkObjectId}|`,
      err.message
    );
    return {
      probability: 0,
    };
  }
};

const checkPurchaseZeroLink = async ({ community, email }) => {
  try {
    // CHECK: if community is empty
    const emptyCommunityResult =
      await emptyCommunityService.checkEmptyCommunity({
        community,
      });

    // if is empty community, return immediately
    if (emptyCommunityResult.probability) {
      return emptyCommunityResult;
    }

    // CHECK: if user has made more than 5 txns in the last 7 days, return fraud result
    const hasHighNumberOfRecentTxnsInComm =
      await checkIfUserHasHighNumberOfRecentTxnsInComm({
        communityId: community._id,
        email,
      });

    if (hasHighNumberOfRecentTxnsInComm) {
      return {
        probability: 100,
        reason: `User has made more than 5 txns in the last 7 days in community ${community._id}`,
      };
    }

    // CHECK: if user has requested for a refund in the last 14 days
    const hasRecentlyRequestedRefund =
      await checkIfUserHasRecentlyRequestedRefund({
        communityId: community._id,
        email,
      });

    if (hasRecentlyRequestedRefund) {
      return {
        probability: 100,
        reason: `User has requested for a refund in the last 14 days in community ${community._id}`,
      };
    }

    return {
      probability: 0,
    };
  } catch (err) {
    logger.error(
      `[FraudCheck][Fn:checkPurchaseZeroLink]|communityId=${community._id}|email=${email}|`,
      err.message
    );
    return {
      probability: 0,
    };
  }
};

const getLarkAlertDetails = ({ result }) => {
  const data = [
    {
      title: 'Failed to create zero link: ',
      value: `${result.reason}`,
    },
  ];

  if (result.createdAt) {
    data.push({
      title: 'Creation date: ',
      value: `${new Date(result.createdAt).toISOString()}`,
    });
  }

  return data;
};

module.exports = {
  checkCreateZeroLink,
  checkPurchaseZeroLink,
  getLarkAlertDetails,
};
