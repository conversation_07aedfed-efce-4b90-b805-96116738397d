const mongoose = require('mongoose');

const { ObjectId } = mongoose.Types;
const { DateTime } = require('luxon');
const FolderModel = require('../../communitiesAPI/models/communityFolders.model');
const FolderItemModel = require('../../communitiesAPI/models/communityFolderItems.model');
const FolderViewerModel = require('../../models/product/folderViewers.model');
const PrimaryMongooseConnection = require('../../rpc/primaryMongooseConnection');

const FolderPurchaseModel = require('../../communitiesAPI/models/communityFolderPurchases.model');
const CommunityRoleModel = require('../../communitiesAPI/models/communityRole.model');

const RegexUtils = require('../../utils/regex.util');
const MongodbUtils = require('../../utils/mongodb.util');
const logger = require('../logger.service');

const {
  DEFAULT_CURRENCY,
  BATCH_METADATA_MODEL_TYPE,
} = require('../../constants/common');
const {
  COMMUNITY_FOLDER_PURCHASE_TYPES,
  communityLibraryTypesMap,
  aclRoles,
  communityLibraryStatusMap,
  communityFolderTypesMap,
} = require('../../communitiesAPI/constants/index');
const { FOLDER_VIEWER_STATUS } = require('./constants');
const { PURCHASE_TYPE } = require('../../constants/common');
const {
  ResourceNotFoundError,
  ParamError,
} = require('../../utils/error.util');
const {
  retrieveLearnerObjectIdsViaSearch,
} = require('../common/membershipSearch.service');
const { affiliateProductService } = require('../affiliate');
const batchMetadataService = require('../batchMetadata');
const {
  hasVideoCoverMediaItems,
} = require('../../utils/multipleCoverMediaItems.util');
const {
  deleteCoverMediaItems,
} = require('../coverMediaItems/common.service');
const SyncProductDataService = require('../product/syncProductData.service');
const { PRODUCT_TYPE } = require('../product/constants');
const storageUsageService = require('../featurePermissions/storageUsage.service');

exports.generateFolderViewersFilter = async ({
  search = '',
  status = ['ALL'],
  communityId,
  folderId,
  excludeLearnerObjectIds = [],
  folderAccess,
  startObjectId = null,
  endObjectId = null,
}) => {
  const filter = {
    $and: [
      { folderObjectId: new ObjectId(folderId) },
      { learnerObjectId: { $nin: excludeLearnerObjectIds } },
    ],
  };

  if (startObjectId && endObjectId) {
    filter._id = {
      $gte: MongodbUtils.toObjectId(startObjectId),
      $lte: MongodbUtils.toObjectId(endObjectId),
    };
  }

  let filterOutFree = false;
  if (!folderAccess) {
    const folder = await FolderModel.findById(folderId)
      .select('access')
      .lean();
    if (folder.access === COMMUNITY_FOLDER_PURCHASE_TYPES.PAID) {
      filterOutFree = true;
    }
  }

  if (
    folderAccess === COMMUNITY_FOLDER_PURCHASE_TYPES.PAID ||
    filterOutFree
  ) {
    filter.$and.push({ status: { $ne: FOLDER_VIEWER_STATUS.FREE } });
  }

  // Include chargeback case when filter refund case
  if (status.includes(FOLDER_VIEWER_STATUS.REFUNDED)) {
    status.push(FOLDER_VIEWER_STATUS.CHARGEBACK);
  }

  const joinedStatus = status.join();
  if (joinedStatus !== 'ALL' && joinedStatus !== '') {
    filter.$and.push({ status: { $in: status } });
  }

  const searchWithEscapedRegexSign = RegexUtils.escapeRegExp(search);

  if (searchWithEscapedRegexSign !== '') {
    const learnerObjectIdRelatedToSearchResult =
      await retrieveLearnerObjectIdsViaSearch(
        searchWithEscapedRegexSign,
        communityId
      );
    const filtered = learnerObjectIdRelatedToSearchResult.filter(
      (i) => !!i
    );
    filter.$and.push({ learnerObjectId: { $in: filtered } });
  }

  logger.info(`generateFolderViewerFilter: ${JSON.stringify(filter)}`);
  return filter;
};

function generateFolderViewersPipeline({
  filter,
  pageSize,
  pageNo,
  sortBy,
  sortOrder,
  projection,
}) {
  const pipelineQuery = [
    {
      $match: filter,
    },
    {
      $sort: { [sortBy]: sortOrder },
    },
    {
      $skip: (pageNo - 1) * pageSize,
    },
    {
      $limit: pageSize,
    },
    ...MongodbUtils.lookupAndUnwind(
      'learners',
      'learnerObjectId',
      '_id',
      'learner'
    ),
    {
      $project: projection,
    },
  ];

  logger.info(
    `generateFolderViewerPipeline: pipeline query: ${JSON.stringify(
      pipelineQuery
    )}`
  );

  return pipelineQuery;
}

exports.retrieveFolderViewers = async ({
  search,
  pageSize,
  pageNo,
  sortBy,
  sortOrder,
  status,
  communityId,
  folderId,
}) => {
  const folder = await FolderModel.findById(folderId)
    .select('access')
    .lean();
  let showPaidUi = folder?.access === COMMUNITY_FOLDER_PURCHASE_TYPES.PAID;

  if (!showPaidUi) {
    const paidFolderView = await FolderViewerModel.exists({
      folderObjectId: new ObjectId(folderId),
      status: {
        $in: [
          FOLDER_VIEWER_STATUS.PAID,
          FOLDER_VIEWER_STATUS.REFUNDED,
          FOLDER_VIEWER_STATUS.CHARGEBACK,
        ],
      },
    });
    showPaidUi = !!paidFolderView;
  }

  const newSortBy = sortBy ?? (showPaidUi ? 'purchaseDate' : 'accessDate');

  const filter = await this.generateFolderViewersFilter({
    search,
    status,
    communityId,
    folderId,
    folderAccess: folder?.access,
  });

  const projection = {
    name: {
      $concat: [
        { $ifNull: ['$learner.firstName', ''] },
        ' ',
        { $ifNull: ['$learner.lastName', ''] },
      ],
    },
    profileImage: '$learner.profileImage',
    email: '$learner.email',
    status: 1,
    createdAt: 1,
    accessDate: 1,
    purchaseDate: 1,
    refundDate: 1,
    localAmount: 1,
    localCurrency: 1,
    amountInUsd: 1,
  };

  const pipelineQuery = generateFolderViewersPipeline({
    filter,
    pageSize,
    pageNo,
    sortBy: newSortBy,
    sortOrder,
    projection,
  });

  const [viewers, totalViewers] = await Promise.all([
    FolderViewerModel.aggregate(pipelineQuery),
    FolderViewerModel.countDocuments(filter),
  ]);

  const metadata = {
    total: totalViewers,
    limit: pageSize,
    page: pageNo,
    pages: Math.ceil(totalViewers / pageSize),
    showPaidUi,
  };

  return {
    viewers,
    metadata,
  };
};

exports.updateFolderViewersAccessDate = async (
  folder,
  userObjectId,
  learnerId
) => {
  const manager = await CommunityRoleModel.findOne({
    communityObjectId: new ObjectId(folder.communityObjectId),
    userObjectId: new ObjectId(userObjectId),
    role: aclRoles.MANAGER,
  }).lean();

  if (manager) {
    return { message: 'Skip managers' };
  }

  if (!folder._id || !learnerId) {
    throw new Error(
      `Missing folderId and learnerId | folderId=${folder._id} | learnerId=${learnerId}`
    );
  }

  const folderObjectId = new ObjectId(folder._id);
  const learnerObjectId = new ObjectId(learnerId);
  const folderViewer = await FolderViewerModel.findOne({
    folderObjectId,
    learnerObjectId,
  }).lean();

  let result;
  if (folderViewer) {
    if (!folderViewer.accessDate) {
      result = await FolderViewerModel.findByIdAndUpdate(
        folderViewer._id,
        { accessDate: DateTime.utc() },
        {
          new: true,
        }
      ).lean();
    }
  } else {
    result = await FolderViewerModel.create({
      folderObjectId,
      learnerObjectId,
      status: FOLDER_VIEWER_STATUS.FREE,
      accessDate: DateTime.utc(),
    });
    result = result.toObject();
    await FolderModel.findOneAndUpdate(
      { _id: folderObjectId },
      { $inc: { accessCount: 1 } }
    );

    await batchMetadataService.add({
      batchMetadataModelType: BATCH_METADATA_MODEL_TYPE.FOLDER_VIEWER,
      entityObjectId: folderObjectId,
      communityObjectId: folder.communityObjectId,
      addedObjectId: result._id,
    });
  }
  return result;
};

exports.upsertFullDiscountFolderViewers = async (addon, session) => {
  const folderViewer = await FolderViewerModel.updateOne(
    {
      folderObjectId: addon.entityObjectId,
      learnerObjectId: addon.learnerObjectId,
    },
    {
      status: FOLDER_VIEWER_STATUS.PAID,
      addonTransactionObjectId: addon._id,
      purchaseDate: addon.updatedAt,
      localAmount: addon.local_amount,
      localCurrency: addon.local_currency,
      amountInUsd: addon.amount,
    },
    {
      upsert: true,
      session,
    }
  );

  await FolderModel.updateOne(
    { _id: addon.entityObjectId },
    { $inc: { accessCount: 1 } },
    { session }
  );

  if (folderViewer.upsertedId) {
    await batchMetadataService.add({
      batchMetadataModelType: BATCH_METADATA_MODEL_TYPE.FOLDER_VIEWER,
      entityObjectId: addon.entityObjectId,
      communityObjectId: addon.communityObjectId,
      addedObjectId: folderViewer.upsertedId,
    });
  }
};

exports.setupDigitalProductAccess = async ({
  learnerObjectId,
  folderObjectId,
  session,
}) => {
  const currentDateTime = new Date();

  const folder = await FolderModel.findById(folderObjectId).lean();

  if (!folder) {
    throw new ResourceNotFoundError('Folder cannot be found');
  }

  if (folder.type !== communityLibraryTypesMap.DIGITAL_PRODUCT) {
    throw new ParamError(`Invalid folder type ${folder.type}`);
  }

  if (folder.access !== COMMUNITY_FOLDER_PURCHASE_TYPES.PAID) {
    return;
  }

  const existsFolderPurchase = await FolderPurchaseModel.findOne({
    folderObjectId,
    learnerObjectId,
  }).lean();

  if (existsFolderPurchase) {
    return;
  }

  const folderViewer = await FolderViewerModel.updateOne(
    { folderObjectId, learnerObjectId },
    {
      status: FOLDER_VIEWER_STATUS.PAID,
      purchaseDate: currentDateTime,
    },
    { upsert: true, session }
  );

  await FolderPurchaseModel.updateOne(
    { folderObjectId, learnerObjectId },
    {
      amount: 0,
      currency: DEFAULT_CURRENCY,
      folderCheckoutId: new ObjectId(),
      local_amount: 0,
      local_currency: DEFAULT_CURRENCY,
      purchaseType: 'free',
    },
    { upsert: true, session }
  );

  await FolderModel.updateOne(
    { _id: folderObjectId },
    {
      $inc: { accessCount: 1 },
    },
    { session }
  );

  if (folderViewer.upsertedId) {
    await batchMetadataService.add({
      batchMetadataModelType: BATCH_METADATA_MODEL_TYPE.FOLDER_VIEWER,
      entityObjectId: folderObjectId,
      communityObjectId: folder.communityObjectId,
      addedObjectId: folderViewer.upsertedId,
    });
  }
};

exports.softDeleteFolderAndFolderItems = async (
  folderObjectId,
  communityObjectId
) => {
  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  try {
    // Calculate total storage usage of folder items before deletion
    const folderItemsToDelete = await FolderItemModel.find({
      communityFolderObjectId: folderObjectId,
      status: { $ne: communityLibraryStatusMap.DELETED },
      size: { $exists: true, $ne: null },
    })
      .select('_id size')
      .lean();

    const totalStorageToDecrement = folderItemsToDelete.reduce(
      (total, item) => {
        return total + (parseInt(item.size, 10) || 0);
      },
      0
    );

    const updatedFolder = await FolderModel.findOneAndUpdate(
      { _id: folderObjectId, communityObjectId },
      { status: communityLibraryStatusMap.DELETED },
      { session, new: true }
    ).lean();

    let productType;
    switch (updatedFolder.type) {
      case communityFolderTypesMap.COURSE:
        productType = PRODUCT_TYPE.COURSE;
        break;
      case communityFolderTypesMap.SESSION:
        productType = PRODUCT_TYPE.SESSION;
        break;
      default:
        productType = PRODUCT_TYPE.DIGITAL_FILES;
    }

    await SyncProductDataService.syncProductData({
      productType,
      entity: updatedFolder,
      session,
    });

    if (!updatedFolder) {
      throw new ParamError('Invalid update for folder');
    }

    const entityType =
      updatedFolder.type === communityFolderTypesMap.SESSION
        ? PURCHASE_TYPE.SESSION
        : PURCHASE_TYPE.FOLDER;

    const updatedFolderItemResult = await FolderItemModel.updateMany(
      { communityFolderObjectId: folderObjectId },
      { status: communityLibraryStatusMap.DELETED },
      { session }
    );

    if (updatedFolderItemResult) {
      updatedFolder.folderItems = updatedFolderItemResult.matchedCount;
      updatedFolder.folderItemsModified =
        updatedFolderItemResult.modifiedCount;

      await affiliateProductService.disableAffiliateProduct({
        communityObjectId: updatedFolder.communityObjectId,
        entityType,
        entityObjectId: folderObjectId,
        session,
      });
    }

    // update video folder item status to deleted
    const coverMediaItems = updatedFolder?.coverMediaItems ?? [];
    if (hasVideoCoverMediaItems(coverMediaItems)) {
      await deleteCoverMediaItems({
        coverMediaItems: coverMediaItems ?? [],
        session,
        communityId: communityObjectId,
      });
    }

    await session.commitTransaction();

    // Update storage usage after successful transaction
    if (totalStorageToDecrement > 0) {
      try {
        await storageUsageService.decrementStorageUsage(
          communityObjectId,
          totalStorageToDecrement
        );
        logger.info('Storage usage decremented for folder deletion', {
          communityId: communityObjectId,
          folderId: folderObjectId,
          folderItemsDeleted: folderItemsToDelete.length,
          storageDecreased: totalStorageToDecrement,
        });
      } catch (storageError) {
        logger.error(
          'Failed to update storage usage for folder deletion',
          {
            communityId: communityObjectId,
            folderId: folderObjectId,
            totalStorageToDecrement,
            folderItemsCount: folderItemsToDelete.length,
            error: storageError.message,
          }
        );
      }
    }

    return updatedFolder;
  } catch (error) {
    logger.info(
      `softDeleteFolderAndFolderItems: ${error.message}, ${error.stack}`
    );
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }
};
