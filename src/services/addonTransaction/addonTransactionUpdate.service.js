const CommunityAddonTransactionModel = require('../../communitiesAPI/models/communityAddonTransactions.model');
const {
  COMMUNITY_ONE_TIME_PAYMENT_ENTITIES,
  COMMUNITY_ENTITY_ONE_TIME_PAYMENT_STATUSES,
} = require('../../communitiesAPI/constants');
const zeroLinkMailService = require('./zeroLinkMail.service');

exports.updateAddonTransactions = async ({
  status,
  addonTransactionId,
  failureCode,
  failureReason,
  eventTime,
  paymentProvider,
}) => {
  const updatePayload = {
    'payment_details.status': status,
    'payment_details.latestUpdatedTime': eventTime,
    'payment_details.paymentProvider': paymentProvider,
  };
  if (failureCode) {
    updatePayload['payment_details.failureCode'] = failureCode;
  }
  if (failureReason) {
    updatePayload['payment_details.failureReason'] = failureReason;
  }

  if (status === COMMUNITY_ENTITY_ONE_TIME_PAYMENT_STATUSES.SUCCESS) {
    updatePayload['payment_details.succeeded_time'] = eventTime;
  } else if (
    status === COMMUNITY_ENTITY_ONE_TIME_PAYMENT_STATUSES.FAILED
  ) {
    updatePayload['payment_details.failed_time'] = eventTime;
  }

  const addonTransaction =
    await CommunityAddonTransactionModel.findOneAndUpdate(
      {
        _id: addonTransactionId,
        $or: [
          { 'payment_details.latestUpdatedTime': { $eq: null } },
          {
            'payment_details.latestUpdatedTime': {
              $lt: eventTime,
            },
          },
        ],
      },
      updatePayload,
      { new: true }
    ).lean();

  // TODO: send email or other actions if needed by different purchase type
  if (
    addonTransaction &&
    addonTransaction.entityCollection ===
      COMMUNITY_ONE_TIME_PAYMENT_ENTITIES.ZERO_LINK
  ) {
    await zeroLinkMailService.handleEmailForZeroLinkPurchase(
      addonTransaction
    );
  }
};
