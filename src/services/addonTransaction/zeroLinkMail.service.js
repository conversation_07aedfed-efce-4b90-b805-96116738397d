const { DateTime } = require('luxon');

const CommunityModel = require('../../communitiesAPI/models/community.model');
const LearnerModel = require('../../models/learners.model');
const ZeroLinkModel = require('../../models/zerolink/zerolink.model');

const formatVariableDataService = require('../mail/formatVariableData.service');
const communityNotificationCommonService = require('../communityNotification/email/common.service');
const logger = require('../logger.service');
const { formatDateTimeByLocale } = require('../../utils/date.util');
const { ZERO_LINK_MAIL_TYPES } = require('../mail/constants');
const { PAYMENT_STATUSES } = require('../../communitiesAPI/constants');
const mailUtils = require('../../utils/mail.util');
const { PURCHASE_TYPE } = require('../../constants/common');
const receiptService = require('../receipts/receipts.service');

function retrievePaymentSucceedTimeFormat(
  paymentTime,
  languagePreference,
  timezone = 'utc'
) {
  if (!paymentTime) {
    return '';
  }
  const date = DateTime.fromJSDate(paymentTime).setZone(timezone);

  return (
    formatDateTimeByLocale(
      date,
      'HH:mm, LLL d yyyy',
      languagePreference
    ) ?? ''
  );
}

async function formatAndSendZeroLinkSuccessEmail({
  mailType,
  owner,
  learner,
  zeroLink,
  community,
  addonTransaction,
}) {
  const memberVariables = formatVariableDataService.formatMemberData({
    learner,
  });
  const ownerVariables = formatVariableDataService.formatOwnerData({
    owner,
  });
  const communityVariables = formatVariableDataService.formatCommunityData(
    {
      community,
    }
  );
  const zeroLinkVariables = formatVariableDataService.formatZeroLinkData({
    zeroLink,
  });

  const mailRequest = {
    mailType,
    mailCourse: community.code ?? 'All',
    mailCourseOffer: 'All',
    data: {
      ...ownerVariables,
      ...communityVariables,
      ...zeroLinkVariables,
      member_email: memberVariables['member_email'],
      member_paid_amount: parseFloat(
        addonTransaction.priceDetails?.localAmount / 100
      ),
      member_paid_currency: addonTransaction.priceDetails?.localCurrency,
    },
  };

  switch (mailType) {
    case ZERO_LINK_MAIL_TYPES.MEMBER_ZERO_LINK_PAYMENT_SUCCESS: {
      mailRequest.toMail = [learner.email];
      mailRequest.toMailName = [memberVariables['member_name']];
      mailRequest.data['payment_succeed_time'] =
        retrievePaymentSucceedTimeFormat(
          addonTransaction.payment_details?.succeeded_time,
          learner.languagePreference,
          learner.timezone
        );

      break;
    }
    case ZERO_LINK_MAIL_TYPES.MANAGER_ZERO_LINK_PAYMENT_SUCCESS: {
      mailRequest.toMail = [owner.email];
      mailRequest.toMailName = [ownerVariables['owner_name']];
      mailRequest.data['payment_succeed_time'] =
        retrievePaymentSucceedTimeFormat(
          addonTransaction.payment_details?.succeeded_time,
          owner.languagePreference,
          owner.timezone
        );

      delete mailRequest.data.zero_link_redirect_link;
      delete mailRequest.data.zero_link_message;
      break;
    }
    default:
      break;
  }

  const isForManager =
    mailType === ZERO_LINK_MAIL_TYPES.MANAGER_ZERO_LINK_PAYMENT_SUCCESS;

  const managerEmailConfig = isForManager
    ? null
    : mailUtils.retrieveManagerMailConfig(
        community.title,
        community.link,
        owner.email,
        owner.name
      );

  const generateReceipt =
    !isForManager && addonTransaction && addonTransaction.amount > 0;

  const config = receiptService.generateReceiptConfig({
    purchasedId: addonTransaction._id,
    purchaseType: PURCHASE_TYPE.ZERO_LINK,
    entityObjectId: zeroLink._id,
    communityObjectId: zeroLink.communityObjectId,
    learnerObjectId: learner._id,
    generateReceipt,
  });

  await communityNotificationCommonService.sendMailToQueue(
    mailRequest.mailType,
    mailRequest.mailCourse,
    mailRequest.mailCourseOffer,
    mailRequest.toMail,
    mailRequest.toMailName,
    mailRequest.data,
    null,
    managerEmailConfig,
    config
  );
}

exports.handleEmailForZeroLinkPurchase = async (addonTransaction) => {
  if (
    addonTransaction.payment_details?.status !== PAYMENT_STATUSES.SUCCESS
  ) {
    logger.info(
      'Payment is not successful, skipping the success email flow'
    );
    return;
  }
  const { entityObjectId, communityObjectId, learnerObjectId } =
    addonTransaction;

  const [zeroLink, community, learner] = await Promise.all([
    ZeroLinkModel.findById(entityObjectId).lean(),
    CommunityModel.findById(communityObjectId).lean(),
    LearnerModel.findById(learnerObjectId).lean(),
  ]);
  const owner =
    await communityNotificationCommonService.retrieveCommunityOwnerInfo(
      community.code
    );

  await Promise.allSettled([
    formatAndSendZeroLinkSuccessEmail({
      mailType: ZERO_LINK_MAIL_TYPES.MANAGER_ZERO_LINK_PAYMENT_SUCCESS,
      owner,
      learner,
      zeroLink,
      community,
      addonTransaction,
    }),
    formatAndSendZeroLinkSuccessEmail({
      mailType: ZERO_LINK_MAIL_TYPES.MEMBER_ZERO_LINK_PAYMENT_SUCCESS,
      owner,
      learner,
      zeroLink,
      community,
      addonTransaction,
    }),
  ]);
};
