const { createEvents } = require('ics');

const logger = require('../logger.service');
const {
  getUploadedFileLinkFromUploadOptions,
} = require('../upload/index');
const {
  addShortUrl,
} = require('../../communitiesAPI/services/web/shortUrl.service');
const {
  getSessionTimeIntervals,
} = require('../../communitiesAPI/services/common/dateUtils');
const communityService = require('../../communitiesAPI/services/common/community.service');

const {
  awsAccessLevel,
  NAS_IO_SERVICE_EMAIL,
  EMAIL_BUCKET_LOCATION,
  EMAIL_ICS_PATH,
  NAS_IO_FRONTEND_URL,
} = require('../../config');
const {
  getEntityPublicPageUrl,
} = require('../../utils/memberPortalLinks.utils');
const { EVENT_TYPES } = require('../../constants/common');

// Code refactoring. These functions were taken from event/index.js
const getCalendarDateFormat = (timestamp) => {
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hours = date.getHours();
  const minutes = date.getMinutes();

  return [year, month, day, hours, minutes];
};

const createCalendarEvents = (eventData, sessions) => {
  const eventsData = sessions.map(({ start, end }) => {
    const formattedStart = getCalendarDateFormat(start);
    const formattedEnd = getCalendarDateFormat(end);
    const data = {
      ...eventData,
      start: formattedStart,
      end: formattedEnd,
    };
    return data;
  });
  const { error = null, value = null } = createEvents(eventsData);
  if (error) {
    logger.error('Unable to create events using ics package ', error);
  }
  return { error, value };
};

const getShortCodeFromFile = (filePath) => {
  const paths = filePath.split('/');
  const shortCode = paths[paths.length - 1].split('.')[0];
  return shortCode;
};

const generateAndUploadIcsFile = async (eventData, sessions, eventId) => {
  const { value = null } = createCalendarEvents(eventData, sessions);
  let icsEventString = null;
  if (value) {
    icsEventString = value;
  }
  if (icsEventString) {
    const icsFileName = `ce_${eventId}.ics`;
    const uploadOptions = {
      Bucket: EMAIL_BUCKET_LOCATION,
      Key: `${EMAIL_ICS_PATH}/${icsFileName}`,
      Body: icsEventString,
      ACL: awsAccessLevel,
      ContentType: 'text/calendar',
      ContentEncoding: 'utf-8',
    };
    const location = await getUploadedFileLinkFromUploadOptions(
      uploadOptions
    );
    return location;
  }
  return null;
};

exports.fetchIcsFileUrl = async (eventData) => {
  const { _id: eventId } = eventData;
  if (!(eventData.startTime && eventData.endTime)) {
    return '';
  }
  const sessionIntervals = getSessionTimeIntervals(
    eventData.startTime,
    eventData.endTime
  );
  logger.info('Session Intervals: ', sessionIntervals);

  const communityId = eventData?.communities?.[0];
  const community = await communityService.getCommunityById(communityId);

  const data = {
    title: eventData?.title,
    organizer: { name: 'Nas', email: NAS_IO_SERVICE_EMAIL },
    description: eventData?.description,
    attendees: [{ name: 'Nas', email: NAS_IO_SERVICE_EMAIL }],
    url: getEntityPublicPageUrl({
      communitySlug: community.link,
      entityType: 'event',
      entitySlug: eventData.slug,
    }),
  };

  if (
    eventData.type === EVENT_TYPES.INPERSON &&
    eventData.inPersonLocationMetadata?.name
  ) {
    data.location = [
      eventData.inPersonLocationMetadata.name,
      eventData.inPersonLocationMetadata.formatted_address ?? '',
    ].join(', ');
  }

  const icsFileUrl = await generateAndUploadIcsFile(
    data,
    sessionIntervals,
    eventId
  );
  logger.info('Uploaded ics file url: ', icsFileUrl);
  if (icsFileUrl) {
    const shortCode = getShortCodeFromFile(icsFileUrl);
    await addShortUrl(
      eventData.description || eventData.title,
      shortCode,
      icsFileUrl
    );
    return `${NAS_IO_FRONTEND_URL}/${shortCode}`;
  }
  return null;
};
