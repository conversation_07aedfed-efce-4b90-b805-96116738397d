const jwt = require('jsonwebtoken');

const axios = require('../../clients/axios.client');
const {
  DISCORD_URL,
  DISCORD_AUTH,
  NAS_IO_FRONTEND_URL,
} = require('../../config');

function formatDiscordEvent(community, event) {
  const { startTime, endTime, title, description, slug } = event;
  const communityLink = community?.link;
  const eventLink = communityLink
    ? `${NAS_IO_FRONTEND_URL}${communityLink}${slug}`
    : NAS_IO_FRONTEND_URL;

  return {
    startTime,
    endTime,
    name: title,
    description: description?.slice(0, 999),
    location: eventLink,
  };
}

exports.createDiscordEvent = async (community, event) => {
  const discordServer = community?.bots?.filter(
    (bot) => bot?.type === 'Discord'
  );
  if (discordServer.length === 0) {
    return;
  }
  const serverKey = discordServer?.[0]?.serverKey;
  const discordEventData = formatDiscordEvent(community, event);
  const requestUrl = `${DISCORD_URL}/api/v1/createEvent/${serverKey}`;
  const authHeader = jwt.sign({}, DISCORD_AUTH);
  const response = await axios.post(requestUrl, discordEventData, {
    headers: {
      Authorization: `Bearer ${authHeader}`,
    },
  });
  if (response?.data) {
    return response?.data?.event;
  }
};

exports.deleteDiscordEvent = async (community, discordEventId) => {
  const discordServer = community?.bots?.filter(
    (bot) => bot?.type === 'Discord'
  );
  if (discordServer.length === 0) {
    return;
  }
  const serverKey = discordServer?.[0]?.serverKey;
  const requestUrl = `${DISCORD_URL}/api/v1/deleteEvent/${serverKey}/${discordEventId}`;
  const authHeader = jwt.sign({}, DISCORD_AUTH);
  await axios.delete(requestUrl, {
    headers: {
      Authorization: `Bearer ${authHeader}`,
    },
  });
};
