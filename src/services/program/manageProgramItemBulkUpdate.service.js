const { ObjectId } = require('mongoose').Types;
const storageUsageService = require('@services/featurePermissions/storageUsage.service');
const commonService = require('./common.service');
const PrimaryMongooseConnection = require('../../rpc/primaryMongooseConnection');
const ProgramItemModel = require('../../models/program/programItem.model');
const ProgramParticipantModel = require('../../models/program/programParticipant.model');
const { ParamError } = require('../../utils/error.util');
const {
  updateCheckpointEvent,
} = require('./manageProgramItemEvent.service');
const { updateUpsellForCheckpoint } = require('./upsell.service');
const {
  isDescriptionNotEmpty,
  updateProgramAllCheckpointsNotEmptyField,
  getCoverVideoMap,
  deleteMedia,
} = require('./manageProgramItem.service');
const {
  PROGRAM_CHALLENGE_TYPE,
  PROGRAM_ITEM_TYPE,
  PARTICIPANT_PROGRAM_STATUS,
  PROGRAM_ITEM_STATUS,
} = require('./constants');
const scheduledNotificationService = require('./scheduledNotification.service');
const {
  validateCheckpointStartEndDates,
} = require('./programItemDateValidation.service');
const CommunityFolderItems = require('../../communitiesAPI/models/communityFolderItems.model');
const logger = require('../logger.service');
const {
  purgeEntityLandingPageCache,
  ENTITY_LANDING_PAGE,
} = require('@/src/utils/memberPortalLinks.utils');

const assignNewIndex = ({
  reorderedCheckpoints,
  createdCheckpoints,
  updatedCheckpoints,
}) => {
  if (!reorderedCheckpoints || reorderedCheckpoints.length === 0) {
    // Remove all index assignment in updatedCheckpoints, because we will use the index in reorderedCheckpoints
    // eslint-disable-next-line no-param-reassign
    updatedCheckpoints = updatedCheckpoints.map((checkpoint) => {
      const { index, ...rest } = checkpoint; // Destructure to exclude `index`
      return rest; // Return the object without `index`
    });
    return;
  }

  // Create maps for quick lookup by _id in createdCheckpoints and updatedCheckpoints
  const createdCheckpointMap = new Map(
    createdCheckpoints.map((cp) => [cp._id, cp])
  );
  const updatedCheckpointMap = new Map(
    updatedCheckpoints.map((cp) => [cp._id, cp])
  );

  // Iterate over reorderedCheckpoints and assign new indexes
  reorderedCheckpoints.forEach((checkpointId, index) => {
    const newIndex = index + 1;

    // Update index if checkpoint is in updatedCheckpoints
    if (updatedCheckpointMap.has(checkpointId)) {
      updatedCheckpointMap.get(checkpointId).index = newIndex;
    }
    // Update index if checkpoint is in createdCheckpoints
    else if (createdCheckpointMap.has(checkpointId)) {
      createdCheckpointMap.get(checkpointId).index = newIndex;
    } else {
      updatedCheckpoints.push({
        _id: new ObjectId(checkpointId),
        index: newIndex,
      });
    }
  });
};

const containsOnlyOneWelcomeCheckpoint = (checkpoints = []) =>
  checkpoints.length === 1 && Boolean(checkpoints[0]?.isWelcomeCheckpoint);

const validationOfCheckpointUpdate = async ({ program, params }) => {
  const {
    createdCheckpoints = [],
    reorderedCheckpoints = [],
    deletedCheckpoints = [],
  } = params;
  if (program.challengeType === PROGRAM_CHALLENGE_TYPE.FIXED) {
    if (
      createdCheckpoints.length > 0 &&
      !containsOnlyOneWelcomeCheckpoint(createdCheckpoints)
    ) {
      throw new ParamError(
        `Only always on challenge can have new checkpoints created`
      );
    }
    if (
      reorderedCheckpoints.length > 0 &&
      !containsOnlyOneWelcomeCheckpoint(reorderedCheckpoints)
    ) {
      throw new ParamError(
        `Only always on challenge can reorder checkpoints`
      );
    }
    if (
      deletedCheckpoints.length > 0 &&
      !containsOnlyOneWelcomeCheckpoint(deletedCheckpoints)
    ) {
      throw new ParamError(
        `Only always on challenge can delete checkpoints`
      );
    }
  }

  const existingCheckpoints = await ProgramItemModel.find({
    programObjectId: program._id,
    _id: { $nin: deletedCheckpoints },
    status: PROGRAM_ITEM_STATUS.ACTIVE,
    isWelcomeCheckpoint: {
      $ne: true,
    },
  })
    .select('_id startTime endTime')
    .sort({ index: 1 })
    .lean();

  // If there is checkpoint create or delete,
  // FE need to pass new array of checkpoints in reorderedCheckpoints
  // we do this check for always on only because we don't have to reorder for fixed challenge
  if (
    (createdCheckpoints.length > 0 || deletedCheckpoints.length > 0) &&
    program.challengeType === PROGRAM_CHALLENGE_TYPE.ALWAYS_ON
  ) {
    if (
      existingCheckpoints.length === 0 &&
      createdCheckpoints.length === 0
    ) {
      throw new ParamError(`You cannot delete all checkpoints`);
    }

    const existingCheckpointIds = existingCheckpoints.map((cp) =>
      cp._id.toString()
    );
    const createdCheckpointIds = createdCheckpoints.map((cp) =>
      cp._id.toString()
    );

    // Combine all IDs from existing and created checkpoints
    const requiredCheckpointIds = new Set([
      ...existingCheckpointIds,
      ...createdCheckpointIds,
    ]);

    // Check for any missing IDs in reorderedCheckpoints
    const missingIds = Array.from(requiredCheckpointIds).filter(
      (id) => !reorderedCheckpoints.includes(id)
    );

    if (missingIds.length > 0) {
      throw new ParamError(
        `Missing checkpoint IDs not found in reorderedCheckpoints: ${missingIds}`
      );
    }
  }

  // Validate the start and end dates for fixed challenge
  validateCheckpointStartEndDates({
    program,
    params,
    existingCheckpoints,
  });
};

const updateCheckpointData = async ({
  item,
  toDeleteMedia,
  mediaItemMap,
}) => {
  let isProgramItemNotEmpty = false;
  const $set = {};
  const $unset = {};
  if (item.title) {
    $set.title = item.title;
  }
  if (item.description) {
    if (item.description.root) {
      $set.description = item.description;
    }

    if (isDescriptionNotEmpty(item.description.root)) {
      isProgramItemNotEmpty = true;
    }
  }
  if (item.submissionQuestions) {
    if (item.submissionQuestions.length > 0) {
      isProgramItemNotEmpty = true;
    }
    const questions = [];
    item.submissionQuestions.forEach((question) => {
      if (question.questionText) {
        questions.push({
          questionText: question.questionText,
          type: question.type || 'text',
          required:
            question.required !== undefined && question.required !== null
              ? question.required
              : true,
        });
      }
    });
    $set.submissionRequired = questions.length > 0;
    $set.submissionQuestions = questions;
  }

  if (item.attachmentItems?.length) {
    const attachments = [];
    item.attachmentItems.forEach((attachment) => {
      attachments.push({
        mediaObjectId: new ObjectId(attachment._id),
      });
    });
    $set.attachmentItems = attachments;
  }
  if (item.coverVideo) {
    if (Object.keys(item.coverVideo).length > 0) {
      isProgramItemNotEmpty = true;
    }
    if (!item.coverVideo.mediaObjectId) {
      $unset.coverVideo = {};
      if (item._id instanceof ObjectId) {
        toDeleteMedia.push(item._id);
      }
    } else {
      const mediaItem = mediaItemMap[item.coverVideo.mediaObjectId];
      if (!mediaItem) {
        throw new ParamError(
          `Media item ${item.coverVideo.mediaObjectId} not found`
        );
      }
      $set.coverVideo = {
        mediaObjectId: mediaItem._id,
      };
    }
  }
  if (item.startTime) {
    $set.startTime = item.startTime;
  }
  if (item.endTime) {
    $set.endTime = item.endTime;
  }
  // because we will pass index 0 as well
  if ('index' in item) {
    $set.index = item.index;
  }

  if (item.isWelcomeCheckpoint) {
    $set.isWelcomeCheckpoint = item.isWelcomeCheckpoint;
  }
  if (item.unlockAfterXDays >= 0) {
    $set.unlockAfterXDays = item.unlockAfterXDays;
  }
  $set.isEmpty = !isProgramItemNotEmpty;

  return { $set, $unset };
};

// Helper function to batch fetch all attachment sizes
const batchFetchAttachmentSizes = async (allAttachmentItems) => {
  if (!allAttachmentItems || allAttachmentItems.length === 0) {
    return new Map();
  }

  const mediaObjectIds = allAttachmentItems
    .filter((item) => item && (item.mediaObjectId || item._id))
    .map((item) => item.mediaObjectId || item._id);

  const uniqueIds = [
    ...new Set(mediaObjectIds.map((id) => id.toString())),
  ];

  const folderItems = await CommunityFolderItems.find({
    _id: { $in: uniqueIds },
    size: { $exists: true, $ne: null },
  })
    .select('_id size')
    .lean();

  // Create a map for O(1) lookup
  const sizeMap = new Map();
  folderItems.forEach((item) => {
    sizeMap.set(item._id.toString(), parseInt(item.size, 10) || 0);
  });

  return sizeMap;
};

// Helper function to calculate storage from pre-fetched sizes
const calculateStorageFromMap = (attachmentItems, sizeMap) => {
  if (!attachmentItems || attachmentItems.length === 0) {
    return 0;
  }

  return attachmentItems.reduce((total, item) => {
    const id = (item.mediaObjectId || item._id)?.toString();
    return total + (sizeMap.get(id) || 0);
  }, 0);
};

exports.bulkUpdateProgramItems = async ({
  community,
  programId,
  params,
  managerEmail,
}) => {
  const [program, ongoingProgramItem] = await Promise.all([
    commonService.getProgram(programId),
    commonService.getOngoingProgramItem({
      program: { _id: programId },
      type: PROGRAM_ITEM_TYPE.CHECKPOINT,
      projection: { _id: 1 },
    }),
  ]);

  await validationOfCheckpointUpdate({ program, params });

  // Handle updated checkpoints
  const {
    createdCheckpoints = [],
    reorderedCheckpoints = [],
    updatedCheckpoints = [],
    deletedCheckpoints = [],
  } = params;
  let programItemIds;
  const mediaItemMap = await getCoverVideoMap([
    ...createdCheckpoints,
    ...updatedCheckpoints,
  ]);
  let needToUpdateCheckpointSchedule = false;
  let updatedOngoingProgramItemEndDate;
  const toDeleteMedia = [];

  // Assign new index of each checkpoint
  assignNewIndex({
    reorderedCheckpoints,
    createdCheckpoints,
    updatedCheckpoints,
  });

  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  // Batch fetch all checkpoints that will be deleted or updated
  const checkpointsToFetch = [
    ...deletedCheckpoints,
    ...updatedCheckpoints.map((item) => item._id),
  ];

  const existingCheckpoints = await ProgramItemModel.find({
    _id: { $in: checkpointsToFetch },
  })
    .select('_id attachmentItems')
    .lean();

  // Create a map for O(1) lookup
  const existingCheckpointMap = new Map();
  existingCheckpoints.forEach((cp) => {
    existingCheckpointMap.set(cp._id.toString(), cp);
  });

  // Collect all attachment items that need size lookup
  const allAttachmentItems = [];

  // From created checkpoints
  createdCheckpoints.forEach((item) => {
    if (item.attachmentItems) {
      allAttachmentItems.push(...item.attachmentItems);
    }
  });

  // From existing checkpoints (for updates and deletes)
  existingCheckpoints.forEach((cp) => {
    if (cp.attachmentItems) {
      allAttachmentItems.push(...cp.attachmentItems);
    }
  });

  // From updated checkpoints (new attachments)
  updatedCheckpoints.forEach((item) => {
    if (item.attachmentItems) {
      allAttachmentItems.push(...item.attachmentItems);
    }
  });

  // Batch fetch all attachment sizes
  const attachmentSizeMap = await batchFetchAttachmentSizes(
    allAttachmentItems
  );

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  // Track storage usage changes
  let totalStorageChange = 0;
  const storageOperations = [];

  try {
    // Handle created checkpoints
    for await (const item of createdCheckpoints) {
      const { $set, $unset } = await updateCheckpointData({
        item,
        toDeleteMedia,
        mediaItemMap,
      });
      // Update event
      await updateCheckpointEvent({
        item,
        $set,
        $unset,
      });
      const [result] = await ProgramItemModel.create(
        [
          {
            type: PROGRAM_ITEM_TYPE.CHECKPOINT,
            programObjectId: program._id,
            communityObjectId: community._id,
            ...$set,
          },
        ],
        {
          session,
        }
      );

      if (params.createdUpsells && params.createdUpsells.length > 0) {
        params.createdUpsells.forEach((upsell) => {
          if (upsell.sourceEntityObjectId === item._id) {
            // item._id === new_1
            // result._id === 67247ee89e1ccb50ad25bda9
            // eslint-disable-next-line no-param-reassign
            upsell.sourceEntityObjectId = result._id.toString();
          }
        });
      }

      item._id = result._id;

      // Calculate storage for new checkpoint attachments
      if ($set.attachmentItems && $set.attachmentItems.length > 0) {
        const attachmentSize = calculateStorageFromMap(
          $set.attachmentItems,
          attachmentSizeMap
        );
        if (attachmentSize > 0) {
          totalStorageChange += attachmentSize;
          storageOperations.push({
            type: 'create',
            checkpointId: result._id,
            size: attachmentSize,
            attachments: $set.attachmentItems,
          });
        }
      }

      // when there is newly created checkpoint, we need to update schedule
      // for now, this flag is not applicable, because fixed challenge cannot have new checkpoint
      needToUpdateCheckpointSchedule = true;

      if (result.isWelcomeCheckpoint) {
        needToUpdateCheckpointSchedule = false;
      }
    }

    const operations =
      ProgramItemModel.collection.initializeUnorderedBulkOp({
        readPreference: 'primary',
      });

    // Handle deleted checkpoints
    for (const itemId of deletedCheckpoints) {
      // Use pre-fetched checkpoint data
      const checkpoint = existingCheckpointMap.get(itemId.toString());
      if (
        checkpoint &&
        checkpoint.attachmentItems &&
        checkpoint.attachmentItems.length > 0
      ) {
        const attachmentSize = calculateStorageFromMap(
          checkpoint.attachmentItems,
          attachmentSizeMap
        );
        if (attachmentSize > 0) {
          totalStorageChange -= attachmentSize;
          storageOperations.push({
            type: 'delete',
            checkpointId: itemId,
            size: attachmentSize,
            attachments: checkpoint.attachmentItems,
          });
        }
      }

      operations.find({ _id: new ObjectId(itemId) }).updateOne({
        $set: {
          status: PROGRAM_ITEM_STATUS.DELETED,
        },
      });
      toDeleteMedia.push(itemId);
    }

    // Handle updated checkpoints
    programItemIds = updatedCheckpoints.map((item) => item._id);
    for await (const item of updatedCheckpoints) {
      // Use pre-fetched checkpoint data
      const existingCheckpoint = existingCheckpointMap.get(
        item._id.toString()
      );

      const { $set, $unset } = await updateCheckpointData({
        item,
        toDeleteMedia,
        mediaItemMap,
      });

      // Calculate storage difference for updated attachments
      if ($set.attachmentItems || existingCheckpoint?.attachmentItems) {
        const oldAttachments = existingCheckpoint?.attachmentItems || [];
        const newAttachments = $set.attachmentItems || [];

        // Calculate old and new storage using pre-fetched sizes
        const oldStorageSize = calculateStorageFromMap(
          oldAttachments,
          attachmentSizeMap
        );
        const newStorageSize = calculateStorageFromMap(
          newAttachments,
          attachmentSizeMap
        );
        const storageDiff = newStorageSize - oldStorageSize;

        if (storageDiff !== 0) {
          totalStorageChange += storageDiff;
          storageOperations.push({
            type: 'update',
            checkpointId: item._id,
            sizeDiff: storageDiff,
            oldAttachments,
            newAttachments,
          });
        }
      }

      if (
        item._id === ongoingProgramItem?._id?.toString() &&
        item.endTime
      ) {
        updatedOngoingProgramItemEndDate = item.endTime;
      }

      // Update event
      const checkpointId = await updateCheckpointEvent({
        item,
        $set,
        $unset,
      });
      operations
        .find({ _id: new ObjectId(item._id) })
        .updateOne({ $set, $unset });

      // If the start time/end time/or event got updated, we need to update checkpoint reminder schedule
      if ($set.startTime || $set.endTime || checkpointId) {
        needToUpdateCheckpointSchedule = true;
      }
    }

    if (operations.batches.length > 0) {
      await operations.execute({ session });
    }

    await deleteMedia({ toDeleteMedia, session });

    // Create/Update upsell
    await updateUpsellForCheckpoint({
      email: managerEmail,
      communityObjectId: community._id,
      createdUpsells: params.createdUpsells,
      updatedUpsells: params.updatedUpsells,
      deletedUpsells: params.deletedUpsells,
      deletedCheckpoints,
      session,
    });

    // Update all completed participants to be incomplete when there is new checkpoint added
    if (createdCheckpoints.length > 0) {
      await ProgramParticipantModel.updateMany(
        {
          programObjectId: program._id,
          status: {
            $in: [
              PARTICIPANT_PROGRAM_STATUS.COMPLETED,
              PARTICIPANT_PROGRAM_STATUS.PARTICIPATED,
            ],
          },
        },
        {
          status: PARTICIPANT_PROGRAM_STATUS.PARTICIPATED,
          'checkpointReminder.allCheckpointReminderSent': false,
        },
        { session }
      );
    }

    await session.commitTransaction();

    // Update storage usage after successful transaction
    if (totalStorageChange !== 0) {
      try {
        if (totalStorageChange > 0) {
          await storageUsageService.incrementStorageUsage(
            community._id,
            totalStorageChange
          );
          logger.info(
            'Storage usage incremented for bulk checkpoint update',
            {
              communityId: community._id,
              programId,
              storageIncrease: totalStorageChange,
              operations: storageOperations.length,
            }
          );
        } else {
          await storageUsageService.decrementStorageUsage(
            community._id,
            Math.abs(totalStorageChange)
          );
          logger.info(
            'Storage usage decremented for bulk checkpoint update',
            {
              communityId: community._id,
              programId,
              storageDecrease: Math.abs(totalStorageChange),
              operations: storageOperations.length,
            }
          );
        }
      } catch (storageError) {
        logger.error(
          'Failed to update storage usage for bulk checkpoint update',
          {
            communityId: community._id,
            programId,
            totalStorageChange,
            operations: storageOperations,
            error: storageError.message,
          }
        );
      }
    }
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }

  await updateProgramAllCheckpointsNotEmptyField(program, programId, true);

  if (updatedOngoingProgramItemEndDate && ongoingProgramItem) {
    await commonService.sendCheckpointEndDateChangesToQueue({
      communityObjectId: community._id,
      programObjectId: programId,
      programItemObjectId: ongoingProgramItem._id,
      leaderboardConfigs: program.leaderboardConfigs,
      programItemEndTime: updatedOngoingProgramItemEndDate,
    });
  }

  if (needToUpdateCheckpointSchedule) {
    // Send the checkpoint and events mobile notification (for fixed challenge)
    await scheduledNotificationService.addChallengeAndCheckpointNotificationSchedule(
      {
        program,
        community,
      }
    );

    // Send the checkpoint and events email noti (for fixed challenge)
    await scheduledNotificationService.addCheckpointEmailSchedules({
      challengeId: program._id,
      community,
      program,
    });
  }

  await purgeEntityLandingPageCache({
    community,
    entityType: ENTITY_LANDING_PAGE.CHALLENGE,
    entitySlug: program.slug,
  });

  await purgeEntityLandingPageCache({
    community,
    entityType: ENTITY_LANDING_PAGE.CHALLENGE,
    entitySlug: program.slug,
  });

  const updatedProgramItems = await ProgramItemModel.find(
    { _id: { $in: programItemIds } },
    {
      title: 1,
      description: 1,
      startTime: 1,
      endTime: 1,
      index: 1,
      submissionRequired: 1,
      submissionQuestions: 1,
      coverVideo: 1,
      attachmentItems: 1,
      event: 1,
      unlockAfterXDays: 1,
    }
  );
  return updatedProgramItems;
};
