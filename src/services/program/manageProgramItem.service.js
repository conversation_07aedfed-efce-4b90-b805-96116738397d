/* eslint-disable no-await-in-loop */
const { DateTime } = require('luxon');
const { ObjectId } = require('mongoose').Types;
const Program = require('../../models/program/program.model');
const ProgramItem = require('../../models/program/programItem.model');
const CommunityFolderItems = require('../../communitiesAPI/models/communityFolderItems.model');
const communityFolderItemService = require('../../communitiesAPI/services/common/communityFolderItems.service');
const commonService = require('./common.service');
const scheduledNotificationService = require('./scheduledNotification.service');
const logger = require('../logger.service');
const { ParamError, InternalError } = require('../../utils/error.util');

const {
  communityLibraryStatusMap,
} = require('../../communitiesAPI/constants');
const { PROGRAM_ITEM_TYPE, PROGRAM_ITEM_STATUS } = require('./constants');
const PrimaryMongooseConnection = require('../../rpc/primaryMongooseConnection');
const { EVENT_TYPES } = require('../../constants/common');
const communityFolderItemsModel = require('../../communitiesAPI/models/communityFolderItems.model');
const {
  validateCheckpointStartEndDates,
} = require('./programItemDateValidation.service');
const {
  purgeEntityLandingPageCache,
  ENTITY_LANDING_PAGE,
} = require('../../utils/memberPortalLinks.utils');

async function getCoverVideoMap(programItems) {
  const mediaIds = [];
  for (const item of programItems) {
    if (item.coverVideo?.mediaObjectId) {
      mediaIds.push(item.coverVideo.mediaObjectId);
    }
  }
  const mediaItems = await CommunityFolderItems.find(
    {
      _id: { $in: mediaIds },
    },
    {
      _id: 1,
    }
  );
  const mediaItemMap = {};
  mediaItems.forEach((media) => {
    mediaItemMap[media._id] = media;
  });
  return mediaItemMap;
}

const updateProgramAllCheckpointsNotEmptyField = async (
  program,
  programId,
  hasFilledUpCheckpoints = false
) => {
  const [notEmptyCheckpointsCount, totalCheckpointsCount] =
    await Promise.all([
      ProgramItem.countDocuments({
        programObjectId: programId,
        isEmpty: false,
        type: PROGRAM_ITEM_TYPE.CHECKPOINT,
      }),
      ProgramItem.countDocuments({
        programObjectId: programId,
        type: PROGRAM_ITEM_TYPE.CHECKPOINT,
      }),
    ]);

  const updateProgramData = {
    'challengeSpecific.areAllCheckpointsNotEmpty':
      notEmptyCheckpointsCount === totalCheckpointsCount,
    'challengeSpecific.hasFilledUpCheckpoints': hasFilledUpCheckpoints,
  };

  await Program.updateOne({ _id: programId }, updateProgramData);
};

const isDescriptionNotEmpty = (root) => {
  if (!root) {
    return false;
  }

  if (
    root.children &&
    root.children.some((child) => child.text && child.text.trim() !== '')
  ) {
    return true;
  }

  if (root.children) {
    for (const child of root.children) {
      if (isDescriptionNotEmpty(child)) {
        return true;
      }
    }
  }

  return false;
};

const deleteMedia = async ({ toDeleteMedia, session }) => {
  if (toDeleteMedia.length <= 0) {
    return;
  }
  const items = await ProgramItem.find(
    { _id: { $in: toDeleteMedia } },
    { coverVideo: 1, attachmentItems: 1 }
  );
  const toDeleteMediaIds = [];
  for (const item of items) {
    if (item.coverVideo?.mediaObjectId) {
      toDeleteMediaIds.push(item.coverVideo.mediaObjectId);
    }

    if (item.attachmentItems?.length > 0) {
      item.attachmentItems.forEach((attachment) => {
        toDeleteMediaIds.push(attachment.mediaObjectId);
      });
    }
  }
  await CommunityFolderItems.updateMany(
    {
      _id: { $in: toDeleteMediaIds },
    },
    {
      $set: {
        status: communityLibraryStatusMap.DELETED,
      },
    },
    {
      session,
    }
  );
};

const updateProgramItems = async ({
  programId,
  programItems,
  community,
}) => {
  const program = await commonService.getProgram(programId);
  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  const programItemIds = programItems.map((item) => item._id);
  const mediaItemMap = await getCoverVideoMap(programItems);
  try {
    const operations = ProgramItem.collection.initializeUnorderedBulkOp({
      readPreference: 'primary',
    });
    const toDeleteMedia = [];
    for (const item of programItems) {
      let isProgramItemNotEmpty = false;
      const $set = {};
      const $unset = {};
      if (item.title) {
        $set.title = item.title;
      }
      if (item.description) {
        $set.description = item.description;
        if (isDescriptionNotEmpty(item.description.root)) {
          isProgramItemNotEmpty = true;
        }
      }
      if (item.submissionQuestions) {
        if (item.submissionQuestions.length > 0) {
          isProgramItemNotEmpty = true;
        }
        const questions = [];
        item.submissionQuestions.forEach((question) => {
          if (question.questionText) {
            questions.push({
              questionText: question.questionText,
              type: question.type || 'text',
              required:
                question.required !== undefined &&
                question.required !== null
                  ? question.required
                  : true,
            });
          }
        });
        $set.submissionRequired = questions.length > 0;
        $set.submissionQuestions = questions;
      }

      if (item.attachmentItems) {
        const attachments = [];
        item.attachmentItems.forEach((attachment) => {
          attachments.push({
            mediaObjectId: new ObjectId(attachment._id),
          });
        });
        $set.attachmentItems = attachments;
      }
      if (item.coverVideo) {
        if (Object.keys(item.coverVideo).length > 0) {
          isProgramItemNotEmpty = true;
        }
        if (!item.coverVideo.mediaObjectId) {
          $set.coverVideo = {};
          toDeleteMedia.push(item._id);
        } else {
          const mediaItem = mediaItemMap[item.coverVideo.mediaObjectId];
          if (!mediaItem) {
            throw new ParamError(
              `Media item ${item.coverVideo.mediaObjectId} not found`
            );
          }
          $set.coverVideo = {
            mediaObjectId: mediaItem._id,
          };
        }
      }

      $set.isEmpty = !isProgramItemNotEmpty;

      operations
        .find({ _id: new ObjectId(item._id) })
        .updateOne({ $set, $unset });
    }
    await operations.execute({ session });

    await deleteMedia({ toDeleteMedia, session });
    await session.commitTransaction();
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }

  await updateProgramAllCheckpointsNotEmptyField(program, programId, true);

  await purgeEntityLandingPageCache({
    community,
    purgeCommunityLandingPage: false,
    entityType: ENTITY_LANDING_PAGE.CHALLENGE,
    entitySlug: program.slug,
  });

  const updatedProgramItems = await ProgramItem.find(
    { _id: { $in: programItemIds } },
    {
      title: 1,
      description: 1,
      startTime: 1,
      endTime: 1,
      index: 1,
      submissionRequired: 1,
      submissionQuestions: 1,
      coverVideo: 1,
      attachmentItems: 1,
      event: 1,
    }
  );
  return updatedProgramItems;
};

const updateOneCheckpointEvent = async (
  community,
  challengeId,
  checkpointId,
  eventData
) => {
  const toUpdateData = eventData;

  if (eventData.type === EVENT_TYPES.INPERSON) {
    toUpdateData.liveLink = '';
  } else if (eventData.type === EVENT_TYPES.LIVE) {
    toUpdateData.inPersonLocation = '';
  }

  const checkpoint = await ProgramItem.findOne({
    _id: checkpointId,
    programObjectId: challengeId,
  });

  if (!checkpoint) {
    throw new ParamError('Checkpoint not found');
  }

  if (checkpoint.endTime < new Date()) {
    throw new ParamError('Cannot update past checkpoint');
  }

  const updatedCheckpoint = await ProgramItem.findOneAndUpdate(
    {
      _id: checkpoint._id,
    },
    {
      $set: {
        event: toUpdateData,
      },
    },
    { new: true }
  );

  await scheduledNotificationService.updateCheckpointEventMobileNotifications(
    {
      challengeId,
      checkpointId,
      event: updatedCheckpoint.event,
      community,
    }
  );
  await scheduledNotificationService.updateCheckpointEventEmailNotifications(
    {
      challengeId,
      checkpointId,
      event: updatedCheckpoint.event,
      community,
    }
  );

  return updatedCheckpoint;
};

const deleteOneCheckpointEvent = async (challengeId, checkpointId) => {
  const checkpoint = await ProgramItem.findOne({
    _id: checkpointId,
    programObjectId: challengeId,
  });
  if (!checkpoint) {
    throw new ParamError('Checkpoint not found');
  }
  if (checkpoint.endTime < new Date()) {
    throw new ParamError('Cannot update past checkpoint');
  }

  const updatedCheckpoint = await ProgramItem.findOneAndUpdate(
    {
      _id: checkpoint._id,
    },
    {
      $unset: { event: '' },
    },
    { new: true }
  );

  await scheduledNotificationService.updateCheckpointEventMobileNotifications(
    {
      challengeId,
      checkpointId,
      event: updatedCheckpoint.event,
    }
  );
  await scheduledNotificationService.updateCheckpointEventEmailNotifications(
    {
      challengeId,
      checkpointId,
      event: updatedCheckpoint.event,
    }
  );

  return updatedCheckpoint;
};

const generateAttachmentItems = ({
  communityFolderObjectId,
  sourceAttachmentItems,
}) => {
  const newAttachmentItems = [];
  for (const attachmentInfo of sourceAttachmentItems) {
    const newAttachmentItemObj = {
      ...attachmentInfo,
      communityFolderObjectId,
    };
    delete newAttachmentItemObj._id;
    delete newAttachmentItemObj.createdAt;
    delete newAttachmentItemObj.updatedAt;
    delete newAttachmentItemObj.__v;
    newAttachmentItems.push(newAttachmentItemObj);
  }
  return newAttachmentItems;
};
const duplicateProgramItem = async ({
  community,
  programId,
  source,
  destination = [],
  duplicateToAll = false,
}) => {
  const program = await commonService.getProgram(programId);
  const sourceProgramItem = await ProgramItem.findOne({
    _id: new ObjectId(source),
  });
  if (!sourceProgramItem) {
    throw new ParamError('Source program item not found');
  }

  let commonFilters;
  if (duplicateToAll) {
    commonFilters = {
      programObjectId: new ObjectId(programId),
      type: sourceProgramItem.type,
    };
  } else {
    commonFilters = {
      _id: { $in: destination },
    };
  }

  const results = {};
  const isEmptyResult = await ProgramItem.updateMany(commonFilters, {
    $set: {
      isEmpty: sourceProgramItem.isEmpty,
    },
  });
  results.isEmptyResult = isEmptyResult;
  logger.info('isEmptyResult updated', isEmptyResult);

  const descriptionResult = await ProgramItem.updateMany(commonFilters, {
    $set: {
      description: sourceProgramItem.description,
    },
  });
  results.description = descriptionResult;
  logger.info('Description updated', descriptionResult);
  const submissionResult = await ProgramItem.updateMany(
    {
      ...commonFilters,
      startTime: { $gt: DateTime.utc() },
    },
    {
      $set: {
        submissionRequired: sourceProgramItem.submissionRequired,
        submissionQuestions: sourceProgramItem.submissionQuestions,
      },
    }
  );
  results.submission = submissionResult;
  logger.info('Submission updated', submissionResult);

  if (sourceProgramItem.coverVideo?.mediaObjectId) {
    let finalDestination = destination;
    const sourceMediaData = await commonService.getMediaData({
      mediaObjectId: sourceProgramItem.coverVideo.mediaObjectId,
    });
    if (duplicateToAll === true) {
      finalDestination = await ProgramItem.find(
        {
          programObjectId: programId,
          _id: { $ne: sourceProgramItem._id },
        },
        {
          _id: 1,
        }
      );
      finalDestination = finalDestination.map((item) => item._id);
    }
    const sameVideoItems = await CommunityFolderItems.find(
      {
        videoObjectId: sourceMediaData.video?._id,
        communityFolderObjectId: { $in: finalDestination },
      },
      { communityFolderObjectId: 1 }
    );
    const filterOutSet = new Set(
      sameVideoItems.map((item) => item.communityFolderObjectId.toString())
    );
    finalDestination = finalDestination.filter(
      (item) => !filterOutSet.has(item.toString())
    );

    const chunkedDestinations = [];
    for (let i = 0; i < finalDestination.length; i += 100) {
      chunkedDestinations.push(finalDestination.slice(i, i + 100));
    }
    const mediaResults = [];
    let chunkIndex = 0;
    for (const chunk of chunkedDestinations) {
      const primaryMongooseConnection =
        await PrimaryMongooseConnection.connect();

      const session = await primaryMongooseConnection.startSession();
      session.startTransaction();
      try {
        const newMediaItems =
          await communityFolderItemService.duplicateFolderItem({
            folderItemObjectId: sourceProgramItem.coverVideo.mediaObjectId,
            associatedCommunityFolderObjectIds: chunk,
            session,
          });
        if (newMediaItems.length !== chunk.length) {
          logger.error(
            'Failed to duplicate cover video media items.',
            `Chunk count is ${chunk.length} but newMediaItems count is ${newMediaItems.length}`
          );
          throw new InternalError(
            'Failed to duplicate cover video media items'
          );
        }
        const operations =
          ProgramItem.collection.initializeUnorderedBulkOp({
            readPreference: 'primary',
          });
        for (const newMediaItem of newMediaItems) {
          operations
            .find({ _id: newMediaItem.communityFolderObjectId })
            .updateOne({
              $set: {
                coverVideo: {
                  mediaObjectId: newMediaItem._id,
                },
              },
            });
        }
        const result = await operations.execute({ session });
        await session.commitTransaction();
        mediaResults.push(result);
        logger.info('Cover video duplicated for chunk', chunkIndex);
        chunkIndex += 1;
      } catch (error) {
        await session.abortTransaction();
        mediaResults.push(
          `Error occured for chunk ${chunkIndex}: ${error.message}`
        );
        logger.error(
          `Error occured for chunk ${chunkIndex}`,
          error,
          error.stack
        );
      } finally {
        await session.endSession();
      }
    }
    results.media = mediaResults;
  }

  if (sourceProgramItem.attachmentItems?.length > 0) {
    // first I have to get all the other program items
    // loop through them and create the array of objects of the attachment items to be created with communityFolderItem
    // then using that array i have to create the folder items, and then update the program items with the new attachment items created for that checkpoint
    const primaryMongooseConnection =
      await PrimaryMongooseConnection.connect();

    const createAttachmentResults = [];

    const session = await primaryMongooseConnection.startSession();
    try {
      session.startTransaction();

      const sourceAttachmentItemIds =
        sourceProgramItem.attachmentItems.map((item) => {
          return item.mediaObjectId;
        });

      let filter = {
        _id: { $in: destination },
      };
      if (duplicateToAll === true) {
        filter = {
          programObjectId: programId,
          _id: { $ne: sourceProgramItem._id },
        };
      }

      const [allProgramItems, sourceAttachmentsItems] = await Promise.all([
        ProgramItem.find(filter, {
          _id: 1,
        }),
        communityFolderItemsModel
          .find({
            _id: { $in: sourceAttachmentItemIds },
          })
          .lean(),
      ]);

      const bulkOps = [];

      for (const programItem of allProgramItems) {
        const newAttachmentItems = generateAttachmentItems({
          communityFolderObjectId: programItem._id,
          sourceAttachmentItems: sourceAttachmentsItems,
        });

        // creating the attachment items and attaching it to the program item

        const newAttachmentItemsCreated =
          await communityFolderItemsModel.create(newAttachmentItems, {
            session,
          });

        bulkOps.push({
          updateOne: {
            filter: { _id: programItem._id },
            update: {
              $set: {
                attachmentItems: newAttachmentItemsCreated.map((item) => {
                  return { mediaObjectId: item._id };
                }),
              },
            },
          },
        });
      }

      if (bulkOps.length > 0) {
        const bulkWriteResult = await ProgramItem.bulkWrite(bulkOps, {
          session,
        });

        createAttachmentResults.push(bulkWriteResult);
      }
      await session.commitTransaction();
    } catch (error) {
      logger.error(
        'Transaction failed. Attachment results:',
        createAttachmentResults
      );
      await session.abortTransaction();
      throw error;
    } finally {
      await session.endSession();
    }
  }

  await purgeEntityLandingPageCache({
    community,
    entityType: ENTITY_LANDING_PAGE.CHALLENGE,
    entitySlug: program.slug,
  });

  await updateProgramAllCheckpointsNotEmptyField(program, programId);

  return results;
};

async function fillShiftCheckpointsOperations({
  checkpointFilters,
  direction,
  deltaInSeconds,
  bulkOperations,
  toShiftIndex,
}) {
  const checkpoints = await ProgramItem.find(checkpointFilters).lean();
  checkpoints.forEach((toUpdateCheckpoint) => {
    const currentStart = DateTime.fromJSDate(toUpdateCheckpoint.startTime);
    const currentEnd = DateTime.fromJSDate(toUpdateCheckpoint.endTime);
    const newStart = currentStart.plus({
      seconds: direction * deltaInSeconds,
    });
    const newEnd = currentEnd.plus({
      seconds: direction * deltaInSeconds,
    });
    const $set = {
      startTime: newStart,
      endTime: newEnd,
    };
    if (toShiftIndex) {
      $set.index = toUpdateCheckpoint.index + direction;
    }
    bulkOperations
      .find({
        _id: toUpdateCheckpoint._id,
      })
      .updateOne({
        $set,
      });
  });
}
async function flexiDateCheckpointsOperations({
  checkpointFilters,
  bulkOperations,
  timeObject,
  dataToOverwrite,
  challenge,
  checkpointsFromFE,
}) {
  const [lastCheckpoint] = await Promise.all([
    ProgramItem.findOne({
      programObjectId: challenge._id,
    })
      .sort({ index: -1 })
      .select('_id index startTime endTime')
      .lean(),
  ]);
  let existingCheckpoints = [];
  if (checkpointsFromFE.length > 0) {
    existingCheckpoints = checkpointsFromFE.filter(
      (checkpoint) => checkpoint.endTime > new Date()
    );
  } else {
    existingCheckpoints = await ProgramItem.find(checkpointFilters)
      .select('_id index startTime endTime')
      .lean();
  }
  existingCheckpoints.forEach((toUpdateCheckpoint) => {
    const now = DateTime.utc();
    const currentStart = DateTime.fromJSDate(toUpdateCheckpoint.startTime);
    const currentEnd = DateTime.fromJSDate(toUpdateCheckpoint.endTime);
    const newStart = currentStart.setZone('utc').set(timeObject);
    const newEnd = currentEnd.setZone('utc').set(timeObject);
    const isOngoing = currentStart < now && currentEnd >= now;

    const isLastCheckpoint =
      lastCheckpoint._id.toString() === toUpdateCheckpoint._id.toString();
    const $set = {
      startTime: newStart.toJSDate(),
      endTime: isLastCheckpoint ? challenge.endTime : newEnd.toJSDate(),
      ...(dataToOverwrite[toUpdateCheckpoint._id.toString()] ?? {}),
    };

    if (newStart < now || isOngoing) {
      delete $set.startTime;
      if (newEnd < currentStart) {
        throw new ParamError(
          'newEndTime has to be more than the startTime.'
        );
      }
    }
    if (bulkOperations) {
      bulkOperations
        .find({
          _id: toUpdateCheckpoint._id,
        })
        .updateOne({
          $set,
        });
    } else {
      // eslint-disable-next-line no-param-reassign
      toUpdateCheckpoint.startTime = $set.startTime;
      // eslint-disable-next-line no-param-reassign
      toUpdateCheckpoint.endTime = $set.endTime;
    }
  });

  return existingCheckpoints;
}

const validateCheckpointDates = async ({
  challenge,
  checkpoint,
  previousCheckpoint,
  startTime,
}) => {
  if (!challenge) {
    throw new ParamError('Challenge not found');
  }
  if (!checkpoint) {
    throw new ParamError('Checkpoint not found');
  }

  if (checkpoint.endTime < new Date()) {
    throw new ParamError('Cannot update past checkpoint');
  }

  const toSetStartTime = startTime
    ? new Date(startTime)
    : checkpoint.startTime;
  if (toSetStartTime >= checkpoint.endTime) {
    throw new ParamError('Start time should be before end time');
  }
  if (toSetStartTime < new Date()) {
    throw new ParamError('Start time provided is over already');
  }

  if (previousCheckpoint) {
    if (toSetStartTime < previousCheckpoint.startTime) {
      throw new ParamError(
        'Start time should not be before the previous checkpoint startTime'
      );
    }
  } else if (toSetStartTime < challenge.startTime) {
    throw new ParamError(
      'First checkpoint start time should be after challenge start time'
    );
  }

  return toSetStartTime;
};

const updateCheckpointDates = async (
  community,
  challengeId,
  checkpointId,
  params
) => {
  const { startTime, applyToAll } = params;

  const [challenge, checkpoint] = await Promise.all([
    Program.findOne({ _id: challengeId }).lean(),
    ProgramItem.findOne({
      _id: checkpointId,
      programObjectId: challengeId,
    }).lean(),
  ]);

  if (checkpoint?.isWelcomeCheckpoint) {
    throw new ParamError('Cannot update welcome checkpoint');
  }
  const previousCheckpoint = await ProgramItem.findOne({
    programObjectId: challenge._id,
    index: checkpoint.index - 1,
    isWelcomeCheckpoint: { $ne: true },
  }).lean();

  const toSetStartTime = await validateCheckpointDates({
    challenge,
    checkpoint,
    previousCheckpoint,
    startTime,
  });

  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  let updatedCheckpoints = [];
  let shiftInfo;
  try {
    if (!applyToAll) {
      const promises = [
        ProgramItem.findByIdAndUpdate(
          checkpoint._id,
          {
            $set: {
              startTime: toSetStartTime,
            },
          },
          { new: true, session, select: '_id startTime endTime index' }
        ),
      ];
      if (previousCheckpoint) {
        promises.push(
          ProgramItem.findByIdAndUpdate(
            previousCheckpoint._id,
            {
              $set: {
                endTime: toSetStartTime,
              },
            },
            { new: true, session, select: '_id startTime endTime index' }
          )
        );
      }
      updatedCheckpoints = await Promise.all(promises);
    } else {
      shiftInfo = {
        dataToOverwrite: {
          [checkpoint._id.toString()]: {
            startTime: toSetStartTime,
          },
        },
      };
      if (previousCheckpoint) {
        shiftInfo.dataToOverwrite[previousCheckpoint._id.toString()] = {
          endTime: toSetStartTime,
        };
      }
      const operations = ProgramItem.collection.initializeUnorderedBulkOp({
        readPreference: 'primary',
      });
      const toSetStartTimeInLuxon =
        DateTime.fromJSDate(toSetStartTime).toUTC();
      shiftInfo.timeObject = {
        hour: toSetStartTimeInLuxon.hour,
        minute: toSetStartTimeInLuxon.minute,
        second: toSetStartTimeInLuxon.second,
        millisecond: toSetStartTimeInLuxon.millisecond,
      };

      await flexiDateCheckpointsOperations({
        checkpointFilters: {
          programObjectId: challenge._id,
          endTime: { $gt: DateTime.utc() },
          isWelcomeCheckpoint: { $ne: true },
        },
        bulkOperations: operations,
        challenge,
        ...shiftInfo,
      });

      if (operations.length > 0) {
        await operations.execute({ session });
      }
    }
    await session.commitTransaction();
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }

  await scheduledNotificationService.addChallengeAndCheckpointNotificationSchedule(
    {
      program: challenge,
      community,
    }
  );
  await scheduledNotificationService.addCheckpointEmailSchedules({
    challengeId: challenge._id,
    community,
    program: challenge,
  });

  await purgeEntityLandingPageCache({
    community,
    entityType: ENTITY_LANDING_PAGE.CHALLENGE,
    entitySlug: challenge.slug,
  });

  return { shiftInfo, checkpoints: updatedCheckpoints };
};

const calculateCheckpointDates = async ({
  challengeId,
  checkpointId,
  params,
}) => {
  const { startTime, applyToAll, checkpoints = [] } = params;

  const [challenge] = await Promise.all([
    Program.findOne({ _id: challengeId }).lean(),
  ]);
  if (!challenge) {
    throw new ParamError(`Challenge not found`);
  }

  // Find previous checkpoint by existing checkpoints from FE
  let checkpoint = {
    ...checkpoints.find((c) => c._id === checkpointId),
  };
  if (!checkpoint?.index) {
    checkpoint = await ProgramItem.findById(checkpointId)
      .select('_id index startTime endTime')
      .lean();
  }
  let previousCheckpoint = {
    ...checkpoints.find((c) => c.index === checkpoint.index - 1),
  };
  if (!previousCheckpoint?.index) {
    previousCheckpoint = await ProgramItem.findOne({
      programObjectId: challenge._id,
      index: checkpoint.index - 1,
      status: PROGRAM_ITEM_STATUS.ACTIVE,
      isWelcomeCheckpoint: { $ne: true },
    })
      .select('_id index startTime endTime')
      .lean();
  }

  // Keep it for backward compatibility
  await validateCheckpointDates({
    challenge,
    checkpoint,
    previousCheckpoint,
    startTime,
  });

  const updatedCheckpoints = [];
  if (!applyToAll) {
    updatedCheckpoints.push({
      ...checkpoint,
      startTime,
    });
    if (previousCheckpoint) {
      // Remove the field of start time if this is ongoing checkpoint
      delete previousCheckpoint.startTime;
      updatedCheckpoints.push({
        ...previousCheckpoint,
        endTime: startTime,
      });
    }
  } else {
    const shiftInfo = {
      dataToOverwrite: {
        [checkpoint._id.toString()]: {
          startTime,
        },
      },
    };
    if (previousCheckpoint) {
      shiftInfo.dataToOverwrite[previousCheckpoint._id.toString()] = {
        endTime: startTime,
      };
    }

    const toSetStartTimeInLuxon = DateTime.fromJSDate(startTime).toUTC();
    shiftInfo.timeObject = {
      hour: toSetStartTimeInLuxon.hour,
      minute: toSetStartTimeInLuxon.minute,
      second: toSetStartTimeInLuxon.second,
      millisecond: toSetStartTimeInLuxon.millisecond,
    };

    const updateCheckpoints = await flexiDateCheckpointsOperations({
      checkpointFilters: {
        programObjectId: challenge._id,
        endTime: { $gt: DateTime.utc() },
        isWelcomeCheckpoint: { $ne: true },
      },
      challenge,
      ...shiftInfo,
      checkpointsFromFE: checkpoints,
    });

    updatedCheckpoints.push(...updateCheckpoints);
  }

  // Do the validation with existing checkpoints from frontend
  if (checkpoints.length > 0) {
    validateCheckpointStartEndDates({
      program: challenge,
      params: { updatedCheckpoints },
      existingCheckpoints: checkpoints,
    });
  }

  return { checkpoints: updatedCheckpoints };
};

const updateOneCheckpointSchedule = async ({
  community,
  challengeId,
  checkpointId,
  newStartTime,
  newEndTime,
}) => {
  const [challenge, checkpoint] = await Promise.all([
    Program.findOne({ _id: challengeId }),
    ProgramItem.findOne({
      _id: checkpointId,
      programObjectId: challengeId,
    }),
  ]);
  if (!challenge) {
    throw new ParamError('Challenge not found');
  }
  if (!checkpoint) {
    throw new ParamError('Checkpoint not found');
  }
  if (checkpoint.endTime < new Date()) {
    throw new ParamError('Cannot update past checkpoint');
  }

  const toSetEndTime = newEndTime
    ? new Date(newEndTime)
    : checkpoint.endTime;
  const toSetStartTime = newStartTime
    ? new Date(newStartTime)
    : checkpoint.startTime;
  if (toSetStartTime >= toSetEndTime) {
    throw new ParamError('Start time should be before end time');
  }
  if (newStartTime) {
    const previousCheckpoint = await ProgramItem.findOne({
      programObjectId: challenge._id,
      index: checkpoint.index - 1,
      status: PROGRAM_ITEM_STATUS.ACTIVE,
    });
    if (previousCheckpoint) {
      if (toSetStartTime < previousCheckpoint.endTime) {
        throw new ParamError(
          'Start time should be after previous checkpoint'
        );
      }
    } else if (toSetStartTime < challenge.startTime) {
      throw new ParamError(
        'First checkpoint start time should be after challenge start time'
      );
    }
  }

  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();
  let updatedCheckpoint;

  try {
    updatedCheckpoint = await ProgramItem.findOneAndUpdate(
      {
        _id: checkpoint._id,
      },
      {
        $set: {
          startTime: toSetStartTime,
          endTime: toSetEndTime,
        },
      },
      {
        new: true,
        session,
      }
    );

    const operations = ProgramItem.collection.initializeUnorderedBulkOp({
      readPreference: 'primary',
    });

    if (toSetEndTime > checkpoint.endTime) {
      const deltaInSeconds = (toSetEndTime - checkpoint.endTime) / 1000;
      await fillShiftCheckpointsOperations({
        checkpointFilters: {
          programObjectId: challenge._id,
          index: { $gt: checkpoint.index },
        },
        direction: 1,
        deltaInSeconds,
        bulkOperations: operations,
      });
      const newChallengeEndTime = DateTime.fromJSDate(
        challenge.endTime
      ).plus({
        seconds: deltaInSeconds,
      });
      await Program.updateOne(
        {
          _id: challenge._id,
        },
        {
          $set: {
            endTime: newChallengeEndTime,
          },
        },
        { session }
      );
    } else {
      const deltaInSeconds = (checkpoint.endTime - toSetEndTime) / 1000;
      await fillShiftCheckpointsOperations({
        checkpointFilters: {
          programObjectId: challenge._id,
          index: { $gt: checkpoint.index },
        },
        direction: -1,
        deltaInSeconds,
        bulkOperations: operations,
      });
      const newChallengeEndTime = DateTime.fromJSDate(
        challenge.endTime
      ).plus({
        seconds: -deltaInSeconds,
      });
      await Program.updateOne(
        {
          _id: challenge._id,
        },
        {
          $set: {
            endTime: newChallengeEndTime,
          },
        },
        { session }
      );
    }

    if (operations.length > 0) {
      await operations.execute({ session });
    }
    await session.commitTransaction();
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }

  await scheduledNotificationService.addChallengeAndCheckpointNotificationSchedule(
    {
      program: challenge,
      community,
    }
  );
  await scheduledNotificationService.addCheckpointEmailSchedules({
    challengeId: challenge._id,
    community,
    program: challenge,
  });

  await purgeEntityLandingPageCache({
    community,
    entityType: ENTITY_LANDING_PAGE.CHALLENGE,
    entitySlug: challenge.slug,
  });

  return updatedCheckpoint;
};

const moveCheckpoint = async ({
  community,
  challengeId,
  checkpointId,
  newIndex,
}) => {
  const [challenge, checkpoint] = await Promise.all([
    Program.findOne({ _id: challengeId }),
    ProgramItem.findOne({
      _id: checkpointId,
      programObjectId: challengeId,
    }),
  ]);
  if (!challenge) {
    throw new ParamError('Challenge not found');
  }
  if (!checkpoint) {
    throw new ParamError('Checkpoint not found');
  }
  const now = new Date();
  if (checkpoint.startTime < now) {
    throw new ParamError('Cannot move past checkpoint');
  }
  if (!newIndex || newIndex < 0) {
    throw new ParamError('Invalid index');
  }
  if (newIndex === checkpoint.index) {
    return checkpoint;
  }

  const targetIndexCheckpoint = await ProgramItem.findOne({
    programObjectId: challenge._id,
    index: newIndex,
    status: PROGRAM_ITEM_STATUS.ACTIVE,
  });
  if (targetIndexCheckpoint.startTime < now) {
    throw new ParamError('Cannot move to past checkpoint');
  }

  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();
  let updatedCheckpoint;

  try {
    updatedCheckpoint = await ProgramItem.findOneAndUpdate(
      {
        _id: checkpoint._id,
      },
      {
        $set: {
          index: newIndex,
          startTime: targetIndexCheckpoint.startTime,
          endTime: targetIndexCheckpoint.endTime,
        },
      },
      {
        new: true,
        session,
      }
    );
    const direction = newIndex > checkpoint.index ? -1 : 1;
    const operations = ProgramItem.collection.initializeUnorderedBulkOp({
      readPreference: 'primary',
    });
    const deltaInSeconds =
      (checkpoint.endTime - checkpoint.startTime) / 1000;
    let indexFilter;
    if (direction === -1) {
      indexFilter = {
        $gt: checkpoint.index,
        $lte: newIndex,
      };
    } else {
      indexFilter = {
        $lt: checkpoint.index,
        $gte: newIndex,
      };
    }

    await fillShiftCheckpointsOperations({
      checkpointFilters: {
        programObjectId: challenge._id,
        index: indexFilter,
      },
      direction,
      deltaInSeconds,
      bulkOperations: operations,
      toShiftIndex: true,
    });
    await operations.execute({ session });
    await session.commitTransaction();
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }

  await scheduledNotificationService.addChallengeAndCheckpointNotificationSchedule(
    {
      program: challenge,
      community,
    }
  );
  await scheduledNotificationService.addCheckpointEmailSchedules({
    challengeId: challenge._id,
    community,
    program: challenge,
  });

  await purgeEntityLandingPageCache({
    community,
    entityType: ENTITY_LANDING_PAGE.CHALLENGE,
    entitySlug: challenge.slug,
  });

  return updatedCheckpoint;
};

const addCheckpoint = async ({
  community,
  challengeId,
  index,
  durationInSeconds,
}) => {
  const challenge = await Program.findOne({ _id: challengeId });
  if (!challenge) {
    throw new ParamError('Challenge not found');
  }
  let deltaInSeconds;
  if (durationInSeconds) {
    deltaInSeconds = durationInSeconds;
  } else {
    deltaInSeconds =
      challenge.challengeSpecific.checkpointDurationInDays * 86400;
  }

  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();
  let newCheckpoint;
  let startTime;
  const currentCheckpointOfIndex = await ProgramItem.findOne({
    programObjectId: challenge._id,
    index,
  });
  if (currentCheckpointOfIndex) {
    startTime = currentCheckpointOfIndex.startTime;
  } else {
    const checkpointCounts = await ProgramItem.countDocuments({
      programObjectId: challenge._id,
      type: PROGRAM_ITEM_TYPE.CHECKPOINT,
      isWelcomeCheckpoint: { $ne: true },
    });
    const lastCheckpoint = await ProgramItem.findOne({
      programObjectId: challenge._id,
      index: checkpointCounts,
    }).lean();

    if (!lastCheckpoint) {
      throw new ParamError('Last checkpoint not found');
    }

    startTime = lastCheckpoint.endTime;
  }
  // if (startTime < new Date()) {
  //   throw new ParamError('Cannot add checkpoint in the past.');
  // }

  try {
    const bulkOperations =
      ProgramItem.collection.initializeUnorderedBulkOp({
        readPreference: 'primary',
      });
    await fillShiftCheckpointsOperations({
      checkpointFilters: {
        programObjectId: challenge._id,
        index: { $gte: index },
      },
      direction: 1,
      deltaInSeconds,
      bulkOperations,
      toShiftIndex: true,
    });
    if (bulkOperations.length > 0) {
      await bulkOperations.execute({ session });
    }
    const endTime = new Date(startTime.getTime() + deltaInSeconds * 1000);
    newCheckpoint = await ProgramItem.create(
      [
        {
          communityObjectId: challenge.communityObjectId,
          programObjectId: challenge._id,
          type: PROGRAM_ITEM_TYPE.CHECKPOINT,
          title: `Checkpoint ${index}`,
          index,
          startTime,
          endTime,
        },
      ],
      { session }
    );
    const newChallengeEndTime = new Date(
      challenge.endTime.getTime() + deltaInSeconds * 1000
    );
    await Program.updateOne(
      { _id: challenge._id },
      {
        $set: {
          endTime: newChallengeEndTime,
        },
      },
      { session }
    );
    await session.commitTransaction();
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }

  await scheduledNotificationService.addChallengeAndCheckpointNotificationSchedule(
    {
      program: challenge,
      community,
    }
  );
  await scheduledNotificationService.addCheckpointEmailSchedules({
    challengeId: challenge._id,
    community,
    program: challenge,
  });

  await purgeEntityLandingPageCache({
    community,
    entityType: ENTITY_LANDING_PAGE.CHALLENGE,
    entitySlug: challenge.slug,
  });

  return newCheckpoint[0];
};

module.exports = {
  updateProgramItems,
  updateCheckpointDates,
  calculateCheckpointDates,
  updateOneCheckpointEvent,
  deleteOneCheckpointEvent,
  duplicateProgramItem,
  isDescriptionNotEmpty,
  updateOneCheckpointSchedule,
  moveCheckpoint,
  addCheckpoint,
  updateProgramAllCheckpointsNotEmptyField,
  getCoverVideoMap,
  deleteMedia,
};
