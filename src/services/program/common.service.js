const { ObjectId } = require('mongoose').Types;
const { DateTime } = require('luxon');
const httpContext = require('express-http-context');

const Community = require('../../communitiesAPI/models/community.model');
const Program = require('../../models/program/program.model');
const ProgramParticipant = require('../../models/program/programParticipant.model');
const ProgramItem = require('../../models/program/programItem.model');
const Membership = require('../../models/membership/membership.model');
const Video = require('../../models/videos.model');
const CommunityDiscount = require('../../communitiesAPI/models/communityDiscounts.model');

const communityFolderItemService = require('../../communitiesAPI/services/web/communityFolderItems.service');
const leaderboardService = require('./leaderboard.service');

// const logger = require('../loggerCreation.service');

const { ParamError } = require('../../utils/error.util');

const {
  PARTICIPANT_PROGRAM_ITEM_STATUS,
  PARTICIPANT_PROGRAM_STATUS,
  PROGRAM_SCHEDULE_STATUS,
  PROGRAM_TYPE,
  PROGRAM_ITEM_TYPE,
  EVENT_TYPE,
} = require('./constants');

const { CHALLENGE_LEADERBOARD_POINTS_QUEUE_URL } = require('../../config');
const localizationModel = require('../../models/platform/localization.model');
const communityFolderItemsModel = require('../../communitiesAPI/models/communityFolderItems.model');
const {
  communityFolderItemsService,
} = require('../../communitiesAPI/services/web');
const { sendMessageToSQSQueue } = require('../../handlers/sqs.handler');

const mongodbUtils = require('../../utils/mongodb.util');

const getCommunity = async (communityId) => {
  const community = await Community.findOne({
    _id: new ObjectId(communityId),
  }).lean();

  if (!community) {
    throw new ParamError('Community not found');
  }
  return community;
};

const getProgram = async (programId) => {
  const program = await Program.findOne(
    {
      _id: new ObjectId(programId),
    },
    {
      descriptionHTML: 0,
      'additionalSettings.instructionsHTML': 0,
      'additionalSettings.rewardsHTML': 0,
    }
  ).lean();

  if (!program) {
    throw new ParamError('Program not found');
  }

  program.isLeaderboardEnabled =
    leaderboardService.isLeaderboardEnabled(program);

  return program;
};

const getProgramScheduleStatus = (program) => {
  const now = DateTime.utc();
  if (program.startTime > now) {
    return PROGRAM_SCHEDULE_STATUS.UPCOMING;
  }

  if (program.endTime < now) {
    return PROGRAM_SCHEDULE_STATUS.ENDED;
  }

  return PROGRAM_SCHEDULE_STATUS.ONGOING;
};

const getCoverVideo = async ({
  communityObjectId,
  programObjectId,
  coverVideo,
}) => {
  const { mediaObjectId } = coverVideo || {};
  if (!mediaObjectId) {
    return null;
  }

  const coverVideoFolderItemId = await communityFolderItemsModel
    .findOne({
      _id: new ObjectId(mediaObjectId),
      communityObjectId,
      isCoverVideo: true,
      communityFolderObjectId: programObjectId,
    })
    .select('_id');

  if (!coverVideoFolderItemId) {
    return null;
  }

  const programCoverVideoItem =
    await communityFolderItemService.getFolderItemByIdWithVideoInfo(
      coverVideoFolderItemId?._id,
      true
    );

  return {
    mediaData: programCoverVideoItem,
    mediaObjectId: programCoverVideoItem._id,
  };
};

const constructParticipantFilters = async ({
  programId,
  learnerObjectIdFilters = null,
  filterProgramItemIds,
  filterProgramItemStatuses,
  filterProgramStatuses,
  filterProgramItemType = PROGRAM_ITEM_TYPE.CHECKPOINT,
  startObjectId = null,
  endObjectId = null,
}) => {
  const filters = {
    programObjectId: new ObjectId(programId),
    $or: [],
  };

  if (startObjectId && endObjectId) {
    filters._id = {
      $gte: mongodbUtils.toObjectId(startObjectId),
      $lte: mongodbUtils.toObjectId(endObjectId),
    };
  }

  if (
    learnerObjectIdFilters &&
    Object.keys(learnerObjectIdFilters).length > 0
  ) {
    filters.learnerObjectId = learnerObjectIdFilters;
  }
  if (
    filterProgramItemIds?.length > 0 &&
    filterProgramItemStatuses?.length > 0
  ) {
    for (const status of filterProgramItemStatuses) {
      switch (status) {
        case PARTICIPANT_PROGRAM_ITEM_STATUS.INCOMPLETE:
          filters.$or.push({
            $and: filterProgramItemIds.map((id) => ({
              $or: [
                {
                  'items.itemObjectId': { $ne: id },
                },
                {
                  items: {
                    $elemMatch: {
                      itemObjectId: id,
                      completionStatus: status,
                    },
                  },
                },
              ],
            })),
          });
          break;
        case PARTICIPANT_PROGRAM_ITEM_STATUS.COMPLETED:
        case PARTICIPANT_PROGRAM_ITEM_STATUS.LATE_COMPLETED:
          filters.$or.push({
            $and: filterProgramItemIds.map((id) => ({
              items: {
                $elemMatch: {
                  itemObjectId: id,
                  completionStatus: status,
                },
              },
            })),
          });
          break;
        default:
          break;
      }
    }
  } else if (filterProgramItemStatuses?.length > 0) {
    if (
      filterProgramItemStatuses.includes(
        PARTICIPANT_PROGRAM_ITEM_STATUS.INCOMPLETE
      )
    ) {
      const itemCounts = await ProgramItem.countDocuments({
        programObjectId: programId,
        type: filterProgramItemType,
      });
      filters.$or.push({ completedCount: { $lt: itemCounts } });
    }
    if (
      filterProgramItemStatuses.includes(
        PARTICIPANT_PROGRAM_ITEM_STATUS.COMPLETED
      )
    ) {
      filters.$or.push({
        'items.completionStatus':
          PARTICIPANT_PROGRAM_ITEM_STATUS.COMPLETED,
      });
    }
    if (
      filterProgramItemStatuses.includes(
        PARTICIPANT_PROGRAM_ITEM_STATUS.LATE_COMPLETED
      )
    ) {
      filters.$or.push({
        'items.completionStatus':
          PARTICIPANT_PROGRAM_ITEM_STATUS.LATE_COMPLETED,
      });
    }
  }
  if (filterProgramStatuses?.length > 0) {
    filters.status = { $in: filterProgramStatuses };
  } else {
    filters.status = {
      $in: [
        PARTICIPANT_PROGRAM_STATUS.PARTICIPATED,
        PARTICIPANT_PROGRAM_STATUS.COMPLETED,
        PARTICIPANT_PROGRAM_STATUS.WINNER,
        PARTICIPANT_PROGRAM_STATUS.KICKED_OUT,
      ],
    };
  }
  if (filters.$or.length === 0) {
    delete filters.$or;
  }
  return filters;
};

const countParticipants = async ({
  programId,
  filterProgramItemIds,
  filterProgramItemStatuses,
  filterProgramStatuses,
}) => {
  const filters = await constructParticipantFilters({
    programId,
    filterProgramItemIds,
    filterProgramItemStatuses,
    filterProgramStatuses,
  });
  return ProgramParticipant.countDocuments(filters);
};

async function getSomeParticipantProfilePics({
  community,
  programId,
  filterProgramItemIds = undefined,
  filterProgramItemStatuses = undefined,
  limit,
}) {
  const filters = await constructParticipantFilters({
    programId,
    filterProgramItemIds,
    filterProgramItemStatuses,
  });
  const participants = await ProgramParticipant.find(filters, {
    learnerObjectId: 1,
  }).limit(limit);
  const membership = await Membership.find({
    communityObjectId: community._id,
    learnerObjectId: { $in: participants.map((p) => p.learnerObjectId) },
  });
  return membership.map((m) => m.profileImage);
}

const getOngoingProgramItem = async ({ program, type, projection }) => {
  const now = DateTime.utc();
  const ongoingProgramItem = await ProgramItem.findOne(
    {
      programObjectId: program._id,
      startTime: { $lte: now },
      endTime: { $gte: now },
      type,
    },
    projection
  ).lean();
  return ongoingProgramItem;
};

const getMediaData = async ({
  mediaObjectId,
  isCommunityManager = false,
}) => {
  if (!mediaObjectId) {
    return null;
  }
  let folderItem;
  try {
    folderItem =
      await communityFolderItemService.getFolderItemByIdWithVideoInfo(
        mediaObjectId,
        isCommunityManager
      );
  } catch (error) {
    return null;
  }
  if (folderItem && folderItem.videoObjectId) {
    const video = await Video.findOne({
      _id: new ObjectId(folderItem.videoObjectId),
    }).lean();
    folderItem.video = video;
  }
  return folderItem;
};

const getAttachments = async ({
  attachmentIds,
  checkpointId,
  isCommunityManager,
}) => {
  let attachments = [];

  if (attachmentIds && attachmentIds.length > 0) {
    // get video data
    attachments =
      await communityFolderItemsService.getFolderItemsByFolderId(
        checkpointId,
        isCommunityManager,
        {
          _id: { $in: attachmentIds },
        },
        0,
        null,
        null
      );

    const attachmentMap = new Map();

    attachments.forEach((attachment) => {
      attachmentMap.set(attachment._id.toString(), attachment);
    });

    // Reconstruct the attachments array in the order of attachmentIds
    attachments = attachmentIds
      .map((id) => attachmentMap.get(id?.toString()))
      .filter(Boolean); // Remove any undefined values if some IDs were not found
  }
  return attachments;
};

const generateProgramParticipantPipeline = ({
  filters,
  pageSize,
  pageNo,
  projection,
  sortOrder = -1,
}) => {
  const skip = (pageNo - 1) * pageSize;
  const programParticipantAggregate = [
    {
      $match: filters,
    },
    {
      $sort: {
        createdAt: sortOrder,
      },
    },
    {
      $skip: skip,
    },
    {
      $limit: pageSize,
    },
    {
      $lookup: {
        from: 'learners',
        localField: 'learnerObjectId',
        foreignField: '_id',
        as: 'learner',
      },
    },
    {
      $unwind: {
        path: '$learner',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: 'country_currency_mapping',
        localField: 'learner.countryId',
        foreignField: 'countryId',
        as: 'country',
      },
    },
    {
      $addFields: {
        'learner.country': { $first: '$country.country' },
      },
    },
  ];
  if (Object.keys(projection).length > 0) {
    programParticipantAggregate.push({
      $project: projection,
    });
  }

  return programParticipantAggregate;
};

const getDiscountDetails = async (discountApplied) => {
  if (!discountApplied || discountApplied.length === 0) {
    return [];
  }

  const discountsAppliedArr = await CommunityDiscount.find(
    {
      _id: { $in: discountApplied ?? [] },
    },
    {
      code: 1,
      value: 1,
      isActive: 1,
      type: 1,
      _id: 1,
    }
  ).lean();
  return discountsAppliedArr;
};

const countParticipantRaw = async ({ filters }) => {
  return ProgramParticipant.countDocuments(filters);
};

const countChallenges = async ({ filters }) => {
  return ProgramItem.countDocuments({
    ...filters,
    type: PROGRAM_TYPE.CHALLENGE,
  });
};

function localizeTimeUnits(locale) {
  const units = {
    'es-mx': {
      days: 'días',
      hours: 'horas',
    },
    'pt-br': {
      days: 'dias',
      hours: 'horas',
    },
    en: {
      days: 'days',
      hours: 'hours',
    },
  };
  return units[locale];
}
const getReplacedText = (text, replacements) => {
  return text.replace(
    /{(\w+)}/g,
    (match, key) => replacements[key] || match
  );
};
const getMobileNotificationBody = async ({
  type,
  challengeId,
  communityId,
  participantId,
  communityCode,
  checkpointId,
  program,
  title,
  body,
  languagePreference = 'en',
  checkpointInfo,
  zone = 'UTC',
}) => {
  let toSend = {};
  const notificationBody = {
    type,
    challengeId,
    communityId,
    communityCode,
    phase: 'challenges',
  };
  let timeRemaining = '';
  notificationBody.checkpointId = checkpointId || null;

  const meta = {};
  let checkpointEndDateInLocal;

  if (checkpointInfo) {
    //  11 Mar 2024 23:00
    checkpointEndDateInLocal = DateTime.fromJSDate(
      checkpointInfo.endTime,
      {
        zone,
      }
    ).toFormat('dd LLL yyyy HH:mm', {
      locale: languagePreference,
    });

    const checkpointTimeRemaining = DateTime.fromJSDate(
      checkpointInfo.endTime,
      {
        zone,
      }
    ).diff(DateTime.utc(), 'hours');

    if (checkpointTimeRemaining.hours > 24) {
      timeRemaining = `${Math.floor(checkpointTimeRemaining.hours / 24)} ${
        localizeTimeUnits(languagePreference)?.days
      }`;
    } else {
      timeRemaining = `${Math.floor(checkpointTimeRemaining.hours)} ${
        localizeTimeUnits(languagePreference)?.hours
      }`;
    }
  }

  const localizedTitle = await localizationModel
    .findOne({
      key: title,
    })
    .lean();

  const localizedDescription = await localizationModel
    .findOne({
      key: body,
    })
    .lean();

  meta.title = getReplacedText(
    localizedTitle[languagePreference] || localizedTitle.en,
    {
      challengeName: program.title,
      challengeTitle: program.title,
      checkpointEndDate: checkpointEndDateInLocal,
      checkpointTitle: checkpointInfo?.title,
      timeRemaining,
      event_name: checkpointInfo?.event?.title,
    }
  );
  meta.body = getReplacedText(
    localizedDescription[languagePreference] || localizedDescription.en,
    {
      challengeName: program.title,
      challengeTitle: program.title,
      checkpointEndDate: checkpointEndDateInLocal,
      checkpointTitle: checkpointInfo?.title,
      timeRemaining,
      event_name: checkpointInfo?.event?.title,
    }
  );

  toSend = {
    ...meta,
    type,
    challengeId,
    communityId,
    participantId,
    checkpointId: checkpointId || '',
    source: 'remote',
  };

  return toSend;
};

const sendLeaderboardConfigsChangesToQueue = async ({
  communityObjectId,
  programObjectId,
  leaderboardConfigs,
}) => {
  const requestId = httpContext.get('reqId');

  const eventType = EVENT_TYPE.RECALCULATE_TOTAL_POINTS;

  const message = {
    event: {
      eventType,
      eventDateTime: new Date().toISOString(),
      communityObjectId,
      programObjectId,
      leaderboardConfigs,
    },
    requestor: 'Learning Portal Backend',
    requestId,
  };

  const messageGroupId = `challenge_leaderboard_${programObjectId}`;

  await sendMessageToSQSQueue({
    queueUrl: CHALLENGE_LEADERBOARD_POINTS_QUEUE_URL,
    messageBody: message,
    messageGroupId,
    isFifo: true,
  });
};

const sendCheckpointEndDateChangesToQueue = async ({
  communityObjectId,
  programObjectId,
  programItemObjectId,
  leaderboardConfigs,
  programItemEndTime,
}) => {
  const requestId = httpContext.get('reqId');

  const eventType = EVENT_TYPE.RECALCULATE_CHECKPOINT_POINTS;

  const message = {
    event: {
      eventType,
      eventDateTime: new Date().toISOString(),
      communityObjectId,
      programObjectId,
      programItemObjectId,
      leaderboardConfigs,
      programItemEndTime,
    },
    requestor: 'Learning Portal Backend',
    requestId,
  };

  const messageGroupId = `challenge_leaderboard_${programObjectId}`;

  await sendMessageToSQSQueue({
    queueUrl: CHALLENGE_LEADERBOARD_POINTS_QUEUE_URL,
    messageBody: message,
    messageGroupId,
    isFifo: true,
  });
};

const checkParticipantLimitReached = async ({ program }) => {
  if (
    !program.additionalSettings?.isCapacitySet ||
    !program.additionalSettings?.participantLimit
  ) {
    return false;
  }

  const participantsCount = await ProgramParticipant.countDocuments({
    programObjectId: program._id,
    status: { $ne: PARTICIPANT_PROGRAM_STATUS.SYSTEM_REMOVED },
  }).read('primary');

  const limit = program.additionalSettings.participantLimit;

  return participantsCount + 1 > limit;
};

module.exports = {
  getCoverVideo,
  getCommunity,
  getProgram,
  countParticipants,
  constructParticipantFilters,
  getSomeParticipantProfilePics,
  getOngoingProgramItem,
  getProgramScheduleStatus,
  getMediaData,
  generateProgramParticipantPipeline,
  getDiscountDetails,
  countParticipantRaw,
  countChallenges,
  getMobileNotificationBody,
  getAttachments,
  sendLeaderboardConfigsChangesToQueue,
  sendCheckpointEndDateChangesToQueue,
  checkParticipantLimitReached,
};
