const { DateTime } = require('luxon');
const communityAddonTransactionsModel = require('../../communitiesAPI/models/communityAddonTransactions.model');
const {
  REROUTE_MEMBER_LINK,
  NAS_IO_FRONTEND_URL,
  NAS_IO_APP_DYNAMIC_LINK,
  env,
} = require('../../config');
const CommunityModel = require('../../communitiesAPI/models/community.model');
const LearnersModel = require('../../models/learners.model');
const ProgramItemModel = require('../../models/program/programItem.model');
const ProgramParticipantModel = require('../../models/program/programParticipant.model');
const {
  retrieveCommunityManagerInfo,
} = require('../communityNotification/email/common.service');
const { subscriptionInfo } = require('../communitySubscription');
const membershipService = require('../membership');
const logger = require('../loggerCreation.service');
const {
  DEFAULT_IMAGES,
  CHALLENGE_MAIL_TYPES,
  JOB_STATUS,
  RESULT_TYPE,
} = require('../mail/constants');
const { sendEmail } = require('../notification');
const {
  PARTICIPANT_PROGRAM_STATUS,
  PROGRAM_STATUS,
  PROGRAM_CHECKPOINTS_REMINDER_BATCH_SIZE,
  PROGRAM_CHALLENGE_TYPE,
} = require('./constants');
const {
  COMMUNITY_ONE_TIME_PAYMENT_ENTITIES,
} = require('../../communitiesAPI/constants');
const {
  ADDON_ACTION_EVENT_TYPES,
  PRODUCTION,
  MOBILE_NOTIFICATION_TYPES,
  MOBILE_NOTIFICATION_RECIPIENTS_SCOPES,
  BATCH_METADATA_MODEL_TYPE,
  PURCHASE_TYPE,
} = require('../../constants/common');
const ActionEventService = require('../actionEvent');
const { reportJobResult } = require('../mail/scheduledEmail.service');
const ProgramModel = require('../../models/program/program.model');
const { ParamError } = require('../../utils/error.util');
const { sendMessageToSQSQueue } = require('../../handlers/sqs.handler');
const mobileNotificationsScheduleModel = require('../../models/mobileNotifications/mobileNotificationsSchedule.model');
const {
  scheduledNotificationStatuses,
} = require('../notification/constants');
const {
  sendMobileNotificationToQueue,
} = require('../notification/mobileNotifications.service');
const { getMobileNotificationBody } = require('./common.service');
const ParticipantProgramItemModel = require('../../models/program/participantProgramItem.model');
const PrimaryMongooseConnection = require('../../rpc/primaryMongooseConnection');
const mailConfigModel = require('../../models/notificationBackend/mailConfig.model');
const {
  CHALLENGE_CHECKPOINT_NOTIFICATION_QUEUE_URL,
} = require('../../config');
const autoEnrollmentService = require('../autoEnrollment');
const upsellService = require('./upsell.service');
const leaderboardService = require('./leaderboard.service');
const { formatDateTimeByLocale } = require('../../utils/date.util');
const { getLock } = require('../../redisLock');
const {
  formatAwardOrResetPointData,
} = require('../mail/formatVariableData.service');
const batchMetadataService = require('../batchMetadata');
const AuthServiceRpc = require('../../rpc/authService.rpc');
const notificationCommonService = require('../communityNotification/email/common.service');
const mailUtils = require('../../utils/mail.util');
const receiptService = require('../receipts/receipts.service');

/*
1. challenge_title
2. start_date
3. end_date
4. start_time
5. timezone_text
6. checkpoint_count
7. name(participant_name)
8. amount_paid
9. email(participant email)
10. community_data_variables
*/

function getTimezoneText(timezone) {
  const dt = DateTime.now().setZone(timezone);
  const offset = dt.offset / 60;
  const sign = offset >= 0 ? '+' : '-';
  const hours = Math.abs(Math.floor(offset));
  const minutes = Math.abs(Math.floor((offset % 1) * 60));

  return `${timezone?.split('/')?.[1] ?? ''} GMT${sign}${hours}:${minutes
    .toString()
    .padStart(2, '0')}`;
}

function getTimezoneOffSet(time) {
  let timezoneOffset = '+0000';
  const ratio = Math.abs(time?.offset) / 60;
  const hours = Math.trunc(ratio);
  const mins = 60 * (ratio - hours);

  if (time?.offset !== 0) {
    timezoneOffset = `${time?.offset < 0 ? '-' : '+'}${
      hours < 10 ? '0' : ''
    }${hours}${mins < 10 ? '0' : ''}${mins}`;
  }

  return timezoneOffset;
}

const getPlatformParamsForApp = () => {
  if (env === PRODUCTION) {
    return {
      apn: 'com.nas.academy', // Android Package Name
      ibi: 'com.nasAcademy', // IOS Bundle ID
      isi: '1624529593', // App Store ID : to redirect user to our app listing
      efr: '1', // skip the app preview page when the Dynamic Link is opened
      ofl: 'https://nas.io/install', // if link opened on desktop, redirect to nas.io/install
    };
  }
  return {
    apn: 'com.dev.nas.academy', // Android Package Name
    ibi: 'com.dev.nasAcademy', // IOS Bundle ID
    ifl: 'https://nas.io/install', // The link to open when the app isn't installed. (Nas Dev app has no store listing)
    efr: '1', // skip the app preview page when the Dynamic Link is opened
    ofl: 'https://nas.io/install', // if link opened on desktop, redirect to nas.io/install
  };
};

function getAppLink(community, challenge, email) {
  const params = {
    link: `${NAS_IO_FRONTEND_URL}/mb/communities/${String(
      community._id
    )}/challenges/${String(challenge._id)}?email=${email}`, // link can be anything to redirect in app
    ...getPlatformParamsForApp(),
  };

  const appLink =
    `${NAS_IO_APP_DYNAMIC_LINK}` +
    '?' +
    new URLSearchParams(params).toString();

  return appLink;
}
const formatCommunityData = ({ community }) => {
  const data = {
    community_code: community.code,
    community_name: community.title,
    community_link: `${REROUTE_MEMBER_LINK}?&activeCommunityId=${community._id}&memberExperience=1`,
    community_profile_image:
      community?.thumbnailImgData?.mobileImgData?.src ??
      DEFAULT_IMAGES.COMMUNITY_PROFILE_IMAGE,
  };
  return data;
};
const formatChallengeMailVariables = async (
  participant,
  challenge,
  community,
  mailType,
  checkpoint = null
) => {
  const {
    // eslint-disable-next-line camelcase
    title: challenge_title,
    _id: challengeObjectId,
  } = challenge;

  const { learnerObjectId, checkoutId, removalReason } = participant;

  const challengeEntityInfo = await ProgramModel.findOne({
    _id: challengeObjectId,
  });

  const participantLearnerInfo = await LearnersModel.findOne(
    {
      _id: learnerObjectId,
    },
    {},
    {
      readPreference: 'primary',
    }
  );

  const checkpointCount = await ProgramItemModel.countDocuments({
    programObjectId: challengeObjectId,
    type: 'checkpoint',
  });

  const { email, timezone: timezoneOfParticipant = 'UTC' } =
    participantLearnerInfo;

  const name =
    participantLearnerInfo?.firstName ??
    participantLearnerInfo?.email?.split('@')?.[0] ??
    '';
  const communityDataVariables = formatCommunityData({ community });

  let challengeDateTimeVaribles = {};
  if (challenge.challengeType === PROGRAM_CHALLENGE_TYPE.FIXED) {
    const { startTime, endTime } = challengeEntityInfo;

    const startTimeInParticipantTimezone = DateTime.fromJSDate(startTime, {
      zone: timezoneOfParticipant,
    });
    const endTimeInParticipantTimezone = DateTime.fromJSDate(endTime, {
      zone: timezoneOfParticipant,
    });

    const timezoneText = getTimezoneText(timezoneOfParticipant);
    const timezoneOffset = getTimezoneOffSet(
      startTimeInParticipantTimezone
    );

    challengeDateTimeVaribles = {
      startDate: startTimeInParticipantTimezone
        .toUTC()
        .toFormat("yyyy-MM-dd'T'HH:mm:ssZZ"),
      endDate: endTimeInParticipantTimezone
        .toUTC()
        .toFormat("yyyy-MM-dd'T'HH:mm:ssZZ"),
      // 13 Mar
      challenge_start_date: formatDateTimeByLocale(
        startTimeInParticipantTimezone,
        'dd MMM',
        participantLearnerInfo?.languagePreference
      ),
      challenge_start_time: formatDateTimeByLocale(
        startTimeInParticipantTimezone,
        'h:mm a',
        participantLearnerInfo?.languagePreference
      ),
      challenge_end_date: formatDateTimeByLocale(
        endTimeInParticipantTimezone,
        'dd MMM',
        participantLearnerInfo?.languagePreference
      ),
      challenge_end_time: formatDateTimeByLocale(
        endTimeInParticipantTimezone,
        'h:mm a',
        participantLearnerInfo?.languagePreference
      ),
      time_zone_text: timezoneText,
      timezoneOffset,
    };
  }
  const challengeMailVariables = {
    challenge_title,
    ...challengeDateTimeVaribles,
    checkpoint_count: checkpointCount,
    name,
    participant_name: name,
    email,
    challengeLink: `${NAS_IO_FRONTEND_URL}/${community?.link}/challenges${challenge?.slug}`,
    appLink: getAppLink(community, challenge, email),
    ...communityDataVariables,
  };

  // using checkoutId need to check if it is a paid one or not and based on that we need to set the amount_paid
  const checkoutInfo = await communityAddonTransactionsModel.findOne({
    _id: checkoutId,
  });

  if (checkoutInfo && checkoutInfo?.amount > 0) {
    challengeMailVariables.amount_paid = checkoutInfo.amount / 100;
    challengeMailVariables.currency = checkoutInfo.currency;
  }

  const communityManagers = await retrieveCommunityManagerInfo(
    community.code,
    mailType
  );

  const getCommunityManagersEmailData = communityManagers.emails.map(
    async (managerEmail) => {
      const learner = await LearnersModel.findOne({
        email: managerEmail,
      }).lean();

      return {
        name: learner?.firstName ?? '',
        email: managerEmail,
      };
    }
  );

  const communityManagersEmailData = await Promise.all(
    getCommunityManagersEmailData
  );

  if (leaderboardService.isLeaderboardEnabled(challenge)) {
    const { rank } =
      await leaderboardService.getParticipantLeaderboardPointsAndRank({
        program: challenge,
        participant,
      });

    challengeMailVariables.participant_rank = rank;
  }
  if (removalReason) {
    challengeMailVariables.removal_reason = removalReason;
    challengeMailVariables.failed_reason = removalReason;
  }
  if (checkpoint) {
    const {
      startTime: checkpointStartDate,
      endTime: checkpointEndDate,
      title,
    } = checkpoint;

    challengeMailVariables.checkpoint_title = title;

    if (challenge.challengeType === PROGRAM_CHALLENGE_TYPE.FIXED) {
      const challengeStartTimeInUTC = DateTime.fromJSDate(
        checkpointStartDate,
        {
          zone: 'UTC',
        }
      );
      const challengeEndTimeInUTC = DateTime.fromJSDate(
        checkpointEndDate,
        {
          zone: 'UTC',
        }
      );
      const challengeStartTimeInUserTimezone = DateTime.fromJSDate(
        checkpointStartDate,
        {
          zone: timezoneOfParticipant,
        }
      );
      const challengeEndTimeInUserTimezone = DateTime.fromJSDate(
        checkpointEndDate,
        {
          zone: timezoneOfParticipant,
        }
      );

      challengeMailVariables.checkpointStartDate = challengeStartTimeInUTC
        .toUTC()
        .toFormat("yyyy-MM-dd'T'HH:mm:ssZZ");
      challengeMailVariables.checkpointEndDate = challengeEndTimeInUTC
        .toUTC()
        .toFormat("yyyy-MM-dd'T'HH:mm:ssZZ");

      //dd MMM, yyyy -  13 Mar, 2024
      const challengeStartDateInddMMMYYYY =
        challengeStartTimeInUserTimezone.toFormat('dd MMM, yyyy', {
          locale: participantLearnerInfo?.languagePreference ?? 'en',
        });
      // h:mm a - 2:00 PM
      const challengeStartTimeInhMMa =
        challengeStartTimeInUserTimezone.toFormat('h:mm a', {
          locale: participantLearnerInfo?.languagePreference ?? 'en',
        });
      const challengeEndDateInddMMMYYYY =
        challengeEndTimeInUserTimezone.toFormat('dd MMM, yyyy', {
          locale: participantLearnerInfo?.languagePreference ?? 'en',
        });
      const challengeEndTimeInhMMa =
        challengeEndTimeInUserTimezone.toFormat('h:mm a', {
          locale: participantLearnerInfo?.languagePreference ?? 'en',
        });
      challengeMailVariables.checkpointStartDateText =
        challengeStartDateInddMMMYYYY;
      challengeMailVariables.checkpoint_start_date =
        challengeStartDateInddMMMYYYY;
      challengeMailVariables.checkpointStartTime =
        challengeStartTimeInhMMa;
      challengeMailVariables.checkpoint_start_time =
        challengeStartTimeInhMMa;
      challengeMailVariables.checkpointEndDateText =
        challengeEndDateInddMMMYYYY;
      challengeMailVariables.checkpoint_end_date =
        challengeEndDateInddMMMYYYY;
      challengeMailVariables.checkpointEndTime = challengeEndTimeInhMMa;
      challengeMailVariables.checkpoint_end_time = challengeEndTimeInhMMa;
      challengeMailVariables.checkpoint_title = title;
    }
  }

  if (
    mailType ===
    CHALLENGE_MAIL_TYPES.CHALLENGE_WINNER_DECLARATION_NOTIFICATION_MEMBER
  ) {
    const participantProgramInfo = await ProgramParticipantModel.findOne({
      programObjectId: challengeObjectId,
      learnerObjectId: participantLearnerInfo._id,
    }).lean();

    challengeMailVariables.completed_checkpoint_count =
      participantProgramInfo?.completedCount ?? 0;
  }

  return { challengeMailVariables, communityManagersEmailData };
};

const getChallengeTestMailVariables = async (
  challenge,
  community,
  mailType
) => {
  const { _id: challengeObjectId, createdBy } = challenge;

  const learner = await LearnersModel.findOne({
    email: createdBy,
  });
  const participant = {
    learnerObjectId: learner._id,
    joinedDate: DateTime.utc(),
    checkoutId: null,
    removalReason: 'This is a dummy reason',
  };

  const checkpoint = await ProgramItemModel.findOne({
    programObjectId: challengeObjectId,
    type: 'checkpoint',
  });

  const { challengeMailVariables } = await formatChallengeMailVariables(
    participant,
    challenge,
    community,
    mailType,
    checkpoint
  );
  return {
    ...challengeMailVariables,
    participant_rank: 1, // cause its a test mail, and we are just adding the rank as 1
  };
};
const sendJoinChallengeMailToParticipantAndManager = async (
  community,
  mailVariableInfo,
  challenge,
  addedByLearnerEmail,
  config = {}
) => {
  const { challengeMailVariables, communityManagersEmailData } =
    mailVariableInfo;

  const { createdBy } = challenge;
  if (challenge.additionalSettings?.chatGroupLink) {
    challengeMailVariables.chat_group_link =
      challenge.additionalSettings.chatGroupLink;
  }

  const authServiceRpc = new AuthServiceRpc();
  const { token } = await authServiceRpc.generateEmailToken(
    challengeMailVariables.email
  );
  if (token) {
    challengeMailVariables.challengeLink = `${challengeMailVariables.challengeLink}?accessToken=${token}`;
  }

  const managerEmailConfig = mailUtils.retrieveManagerMailConfig(
    community.title,
    community.link,
    challenge.createdBy
  );

  await notificationCommonService.sendMailToQueue(
    CHALLENGE_MAIL_TYPES.CHALLENGE_PARTICIPANT_ADDED_MEMBER,
    community.code,
    challenge._id,
    [challengeMailVariables.email],
    [challengeMailVariables?.name ?? ''],
    challengeMailVariables,
    null,
    managerEmailConfig,
    config
  );

  // TODO: review with minh if we need to send email to all the community managers or just the one who added the participant or just the owner
  const sendEmailToCommunityManagers = communityManagersEmailData.map(
    async (managerEmailData) => {
      if (
        managerEmailData.email === addedByLearnerEmail ||
        managerEmailData.email === createdBy
      ) {
        const {
          email: managerEmail,
          startDate,
          endDate,
          timezoneText,
        } = managerEmailData;

        await sendEmail({
          email: challengeMailVariables.email,
          mailType:
            CHALLENGE_MAIL_TYPES.CHALLENGE_PARTICIPANT_ADDED_MANAGER,
          toMailName: [managerEmailData?.name ?? ''],
          toMail: [managerEmail],
          replyToMail: challenge.createdBy,
          data: {
            ...challengeMailVariables,
            startDate,
            endDate,
            time_zone_text: timezoneText,
            timezoneOffset: managerEmailData.timezone_offset,
          },
          mailCourse: community.code,
          mailCourseOffer: challenge._id,
          name: challengeMailVariables.name,
        });
        return true;
      }
      return true;
    }
  );

  await Promise.all(sendEmailToCommunityManagers);

  return true;
};

const sendRemovalChallengeMailToParticipantAndManager = async (
  community,
  mailVariableInfo,
  challenge
) => {
  const { challengeMailVariables } = mailVariableInfo;

  await sendEmail({
    email: challengeMailVariables.email,
    mailType:
      CHALLENGE_MAIL_TYPES.CHALLENGE_PARTICIPANT_REMOVED_NOTIFICATION_MEMBER,
    toMailName: [challengeMailVariables?.name ?? ''],
    toMail: [challengeMailVariables.email],
    replyToMail: challenge.createdBy,
    data: challengeMailVariables,
    mailCourse: community.code,
    mailCourseOffer: challenge._id,
  });

  return true;
};

const sendWinnerChallengeMailToParticipantAndManager = async (
  community,
  mailVariableInfo,
  challenge
) => {
  const { challengeMailVariables } = mailVariableInfo;

  await sendEmail({
    email: challengeMailVariables.email,
    mailType:
      CHALLENGE_MAIL_TYPES.CHALLENGE_WINNER_DECLARATION_NOTIFICATION_MEMBER,
    toMailName: [challengeMailVariables?.name ?? ''],
    toMail: [challengeMailVariables.email],
    replyToMail: challenge.createdBy,
    data: challengeMailVariables,
    mailCourse: community.code,
    mailCourseOffer: challenge._id,
  });

  return true;
};

const addParticipant = async ({
  challenge,
  learnerObjectId,
  checkoutId,
  community,
  session = null,
  addedByLearnerObject = null,
}) => {
  const { _id: communityObjectId } = community;
  const { _id: programObjectId, additionalSettings } = challenge;

  const createParticipantObj = {
    communityObjectId,
    programObjectId,
    learnerObjectId,
    status: PARTICIPANT_PROGRAM_STATUS.PARTICIPATED,
  };

  if (checkoutId) {
    createParticipantObj.checkoutId = checkoutId;
  }

  if (addedByLearnerObject) {
    createParticipantObj.addedByLearnerObjectId = addedByLearnerObject;
  }
  createParticipantObj.joinedDate = DateTime.utc();

  const [existingParticipant, addonTransaction] = await Promise.all([
    ProgramParticipantModel.findOne({
      communityObjectId,
      programObjectId,
      learnerObjectId,
      status: { $ne: PARTICIPANT_PROGRAM_STATUS.SYSTEM_REMOVED },
    }).lean(),
    communityAddonTransactionsModel.findById(checkoutId).lean(),
  ]);

  if (existingParticipant) {
    return existingParticipant;
  }

  const autoEnrollments = additionalSettings?.autoEnrollments;

  await autoEnrollmentService.handleAutoEnrollments({
    autoEnrollments,
    learnerObjectId,
    session,
  });

  const [participant] = await ProgramParticipantModel.create(
    [createParticipantObj],
    {
      session,
    }
  );

  const generateReceipt = addonTransaction && addonTransaction.amount > 0;

  // TODO: remove try catch after adding the mail template on sendgrid otherwise the whole api will fail
  try {
    if (
      (addonTransaction && addonTransaction.amount === 0) ||
      !addonTransaction
    ) {
      const learner = await LearnersModel.findById(learnerObjectId).lean();

      await ActionEventService.sendFreeAddonActionEvent({
        actionEventType: ADDON_ACTION_EVENT_TYPES.CHALLENGE_SIGNUP,
        entityObjectId: programObjectId,
        entityCollection: COMMUNITY_ONE_TIME_PAYMENT_ENTITIES.CHALLENGE,
        communityObjectId,
        addonTransaction,
        learner,
      });
    }

    const mailVariableInfo = await formatChallengeMailVariables(
      participant,
      challenge,
      community,
      CHALLENGE_MAIL_TYPES.CHALLENGE_PARTICIPANT_ADDED_MANAGER // this one is only needed for checking if noti pref is enabled for managers
    );

    const config = receiptService.generateReceiptConfig({
      purchasedId: addonTransaction?._id,
      purchaseType: PURCHASE_TYPE.CHALLENGE,
      entityObjectId: challenge._id,
      communityObjectId,
      learnerObjectId,
      generateReceipt,
    });

    await sendJoinChallengeMailToParticipantAndManager(
      community,
      mailVariableInfo,
      challenge,
      null,
      config
    );

    const upsellWithEntityInfo =
      await upsellService.retrieveUpsellForChallenge(
        challenge._id,
        learnerObjectId,
        true
      );

    if (upsellWithEntityInfo) {
      await upsellService.formatAndSendUpsellTypeEmail({
        learnerObjectId,
        upsell: upsellWithEntityInfo,
        sourceParentEntity: challenge,
        community,
      });
    }
  } catch (error) {
    logger.error('Error in sending join mail', error);
  }

  await batchMetadataService.add({
    batchMetadataModelType: BATCH_METADATA_MODEL_TYPE.PROGRAM_PARTICIPANT,
    entityObjectId: programObjectId,
    communityObjectId,
    community,
    addedObjectId: participant._id,
  });

  return participant;
};
const addParticipantsByCM = async (
  participants,
  challenge,
  community,
  addedByLearnerObjectId
) => {
  const {
    communityObjectId,
    _id: programObjectId,
    additionalSettings,
  } = challenge;

  let addedByLearnerInfo = null;

  if (addedByLearnerObjectId) {
    addedByLearnerInfo = await LearnersModel.findById(
      addedByLearnerObjectId
    ).select('name email');
  }

  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const participantsAdded = [];

  for await (const participant of participants) {
    const { email, subscriptionObjectId } = participant;

    const learner = await LearnersModel.findOne({
      email,
      isActive: true,
    });

    if (!learner) {
      return {
        email,
        error: 'Learner not found',
        subscriptionObjectId,
      };
    }

    if (!subscriptionObjectId) {
      const isManager =
        await membershipService.getService.isCommunityManager({
          email,
          communityCode: community.code,
        });
      if (!isManager) {
        return {
          email,
          error: 'Subscription not passed for non CM',
        };
      }
    } else {
      const isLearnerSubscribedToCommunity =
        await subscriptionInfo.isSubscriptionIdActiveForCommunity(
          subscriptionObjectId,
          community.code
        );

      if (!isLearnerSubscribedToCommunity) {
        return {
          email,
          error: 'Learner not subscribed to community',
          subscriptionObjectId,
        };
      }
    }

    const session = await primaryMongooseConnection.startSession();
    session.startTransaction();

    let createdParticipant;

    try {
      const createParticipantObj = {
        communityObjectId,
        programObjectId,
        learnerObjectId: learner._id,
        status: PARTICIPANT_PROGRAM_STATUS.PARTICIPATED,
        'modifiedByLearnerObjectId.added': addedByLearnerObjectId,
      };

      createParticipantObj.joinedDate = DateTime.utc();

      const existingParticipant = await ProgramParticipantModel.findOne({
        communityObjectId,
        programObjectId,
        learnerObjectId: learner._id,
      }).lean();

      if (existingParticipant) {
        // return existingParticipant;
        createdParticipant =
          await ProgramParticipantModel.findOneAndUpdate(
            {
              _id: existingParticipant._id,
            },
            {
              status: PARTICIPANT_PROGRAM_STATUS.PARTICIPATED,
              'modifiedByLearnerObjectId.added': addedByLearnerObjectId,
              joinedDate: DateTime.utc(),
            },
            {
              new: true,
              session,
            }
          ).lean();
      } else {
        createdParticipant = (
          await ProgramParticipantModel.create([createParticipantObj], {
            session,
          })
        )[0].toObject();
      }

      const autoEnrollments = additionalSettings?.autoEnrollments;

      await autoEnrollmentService.handleAutoEnrollments({
        autoEnrollments,
        learnerObjectId: learner._id,
        session,
      });

      await session.commitTransaction();
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      await session.endSession();
    }

    await batchMetadataService.add({
      batchMetadataModelType:
        BATCH_METADATA_MODEL_TYPE.PROGRAM_PARTICIPANT,
      entityObjectId: programObjectId,
      communityObjectId,
      community,
      addedObjectId: createdParticipant._id,
    });

    // TODO: remove try catch after adding the mail template on sendgrid otherwise the whole api will fail
    try {
      await ActionEventService.sendFreeAddonActionEvent({
        actionEventType: ADDON_ACTION_EVENT_TYPES.CHALLENGE_SIGNUP,
        entityObjectId: programObjectId,
        entityCollection: COMMUNITY_ONE_TIME_PAYMENT_ENTITIES.CHALLENGE,
        communityObjectId,
        addonTransaction: null,
        learner,
      });

      const mailVariableInfo = await formatChallengeMailVariables(
        createdParticipant,
        challenge,
        community,
        CHALLENGE_MAIL_TYPES.CHALLENGE_PARTICIPANT_ADDED_MANAGER // this one is only needed for checking if noti pref is enabled for managers
      );

      await sendJoinChallengeMailToParticipantAndManager(
        community,
        mailVariableInfo,
        challenge,
        addedByLearnerInfo?.email
      );

      const upsellWithEntityInfo =
        await upsellService.retrieveUpsellForChallenge(
          challenge._id,
          learner._id,
          true
        );

      if (upsellWithEntityInfo) {
        await upsellService.formatAndSendUpsellTypeEmail({
          learnerObjectId: learner._id,
          upsell: upsellWithEntityInfo,
          sourceParentEntity: challenge,
          community,
          learner,
        });
      }
    } catch (error) {
      logger.error('Error in sending join mail', error);
    }

    participantsAdded.push(createdParticipant);
  }

  return participantsAdded;
};

const removeParticipants = async (
  removeParticipantsBodyParams,
  challenge,
  community,
  removedByLearnerObjectId
) => {
  const { participants } = removeParticipantsBodyParams;
  let removedByLearnerInfo = null;

  if (removedByLearnerObjectId) {
    removedByLearnerInfo = await LearnersModel.findById(
      removedByLearnerObjectId
    ).select('name email');
  }
  const removedParticipantsStatus = participants.map(
    async (participant) => {
      const { participantObjectId, removalReason } = participant;
      const updatedParticipant =
        await ProgramParticipantModel.findOneAndUpdate(
          {
            _id: participantObjectId,
          },
          {
            status: PARTICIPANT_PROGRAM_STATUS.KICKED_OUT,
            'modifiedByLearnerObjectId.kickedOut':
              removedByLearnerObjectId,
            removalReason,
            kickedOutDate: DateTime.utc(),
          },
          {
            new: true,
          }
        );

      // TODO: remove try catch after adding the mail template on sendgrid otherwise the whole api will fail
      try {
        const mailVariableInfo = await formatChallengeMailVariables(
          updatedParticipant,
          challenge,
          community,
          null
        );

        await sendRemovalChallengeMailToParticipantAndManager(
          community,
          mailVariableInfo,
          challenge,
          removedByLearnerInfo?.email
        );
      } catch (error) {
        logger.error('Error in sending removal mail', error);
      }
      return updatedParticipant;
    }
  );

  const participantsRemoved = await Promise.all(removedParticipantsStatus);

  return participantsRemoved;
};

const declareWinners = async (
  winnerBodyParams,
  challenge,
  community,
  declaredWinnerByLearnerObjectId
) => {
  const { winners } = winnerBodyParams;
  const { _id: communityObjectId } = community;
  const { _id: programObjectId } = challenge;

  const declaredWinnersStatus = winners.map(async (winner) => {
    const { participantObjectId, declareWinner } = winner;

    let updateFilter = {};

    const participantInfo = await ProgramParticipantModel.findOne({
      _id: participantObjectId,
      communityObjectId,
      programObjectId,
    }).lean();

    if (declareWinner) {
      updateFilter = {
        status: PARTICIPANT_PROGRAM_STATUS.WINNER,
        'modifiedByLearnerObjectId.declaredWinner':
          declaredWinnerByLearnerObjectId,
        declaredWinnerDate: DateTime.utc(),
      };
    } else if (
      participantInfo.status === PARTICIPANT_PROGRAM_STATUS.WINNER &&
      !declareWinner // this if condition might or might not be needed
    ) {
      const isAllTasksCompleted = participantInfo.completedDate;
      if (isAllTasksCompleted) {
        updateFilter = {
          status: PARTICIPANT_PROGRAM_STATUS.COMPLETED,
        };
      } else {
        updateFilter = {
          status: PARTICIPANT_PROGRAM_STATUS.PARTICIPATED,
        };
      }
    }

    const updatedWinner = await ProgramParticipantModel.findOneAndUpdate(
      {
        _id: participantObjectId,
      },
      updateFilter,
      {
        new: true,
      }
    );
    // TODO: remove try catch after adding the mail template on sendgrid otherwise the whole api will fail
    try {
      // TODO: Send notification to winner
      const mailVariableInfo = await formatChallengeMailVariables(
        updatedWinner,
        challenge,
        community,
        null
      );

      await sendWinnerChallengeMailToParticipantAndManager(
        community,
        mailVariableInfo,
        challenge
      );
    } catch (error) {
      logger.error('Error in sending winner mail', error);
    }
    return updatedWinner;
  });

  const winnersDeclared = await Promise.all(declaredWinnersStatus);

  return winnersDeclared;
};

const updateParticipant = async ({
  participantId,
  programId,
  payload,
}) => {
  const toDBPayload = {};
  if (payload.status) {
    toDBPayload.status = payload.status;
    switch (payload.status) {
      case PARTICIPANT_PROGRAM_STATUS.KICKED_OUT:
        toDBPayload.kickedOutDate = DateTime.utc();
        break;
      case PARTICIPANT_PROGRAM_STATUS.WINNER:
        toDBPayload.declaredWinnerDate = DateTime.utc();
        break;
      case PARTICIPANT_PROGRAM_STATUS.COMPLETED:
        break;
      default:
        break;
    }
  }
  const updatedParticipant =
    await ProgramParticipantModel.findOneAndUpdate(
      {
        _id: participantId,
        programObjectId: programId,
      },
      {
        $set: toDBPayload,
      },
      {
        new: true,
      }
    );

  return updatedParticipant;
};

const sendCheckpointReminderToParticipants = async ({
  community,
  checkpoint,
  challenge,
  jobId,
  mailType,
}) => {
  const { _id: communityObjectId } = community;
  const mailConfig = await mailConfigModel.findOne({
    mailType,
    mailCourseOffer: String(challenge._id),
    mailCourse: community.code,
  });

  if (mailConfig && !mailConfig?.preferences?.isEnabled) {
    // update the job status to completed
    await reportJobResult({
      jobObjectId: jobId,
      resultType: RESULT_TYPE.SINGLE,
      jobStatus: JOB_STATUS.COMPLETED,
      results: {
        status: JOB_STATUS.COMPLETED,
        message:
          'Checkpoint did not sent to participants as the mail is disabled for the community',
      },
    });
    return false;
  }

  let filter = {
    programObjectId: challenge._id,
    communityObjectId,
    status: PARTICIPANT_PROGRAM_STATUS.PARTICIPATED,
  };

  if (
    mailType ===
      CHALLENGE_MAIL_TYPES.CHALLENGE_PARTICIPANT_CHECKPOINT_ENDING_IN_1_HOUR_REMINDER ||
    mailType ===
      CHALLENGE_MAIL_TYPES.CHALLENGE_PARTICIPANT_CHECKPOINT_70_PERCENT_REMINDER
  ) {
    filter = {
      programObjectId: challenge._id,
      communityObjectId,
      // items should not have the _id of the checkpoint
      'items.itemObjectId': { $ne: checkpoint._id },
      status: {
        $in: [
          PARTICIPANT_PROGRAM_STATUS.PARTICIPATED,
          PARTICIPANT_PROGRAM_STATUS.WINNER,
          PARTICIPANT_PROGRAM_STATUS.COMPLETED,
        ],
      },
    };
  }

  const participantsCount = await ProgramParticipantModel.countDocuments(
    filter
  );

  let offset = 0;
  const limit = PROGRAM_CHECKPOINTS_REMINDER_BATCH_SIZE;

  while (offset < participantsCount) {
    // eslint-disable-next-line no-await-in-loop
    await sendMessageToSQSQueue({
      messageBody: {
        programObjectId: challenge._id,
        mailType,
        communityObjectId,
        checkpointId: checkpoint?._id,
        skip: offset,
        limit,
      },
      messageGroupId: 'checkpoint-reminder',
      queueUrl: CHALLENGE_CHECKPOINT_NOTIFICATION_QUEUE_URL,
      isFifo: false,
    });
    offset += limit;
  }

  // update the job status to completed
  await reportJobResult({
    jobObjectId: jobId,
    resultType: RESULT_TYPE.SINGLE,
    jobStatus: JOB_STATUS.COMPLETED,
    results: {
      status: JOB_STATUS.COMPLETED,
      message: 'Checkpoint reminder sent to all participants',
    },
  });

  return true;
};

const sendChallengeStartorEndNotificationToAllParticipants = async ({
  challengeId,
  communityId,
  communityCode,
  notificationId,
  type,
  program,
  title,
  body,
}) => {
  const participants = await ProgramParticipantModel.find({
    programObjectId: challengeId,
    communityObjectId: communityId,
    status: {
      $in: [
        PARTICIPANT_PROGRAM_STATUS.PARTICIPATED,
        PARTICIPANT_PROGRAM_STATUS.WINNER,
        PARTICIPANT_PROGRAM_STATUS.COMPLETED,
      ],
    },
  })
    .populate('learnerObjectId')
    .lean();

  const sendNotificationsOnMobile = participants.map(
    async (participant) => {
      const { learnerObjectId: learnerInfo, _id: participantId } =
        participant;
      const { learnerId: userId } = learnerInfo;
      const toSend = await getMobileNotificationBody({
        type,
        challengeId,
        communityId,
        participantId,
        communityCode,
        userId,
        program,
        title,
        body,
        zone: learnerInfo.timezone,
      });
      await sendMobileNotificationToQueue(
        type,
        [userId],
        MOBILE_NOTIFICATION_RECIPIENTS_SCOPES.GLOBAL,
        toSend,
        userId
      );
    }
  );
  await Promise.all(sendNotificationsOnMobile);
  await mobileNotificationsScheduleModel.updateOne(
    {
      _id: notificationId,
    },
    {
      $set: {
        status: scheduledNotificationStatuses.FINISHED,
      },
    }
  );

  return true;
};

const sendCheckpointMobileNotificationsToParticipants = async ({
  challengeId,
  communityId,
  communityCode,
  notificationId,
  program,
  type,
  checkpointInfo,
  title,
  body,
}) => {
  let programParticipants;

  switch (type) {
    case MOBILE_NOTIFICATION_TYPES.CHECKPOINT_EVENT_NOW:
    case MOBILE_NOTIFICATION_TYPES.CHECKPOINT_EVENT_1_HOUR:
    case MOBILE_NOTIFICATION_TYPES.CHECKPOINT_EVENT_24_HOUR:
    case MOBILE_NOTIFICATION_TYPES.CHECKPOINT_START:
      programParticipants = await ProgramParticipantModel.find({
        programObjectId: challengeId,
        communityObjectId: communityId,
        status: {
          $in: [
            PARTICIPANT_PROGRAM_STATUS.PARTICIPATED,
            PARTICIPANT_PROGRAM_STATUS.WINNER,
            PARTICIPANT_PROGRAM_STATUS.COMPLETED,
          ],
        },
      })
        .populate('learnerObjectId')
        .lean();
      break;
    case MOBILE_NOTIFICATION_TYPES.CHECKPOINT_70_PERCENT_DONE:
    case MOBILE_NOTIFICATION_TYPES.CHECKPOINT_ENDING_IN_1_HOUR:
      programParticipants = await ProgramParticipantModel.find({
        programObjectId: challengeId,
        communityObjectId: communityId,
        // items should not have the _id of the checkpoint which is ending in 1 hour (might have to change the filter to aggregate if its not working as expected)
        'items.itemObjectId': { $ne: checkpointInfo._id },
        status: {
          $in: [
            PARTICIPANT_PROGRAM_STATUS.PARTICIPATED,
            PARTICIPANT_PROGRAM_STATUS.WINNER,
            PARTICIPANT_PROGRAM_STATUS.COMPLETED,
          ],
        },
      })
        .populate('learnerObjectId')
        .lean();
      break;
    default:
      programParticipants = [];
      break;
  }

  const sendNotificationsOnMobile = programParticipants.map(
    async (participant) => {
      const { learnerObjectId: learnerInfo, _id: participantId } =
        participant;
      const { learnerId: userId, languagePreference } = learnerInfo;
      const toSend = await getMobileNotificationBody({
        type,
        challengeId,
        communityId,
        participantId,
        communityCode,
        userId,
        program,
        checkpointInfo,
        title,
        checkpointId: checkpointInfo._id,
        body,
        languagePreference,
        zone: learnerInfo.timezone,
      });
      await sendMobileNotificationToQueue(
        type,
        [userId],
        MOBILE_NOTIFICATION_RECIPIENTS_SCOPES.GLOBAL,
        toSend
      );
      return true;
    }
  );

  await Promise.all(sendNotificationsOnMobile);

  await mobileNotificationsScheduleModel.updateOne(
    {
      _id: notificationId,
    },
    {
      $set: {
        status: scheduledNotificationStatuses.FINISHED,
      },
    }
  );
  return true;
};

const sendAlwaysOnCheckpointMobileNotificationsToParticipant = async ({
  challengeId,
  communityId,
  communityCode,
  participantId,
  program,
  type,
  checkpointInfo,
  title,
  body,
}) => {
  const participant = await ProgramParticipantModel.findById(participantId)
    .populate('learnerObjectId')
    .lean();

  if (!communityCode) {
    const community = await CommunityModel.findById(communityId)
      .select('code')
      .lean();
    // eslint-disable-next-line no-param-reassign
    communityCode = community.code;
  }
  const { learnerObjectId: learnerInfo } = participant;
  const { learnerId: userId, languagePreference } = learnerInfo;
  const toSend = await getMobileNotificationBody({
    type,
    challengeId,
    communityId,
    communityCode,
    userId,
    program,
    title,
    checkpointId: checkpointInfo._id.toString(),
    body,
    languagePreference,
    zone: learnerInfo.timezone,
  });
  await sendMobileNotificationToQueue(
    type,
    [userId],
    MOBILE_NOTIFICATION_RECIPIENTS_SCOPES.GLOBAL,
    toSend
  );
  return true;
};

const sendChallengeMobileNotification = async ({
  notificationBody,
  notificationId,
}) => {
  const {
    communityCode,
    type,
    challengeId,
    checkpointId,
    communityId,
    title,
    body,
    participantId,
  } = notificationBody;

  let checkpointInfo;
  const program = await ProgramModel.findOne({
    _id: challengeId,
    status: PROGRAM_STATUS.PUBLISHED,
  });
  if (!program) {
    // update the job status to completed
    await mobileNotificationsScheduleModel.updateOne(
      {
        _id: notificationId,
      },
      {
        $set: {
          status: scheduledNotificationStatuses.FINISHED,
        },
      }
    );

    throw new ParamError('Challenge not found', 404);
  }

  if (checkpointId) {
    checkpointInfo = await ProgramItemModel.findOne({
      _id: checkpointId,
    });
  }

  switch (type) {
    case MOBILE_NOTIFICATION_TYPES.CHALLENGE_ENDED:
    case MOBILE_NOTIFICATION_TYPES.CHALLENGE_STARTED: {
      await sendChallengeStartorEndNotificationToAllParticipants({
        challengeId,
        communityId,
        communityCode,
        notificationId,
        program,
        type,
        title,
        body,
      });
      break;
    }
    case MOBILE_NOTIFICATION_TYPES.CHECKPOINT_EVENT_NOW:
    case MOBILE_NOTIFICATION_TYPES.CHECKPOINT_EVENT_1_HOUR:
    case MOBILE_NOTIFICATION_TYPES.CHECKPOINT_EVENT_24_HOUR:
    case MOBILE_NOTIFICATION_TYPES.CHECKPOINT_70_PERCENT_DONE:
    case MOBILE_NOTIFICATION_TYPES.CHECKPOINT_ENDING_IN_1_HOUR:
    case MOBILE_NOTIFICATION_TYPES.CHECKPOINT_START: {
      await sendCheckpointMobileNotificationsToParticipants({
        challengeId,
        communityId,
        communityCode,
        notificationId,
        program,
        type,
        checkpointInfo,
        title,
        body,
      });
      break;
    }
    case MOBILE_NOTIFICATION_TYPES.ALWAYS_ON_CHECKPOINT_START: {
      await sendAlwaysOnCheckpointMobileNotificationsToParticipant({
        challengeId,
        communityId,
        communityCode,
        participantId,
        program,
        type,
        checkpointInfo,
        title,
        body,
      });
      break;
    }
    default:
      break;
  }

  return true;
};

const clearCompletions = async ({ programId, programItemId }) => {
  const [program, programItem] = await Promise.all([
    ProgramModel.findOne({
      _id: programId,
    }),
    ProgramItemModel.findOne({
      _id: programItemId,
    }),
  ]);
  if (!program || !programItem) {
    throw new ParamError('Program or ProgramItem not found');
  }

  let results;
  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();
  try {
    await ParticipantProgramItemModel.deleteMany({
      programObjectId: programId,
      itemObjectId: programItemId,
    });
    const completedParticipants = await ProgramParticipantModel.find(
      {
        programObjectId: programId,
        'items.itemObjectId': programItemId,
      },
      { _id: 1 }
    );
    const participantIds = completedParticipants.map(
      (participant) => participant._id
    );
    results = await ProgramParticipantModel.updateMany(
      {
        _id: { $in: participantIds },
      },
      {
        $pull: {
          items: {
            itemObjectId: programItemId,
          },
        },
        $inc: {
          completedCount: -1,
        },
      },
      {
        session,
      }
    );
    await session.commitTransaction();
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }
  return results;
};

const awardPoints = async ({
  communityObjectId,
  challengeObjectId,
  participantObjectId,
  checkpointObjectId,
  reason,
  awardType,
  notify,
}) => {
  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  let result;

  const lock = await getLock();
  const releaseLock = await lock(participantObjectId, 5000); // 5 sec

  try {
    result = await leaderboardService.awardPoints({
      programObjectId: challengeObjectId,
      participantObjectId,
      programItemObjectId: checkpointObjectId,
      awardType,
      reason,
      session,
    });
    await session.commitTransaction();
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
    await releaseLock();
  }

  if (notify) {
    const [community, programItem, learner] = await Promise.all([
      CommunityModel.findById(communityObjectId, {
        link: 1,
        code: 1,
      }).lean(),
      ProgramItemModel.findById(checkpointObjectId, { title: 1 }).lean(),
      LearnersModel.findById(result.participant?.learnerObjectId).lean(),
    ]);

    const additionalFields = formatAwardOrResetPointData({
      community,
      program: result.program,
      programItem,
      point: result.participantProgramItem.bonusPoint,
      reason: result.participantProgramItem.reason,
    });

    await leaderboardService.formatAndSendAwardOrResetEmail({
      mailType: CHALLENGE_MAIL_TYPES.MEMBER_CHALLENGE_AWARD_POINT,
      program: result.program,
      learner,
      community,
      additionalFields,
    });
  }

  return result.participantProgramItem;
};

const resetPoints = async ({
  communityObjectId,
  challengeObjectId,
  participantObjectId,
  checkpointObjectId,
  reason,
  notify,
}) => {
  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  let result;

  const lock = await getLock();
  const releaseLock = await lock(participantObjectId, 5000); // 5 sec

  try {
    result = await leaderboardService.resetPoints({
      programObjectId: challengeObjectId,
      participantObjectId,
      programItemObjectId: checkpointObjectId,
      reason,
      session,
    });
    await session.commitTransaction();
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
    await releaseLock();
  }

  if (notify) {
    const [community, programItem, learner] = await Promise.all([
      CommunityModel.findById(communityObjectId, {
        link: 1,
        code: 1,
      }).lean(),
      ProgramItemModel.findById(checkpointObjectId, { title: 1 }).lean(),
      LearnersModel.findById(result.participant?.learnerObjectId).lean(),
    ]);

    const additionalFields = formatAwardOrResetPointData({
      community,
      program: result.program,
      programItem,
      point: 0,
      reason: result.participantProgramItem?.reason,
    });

    await leaderboardService.formatAndSendAwardOrResetEmail({
      mailType: CHALLENGE_MAIL_TYPES.MEMBER_CHALLENGE_RESET_POINT,
      program: result.program,
      learner,
      community,
      additionalFields,
    });
  }

  return result.participantProgramItem;
};

module.exports = {
  formatChallengeMailVariables,
  getChallengeTestMailVariables,
  addParticipant,
  removeParticipants,
  declareWinners,
  addParticipantsByCM,
  updateParticipant,
  sendCheckpointReminderToParticipants,
  sendChallengeMobileNotification,
  clearCompletions,
  awardPoints,
  resetPoints,
};
