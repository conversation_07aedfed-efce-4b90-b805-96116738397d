// const { DateTime } = require('luxon');
const {
  sendMobileNotification,
  // scheduleMobileNotification,
} = require('../../notification/mobileNotifications.service');
const {
  MOBILE_NOTIFICATION_TYPES,
  MOBILE_NOTIFICATION_RECIPIENTS_SCOPES,
} = require('../../../constants/common');
const logger = require('../../logger.service');
const CommunityRoleModel = require('../../../communitiesAPI/models/communityRole.model');
const { aclRoles } = require('../../../communitiesAPI/constants');
const MongodbUtils = require('../../../utils/mongodb.util');
const { NAS_IO_FRONTEND_URL } = require('../../../config');

const sendMemberPendingApprovalMobileNotification = async (community) => {
  try {
    const {
      title,
      _id: communityObjectId,
      code: communityCode,
    } = community;

    const query = [
      {
        $match: {
          communityCode,
          role: aclRoles.ADMIN,
        },
      },
      ...MongodbUtils.lookupAndUnwind(
        'users',
        'userObjectId',
        '_id',
        'user'
      ),
      {
        $project: {
          userId: '$user.user_id',
        },
      },
    ];

    const communityAdmins = await CommunityRoleModel.aggregate(query);

    if (!communityAdmins || communityAdmins.length === 0) {
      logger.info(`No admins found for community: ${communityCode}`);
      return;
    }

    // Extract admin userIds
    const adminUserIds = communityAdmins
      .map((admin) => admin.userId)
      .filter(Boolean);

    if (adminUserIds.length === 0) {
      logger.info(
        `No admin userIds found for community: ${communityCode}`
      );
      return;
    }

    // Send notification to all admins
    await sendMobileNotification(
      MOBILE_NOTIFICATION_TYPES.NEW_MEMBER_APPLICATION,
      adminUserIds,
      MOBILE_NOTIFICATION_RECIPIENTS_SCOPES.GLOBAL,
      {
        title: `New member request for ${title}!`,
        body: `You’ve got a new application to join your community. Check it out!`,
        communityTitle: title,
        communityId: communityObjectId,
        link: `${NAS_IO_FRONTEND_URL}/mb/communities/${communityObjectId}/community?initialIndex=0`,
      }
    );

    logger.info(
      `Sent mobile notifications to ${adminUserIds.length} community admins for new member application in ${communityCode}. LearnerObjectIds:`,
      adminUserIds
    );
  } catch (error) {
    logger.error(
      `Error sending mobile notification to CM: ${error.message}`,
      error
    );
  }
};

exports.sendEnrollmentNotification = async ({
  community,
  learner,
  options = {},
}) => {
  const {
    title,
    request_approval: requestApproval,
    applicationConfig,
    _id: communityObjectId,
  } = community;

  const bypassPendingApproval = options.bypassPendingApproval;

  const autoApproval = requestApproval
    ? applicationConfig?.autoApproval ?? false
    : false;

  if (!bypassPendingApproval && requestApproval && !autoApproval) {
    // Send member pending application approval notification to community managers
    logger.info(
      `Sending mobile notification to CMs for new member application. CommunityId: ${community._id}, Learner: ${learner._id}`
    );
    await sendMemberPendingApprovalMobileNotification(community);
    return;
  }

  // const sevenDaysFromNow = DateTime.utc().plus({ days: 7 });
  const { learnerId } = learner;

  const result = await Promise.allSettled([
    sendMobileNotification(
      MOBILE_NOTIFICATION_TYPES.APPLICATION_APPROVED,
      undefined,
      MOBILE_NOTIFICATION_RECIPIENTS_SCOPES.SINGLE,
      {
        title: `Welcome to ${title}!`,
        body: 'Go to the portal to get started and connect with other members in the community.',
        communityId: communityObjectId,
      },
      learnerId
    ),
    // Requested by Janet
    // scheduleMobileNotification({
    //   dueDate: sevenDaysFromNow,
    //   recurring: false,
    //   notification: {
    //     type: MOBILE_NOTIFICATION_TYPES.FIRST_EVENT_ATTENDANCE,
    //     communityCode,
    //   },
    //   recipients: {
    //     type: MOBILE_NOTIFICATION_RECIPIENTS_SCOPES.SINGLE,
    //     userId: learnerId,
    //   },
    // }),
  ]);

  logger.info(`sendEnrollmentNotification: ${JSON.stringify(result)}`);
};

exports.sendRenewalFailureNotification = async ({
  community,
  learner,
}) => {
  const { learnerId, firstName } = learner;
  const { title } = community;

  const result = await Promise.allSettled([
    sendMobileNotification(
      MOBILE_NOTIFICATION_TYPES.RENEWAL_FAILED_PAYMENT,
      undefined,
      MOBILE_NOTIFICATION_RECIPIENTS_SCOPES.SINGLE,
      {
        name: firstName ?? '',
        community_name: title,
      },
      learnerId
    ),
  ]);

  logger.info(`sendRenewalFailureNotification: ${JSON.stringify(result)}`);
};

exports.sendPlanReferralRewardNotification = async ({
  refereeCommunity,
  community,
  learner,
  rewardAmount,
  currency,
  isFirstBillingCycle,
}) => {
  const { learnerId } = learner;
  const { title } = refereeCommunity;

  const type = isFirstBillingCycle
    ? MOBILE_NOTIFICATION_TYPES.COMMUNITY_REFERRAL_SUCCESS
    : MOBILE_NOTIFICATION_TYPES.COMMUNITY_REFERRAL_RENEWED;

  const result = await Promise.allSettled([
    sendMobileNotification(
      type,
      undefined,
      MOBILE_NOTIFICATION_RECIPIENTS_SCOPES.SINGLE,
      {
        communityName: title,
        currency,
        amount: rewardAmount / 100,
        communityId: community._id,
      },
      learnerId
    ),
  ]);

  logger.info(
    `sendPlanReferralRewardNotification: ${JSON.stringify(result)}`
  );
};

exports.sendMembersLimitReachingNotification = async ({
  community,
  learner,
}) => {
  const { learnerId } = learner;
  const { _id: communityObjectId } = community;

  const type = MOBILE_NOTIFICATION_TYPES.MEMBERS_LIMIT_REACHING;

  const result = await Promise.allSettled([
    sendMobileNotification(
      type,
      undefined,
      MOBILE_NOTIFICATION_RECIPIENTS_SCOPES.SINGLE,
      {
        communityId: communityObjectId,
      },
      learnerId
    ),
  ]);

  logger.info(
    `sendMembersLimitReachingNotification: ${JSON.stringify(result)}`
  );
};

exports.sendMembersLimitReachedNotification = async ({
  community,
  learner,
}) => {
  const { learnerId } = learner;
  const { _id: communityObjectId } = community;

  const type = MOBILE_NOTIFICATION_TYPES.MEMBERS_LIMIT_REACHED;

  const result = await Promise.allSettled([
    sendMobileNotification(
      type,
      undefined,
      MOBILE_NOTIFICATION_RECIPIENTS_SCOPES.SINGLE,
      {
        communityId: communityObjectId,
      },
      learnerId
    ),
  ]);

  logger.info(
    `sendMembersLimitReachedNotification: ${JSON.stringify(result)}`
  );
};
