const EmailService = require('./email');
const LarkService = require('./lark');
const MobileService = require('./mobile');
const WhatsappService = require('./whatsapp');
const logger = require('../logger.service');
const CommunityModel = require('../../communitiesAPI/models/community.model');
const LearnerModel = require('../../models/learners.model');
const CommunityRoleModel = require('../../communitiesAPI/models/communityRole.model');
const { aclRoles } = require('../../communitiesAPI/constants');
const CommunityPlanOrderModel = require('../../models/plan/communityPlanOrder.model');

exports.sendEnrollmentNotification = async ({
  community,
  learner,
  purchaseTransaction = null,
  application = null,
  options = {
    bypassPendingApproval: false,
  },
}) => {
  const result = await Promise.allSettled([
    EmailService.enrollmentMail({
      community,
      learner,
      purchaseTransaction,
      application,
      options,
    }),
    MobileService.sendEnrollmentNotification({
      community,
      learner,
      options,
    }),
    WhatsappService.sendEnrollmentWhatsapp({
      community,
      learner,
      purchaseTransaction,
      options,
    }),
  ]);

  logger.info(`sendEnrollmentNotification: ${JSON.stringify(result)}`);
};

exports.sendRenewalFailureNotification = async ({
  purchaseTransactionObjectId,
  communityId,
  learnerObjectId,
  paymentMetadata,
}) => {
  const [community, learner] = await Promise.all([
    CommunityModel.findById(communityId).lean(),
    LearnerModel.findById(learnerObjectId).lean(),
  ]);
  const result = await Promise.all([
    EmailService.renewalFailureMail({
      purchaseTransactionObjectId,
      community,
      learner,
      paymentMetadata,
    }),
    MobileService.sendRenewalFailureNotification({ community, learner }),
    WhatsappService.sendRenewalFailureWhatsapp({
      community,
      learner,
      paymentMetadata,
    }),
  ]);

  logger.info(`sendRenewalFailureNotification: ${JSON.stringify(result)}`);
};

exports.sendCancelSubscriptionNotification = async ({
  subscription,
  community,
  learner,
}) => {
  const result = await Promise.allSettled([
    EmailService.cancelSubscriptionMail({
      subscription,
      community,
      learner,
    }),
  ]);

  logger.info(`sendRenewalFailureNotification: ${JSON.stringify(result)}`);
};

exports.sendAffiliateSaleNotification = async ({
  paidAmount,
  currency,
  transactionCreatedAt,
  commissionEarningAmount,
  commissionEarningCurrency,
  entityTitle,
  communityObjectId,
  learnerObjectId,
}) => {
  await Promise.allSettled([
    EmailService.affiliateSaleMail({
      paidAmount,
      currency,
      transactionCreatedAt,
      commissionEarningAmount,
      commissionEarningCurrency,
      entityTitle,
      communityObjectId,
      learnerObjectId,
    }),
  ]);
};

exports.sendSaleNotification = async ({
  paidAmount,
  currency,
  transactionCreatedAt,
  entityTitle,
  entityLink,
  communityObjectId,
  learnerObjectId,
}) => {
  await Promise.allSettled([
    EmailService.saleMail({
      paidAmount,
      currency,
      transactionCreatedAt,
      entityTitle,
      entityLink,
      communityObjectId,
      learnerObjectId,
    }),
  ]);
};

exports.sendPurchasePlanNotification = async ({
  planOrderObjectId,
  communityObjectId,
  learnerObjectId,
  amount,
  currency,
  nextBillingAmount,
  sendMail = true,
  sendLarkNoti = false,
}) => {
  const [community, learner] = await Promise.all([
    CommunityModel.findById(communityObjectId).lean(),
    LearnerModel.findById(learnerObjectId).lean(),
  ]);

  if (!learner) {
    return;
  }

  await Promise.allSettled([
    sendMail
      ? EmailService.purchasePlanMail({
          community,
          learner,
        })
      : null,
    LarkService.purchasePlanAlert({
      planOrderObjectId,
      community,
      learner,
      amount,
      currency,
      nextBillingAmount,
      sendLarkNoti,
    }),
  ]);
};

exports.sendCancelledPlanNotification = async ({
  planOrderObjectId,
  communityObjectId,
  learnerObjectId,
  subscriptionExpiryDate,
  failureReason,
  sendMail = true,
  sendLarkNoti = true,
}) => {
  const [community, learner, planOrder] = await Promise.all([
    CommunityModel.findById(communityObjectId).lean(),
    LearnerModel.findById(learnerObjectId).lean(),
    CommunityPlanOrderModel.findById(planOrderObjectId).lean(),
  ]);

  if (!learner) {
    return;
  }

  await Promise.allSettled([
    sendMail
      ? EmailService.cancelledPlanMail({
          community,
          learner,
          subscriptionExpiryDate,
          planOrder,
        })
      : null,
    sendLarkNoti
      ? LarkService.cancelPlanAlert({
          planOrder,
          community,
          learner,
          subscriptionExpiryDate,
          failureReason,
        })
      : null,
  ]);
};

exports.sendCancelledLarkNotification = async ({ planOrder }) => {
  const [community, learner] = await Promise.all([
    CommunityModel.findById(planOrder.communityObjectId).lean(),
    LearnerModel.findById(planOrder.learnerObjectId).lean(),
  ]);
  await LarkService.cancelPlanAlert({
    planOrder,
    community,
    learner,
    subscriptionExpiryDate: planOrder.cancelledAt,
    failureReason: 'change-tier',
  });
};

exports.sendRenewalFailurePlanNotification = async ({
  planOrderObjectId,
  communityObjectId,
  learnerObjectId,
  paymentMetadata,
}) => {
  const [planOrder, community, learner] = await Promise.all([
    CommunityPlanOrderModel.findById(planOrderObjectId).lean(),
    CommunityModel.findById(communityObjectId).lean(),
    LearnerModel.findById(learnerObjectId).lean(),
  ]);

  if (!learner) {
    return;
  }

  await Promise.allSettled([
    EmailService.renewalFailurePlanMail({
      planOrder,
      community,
      learner,
      paymentMetadata,
    }),
  ]);
};

exports.sendPlanReferralRewardNotification = async ({
  communityObjectId,
  refereeCommunityObjectId,
  rewardAmount,
  currency,
  recurringRewardAmount,
  isFirstBillingCycle,
}) => {
  const [community, refereeCommunity] = await Promise.all([
    CommunityModel.findById(communityObjectId).lean(),
    CommunityModel.findById(refereeCommunityObjectId).lean(),
  ]);

  if (!community || !refereeCommunity) {
    return;
  }

  const ownerRole = await CommunityRoleModel.findOne({
    communityObjectId,
    role: aclRoles.OWNER,
  }).lean();

  if (!ownerRole) {
    return;
  }

  const learner = await LearnerModel.findOne({
    email: ownerRole.email,
    isActive: true,
  }).lean();

  if (!learner) {
    return;
  }

  await Promise.allSettled([
    EmailService.planReferralRewardMail({
      community,
      refereeCommunity,
      learner,
      rewardAmount,
      currency,
      recurringRewardAmount,
      isFirstBillingCycle,
    }),
    MobileService.sendPlanReferralRewardNotification({
      refereeCommunity,
      community,
      learner,
      rewardAmount,
      currency,
      isFirstBillingCycle,
    }),
  ]);
};

exports.sendMembersLimitReachingNotification = async ({
  communityObjectId,
}) => {
  const community = await CommunityModel.findById(
    communityObjectId
  ).lean();

  if (!community) {
    return;
  }

  const ownerRole = await CommunityRoleModel.findOne({
    communityObjectId,
    role: aclRoles.OWNER,
  }).lean();

  if (!ownerRole) {
    return;
  }

  const learner = await LearnerModel.findOne({
    email: ownerRole.email,
    isActive: true,
  }).lean();

  if (!learner) {
    return;
  }

  await Promise.allSettled([
    MobileService.sendMembersLimitReachingNotification({
      community,
      learner,
    }),
  ]);
};

exports.sendMembersLimitReachedNotification = async ({
  communityObjectId,
}) => {
  const community = await CommunityModel.findById(
    communityObjectId
  ).lean();

  if (!community) {
    return;
  }

  const ownerRole = await CommunityRoleModel.findOne({
    communityObjectId,
    role: aclRoles.OWNER,
  }).lean();

  if (!ownerRole) {
    return;
  }

  const learner = await LearnerModel.findOne({
    email: ownerRole.email,
    isActive: true,
  }).lean();

  if (!learner) {
    return;
  }

  await Promise.allSettled([
    MobileService.sendMembersLimitReachedNotification({
      community,
      learner,
    }),
  ]);
};
