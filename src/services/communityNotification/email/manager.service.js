const CommonService = require('./common.service');
const NameUtils = require('../../../utils/name.util');

function retrieveMailType(
  community,
  options = { bypassPendingApproval: false }
) {
  const { request_approval: requestApproval } = community;
  const bypassPendingApproval = !!options.bypassPendingApproval;

  if (requestApproval && !bypassPendingApproval) {
    return 'COMMUNITY_MEMBER_APPLICATION_CONGRATS';
  }

  return 'COMMUNITY_MEMBER_SIGNUP_CONGRATS';
}

function retrieveQuestionsAndAnswers({ application, community }) {
  const { requireApplication, applicationConfigDataFields } = community;

  const questionsAndAnswers = [];

  if (requireApplication && applicationConfigDataFields && application) {
    applicationConfigDataFields.forEach((question) => {
      if (question.label && question.fieldName && !question.isDeleted) {
        let answer = '';
        const field = application[question.fieldName];
        const inputSectionKey = question.inputSectionKey;

        if (inputSectionKey === 'radio') {
          const options = question.options;
          // Radio button content is the field label
          answer = options.find((option) => option.value === field)?.label;
        } else if (typeof field === 'string') {
          answer = field;
        } else if (Array.isArray(field)) {
          answer = field.join('; ');
        } else if (typeof field === 'object') {
          const optionMap = {};
          for (const option of question.options) {
            optionMap[option.value] = option.label;
          }

          for (const [key, value] of Object.entries(field)) {
            if (value) {
              answer = `${optionMap[key]}; ${answer}`;
            }
          }
        }

        questionsAndAnswers.push({ question: question.label, answer });
      }
    });
  }

  return questionsAndAnswers;
}

exports.sendEnrollmentMail = async ({
  community,
  learner,
  purchaseTransaction,
  application,
  memberCountInfo,
  options,
}) => {
  const {
    code: communityCode,
    title,
    isWhatsappExperienceCommunity,
    isPaidCommunity,
  } = community;

  const mailType = retrieveMailType(community, options);

  const { emails, names } =
    await CommonService.retrieveCommunityManagerInfo(
      communityCode,
      mailType
    );

  if (emails.length === 0) {
    return;
  }

  const communityProfileImage =
    CommonService.retrieveCommunityProfileImage(community);

  const memberName = NameUtils.getName(
    learner.firstName,
    learner.lastName
  );

  let accessType = 'Free';
  let amountPaidWithCurrency;

  if (isPaidCommunity && purchaseTransaction) {
    accessType = 'Paid';

    const { local_amount: localAmount, local_currency: localCurrency } =
      purchaseTransaction;

    amountPaidWithCurrency = `${localCurrency} ${(
      localAmount / 100
    ).toFixed(2)}`;
  }

  const questionsAndAnswers = retrieveQuestionsAndAnswers({
    application,
    community,
  });

  const emailData = {
    community_profile_pic: communityProfileImage,
    community_name: title,
    sb_name: memberName,
    sb_email: learner.email,
    sb_phone_number: learner.phoneNumber,
    sb_amount_paid: amountPaidWithCurrency,
    is_whatsapp_experience_community: isWhatsappExperienceCommunity,
    type: accessType,
    no_of_members: memberCountInfo.summary.memberCount,
    no_of_pending_applications:
      memberCountInfo.statusBreakdown?.pendingApproval,
    questions_answers: questionsAndAnswers,
  };

  await CommonService.sendMailToQueue(
    mailType,
    communityCode,
    'All',
    emails,
    names,
    emailData
  );
};
