const CommonService = require('./common.service');
const NameUtils = require('../../../utils/name.util');

function retrieveMailType({ community, purchaseTransaction }) {
  const { isPaidCommunity } = community;

  if (isPaidCommunity && purchaseTransaction.amount > 0) {
    return 'ADMINPURCHASES';
  }
}

exports.sendEnrollmentMall = async ({
  community,
  purchaseTransaction,
  learner,
  memberCountInfo,
}) => {
  const { code: communityCode, title, link } = community;
  const {
    country,
    amount,
    currency,
    local_amount: localAmount,
    local_currency: localCurrency,
  } = purchaseTransaction;

  const mailType = retrieveMailType({ community, purchaseTransaction });

  // Not for free or 100% discount purchase
  if (!mailType) {
    return;
  }

  const name = NameUtils.getName(
    learner.firstName,
    learner.lastName,
    learner.email
  );

  const emailData = {
    type: 'Paid',
    community_name: title,
    community_code: communityCode,
    community_url: link,
    student_email: learner.email,
    student_name: name,
    country,
    amount: localAmount / 100,
    currency: localCurrency,
    default_currency_amount: amount / 100,
    default_currency: currency,
    no_of_members_enrolled: memberCountInfo.summary.memberCount,
    no_of_members_pending_approval:
      memberCountInfo.statusBreakdown.pendingApproval,

    // Remove total communtiy count calculation
    total_communities: 0,
    total_members: 0,
    total_current_members: 0,
    total_pending_approval: 0,
  };

  await CommonService.sendMailToQueue(
    mailType,
    communityCode,
    'All',
    [],
    [],
    emailData
  );
};
