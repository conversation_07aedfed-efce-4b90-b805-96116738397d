const CommunityTopupOrderModel = require('../../models/order/communityTopupOrder.model');
const MetaAdsCampaignModel = require('../../models/magicAudience/metaAdsCampaigns.model');
const { ParamError } = require('../../utils/error.util');
const { TOPUP_TYPE, PURCHASE_TYPE } = require('../../constants/common');
const { PAYMENT_STATUSES } = require('../../communitiesAPI/constants');
const { META_ADS_STATUS } = require('../magicAudience/constants');

exports.verifyPayment = async ({ signupId }) => {
  const topupOrder = await CommunityTopupOrderModel.findById(
    signupId
  ).lean();
  if (!topupOrder) {
    throw new ParamError(`Invalid topup order id: ${signupId}`);
  }

  let isValid = false;
  if (topupOrder.topupType === PURCHASE_TYPE.ADS_CAMPAIGN_TOPUP) {
    const campaign = await MetaAdsCampaignModel.findById(
      topupOrder.entityObjectId
    ).lean();
    isValid =
      topupOrder.status === PAYMENT_STATUSES.SUCCESS &&
      campaign?.status === META_ADS_STATUS.PENDING;
  }

  return { isValid };
};
