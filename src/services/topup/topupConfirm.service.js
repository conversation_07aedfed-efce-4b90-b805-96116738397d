const logger = require('../logger.service');
const PaymentBackendRpc = require('../../rpc/paymentBackend');
const { ToUserError } = require('../../utils/error.util');
const CommonService = require('../communitySignup/common');
const fraudService = require('../fraud');
const { GENERIC_ERROR } = require('../../constants/errorCode');
const { handleTopupTransaction } = require('./paymentProviders');

async function checkPurchaseForFraud({
  communityObjectId,
  entityId,
  entityType,
}) {
  const fraudEngine = new fraudService.FraudEngine({
    communityId: communityObjectId,
    eventName: 'purchase',
    entityType,
    entityId,
    data: {
      content: 'Purchase details',
      contentSource: entityType,
    },
    checksToPerform: [fraudService.COMMON_FRAUD_CHECKS.RISKY_PURCHASE],
    autoConsequencesToApply: [
      fraudService.COMMON_CONSEQUENCES.RESTRICT_CHECKOUT,
    ],
  });
  let fraudResult;
  try {
    fraudResult = await fraudEngine.performCheck();
  } catch (error) {
    logger.error('Error in fraud check:', error.message, error.stack);
  }
  if (fraudResult?.overallOutcome === fraudService.OUTCOMES.RESTRICT) {
    throw new ToUserError(
      'Checkout is currently unavailable for this community. Please contact the community admin for assistance.',
      GENERIC_ERROR.COMMUNITY_KYC_PENDING_ERROR
    );
  }
}

exports.confirmTopupOrder = async ({
  ip,
  userAgent,
  signupToken,
  metadata,
  confirmType,
  paymentMethodId,
}) => {
  logger.info(
    `confirm: ${JSON.stringify({
      ip,
      userAgent,
      signupToken,
      metadata,
      confirmType,
      paymentMethodId,
    })}`
  );

  const {
    communityObjectId,
    email,
    customerId,
    topupOrderId,
    paymentProvider,
    paymentMetadata,
    paymentStatus,
  } = CommonService.SignupTokenService.decodeSignupToken(signupToken);

  // to move to worker pool
  checkPurchaseForFraud({
    entityId: topupOrderId,
    entityType: confirmType,
    communityObjectId,
  });

  const paymentBackendRpc = new PaymentBackendRpc();
  await paymentBackendRpc.init();

  const result = await handleTopupTransaction({
    email,
    metadata,
    customerId,
    topupOrderId,
    paymentProvider,
    paymentMethodId,
    paymentMetadata,
    paymentStatus,
    ip,
  });
  const signupId = topupOrderId;

  return {
    ...result,
    signupId,
    confirmType,
  };
};
