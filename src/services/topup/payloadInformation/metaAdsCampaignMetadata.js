const ObjectId = require('mongoose').Types.ObjectId;
const MetaAdsSetModel = require('../../../models/magicAudience/metaAdsSets.model');
const MetaAdsCampaignModel = require('../../../models/magicAudience/metaAdsCampaigns.model');
const CountryService = require('../../communitySignup/signup/payloadInformation/country.service');
const CommonService = require('../../communitySignup/common');
const LearnerService = require('../../communitySignup/signup/payloadInformation/learner.service');
const {
  DEFAULT_CURRENCY,
  PURCHASE_TYPE,
  CONFIG_TYPES,
} = require('../../../constants/common');
const CommunityModel = require('../../../communitiesAPI/models/community.model');
const {
  normalizeAndRoundAmountByCurrency,
} = require('../../../utils/currency.util');
const { META_ADS_STATUS } = require('../../magicAudience/constants');
const { ParamError, InternalError } = require('../../../utils/error.util');
const { getConfigByTypeFromCache } = require('../../config.service');
const {
  getDeductibleWalletBalance,
} = require('../../wallet/adsCampaign/getAdsBalance.service');

const getPlatformFee = async () => {
  const config = await getConfigByTypeFromCache(
    CONFIG_TYPES.ADS_CAMPAIGN_FEE_STRUCTURE
  );

  const currentDate = new Date();
  const currentDateString = currentDate.toISOString();

  const configValues = config?.paymentStructureValues;

  const effectiveFeeConfig = configValues?.find((payoutFeeConfig) => {
    const { effectiveTimeStart, effectiveTimeEnd } = payoutFeeConfig;

    return (
      currentDateString >= effectiveTimeStart.toISOString() &&
      currentDateString < effectiveTimeEnd.toISOString()
    );
  });
  if (effectiveFeeConfig === null) {
    throw new InternalError('Missing payment fee structure config');
  }

  return effectiveFeeConfig.platformFee;
};

const retrieveMetaAdsCampaign = async ({
  item,
  community,
  countryInfo,
  paymentBackendRpc,
}) => {
  const { entityObjectId } = item;

  const metaAdsCampaign = await MetaAdsCampaignModel.findOne({
    _id: entityObjectId,
    status: META_ADS_STATUS.DRAFT,
  }).lean();

  const metaAdsCampaignSetList = await MetaAdsSetModel.aggregate([
    {
      $match:
        /**
         * query: The query in MQL.
         */
        {
          campaignId: new ObjectId(entityObjectId),
          status: META_ADS_STATUS.DRAFT,
        },
    },
    {
      $group:
        /**
         * _id: The id of the group.
         * fieldN: The first field name.
         */
        {
          _id: '$campaignId',
          totalBudgetInUSD: {
            $sum: '$totalBudgetInUSD',
          },
        },
    },
  ]);

  if (
    !metaAdsCampaign ||
    !metaAdsCampaignSetList ||
    metaAdsCampaignSetList.length !== 1
  ) {
    throw new ParamError('Invalid campaign');
  }

  const metaAdsCampaignSet = metaAdsCampaignSetList[0];
  if (metaAdsCampaignSet.totalBudgetInUSD <= 0) {
    throw new ParamError('Invalid campaign budget');
  }

  // Get fees
  const feeRate = await getPlatformFee(); // 10% of campaign budget
  const totalFeeInUsd = normalizeAndRoundAmountByCurrency(
    metaAdsCampaignSet.totalBudgetInUSD * feeRate
  );

  // Get deductible wallet balance
  const totalDeductibleWalletBalanceInUsd =
    await getDeductibleWalletBalance({
      communityId: community._id,
    });
  // If totalDeductibleWalletBalanceInUsd is more than the current campaign budget + fee
  // We will use metaAdsCampaignSet.totalBudgetInUSD + totalFeeInUsd as deductibleWalletBalanceInUsd
  // for example:
  // totalBudgetInUSD = 10 usd, fee = 1 usd
  // totalDeductibleWalletBalanceInUsd = 15 usd
  // deductibleWalletBalanceInUsd = 11 usd
  let requirePayment = true;
  const totalCharge = metaAdsCampaignSet.totalBudgetInUSD + totalFeeInUsd;
  let deductibleWalletBalanceInUsd = totalDeductibleWalletBalanceInUsd;
  if (totalDeductibleWalletBalanceInUsd >= totalCharge) {
    deductibleWalletBalanceInUsd = totalCharge;
    requirePayment = false;
  }

  // Get localized amount
  // Default to USD if no localize price
  const localCurrency = countryInfo?.localisePrice
    ? countryInfo.currencyCode
    : DEFAULT_CURRENCY;
  const conversionRateData = await paymentBackendRpc.getConversionRate(
    DEFAULT_CURRENCY,
    localCurrency
  );
  const conversionRateBeforeMarkup = conversionRateData.conversionRate;
  const conversionRate = conversionRateBeforeMarkup * 1.03;

  const amountInLocalCurrency = normalizeAndRoundAmountByCurrency(
    metaAdsCampaignSet.totalBudgetInUSD * conversionRate,
    localCurrency
  );
  const totalFeeInLocalCurrency = normalizeAndRoundAmountByCurrency(
    totalFeeInUsd * conversionRate,
    localCurrency
  );

  let deductibleWalletBalanceInLocalCurrency =
    normalizeAndRoundAmountByCurrency(
      deductibleWalletBalanceInUsd * conversionRate,
      localCurrency
    );
  // To prevent the friction of currency conversion
  // if this topup is not required payment, we will use
  // amountInLocalCurrency + totalFeeInLocalCurrency as deductibleWalletBalance
  if (!requirePayment) {
    deductibleWalletBalanceInLocalCurrency =
      amountInLocalCurrency + totalFeeInLocalCurrency;
  }

  const checkoutAmountInUsd =
    metaAdsCampaignSet.totalBudgetInUSD +
    totalFeeInUsd -
    deductibleWalletBalanceInUsd;
  const checkoutAmountInLocalCurrency =
    amountInLocalCurrency +
    totalFeeInLocalCurrency -
    deductibleWalletBalanceInLocalCurrency;

  const priceDetails = {
    amountInUsd: {
      requestedAmount: metaAdsCampaignSet.totalBudgetInUSD,
      totalFee: totalFeeInUsd,
      feeDetails: { nasIOFee: totalFeeInUsd },
      deductibleWalletBalance: deductibleWalletBalanceInUsd,
      checkoutAmount: checkoutAmountInUsd,
    },
    amountInLocalCurrency: {
      requestedAmount: amountInLocalCurrency,
      totalFee: totalFeeInLocalCurrency,
      feeDetails: { nasIOFee: totalFeeInLocalCurrency },
      deductibleWalletBalance: deductibleWalletBalanceInLocalCurrency,
      checkoutAmount: checkoutAmountInLocalCurrency,
    },
    conversionRateBeforeMarkup,
    conversionRate,
    localCurrency,
  };

  return {
    ...metaAdsCampaign,
    title: metaAdsCampaign.campaignName,
    priceDetails,
    metadata: {
      requirePayment,
    },
    topupType: item.topupType,
    purchaseType: PURCHASE_TYPE.ADS_CAMPAIGN_TOPUP,
  };
};

exports.retrieveItemsWithMetadata = async ({
  communityId,
  items,
  ip,
  learnerObjectId,
  signupToken,
  paymentBackendRpc,
}) => {
  const [community, countryInfo, learner] = await Promise.all([
    await CommunityModel.findById(communityId).lean(),
    CountryService.retrieveCountryInfo(ip),
    LearnerService.retrieveLearnerInfo({ learnerObjectId }),
  ]);

  const itemsWithMetadata = await Promise.all(
    items.map(async (item) => {
      const { topupType } = item;

      if (topupType === PURCHASE_TYPE.ADS_CAMPAIGN_TOPUP) {
        const itemWithMetadata = await retrieveMetaAdsCampaign({
          item,
          community,
          countryInfo,
          paymentBackendRpc,
        });
        return itemWithMetadata;
      }
    })
  );

  const decodedSignupToken =
    CommonService.SignupTokenService.decodeSignupToken(signupToken);

  return {
    community,
    items: itemsWithMetadata,
    learner,
    countryInfo,
    decodedSignupToken,
  };
};
