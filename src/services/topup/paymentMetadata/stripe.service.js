exports.generateTopupPaymentMetadata = ({
  topupOrder,
  entity,
  community,
  ip,
  userAgent,
}) => {
  const { learnerObjectId } = topupOrder;
  const country = topupOrder.country.name;

  const { code: communityCode } = community;

  const description = `Payment for ${entity.title}`;

  const metadata = {
    entitySignupId: topupOrder._id,
    topupType: topupOrder.topupType,
    purchaseType: entity.purchaseType,
    description,
    communityCode,
    communityObjectId: community._id,
    client_user_agent: userAgent,
    client_ip_address: ip,
    country,
    learnerId: learnerObjectId,
  };

  return { description, metadata };
};
