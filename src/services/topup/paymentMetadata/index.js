const { PAYMENT_PROVIDER } = require('../../../constants/common');
const StripeService = require('./stripe.service');
const { ParamError } = require('../../../utils/error.util');

exports.generateTopupPaymentMetadata = ({
  community,
  topupOrder,
  entity,
  ip,
  userAgent,
  paymentProvider,
}) => {
  let paymentMetadata = {};

  if (
    [PAYMENT_PROVIDER.STRIPE, PAYMENT_PROVIDER.STRIPE_US].includes(
      paymentProvider
    )
  ) {
    paymentMetadata = StripeService.generateTopupPaymentMetadata({
      community,
      topupOrder,
      entity,
      ip,
      userAgent,
    });
  }

  return paymentMetadata;
};
