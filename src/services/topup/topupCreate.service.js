const jwt = require('jsonwebtoken');
const logger = require('../logger.service');
const PaymentBackendRpc = require('../../rpc/paymentBackend');
const mongooseUtil = require('../../utils/mongodb.util');
const { CHECKOUT_TYPE, PURCHASE_TYPE } = require('../../constants/common');
const { JWT_SECRET_KEY } = require('../../config');
const CustomerService = require('../communitySignup/signup/processTransaction/customer.service');
const { ParamError, ToUserError } = require('../../utils/error.util');
const CommunityService = require('../communitySignup/signup/validation/community.service');
const PaymentProviderService = require('../communitySignup/signup/validation/paymentProvider.service');
const metaAdsCampaignHandler = require('./topupOrderHandler/metaAdsCampaignHandler');
const {
  retrieveItemsWithMetadata,
} = require('./payloadInformation/metaAdsCampaignMetadata');
const CommunityTopupOrderModel = require('../../models/order/communityTopupOrder.model');
const { PAYMENT_STATUSES } = require('../../communitiesAPI/constants');
const { GENERIC_ERROR } = require('../../constants/errorCode');

const processTransactionWithRetry = async (params) => {
  return mongooseUtil.withTransactionRetry(async (session) => {
    const {
      ip,
      userAgent,
      community,
      learner,
      countryInfo,
      timezone,
      requestor,
      trackingData: initialTrackingData,
      items,
      decodedSignupToken,
      paymentProvider,
      memberInfo,
    } = params;

    const trackingData = { ...(initialTrackingData ?? {}), ip, userAgent };

    const metaAdsCampaign = items.find(
      (item) => item.topupType === PURCHASE_TYPE.ADS_CAMPAIGN_TOPUP
    );

    let purchasedStatus;
    let priceDetails = {};
    const paymentStatus = { requirePayment: false };

    if (metaAdsCampaign) {
      purchasedStatus =
        await metaAdsCampaignHandler.handleMetaAdsTopupOrder({
          ip,
          userAgent,
          community,
          learner,
          countryInfo,
          timezone,
          requestor,
          trackingData,
          metaAdsCampaign,
          decodedSignupToken,
          paymentProvider,
          memberInfo,
          session,
        });

      priceDetails = purchasedStatus.priceDetails;
      delete purchasedStatus.priceDetails;
      paymentStatus.requirePayment = purchasedStatus.requirePayment;
      paymentStatus.topupType = metaAdsCampaign.topupType;
      paymentStatus.signupId = purchasedStatus.signupId;
      paymentStatus.currency = purchasedStatus.currency;
      paymentStatus.paymentMetadata = purchasedStatus.paymentMetadata;
    }

    return {
      purchasedStatus,
      paymentStatus,
      priceDetails,
    };
  });
};

const generateSignupToken = ({
  community,
  learner,
  customerId,
  topupOrderId,
  paymentProvider,
  paymentMetadata,
  entityType,
  paymentStatus,
}) => {
  const { email } = learner;
  const { _id: communityObjectId } = community;

  const token = {
    communityObjectId,
    email,
    topupOrderId,
    customerId,
    paymentProvider,
    paymentMetadata,
    entityType,
    paymentStatus,
  };

  const signedToken = jwt.sign(token, JWT_SECRET_KEY, {
    expiresIn: '15m',
  });

  return signedToken;
};

const processTopupOrder = async ({
  ip,
  userAgent,
  community,
  learner,
  countryInfo,
  timezone,
  requestor,
  trackingData,
  items,
  paymentProvider,
  memberInfo,
  paymentBackendRpc,
  decodedSignupToken,
}) => {
  try {
    const transactionData = await processTransactionWithRetry({
      ip,
      userAgent,
      community,
      learner,
      countryInfo,
      timezone,
      requestor,
      trackingData,
      items,
      paymentProvider,
      memberInfo,
      paymentBackendRpc,
      decodedSignupToken,
    });

    // Perform non-transactional operations here
    const { purchasedStatus, paymentStatus, priceDetails } =
      transactionData;

    let customerInfo = {};
    if (paymentProvider) {
      customerInfo = await CustomerService.retrieveCustomerInfo({
        paymentStatus,
        learner,
        paymentProvider,
        paymentBackendRpc,
      });
    }

    const newSignupToken = generateSignupToken({
      community,
      learner,
      paymentProvider,
      topupOrderId: purchasedStatus.signupId,
      customerId: customerInfo?.customerId,
      paymentMetadata: paymentStatus.paymentMetadata,
      entityType: paymentStatus.entityType,
      paymentStatus,
    });

    const savedPaymentMethods = customerInfo?.paymentMethods ?? [];

    const transactionStatus = {
      signupToken: newSignupToken,
      paymentStatus,
      purchasedStatus,
      priceDetails,
      customerId: customerInfo?.customerId,
      savedPaymentMethods,
      upiPaymentMethods: customerInfo?.upiPaymentMethods,
    };

    logger.info(
      `processTransaction: transactionStatus ${JSON.stringify(
        transactionStatus
      )}`
    );
    return transactionStatus;
  } catch (error) {
    logger.error(
      `processTransaction: error: ${error.message}, stack: ${error.stack}`
    );
    throw error;
  }
};

const validatePendingTopupOrderForSameEntity = async ({
  items,
  learnerObjectId,
}) => {
  await Promise.all(
    items.map(async (item) => {
      const exists = await CommunityTopupOrderModel.exists({
        entityObjectId: item._id,
        learnerObjectId,
        status: {
          $in: [PAYMENT_STATUSES.PENDING, PAYMENT_STATUSES.SUCCESS],
        },
      });

      if (exists) {
        throw new ToUserError(
          'Payment is still processing. Please refresh the page in 5 mins',
          GENERIC_ERROR.PAYMENT_IN_PROCESSING_ERROR
        );
      }
    })
  );
};

const validatePayload = async (payloadInformation) => {
  const { community, items, paymentProvider, learnerObjectId } =
    payloadInformation;

  if (items.length === 0 || items.length > 1) {
    throw new ParamError(
      'Items payload cannot be empty or more than 1 items'
    );
  }

  CommunityService.validateCommunity(community, items);

  await Promise.all([
    PaymentProviderService.validatePaymentProviderByCheckoutType({
      checkoutType: CHECKOUT_TYPE.TOPUP,
      checkoutCurrency: '',
      paymentProvider,
    }),
    validatePendingTopupOrderForSameEntity({ items, learnerObjectId }),
  ]);
};

exports.createTopupOrder = async ({
  ip,
  userAgent,
  requestLanguagePreference,
  email,
  learnerObjectId,
  signupToken,
  communityId,
  timezone,
  requestor,
  paymentProvider,
  trackingData,
  items,
}) => {
  logger.info(
    `createTopupOrder: ${JSON.stringify({
      ip,
      userAgent,
      requestLanguagePreference,
      email,
      learnerObjectId,
      signupToken,
      communityId,
      timezone,
      requestor,
      paymentProvider,
      trackingData,
      items,
    })}`
  );

  const paymentBackendRpc = new PaymentBackendRpc();
  await paymentBackendRpc.init();

  // Fetch all related info of meta campaign and localized pricing
  const payloadInformation = await retrieveItemsWithMetadata({
    communityId,
    items,
    ip,
    learnerObjectId,
    signupToken,
    paymentBackendRpc,
  });

  await validatePayload({
    paymentProvider,
    learnerObjectId,
    ...payloadInformation,
  });

  // Create topup order and create the metadata that we plan to send to payment gateway
  const transactionStatus = await processTopupOrder({
    ip,
    userAgent,
    timezone,
    requestor,
    trackingData,
    paymentBackendRpc,
    paymentProvider,
    ...payloadInformation,
  });

  return transactionStatus;
};
