const { PAYMENT_STATUSES } = require('../../../communitiesAPI/constants');
const {
  DEFAULT_CURRENCY,
  PAYMENT_PROVIDER,
  TRANSACTION_TYPE,
  PURCHASE_TYPE,
} = require('../../../constants/common');
const service = require('../../transaction');
const {
  topupOrderPaymentUpdates,
} = require('../topupPaymentUpdate.service');
const PrimaryMongooseConnection = require('../../../rpc/primaryMongooseConnection');

const createCampaignSpendTransaction = async ({
  topupOrder,
  email,
  session,
}) => {
  const {
    priceDetailsInUsd: priceDetails,
    priceDetailsInLocalCurrency,
    entityObjectId,
  } = topupOrder;

  const totalCampaignAmount =
    priceDetails.requestedAmount + priceDetails.nasIOFee;

  const amountBreakdownInUsd = {
    expectedPaidAmount: priceDetails.requestedAmount,
    itemPrice: priceDetails.requestedAmount,
    discountedItemPrice: priceDetails.requestedAmount,
    exchangeRate: 1,
    originalAmount: priceDetails.requestedAmount,
    discountAmount: 0,
    paidAmount: priceDetails.requestedAmount,
    fee: {
      adsCampaignProcessFee: priceDetails.nasIOFee,
    },
    calculatedRawFee: {},
    revenueShareAmount: 0,
    totalFee: priceDetails.nasIOFee,
    netAmount: totalCampaignAmount,
  };

  const spendRevenueTransaction = {
    purchasedId: topupOrder._id,
    transactionType: TRANSACTION_TYPE.OUTBOUND,
    purchaseType: PURCHASE_TYPE.ADS_CAMPAIGN_SPEND,
    originalAmount: totalCampaignAmount,
    originalCurrency: DEFAULT_CURRENCY,
    originalPaidAmount: totalCampaignAmount,
    originalDiscountAmount: 0,
    rawFee: { currency: DEFAULT_CURRENCY },
    feeInUsd: {},
    finalFeeInUsd: {},
    totalFeeInUsd: 0,
    netAmountInUsd: totalCampaignAmount,
    amountInUsd: totalCampaignAmount,
    paymentMethod: 'NA',
    paymentBrand: 'NA',
    paymentProvider: PAYMENT_PROVIDER.NAS,
    communityObjectId: topupOrder.communityObjectId,
    learnerObjectId: topupOrder.learnerObjectId, // put community owner
    email, // put community owner
    transactionCreatedAt: new Date(),
    transactionReferenceId: `${topupOrder._id}_spend`,
    revenueShareAmountInUsd: 0,
    revenueSharePercentage: 0,
    amountBreakdownInUsd,
    amountBreakdownInLocalCurrency: {
      ...amountBreakdownInUsd,
      currency: DEFAULT_CURRENCY,
    },
    entityObjectId,
    metadata: {
      priceDetails,
      priceDetailsInLocalCurrency,
    },
  };

  const transactionResult =
    await service.TransactionCreateService.createTransaction({
      ...spendRevenueTransaction,
      session,
    });
};

exports.handleAutoEnrollment = async ({
  email,
  requirePayment,
  topupOrder,
}) => {
  if (requirePayment) {
    return;
  }

  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  try {
    // create spend transaction
    await createCampaignSpendTransaction({
      topupOrder,
      email,
      session,
    });

    // update campaign status
    await topupOrderPaymentUpdates({
      topupOrderId: topupOrder._id,
      topupOrder,
      status: PAYMENT_STATUSES.SUCCESS,
      eventTime: new Date(),
      session,
    });
    await session.commitTransaction();

    const paymentStatus = {
      purchased: true,
      requirePayment: false,
      currency: topupOrder.localCurrency,
      signupId: topupOrder._id,
      paymentMetadata: {},
      priceDetails: topupOrder.priceDetails,
    };

    return paymentStatus;
  } catch (err) {
    await session.abortTransaction();
    throw err;
  } finally {
    await session.endSession();
  }
};
