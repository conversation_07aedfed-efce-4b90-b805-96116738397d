const httpContext = require('express-http-context');
const CommunityTopupOrder = require('../../../models/order/communityTopupOrder.model');
const { ParamError } = require('../../../utils/error.util');
const PaymentMetadataService = require('../paymentMetadata');
const { PAYMENT_STATUSES } = require('../../../communitiesAPI/constants');

const createOrUpdateMetaAdsTopupOrder = async ({
  community,
  learner,
  countryInfo,
  timezone,
  requestor,
  trackingData,
  metaAdsCampaign,
  decodedSignupToken,
  paymentProvider,
  session,
}) => {
  const { priceDetails } = metaAdsCampaign;

  const paymentDetails = {
    status: PAYMENT_STATUSES.INCOMPLETE,
    paymentProvider,
    latestUpdatedTime: new Date(),
    requestId: httpContext.get('reqId'),
  };

  let topupOrder;
  if (decodedSignupToken?.topupOrderId) {
    // In the second api call, FE will provide the payment provider in the request
    // So we only need to update payment provider
    topupOrder = await CommunityTopupOrder.findByIdAndUpdate(
      decodedSignupToken.topupOrderId,
      { paymentDetails }
    ).lean();
    // For second api call of topup api, we will use the price that stored in topup order
    // So that the price that user see will be consistent
    topupOrder.priceDetails = {
      amountInUsd: {
        ...topupOrder.priceDetailsInUsd,
        totalFee: topupOrder.priceDetailsInUsd.nasIOFee,
      },
      amountInLocalCurrency: {
        ...topupOrder.priceDetailsInLocalCurrency,
        totalFee: topupOrder.priceDetailsInLocalCurrency.nasIOFee,
      },
      localCurrency: topupOrder.localCurrency,
    };
  } else {
    const generatedTopupOrder = {
      communityObjectId: community._id,
      learnerObjectId: learner._id,
      paymentDetails,
      status: PAYMENT_STATUSES.INCOMPLETE,
      topupType: metaAdsCampaign.topupType,
      entityObjectId: metaAdsCampaign._id,
      country: {
        code: countryInfo.countryCode,
        name: countryInfo.country,
      },
      amountInUsd: priceDetails.amountInUsd.checkoutAmount,
      amountInLocalCurrency:
        priceDetails.amountInLocalCurrency.checkoutAmount,
      localCurrency: priceDetails.localCurrency,
      priceDetailsInUsd: {
        requestedAmount: priceDetails.amountInUsd.requestedAmount,
        nasIOFee: priceDetails.amountInUsd.feeDetails.nasIOFee,
        deductibleWalletBalance:
          priceDetails.amountInUsd.deductibleWalletBalance,
        checkoutAmount: priceDetails.amountInUsd.checkoutAmount,
      },
      priceDetailsInLocalCurrency: {
        requestedAmount:
          priceDetails.amountInLocalCurrency.requestedAmount,
        nasIOFee: priceDetails.amountInLocalCurrency.feeDetails.nasIOFee,
        deductibleWalletBalance:
          priceDetails.amountInLocalCurrency.deductibleWalletBalance,
        checkoutAmount: priceDetails.amountInLocalCurrency.checkoutAmount,
        conversionRateBeforeMarkup:
          priceDetails.conversionRateBeforeMarkup,
        conversionRate: priceDetails.conversionRate,
      },
    };

    topupOrder = (
      await CommunityTopupOrder.create([generatedTopupOrder], {
        session,
      })
    )[0].toObject();
    topupOrder.priceDetails = priceDetails;
  }
  return topupOrder;
};

exports.handleMetaAdsTopupOrder = async ({
  ip,
  userAgent,
  community,
  learner,
  countryInfo,
  timezone,
  requestor,
  trackingData,
  metaAdsCampaign,
  decodedSignupToken,
  paymentProvider,
  memberInfo,
  session,
}) => {
  const { requirePayment } = metaAdsCampaign.metadata;

  const topupOrder = await createOrUpdateMetaAdsTopupOrder({
    community,
    learner,
    countryInfo,
    timezone,
    requestor,
    trackingData,
    metaAdsCampaign,
    decodedSignupToken,
    paymentProvider,
    session,
  });

  const { _id: signupId, localCurrency } = topupOrder;

  const purchased = false;

  const paymentMetadata =
    PaymentMetadataService.generateTopupPaymentMetadata({
      community,
      topupOrder,
      entity: metaAdsCampaign,
      ip,
      userAgent,
      paymentProvider,
    });

  const paymentStatus = {
    purchased,
    requirePayment,
    currency: localCurrency,
    signupId,
    paymentMetadata,
    priceDetails: topupOrder.priceDetails,
  };

  return paymentStatus;
};
