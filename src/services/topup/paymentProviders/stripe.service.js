const {
  PAYMENT_PROVIDER,
  CURRENCY_WITH_NON_DECIMAL_POINTS,
} = require('../../../constants/common');
const { ParamError } = require('../../../utils/error.util');
const PaymentBackendRpc = require('../../../rpc/paymentBackend');

exports.createPaymentIntent = async ({
  email,
  topupOrder,
  paymentMethodId,
  customerId,
  paymentMetadata,
  paymentProvider = PAYMENT_PROVIDER.STRIPE,
  confirmMetadata,
}) => {
  if (!paymentMethodId) {
    throw new ParamError('Required payment method id');
  }

  const paymentBackendRpc = new PaymentBackendRpc();
  await paymentBackendRpc.init();

  const { amountInLocalCurrency: localAmount } = topupOrder;
  const { localCurrency } = topupOrder;

  let normalizeAmountByCurrency = localAmount;

  if (CURRENCY_WITH_NON_DECIMAL_POINTS.includes(localCurrency)) {
    normalizeAmountByCurrency /= 100;
  }

  const { metadata, description } = paymentMetadata;

  const result = await paymentBackendRpc.createStripePaymentIntent(
    email,
    customerId,
    paymentMethodId,
    normalizeAmountByCurrency,
    localCurrency,
    description,
    metadata,
    paymentProvider
  );

  let paymentId;
  const match = result?.clientSecret?.match(/(pi_[a-zA-Z0-9]+)/);
  if (match) {
    const paymentIntentId = match[1];
    paymentId = paymentIntentId;
  }
  return {
    ...result,
    paymentId,
  };
};
