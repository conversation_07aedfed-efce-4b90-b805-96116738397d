/* eslint-disable no-unused-expressions */
const ObjectId = require('mongoose').Types.ObjectId;

const Community = require('../../communitiesAPI/models/community.model');
const EarningAnalytics = require('../../models/earningAnalytics.model');
const MagicAudienceApplication = require('../../models/advertisement/magicAudienceApplication.model');
const memberService = require('../membership/count.service');
const larkService = require('../../communitiesAPI/services/common/lark.service');
const { getConfigByTypeFromCacheSync } = require('../config.service');

const logger = require('../logger.service');

const {
  SUBSCRIPTION_TYPE,
  CONFIG_TYPES,
} = require('../../constants/common');

const updateOverallEarnings = (overallEarnings, earning) => {
  // eslint-disable-next-line no-param-reassign
  overallEarnings.revenueInLocalCurrency +=
    earning.revenueInLocalCurrency ?? 0;
  // eslint-disable-next-line no-param-reassign
  overallEarnings.revenueInUsd += earning.revenueInUsd ?? 0;
  // eslint-disable-next-line no-param-reassign
  overallEarnings.quantity += earning.quantity ?? 0;
};

async function getTotalEarningStats(community) {
  const earningAnalytics = await EarningAnalytics.find(
    {
      communityObjectId: new ObjectId(community._id),
    },
    {
      actionEventObjectIds: 0,
    }
  ).lean();

  const overallEarnings = {
    baseCurrency: community.baseCurrency,
    quantity: 0,
    revenueInUsd: 0,
    revenueInLocalCurrency: 0,
  };

  earningAnalytics.forEach((earningAnalytic) => {
    const {
      subscriptions,
      folders,
      events,
      campaignRewards = [],
      sessions = [],
      challenges = [],
    } = earningAnalytic;

    subscriptions.forEach((earning) => {
      if (
        earning.type === SUBSCRIPTION_TYPE.NEW_SUBSCRIPTION ||
        earning.type === SUBSCRIPTION_TYPE.EXISTING_SUBSCRIPTION
      ) {
        updateOverallEarnings(overallEarnings, earning);
      }
    });
    folders.forEach((earning) =>
      updateOverallEarnings(overallEarnings, earning)
    );
    events.forEach((earning) =>
      updateOverallEarnings(overallEarnings, earning)
    );
    campaignRewards?.forEach((earning) =>
      updateOverallEarnings(overallEarnings, earning)
    );
    sessions?.forEach((earning) =>
      updateOverallEarnings(overallEarnings, earning)
    );
    challenges?.forEach((earning) =>
      updateOverallEarnings(overallEarnings, earning)
    );
  });

  return overallEarnings;
}

async function sendApplicationToLark({ header, contentList }) {
  const lpbeConfig = getConfigByTypeFromCacheSync(
    CONFIG_TYPES.LEARN_PORTAL_BACKEND_ENV_CONFIG_TYPE
  );
  const configName = 'PAID_ADS_LARK_WEBHOOK';
  const webhookLink = lpbeConfig?.envVarData?.[configName];

  const template = larkService.generateMessageTemplate(header);
  const content = contentList.map((item) =>
    larkService.generateTextPayload(item.title, item.value)
  );

  const payload = await larkService.insertContentToTemplate(
    template,
    content
  );
  const result = await larkService.sendPushNotificationToLark(
    webhookLink,
    payload
  );

  return result;
}

exports.createMagicAudienceApplication = async (
  communityObjectId,
  learner,
  params,
  email
) => {
  const community = await Community.findById(communityObjectId).lean();

  if (!community) {
    throw Error('Community not found');
  }
  const [memberCountInfo, earningStats] = await Promise.all([
    memberService.countCommunityMembers({
      communityCode: community.code,
    }),
    getTotalEarningStats(community),
  ]);

  const toSave = {
    communityObjectId,
    learnerObjectId: learner?._id,
    totalMembers: memberCountInfo.summary.memberCount,
    totalSales: earningStats.quantity,
    totalGmv: earningStats.revenueInLocalCurrency ?? 0,
    totalGmvInUsd: earningStats.revenueInUsd ?? 0,
    gmvCurrency: earningStats.baseCurrency,
    communityCountryCreatedIn: community.countryCreatedIn,
    ...params,
  };

  const application = await MagicAudienceApplication.create(toSave);
  try {
    const appliedProductTypes = new Set();
    for (const product of params.entities) {
      appliedProductTypes.add(product.type);
    }
    // eslint-disable-next-line no-unused-vars
    const result = await sendApplicationToLark({
      header: 'New Magic Audience Application',
      contentList: [
        { title: 'Community Code: ', value: community.code },
        { title: 'Community Name: ', value: community.name },
        { title: 'CM: ', value: email },
        { title: 'Goal: ', value: params.goal },
        { title: 'Budget: ', value: params.budget },
        {
          title: 'Product Types: ',
          value: [...appliedProductTypes].join(', '),
        },
        { title: 'Social Media Link: ', value: params.socialMediaLink },
        { title: 'Community Currency: ', value: toSave.gmvCurrency },
        {
          title: 'Total Members: ',
          value: `${memberCountInfo.summary.memberCount}`,
        },
        { title: 'Total Sales: ', value: `${earningStats.quantity}` },
        {
          title: 'Total GMV: ',
          value: `${earningStats.revenueInLocalCurrency ?? 0} ${
            earningStats.baseCurrency
          }`,
        },
        {
          title: 'Total GMV in USD: ',
          value: `${earningStats.revenueInUsd ?? 0} USD`,
        },
        {
          title: 'Country Created In: ',
          value: community.countryCreatedIn,
        },
      ],
    });
  } catch (error) {
    logger.error('Error sending application to Lark', error, error.stack);
  }
  return application.toObject();
};
