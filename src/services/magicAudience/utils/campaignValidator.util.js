const { PROGRAM_CHALLENGE_TYPE } = require('../../program/constants');
const { META_ADS_ENTITY_TYPE } = require('../constants');

const TWO_DAYS_MS = 2 * 24 * 60 * 60 * 1000;

const checkIfEntityIsEligibleToRunACampaign = ({ entity, entityType }) => {
  if (
    entityType !== META_ADS_ENTITY_TYPE.EVENT &&
    entityType !== META_ADS_ENTITY_TYPE.CHALLENGE
  ) {
    return true;
  }

  // ---------- EVENT & CHALLENGE share the same date logic ----------
  const now = Date.now();
  const start = new Date(entity.startTime).getTime();
  const end = new Date(entity.endTime).getTime();

  const tooCloseToStart = start < now + TWO_DAYS_MS;
  const alreadyEnded = end < now;

  /* ------------ Events ------------ */
  if (entityType === META_ADS_ENTITY_TYPE.EVENT) {
    if (alreadyEnded)
      throw new Error('Event has already ended and cannot run a campaign');

    if (tooCloseToStart)
      throw new Error(
        'Event starts within 2 days and cannot run a campaign'
      );

    return true;
  }

  /* ------------ Challenges ------------ */
  if (entity.challengeType === PROGRAM_CHALLENGE_TYPE.ALWAYS_ON) {
    return true; // Always-on challenges are exempt
  }

  if (alreadyEnded)
    throw new Error(
      'Challenge has already ended and cannot run a campaign'
    );

  if (tooCloseToStart)
    throw new Error(
      'Challenge starts within 2 days and cannot run a campaign'
    );

  return true; // Passed all checks
};
module.exports = {
  checkIfEntityIsEligibleToRunACampaign,
};
