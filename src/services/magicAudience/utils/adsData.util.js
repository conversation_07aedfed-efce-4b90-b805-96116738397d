const {
  COMMUNITY_EVENT_ACCESS_TYPES,
} = require('../../../communitiesAPI/constants');
const { NAS_IO_FRONTEND_URL } = require('../../../config');
const {
  getEntityPublicPageUrl,
} = require('../../../utils/memberPortalLinks.utils');
const {
  generateCoverMediaItems,
} = require('../../../utils/multipleCoverMediaItems.util');
const {
  META_AD_FORMATS,
  GET_SLUG_OF_PRODUCT,
  META_ADS_ENTITY_TYPE,
  MODEL_BY_ENTITY,
  PLATFORM_CONFIG,
  META_AD_CALL_TO_ACTION_TYPES,
  LINK_CLICK_EVENT,
  META_ADS_CONVERSION_EVENTS,
  ILLEGAL_COUNTRY_CODES,
} = require('../constants');

const buildAssetCustomizationRules = (adFormat) => {
  const mediaLabelKey =
    adFormat === META_AD_FORMATS.SINGLE_IMAGE
      ? 'image_label'
      : 'video_label';

  const baseRule = {
    title_label: { name: 'title_feed' },
    body_label: { name: 'body_feed' },
    [mediaLabelKey]: { name: 'label_feed' },
  };

  return Object.entries(PLATFORM_CONFIG).map(([platform, positions]) => ({
    customization_spec: {
      publisher_platforms: [platform],
      ...positions,
    },
    ...baseRule,
  }));
};

const computeStartAndEndTime = ({ duration }) => {
  const startTime = new Date();
  const endTime = new Date(startTime);
  endTime.setDate(startTime.getDate() + duration);
  return { startTime, endTime };
};

const getEntityInfo = async ({ entityType, entityObjectId }) => {
  const Model = MODEL_BY_ENTITY[entityType];
  if (!Model) throw new Error(`Invalid entity type: ${entityType}`);

  /* ---------- fetch entity doc ---------- */
  const entityInfo = await Model.findById(entityObjectId).lean();
  if (!entityInfo)
    throw new Error(`Entity not found for id: ${entityObjectId}`);

  const coverMediaItems = await generateCoverMediaItems({
    entityType,
    entity: entityInfo,
  });

  const isEntityPaid =
    entityType === META_ADS_ENTITY_TYPE.COMMUNITY
      ? !!entityInfo.isPaidCommunity
      : entityInfo.access === COMMUNITY_EVENT_ACCESS_TYPES.PAID;

  return {
    ...entityInfo,
    coverMediaItems,
    isEntityPaid,
  };
};

const getMetaAdsConversionEvent = (isEntityPaid, isCommunityPaid) => {
  if (isEntityPaid || isCommunityPaid) {
    return META_ADS_CONVERSION_EVENTS.COMPLETE_REGISTRATION;
  }
  return META_ADS_CONVERSION_EVENTS.SUBSCRIBE;
};
const getAdLinkUrl = async (campaign, communityInfo) => {
  const { entityType, entityObjectId } = campaign;
  const entitySlugGetter = GET_SLUG_OF_PRODUCT[entityType];

  if (entityType === META_ADS_ENTITY_TYPE.COMMUNITY) {
    return `${NAS_IO_FRONTEND_URL}${communityInfo?.link}`;
  }
  const entityInfo = await MODEL_BY_ENTITY[entityType]
    .findById(entityObjectId)
    .lean();
  return getEntityPublicPageUrl({
    communitySlug: communityInfo?.link,
    entityType,
    entitySlug: entitySlugGetter(entityInfo),
  });
};

const buildAssetFeedSpec = ({
  adFormat,
  pageId,
  primaryTexts,
  headlines,
  descriptions,
  linkUrls,
  mediaUrls,
  videoInfo,
}) => {
  const BODY_LABELS = [{ name: 'body_feed' }, { name: 'body_story' }];
  const TITLE_LABELS = [{ name: 'title_feed' }, { name: 'title_story' }];
  const IMAGE_LABELS = [{ name: 'label_feed' }];

  const base = {
    ad_formats: [adFormat],
    page_id: pageId,
    call_to_action_types: [META_AD_CALL_TO_ACTION_TYPES.SIGN_UP],
    link_urls: linkUrls.map((url) => ({ website_url: url })),
    bodies: primaryTexts.map((text) => ({
      text,
      adlabels: BODY_LABELS,
    })),
    titles: headlines.map((text) => ({
      text,
      adlabels: TITLE_LABELS,
    })),
    descriptions: descriptions.map((text) => ({
      text,
    })),
    asset_customization_rules: buildAssetCustomizationRules(adFormat),
  };

  return adFormat === META_AD_FORMATS.SINGLE_IMAGE
    ? {
        ...base,
        images: mediaUrls.map((url) => ({ url, adlabels: IMAGE_LABELS })),
      }
    : {
        ...base,
        videos: videoInfo.map((video) => ({
          video_id: video.videoId,
          adlabels: IMAGE_LABELS,
        })),
      };
};

const getValidCountryCode = (countryCode) => {
  if (ILLEGAL_COUNTRY_CODES.includes(countryCode)) {
    return 'US'; // Default to US if illegal country code
  }
  return countryCode;
};
const getActionValue = (actions = [], type) =>
  Number(actions.find((a) => a.action_type === type)?.value ?? 0);

const getConversionEventName = (conversionEvent) => {
  switch (conversionEvent) {
    case META_ADS_CONVERSION_EVENTS.COMPLETE_REGISTRATION:
      return 'offsite_conversion.fb_pixel_complete_registration';
    case META_ADS_CONVERSION_EVENTS.SUBSCRIBE:
      return 'offsite_conversion.fb_pixel_custom';
    default:
      return 'Unknown Event';
  }
  // // it is always going to be this event since we are using pixel event for conversions
  // return 'offsite_conversion.fb_pixel_custom';
};
/** helper – pct diff vs previous period */
const calcPct = (current, previous) =>
  previous === 0 ? null : ((current - previous) / previous) * 100;
/** helper – summarise an array of insight rows */
const aggregateTotals = (rows = [], convertedEvent) =>
  rows.reduce(
    (tot, row) => {
      // eslint-disable-next-line no-param-reassign
      tot.impressions += Number(row.impressions ?? 0);
      // eslint-disable-next-line no-param-reassign
      tot.pageVisits += getActionValue(row.actions, LINK_CLICK_EVENT);
      // eslint-disable-next-line no-param-reassign
      tot.convertedEvents += getActionValue(row.actions, convertedEvent);
      // eslint-disable-next-line no-param-reassign
      tot.spend += Number(row.spend ?? 0);
      return tot;
    },
    { impressions: 0, pageVisits: 0, convertedEvents: 0, spend: 0 }
  );

module.exports = {
  getValidCountryCode,
  getConversionEventName,
  buildAssetFeedSpec,
  computeStartAndEndTime,
  getEntityInfo,
  getMetaAdsConversionEvent,
  getAdLinkUrl,
  aggregateTotals,
  calcPct,
  getActionValue,
};
