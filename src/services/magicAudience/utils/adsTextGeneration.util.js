const { OpenAI } = require('openai');
const { z } = require('zod');
const { zodResponseFormat } = require('openai/helpers/zod');
const { OPENAI_API_KEY } = require('../../../config');
const logger = require('../../logger.service');

const promptTemplate = (entity) => {
  return `your task is to generate the headline, primaryText, and description for the following entity '${entity}' the response should be in JSON format with following keys headlines,primaryText, and description.`;
};
const openai = new OpenAI({
  apiKey: OPENAI_API_KEY,
});

const responseFormatSchema = z.object({
  headlines: z.string().max(40),
  primaryText: z.string().max(240),
  description: z.string().max(40),
});

const generateAdsText = async (entityInfo) => {
  const prompt = promptTemplate(entityInfo.title);
  const completion = await openai.chat.completions.parse({
    model: 'gpt-4o',
    messages: [
      {
        role: 'system',
        content:
          'You are a helpful assistant that generates ad text. always return the response in JSON format, please keep the primary text to be not more than 240 characters, max 40 char for headline and description { headlines:"", primaryText:"", description:"" }',
      },
      {
        role: 'user',
        content: prompt,
      },
    ],
    response_format: zodResponseFormat(responseFormatSchema, 'adsText'),
  });

  const event = completion.choices[0].message;
  try {
    const JSONData = JSON.parse(event.content);
    return responseFormatSchema.parse(JSONData);
  } catch (error) {
    logger.error('Error parsing JSON:', error);
  }
};

module.exports = {
  generateAdsText,
};
