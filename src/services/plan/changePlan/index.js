const PrimaryMongooseConnection = require('../../../rpc/primaryMongooseConnection');
const paymentProviderService = require('./paymentProvider');
const { PAYMENT_PROVIDER } = require('../../../constants/common');
const logger = require('../../logger.service');
const { ParamError } = require('../../../utils/error.util');
const validationService = require('../validation.service');
const planOrderService = require('../planOrder.service');
const planCreditService = require('../credit');
const commonService = require('../common.service');
const planPriceService = require('../planPrices.service');
const PaymentBackendRpc = require('../../../rpc/paymentBackend');

async function updateSubscriptionPlan({
  planOrder,
  price,
  planObjectId,
  session,
  paymentBackendRpc,
  referrer = null,
}) {
  const paymentProvider = planOrder.paymentDetails.paymentProvider;

  const newPlanOrder = await planOrderService.changePlanOrderInterval({
    planOrder,
    planObjectId,
    price,
    referrer,
    session,
  });

  const newCreditPlanOrder =
    await planCreditService.retrievePlanOrderWithCreditHandling({
      planOrder: newPlanOrder,
      curentPriceInfo: price,
      overwriteNextBillingCreditHistory: true,
      paymentBackendRpc,
      session,
    });

  const existingNextBillingCredits = (
    newCreditPlanOrder.creditHistory ?? []
  ).find(
    (credit) => credit.billingCycle === newCreditPlanOrder.billingCycle + 1
  );
  const nextBillingPrice =
    existingNextBillingCredits?.paidAmount ??
    newCreditPlanOrder.amountInLocalCurrency;

  switch (paymentProvider) {
    case PAYMENT_PROVIDER.EBANX:
      await planOrderService.updateRenewalAmount({
        planOrder: newPlanOrder,
        amountInLocalCurrency: nextBillingPrice,
        session,
      });
      break;
    default:
      break;
  }

  const result = await paymentProviderService.changePlan({
    planOrder: newPlanOrder,
    priceId: newCreditPlanOrder.paymentProviderPriceId,
    paymentBackendRpc,
  });

  return result;
}

async function validatePayloadInformation({
  price,
  pendingPlanOrder,
  planOrder,
  learnerObjectId,
  allowSameInterval = false,
}) {
  const { interval, intervalCount } = planOrder;

  validationService.checkIfCanChangePlan({
    pendingPlanOrder,
    planOrder,
    price,
    learnerObjectId,
    throwError: true,
  });

  if (planOrder.paymentProviderPriceId === price.id) {
    throw new ParamError('Plan order is already subscribed to the plan');
  }

  if (
    !allowSameInterval &&
    interval === price.interval &&
    intervalCount === price.intervalCount
  ) {
    throw new ParamError('Cannot change plan with the same interval');
  }
}

exports.changePlan = async ({
  communityObjectId,
  learnerObjectId,
  priceId,
  allowSameInterval = false,
  communityReferralCode,
}) => {
  const paymentBackendRpc = new PaymentBackendRpc();
  await paymentBackendRpc.init();

  const [existingPlanOrder, pendingPlanOrder, community, learner] =
    await Promise.all([
      planOrderService.retrieveExistingPlanOrder({
        communityObjectId,
      }),
      planOrderService.retrievePendingPlanOrder({
        communityObjectId,
      }),
      commonService.retrieveActiveCommunity(communityObjectId),
      commonService.retrieveLearner(learnerObjectId),
    ]);

  if (!existingPlanOrder) {
    throw new ParamError('No active plan order');
  }

  if (
    existingPlanOrder.learnerObjectId.toString() !==
    learnerObjectId.toString()
  ) {
    throw new ParamError('Only purchaser able to change plan');
  }

  const {
    entityType,
    communityReferralCode: existingCommunityReferralCode,
  } = existingPlanOrder;

  const selectedCommunityReferralCode =
    communityReferralCode ?? existingCommunityReferralCode;

  const { plan, planPrices, referrer } =
    await planPriceService.retrievePlanPrices({
      community,
      existingPlanOrder,
      entityType,
      email: learner.email,
      communityReferralCode: selectedCommunityReferralCode,
    });

  const price = planPrices.find((planPrice) => planPrice.id === priceId);

  await validatePayloadInformation({
    price,
    pendingPlanOrder,
    planOrder: existingPlanOrder,
    learnerObjectId,
    allowSameInterval,
  });

  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  try {
    const result = await updateSubscriptionPlan({
      planObjectId: plan._id,
      planOrder: existingPlanOrder,
      price,
      session,
      paymentBackendRpc,
      referrer,
    });

    await session.commitTransaction();

    return result;
  } catch (err) {
    logger.error(`changePlan: error: ${err.message}, stack: ${err.stack}`);
    await session.abortTransaction();
    throw err;
  } finally {
    await session.endSession();
  }
};
