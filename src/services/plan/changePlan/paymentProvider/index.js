const stripeService = require('./stripe.service');
const stripeIndiaService = require('./stripeIndia.service');
const ebanxService = require('./ebanx.service');
const { PAYMENT_PROVIDER } = require('../../../../constants/common');

exports.changePlan = async ({ planOrder, priceId, paymentBackendRpc }) => {
  const { paymentDetails } = planOrder;
  const paymentProvider = paymentDetails.paymentProvider;

  switch (paymentProvider) {
    case PAYMENT_PROVIDER.STRIPE:
    case PAYMENT_PROVIDER.STRIPE_US:
      return stripeService.changePlan({
        planOrder,
        priceId,
        paymentBackendRpc,
        paymentProvider,
      });
    case PAYMENT_PROVIDER.STRIPE_INDIA:
      return stripeIndiaService.changePlan({
        planOrder,
        priceId,
        paymentBackendRpc,
      });
    case PAYMENT_PROVIDER.EBANX:
      return ebanxService.changePlan();
    default:
      throw new Error(`Does not support ${paymentProvider}`);
  }
};
