const {
  PAYMENT_RESPONSE_ACTION,
} = require('../../../../constants/common');

exports.changePlan = async ({ planOrder, priceId, paymentBackendRpc }) => {
  const { paymentProviderSubscriptionId } = planOrder;

  await paymentBackendRpc.changeStripeIndiaPlan({
    stripeSubscriptionId: paymentProviderSubscriptionId,
    priceId,
  });

  return {
    action: PAYMENT_RESPONSE_ACTION.NONE,
    success: true,
  };
};
