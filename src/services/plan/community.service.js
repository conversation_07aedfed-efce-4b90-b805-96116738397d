const CommunityModel = require('../../communitiesAPI/models/community.model');

const paymentFeeStructureConfigService = require('../config/paymentFeeStructureConfig.service');
const feeService = require('../../communitiesAPI/services/common/fee.service');
const { PLAN_FEATURE } = require('./constants');
const FeeService = require('../../communitiesAPI/services/common/fee.service');
const {
  retrievePaymentFeeStructure,
} = require('../config/paymentFeeStructureConfig.service');

async function reallocateBasePayoutFeeConfigs({
  community,
  entityType = null,
  targetEffectiveStartTime = null,
}) {
  const paymentFeeStructure =
    await paymentFeeStructureConfigService.retrievePaymentFeeStructure({
      baseCurrency: community.baseCurrency,
      planType: entityType,
      targetEffectiveStartTime,
    });

  const { feeConfig: basePayoutFeeConfig } =
    await feeService.retrieveBasePayoutFeeConfigAndOtherSettings({
      community,
      paymentFeeStructure,
      targetEffectiveStartTime,
    });

  let basePayoutFeeConfigs = [];

  if (
    !community.basePayoutFeeConfigs ||
    community.basePayoutFeeConfigs.length === 0
  ) {
    basePayoutFeeConfigs = [basePayoutFeeConfig];
  } else {
    const updatedBasePayoutFeeConfigs = [
      ...community.basePayoutFeeConfigs,
    ];

    const lastBasePayoutFeeConfig = updatedBasePayoutFeeConfigs.pop();
    lastBasePayoutFeeConfig.effectiveTimeEnd =
      basePayoutFeeConfig.effectiveTimeStart;

    updatedBasePayoutFeeConfigs.push(
      lastBasePayoutFeeConfig,
      basePayoutFeeConfig
    );

    basePayoutFeeConfigs = updatedBasePayoutFeeConfigs;
  }

  return basePayoutFeeConfigs;
}

exports.reallocateCustomPayoutFeeConfigs = async ({
  community,
  entityType = null,
  targetEffectiveStartTime = null,
}) => {
  const payoutFeeConfig =
    await paymentFeeStructureConfigService.retrieveCustomFeeConfigs({
      customFeeConfigs: community.config?.customFeeConfigs ?? [],
      planType: entityType,
      targetEffectiveStartTime,
    });
  if (!payoutFeeConfig) {
    return null;
  }

  const paymentFeeStructure = await retrievePaymentFeeStructure({
    baseCurrency: community.baseCurrency,
    planType: community.config?.planType,
    targetEffectiveStartTime: payoutFeeConfig.effectiveTimeStart,
  });

  if (!payoutFeeConfig.purchaseTypeConfigs) {
    const { feeConfig } =
      await FeeService.retrieveBasePayoutFeeConfigAndOtherSettings({
        community,
        paymentFeeStructure,
        targetEffectiveStartTime: payoutFeeConfig.effectiveTimeStart,
      });

    const { purchaseTypeConfigs } = feeConfig;
    payoutFeeConfig.purchaseTypeConfigs = purchaseTypeConfigs;
  }

  let payoutFeeConfigs = [];

  if (
    !community.payoutFeeConfigs ||
    community.payoutFeeConfigs.length === 0
  ) {
    payoutFeeConfigs = [payoutFeeConfig];
  } else {
    const updatedBasePayoutFeeConfigs = [...community.payoutFeeConfigs];

    const lastPayoutFeeConfig = updatedBasePayoutFeeConfigs.pop();
    // For the effectiveTimeEnd that is in very far away future (like 2300-01-01)
    // We will update the effectiveTimeEnd of last config to be payoutFeeConfig.effectiveTimeStart
    if (
      lastPayoutFeeConfig.effectiveTimeEnd >
      payoutFeeConfig.effectiveTimeStart
    ) {
      lastPayoutFeeConfig.effectiveTimeEnd =
        payoutFeeConfig.effectiveTimeStart;
    }

    updatedBasePayoutFeeConfigs.push(lastPayoutFeeConfig, payoutFeeConfig);

    payoutFeeConfigs = updatedBasePayoutFeeConfigs;
  }

  return payoutFeeConfigs;
};

exports.switchEnrollPlanOrder = async ({
  community,
  communityObjectId,
  existingPlanInfo = {},
  newPlanInfo = {},
  session = undefined,
}) => {
  const { features: existingFeatures } = existingPlanInfo;
  const { entityType, features } = newPlanInfo;
  const storageIncrementInMb =
    (features.find((feature) => {
      return feature.type === PLAN_FEATURE.STORAGE_UPGRADE_IN_GB;
    })?.value ?? 0) * 1000;
  const storageDecrementInMb =
    (existingFeatures.find((feature) => {
      return feature.type === PLAN_FEATURE.STORAGE_UPGRADE_IN_GB;
    })?.value ?? 0) * 1000;

  const incrementQuery = {
    communityVideoSizeLimit: storageIncrementInMb - storageDecrementInMb,
  };

  const setQuery = { 'config.planType': entityType };

  const payoutFeeConfigs = await this.reallocateCustomPayoutFeeConfigs({
    community,
    entityType,
  });
  if (payoutFeeConfigs) {
    setQuery.payoutFeeConfigs = payoutFeeConfigs;
  }

  const basePayoutFeeConfigs = await reallocateBasePayoutFeeConfigs({
    community,
    entityType,
  });

  if (basePayoutFeeConfigs) {
    setQuery.basePayoutFeeConfigs = basePayoutFeeConfigs;
  }

  const result = await CommunityModel.updateOne(
    {
      _id: communityObjectId,
      'config.planType': { $ne: entityType },
    },
    {
      $set: setQuery,
      $inc: incrementQuery,
    },
    { session }
  );

  return result;
};

exports.enrollPlanOrder = async ({
  community,
  features,
  entityType,
  communityObjectId,
  session = undefined,
}) => {
  if (!features) {
    throw new Error('No features');
  }

  const storageIncrementInMb =
    (features.find((feature) => {
      return feature.type === PLAN_FEATURE.STORAGE_UPGRADE_IN_GB;
    })?.value ?? 0) * 1000;

  const incrementQuery = {
    communityVideoSizeLimit: storageIncrementInMb,
  };

  const setQuery = { 'config.planType': entityType };

  const payoutFeeConfigs = await this.reallocateCustomPayoutFeeConfigs({
    community,
    entityType,
  });
  if (payoutFeeConfigs) {
    setQuery.payoutFeeConfigs = payoutFeeConfigs;
  }

  const basePayoutFeeConfigs = await reallocateBasePayoutFeeConfigs({
    community,
    entityType,
  });

  if (basePayoutFeeConfigs) {
    setQuery.basePayoutFeeConfigs = basePayoutFeeConfigs;
  }

  const result = await CommunityModel.updateOne(
    {
      _id: communityObjectId,
      'config.planType': { $ne: entityType },
    },
    {
      $set: setQuery,
      $inc: incrementQuery,
    },
    { session }
  );

  return result;
};

exports.cancelPlanOrder = async ({
  community,
  features,
  entityType,
  communityObjectId,
  session = undefined,
}) => {
  if (!features) {
    throw new Error('No features');
  }

  const storageIncrementInMb =
    (features.find((feature) => {
      return feature.type === PLAN_FEATURE.STORAGE_UPGRADE_IN_GB;
    })?.value ?? 0) * 1000;

  const incrementQuery = {
    communityVideoSizeLimit: -storageIncrementInMb,
  };

  const setQuery = {};

  const payoutFeeConfigs = await this.reallocateCustomPayoutFeeConfigs({
    community,
  });
  if (payoutFeeConfigs) {
    setQuery.payoutFeeConfigs = payoutFeeConfigs;
  }

  const basePayoutFeeConfigs = await reallocateBasePayoutFeeConfigs({
    community,
  });

  if (basePayoutFeeConfigs) {
    setQuery.basePayoutFeeConfigs = basePayoutFeeConfigs;
  }

  const result = await CommunityModel.updateOne(
    {
      _id: communityObjectId,
      'config.planType': entityType,
    },
    {
      $unset: { 'config.planType': 1 },
      $inc: incrementQuery,
      $set: setQuery,
    },
    { session }
  );

  return result;
};
