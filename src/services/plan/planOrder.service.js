const httpContext = require('express-http-context');
const moment = require('moment');

const PlanOrderModel = require('../../models/plan/communityPlanOrder.model');
const PlanAdhocRecordsModel = require('../../models/plan/communityPlanAdhocRecords.model');
const PlanModel = require('../../models/plan/communityPlan.model');

const PaymentBackendRpc = require('../../rpc/paymentBackend');
const { PAYMENT_PROVIDER } = require('../../constants/common');
const { PAYMENT_STATUSES } = require('../../communitiesAPI/constants');
const { ParamError } = require('../../utils/error.util');
const {
  ORDER_STATUS,
  PLAN_ADHOC_STATUS,
  ENTITY_TYPE_RANKS,
  TIER_CHANGE_CATEGORY,
} = require('./constants');
const paymentProviderUtils = require('../../utils/paymentProvider.util');
const notificationService = require('../communityNotification');
const validationService = require('./validation.service');
const commonService = require('./common.service');

function getGracePeriodEndDate(planOrder) {
  const paymentProvider = planOrder.paymentDetails.paymentProvider;

  const gracePeriodEndDate = moment(planOrder.nextBillingDate);
  switch (paymentProvider) {
    case PAYMENT_PROVIDER.STRIPE:
    case PAYMENT_PROVIDER.STRIPE_INDIA:
    case PAYMENT_PROVIDER.STRIPE_US:
    case PAYMENT_PROVIDER.EBANX:
      gracePeriodEndDate.add(7, 'day');
      break;
    default:
      throw new Error(`Invalid payment provider: ${paymentProvider}`);
  }

  return gracePeriodEndDate.toDate();
}

exports.lockAndRetrievePlanOrderForRenewal = async (planOrderObjectId) => {
  // To prevent double charge
  const planOrder = await PlanOrderModel.findOneAndUpdate(
    {
      _id: planOrderObjectId,
      'paymentDetails.renewalLock': { $exists: false },
    },
    {
      $set: { 'paymentDetails.renewalLock': true },
    },
    { new: true }
  ).lean();

  if (!planOrder) {
    throw new Error(
      `This plan order is under renewal process: planOrderObjectId=${planOrderObjectId}`
    );
  }

  return planOrder;
};

exports.unlockPlanOrderForRenewal = async (planOrderObjectId) => {
  const result = await PlanOrderModel.updateOne(
    {
      _id: planOrderObjectId,
      'paymentDetails.renewalLock': true,
    },
    {
      $unset: { 'paymentDetails.renewalLock': 1 },
    }
  );

  if (result.modifiedCount !== 1) {
    throw new Error(
      `This plan order cannot be unlocked: planOrderObjectId=${planOrderObjectId}`
    );
  }
};

exports.retrievePlanOrder = async (planOrderObjectId) => {
  const planOrder = await PlanOrderModel.findById(planOrderObjectId)
    .read('primary')
    .lean();

  return planOrder;
};

exports.retrieveExistingPlanAdhocRecord = async ({
  communityObjectId,
}) => {
  const planAdhocRecord = await PlanAdhocRecordsModel.findOne({
    communityObjectId,
    status: PLAN_ADHOC_STATUS.ACTIVE,
  }).lean();

  return planAdhocRecord;
};

async function retrievePlanByType({ type }) {
  const plan = await PlanModel.findOne({
    type,
    isActive: true,
  }).lean();

  if (!plan) {
    throw new ParamError('Plan not found');
  }

  return plan;
}

exports.createOrUpdatePlanAdhocRecord = async ({
  adhocRecord,
  addedBy,
}) => {
  const { entityType, communityCode, creatorEmail, notes, status } =
    adhocRecord;

  const planType = `${entityType}_ADHOC`;

  const [community, plan] = await Promise.all([
    commonService.retrieveCommunityByCode(communityCode),
    retrievePlanByType({ type: planType }),
  ]);

  const communityObjectId = community._id;

  const [planOrder, pendingPlanOrder] = await Promise.all([
    this.retrieveExistingPlanOrder({
      communityObjectId,
    }),
    this.retrievePendingPlanOrder({
      communityObjectId,
    }),
  ]);

  if (planOrder || pendingPlanOrder) {
    throw new ParamError('Plan order already exists');
  }

  const adhocRecordData = {
    entityType,
    communityObjectId,
    status,
    planObjectId: plan._id,
    creatorEmail,
    notes,
    addedBy,
  };

  const existingPlanAdhocRecord = await PlanAdhocRecordsModel.findOne({
    communityObjectId,
  }).lean();

  if (!existingPlanAdhocRecord) {
    if (status === PLAN_ADHOC_STATUS.INACTIVE) {
      // Not even enrolled can ignore
      return {
        planAdhocRecord: null,
        created: false,
        switchedTier: false,
      };
    }
    try {
      const planAdhocRecord = await PlanAdhocRecordsModel.create(
        adhocRecordData
      );

      return {
        planAdhocRecord: planAdhocRecord.toObject(),
        created: true, // (enroll)
        switchedTier: false,
      };
    } catch (err) {
      if (err.code === 11000) {
        return {
          planAdhocRecord: null,
          created: false,
          switchedTier: false,
        };
      }

      throw err;
    }
  }

  const created = false;

  const isEntitySame = existingPlanAdhocRecord.entityType === entityType;
  const isStatusSame = existingPlanAdhocRecord.status === status;

  let switchedTier = false;

  if (!isEntitySame && status === PLAN_ADHOC_STATUS.INACTIVE) {
    // INACTIVE PRO <> INACTIVE PLATINUM
    // ACTIVE PRO > INACTIVE PLATINUM
    // ACTIVE PLATINUM > INACTIVE PRO
    throw new Error(
      `Cannot make plan ${status} as there is no existing active ${entityType} plan for ${communityCode}`
    );
  }

  if (!isEntitySame && status === PLAN_ADHOC_STATUS.ACTIVE) {
    if (isStatusSame) {
      // ACTIVE PRO > ACTIVE PLATINUM (Upgrade)
      // ACTIVE PLATINUM > ACTIVE PRO (Downgrade)
      switchedTier = true;
    }
  }
  // (isEntitySame && !isStatusSame)
  // -- ACTIVE <> INACTIVE for PRO (unenroll/enroll)
  // -- ACTIVE <> INACTIVE for PLATINUM (unenroll/enroll)
  // (!isEntitySame && !isStatusSame && status === PLAN_ADHOC_STATUS.ACTIVE)
  // -- INACTIVE PRO > ACTIVE PLATINUM (enroll)
  // -- INACTIVE PLATINUM > ACTIVE PRO (enroll)
  // (isEntitySame && isStatusSame)

  const updatedPlanAdhocRecord =
    await PlanAdhocRecordsModel.findOneAndUpdate(
      {
        communityObjectId,
      },
      adhocRecordData,
      { new: true }
    ).lean();

  if (isEntitySame && isStatusSame) {
    // No change needed
    return {
      planAdhocRecord: null,
      created,
      switchedTier,
    };
  }
  return {
    existingPlanAdhocRecord,
    planAdhocRecord: updatedPlanAdhocRecord,
    created: false,
    switchedTier,
  };
};

exports.cancelAllPlanAdhocRecords = async ({
  email,
  entityType = null,
}) => {
  const matchFilter = {
    creatorEmail: email,
    status: PLAN_ADHOC_STATUS.ACTIVE,
  };

  if (entityType) {
    matchFilter.entityType = entityType;
  }

  const planAdhocRecords = await PlanAdhocRecordsModel.find(
    matchFilter
  ).lean();

  const result = await Promise.all(
    planAdhocRecords.map(async (planAdhocRecord) =>
      PlanAdhocRecordsModel.findByIdAndUpdate(
        planAdhocRecord._id,
        {
          $set: { status: PLAN_ADHOC_STATUS.INACTIVE },
        },
        { new: true }
      ).lean()
    )
  );

  return result;
};

exports.retrieveExistingPlanOrder = async ({
  communityObjectId,
  entityType = null,
}) => {
  const filter = {
    communityObjectId,
    $or: [
      {
        status: ORDER_STATUS.CURRENT,
      },
      {
        status: ORDER_STATUS.CANCELLED,
        cancelledAt: { $gte: new Date() },
      },
    ],
  };
  if (entityType) {
    filter.entityType = entityType;
  }
  const planOrder = await PlanOrderModel.findOne(filter).lean();

  return planOrder;
};

exports.retrievePendingPlanOrder = async ({ communityObjectId }) => {
  const filter = {
    communityObjectId,
    status: ORDER_STATUS.PENDING,
  };
  const planOrder = await PlanOrderModel.findOne(filter).lean();
  return planOrder;
};

/**
 * Format get plan api response for non paid plans like Free tier and adhoc plans
 * @param {Object} params
 * @returns {Object} planInfo
 */
exports.transformToPlanInfoForNonPaidPlan = ({
  billingHistory,
  learnerObjectId,
  isAdhoc,
}) => {
  const isSubscriber = false;
  const isPaymentFailed = false;
  const canChangePaymentMethod = false;
  const canCancelPlan = false;
  const canChangePlan = !isAdhoc;
  const canChangeTier = !isAdhoc;

  const planInfo = {
    learnerObjectId,
    isSubscriber,
    isPaymentFailed,
    canChangePaymentMethod,
    canChangeTier,
    canChangePlan,
    canCancelPlan,
    billingHistory,
    isAdhoc,
  };

  return planInfo;
};

function retrievePlanOriginalPrice({
  planPrices,
  interval,
  intervalCount,
}) {
  const currentPrice = planPrices?.find(
    (price) =>
      price.interval === interval && price.intervalCount === intervalCount
  );

  return currentPrice?.originalAmount;
}

exports.transformToPlanInfo = ({
  pendingPlanOrder,
  pendingReferrer,
  planOrder,
  paymentMethod,
  billingHistory,
  learnerObjectId,
  nextBillingPrice,
  isTrial,
  isLatestBillingTransactionTrial,
  latestBillingTransaction,
  referrer,
  planPrices = null,
}) => {
  const {
    _id: planOrderObjectId,
    planObjectId,
    learnerObjectId: purchaserLearnerObjectId,
    paymentProviderSubscriptionId,
    interval,
    intervalCount,
    nextBillingDate,
    status,
    amountInLocalCurrency,
    localCurrency,
    paymentDetails,
    billingCycle,
    cancelledAt,
    previousAmountInLocalCurrency,
    previousLocalCurrency,
    planHistory,
    entityType,
  } = planOrder;

  const canCancelPlan = validationService.checkIfCanCancelPlan({
    planOrder,
    learnerObjectId,
    throwError: false,
  });

  const canChangePlan = validationService.checkIfCanChangePlan({
    pendingPlanOrder,
    planOrder,
    price: null,
    learnerObjectId,
    throwError: false,
  });

  const canChangeTier = validationService.checkIfCanChangeTier({
    pendingPlanOrder,
    planOrder,
    price: null,
    learnerObjectId,
    throwError: false,
  });

  const isSubscriber = validationService.isPlanPurchaser({
    planOrder,
    learnerObjectId,
    throwError: false,
  });

  const canChangePaymentMethod =
    validationService.checkIfCanChangePaymentMethod({
      planOrder,
      learnerObjectId,
      throwError: false,
    });

  const isPaymentFailed =
    paymentDetails.status === PAYMENT_STATUSES.FAILED;

  const planInfo = {
    referrer,
    planOrderObjectId,
    planObjectId,
    learnerObjectId: purchaserLearnerObjectId,
    paymentProviderSubscriptionId,
    interval,
    intervalCount,
    nextBillingDate,
    cancelledAt,
    paymentProvider: paymentDetails.paymentProvider,
    status,
    amount: amountInLocalCurrency,
    currency: localCurrency,
    paymentDetails,
    billingCycle,
    isTrial,
    currentPaymentMethod: paymentMethod,
    isSubscriber,
    canChangeTier,
    canChangePlan,
    canChangePaymentMethod,
    canCancelPlan,
    previousAmountInLocalCurrency,
    previousLocalCurrency,
    billingHistory,
    planHistory,
    isPaymentFailed,
    phoneNumber: planOrder.phoneNumber,
  };

  planInfo.currentPlan = {
    cancelledAt,
    nextBillingDate,
    interval,
    intervalCount,
    currency: localCurrency,
    amount: amountInLocalCurrency,
    entityType,
    billingCycle,
    isTrial,
    selectedInterval: interval, // Interval of the current plan, used for display in the change plan UI
    selectedIntervalCount: intervalCount, // Interval count of the current plan, used for display in the change plan UI
  };

  const originalPrice = retrievePlanOriginalPrice({
    planPrices,
    interval,
    intervalCount,
  });

  if (originalPrice) {
    planInfo.currentPlan.originalPrice = originalPrice;
  }

  const planExistsInPlanHistory = (planInfo.planHistory ?? []).find(
    (info) => info.billingCycle === billingCycle
  );

  if (planExistsInPlanHistory) {
    planInfo.currentPlan = {
      nextBillingDate: planExistsInPlanHistory.nextBillingDate,
      interval: planExistsInPlanHistory.interval,
      intervalCount: planExistsInPlanHistory.intervalCount,
      currency: planExistsInPlanHistory.localCurrency,
      amount: planExistsInPlanHistory.amountInLocalCurrency,
      entityType,
      selectedInterval: planExistsInPlanHistory.interval,
      selectedIntervalCount: planExistsInPlanHistory.intervalCount,
    };

    const planHistoryOriginalPrice = retrievePlanOriginalPrice({
      planPrices,
      interval: planExistsInPlanHistory.interval,
      intervalCount: planExistsInPlanHistory.intervalCount,
    });

    if (planHistoryOriginalPrice) {
      planInfo.currentPlan.originalPrice = planHistoryOriginalPrice;
    }

    planInfo.nextBillingPrice = {
      interval,
      intervalCount,
      currency: localCurrency,
      amount: amountInLocalCurrency,
      entityType,
    };
  }

  if (isTrial) {
    if (nextBillingPrice) {
      planInfo.nextBillingPrice = {
        interval: nextBillingPrice.interval,
        intervalCount: nextBillingPrice.intervalCount,
        currency: nextBillingPrice.currency,
        amount: nextBillingPrice.unitAmount,
      };
    }

    if (isLatestBillingTransactionTrial) {
      planInfo.previousBillingPrice = {
        interval: latestBillingTransaction.interval,
        intervalCount: latestBillingTransaction.intervalCount,
        currency: latestBillingTransaction.currency,
        amount: latestBillingTransaction.amount,
      };

      planInfo.currentPlan = {
        nextBillingDate,
        interval: latestBillingTransaction.interval,
        intervalCount: latestBillingTransaction.intervalCount,
        currency: latestBillingTransaction.currency,
        amount: latestBillingTransaction.amount,
        originalPrice: latestBillingTransaction.originalAmount,
        entityType,
        selectedInterval: interval,
        selectedIntervalCount: intervalCount,
      };
    }
  }

  if (pendingPlanOrder) {
    planInfo.isDowngradedPlan = true;
    planInfo.nextBillingPrice = {
      interval: pendingPlanOrder.interval,
      intervalCount: pendingPlanOrder.intervalCount,
      currency: pendingPlanOrder.localCurrency,
      amount: pendingPlanOrder.amountInLocalCurrency,
      entityType: pendingPlanOrder.entityType,
      referrer: pendingReferrer,
    };
  }

  if (isPaymentFailed) {
    planInfo.gracePeriodEndDate = getGracePeriodEndDate(planOrder);
  }

  return planInfo;
};

exports.retrieveLastPurchasePlanOrder = async ({ communityObjectId }) => {
  const planOrder = await PlanOrderModel.findOne({
    status: { $nin: [ORDER_STATUS.PENDING, ORDER_STATUS.INVALID] },
    communityObjectId,
    'paymentDetails.completedPayment': true,
  })
    .sort({ _id: -1 })
    .lean();

  return planOrder;
};

function isUpdateOperation(decodedSignupToken) {
  return decodedSignupToken?.planOrderObjectId != null;
}

exports.isTierUpgrade = ({ newEntityType, existingEntityType }) => {
  return (
    ENTITY_TYPE_RANKS[newEntityType] <
    ENTITY_TYPE_RANKS[existingEntityType]
  );
};

function getReformattedPlanOrderFieldsForCreditDetails({
  planOrder,
  creditDetails,
}) {
  if (!creditDetails?.creditPlan) {
    return planOrder;
  }
  /**
   * Either a zero Price plan that points to originalAmount plan
   * Eg. Original Price: 100
   *     Scenario 1 - Credit in wallet: 230 | FirstBillingPaidAmount: 0 | RenewalAmount: 0
   *                  Plan Price points to $0
   *     Scenario 2 - Credit in wallet: 130 | FirstBillingPaidAmount: 0 | RenewalAmount: 30
   *                  Plan Price points to $70 with 1 billingcycle trial
   *     Scenario 3 - Credit in wallet: 30 | FirstBillingPaidAmount: 70 | RenewalAmount: 100
   *                  Plan Price points to $70
   */
  const {
    creditHistory,
    creditPlan: entityInfo,
    creditPrice: price,
  } = creditDetails;

  const priceId = price?.priceId;
  const localCurrency = price?.currency;
  const paymentProviderForPriceId = price?.paymentProviderForPriceId;
  const firstBillingCredit = creditHistory.find(
    (credit) => credit.billingCycle === 1
  );

  return {
    ...planOrder,
    paymentProviderPriceId: priceId, // finalPrice to charge after accounting all credits
    paymentProviderForPriceId,
    entityType: entityInfo.entityType,
    planObjectId: entityInfo._id,
    billingModel: entityInfo.billingModel,
    features: entityInfo.features,
    // metadata fields used for data dashboard. should not be reassigned
    metadata: {
      firstBilling: {
        ...planOrder.metadata.firstBilling,
        priceId,
        localCurrency,
        amountInLocalCurrency: planOrder.amountInLocalCurrency, // plan Amount
        creditsInLocalCurrency: firstBillingCredit?.paidCreditAmount, // credit used
        paidAmountInLocalCurrency: firstBillingCredit?.paidAmount, // final paidAmount
      },
    },
    creditHistory,
  };
}

function generatePlanOrder({
  community,
  learner,
  countryInfo,
  timezone,
  requestor,
  trackingData,
  plan,
  paymentProvider,
}) {
  const { priceId, metadata, nextBillingPriceId, referralDetails } = plan;

  const {
    price,
    entityInfo,
    existingPlanOrder,
    prorationDetails,
    creditDetails,
    isChangeTier,
    isTierUpgrade,
  } = metadata;

  const { _id: communityObjectId, payment_methods: paymentMethods } =
    community;

  const localAmount = price.unitAmount;
  const localCurrency = price.currency;

  const paymentProviderForPriceId =
    paymentProviderUtils.retrievePaymentProviderForPlan(
      paymentMethods,
      localCurrency
    );

  const { _id: learnerObjectId } = learner;

  const recurring = price.recurring;

  const interval = recurring.interval;
  const intervalCount = recurring.interval_count;

  const completedPayment = false;

  const country = {
    code: countryInfo.countryCode,
    name: countryInfo.country,
  };

  const planOrder = {
    communityObjectId,
    learnerObjectId,
    entityType: entityInfo.entityType,
    country,
    requestor,
    amountInLocalCurrency: localAmount,
    localCurrency,
    paymentDetails: {
      completedPayment,
      status: PAYMENT_STATUSES.INCOMPLETE,
      latestUpdatedTime: new Date(),
      recurringPurchase: true,
      paymentProvider,
      requestId: httpContext.get('reqId'),
    },
    applyDiscount: false,
    timezone,
    trackingData,
    interval,
    intervalCount,
    paymentProviderPriceId: priceId,
    paymentProviderForPriceId,
    planObjectId: entityInfo._id,
    billingModel: entityInfo.billingModel,
    features: entityInfo.features,
    status: ORDER_STATUS.DRAFT,
    billingCycle: 1,
    // metadata fields used for data dashboard. should not be reassigned
    metadata: {
      firstBilling: {
        priceId,
        interval,
        intervalCount,
        amountInLocalCurrency: localAmount,
        paidAmountInLocalCurrency: localAmount,
        localCurrency,
      },
    },
  };

  if (referralDetails) {
    planOrder.communityReferralCode =
      referralDetails.communityReferralCode;
    planOrder.referralRewardTemplateObjectId =
      referralDetails.referralRewardTemplateObjectId;
    planOrder.referrerCommunityObjectId =
      referralDetails.referrerCommunityObjectId;
  }

  if (nextBillingPriceId) {
    // Fields to identify the nextBillingPlan the user has chosen
    planOrder.isOnTrial = true;
    planOrder.metadata.firstBilling.isTrial = true;
    planOrder.metadata.nextBillingPriceId = nextBillingPriceId;
  }

  if (isChangeTier) {
    planOrder.isOnTrial = false;
    planOrder.metadata.firstBilling.isTrial = false;
    planOrder.tierChangeCategory = isTierUpgrade
      ? TIER_CHANGE_CATEGORY.UPGRADE
      : TIER_CHANGE_CATEGORY.DOWNGRADE;
    planOrder.previousPlanOrder = {
      planOrderObjectId: existingPlanOrder._id,
      entityType: existingPlanOrder.entityType,
      interval: existingPlanOrder.interval,
      intervalCount: existingPlanOrder.intervalCount,
      billingCycle: existingPlanOrder.billingCycle,
    };
    if (isTierUpgrade) {
      if (prorationDetails) {
        planOrder.prorationDetails = prorationDetails;
      }
      if (creditDetails) {
        const creditPlanOrder =
          getReformattedPlanOrderFieldsForCreditDetails({
            planOrder,
            creditDetails,
          });
        return creditPlanOrder;
      }
    }
  }
  return planOrder;
}

async function updatePlanOrder(
  planOrderObjectId,
  updateData,
  session,
  ignoreFilter = false
) {
  const filter = {
    _id: planOrderObjectId,
  };

  let updatedPlanOrder;

  if (ignoreFilter) {
    updatedPlanOrder = await PlanOrderModel.findOneAndUpdate(
      filter,
      updateData,
      {
        new: true,
        session,
      }
    ).lean();
  } else {
    filter.$and = [
      { 'paymentDetails.status': { $ne: PAYMENT_STATUSES.PENDING } },
      { 'paymentDetails.status': { $ne: PAYMENT_STATUSES.SUCCESS } },
    ];

    updatedPlanOrder = await PlanOrderModel.findOneAndReplace(
      filter,
      updateData,
      {
        new: true,
        session,
      }
    ).lean();
  }

  if (!updatedPlanOrder) {
    throw new ParamError('Plan order cannot be updated');
  }

  return updatedPlanOrder;
}

exports.createOrUpdatePlanOrder = async ({
  community,
  learner,
  countryInfo,
  timezone,
  requestor,
  trackingData,
  plan,
  decodedSignupToken,
  paymentProvider,
  session,
}) => {
  const generatedPlanOrder = generatePlanOrder({
    community,
    learner,
    countryInfo,
    timezone,
    requestor,
    trackingData,
    plan,
    paymentProvider,
  });

  const updateOperation = isUpdateOperation(decodedSignupToken);

  let planOrder;

  if (updateOperation) {
    planOrder = await updatePlanOrder(
      decodedSignupToken.planOrderObjectId,
      generatedPlanOrder,
      session
    );
  } else {
    planOrder = (
      await PlanOrderModel.create([generatedPlanOrder], {
        session,
      })
    )[0].toObject();
  }

  return planOrder;
};

async function handleCancellationBeforeDowngradeOccurs({
  planOrder,
  paymentBackendRpc,
}) {
  if (!planOrder.nextPlanOrder?.planOrderObjectId) {
    return;
  }
  const nextPlanOrder = await PlanOrderModel.findById(
    planOrder.nextPlanOrder.planOrderObjectId
  ).lean();

  if (
    !nextPlanOrder ||
    nextPlanOrder.status !== ORDER_STATUS.PENDING ||
    nextPlanOrder.tierChangeCategory !== TIER_CHANGE_CATEGORY.DOWNGRADE
  ) {
    return;
  }
  const updateQuery = {
    status: ORDER_STATUS.INVALID,
    cancelledAt: planOrder.cancelledAt,
    unsubscribedAt: planOrder.unsubscribedAt,
    cancellationReasons: planOrder.cancellationReasons,
  };

  const paymentProvider = nextPlanOrder.paymentDetails.paymentProvider;

  const { paymentProviderSubscriptionId } = nextPlanOrder;

  const cancellationReason =
    'User cancelled previous plan before nextBilling plan becomes active';

  switch (paymentProvider) {
    case PAYMENT_PROVIDER.STRIPE:
    case PAYMENT_PROVIDER.STRIPE_US:
    case PAYMENT_PROVIDER.STRIPE_INDIA:
      await paymentBackendRpc.cancelStripeSubscription(
        paymentProviderSubscriptionId,
        cancellationReason,
        paymentProvider,
        false,
        true
      );
      break;
    case PAYMENT_PROVIDER.EBANX:
      updateQuery.scheduledCancellation = true;
      break;
    default:
      throw new ParamError(
        `Payment provider ${paymentProvider} not supported`
      );
  }

  await PlanOrderModel.findOneAndUpdate(
    { _id: nextPlanOrder._id, status: ORDER_STATUS.PENDING },
    {
      $set: updateQuery,
    },
    { new: true }
  );
}

exports.cancelExistingPlanOrder = async (
  existingPlanOrder,
  formattedCancellationReasons = []
) => {
  if (!existingPlanOrder) {
    throw new ParamError('Plan order not found');
  }

  if (existingPlanOrder.status !== ORDER_STATUS.CURRENT) {
    throw new ParamError('Plan order is not active');
  }

  const paymentBackendRpc = new PaymentBackendRpc();
  await paymentBackendRpc.init();

  const updateQuery = {
    status: ORDER_STATUS.CANCELLED,
    cancelledAt: existingPlanOrder.nextBillingDate,
    unsubscribedAt: new Date(),
  };

  if (formattedCancellationReasons.length) {
    updateQuery.cancellationReasons = formattedCancellationReasons;
  }

  const paymentProvider = existingPlanOrder.paymentDetails.paymentProvider;

  const { paymentProviderSubscriptionId } = existingPlanOrder;

  const cancellationReason = 'User cancelled';

  switch (paymentProvider) {
    case PAYMENT_PROVIDER.STRIPE:
    case PAYMENT_PROVIDER.STRIPE_US:
    case PAYMENT_PROVIDER.STRIPE_INDIA:
      await paymentBackendRpc.cancelStripeSubscription(
        paymentProviderSubscriptionId,
        cancellationReason,
        paymentProvider,
        false,
        true
      );
      break;
    case PAYMENT_PROVIDER.EBANX:
      updateQuery.scheduledCancellation = true;
      break;
    default:
      throw new ParamError(
        `Payment provider ${paymentProvider} not supported`
      );
  }

  const updatedPlanOrder = await PlanOrderModel.findOneAndUpdate(
    { _id: existingPlanOrder._id, status: ORDER_STATUS.CURRENT },
    {
      $set: updateQuery,
    },
    { new: true }
  );

  if (!updatedPlanOrder) {
    throw new ParamError('Plan order cannot be cancelled');
  }
  await handleCancellationBeforeDowngradeOccurs({
    planOrder: updatedPlanOrder,
    paymentBackendRpc,
  });

  await notificationService.sendCancelledPlanNotification({
    planOrderObjectId: updatedPlanOrder._id,
    communityObjectId: updatedPlanOrder.communityObjectId,
    learnerObjectId: updatedPlanOrder.learnerObjectId,
    subscriptionExpiryDate: updatedPlanOrder.cancelledAt,
    failureReason: cancellationReason,
    alreadyCancelled: false,
  });

  return updatedPlanOrder;
};

exports.changePlanOrderInterval = async ({
  planOrder,
  price,
  planObjectId,
  referrer = null,
  session = undefined,
}) => {
  const planHistory = {
    paymentProviderPriceId: planOrder.paymentProviderPriceId,
    intervalCount: planOrder.intervalCount,
    interval: planOrder.interval,
    nextBillingDate: planOrder.nextBillingDate,
    changedDateTime: new Date(),
    billingCycle: planOrder.billingCycle,
    amountInLocalCurrency: planOrder.amountInLocalCurrency,
    localCurrency: planOrder.localCurrency,
  };

  if (planOrder.referrerCommunityObjectId) {
    planHistory.communityReferralCode = planOrder.communityReferralCode;
    planHistory.referrerCommunityObjectId =
      planOrder.referrerCommunityObjectId;
    planHistory.referralRewardTemplateObjectId =
      planOrder.referralRewardTemplateObjectId;
  }

  if (planOrder.previousAmountInLocalCurrency) {
    planHistory.previousAmountInLocalCurrency =
      planOrder.previousAmountInLocalCurrency;
  }

  const newPlan = {
    interval: price.interval,
    intervalCount: price.intervalCount,
    amountInLocalCurrency: price.checkoutAmount,
    paymentProviderPriceId: price.id,
    planObjectId,
  };

  if (referrer) {
    newPlan.communityReferralCode = referrer.communityReferralCode;
    newPlan.referrerCommunityObjectId = referrer.communityObjectId;
    newPlan.referralRewardTemplateObjectId =
      referrer.referralRewardTemplateObjectId;
  }

  const updatedPlanOrder = await PlanOrderModel.findByIdAndUpdate(
    planOrder._id,
    {
      $set: newPlan,
      $unset: {
        previousAmountInLocalCurrency: 1,
        previousLocalCurrency: 1,
      },
      $push: {
        planHistory,
      },
    },
    { new: true, session }
  ).lean();

  if (!updatedPlanOrder) {
    throw new ParamError('Plan order not updated');
  }

  return updatedPlanOrder;
};

exports.updateRenewalAmount = async ({
  planOrder,
  amountInLocalCurrency,
  session = undefined,
}) => {
  const updatedPlanOrder = await PlanOrderModel.updateOne(
    { _id: planOrder._id },
    {
      $set: {
        'paymentDetails.renewalAmount': amountInLocalCurrency,
      },
    },
    { new: true, session }
  ).lean();

  return updatedPlanOrder;
};

exports.transferPlanOrder = async ({
  planOrder,
  transferToCommunityObjectId,
  session = undefined,
}) => {
  await PlanOrderModel.updateOne(
    { _id: planOrder._id },
    {
      $set: {
        communityObjectId: transferToCommunityObjectId,
      },
    },
    { session }
  );
};
