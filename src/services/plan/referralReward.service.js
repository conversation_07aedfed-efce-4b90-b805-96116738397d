const { ObjectId } = require('mongoose').Types;
const { PURCHASE_TYPE } = require('../../constants/common');
const ReferralRewardModel = require('../../models/communityReferral/communityReferralReward.model');
const RevenueTransactionModel = require('../../models/revenueTransaction.model');

exports.transferReferralRewardAndRevenueTransaction = async ({
  planOrder,
  transferFromCommunityObjectId,
  transferToCommunityObjectId,
  session = undefined,
}) => {
  await ReferralRewardModel.updateMany(
    {
      referrerCommunityObjectId: planOrder.referrerCommunityObjectId,
      refereeCommunityObjectId: transferFromCommunityObjectId,
    },
    {
      $set: {
        refereeCommunityObjectId: transferToCommunityObjectId,
      },
    },
    { session }
  );

  await RevenueTransactionModel.updateMany(
    {
      communityObjectId: planOrder.referrerCommunityObjectId,
      'metadata.refereeCommunityObjectId': new ObjectId(
        transferFromCommunityObjectId
      ),
      purchaseType: PURCHASE_TYPE.REFERRAL_REWARD,
    },
    {
      $set: {
        'metadata.refereeCommunityObjectId': new ObjectId(
          transferToCommunityObjectId
        ),
      },
    },
    { session }
  );
};
