exports.getPaymentInfo = async (transaction) => {
  const { metadata = {}, entityType = '', paidCredits = [] } = transaction;

  const {
    interval = '',
    intervalCount,
    isRenewalPayment,
    isTrial,
    paymentMethodDetails,
  } = metadata;
  let totalPaidCreditAmount = 0;
  paidCredits.forEach((credit) => {
    totalPaidCreditAmount += credit.amount;
  });

  const billing = {
    interval,
    intervalCount,
    isRenewalPayment,
    invoiceDate: transaction.transactionCreatedAt,
    description: `${entityType} Subscription renewal`,
    key: `plan-billing-history-subscription-renewal-${entityType.toLowerCase()}-${interval.toLowerCase()}-${intervalCount}`,
    totalPaidCreditAmount,
    paymentMethod: 'card',
    originalAmount: transaction.originalAmount,
    amount: transaction.paidAmount,
    currency: transaction.paidCurrency,
    cardBrand: paymentMethodDetails?.cardBrand?.toLowerCase(),
    cardLast4: paymentMethodDetails?.last4,
  };

  if (isRenewalPayment === false) {
    // To remove interval condition once isOnTrial is backfilled data
    if (isTrial || (intervalCount === 14 && interval === 'day')) {
      billing.description = `${intervalCount} ${interval}${
        intervalCount > 1 ? 's' : ''
      } trial`;
      billing.key = `plan-billing-history-subscription-trial-${entityType.toLowerCase()}-${interval.toLowerCase()}-${intervalCount}`;
    } else {
      billing.description = `${entityType} Subscription paid`;
      billing.key = `plan-billing-history-subscription-${entityType.toLowerCase()}-${interval.toLowerCase()}-${intervalCount}`;
    }
  }

  return billing;
};
