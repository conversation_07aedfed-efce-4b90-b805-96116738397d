exports.getPaymentInfo = async (transaction) => {
  const charges = transaction?.source?.charge ?? {};

  const paymentMethodType = charges.payment_method_details?.type;

  const { metadata = {}, entityType, paidCredits = [] } = transaction;
  const {
    interval = '',
    intervalCount,
    isRenewalPayment,
    isTrial,
  } = metadata;
  let totalPaidCreditAmount = 0;
  paidCredits.forEach((credit) => {
    totalPaidCreditAmount += credit.amount;
  });
  const billing = {
    interval,
    intervalCount,
    isRenewalPayment,
    invoiceDate: transaction.transactionCreatedAt,
    description: `${entityType} Subscription renewal`,
    key: `plan-billing-history-subscription-renewal-${entityType.toLowerCase()}-${interval.toLowerCase()}-${intervalCount}`,
    totalPaidCreditAmount,
    originalAmount: transaction.originalAmount,
    paymentMethod: paymentMethodType,
    amount: transaction.paidAmount,
    currency: transaction.paidCurrency,
  };

  if (isRenewalPayment === false) {
    // To remove interval condition once isTrial is backfilled data
    if (isTrial || (intervalCount === 14 && interval === 'day')) {
      billing.description = `${intervalCount} ${interval}${
        intervalCount > 1 ? 's' : ''
      } trial`;
      billing.key = `plan-billing-history-subscription-trial-${entityType.toLowerCase()}-${interval.toLowerCase()}-${intervalCount}`;
    } else {
      billing.description = `${entityType} Subscription paid`;
      billing.key = `plan-billing-history-subscription-${entityType.toLowerCase()}-${interval.toLowerCase()}-${intervalCount}`;
    }
  }

  const paymentMethodInfo =
    charges.payment_method_details?.[paymentMethodType] ?? {};

  if (paymentMethodType === 'card') {
    billing.cardBrand = paymentMethodInfo.brand?.toLowerCase();
    billing.cardLast4 = paymentMethodInfo.last4;
  } else {
    billing.upiId = paymentMethodInfo.vpa;
  }

  return billing;
};
