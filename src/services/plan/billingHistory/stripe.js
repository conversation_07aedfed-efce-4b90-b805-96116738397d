exports.getPaymentInfo = async (transaction) => {
  let cardInfo;

  const charges = transaction?.source?.paymentIntent?.charges?.data;
  if (charges && charges.length > 0) {
    cardInfo = charges[0].payment_method_details.card;
  } else {
    cardInfo = transaction?.source?.charge?.payment_method_details?.card;
  }

  const { metadata = {}, entityType = '', paidCredits = [] } = transaction;
  const {
    interval = '',
    intervalCount,
    isRenewalPayment,
    isTrial,
  } = metadata;
  let totalPaidCreditAmount = 0;
  paidCredits.forEach((credit) => {
    totalPaidCreditAmount += credit.amount;
  });
  const billing = {
    interval,
    intervalCount,
    isRenewalPayment,
    invoiceDate: transaction.transactionCreatedAt,
    description: `${entityType} Subscription renewal`,
    key: `plan-billing-history-subscription-renewal-${entityType.toLowerCase()}-${interval.toLowerCase()}-${intervalCount}`,
    paymentMethod: 'card',
    cardBrand: cardInfo?.brand?.toLowerCase(),
    cardLast4: cardInfo?.last4,
    totalPaidCreditAmount,
    originalAmount: transaction.originalAmount,
    amount: transaction.paidAmount,
    currency: transaction.paidCurrency,
  };

  if (isRenewalPayment === false) {
    // To remove interval condition once isTrial is backfilled data
    if (isTrial || (intervalCount === 14 && interval === 'day')) {
      billing.description = `${intervalCount} ${interval}${
        intervalCount > 1 ? 's' : ''
      } trial`;
      billing.key = `plan-billing-history-subscription-trial-${entityType.toLowerCase()}-${interval.toLowerCase()}-${intervalCount}`;
    } else {
      billing.description = `${entityType} Subscription paid`;
      billing.key = `plan-billing-history-subscription-${entityType.toLowerCase()}-${interval.toLowerCase()}-${intervalCount}`;
    }
  }

  return billing;
};
