const {
  PAYMENT_PROVIDER,
  PURCHASE_TYPE,
} = require('../../../constants/common');
const stripeService = require('./stripe');
const stripeIndiaService = require('./stripeIndia');
const ebanxService = require('./ebanx');

function getCreditBalanceBillingInfo(transaction) {
  return {
    invoiceDate: transaction.transactionCreatedAt,
    description: 'Unused portion of previous plan',
    key: 'plan-billing-history-inbound-credit',
    amount: transaction.paidAmount,
    currency: transaction.paidCurrency,
  };
}

exports.getBillingHistory = async (transactions) => {
  const billingHistory = [];

  for await (const transaction of transactions) {
    let billing;

    const { paymentProvider, purchaseType } = transaction;
    if (purchaseType === PURCHASE_TYPE.CREDIT_BALANCE) {
      billing = getCreditBalanceBillingInfo(transaction);
    } else {
      switch (paymentProvider) {
        case PAYMENT_PROVIDER.STRIPE:
        case PAYMENT_PROVIDER.STRIPE_US:
          billing = await stripeService.getPaymentInfo(transaction);
          break;
        case PAYMENT_PROVIDER.STRIPE_INDIA:
          billing = await stripeIndiaService.getPaymentInfo(transaction);
          break;
        case PAYMENT_PROVIDER.EBANX:
          billing = await ebanxService.getPaymentInfo(transaction);
          break;
        default:
          break;
      }
    }

    if (billing) {
      billingHistory.push(billing);
    }
  }

  return billingHistory;
};
