const PaymentBackendRpc = require('../../rpc/paymentBackend');
const commonService = require('./common.service');
const planService = require('./plan.service');
const pricingService = require('./pricing.service');
const planOrderService = require('./planOrder.service');
const {
  getCountryInfoByCountryFromDB,
} = require('../countryInfoMapping/countryInfoMapping.service');

const {
  DEFAULT_COUNTRY,
  DEFAULT_CURRENCY,
} = require('../../constants/common');
const { ForbiddenError } = require('../../utils/error.util');
const { GENERIC_ERROR } = require('../../constants/errorCode');

async function retrievePlanRecurringPrices({
  plan,
  localCurrencyEnforced = null,
  countryInfo = null,
  community = null,
  paymentBackendRpc,
}) {
  const nextBillingPrice = await planService.retrieveNextBillingPlan({
    plan,
  });

  const prices = await pricingService.retrievePrices({
    plan: nextBillingPrice ?? plan,
    countryInfo,
    localCurrencyEnforced,
    community,
    paymentBackendRpc,
  });
  return prices;
}

async function formatPricesIfCommunityReferralCodeApplied({
  isCommunityReferralCodeApplied,
  prices,
  entityType,
  countryInfo,
  localCurrencyEnforced = null,
  paymentBackendRpc,
}) {
  if (!isCommunityReferralCodeApplied) {
    return prices;
  }
  const finalReferralCurrency = prices?.[0]?.currency?.toUpperCase();

  const [defaultPlan, defaultPriceCountryInfo] = await Promise.all([
    planService.retrieveDefaultPlan({
      entityType,
    }),
    finalReferralCurrency !== countryInfo?.currencyCode?.toUpperCase() &&
    finalReferralCurrency === DEFAULT_CURRENCY
      ? getCountryInfoByCountryFromDB(DEFAULT_COUNTRY)
      : countryInfo,
  ]);

  const defaultPrices = await retrievePlanRecurringPrices({
    plan: defaultPlan,
    countryInfo: defaultPriceCountryInfo,
    localCurrencyEnforced,
    paymentBackendRpc,
  });

  const defaultPriceMap = new Map();
  defaultPrices.forEach((price) => {
    const { interval, intervalCount, currency } = price;
    defaultPriceMap.set(`${interval}-${intervalCount}-${currency}`, price);
  });

  const finalPrices = await Promise.all(
    prices.map((price) => {
      const { currency } = price;
      const finalPrice = price;
      const recurringPrice = price.nextBillingPrice
        ? price.nextBillingPrice
        : price;
      const { interval, intervalCount } = recurringPrice;
      const defaultPrice = defaultPriceMap.get(
        `${interval}-${intervalCount}-${currency}`
      );
      if (defaultPrice) {
        if (price.nextBillingPrice) {
          finalPrice.nextBillingPrice.originalAmount =
            defaultPrice.checkoutAmount;
        } else {
          finalPrice.originalAmount = defaultPrice.checkoutAmount;
        }
      }
      return finalPrice;
    })
  );
  return finalPrices;
}

async function retrievePlanPricesForNonSubscribers({
  community,
  countryInfo,
  entityType,
  paymentBackendRpc,
  communityReferralCode,
  localCurrencyEnforced = null,
}) {
  const lastPurchasedOrder =
    await planOrderService.retrieveLastPurchasePlanOrder({
      communityObjectId: community._id,
    });

  const { plan, isCommunityReferralCodeApplied, referrer } =
    await planService.retrieveAvailablePlanForNonSubscribers({
      entityType,
      hasSubscribedBefore: !!lastPurchasedOrder,
      communityReferralCode,
      community,
    });

  let finalPrices = await pricingService.retrievePrices({
    plan,
    localCurrencyEnforced,
    community,
    countryInfo,
    paymentBackendRpc,
  });

  if (isCommunityReferralCodeApplied) {
    finalPrices = await formatPricesIfCommunityReferralCodeApplied({
      isCommunityReferralCodeApplied,
      localCurrencyEnforced,
      prices: finalPrices,
      entityType,
      countryInfo,
      paymentBackendRpc,
    });
  }

  return {
    plan,
    planPrices: finalPrices,
    isCommunityReferralCodeApplied,
    referrer,
  };
}

function replaceCurrentIntervalWithCurrentPrice({
  latestPlanPrices,
  latestPlan,
  currentPlanPrices,
  currentPlan,
}) {
  const actualCurrentPlanPrice = currentPlanPrices.find(
    (planPrice) => planPrice.isCurrentPrice
  );
  const finalPrices = latestPlanPrices.map((planPrice) => {
    if (
      planPrice.interval === actualCurrentPlanPrice.interval &&
      planPrice.intervalCount === actualCurrentPlanPrice.intervalCount
    ) {
      return { ...actualCurrentPlanPrice, planObjectId: currentPlan._id };
    }
    return { ...planPrice, planObjectId: latestPlan._id };
  });
  return finalPrices;
}

async function retrievePlanPricesReferralFlowForExistingPlanOrder({
  existingPlanOrder,
  community,
  entityType,
  countryInfo,
  paymentBackendRpc,
  currentPlanPrices,
  currentPlan,
}) {
  const { communityReferralCode, localCurrency: localCurrencyEnforced } =
    existingPlanOrder;
  const refereeCommunityObjectId = community._id;
  // Handle existing plans with referral Codes

  let referralDetails = {};
  try {
    referralDetails = await planService.retrieveCommunityReferralDetails({
      communityReferralCode,
      planType: entityType,
      refereeCommunityObjectId,
    });
  } catch (err) {
    if (err.name === GENERIC_ERROR.INVALID_COMMUNITY_REFERRAL_LINK.name) {
      return {};
    }
    throw err;
  }

  const { referrerCommunity, referralRewardTemplate } = referralDetails;
  let latestReferralPlan = referralDetails.plan;
  if (latestReferralPlan.nextBillingPlanObjectId) {
    // Ensure the trial prices is not returned for existing users
    latestReferralPlan = await planService.retrieveNextBillingPlan({
      plan: latestReferralPlan,
    });
  }
  const latestReferralPlanPrices = await pricingService.retrievePrices({
    plan: latestReferralPlan,
    community,
    countryInfo,
    localCurrencyEnforced,
    paymentBackendRpc,
  });

  const finalPrices = replaceCurrentIntervalWithCurrentPrice({
    latestPlanPrices: latestReferralPlanPrices,
    latestPlan: latestReferralPlan,
    currentPlanPrices,
    currentPlan,
  });

  const formattedPrices = await formatPricesIfCommunityReferralCodeApplied(
    {
      isCommunityReferralCodeApplied: true,
      prices: finalPrices,
      entityType,
      countryInfo,
      localCurrencyEnforced,
      paymentBackendRpc,
    }
  );

  return {
    plan: latestReferralPlan,
    planPrices: formattedPrices,
    isCommunityReferralCodeApplied: true,
    referrer: {
      title: referrerCommunity.title,
      link: referrerCommunity.link,
      profileImage:
        referrerCommunity.thumbnailImgData?.desktopImgData?.src,
      communityObjectId: referrerCommunity._id,
      referralRewardTemplateObjectId: referralRewardTemplate._id,
      communityReferralCode,
    },
  };
}

async function retrieveActivePlanPricesForExistingPlanOrder({
  community,
  communityReferralCode,
  countryInfo,
  existingPlanOrder,
  entityType,
  paymentBackendRpc,
}) {
  let currentPlan = await planService.retrievePlanFromPlanObjectId(
    existingPlanOrder.planObjectId,
    null
  );

  if (currentPlan.nextBillingPlanObjectId) {
    // Ensure the trial prices is not returned for existing users
    const nextBillingPlan = await planService.retrieveNextBillingPlan({
      isActive: null,
      plan: currentPlan,
      throwError: false,
    });

    if (nextBillingPlan) {
      currentPlan = nextBillingPlan;
    }
  }

  if (!currentPlan) {
    return { plan: null };
  }

  const currentPlanPrices = await pricingService.retrieveSpecificPrices({
    plan: currentPlan,
    planOrder: existingPlanOrder,
    paymentBackendRpc,
  });

  const hasExistingReferral = !!(
    existingPlanOrder.referralRewardTemplateObjectId &&
    existingPlanOrder.communityReferralCode
  );

  const hasNewCommunityReferralCode =
    communityReferralCode != null
      ? existingPlanOrder.communityReferralCode !== communityReferralCode
      : false;

  // Handle has existing referral code that's still valid flow
  if (hasExistingReferral && !hasNewCommunityReferralCode) {
    const referralPlanPricesDetails =
      await retrievePlanPricesReferralFlowForExistingPlanOrder({
        existingPlanOrder,
        community,
        entityType,
        countryInfo,
        paymentBackendRpc,
        currentPlanPrices,
        currentPlan,
      });
    if (referralPlanPricesDetails.isCommunityReferralCodeApplied) {
      return referralPlanPricesDetails;
    }
  }

  // Return plan prices as it is if existing plan is not under referral reward template
  if (
    !hasExistingReferral &&
    !communityReferralCode &&
    currentPlan.isActive
  ) {
    return {
      plan: currentPlan,
      planPrices: currentPlanPrices,
    };
  }

  // Return default flow if no referral + currentPlan is inActive
  const { plan, planPrices, isCommunityReferralCodeApplied, referrer } =
    await retrievePlanPricesForNonSubscribers({
      community,
      countryInfo,
      entityType,
      paymentBackendRpc,
      communityReferralCode,
      localCurrencyEnforced: existingPlanOrder.localCurrency,
    });

  const finalPrices = replaceCurrentIntervalWithCurrentPrice({
    latestPlanPrices: planPrices,
    latestPlan: plan,
    currentPlanPrices,
    currentPlan,
  });

  return {
    plan,
    planPrices: finalPrices,
    isCommunityReferralCodeApplied,
    referrer,
  };
}

exports.retrievePlanPrices = async ({
  countryInfo,
  community,
  existingPlanOrder,
  entityType,
  email,
  communityReferralCode,
}) => {
  const paymentBackendRpc = new PaymentBackendRpc();

  const [isManagerRole] = await Promise.all([
    commonService.isManagerRole({
      communityObjectId: community._id,
      email,
    }),
    paymentBackendRpc.init(),
  ]);

  if (!isManagerRole) {
    throw new ForbiddenError('Only managers can have access');
  }

  const alreadySubscribed = !!existingPlanOrder;

  if (existingPlanOrder && existingPlanOrder.entityType === entityType) {
    const { plan, planPrices, isCommunityReferralCodeApplied, referrer } =
      await retrieveActivePlanPricesForExistingPlanOrder({
        community,
        countryInfo,
        existingPlanOrder,
        entityType,
        paymentBackendRpc,
        communityReferralCode,
      });

    if (plan) {
      return {
        plan,
        planPrices,
        alreadySubscribed,
        isCommunityReferralCodeApplied,
        referrer,
      };
    }
  }

  // If there is no existing plan order that is of the specified entityType,
  // Or if the current plan is no longer active
  // Return referral prices if communityReferralCode is provided
  // Otherwise return default prices.
  const { plan, planPrices, isCommunityReferralCodeApplied, referrer } =
    await retrievePlanPricesForNonSubscribers({
      community,
      countryInfo,
      entityType,
      paymentBackendRpc,
      communityReferralCode,
      localCurrencyEnforced: existingPlanOrder
        ? existingPlanOrder.localCurrency
        : null,
    });

  return {
    plan,
    planPrices,
    alreadySubscribed,
    isCommunityReferralCodeApplied,
    referrer,
  };
};
