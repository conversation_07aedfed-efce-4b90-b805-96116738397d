const PlanService = require('../plan.service');
const WalletService = require('../../wallet/wallet.service');

const PlanOrderModel = require('../../../models/plan/communityPlanOrder.model');
const {
  normalizeAndRoundAmountByCurrency,
} = require('../../../utils/currency.util');
const { ParamError } = require('../../../utils/error.util');

async function getExchangeRateToPaidCurrency({
  paymentBackendRpc,
  from,
  to,
}) {
  const conversionRateData = await paymentBackendRpc.getConversionRate(
    from,
    to
  );
  let exchangeRate = conversionRateData.conversionRate;
  if (exchangeRate !== 1) {
    // The idea is to ensure that users pays more
    // Therefore lower rates for credit to paidCurrrency
    exchangeRate *= 1 - 0.03;
  }
  return exchangeRate;
}

async function getCreditBreakdown({
  originalAmount,
  paidCurrency,
  communityCreditBalance = {},
  creditsToExclude = [],
  paymentBackendRpc,
}) {
  const creditWallet = { ...communityCreditBalance };
  creditsToExclude.forEach((credit) => {
    const { creditCurrency, creditAmount } = credit;
    const existingAmount = creditWallet[creditCurrency] ?? 0;
    if (existingAmount > creditAmount) {
      creditWallet[creditCurrency] = existingAmount - creditAmount;
    } else {
      creditWallet[creditCurrency] = 0;
    }
  });
  // Assuming original amount is 100 SGD
  // Wallet has 50 SGD and 70 USD credits
  // Paid Credits: 50 SGD + (50 SGD taken from usd wallet)

  const paidCredits = [];
  let remainingAmountToBePaid = originalAmount;

  if (creditWallet[paidCurrency] && remainingAmountToBePaid > 0) {
    let creditAmount = 0;
    if (remainingAmountToBePaid >= creditWallet[paidCurrency]) {
      // use up all credits
      creditAmount = creditWallet[paidCurrency];
    } else {
      creditAmount = remainingAmountToBePaid; // 50
    }
    paidCredits.push({
      creditAmount,
      creditCurrency: paidCurrency,
      exchangeRate: 1,
      currency: paidCurrency,
      amount: creditAmount,
    });
    remainingAmountToBePaid -= creditAmount;
  }

  if (remainingAmountToBePaid > 0) {
    const currencies = Object.keys(creditWallet);
    await Promise.all(
      currencies.map(async (creditCurrency) => {
        if (
          creditCurrency === paidCurrency ||
          remainingAmountToBePaid <= 0
        ) {
          return;
        }
        // If paidCurrency is SGD and wallet is in USD
        // Exchange rate to paidCurrency should be [USD>SGD*(1-0.03)]
        const exchangeRateToPaidCurrency =
          await getExchangeRateToPaidCurrency({
            paymentBackendRpc,
            from: creditCurrency,
            to: paidCurrency,
          });

        const creditBalance = creditWallet[creditCurrency];
        const creditBalanceInPaidCurrency =
          normalizeAndRoundAmountByCurrency(
            creditBalance * exchangeRateToPaidCurrency,
            paidCurrency
          );

        let amount = 0;
        let creditAmount = 0;
        if (remainingAmountToBePaid >= creditBalanceInPaidCurrency) {
          creditAmount = creditBalance;
          amount = creditBalanceInPaidCurrency;
        } else if (remainingAmountToBePaid < creditBalanceInPaidCurrency) {
          creditAmount = normalizeAndRoundAmountByCurrency(
            remainingAmountToBePaid * (1 / exchangeRateToPaidCurrency),
            creditCurrency
          );
          amount = remainingAmountToBePaid;
        }
        paidCredits.push({
          creditCurrency,
          creditAmount,
          exchangeRate: exchangeRateToPaidCurrency,
          currency: paidCurrency,
          amount,
        });
        remainingAmountToBePaid -= amount;
      })
    );
  }
  return {
    originalAmount,
    paidAmount: remainingAmountToBePaid,
    paidCreditAmount: originalAmount - remainingAmountToBePaid,
    paidCredits,
  };
}

exports.getBillingCycleCredit = async ({
  billingCycle,
  isProratedAmountIncluded = false,
  originalAmount,
  paidCurrency,
  creditWallet,
  paymentBackendRpc,
}) => {
  const creditInfo = await getCreditBreakdown({
    originalAmount,
    paidCurrency,
    communityCreditBalance: creditWallet,
    paymentBackendRpc,
  });

  if (creditInfo.paidCreditAmount > 0) {
    return {
      billingCycle,
      isProratedAmountIncluded,
      ...creditInfo,
    };
  }
  return null;
};

exports.retrievePlanOrderWithCreditHandling = async ({
  planOrder,
  curentPriceInfo = null,
  overwriteNextBillingCreditHistory = false,
  paymentBackendRpc,
  session,
}) => {
  const nextBillingCycle = planOrder.billingCycle + 1;
  const creditHistory = planOrder.creditHistory ?? [];
  const existingNextBillingCredits = creditHistory.find(
    (credit) => credit.billingCycle === nextBillingCycle
  );

  if (existingNextBillingCredits && !overwriteNextBillingCreditHistory) {
    // No need to recalculate and reassign prices
    return planOrder;
  }
  const creditWallet = await WalletService.getCreditBalance(
    planOrder.communityObjectId,
    planOrder.learnerObjectId
  );

  const {
    amountInLocalCurrency: originalAmount,
    localCurrency: paidCurrency,
  } = planOrder;

  const nextBillingCredits = await this.getBillingCycleCredit({
    billingCycle: nextBillingCycle,
    originalAmount,
    paidCurrency,
    creditWallet,
    paymentBackendRpc,
  });

  const nextBillingPaidAmount = nextBillingCredits
    ? nextBillingCredits.paidAmount
    : originalAmount;

  const updateQuery = { $set: {} };
  const arrayFilters = [];
  if (nextBillingCredits && nextBillingCredits.paidCreditAmount !== 0) {
    if (existingNextBillingCredits) {
      updateQuery.$set = {
        'creditHistory.$[element].paidCredits':
          nextBillingCredits.paidCredits,
        'creditHistory.$[element].originalAmount':
          nextBillingCredits.originalAmount,
        'creditHistory.$[element].paidAmount':
          nextBillingCredits.paidAmount,
        'creditHistory.$[element].paidCreditAmount':
          nextBillingCredits.paidCreditAmount,
        'creditHistory.$[element].isProratedAmountIncluded':
          nextBillingCredits.isProratedAmountIncluded,
      };
      arrayFilters.push({ 'element.billingCycle': nextBillingCycle });
    } else {
      updateQuery.$push = { creditHistory: nextBillingCredits };
    }
  }

  const currentPrice = curentPriceInfo?.amount ?? originalAmount;
  if (currentPrice !== nextBillingPaidAmount) {
    const { interval, intervalCount, entityType } = planOrder;
    const originalPlan = await PlanService.retrievePlan({
      planObjectId: planOrder.planObjectId,
      entityType,
    });
    const { priceId: newPriceId, plan: newPlan } =
      await PlanService.createCustomiseCreditPlan({
        title: `${planOrder.type}_CREDIT_APPLIED - ${planOrder._id}-${nextBillingCycle}`,
        entityType,
        interval,
        intervalCount,
        defaultAmount: nextBillingCredits.paidAmount,
        defaultCurrency: planOrder.localCurrency,
        originalPriceId: planOrder.paymentProviderPriceId,
        originalAmount: planOrder.amountInLocalCurrency,
        originalPlan,
        nextBillingPlanObjectId: originalPlan._id,
        paymentProvider: planOrder.paymentProviderForPriceId,
        paymentBackendRpc,
      });

    if (newPriceId !== planOrder.paymentProviderPriceId) {
      updateQuery.$set.paymentProviderPriceId = newPriceId;
      updateQuery.$set.planObjectId = newPlan._id;
    }
  }

  const updatedPlanOrder = await PlanOrderModel.findByIdAndUpdate(
    planOrder._id,
    updateQuery,
    { new: true, arrayFilters, session }
  ).lean();

  if (!updatedPlanOrder) {
    throw new ParamError('Plan order not updated');
  }

  return updatedPlanOrder;
};
