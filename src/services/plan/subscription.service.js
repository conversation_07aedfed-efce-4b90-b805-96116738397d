const CommunityModel = require('../../communitiesAPI/models/community.model');
const LearnerModel = require('../../models/learners.model');
const SubscriptionModel = require('../../communitiesAPI/models/communitySubscriptions.model');
const PlanOrderModel = require('../../models/plan/communityPlanOrder.model');
const PlanAdhocRecordModel = require('../../models/plan/communityPlanAdhocRecords.model');
const commonService = require('./common.service');

const {
  removeMember,
} = require('../../communitiesAPI/services/common/communityManager.service');

const { ParamError } = require('../../utils/error.util');
const {
  PLAN_ADHOC_STATUS,
  ORDER_STATUS,
  ENTITY_TYPE,
  NASIO_PRO_COMMUNITY_CODE,
  NASIO_AI_SCHOOL_COMMUNITY_CODE,
} = require('./constants');
const {
  COMMUNITY_SUBSCRIPTION_STATUSES,
} = require('../../constants/common');

async function shouldStayInProCommunity({
  email,
  currentCommunityObjectId,
}) {
  const learner = await LearnerModel.findOne({
    email,
    isActive: true,
  }).lean();

  if (!learner) {
    return false;
  }

  const communityWithPlan = await CommunityModel.findOne({
    'config.planType': { $in: [ENTITY_TYPE.PRO, ENTITY_TYPE.PLATINUM] },
    createdBy: email,
    _id: { $ne: currentCommunityObjectId },
  }).lean();

  if (communityWithPlan) {
    return true;
  }

  const otherPlan = await PlanOrderModel.findOne({
    learnerObjectId: learner._id,
    $or: [
      { status: ORDER_STATUS.CURRENT },
      { status: ORDER_STATUS.CANCELLED, cancelledAt: { $gt: new Date() } },
    ],
    communityObjectId: { $ne: currentCommunityObjectId },
  }).lean();

  if (otherPlan) {
    return true;
  }
  const otherAdhocPlan = await PlanAdhocRecordModel.findOne({
    creatorEmail: email,
    status: PLAN_ADHOC_STATUS.ACTIVE,
    communityObjectId: { $ne: currentCommunityObjectId },
  }).lean();

  if (otherAdhocPlan) {
    return true;
  }

  return false;
}

async function unsubscribeFromAiSchoolCommunity({
  community,
  learner,
  removalReason,
}) {
  const ignoreManager = true;

  // Only remove free subscription
  const subscription = await SubscriptionModel.findOne({
    communityCode: community.code,
    learnerObjectId: learner._id,
    status: COMMUNITY_SUBSCRIPTION_STATUSES.CURRENT,
    stripeSubscriptionId: { $exists: false },
  }).lean();

  if (!subscription) {
    return;
  }

  await removeMember(
    {
      communityId: community._id,
      community,
      learnerId: learner.learnerId,
      learner,
      removalReason,
    },
    ignoreManager
  );
}

async function unsubscribeOneEmailFromProCommunity({
  removeMemberParams = {},
  currentCommunityObjectId,
  email,
}) {
  const shouldStayInPro = await shouldStayInProCommunity({
    email,
    currentCommunityObjectId,
  });

  if (!shouldStayInPro) {
    const ignoreManager = true;
    await removeMember(removeMemberParams, ignoreManager);
  }

  return !shouldStayInPro;
}

exports.removeLearnerAndCommOwnerFromProCommunity = async ({
  purchaserLearnerObjectId,
  communityObjectId,
  options = {},
}) => {
  // get community and validate
  const [community, nasioProCommunity, nasioAiSchoolCommunity] =
    await Promise.all([
      commonService.retrieveActiveCommunity(communityObjectId),
      commonService.retrieveCommunityByCode(NASIO_PRO_COMMUNITY_CODE),
      commonService.retrieveCommunityByCode(
        NASIO_AI_SCHOOL_COMMUNITY_CODE
      ),
    ]);

  if (!community) {
    throw new ParamError(
      `Community not found for communityObjectId=${communityObjectId}`
    );
  }

  if (!nasioProCommunity) {
    throw new ParamError(
      `Community not found for communityCode=${NASIO_PRO_COMMUNITY_CODE}`
    );
  }

  if (!nasioAiSchoolCommunity) {
    throw new ParamError(
      `Community not found for communityCode=${NASIO_AI_SCHOOL_COMMUNITY_CODE}`
    );
  }

  const nasioProCommunityId = nasioProCommunity._id;

  const removalReason =
    options?.removalReason || 'Community pro plan cancelled';

  // common params to remove member from pro community.
  const removeFromProCommunityCommonParams = {
    communityId: nasioProCommunityId,
    removalReason,
  };

  // remove community owner from pro community
  const communityOwnerEmail = community.createdBy;
  const communityOwnerLearner = await commonService.retrieveLearnerByEmail(
    communityOwnerEmail
  );
  const communityOwnerRemovedFromProCommunity =
    await unsubscribeOneEmailFromProCommunity({
      removeMemberParams: {
        ...removeFromProCommunityCommonParams,
        learnerId: communityOwnerLearner.learnerId,
      },
      currentCommunityObjectId: communityObjectId,
      email: communityOwnerEmail,
    });

  // remove pro purchaser as well if purchaser is different from community owner
  if (
    communityOwnerLearner._id.toString() !==
    purchaserLearnerObjectId.toString()
  ) {
    // get purchasing learner
    const purchaserLearner = await commonService.retrieveLearner(
      purchaserLearnerObjectId
    );

    // remove member
    const purchaserRemovedFromProCommunity =
      await unsubscribeOneEmailFromProCommunity({
        removeMemberParams: {
          ...removeFromProCommunityCommonParams,
          learnerId: purchaserLearner.learnerId,
        },
        currentCommunityObjectId: communityObjectId,
        email: purchaserLearner.email,
      });

    if (purchaserRemovedFromProCommunity) {
      await unsubscribeFromAiSchoolCommunity({
        community: nasioAiSchoolCommunity,
        learner: purchaserLearner,
        removalReason,
      });
    }
  } else if (communityOwnerRemovedFromProCommunity) {
    await unsubscribeFromAiSchoolCommunity({
      community: nasioAiSchoolCommunity,
      learner: communityOwnerLearner,
      removalReason,
    });
  }

  return { success: true };
};
