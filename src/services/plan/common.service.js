const CommunityModel = require('../../communitiesAPI/models/community.model');
const RoleModel = require('../../communitiesAPI/models/communityRole.model');
const LearnerModel = require('../../models/learners.model');

const { aclRoles } = require('../../communitiesAPI/constants');
const { ParamError } = require('../../utils/error.util');

exports.retrieveActiveCommunity = async (communityObjectId) => {
  const community = await CommunityModel.findOne({
    _id: communityObjectId,
    isActive: true,
  }).lean();

  if (!community) {
    throw new ParamError('Community not found');
  }

  return community;
};

exports.retrieveCommunityByCode = async (communityCode) => {
  const community = await CommunityModel.findOne({
    code: communityCode,
    isActive: true,
  }).lean();

  if (!community) {
    throw new Error(`Community not found ${communityCode}`);
  }

  return community;
};

exports.isManagerRole = async ({ communityObjectId, email }) => {
  const communityRole = await RoleModel.findOne({
    communityObjectId,
    role: aclRoles.MANAGER,
    email,
  }).lean();

  return !!communityRole;
};

exports.retrieveLearner = async (learnerObjectId) => {
  const learner = await LearnerModel.findById(learnerObjectId).lean();

  if (!learner) {
    throw new ParamError('Learner not found');
  }

  return learner;
};

exports.retrieveLearnerByEmail = async (email) => {
  const learner = await LearnerModel.findOne({
    email,
    isActive: true,
  }).lean();

  if (!learner) {
    throw new ParamError('Learner not found');
  }

  return learner;
};
