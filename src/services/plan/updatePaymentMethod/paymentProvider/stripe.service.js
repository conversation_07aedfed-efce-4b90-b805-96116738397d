const { ParamError } = require('../../../../utils/error.util');
const {
  PAYMENT_RESPONSE_ACTION,
} = require('../../../../constants/common');

exports.updatePaymentMethod = async ({
  planOrder,
  metadata,
  paymentBackendRpc,
}) => {
  const { paymentMethodId } = metadata;
  const { paymentProviderSubscriptionId, paymentDetails } = planOrder;
  const { paymentProvider } = paymentDetails;

  if (!paymentMethodId) {
    throw new ParamError('Missing payment method id');
  }

  await paymentBackendRpc.updateStripePaymentMethod(
    paymentProviderSubscriptionId,
    paymentMethodId,
    paymentProvider
  );

  return {
    action: PAYMENT_RESPONSE_ACTION.NONE,
    success: true,
  };
};
