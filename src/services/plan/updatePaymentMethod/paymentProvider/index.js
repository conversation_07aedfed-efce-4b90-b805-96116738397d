const stripeService = require('./stripe.service');
const stripeIndiaService = require('./stripeIndia.service');
const ebanxService = require('./ebanx.service');
const { PAYMENT_PROVIDER } = require('../../../../constants/common');
const { ParamError } = require('../../../../utils/error.util');
const PaymentBackendRpc = require('../../../../rpc/paymentBackend');

exports.updatePaymentMethod = async ({ planOrder, metadata }) => {
  const paymentBackendRpc = new PaymentBackendRpc();
  await paymentBackendRpc.init();

  const paymentProvider = planOrder.paymentDetails.paymentProvider;

  switch (paymentProvider) {
    case PAYMENT_PROVIDER.STRIPE:
    case PAYMENT_PROVIDER.STRIPE_US:
      return stripeService.updatePaymentMethod({
        planOrder,
        metadata,
        paymentBackendRpc,
      });
    case PAYMENT_PROVIDER.STRIPE_INDIA:
      return stripeIndiaService.updatePaymentMethod({
        planOrder,
        metadata,
        paymentBackendRpc,
      });
    case PAYMENT_PROVIDER.EBANX:
      return ebanxService.updatePaymentMethod({
        planOrder,
        metadata,
      });
    default:
      throw new ParamError(`Does not support ${paymentProvider}`);
  }
};
