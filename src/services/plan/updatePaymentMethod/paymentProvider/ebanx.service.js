const { ResourceNotFoundError } = require('../../../../utils/error.util');
const paymentProviderService = require('../../../communitySignup/signupConfirm/paymentProvider');
const UserPaymentTokenModel = require('../../../../models/userPaymentToken.model');
const PlanOrderModel = require('../../../../models/plan/communityPlanOrder.model');
const {
  USER_PAYMENT_TOKEN_STATUS,
  PAYMENT_RESPONSE_ACTION,
} = require('../../../../constants/common');

exports.updatePaymentMethod = async ({ planOrder, metadata }) => {
  const { learnerObjectId, paymentDetails, localCurrency } = planOrder;
  const { userPaymentTokenId } = paymentDetails;

  // Set current payment token to be inactive
  const inactiveUserPaymentToken =
    await UserPaymentTokenModel.findByIdAndUpdate(
      userPaymentTokenId,
      { status: USER_PAYMENT_TOKEN_STATUS.INACTIVE },
      { new: true }
    ).lean();

  if (!inactiveUserPaymentToken) {
    throw new ResourceNotFoundError('Payment token not found');
  }

  // eslint-disable-next-line no-param-reassign
  metadata.name = inactiveUserPaymentToken.metadata.name;
  // eslint-disable-next-line no-param-reassign
  metadata.countryCode = inactiveUserPaymentToken.metadata.countryCode;

  const userPaymentToken =
    await paymentProviderService.EbanxService.createOrSavePaymentToken(
      learnerObjectId,
      metadata,
      localCurrency
    );

  await PlanOrderModel.updateOne(
    { _id: planOrder._id },
    {
      'paymentDetails.userPaymentTokenId': userPaymentToken._id,
    }
  );

  return {
    action: PAYMENT_RESPONSE_ACTION.NONE,
    success: true,
  };
};
