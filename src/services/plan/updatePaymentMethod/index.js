const paymentProviderService = require('./paymentProvider');
const planOrderService = require('../planOrder.service');
const validationService = require('../validation.service');

exports.updateSubscriptionPaymentMethodForPlan = async ({
  learnerObjectId,
  communityObjectId,
  metadata,
}) => {
  const planOrder = await planOrderService.retrieveExistingPlanOrder({
    communityObjectId,
  });

  validationService.checkIfCanChangePaymentMethod({
    planOrder,
    learnerObjectId,
    throwError: true,
  });

  const result = await paymentProviderService.updatePaymentMethod({
    planOrder,
    metadata,
  });

  return result;
};
