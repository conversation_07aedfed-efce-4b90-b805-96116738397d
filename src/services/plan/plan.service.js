const ObjectId = require('mongoose').Types.ObjectId;

const CommunityModel = require('../../communitiesAPI/models/community.model');
const CommunityRoleModel = require('../../communitiesAPI/models/communityRole.model');
const CommunityReferralRewardTemplateModel = require('../../models/communityReferral/communityReferralRewardTemplate.model');
const PlanModel = require('../../models/plan/communityPlan.model');

const logger = require('../logger.service');

const {
  InternalError,
  ParamError,
  ToUserError,
} = require('../../utils/error.util');
const { GENERIC_ERROR } = require('../../constants/errorCode');
const { aclRoles } = require('../../communitiesAPI/constants');
const CommunityPlanModel = require('../../models/plan/communityPlan.model');
const { getConfigByTypeFromCache } = require('../config.service');
const {
  CONFIG_TYPES,
  PAYMENT_PROVIDER,
} = require('../../constants/common');

const getReferrerFromCommunityReferralCode = async (
  communityReferralCode
) => {
  const community = await CommunityModel.findOne(
    {
      isActive: true,
      communityReferralCode,
    },
    {
      _id: 1,
      title: 1,
      link: 1,
      isDemo: 1,
      restrictedInfo: 1,
      code: 1,
      thumbnailImgData: 1,
    }
  ).lean();
  return community;
};

exports.validateReferralCommunity = async (
  community,
  refereeCommunityObjectId = null
) => {
  try {
    if (!community) {
      throw new ParamError(`Community not found`);
    }
    const { isDemo, restrictedInfo } = community;
    if (isDemo) {
      throw new ParamError('Community is demo');
    }
    // To double check the fraud check w the team
    if (restrictedInfo?.checkout || restrictedInfo?.freeSubscribe) {
      throw new ParamError('Referral Community is currently restricted');
    }
  } catch (err) {
    throw new ToUserError(
      'This referral link is not valid',
      GENERIC_ERROR.INVALID_COMMUNITY_REFERRAL_LINK
    );
  }

  if (refereeCommunityObjectId) {
    const [referrerOwner, refereeOwner] = await Promise.all([
      CommunityRoleModel.findOne(
        {
          communityObjectId: community._id,
          role: aclRoles.OWNER,
        },
        { email: 1 }
      ).lean(),
      CommunityRoleModel.findOne(
        {
          communityObjectId: new ObjectId(
            refereeCommunityObjectId.toString()
          ),
          role: aclRoles.OWNER,
        },
        { email: 1 }
      ).lean(),
    ]);

    if (referrerOwner?.email === refereeOwner?.email) {
      throw new ToUserError(
        'You cannot use referralCode that belongs to the referrer community',
        GENERIC_ERROR.CANNOT_USE_OWN_COMMUNITY_REFERRAL_LINK
      );
    }
  }

  return community;
};

exports.getLatestReferralTemplate = async ({
  planType,
  referrerCommunityObjectId,
}) => {
  const currentDate = new Date();
  let referralRewardTemplate =
    await CommunityReferralRewardTemplateModel.findOne({
      planType,
      isDisabled: false,
      effectiveTimeStart: { $lte: currentDate },
      $or: [
        { effectiveTimeEnd: { $gt: currentDate } },
        { effectiveTimeEnd: null },
      ],
      applyToAll: false,
      communityObjectIds: referrerCommunityObjectId,
    })
      .sort({ createdAt: -1 })
      .lean();

  if (!referralRewardTemplate) {
    // Retrieve active default template
    // Default template does not have effective time start and time end
    referralRewardTemplate =
      await CommunityReferralRewardTemplateModel.findOne({
        planType,
        isDisabled: false,
        applyToAll: true,
      })
        .sort({ createdAt: -1 })
        .lean();
  }
  return referralRewardTemplate;
};

exports.retrieveCommunityReferralDetails = async ({
  communityReferralCode,
  planType,
  referrerCommunity = null,
  refereeCommunityObjectId = null,
}) => {
  if (
    referrerCommunity &&
    referrerCommunity.communityReferralCode !== communityReferralCode
  ) {
    throw new InternalError(
      'Do not pass in referrerCommunity if communityReferralCode does not match with input field'
    );
  }
  let finalReferrerCommunity = referrerCommunity;
  if (!finalReferrerCommunity) {
    finalReferrerCommunity = await getReferrerFromCommunityReferralCode(
      communityReferralCode
    );
  }
  await this.validateReferralCommunity(
    finalReferrerCommunity,
    refereeCommunityObjectId
  );

  const referralRewardTemplate = await this.getLatestReferralTemplate({
    planType,
    referrerCommunityObjectId: finalReferrerCommunity._id,
  });

  if (!referralRewardTemplate) {
    throw new InternalError(
      `Default referral reward template does not exist for plan type: ${planType}`
    );
  }

  const plan = await PlanModel.findOne({
    _id: referralRewardTemplate.planObjectId,
    isActive: true,
  }).lean();

  if (!plan) {
    logger.info(
      `Plan ${referralRewardTemplate.planObjectId} for referral reward template ${referralRewardTemplate._id} does not exist`
    );
  }

  return {
    referrerCommunity: finalReferrerCommunity,
    plan,
    referralRewardTemplate,
    communityReferralCode,
  };
};

exports.retrieveNextBillingPlan = async ({
  plan,
  isActive = true,
  throwError = true,
}) => {
  if (!plan.nextBillingPlanObjectId) {
    return null;
  }
  const query = {
    _id: plan.nextBillingPlanObjectId,
  };

  if (isActive) {
    query.isActive = isActive;
  }

  const nextBillingPlan = await PlanModel.findOne(query).lean();

  if (!nextBillingPlan && throwError) {
    throw new ParamError('Next billing plan not found');
  }
  return nextBillingPlan;
};

exports.retrieveDefaultPlan = async ({ entityType }) => {
  const matchFilter = {
    isDefault: true,
    entityType,
    isActive: true,
  };

  const plan = await PlanModel.findOne(matchFilter).lean();

  if (!plan) {
    throw new InternalError(`Default Plan not found for ${entityType}`);
  }

  return plan;
};

exports.retrievePlanFromPlanObjectId = async (
  planObjectId,
  isActive = true
) => {
  const matchFilter = {
    _id: planObjectId,
  };

  if (isActive != null) {
    matchFilter.isActive = isActive;
  }

  const plan = await PlanModel.findOne(matchFilter).lean();

  if (!plan) {
    throw new ParamError(`Plan not found for ${planObjectId}`);
  }

  return plan;
};

exports.retrievePlan = async ({ entityType, planObjectId }) => {
  const matchFilter = {
    _id: planObjectId,
    entityType,
    isActive: true,
  };

  const plan = await PlanModel.findOne(matchFilter).lean();

  if (!plan) {
    throw new InternalError(
      `Plan not found for ${planObjectId} ${entityType}`
    );
  }

  return plan;
};

/**
 * This function is only called in retrievePlanPrices used in the get prices route to retrieve
 * available prices for specified plan
 * @param {*} entityType // to identify specific plans like PRO
 * @param {*} communityReferralCode // referral code from FE
 * @param {*} community // community object
 * @param {*} hasSubscribedBefore // whether the community has subscribed before
 * @returns
 */
exports.retrieveAvailablePlanForNonSubscribers = async ({
  entityType,
  hasSubscribedBefore,
  communityReferralCode,
  community,
}) => {
  let plan;
  let referrer;
  let isCommunityReferralCodeApplied = false;

  // Referral code from FE is applied if the community does not have existing subscription
  if (!plan && communityReferralCode) {
    const {
      plan: referralPlan,
      referrerCommunity,
      referralRewardTemplate,
    } = await this.retrieveCommunityReferralDetails({
      communityReferralCode,
      planType: entityType,
      refereeCommunityObjectId: community._id,
    });
    if (referralPlan) {
      plan = referralPlan;
      isCommunityReferralCodeApplied = true;
    }
    referrer = {
      title: referrerCommunity.title,
      link: referrerCommunity.link,
      profileImage:
        referrerCommunity.thumbnailImgData?.desktopImgData?.src,
      communityObjectId: referrerCommunity._id,
      referralRewardTemplateObjectId: referralRewardTemplate._id,
      communityReferralCode,
    };
  }

  if (!plan) {
    plan = await this.retrieveDefaultPlan({
      entityType,
    });
  }

  if (!plan) {
    throw new ParamError(`Plan not found for ${entityType}`);
  }

  plan.nextBillingPlan = await this.retrieveNextBillingPlan({ plan });

  // Community that has subscribed before shld not recieve the discounted first billing plan.
  // Hence, reassigned to nextBillingPlan if it exists
  // This only applies if referralCode is not applied
  if (
    hasSubscribedBefore &&
    plan.nextBillingPlan &&
    !isCommunityReferralCodeApplied
  ) {
    return {
      plan: plan.nextBillingPlan,
      isCommunityReferralCodeApplied,
      referrer,
      communityReferralCode,
    };
  }

  return { plan, isCommunityReferralCodeApplied, referrer };
};

async function getZeroPaymentPriceIdAndProductId({
  paymentBackendRpc,
  paymentProvider,
  localCurrency,
  interval,
  intervalCount,
}) {
  const { envVarData = null } = await getConfigByTypeFromCache(
    CONFIG_TYPES.LEARN_PORTAL_BACKEND_ENV_CONFIG_TYPE
  );

  // Default to priceId 0
  let productId =
    envVarData?.PLAN_DEFAULT_ZERO_DOLLAR_PRODUCT_ID_STRIPE_US;

  if (PAYMENT_PROVIDER.STRIPE_INDIA === paymentProvider) {
    productId =
      envVarData?.PLAN_DEFAULT_ZERO_DOLLAR_PRODUCT_ID_STRIPE_INDIA;
  }

  const prices = await paymentBackendRpc.listAllStripeProductPricing({
    stripeProductId: productId,
    paymentProvider,
    localCurrency,
    baseCurrency: localCurrency,
    allCurrency: false,
    onlySelectedCurrency: true,
  });
  const finalPrice = prices.price.filter((price) => {
    const { recurring } = price;
    return (
      recurring.interval.toUpperCase() === interval.toUpperCase() &&
      recurring.interval_count === intervalCount
    );
  })?.[0];

  return { productId, priceId: finalPrice.id };
}

exports.createCustomiseCreditPlan = async ({
  title,
  entityType,
  interval,
  intervalCount,
  defaultAmount,
  defaultCurrency,
  originalPriceId,
  originalAmount,
  originalPlan,
  nextBillingPlan = null,
  paymentBackendRpc,
  paymentProvider,
  session,
}) => {
  // No need for new plan and price since the amount is the same
  if (originalAmount === defaultAmount) {
    return { plan: originalPlan, priceId: originalPriceId };
  }

  let productId;
  let priceId;

  if (defaultAmount > 0) {
    const data = {
      name: title,
      description: title,
      prices: [
        {
          currency: defaultCurrency,
          unit_amount: defaultAmount,
          recurring: {
            interval,
            interval_count: intervalCount,
          },
        },
      ],
      paymentProvider,
    };

    const result = await paymentBackendRpc.createStripeProduct(data);
    productId = result?.product?.id;
    priceId = result?.product?.default_price;
  } else {
    const zeroPaymentDetails = await getZeroPaymentPriceIdAndProductId({
      paymentBackendRpc,
      paymentProvider,
      localCurrency: defaultCurrency,
      interval,
      intervalCount,
    });
    productId = zeroPaymentDetails.productId;
    priceId = zeroPaymentDetails.priceId;
  }

  const newPlanData = {
    entityType,
    billingModel: originalPlan.billingModel,
    features: originalPlan.features,
    type: title,
    isActive: true,
    paymentProviders: [
      {
        provider: paymentProvider,
        productId,
      },
    ],
    index: 1,
  };
  if (nextBillingPlan) {
    newPlanData.nextBillingPlanObjectId = nextBillingPlan._id;
  }
  const plan = (
    await CommunityPlanModel.create([newPlanData], { session })
  )[0].toObject();
  return { plan, priceId };
};
