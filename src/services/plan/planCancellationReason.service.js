const PlanCancellationReasonModel = require('../../models/plan/planCancellationReason.model');
const { ParamError } = require('../../utils/error.util');

exports.retrieveValidCancellationReasons = async () => {
  const reasons = await PlanCancellationReasonModel.find({
    disable: false,
  })
    .sort({ index: 1 })
    .lean();

  return reasons;
};

exports.validateAndFormatCancellationReasons = async (
  cancellationReasons = []
) => {
  const reasons = await this.retrieveValidCancellationReasons();

  const reasonMap = new Map();
  reasons.forEach((reason) => {
    reasonMap.set(reason.key, reason);
  });

  const formattedCancellationReasonMap = new Map();
  cancellationReasons.forEach((reason) => {
    const validReasonDetails = reasonMap.get(reason.key);
    if (!validReasonDetails) {
      throw new ParamError('Invalid cancellation reason');
    }
    if (validReasonDetails.enableUserInput) {
      if (!reason.reason && validReasonDetails.userInput.isRequired) {
        throw new ParamError(`Missing reason for ${reason.key}`);
      }
      formattedCancellationReasonMap.set(reason.key, reason);
    } else if (reason.reason) {
      throw new ParamError(
        `${reason.key} should not have a reason attached to it`
      );
    } else {
      const englishLabel = validReasonDetails.label?.en ?? '';
      formattedCancellationReasonMap.set(reason.key, {
        key: reason.key,
        reason: englishLabel,
      });
    }
  });

  return [...formattedCancellationReasonMap.values()];
};
