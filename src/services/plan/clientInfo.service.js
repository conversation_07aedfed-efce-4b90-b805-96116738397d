const status = require('http-status');
const planOrderService = require('./planOrder.service');
const validationService = require('./validation.service');
const PlanOrderModel = require('../../models/plan/communityPlanOrder.model');
const {
  getFormattedNumber,
  checkPhoneNumber,
} = require('../../utils/phoneNumber.util');
const logger = require('../logger.service');

exports.updatePlanClientInfo = async ({
  communityObjectId,
  learnerObjectId,
  phoneNumber,
}) => {
  const planOrder = await planOrderService.retrieveExistingPlanOrder({
    communityObjectId,
  });

  validationService.checkIfCanUpdateClientInfo({
    planOrder,
    learnerObjectId,
    throwError: true,
  });

  // check if the phone number exist in the database already
  const formattedPhoneNumber = getFormattedNumber(phoneNumber);
  if (!checkPhoneNumber(formattedPhoneNumber)) {
    const err = new Error('Invalid phone number');
    err.status = status.BAD_REQUEST;
    throw err;
  }

  const updatedPlanOrder = await PlanOrderModel.findOneAndUpdate(
    { _id: planOrder._id },
    {
      phoneNumber: formattedPhoneNumber,
    },
    {
      new: true,
    }
  ).lean();

  return updatedPlanOrder;
};
