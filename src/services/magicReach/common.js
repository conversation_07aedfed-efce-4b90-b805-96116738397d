const productService = require('../common/communityProducts.service');

const { BUCKET_NAMES } = require('./constants');
const { PURCHASE_TYPE } = require('../../constants/common');
const { FILTERING_FIELDS } = require('../membership/constants');
const { PRODUCT_TYPE } = require('../product/constants');

exports.getFilters = (params) => {
  const filters = {};
  let filteringFields;
  switch (params.bucketName) {
    case BUCKET_NAMES.EVENT_ATTENDEES:
      filteringFields = ['eventObjectId', 'memberType'];
      break;

    case BUCKET_NAMES.CUSTOM_FILTERS: {
      filteringFields = Object.values(FILTERING_FIELDS);
      break;
    }
    default:
      filteringFields = [];
  }
  Object.keys(params).forEach((key) => {
    if (filteringFields.includes(key)) {
      filters[key] = params[key];
    }
  });
  return filters;
};

exports.getMentionedProductsLiveInfoCache = async (
  mentionedProducts = []
) => {
  if (mentionedProducts.length === 0) {
    return new Map();
  }
  const subscriptionObjectIds = [];
  const eventObjectIds = [];
  const productObjectIds = [];
  const programObjectIds = [];

  mentionedProducts.forEach((product) => {
    switch (product.type) {
      case PURCHASE_TYPE.SUBSCRIPTION:
        subscriptionObjectIds.push(product.productObjectId);
        break;
      case PURCHASE_TYPE.CHALLENGE:
        programObjectIds.push(product.productObjectId);
        break;
      case PURCHASE_TYPE.EVENT:
        eventObjectIds.push(product.productObjectId);
        break;
      case PURCHASE_TYPE.SESSION:
      case PURCHASE_TYPE.FOLDER:
      case PRODUCT_TYPE.DIGITAL_FILES:
      case PRODUCT_TYPE.COURSE:
        productObjectIds.push(product.productObjectId);
        break;
      default:
        break;
    }
  });

  const existingProductCache = await productService.retrieveEntitiesCache({
    subscriptionObjectIds,
    eventObjectIds,
    productObjectIds,
    programObjectIds,
    source: productService.SOURCE_TYPES.MAGIC_REACH,
  });

  return existingProductCache;
};

exports.getFormattedMentionedProducts = async (
  mentionedProducts = [],
  formatForDatabase = false
) => {
  if (mentionedProducts.length === 0) {
    return [];
  }
  const existingProductCache =
    await this.getMentionedProductsLiveInfoCache(mentionedProducts);

  const formattedMentionedProducts = mentionedProducts.map((product) => {
    const { type, productObjectId } = product;
    const key = `${type}-${productObjectId}`;
    const productInfo = existingProductCache.get(key);
    if (formatForDatabase) {
      return {
        ...product,
        archivedProductInfo: productInfo,
      };
    }
    return {
      ...product,
      productInfo,
    };
  });
  return formattedMentionedProducts;
};
