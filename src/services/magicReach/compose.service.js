const mongoose = require('mongoose');

const ObjectId = mongoose.Types.ObjectId;

const Membership = require('../../models/membership/membership.model');
const EmailScheduledJob = require('../../models/mail/emailScheduledJob.model');
const Community = require('../../communitiesAPI/models/community.model');
const CommunityMagicReachEmail = require('../../models/magicReach/communityMagicReachEmail.model');
const MembershipSegment = require('../../models/membership/segment.model');
const CommunityMagicReachStats = require('../../models/magicReach/communityMagicReachStats.model');
const CommunityPostModel = require('../../communitiesAPI/models/communityPost.model');

const { transformPaginatedParam } = require('../../utils/pagination.util');
const {
  getMembersWithPhoneNumbers,
} = require('./recipients/subscription.recipients');
const {
  getWhatsappMemberUsers,
  getWhatsappNonMemberUsers,
} = require('./recipients/whatsapp.recipients');

const {
  getEventAttendees,
  countEventAttendees,
  getEventAttendeesBucketMeta,
} = require('./recipients/event.recipients');
const {
  getFolderViewers,
  countFolderViewers,
  getFolderViewersBucketMeta,
} = require('./recipients/folder.recipients');
const {
  getChallengeParticipants,
  countChallengeParticipants,
  getChallengeParticipantsBucketMeta,
} = require('./recipients/challenge.recipients');
const {
  countCustomFilterMembers,
  getCustomFilterMembers,
  getOptOutMembers,
  countOptOutMembers,
} = require('./recipients/membership.recipients');
const {
  getOptOutEmailsByCommunity,
  getOptOutWhatsappByCommunity,
} = require('./utils/optInOut.util');
const {
  getDefaultCommunitySendLimitConfig,
  extractCommunitySendLimitConfig,
} = require('./utils/communityLimit.util');

const {
  validateMongoosePayload,
  ParamError,
} = require('../../utils/error.util');
const logger = require('../logger.service');
const membershipService = require('../membership');
const PrimaryMongooseConnection = require('../../rpc/primaryMongooseConnection');

const {
  PLATFORMS,
  BUCKET_NAMES,
  BUCKET_RELATIONSHIPS,
  MAGIC_REACH_MESSAGE_STATUS,
  COMMUNITY_STATS_WINDOW,
  MEMBER_BUCKETS,
} = require('./constants');
const {
  countAbandonedCheckouts,
  getAbandonedCheckouts,
} = require('./recipients/abandonedCarts.recipients');
const {
  countAffiliates,
  getAffiliates,
} = require('./recipients/affiliate.recipients');
const { getFormattedMentionedProducts } = require('./common');
const productService = require('../common/communityProducts.service');
const { getAuthorDetails } = require('./utils/learner.utils');

async function getCommunity(communityId) {
  const community = await Community.findById(communityId);
  if (!community) {
    throw new ParamError('Invalid communityId');
  }
  return community;
}

const searchRecipients = async ({
  communityId,
  bucketName,
  searchString,
  filters,
  otherFilters,
  communityRole,
  status,
  pageNo,
  pageSize,
  sortBy,
  sortOrder,
  additionalData = {},
  startObjectId = null,
  endObjectId = null,
}) => {
  let { community, optOutEmails, optOutWhatsapp } = additionalData;
  if (!community) {
    community = await getCommunity(communityId);
  }
  if (!optOutEmails) {
    optOutEmails = await getOptOutEmailsByCommunity(community._id);
  }
  if (!optOutWhatsapp) {
    optOutWhatsapp = await getOptOutWhatsappByCommunity(community._id);
  }
  const paramObject = {
    community,
    communityCode: community.code,
    communityId,
    searchString,
    filters,
    excludeEmails: optOutEmails,
    excludePhoneNumbers: optOutWhatsapp,
    startObjectId,
    endObjectId,
    ...transformPaginatedParam(pageNo, pageSize),
  };

  let results = {
    users: [],
    meta: { total: 0 },
  };
  let isDefault = false;
  switch (bucketName) {
    case BUCKET_NAMES.MEMBERS_WITH_PHONE_NUMBERS:
      results = await getMembersWithPhoneNumbers(paramObject);
      break;
    case BUCKET_NAMES.WHATSAPP_NON_NAS:
      results = await getWhatsappNonMemberUsers(paramObject);
      break;
    case BUCKET_NAMES.WHATSAPP_NAS:
      results = await getWhatsappMemberUsers(paramObject);
      break;
    case BUCKET_NAMES.EVENT_ATTENDEES:
      results = await getEventAttendees(paramObject);
      break;
    case BUCKET_NAMES.FOLDER_VIEWERS:
      results = await getFolderViewers(paramObject);
      break;
    case BUCKET_NAMES.CHALLENGE_PARTICIPANTS:
      results = await getChallengeParticipants(paramObject);
      break;
    case BUCKET_NAMES.CUSTOM_FILTERS:
      results = await getCustomFilterMembers(paramObject);
      break;
    case BUCKET_NAMES.ABANDONED_CHECKOUTS:
      results = await getAbandonedCheckouts(paramObject);
      break;
    case BUCKET_NAMES.AFFILIATES:
      results = await getAffiliates(paramObject);
      break;
    default:
      isDefault = true;
      break;
  }

  if (isDefault) {
    const limit = parseInt(pageSize || 100, 10);
    let skip = 0;
    if (pageNo) {
      skip = (parseInt(pageNo, 10) - 1) * limit;
    }
    const projection = {
      email: 1,
      learnerObjectId: 1,
      name: 1,
      profileImage: 1,
      phoneNumber: 1,
    };
    if (bucketName) {
      projection.inBuckets = [bucketName];
    }
    results = await membershipService.getService.getCommunityMembers({
      communityId,
      searchString,
      communityRole,
      status,
      skip,
      limit,
      sortBy,
      sortOrder,
      otherFilters,
      reachFilters: {
        optOutFromEmail: false,
      },
      projection,
      startObjectId,
      endObjectId,
    });
  }

  if (results.members) {
    results.users = results.members;
    delete results.members;
  }

  if (results?.meta?.total) {
    results.meta.page = pageNo;
    results.meta.pages = Math.ceil(results.meta.total / pageSize);
  }
  return results;
};

const countOptOuts = async function ({ communityId }) {
  const community = await getCommunity(communityId);
  const fromEmailCount = await countOptOutMembers(community);
  return {
    fromEmailCount,
  };
};

const getOptOuts = async function ({
  communityId,
  skip,
  limit,
  sortBy,
  sortOrder,
}) {
  const optOuts = await getOptOutMembers({
    communityId,
    skip,
    limit,
    sortBy,
    sortOrder,
  });
  return optOuts;
};

/**
 * Remove subset buckets
 * @param {*} selectedBuckets
 * @returns {Array<bucketName>}
 */
const getToCountBuckets = function (selectedBuckets) {
  let toRemoveBuckets = [];
  if (!selectedBuckets) {
    return toRemoveBuckets;
  }
  for (const bucket of selectedBuckets) {
    if (bucket in BUCKET_RELATIONSHIPS) {
      toRemoveBuckets = toRemoveBuckets.concat(
        BUCKET_RELATIONSHIPS[bucket]
      );
    }
  }
  const toCountBuckets = selectedBuckets.filter(
    (bucket) => !toRemoveBuckets.includes(bucket)
  );
  return toCountBuckets;
};

const countUsersByBucket = async ({
  segment,
  bucketName,
  communityId,
  filters,
  additionalData = {},
}) => {
  let { community, optOutEmails, optOutWhatsapp } = additionalData;
  if (!community) {
    community = await getCommunity(communityId);
  }
  if (!optOutEmails) {
    optOutEmails = await getOptOutEmailsByCommunity(community._id);
  }
  if (!optOutWhatsapp) {
    optOutWhatsapp = await getOptOutWhatsappByCommunity(community._id);
  }
  const paramObject = {
    community,
    communityCode: community.code,
    communityId,
    filters,
    excludeEmails: optOutEmails,
    excludePhoneNumbers: optOutWhatsapp,
  };
  let count = 0;
  switch (bucketName) {
    case BUCKET_NAMES.EVENT_ATTENDEES: {
      count = await countEventAttendees(paramObject);
      return count;
    }
    case BUCKET_NAMES.FOLDER_VIEWERS: {
      count = await countFolderViewers(paramObject);
      return count;
    }
    case BUCKET_NAMES.CHALLENGE_PARTICIPANTS: {
      count = await countChallengeParticipants(paramObject);
      return count;
    }
    case BUCKET_NAMES.CUSTOM_FILTERS:
      count = await countCustomFilterMembers(paramObject);
      return count;
    case BUCKET_NAMES.ABANDONED_CHECKOUTS:
      count = await countAbandonedCheckouts(paramObject);
      return count;
    case BUCKET_NAMES.AFFILIATES:
      count = await countAffiliates(paramObject);
      return count;
    default:
      break;
  }

  const { queryParams, otherFilters } =
    await membershipService.membershipSearchUtils.processGetMembersQueryParams(
      {
        segment: segment.name,
      }
    );

  count =
    await membershipService.countService.countCommunityMembersWithFilters({
      communityId: community._id,
      otherFilters,
      reachFilters: {
        optOutFromEmail: false,
        optOutFromWhatsapp: false,
      },
      communityRole: queryParams.role || [],
      status: queryParams.status || [],
      searchString: queryParams.searchString,
    });

  return count;
};

async function getBucketMeta({ communityId, bucketName, filters }) {
  let bucketMeta = {};
  const paramObject = { communityId, ...filters };
  switch (bucketName) {
    case BUCKET_NAMES.EVENT_ATTENDEES: {
      bucketMeta = await getEventAttendeesBucketMeta(paramObject);
      break;
    }
    case BUCKET_NAMES.FOLDER_VIEWERS: {
      bucketMeta = await getFolderViewersBucketMeta(paramObject);
      break;
    }
    case BUCKET_NAMES.CHALLENGE_PARTICIPANTS: {
      bucketMeta = await getChallengeParticipantsBucketMeta(paramObject);
      break;
    }
    default:
      bucketMeta = {};
      break;
  }
  return bucketMeta;
}

const filterSelectedUsers = async (selectedUsers, communityId) => {
  const emails = selectedUsers.map((user) => user.email);

  const results = await Membership.find({
    communityObjectId: new ObjectId(communityId),
    email: { $in: emails },
    'reachInfo.optOutFromEmail': { $ne: true }, // Some data might not have reach info
  }).lean();

  const userSet = new Set();
  results.forEach((user) => {
    userSet.add(user.email);
  });
  const filtered = selectedUsers.filter((user) => userSet.has(user.email));
  return filtered;
};

/**
 * Count the number of recipients from the selection params
 * @param {} param0
 * @returns total, member and non member counts
 */
const countRecipients = async ({
  communityId,
  selectedUsers = [],
  unselectedUsers = [],
  selectedBuckets = [],
  bucketFilters = {},
}) => {
  const toCountBuckets = getToCountBuckets(selectedBuckets);
  let total = 0;
  let member = 0;
  let nonMember = 0;

  const buckets = await MembershipSegment.find({
    bucketName: { $in: toCountBuckets },
  }).lean();

  const segmentBucketMap = new Map();

  buckets.forEach((bucket) =>
    segmentBucketMap.set(bucket.bucketName, bucket)
  );

  await Promise.all(
    toCountBuckets.map(async (bucket) => {
      const count = await countUsersByBucket({
        segment: segmentBucketMap.get(bucket),
        communityId,
        bucketName: bucket,
        filters: bucketFilters[bucket] || {},
      });

      total += count;
      if (MEMBER_BUCKETS.includes(bucket)) {
        member += count;
      } else {
        nonMember += count;
      }
    })
  );

  for (const user of selectedUsers) {
    if (
      !user.inBuckets?.some((bucket) => toCountBuckets.includes(bucket))
    ) {
      total += 1;
      if (
        user.inBuckets?.some((bucket) => MEMBER_BUCKETS.includes(bucket))
      ) {
        member += 1;
      } else {
        nonMember += 1;
      }
    }
  }

  for (const user of unselectedUsers) {
    if (
      user.inBuckets?.some((bucket) => toCountBuckets.includes(bucket))
    ) {
      total -= 1;
      if (
        user.inBuckets?.some((bucket) => MEMBER_BUCKETS.includes(bucket))
      ) {
        member -= 1;
      } else {
        nonMember -= 1;
      }
    }
  }

  return {
    total,
    member,
    nonMember,
  };
};

/**
 * Create a Magic Reach message in DB
 * @param {*} param messageData should be valid Mongo model payload
 * @returns {Promise<ObjectId>}
 */
const createMessage = async ({
  authorUserObjectId,
  communityId,
  messageData,
}) => {
  const dbPayload = messageData;
  if (!Object.keys(dbPayload).includes('isDraft')) {
    dbPayload.isDraft = true;
  }
  dbPayload.communityId = communityId;
  dbPayload.author = authorUserObjectId;
  dbPayload.status = MAGIC_REACH_MESSAGE_STATUS.DRAFT;

  const message = new CommunityMagicReachEmail(dbPayload);
  await validateMongoosePayload({
    operation: 'Create Magic Reach Message',
    document: message,
    payload: dbPayload,
  });
  const result = await CommunityMagicReachEmail.create(dbPayload);
  if (!result) {
    throw new Error(
      'Failed to create message in DB but no exception was thrown. The most likely problem is network issue.'
    );
  }

  return result._id;
};

/**
 * Update a Magic Reach message in DB
 * @param {*} param messageData should be valid Mongo model payload
 * @returns {Promise<Model>}
 */
const updateMessage = async ({ communityId, messageId, messageData }) => {
  const dbPayload = messageData;
  dbPayload.communityId = communityId;
  const message = new CommunityMagicReachEmail(dbPayload);
  await validateMongoosePayload({
    operation: 'Update Magic Reach Message',
    document: message,
    payload: dbPayload,
  });
  const originalMessage = await CommunityMagicReachEmail.findOne({
    _id: messageId,
  })
    .select('_id status')
    .lean();
  if (!originalMessage) {
    throw new Error(`Magic Reach message [${messageId}] doesn't exist`);
  }

  let updatedMessage;
  if (
    messageData.status === MAGIC_REACH_MESSAGE_STATUS.DRAFT &&
    originalMessage.status === MAGIC_REACH_MESSAGE_STATUS.SCHEDULED
  ) {
    updatedMessage = await this.removeScheduleAndUpdateMessageAsDraft({
      messageId,
      messageData,
    });
  } else {
    updatedMessage = await CommunityMagicReachEmail.findOneAndUpdate(
      { _id: messageId },
      dbPayload,
      { new: true }
    );
  }

  return updatedMessage;
};

/**
 * Remove scheduled job from Magic Reach message in DB
 * Update MR if messageData is provided
 * @param {*} param messageId, messageData
 * @returns {Promise<Model>}
 */
const removeScheduleAndUpdateMessageAsDraft = async ({
  messageId,
  messageData = {},
}) => {
  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();
  try {
    const dbPayload = messageData;
    dbPayload.status = MAGIC_REACH_MESSAGE_STATUS.DRAFT;
    const updatedMessage = await CommunityMagicReachEmail.findOneAndUpdate(
      { _id: messageId },
      {
        $unset: { schedule: 1 },
        $set: dbPayload,
      },
      { upsert: false, new: true, session }
    ).lean();

    if (!updatedMessage) {
      throw new Error(
        `Update Magic Reach message in DB returned undefined. Most likely Magic Reach message [${messageId}] doesn't exist`
      );
    }
    await EmailScheduledJob.deleteMany(
      {
        entityObjectId: messageId,
      },
      { session }
    );

    await session.commitTransaction();
    return updatedMessage;
  } catch (error) {
    await session.abortTransaction();
    logger.error(`deleteScheduleMessage error|error=${error}`);
    throw error;
  } finally {
    await session.endSession();
  }
};

/**
 * Delete a Magic Reach  draft or scheduled message
 * @param {*} param messageId
 * @returns {Promise<Model>}
 */
const deleteMessage = async ({ messageId }) => {
  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  try {
    const message = await CommunityMagicReachEmail.findOne({
      _id: messageId,
    }).lean();
    if (!message) {
      throw new Error(`Magic Reach message [${messageId}] doesn't exist`);
    }
    if (
      message.status !== MAGIC_REACH_MESSAGE_STATUS.SCHEDULED &&
      message.status !== MAGIC_REACH_MESSAGE_STATUS.DRAFT
    ) {
      throw new Error(
        `Can only delete scheduled or draft Magic Reach message`
      );
    }
    if (message.status === MAGIC_REACH_MESSAGE_STATUS.SCHEDULED) {
      await EmailScheduledJob.deleteMany(
        {
          entityObjectId: messageId,
        },
        { session }
      );
    }
    await CommunityMagicReachEmail.findOneAndDelete(
      { _id: messageId },
      { session }
    );
    await session.commitTransaction();
  } catch (error) {
    await session.abortTransaction();
    logger.error(`deleteMessage error|error=${error}`);
    throw error;
  } finally {
    await session.endSession();
  }
};

/**
 * Get a Magic Reach message in DB
 * @param {ObjectId} messageId
 * @returns {Promise<Model>}
 */
const getMessage = async (messageId) => {
  const message = await CommunityMagicReachEmail.findOne({
    _id: messageId,
  });
  if (!message) {
    throw new Error(
      `Failed to get Magic Reach message [${messageId}] from DB. Most likely it doesn't exist`
    );
  }
  if (!message.status) {
    message.status = message.isDraft
      ? MAGIC_REACH_MESSAGE_STATUS.DRAFT
      : MAGIC_REACH_MESSAGE_STATUS.SENT;
  }

  return message;
};

/**
 * Get a Magic Reach message in DB
 * @param {ObjectId} messageId
 * @returns {Promise<Model>}
 */
const getMessageData = async (messageId) => {
  const message = await getMessage(messageId);
  const messageData = message.toObject();
  const author = (await getAuthorDetails(messageData.author))?.[0] ?? {};
  messageData.learnerObjectId = author.learnerObjectId;
  messageData.learner = {
    profileImage: author.profileImage,
    firstName: author.firstName,
    lastName: author.lastName,
  };
  // Populate the mentionedProducts information
  if (messageData.mentionedProducts?.length > 0) {
    messageData.mentionedProducts = await getFormattedMentionedProducts(
      messageData.mentionedProducts
    );
  }
  if (messageData.sentResults?.AnnouncementV2?.announcementObjectId) {
    const post = await CommunityPostModel.findById(
      messageData.sentResults.AnnouncementV2.announcementObjectId
    )
      .select('visibilityType commentCount impressions reactionCounts')
      .lean();
    delete post?._version;
    messageData.postDetails = post;
  }
  return messageData;
};

/**
 * Get single bucket information
 * @returns
 */
const getBucket = async ({ bucketName, communityId, filters }) => {
  let bucket = await MembershipSegment.findOne({
    isBucket: true,
    bucketName,
  }).lean();
  if (!bucket) {
    bucket = {};
  }
  const count = await countUsersByBucket({
    segment: bucket,
    communityId,
    bucketName,
    filters,
  });
  const bucketMeta = await getBucketMeta({
    bucketName,
    communityId,
    filters,
  });
  return {
    ...bucket,
    bucket: bucket.bucketName,
    totalUsers: count,
    bucketMeta,
  };
};

/**
 * Get magic reach bucket information, including the counts for the community
 * @param {ObjectId} communityId
 * @returns bucket information with count
 */
const getBuckets = async ({
  communityId,
  filters,
  languagePreference,
}) => {
  const buckets =
    await membershipService.getService.getCommunityMembershipSegments({
      bucketOnly: true,
      communityId,
      languagePreference,
    });
  const promises = buckets.map(async (segment) => {
    try {
      const bucketMeta = await getBucketMeta({
        bucketName: segment.bucketName,
        filters,
        communityId,
      });
      return {
        ...segment,
        bucket: segment.bucketName,
        totalUsers: segment.count,
        bucketMeta,
        //TODO: to remove when app has released their changes
        active: true,
      };
    } catch (error) {
      logger.error(
        `Failed to retrieve count for bucket|`,
        `bucketName=${segment.bucketName}|`,
        `error=${error}|`,
        `trace=${error.stack}`
      );
      throw new Error(error);
    }
  });
  const resolvedData = await Promise.allSettled(promises);
  const results = resolvedData
    .filter((result) => result.status === 'fulfilled')
    .map((result) => result.value);
  return results;
};

/**
 * Return magic reach stat and limit for a community
 * @param {*} param0
 * @returns
 */
const getCommunityMagicReachLimitAndStats = async ({
  communityId,
  window = COMMUNITY_STATS_WINDOW.LIFETIME,
}) => {
  const community = await getCommunity(communityId);
  const results = {};
  const platformsToQuery = [PLATFORMS.WHATSAPP, PLATFORMS.EMAIL];

  await Promise.all(
    platformsToQuery.map(async (platform) => {
      try {
        // For WhatsApp, fetch both lifetime and current monthly stats
        // For Email, only fetch the requested window (usually lifetime)
        const query = {
          platform,
          communityId: new ObjectId(communityId),
        };

        if (platform === PLATFORMS.WHATSAPP) {
          const currentPeriod = new Date().toISOString().substring(0, 7); // "2025-07"
          query.$or = [
            { window: COMMUNITY_STATS_WINDOW.LIFETIME },
            {
              window: COMMUNITY_STATS_WINDOW.MONTHLY,
              currentMonth: currentPeriod,
            },
          ];
        } else {
          query.window = window;
        }

        const stats = await CommunityMagicReachStats.find(query, {
          window: 1,
          totalSent: 1,
          memberSent: 1,
          nonMemberSent: 1,
          _id: 0,
        });
        const statsByWindowMap = stats.reduce((acc, entry) => {
          acc[entry.window] = entry;
          return acc;
        }, {});
        results[platform] = {
          sendLimit: await extractCommunitySendLimitConfig({
            community,
            platform,
          }),
          stats: statsByWindowMap,
        };
      } catch (error) {
        logger.error(
          'Error in getting community magic reach stat, returning default value|',
          `error=${error}|`,
          `trace=${error.stack}`
        );
        results[platform] = {
          limitConfig: await getDefaultCommunitySendLimitConfig(),
          stats: {},
        };
      }
    })
  );

  return results;
};

const getCommunityMagicReachLimit = async ({ communityId }) => {
  const community = await getCommunity(communityId);
  const results = {};
  const platformsToQuery = [PLATFORMS.WHATSAPP];

  await Promise.all(
    platformsToQuery.map(async (platform) => {
      try {
        results[platform] = await extractCommunitySendLimitConfig({
          community,
          platform,
        });
      } catch (error) {
        logger.error(
          'Error in getting community magic reach limit, returning default value|',
          `error=${error}|`,
          `trace=${error.stack}`
        );
        results[platform] = await getDefaultCommunitySendLimitConfig();
      }
    })
  );

  return results;
};

/**
 * Return magic reach stats for a community
 * @param {*} param0
 * @returns
 */
const getCommunityMagicReachStats = async ({
  communityId,
  window = COMMUNITY_STATS_WINDOW.LIFETIME,
}) => {
  const results = {};
  const platformsToQuery = [PLATFORMS.WHATSAPP, PLATFORMS.EMAIL];

  await Promise.all(
    platformsToQuery.map(async (platform) => {
      try {
        const stats = await CommunityMagicReachStats.find(
          {
            platform,
            communityId: new ObjectId(communityId),
            window,
          },
          {
            window: 1,
            totalSent: 1,
            memberSent: 1,
            nonMemberSent: 1,
            _id: 0,
          }
        );
        const statsByWindowMap = stats.reduce((acc, entry) => {
          acc[entry.window] = entry;
          return acc;
        }, {});
        results[platform] = statsByWindowMap;
      } catch (error) {
        logger.error(
          'Error in getting community magic reach stats',
          `error=${error}|`,
          `trace=${error.stack}`
        );
        results[platform] = {};
      }
    })
  );

  return results;
};

/**
 * Get single bucket information
 * @returns
 */
const getProducts = async ({ communityId, search, pageSize }) => {
  const products = await productService.retrieveAllProducts({
    search,
    communityObjectId: communityId,
    onlyPublished: true,
    onlyPaid: false,
    pageSize,
    source: productService.SOURCE_TYPES.MAGIC_REACH,
    withSubscriptionType: true,
  });

  return products;
};

module.exports = {
  getProducts,
  getBucket,
  getBuckets,
  searchRecipients,
  getToCountBuckets,
  countRecipients,
  countOptOuts,
  getOptOuts,
  createMessage,
  updateMessage,
  getMessage,
  getMessageData,
  getCommunityMagicReachLimitAndStats,
  getCommunityMagicReachStats,
  getCommunityMagicReachLimit,
  deleteMessage,
  removeScheduleAndUpdateMessageAsDraft,
  filterSelectedUsers,
  countUsersByBucket,
};
