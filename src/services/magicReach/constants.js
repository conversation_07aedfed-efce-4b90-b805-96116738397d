const { BATCH_METADATA_MODEL_TYPE } = require('../../constants/common');

const PLATFORMS = {
  EMAIL: 'Email',
  WHATSAPP: 'Whatsapp',
  DISCORD: 'Discord',
  ANNOUNCEMENT: 'Announcement',
  DISCORD_V2: 'DiscordV2',
  ANNOUNCEMENT_V2: 'AnnouncementV2',
};

const MAGIC_REACH_MESSAGE_STATUS = {
  DRAFT: 'draft',
  SENT: 'sent',
  PROCESSING: 'processing',
  PROCESSING_FAILED: 'processing_failed',
  REVIEWING: 'reviewing',
  BLOCKED: 'blocked',
  SCHEDULED: 'scheduled',
};

const BUCKET_NAMES = {
  INTERESTED: 'Interested',
  UNSUBSCRIBED: 'Unsubscribed',
  INVITED: 'Invited',
  MEMBERS: 'Members',
  MEMBERS_WITH_PHONE_NUMBERS: 'Members with Phone Numbers',
  WHATSAPP_NON_NAS: 'Whatsapp Users without Community Membership',
  WHATSAPP_NAS: 'Whatsapp Users with Community Membership',
  FOLDER_VIEWERS: 'Folder Viewers',
  EVENT_ATTENDEES: 'Event Attendees',
  CHALLENGE_PARTICIPANTS: 'Challenge Participants',
  CUSTOM_FILTERS: 'Custom Filters',
  ABANDONED_CHECKOUTS: 'Abandoned Checkouts',
  AFFILIATES: 'Affiliates',
};

const SENDGRID_EVENT = {
  OPEN: 'open',
  CLICK: 'click',
  PROCESSED: 'processed',
  DEFERRED: 'deferred',
  DELIVERED: 'delivered',
  BOUNCE: 'bounce',
  DROPPED: 'dropped',
  SPARMREPORT: 'spamreport',
  UNSUBSCRIBE: 'unsubscribe',
  GROUP_UNSUBSCRIBE: 'group_unsubscribe',
  GROUP_RESUBSCRIBE: 'group_resubscribe',
};

const WHATSAPP_EVENT = {
  SENT: 'sent',
  DELIVERED: 'delivered',
  READ: 'read',
  FAILED: 'failed',
};

const EMAIL_RECIPIENT_FILTER = [
  'isDelivered',
  'isProcessed',
  'isOpened',
  'isClicked',
  'isBounced',
  'isFailed',
];

const WHATSAPP_RECIPIENT_FILTER = [
  'isSent',
  'isDelivered',
  'isRead',
  'isFailed',
];

const CLICK_RECIPIENT_FILTER = ['linkObjectId'];

const EARNING_RECIPIENT_FILTER = ['hasPaid'];

const BUCKET_RELATIONSHIPS = {
  [BUCKET_NAMES.MEMBERS]: [BUCKET_NAMES.MEMBERS_WITH_PHONE_NUMBERS],
  [BUCKET_NAMES.MEMBERS_WITH_PHONE_NUMBERS]: [BUCKET_NAMES.WHATSAPP_NAS],
};

const MEMBER_BUCKETS = [
  BUCKET_NAMES.MEMBERS,
  BUCKET_NAMES.MEMBERS_WITH_PHONE_NUMBERS,
  BUCKET_NAMES.WHATSAPP_NAS,
  BUCKET_NAMES.WHATSAPP_NON_NAS,
];

const NON_MEMBER_BUKCETS = [BUCKET_NAMES.INTERESTED, BUCKET_NAMES.INVITED];

const SEARCH_RECIPIENT_DB_PAGE_SIZE = 500;
const DB_INSERT_BATCH_SIZE = 100;
const SEND_TO_QUEUE_BATCH_SIZE = 100;

const GENERIC_REPLY_TO_NAME = 'Community Manager';
const FALLBACK_REPLY_TO = '<EMAIL>';

const FRAUD_CHECK_STATUS = {
  REVIEWING: 'reviewing',
  APPROVED: 'approved',
  REJECTED: 'rejected',
  SENT_ON_DETECTION: 'sent_on_detection',
};

const MAIL_TYPES = {
  PENDING_APPROVAL_NOTIFY_COPS:
    'COMMUNITY_MAGIC_REACH_PENDING_APPROVAL_NOTIFY_COPS',
  WARNING_NOTIFY_COPS: 'COMMUNITY_MAGIC_REACH_WARNING_NOTIFY_COPS',
  PENDING_APPROVAL_NOTIFY_CM:
    'COMMUNITY_MAGIC_REACH_PENDING_APPROVAL_NOTIFY_CM',
  BLOCKED_NOTIFY_MANAGER: 'COMMUNITY_MAGIC_REACH_BLOCKED_NOTIFY_MANAGER',
  APPROVED_NOTIFY_MANAGER: 'COMMUNITY_MAGIC_REACH_APPROVED_NOTIFY_MANAGER',
};

const WHATSAPP_VERSION = 'v16.0';
const WHATSAPP_PHONE_URL = `https://graph.facebook.com/${WHATSAPP_VERSION}`;
const WHATSAPP_MESSAGE_TYPE = {
  INITIATE: 'initiate',
  REPLIES: 'replies',
  OPT_IN: 'opt-in',
  OPT_OUT: 'opt-out',
};

const NO_LIMIT = -1;

const COMMUNITY_STATS_WINDOW = {
  LIFETIME: 'lifetime',
  MONTHLY: 'monthly',
};

const WHATSAPP_BUTTON_PAYLOAD_PREFIX = 'magicReachEmailId_';
const WHATSAPP_DEFAULT_TEMPLATE_ID = '6443ad8046474012711642b3';

const PRODUCT_CARD_ATTRIBUTES = {
  LINK_TYPES: {
    CHECKOUT: 'checkout',
    LANDING_PAGE: 'landingPage',
  },
  SIZE: {
    SMALL: 'sm',
    LARGE: 'lg',
  },
  VARIABLES: {
    SLUG: 'slug',
    THUMBNAIL: 'thumbnail',
    TITLE: 'title',
    SUBTITLE_SM: 'subtitle_sm',
    SUBTITLE_LG: 'subtitle_lg',
    PRICE_TAG: 'price_tag',
  },
  LOCALIZATION_KEYS: {
    BUTTON_TEXT_EVENT: 'register',
    BUTTON_TEXT_CHALLENGE: 'join-challenge',
    BUTTON_TEXT_FOLDER: 'buy-now',
    BUTTON_TEXT_SESSION: 'book-now',
    BUTTON_TEXT_SUBSCRIPTION: 'join-now',
    ENTITY_TYPE_EVENT: 'event',
    ENTITY_TYPE_CHALLENGE: 'challenge',
    ENTITY_TYPE_FOLDER: 'digital-product',
    ENTITY_TYPE_SESSION: '1-1-session',
    ENTITY_TYPE_SUBSCRIPTION: 'membership',
    PRICE_TAG_FLEXIBLE: 'pay-what-you-want',
    PRICE_TAG_FREE: 'free',
    SUBTITLE_TEXT_SUBSCRIPTION: 'product-card-membership-subtitle',
    SUBTITLE_TEXT_SESSION: 'product-card-session-subtitle',
    DAYS: 'days',
    DISCOUNT_SUBSCRIPTION_X_MONTHS:
      'product-card-membership-discount-x-months',
    DISCOUNT_SUBSCRIPTION_MONTHLY:
      'product-card-membership-discount-monthly',
    DISCOUNT_SUBSCRIPTION_ANNUALLY:
      'product-card-membership-discount-annually',
  },
  DEFAULT_TEXT: {
    BUTTON_TEXT_EVENT: 'Register',
    BUTTON_TEXT_CHALLENGE: 'Join challenge',
    BUTTON_TEXT_FOLDER: 'Buy now',
    BUTTON_TEXT_SESSION: 'Book now',
    BUTTON_TEXT_SUBSCRIPTION: 'Join now',
    ENTITY_TYPE_EVENT: 'Event',
    ENTITY_TYPE_CHALLENGE: 'Challenge',
    ENTITY_TYPE_FOLDER: 'Digital Product',
    ENTITY_TYPE_SESSION: '1:1 Session',
    ENTITY_TYPE_SUBSCRIPTION: 'Membership',
    PRICE_TAG_FLEXIBLE: 'Pay what you want',
    PRICE_TAG_FREE: 'Free',
    SUBTITLE_TEXT_SUBSCRIPTION: 'By {{ownerName}}',
    SUBTITLE_TEXT_SESSION:
      '{{durationIntervalInMinutes}} min with {{hostName}}',
    DAYS: 'days',
    DISCOUNT_SUBSCRIPTION_X_MONTHS:
      'or save {{percent}}% per {{interval}} months',
    DISCOUNT_SUBSCRIPTION_MONTHLY: 'or save {{percent}}% monthly',
    DISCOUNT_SUBSCRIPTION_ANNUALLY: 'or save {{percent}}% annually',
  },
};

const BUCKET_NAME_TO_BATCH_METADATA_MODEL_TYPE = {
  [BUCKET_NAMES.INTERESTED]: BATCH_METADATA_MODEL_TYPE.MEMBERSHIP,
  [BUCKET_NAMES.UNSUBSCRIBED]: BATCH_METADATA_MODEL_TYPE.MEMBERSHIP,
  [BUCKET_NAMES.INVITED]: BATCH_METADATA_MODEL_TYPE.MEMBERSHIP,
  [BUCKET_NAMES.MEMBERS]: BATCH_METADATA_MODEL_TYPE.MEMBERSHIP,
  [BUCKET_NAMES.MEMBERS_WITH_PHONE_NUMBERS]:
    BATCH_METADATA_MODEL_TYPE.SUBSCRIPTION,
  [BUCKET_NAMES.FOLDER_VIEWERS]: BATCH_METADATA_MODEL_TYPE.FOLDER_VIEWER,
  [BUCKET_NAMES.EVENT_ATTENDEES]: BATCH_METADATA_MODEL_TYPE.EVENT_ATTENDEE,
  [BUCKET_NAMES.CHALLENGE_PARTICIPANTS]:
    BATCH_METADATA_MODEL_TYPE.PROGRAM_PARTICIPANT,
  [BUCKET_NAMES.CUSTOM_FILTERS]: BATCH_METADATA_MODEL_TYPE.MEMBERSHIP,
};

module.exports = {
  WHATSAPP_DEFAULT_TEMPLATE_ID,
  PLATFORMS,
  MAGIC_REACH_MESSAGE_STATUS,
  BUCKET_NAMES,
  SEARCH_RECIPIENT_DB_PAGE_SIZE,
  DB_INSERT_BATCH_SIZE,
  SEND_TO_QUEUE_BATCH_SIZE,
  BUCKET_RELATIONSHIPS,
  GENERIC_REPLY_TO_NAME,
  FALLBACK_REPLY_TO,
  SENDGRID_EVENT,
  WHATSAPP_PHONE_URL,
  FRAUD_CHECK_STATUS,
  MAIL_TYPES,
  WHATSAPP_MESSAGE_TYPE,
  WHATSAPP_EVENT,
  EMAIL_RECIPIENT_FILTER,
  WHATSAPP_RECIPIENT_FILTER,
  COMMUNITY_STATS_WINDOW,
  NO_LIMIT,
  MEMBER_BUCKETS,
  NON_MEMBER_BUKCETS,
  WHATSAPP_BUTTON_PAYLOAD_PREFIX,
  PRODUCT_CARD_ATTRIBUTES,
  EARNING_RECIPIENT_FILTER,
  CLICK_RECIPIENT_FILTER,
  BUCKET_NAME_TO_BATCH_METADATA_MODEL_TYPE,
};
