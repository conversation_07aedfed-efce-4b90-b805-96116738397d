const axios = require('../../../clients/axios.client');

const MagicReachNodeWrapper = require('../contentFormatter/MagicReachNodeWrapper');

const logger = require('../../logger.service');

const {
  getAuthorDetails,
  getReplyToMailName,
} = require('../utils/learner.utils');

const {
  NOTIFICATION_URL,
  NOTIFICATION_AUTH,
  env,
} = require('../../../config');

const { MAIL_TYPES, GENERIC_REPLY_TO_NAME } = require('../constants');

const { createChatComletion } = require('../../../clients/openai.client');
const fraudService = require('../../fraud');

async function createChatForFraudProbability(content) {
  const prompt = await fraudService.getFraudOpenAIPrompt(
    fraudService.PROMPT_NAMES.MAGIC_REACH
  );

  return `${prompt} 
  Give your answer in JSON format with the following structure and instructions:
  {
    "probability": 0-100,
    "reason": "Your reason here, in English, for the user input being suspicious or the scraped content from external links being suspicious.",
  }

  Following is the content:
  ${content}`;
}

async function getAuthor(authorUserObjectId) {
  const author = await getAuthorDetails(authorUserObjectId);
  const authorEmail = author.length > 0 ? author[0].email : undefined;
  if (!authorEmail) {
    throw new Error('CM Email is undefined');
  }
  const authorName =
    author.length > 0
      ? getReplyToMailName(author[0])
      : 'Community Manager';

  return {
    authorEmail,
    authorName,
  };
}

const checkForExternalLink = async ({ content, title }) => {
  const rootPayload = {
    ...content?.root,
    title,
  };
  const links = new MagicReachNodeWrapper(rootPayload).getLinks();

  for (const link of links) {
    try {
      const parsedUrl = new URL(link);
      if (parsedUrl.hostname !== 'nas.io') {
        return true;
      }
    } catch (error) {
      logger.info(
        'Invalid URL encountered for external link check',
        link,
        error.message
      );
      return true;
    }
  }

  return false;
};

const consultOpenAPIForFraudProbability = async ({ content, title }) => {
  const rootPayload = {
    ...content?.root,
    title,
  };
  const formattedMessage = new MagicReachNodeWrapper(
    rootPayload
  ).getMessageForFraudCheck();

  try {
    const blacklistKeywordResult =
      await fraudService.hasBlacklistedKeywords({
        content: `${title}, ${formattedMessage}`,
      });
    if (blacklistKeywordResult.hasBlacklistedKeyword) {
      return {
        probability: 100,
        reason: `The message contains a blacklisted keyword: ${blacklistKeywordResult.blacklistedKeyword}`,
      };
    }
    const chatContent = await createChatForFraudProbability(
      formattedMessage
    );
    const probabilityResponse = await createChatComletion([
      {
        role: 'user',
        content: chatContent,
      },
    ]);
    const resultString =
      probabilityResponse?.choices?.[0]?.message?.content
        ?.replace('```json\n', '')
        ?.replace('\n```', '');
    const result = JSON.parse(resultString);
    const probability = result.probability;
    const reason = result.reason;
    return {
      probability,
      reason,
    };
  } catch (error) {
    if (error.response) {
      logger.error(
        `Error in calling OpenAI chatCompletion|`,
        `errorStatus=${error.response.status}|`,
        `errorData=${error.response.data}`
      );
    } else {
      logger.error(`Error with OpenAI API request|error=${error.message}`);
    }
    return {
      probability: 0,
    };
  }
};

/**
 * Send an email to COPs mailbox notifying a potential fraud Magic Reach message
 * @param {*} param
 */
const notifyCOPSOnPendingApproval = async ({
  community,
  reason,
  title,
}) => {
  const emailData = {
    reason,
    communityName: community.title,
    title,
    communityCode: community.code,
    env,
  };
  try {
    const data = {
      mailType: MAIL_TYPES.PENDING_APPROVAL_NOTIFY_COPS,
      mailSubject: `[${env.toLocaleUpperCase()}] Pending approval message from ${
        community.title
      } community (code ${community.code})`,
      requesterServiceName: 'LPBE',
      data: emailData,
    };
    await axios.post(`${NOTIFICATION_URL}/api/v1/send-email`, data, {
      headers: {
        Authorization: `Bearer ${NOTIFICATION_AUTH}`,
      },
    });
  } catch (error) {
    logger.error(
      'Error on axios post request to notify COPS on pending approval Magic Reach|',
      `error=${error}|`,
      `trace=${error.stack}`
    );
  }
};

/**
 * Send an email to COPs mailbox notifying a potential fraud Magic Reach message
 * @param {*} param
 */
const notifyCOPSOnWarning = async ({ community, reason, title }) => {
  const emailData = {
    reason,
    communityName: community.title,
    title,
    communityCode: community.code,
    env,
  };
  try {
    const data = {
      mailType: MAIL_TYPES.WARNING_NOTIFY_COPS,
      mailSubject: `[${env.toLocaleUpperCase()}] Magic Reach potential fraud message warning from ${
        community.title
      } community (code ${community.code}) `,
      requesterServiceName: 'LPBE',
      data: emailData,
    };
    await axios.post(`${NOTIFICATION_URL}/api/v1/send-email`, data, {
      headers: {
        Authorization: `Bearer ${NOTIFICATION_AUTH}`,
      },
    });
  } catch (error) {
    logger.error(
      'Error on axios post request to notify COPS on pending approval Magic Reach|',
      `error=${error}|`,
      `trace=${error.stack}`
    );
  }
};

/**
 * Send an email to COPs mailbox notifying a potential fraud Magic Reach message that needs approval
 * @param {*} param
 */
const notifyCMOnPendingApproval = async ({
  title,
  authorUserObjectId,
}) => {
  const { authorEmail, authorName } = await getAuthor(authorUserObjectId);
  try {
    const emailData = {
      magicReachMessageTitle: title,
    };

    const data = {
      mailType: MAIL_TYPES.PENDING_APPROVAL_NOTIFY_CM,
      mailSubject: `Your latest Magic Reach message is in review`,
      toMail: [authorEmail],
      toMailName: [authorName],
      requesterServiceName: 'LPBE',
      data: emailData,
    };
    await axios.post(`${NOTIFICATION_URL}/api/v1/send-email`, data, {
      headers: {
        Authorization: `Bearer ${NOTIFICATION_AUTH}`,
      },
    });
  } catch (error) {
    logger.error(
      'Error on axios post request to notify CM on pending approval Magic Reach|',
      `error=${error}|`,
      `trace=${error.stack}`
    );
  }
};

/**
 * Send an email to CM notifying that a Magic reach message is blocked due to fraud
 * @param {*} param0
 */
const notifyManagerOnApproved = async ({ title, authorUserObjectId }) => {
  const { authorEmail, authorName } = await getAuthor(authorUserObjectId);
  const emailData = {
    magicReachMessageTitle: title,
  };
  try {
    const data = {
      mailType: MAIL_TYPES.APPROVED_NOTIFY_MANAGER,
      mailSubject: 'Your magic reach message is approved',
      toMail: [authorEmail],
      toMailName: [authorName],
      requesterServiceName: 'LPBE',
      data: emailData,
    };
    await axios.post(`${NOTIFICATION_URL}/api/v1/send-email`, data, {
      headers: {
        Authorization: `Bearer ${NOTIFICATION_AUTH}`,
      },
    });
  } catch (error) {
    logger.error(
      'Error on axios post request to notify CM on Magic Reach message approved|',
      `error=${error}|`,
      `trace=${error.stack}`
    );
  }
};

/**
 * Send an email to CM notifying that a Magic reach message is blocked due to fraud
 * @param {*} param0
 */
const notifyManagerOnBlocked = async ({ title, authorUserObjectId }) => {
  const { authorEmail, authorName } = await getAuthor(authorUserObjectId);
  const emailData = {
    magicReachMessageTitle: title,
  };
  try {
    const data = {
      mailType: MAIL_TYPES.BLOCKED_NOTIFY_MANAGER,
      mailSubject: 'Your magic reach message is blocked',
      toMail: [authorEmail],
      toMailName: [authorName],
      requesterServiceName: 'LPBE',
      data: emailData,
    };
    await axios.post(`${NOTIFICATION_URL}/api/v1/send-email`, data, {
      headers: {
        Authorization: `Bearer ${NOTIFICATION_AUTH}`,
      },
    });
  } catch (error) {
    logger.error(
      'Error on axios post request to notify CM on Magic Reach message being blocked|',
      `error=${error}|`,
      `trace=${error.stack}`
    );
  }
};

module.exports = {
  consultOpenAPIForFraudProbability,
  checkForExternalLink,
  notifyCMOnPendingApproval,
  notifyCOPSOnPendingApproval,
  notifyCOPSOnWarning,
  notifyManagerOnBlocked,
  notifyManagerOnApproved,
};
