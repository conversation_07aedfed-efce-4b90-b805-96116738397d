/* eslint-disable no-unused-vars */
const logger = require('./logger.service');
const membershipService = require('./membership');
const {
  COMMUNITY_EVENT_ACCESS_TYPES,
  communityLibraryStatusMap,
} = require('../communitiesAPI/constants');
const CommunityEventsModel = require('../communitiesAPI/models/communityEvents.model');
const revenueTransactionsSchema = require('../models/revenueTransaction.model');
const CommunityModel = require('../communitiesAPI/models/community.model');
const communityFoldersModel = require('../communitiesAPI/models/communityFolders.model');
const ProgramModel = require('../models/program/program.model');

// few constants
const taskIds = {
  HOW_DO_I_USE_NAS_IO: 'HOW_DO_I_USE_NAS_IO',
  COMPLETE_YOUR_COMMUNITY_INFO: 'COMPLETE_YOUR_COMMUNITY_INFO',
  CONNECT_YOUR_CHAT_GROUP: 'CONNECT_YOUR_CHAT_GROUP',
  SET_MEMBERSHIP_PRICING: 'SET_MEMBERSHIP_PRICING',
  GET_YOUR_FIRST_N_SALES: 'GET_YOUR_FIRST_N_SALES',
  HOST_A_PAID_EVENT: 'HOST_A_PAID_EVENT',
  SELL_ANY_CONTENT: 'SELL_ANY_CONTENT',
  GET_YOUR_N_SALES: 'GET_YOUR_N_SALES',
  INVITE_YOUR_N_MEMBERS: 'INVITE_YOUR_N_MEMBERS',
  ADD_CHAT_BOT: 'ADD_CHAT_BOT',
  COMPLETE_YOUR_COMMUNITY_LINK: 'COMPLETE_YOUR_COMMUNITY_LINK',
  SETUP_PAID_MEMBERSHIP: 'SETUP_PAID_MEMBERSHIP',
  HOST_AN_EVENT: 'HOST_AN_EVENT',
  SHARE_A_DIGITAL_PRODUCT: 'SHARE_A_DIGITAL_PRODUCT',
  INVITE_YOUR_1_MEMBER: 'INVITE_YOUR_1_MEMBER',
  GET_YOUR_FIRST_10_SALES_ON_ANYTHING:
    'GET_YOUR_FIRST_10_SALES_ON_ANYTHING',
  START_A_CHALLENGE: 'START_A_CHALLENGE',
};

// https://d2oi1rqwb0pj00.cloudfront.net/nasIO/onboarding/jpg/default-community-logo-
const defaultThumbnailImageForCommunity =
  'https://d2oi1rqwb0pj00.cloudfront.net/nasIO/onboarding/jpg/default-community-logo-';
const defaultFullScreenBannerImageForCommunity =
  'https://d2oi1rqwb0pj00.cloudfront.net/nasIO/onboarding/png/default-background-1.png';
const {
  HOW_DO_I_USE_NAS_IO,
  COMPLETE_YOUR_COMMUNITY_INFO,
  CONNECT_YOUR_CHAT_GROUP,
  SET_MEMBERSHIP_PRICING,
  GET_YOUR_FIRST_N_SALES,
  HOST_A_PAID_EVENT,
  SELL_ANY_CONTENT,
  GET_YOUR_N_SALES,
  INVITE_YOUR_N_MEMBERS,
  ADD_CHAT_BOT,
  INVITE_YOUR_1_MEMBER,
  SHARE_A_DIGITAL_PRODUCT,
  HOST_AN_EVENT,
  SETUP_PAID_MEMBERSHIP,
  COMPLETE_YOUR_COMMUNITY_LINK,
  GET_YOUR_FIRST_10_SALES_ON_ANYTHING,
  START_A_CHALLENGE,
} = taskIds;

const communityInfoStatus = (community) => {
  // check if the community has a thumbnail image, full screen banner image and description, if yes then return true else false
  const { thumbnailImgData, fullScreenBannerImgData } = community || {};
  let isCommunityInfoStatusCompleted = false;
  // regex to check if the thumbnail image is the default image
  const isThumbnailChanged = !thumbnailImgData?.desktopImgData?.src?.match(
    `/${defaultThumbnailImageForCommunity}`
  );

  const isDescriptionWritten = community?.description !== '';

  if (isThumbnailChanged && isDescriptionWritten) {
    isCommunityInfoStatusCompleted = true;
  }
  return isCommunityInfoStatusCompleted;
};

const checkIfCommunityHasNMembers = async (community, n = 100) => {
  const memberCountInfo =
    await membershipService.countService.countCommunityMembers({
      communityId: community._id,
    });
  const countOfMembers = memberCountInfo.summary.memberCount;
  if (countOfMembers >= n) {
    return true;
  }
  return false;
};

const checkIfCommunityHasMoreThanOneMember = async (community) => {
  const memberCountInfo =
    await membershipService.countService.countCommunityMembers({
      communityId: community._id,
    });
  const countOfMembers = memberCountInfo.summary.memberCount;
  if (countOfMembers > 1) {
    return true;
  }
  return false;
};
const checkCommunityIsAPaidCommunity = async (community) => {
  const { isPaidCommunity } = community || {};
  return isPaidCommunity ?? false;
};
const checkIfCommunityHasPaidFolders = async (community) => {
  const folderInfo = await communityFoldersModel.findOne({
    communityObjectId: community._id,
    access: COMMUNITY_EVENT_ACCESS_TYPES.PAID,
    status: communityLibraryStatusMap.PUBLISHED,
  });

  if (folderInfo) {
    return true;
  }
  return false;
};
// helper function to return the task information for the particular subtask
const getTaskInformationForSubTask = (
  subTaskCompletionInformation,
  taskStatusInfo,
  isTaskCompletedBefore,
  parentGoalId
) => {
  if (isTaskCompletedBefore) {
    return subTaskCompletionInformation;
  }
  const taskInformationForSubTask = {
    ...subTaskCompletionInformation,
  };
  if (parentGoalId) {
    taskInformationForSubTask.goalId = parentGoalId;
  }
  taskInformationForSubTask.isCompleted = taskStatusInfo;
  if (!isTaskCompletedBefore && taskStatusInfo) {
    taskInformationForSubTask.completedDate = new Date().toISOString();
  }
  return taskInformationForSubTask;
};
const isPaidCommunityEventHosted = async (community) => {
  // a covered query is used here to avoid the index scan because no fields are used
  const communityEvents = await CommunityEventsModel.findOne(
    {
      communities: community._id,
      access: COMMUNITY_EVENT_ACCESS_TYPES.PAID,
    },
    {
      _id: 0,
      communities: 1,
    }
  ).limit(1);
  if (communityEvents) {
    return true;
  }

  return false;
};

const isEventHosted = async (community) => {
  const communityEvents = await CommunityEventsModel.findOne(
    {
      communities: community._id,
    },
    {
      _id: 0,
      communities: 1,
    }
  );
  if (communityEvents) {
    return true;
  }
  return false;
};

const isChallengeHosted = async (community) => {
  const communityChallenge = await ProgramModel.findOne(
    {
      communityObjectId: community._id,
    },
    {
      _id: 0,
      communityObjectId: 1,
    }
  );

  if (communityChallenge) {
    return true;
  }
  return false;
};

const isCoursePublished = async (community) => {
  const communityFolders = await communityFoldersModel.findOne(
    {
      communityObjectId: community._id,
      status: communityLibraryStatusMap.PUBLISHED,
    },
    {
      _id: 0,
      communityObjectId: 1,
    }
  );
  if (communityFolders) {
    return true;
  }
  return false;
};
// this function is used to get the task completion information for the community, related to sales (custom logic here)
const getTaskCompletionForCommunitySales = async (
  community,
  type,
  numberOfSales
) => {
  try {
    let limitForNumberOfSales = 1;
    let filter = {
      communityObjectId: community._id,
    };
    if (type === 'GET_YOUR_FIRST_N_SALES_COMMUNITY') {
      filter = {
        ...filter,
        purchaseType: 'SUBSCRIPTION',
      };
      limitForNumberOfSales = 10;
    }
    if (type === 'SELL_ANY_CONTENT') {
      filter = {
        ...filter,
        purchaseType: 'FOLDER',
      };
      limitForNumberOfSales = 1;
    }

    if (type === 'GET_YOUR_N_SALES_EVENTS_FOLDERS') {
      filter.purchaseType = { $in: ['FOLDER', 'EVENT'] };
      limitForNumberOfSales = 10;
    }

    const revenueTransactions = await revenueTransactionsSchema
      .find({
        ...filter,
      })
      .limit(limitForNumberOfSales)
      .count();

    if (revenueTransactions >= numberOfSales) {
      return true;
    }
    return false;
  } catch (error) {
    logger.error(
      `Error in getTaskCompletionForCommunitySales for community ${community._id} and type ${type} and numberOfSales ${numberOfSales}`,
      error,
      error.stack
    );
    return false;
  }
};

const checkIfCommunityHas10SalesOnAnything = async (community) => {
  const revenueTransactions = await revenueTransactionsSchema
    .find({
      communityObjectId: community._id,
      transactionType: 'INBOUND',
    })
    .limit(10);
  return revenueTransactions.length === 10;
};

//  subTaskCompletionInformation should look like this
/*
                {
                    subTaskObjectId: new ObjectId('')
                    subTaskId: 'HOW_TO_USE_NAS_IO',
                    completedDate: so and so,
                    isCompleted: true
                }
*/
const getSubTaskCompletionInformation = async (
  subTaskUniqueId,
  community,
  taskUniqueId,
  parentGoalId
) => {
  // getting the old task progress
  const { taskMetaData } = community;
  // checking if the task is completed before
  const isTaskCompletedBefore =
    taskMetaData?.tasksProgress?.[taskUniqueId]?.subTasks?.[
      subTaskUniqueId
    ]?.isCompleted ?? false;

  // this variable is for storing the task status information type : boolean
  let taskStatusInfo = null;
  // this variable is for storing the subtask completion information
  const subTaskCompletionInformation = {
    subTaskId: subTaskUniqueId,
    isCompleted: isTaskCompletedBefore,
  };

  // if the task is completed before then we will return the old task progress and we dont have to do anything
  if (isTaskCompletedBefore) {
    if (parentGoalId) {
      return {
        ...subTaskCompletionInformation,
        completedDate:
          taskMetaData?.tasksProgress?.[taskUniqueId]?.subTasks?.[
            subTaskUniqueId
          ]?.completedDate ?? new Date().toISOString(),
        goalId: parentGoalId,
      };
    }
    return subTaskCompletionInformation?.completedDate
      ? {
          ...subTaskCompletionInformation,
          completedDate: subTaskCompletionInformation?.completedDate,
        }
      : subTaskCompletionInformation;
  }

  // switch case for the subtask unique id, custom logic here
  switch (subTaskUniqueId) {
    case HOW_DO_I_USE_NAS_IO:
      return {
        subTaskId: HOW_DO_I_USE_NAS_IO,
        isCompleted: isTaskCompletedBefore,
      };
    case COMPLETE_YOUR_COMMUNITY_INFO:
      taskStatusInfo = await communityInfoStatus(
        community,
        isTaskCompletedBefore
      );
      return getTaskInformationForSubTask(
        subTaskCompletionInformation,
        taskStatusInfo,
        isTaskCompletedBefore,
        parentGoalId
      );
    case CONNECT_YOUR_CHAT_GROUP:
      // checking if the community has a chat group
      taskStatusInfo = community?.platforms?.length > 0;
      return getTaskInformationForSubTask(
        subTaskCompletionInformation,
        taskStatusInfo,
        isTaskCompletedBefore,
        parentGoalId
      );
    case SET_MEMBERSHIP_PRICING:
      // checking if the community has a membership pricing
      taskStatusInfo = community?.isPaidCommunity ?? false;
      return getTaskInformationForSubTask(
        subTaskCompletionInformation,
        taskStatusInfo,
        isTaskCompletedBefore,
        parentGoalId
      );
    case HOST_A_PAID_EVENT:
      // checking if the community has a paid event
      taskStatusInfo = await isPaidCommunityEventHosted(community);
      return getTaskInformationForSubTask(
        subTaskCompletionInformation,
        taskStatusInfo,
        isTaskCompletedBefore,
        parentGoalId
      );
    case GET_YOUR_FIRST_N_SALES:
      taskStatusInfo = await getTaskCompletionForCommunitySales(
        community,
        'GET_YOUR_FIRST_N_SALES_COMMUNITY',
        10
      );

      return getTaskInformationForSubTask(
        subTaskCompletionInformation,
        taskStatusInfo,
        isTaskCompletedBefore,
        parentGoalId
      );
    case SELL_ANY_CONTENT:
      taskStatusInfo = await checkIfCommunityHasPaidFolders(community);
      return getTaskInformationForSubTask(
        subTaskCompletionInformation,
        taskStatusInfo,
        isTaskCompletedBefore,
        parentGoalId
      );
    case GET_YOUR_N_SALES:
      taskStatusInfo = await getTaskCompletionForCommunitySales(
        community,
        'GET_YOUR_N_SALES_EVENTS_FOLDERS',
        10
      );
      return getTaskInformationForSubTask(
        subTaskCompletionInformation,
        taskStatusInfo,
        isTaskCompletedBefore,
        parentGoalId
      );
    case INVITE_YOUR_N_MEMBERS:
      taskStatusInfo = await checkIfCommunityHasNMembers(community, 100);
      return getTaskInformationForSubTask(
        subTaskCompletionInformation,
        taskStatusInfo,
        isTaskCompletedBefore,
        parentGoalId
      );
    case ADD_CHAT_BOT:
      taskStatusInfo = community?.bots?.length > 0;
      return getTaskInformationForSubTask(
        subTaskCompletionInformation,
        taskStatusInfo,
        isTaskCompletedBefore,
        parentGoalId
      );
    case COMPLETE_YOUR_COMMUNITY_LINK:
      taskStatusInfo = true; // by default we will set it to true
      return getTaskInformationForSubTask(
        subTaskCompletionInformation,
        taskStatusInfo,
        isTaskCompletedBefore,
        parentGoalId
      );

    case SETUP_PAID_MEMBERSHIP:
      taskStatusInfo = await checkCommunityIsAPaidCommunity(community);
      return getTaskInformationForSubTask(
        subTaskCompletionInformation,
        taskStatusInfo,
        isTaskCompletedBefore,
        parentGoalId
      );
    case START_A_CHALLENGE:
      taskStatusInfo = await isChallengeHosted(community);
      return getTaskInformationForSubTask(
        subTaskCompletionInformation,
        taskStatusInfo,
        isTaskCompletedBefore,
        parentGoalId
      );

    case HOST_AN_EVENT:
      taskStatusInfo = await isEventHosted(community);
      return getTaskInformationForSubTask(
        subTaskCompletionInformation,
        taskStatusInfo,
        isTaskCompletedBefore,
        parentGoalId
      );

    case SHARE_A_DIGITAL_PRODUCT:
      taskStatusInfo = await isCoursePublished(community);
      return getTaskInformationForSubTask(
        subTaskCompletionInformation,
        taskStatusInfo,
        isTaskCompletedBefore,
        parentGoalId
      );

    case INVITE_YOUR_1_MEMBER:
      taskStatusInfo = await checkIfCommunityHasMoreThanOneMember(
        community
      );
      return getTaskInformationForSubTask(
        subTaskCompletionInformation,
        taskStatusInfo,
        isTaskCompletedBefore,
        parentGoalId
      );

    case GET_YOUR_FIRST_10_SALES_ON_ANYTHING:
      taskStatusInfo = await checkIfCommunityHas10SalesOnAnything(
        community
      );
      return getTaskInformationForSubTask(
        subTaskCompletionInformation,
        taskStatusInfo,
        isTaskCompletedBefore,
        parentGoalId
      );
    default:
      return {};
  }
};

// this function is for getting the task information for the subtask
const getTotalStepsForTaskWithGoalId = (task, goalId) => {
  let totalSteps = 0;
  if (task?.subTasks?.length > 0) {
    task.subTasks.forEach((subTask) => {
      if (subTask.parentGoalId === goalId) {
        totalSteps += 1;
      }
    });
  }
  return totalSteps;
};

const getSelectedTaskIdForTask = (task, community) => {
  // comparing the taskId to check if it comes under the goal category
  if (task.taskId === 'MAKE_YOUR_FIRST_DOLLAR_TASK') {
    // custom logic here to check set the goal
    const { isFreeCommunity, isPaidCommunity } = community || {};
    if (isFreeCommunity)
      return {
        selectedGoalIdForTask: 'SELL_CONTENT_MONETIZATION_GOAL',
        noOfSteps: getTotalStepsForTaskWithGoalId(
          task,
          'SELL_CONTENT_MONETIZATION_GOAL'
        ),
      };
    if (isPaidCommunity)
      return {
        selectedGoalIdForTask: 'SELL_MEMBERSHIP_MONETIZATION_GOAL',
        noOfSteps: getTotalStepsForTaskWithGoalId(
          task,
          'SELL_MEMBERSHIP_MONETIZATION_GOAL'
        ),
      };
    // for NFT community
    return 'SELL_CONTENT_MONETIZATION_GOAL';
  }
  return {
    selectedGoalIdForTask: null,
    noOfSteps: null,
  };
};

const getSuperChargeTasksInformation = async (
  allTasks,
  community,
  storeProgressInDb
) => {
  try {
    // this variable is for storing the progress of all the tasks
    let percentageOfTasksCompleted = 0;
    // this variable is for storing the task progress of all the tasks, both of them will be stored in the community
    const tasksProgress = {};

    // this variable is for storing the number of the tasks completed
    let totalTasksCompleted = 0;

    // looping through all the tasks and getting the progress of each task
    for await (const task of allTasks) {
      // destructuring the task object and getting the subtasks array from task, can check task definition and subtask definition in the https://nasacademy.atlassian.net/wiki/spaces/TS/pages/552042523/Entity+Definitions+for+super+charge+v2+feature documentation
      const { subTasks, taskCompletionCriteria } = task;

      // this variable is for checking what is goal id of the subTask, if the goal id is null then it means that the task is not a part of any goal (and this is an optional field )
      const { selectedGoalIdForTask, noOfSteps } =
        await getSelectedTaskIdForTask(task, community);

      /* subTasks Progress should look like https://nasacademy.atlassian.net/wiki/spaces/TS/pages/552042523/Entity+Definitions+for+super+charge+v2+feature (refer to subtasks progress)
        this variable will be used to store the progress of all the subtasks of a task
    */
      const subTasksProgress = {};
      // this variable will be used to store the progress of all the subtasks of a task
      let subTasksCompleted = 0;
      // Looping through all the subtasks of a task and getting the progress of each subtask
      for await (const subTask of subTasks) {
        // this is the unique id of the subtask
        const { taskUniqueId } = subTask;
        /*
        the subTaskCompletionInformation should look like this https://nasacademy.atlassian.net/wiki/spaces/TS/pages/552042523/Entity+Definitions+for+super+charge+v2+feature (refer to subTaskCompleteSchema information)
            {
                subTaskId: 'HOW_TO_USE_NAS_IO',
                completedDate: so and so,
                isCompleted: true
            }
        with this helper function we will get the progress of the subtask
        */
        const subTaskCompletionInformation =
          await getSubTaskCompletionInformation(
            taskUniqueId,
            community,
            task?.taskId,
            subTask?.parentGoalId
          );
        //  if the subtask is completed then we will increment the subTasksCompleted variable
        subTasksCompleted += subTaskCompletionInformation.isCompleted
          ? 1
          : 0;
        //   storing the progress of the subtask in the subTasksProgress variable
        subTasksProgress[taskUniqueId] = subTaskCompletionInformation;
      }

      // storing the progress of the task in the tasksProgress variable
      tasksProgress[task.taskId] = {
        subTasks: subTasksProgress,
      };

      if (!selectedGoalIdForTask) {
        // this is to show how many steps are left in the task
        tasksProgress[task.taskId].stepsLeft =
          subTasks.length - subTasksCompleted;

        tasksProgress[task.taskId].isCompleted =
          subTasksCompleted === subTasks.length;
      } else {
        // this is to show how many steps are left in the task for a particular goal
        tasksProgress[task.taskId].stepsLeft = Object.values(
          tasksProgress[task.taskId].subTasks
          // eslint-disable-next-line array-callback-return
        ).filter((subTask) => {
          if (subTask.goalId === selectedGoalIdForTask) {
            return subTask.isCompleted === false;
          }
        }).length;

        tasksProgress[task.taskId].isCompleted =
          Object.values(
            tasksProgress[task.taskId].subTasks
            // eslint-disable-next-line array-callback-return
          ).filter((subTask) => {
            if (subTask.goalId === selectedGoalIdForTask) {
              return subTask.isCompleted === true;
            }
          }).length === noOfSteps;
        // this is to show the selected goal for the task
        tasksProgress[task.taskId].selectedGoalIdForTask =
          selectedGoalIdForTask;
      }

      // if the task completion criteria is ALL then we will mark the task as completed only if all the subtasks are completed
      // if the task completion criteria is ANY then we will mark the task as completed if any one of the subtask is completed
      if (taskCompletionCriteria === 'ANY') {
        // if any one of the subtask is completed then we will mark the task as completed
        tasksProgress[task.taskId].isCompleted =
          Object.values(tasksProgress[task.taskId].subTasks).filter(
            (subTask) => {
              return subTask.isCompleted === true;
            }
          ).length > 0;
      }
      if (tasksProgress[task.taskId].isCompleted) {
        // TODO: add one for if condition for the new tasks that involves in making the task complete when any one of the task is completed

        totalTasksCompleted += 1;
      }
    }

    // calculating the percentage of tasks completed
    percentageOfTasksCompleted = Math.round(
      (totalTasksCompleted / allTasks.length) * 100
    );

    logger.info(
      `the current Progress of the community with ID ${
        community._id
      } is ${JSON.stringify(tasksProgress)}`
    );

    if (storeProgressInDb) {
      // updating the community with the new progress
      await CommunityModel.findOneAndUpdate(
        {
          _id: community._id,
        },
        {
          $set: {
            taskMetaData: {
              tasksProgress,
              tasksCompletionPercentage: percentageOfTasksCompleted,
            },
          },
        }
      );
    }

    // returning the progress of all the tasks and the percentage of tasks completed
    return {
      tasksProgress,
      percentageOfTasksCompleted,
    };
  } catch (error) {
    logger.info(
      'getSuperChargeTasksInformation failed due to',
      error,
      error.stack
    );
    const err = new Error(
      'Something went wrong while getting the progress'
    );
    err.status = 500;
    throw err;
  }
};

const updateCommunityProgress = async ({
  communityId,
  taskId,
  subTaskId,
  isCompleted,
}) => {
  try {
    // getting the community with the communityId, and its taskMetaData
    const communityTasks = await CommunityModel.findOne(
      {
        _id: communityId,
      },
      {
        taskMetaData: 1,
        _id: 0,
      }
    );

    // destructuring the communityTasks object and getting the taskMetaData from it
    const { taskMetaData } = communityTasks || {};
    const { tasksProgress } = taskMetaData || {};

    // this variable will be used to store the subtasks of a task

    const subTasks = tasksProgress[taskId]?.subTasks || {};
    // this is the query that will be used to update the community progress
    const updateQuery = {};

    // this variable will be used to store the progress of all the subtasks of a task for a particular goal
    let numberOfParticularGoalSubTasks = 0;
    // this variable will be used to store the progress of all the subtasks of a task
    const goalId = tasksProgress?.[taskId]?.selectedGoalIdForTask;

    // if the goalId is present then we will get the number of subtasks of a task for a particular goal
    if (goalId) {
      numberOfParticularGoalSubTasks = Object.values(subTasks).filter(
        (subTaskIDInLoop) => {
          return subTaskIDInLoop.goalId === goalId;
        }
      ).length;
    }
    // if the subTaskId is present then we will update the progress of the subtask
    if (subTaskId && taskId) {
      updateQuery[
        `taskMetaData.tasksProgress.${taskId}.subTasks.${subTaskId}.isCompleted`
      ] = isCompleted;

      // check how many subtasks are completed including the current subtask
      const subTasksCompleted = Object.values(subTasks).filter(
        (subTaskIDInLoop) => {
          if (subTaskIDInLoop.subTaskId === subTaskId) {
            return true;
          }
          if (goalId) {
            return (
              subTaskIDInLoop.goalId === goalId &&
              subTaskIDInLoop.isCompleted
            );
          }
          return subTaskIDInLoop.isCompleted;
        }
      ).length;
      // if all the subtasks are completed then we will mark the task as completed else we will mark it as not completed
      if (subTasksCompleted === Object.keys(subTasks).length) {
        updateQuery[`taskMetaData.tasksProgress.${taskId}.isCompleted`] =
          isCompleted;
      }

      // update the stepsLeft in the tasksProgress
      if (numberOfParticularGoalSubTasks) {
        updateQuery[`taskMetaData.tasksProgress.${taskId}.stepsLeft`] =
          numberOfParticularGoalSubTasks - subTasksCompleted;
      } else {
        updateQuery[`taskMetaData.tasksProgress.${taskId}.stepsLeft`] =
          Object.keys(subTasks).length - subTasksCompleted;
      }
      // check how many subtasks are there in the task
    }
    // if the taskId is present then we will update the progress of the task
    if (taskId && !subTaskId) {
      updateQuery[`taskMetaData.tasksProgress.${taskId}.isCompleted`] =
        isCompleted;

      // make all the subtasks of the task as completed ( they are objects inside the subTasks object of the task)
      Object.keys(subTasks).forEach((subTaskIdInLoop) => {
        updateQuery[
          `taskMetaData.tasksProgress.${taskId}.subTasks.${subTaskIdInLoop}.isCompleted`
        ] = isCompleted;
      });

      // update the stepsLeft in the tasksProgress
      updateQuery[`taskMetaData.tasksProgress.${taskId}.stepsLeft`] = 0;
    }

    const updatedCommunityInfo = await CommunityModel.findOneAndUpdate(
      {
        _id: communityId,
      },
      {
        $set: updateQuery,
      },
      {
        new: true,
      }
    );
    logger.info(`updated community progress ${updatedCommunityInfo}`);
    return {
      tasksProgress: updatedCommunityInfo?.taskMetaData?.tasksProgress,
    };
  } catch (error) {
    logger.info(
      'updateCommunityProgress failed due to',
      error,
      error.stack
    );
    const err = new Error(
      'Something went wrong while updating the progress'
    );
    err.status = 500;
    throw err;
  }
};
module.exports = {
  communityInfoStatus,
  checkIfCommunityHasMoreThanOneMember,
  getSuperChargeTasksInformation,
  updateCommunityProgress,
};
