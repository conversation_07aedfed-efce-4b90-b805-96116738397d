const { SCRAPE_SOCIAL_MEDIA_INFO_SQS_QUEUE_URL } = require('../../config');
const {
  SUPPORTED_SOCIAL_MEDIA_TYPES_FOR_TEMPLATES,
} = require('../../constants/common');
const { sendMessageToSQSQueue } = require('../../handlers/sqs.handler');
const learnersModel = require('../../models/learners.model');
const usersModel = require('../../models/users.model');
const logger = require('../logger.service');

const checkIfScrapingIsRequired = async ({ learnerObjectId }) => {
  const learnerInfo = await learnersModel
    .findOne({ _id: learnerObjectId })
    .select('socialMedia')
    .lean();

  const socialMedia = learnerInfo?.socialMedia || [];

  const acceptedSocialMediaExists = socialMedia.some(({ type }) =>
    SUPPORTED_SOCIAL_MEDIA_TYPES_FOR_TEMPLATES.includes(type)
  );
  return acceptedSocialMediaExists;
};

const scrapeSocialMediaInfo = async ({ communityId, learnerObjectId }) => {
  try {
    const isScrapingRequired = await checkIfScrapingIsRequired({
      learnerObjectId,
    });

    if (!isScrapingRequired) {
      return;
    }

    const { _id: userObjectId } = await usersModel
      .findOne({
        learner: learnerObjectId,
      })
      .select('_id')
      .lean();

    await sendMessageToSQSQueue({
      queueUrl: SCRAPE_SOCIAL_MEDIA_INFO_SQS_QUEUE_URL,
      messageBody: {
        data: {
          userObjectId,
          communityObjectId: communityId,
        },
        requestor: 'learning portal backend',
      },
    });
  } catch (error) {
    logger.error('Error scraping social media info: ', error);
  }
};

module.exports = {
  scrapeSocialMediaInfo,
};
