const mongoose = require('mongoose');
const { FOLDER_ITEM_STATUS } = require('../../communitiesAPI/constants');
const communityFolderItemsModel = require('../../communitiesAPI/models/communityFolderItems.model');
const {
  getFolderItemsWithFilter,
  updateManyFolderItems,
} = require('../../communitiesAPI/services/common/communityFolderItems/index.service');
const {
  COVER_MEDIA_TYPES,
} = require('../../constants/coverMediaItems.constant');
const { InternalError } = require('../../utils/error.util');
const {
  hasVideoCoverMediaItems,
} = require('../../utils/multipleCoverMediaItems.util');
const logger = require('../logger.service');
const storageUsageService = require('../featurePermissions/storageUsage.service');

const { ObjectId } = mongoose.Types;

exports.verifyVideoCoverMediaItems = async ({
  entityObjectId,
  coverMediaItems,
}) => {
  // folderItem.folderObjectId is set to communityObjectId initially until entity is created with hasValidFolderObjectId=false.
  // after creating the entity, we want to update folderObjectId to be entityObjectId and set hasValidFolderObjectId=true.
  const videoFolderItemIds = coverMediaItems
    .filter((item) => item.mediaType === COVER_MEDIA_TYPES.VIDEO)
    .map((item) => item.folderItemId);

  if (!videoFolderItemIds.length) return;

  const folderItemsFilter = {
    _id: { $in: videoFolderItemIds },
  };
  const videoFolderItems = await getFolderItemsWithFilter(
    folderItemsFilter
  );

  if (!videoFolderItems.length) return;

  const invalidVideoFolderItems = videoFolderItems.filter(
    (item) => item.hasValidFolderObjectId === false
  );

  if (!invalidVideoFolderItems.length) return;

  // update folderObjectId and hasValidFolderObjectId for invalid video folder items
  const invalidVideoFolderItemIds = invalidVideoFolderItems.map(
    (item) => item._id
  );

  logger.info(
    `Found ${invalidVideoFolderItemIds.length} unverified video folder items for entity ${entityObjectId}`,
    invalidVideoFolderItemIds
  );
  const filter = {
    _id: { $in: invalidVideoFolderItemIds },
  };
  const update = {
    $set: {
      folderObjectId: entityObjectId,
      hasValidFolderObjectId: true,
    },
  };

  const result = await updateManyFolderItems(filter, update);
  return result;
};

/**
 * Deletes removed video cover media items by marking them as deleted
 * @param {Array} oldCoverMediaItems - Previous cover media items
 * @param {Array} newCoverMediaItems - New cover media items
 * @param {Object} session - MongoDB session (optional)
 * @param {String} communityId - Community ID for storage tracking (optional)
 */
exports.deleteRemovedVideoCoverMediaItems = async ({
  oldCoverMediaItems,
  newCoverMediaItems,
  session,
  communityId, // Optional - storage tracking only happens if provided
}) => {
  try {
    // if no oldCoverMediaItems then do nothing
    if (
      !Array.isArray(newCoverMediaItems) || // invalid newCoverMediaItems
      !Array.isArray(oldCoverMediaItems) || // invalid oldCoverMediaItems
      (Array.isArray(oldCoverMediaItems) && !oldCoverMediaItems.length) // no items in oldCoverMediaItems
    ) {
      return;
    }

    const oldVideoCoverMediaItems = oldCoverMediaItems.filter(
      (item) => item.mediaType === COVER_MEDIA_TYPES.VIDEO
    );

    // if no oldVideoCoverMediaItems then do nothing
    if (!oldVideoCoverMediaItems.length) return;

    const oldFolderItemIds = oldVideoCoverMediaItems.map(
      (item) => item.folderItemId
    );

    // newFolderItemIdsMap is a map of {[folderItemsId]: true}. For faster lookup
    const newFolderItemIdsMap = newCoverMediaItems.reduce((acc, item) => {
      if (item.mediaType !== COVER_MEDIA_TYPES.VIDEO || !item.folderItemId)
        return acc;
      acc[item.folderItemId] = true;
      return acc;
    }, {});

    // get folderItems to delete i.e folderItems which are not present in newCoverMediaItems but present in oldCoverMediaItems
    const folderItemsToDelete = oldFolderItemIds.filter(
      (folderItemId) => !newFolderItemIdsMap[folderItemId]
    );

    if (!folderItemsToDelete.length) return;

    // Calculate total storage to decrement before deletion (only if communityId provided)
    let totalStorageToDecrement = 0;
    if (communityId) {
      const folderItemsToDeleteDetails = await getFolderItemsWithFilter({
        _id: { $in: folderItemsToDelete },
        size: { $exists: true, $ne: null },
      });

      totalStorageToDecrement = folderItemsToDeleteDetails.reduce(
        (total, item) => {
          return total + (parseInt(item.size, 10) || 0);
        },
        0
      );
    }

    // update status to deleted
    const filter = {
      _id: { $in: folderItemsToDelete },
    };
    const update = {
      $set: {
        status: FOLDER_ITEM_STATUS.DELETED,
      },
    };
    const options = session ? { session } : {};

    const result = await updateManyFolderItems(filter, update, options);

    // Update storage usage after successful deletion
    if (communityId && totalStorageToDecrement > 0) {
      try {
        // If we're in a transaction, we should wait for it to commit
        // For now, we'll update storage outside the transaction
        if (!session) {
          await storageUsageService.decrementStorageUsage(
            communityId,
            totalStorageToDecrement
          );
          logger.info(
            'Storage usage decremented for cover media deletion',
            {
              communityId,
              folderItemsDeleted: folderItemsToDelete.length,
              storageDecreased: totalStorageToDecrement,
            }
          );
        } else {
          // Log for manual verification when in transaction
          logger.info(
            'Cover media deletion occurred in transaction - storage tracking needed',
            {
              communityId,
              folderItemsDeleted: folderItemsToDelete.length,
              storageToDecrement: totalStorageToDecrement,
            }
          );
        }
      } catch (storageError) {
        logger.error(
          'Failed to update storage usage for cover media deletion',
          {
            communityId,
            folderItemsDeleted: folderItemsToDelete.length,
            totalStorageToDecrement,
            error: storageError.message,
          }
        );
      }
    }

    return result;
  } catch (error) {
    logger.error(
      `[Fn:deleteRemovedVideoCoverMediaItems] Error deleting removed video cover media items`,
      {
        oldCoverMediaItems,
        newCoverMediaItems,
      },
      error,
      error.stack
    );
    throw new InternalError(
      'Error deleting removed video cover media items'
    );
  }
};

/**
 * Deletes all cover media items by marking them as deleted
 * @param {Array} coverMediaItems - Cover media items to delete
 * @param {Object} session - MongoDB session (optional)
 * @param {String} communityId - Community ID for storage tracking (optional)
 */
exports.deleteCoverMediaItems = async ({
  coverMediaItems,
  session,
  communityId, // Optional - storage tracking only happens if provided
}) => {
  if (!Array.isArray(coverMediaItems) || !coverMediaItems.length) {
    return;
  }

  const result = await this.deleteRemovedVideoCoverMediaItems({
    oldCoverMediaItems: coverMediaItems,
    newCoverMediaItems: [],
    session,
    communityId,
  });

  return result;
};

exports.duplicateCoverMediaItems = async ({ oldCoverMediaItems }) => {
  if (!hasVideoCoverMediaItems(oldCoverMediaItems)) {
    return oldCoverMediaItems;
  }

  const videoFolderItemIds = oldCoverMediaItems.reduce((acc, item) => {
    if (item.mediaType === COVER_MEDIA_TYPES.VIDEO) {
      acc.push(item.folderItemId);
    }
    return acc;
  }, []);

  const filter = {
    _id: { $in: videoFolderItemIds },
  };

  const oldVideoFolderItems = await communityFolderItemsModel
    .find(filter)
    .lean();

  if (!oldVideoFolderItems.length) {
    return oldCoverMediaItems;
  }

  const oldToNewFolderItemMap = {};

  const newVideoFolderItems = oldVideoFolderItems.map((item) => {
    const newFolderItem = { ...item };
    newFolderItem.hasValidFolderObjectId = false;

    delete newFolderItem._id;
    const newFolderItemObjectId = new ObjectId();
    newFolderItem._id = newFolderItemObjectId;
    oldToNewFolderItemMap[item._id.toString()] =
      newFolderItemObjectId.toString();

    return newFolderItem;
  });

  await communityFolderItemsModel.insertMany(newVideoFolderItems);

  return oldCoverMediaItems.map((item) => {
    if (item.mediaType === COVER_MEDIA_TYPES.VIDEO) {
      return {
        ...item,
        folderItemId: oldToNewFolderItemMap[item.folderItemId],
      };
    }
    return item;
  });
};
