const { ObjectId } = require('mongoose').Types;
const LearnerModel = require('../../../models/learners.model');
const LocalizationModel = require('../../../models/platform/localization.model');
const ZeroLinkModel = require('../../../models/zerolink/zerolink.model');
const { ZERO_LINK_STATUS } = require('../../zeroLink/constants');

function buildSearchStage({ communityId, searchString }) {
  const newMatchQuery = {
    index: 'default',
    compound: {
      must: [
        {
          equals: {
            path: 'communityObjectId',
            value: new ObjectId(communityId),
          },
        },
        {
          text: {
            query: `${ZERO_LINK_STATUS.ACTIVE} OR ${ZERO_LINK_STATUS.INACTIVE}`,
            path: 'status',
          },
        },
      ],
      filter: [],
    },
  };

  if (searchString !== '') {
    newMatchQuery.compound.filter.push({
      text: {
        query: searchString,
        path: 'searchTitle',
      },
    });
  }

  return newMatchQuery;
}

function buildSearchPipeline({
  communityId,
  searchString,
  projection = {},
  skip,
  limit,
}) {
  const searchStage = buildSearchStage({
    communityId,
    searchString,
  });
  const pipeline = [
    {
      $search: searchStage,
    },
    {
      $project: projection,
    },
    {
      $skip: skip,
    },
    {
      $limit: limit,
    },
  ];
  return pipeline;
}

function buildSearchMetaPipeline({ communityId, searchString }) {
  const searchStage = buildSearchStage({
    communityId,
    searchString,
  });
  const pipeline = [
    {
      $search: searchStage,
    },
    {
      $count: 'total',
    },
  ];
  return pipeline;
}

async function formatSearchString({ search, languagePreference }) {
  if (languagePreference === 'en') {
    return search;
  }
  const LOCALIZED_FLEXIBLE_PAYMENT_KEY = 'flexible-payment';
  const localised = await LocalizationModel.findOne({
    key: LOCALIZED_FLEXIBLE_PAYMENT_KEY,
  }).lean();

  const flexibleValue = localised?.[languagePreference];
  if (
    flexibleValue &&
    flexibleValue.toLocaleLowerCase().includes(search.toLocaleLowerCase())
  ) {
    return localised.en ?? search;
  }
  return search;
}

exports.retrieveFilterInfo = async ({
  learnerObjectId,
  communityId,
  search,
  pageNo,
  pageSize,
}) => {
  const learner = await LearnerModel.findById(learnerObjectId, {
    languagePreference: 1,
  }).lean();
  const searchString = await formatSearchString({
    search,
    languagePreference: learner.languagePreference ?? 'en',
  });
  const projection = {
    title: 1,
    pricingConfig: 1,
    amount: 1,
    currency: 1,
    status: 1,
  };
  const pipeline = buildSearchPipeline({
    communityId,
    searchString,
    projection,
    skip: (pageNo - 1) * pageSize,
    limit: pageSize,
  });
  const metaPipeline = buildSearchMetaPipeline({
    communityId,
    searchString,
  });
  const [zeroLinks, meta] = await Promise.all([
    ZeroLinkModel.aggregate(pipeline),
    ZeroLinkModel.aggregate(metaPipeline),
  ]);

  if (!meta?.length) {
    return {
      entities: [],
      metadata: {
        total: 0,
      },
    };
  }

  const totalCount = meta[0].total;
  return {
    entities: zeroLinks,
    metadata: {
      total: totalCount,
      limit: pageSize,
      page: pageNo,
      pages: Math.ceil(totalCount / pageSize),
    },
  };
};
