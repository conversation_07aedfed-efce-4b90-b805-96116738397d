/* eslint-disable no-unused-vars */
const { ObjectId } = require('mongoose').Types;
const RevenueTransactionModel = require('../../models/revenueTransaction.model');
const CommunityPurchaseTransactionsModel = require('../../communitiesAPI/models/communityPurchaseTransactions.model');
const AddonTransactionsModel = require('../../communitiesAPI/models/communityAddonTransactions.model');
const CommunityEventsModel = require('../../communitiesAPI/models/communityEvents.model');
const CommunityFoldersModel = require('../../communitiesAPI/models/communityFolders.model');
const CommunityDiscountsModel = require('../../communitiesAPI/models/communityDiscounts.model');
const ProgramModel = require('../../models/program/program.model');
const CommunityModel = require('../../communitiesAPI/models/community.model');
const RequestedRefundModel = require('../../models/requestedRefund.model');
const ReferralRewardModel = require('../../models/communityReferral/communityReferralReward.model');
const LearnerModel = require('../../models/learners.model');
const {
  PURCHASE_TYPE,
  DEFAULT_CURRENCY,
  WAIVED_STATUS,
  PAYMENT_PROVIDER,
  TRANSACTION_TYPE,
  PRODUCT_PURCHASE_TYPES,
  PAYABLE_PURCHASE_TYPES,
  FEE_CALCULATION_VERSION,
} = require('../../constants/common');
const MongoDbUtils = require('../../utils/mongodb.util');
const RegexUtils = require('../../utils/regex.util');
const {
  CENTS_PER_DOLLAR,
  MEMBERSHIP_PLAN_TYPE,
  MEMBERSHIP_PLAN_INTERVAL,
  MEMBERSHIP_PLAN_COMBINED_INTERVAL,
} = require('../../communitiesAPI/constants');
const {
  retrieveLearnerObjectIdViaSearch,
} = require('../membership/utils/membershipSearch.utils');
const ZeroLinkModel = require('../../models/zerolink/zerolink.model');

function projectRevenueTransactionRequiredData() {
  return {
    $project: {
      inboundData: 1,
      purchasedId: 1,
      paymentProvider: 1,
      amountInUsd: 1,
      discountCode: 1,
      discountTransactionData: 1,
      email: 1,
      rawFee: 1,
      finalFeeInUsd: 1,
      netAmountInUsd: 1,
      originalAmount: 1,
      originalDiscountAmount: 1,
      originalPaidAmount: 1,
      originalCurrency: 1,
      paymentBrand: 1,
      paymentMethod: 1,
      purchaseType: 1,
      totalFeeInUsd: 1,
      transactionReferenceId: 1,
      transactionCreatedAt: 1,
      transactionType: 1,
      amountBreakdownInUsd: 1,
      amountBreakdownInLocalCurrency: 1,
      amountBreakdownInOriginalCurrency: 1,
      revenueSharePercentage: 1,
      passOnTakeRate: 1,
      passOnPaymentGatewayFee: 1,
      refundedTransactionReferenceId: 1,
      metadata: 1,
      phoneNumber: '$learnerData.phoneNumber',
      fullName: {
        $concat: ['$learnerData.firstName', ' ', '$learnerData.lastName'],
      },
      affiliate: 1,
      communityObjectId: 1,
    },
  };
}

async function retrieveSubscriptionByIntervalType(search, communityCode) {
  let matchFilter;

  if (
    search.length <= MEMBERSHIP_PLAN_TYPE.ANNUALLY.length &&
    MEMBERSHIP_PLAN_TYPE.ANNUALLY.toUpperCase().startsWith(search)
  ) {
    matchFilter = {
      community_code: communityCode,
      $or: [
        { interval_count: 12, interval: MEMBERSHIP_PLAN_INTERVAL.MONTH },
        { interval_count: 1, interval: MEMBERSHIP_PLAN_INTERVAL.YEAR },
      ],
      'payment_details.complete_payment': 1,
    };
  } else if (
    search.length <= MEMBERSHIP_PLAN_TYPE.MONTHLY.length &&
    MEMBERSHIP_PLAN_TYPE.MONTHLY.toUpperCase().startsWith(search)
  ) {
    matchFilter = {
      community_code: communityCode,
      interval_count: 1,
      interval: MEMBERSHIP_PLAN_INTERVAL.MONTH,
      'payment_details.complete_payment': 1,
    };
  } else if (
    search.length <= MEMBERSHIP_PLAN_TYPE.QUARTERLY.length &&
    MEMBERSHIP_PLAN_TYPE.QUARTERLY.toUpperCase().startsWith(search)
  ) {
    matchFilter = {
      community_code: communityCode,
      interval_count: 3,
      interval: MEMBERSHIP_PLAN_INTERVAL.MONTH,
      'payment_details.complete_payment': 1,
    };
  } else if (
    search.length <= MEMBERSHIP_PLAN_TYPE.SEMIANNUALLY.length &&
    MEMBERSHIP_PLAN_TYPE.SEMIANNUALLY.toUpperCase().startsWith(search)
  ) {
    matchFilter = {
      community_code: communityCode,
      interval_count: 6,
      interval: MEMBERSHIP_PLAN_INTERVAL.MONTH,
      'payment_details.complete_payment': 1,
    };
  }

  if (matchFilter) {
    return CommunityPurchaseTransactionsModel.find(matchFilter, {
      _id: 0,
      id: '$_id',
    }).lean();
  }

  return [];
}

async function retrieveSubscriptionByCombinedInterval(
  subscriptionIntervals,
  communityCode
) {
  const intervals = [];

  subscriptionIntervals.forEach((interval) => {
    if (interval === MEMBERSHIP_PLAN_COMBINED_INTERVAL.ANNUALLY) {
      intervals.push(
        { interval_count: 12, interval: MEMBERSHIP_PLAN_INTERVAL.MONTH },
        { interval_count: 1, interval: MEMBERSHIP_PLAN_INTERVAL.YEAR }
      );
    } else if (
      interval === MEMBERSHIP_PLAN_COMBINED_INTERVAL.SEMIANNUALLY
    ) {
      intervals.push({
        interval_count: 6,
        interval: MEMBERSHIP_PLAN_INTERVAL.MONTH,
      });
    } else if (interval === MEMBERSHIP_PLAN_COMBINED_INTERVAL.QUARTERLY) {
      intervals.push({
        interval_count: 3,
        interval: MEMBERSHIP_PLAN_INTERVAL.MONTH,
      });
    } else if (interval === MEMBERSHIP_PLAN_COMBINED_INTERVAL.MONTHLY) {
      intervals.push({
        interval_count: 1,
        interval: MEMBERSHIP_PLAN_INTERVAL.MONTH,
      });
    }
  });

  if (intervals.length > 0) {
    const matchFilter = {
      community_code: communityCode,
      $or: intervals,
      'payment_details.complete_payment': 1,
    };

    return CommunityPurchaseTransactionsModel.find(matchFilter, {
      _id: 1,
    }).lean();
  }

  return [];
}

async function retrieveEventObjectIdsByTitle(search, communityObjectId) {
  return CommunityEventsModel.find(
    {
      communities: communityObjectId,
      title: { $regex: search, $options: 'i' },
    },
    { _id: 1 }
  ).lean();
}

async function retrieveProductObjectIdsByTitle(search, communityObjectId) {
  return CommunityFoldersModel.find(
    {
      communityObjectId,
      title: { $regex: search, $options: 'i' },
    },
    { _id: 1 }
  ).lean();
}

async function retrieveChallengeObjectIdsByTitle(
  search,
  communityObjectId
) {
  return ProgramModel.find(
    {
      communityObjectId,
      title: { $regex: search, $options: 'i' },
    },
    { _id: 1 }
  ).lean();
}

async function retrieveZeroLinkObjectIdsByTitle(
  search,
  communityObjectId
) {
  const links = await ZeroLinkModel.aggregate([
    {
      $search: {
        index: 'default',
        compound: {
          must: [
            {
              equals: {
                path: 'communityObjectId',
                value: communityObjectId,
              },
            },
          ],
          filter: [
            {
              text: {
                query: search,
                path: 'searchTitle',
              },
            },
          ],
        },
      },
    },
    { $project: { _id: 1 } },
  ]);

  return links.map((link) => link._id);
}

async function convertToCache(dataArr, key, valuePath = null) {
  const cache = dataArr.reduce((acc, data) => {
    return acc.set(
      data[key].toString(),
      valuePath == null ? data : data[valuePath]
    );
  }, new Map());

  return cache;
}

async function convertToCacheWithTwoKey(
  dataArr,
  key1,
  key2,
  valuePath = null
) {
  const cache = dataArr.reduce((acc, data) => {
    return acc.set(
      `${data[key1].toString()}-${data[key2].toString()}`,
      valuePath == null ? data : data[valuePath]
    );
  }, new Map());

  return cache;
}

exports.generateTransactionsMatchFilter = async (
  communityId,
  purchaseType,
  startDateInUtc,
  endDateInUtc,
  discountCodes,
  eventName,
  folderName,
  zeroLinkIds,
  eventsIds,
  productsIds,
  challengesIds,
  subscriptionIntervals,
  transactionType,
  search = '',
  payoutId = ''
) => {
  const matchFilter = {
    communityObjectId: new ObjectId(communityId),
  };

  if (transactionType) {
    matchFilter.transactionType = transactionType;
  }

  if (eventName) {
    const eventNameWithEscapedRegexSign =
      RegexUtils.escapeRegExp(eventName);

    const eventObjectIds = await retrieveEventObjectIdsByTitle(
      eventNameWithEscapedRegexSign,
      communityId
    );

    matchFilter.purchaseType = PURCHASE_TYPE.EVENT;
    matchFilter.purchasedId = {
      $in: eventObjectIds.map((entity) => entity._id),
    };
  } else if (folderName) {
    const folderNameWithEscapedRegexSign =
      RegexUtils.escapeRegExp(folderName);

    const productObjectIds = await retrieveProductObjectIdsByTitle(
      folderNameWithEscapedRegexSign,
      communityId
    );

    matchFilter.purchaseType = {
      $in: PRODUCT_PURCHASE_TYPES,
    };
    matchFilter.purchasedId = {
      $in: productObjectIds.map((entity) => entity._id),
    };
  }

  const { code } = await CommunityModel.findById(communityId, {
    code: 1,
  }).lean();

  const parsedSubscriptionIntervals =
    subscriptionIntervals?.split(',') ?? [];

  if (parsedSubscriptionIntervals.length > 0) {
    if (!('$or' in matchFilter)) {
      matchFilter.$or = [];
    }

    const subscriptionIds = await retrieveSubscriptionByCombinedInterval(
      parsedSubscriptionIntervals,
      code
    );

    matchFilter.$or.push({
      purchaseType: PURCHASE_TYPE.SUBSCRIPTION,
      purchasedId: {
        $in: subscriptionIds.map(({ _id }) => _id),
      },
    });
  }

  const parsedZeroLinkIds = zeroLinkIds?.split(',') ?? [];

  if (parsedZeroLinkIds.length > 0) {
    if (!('$or' in matchFilter)) {
      matchFilter.$or = [];
    }

    matchFilter.$or.push({
      purchaseType: PURCHASE_TYPE.ZERO_LINK,
      entityObjectId: {
        $in: parsedZeroLinkIds.map(
          (entityObjectId) => new ObjectId(entityObjectId)
        ),
      },
    });
  }

  const parsedEventsIds = eventsIds?.split(',') ?? [];

  if (parsedEventsIds.length > 0) {
    if (!('$or' in matchFilter)) {
      matchFilter.$or = [];
    }

    matchFilter.$or.push({
      purchaseType: PURCHASE_TYPE.EVENT,
      entityObjectId: {
        $in: parsedEventsIds.map(
          (entityObjectId) => new ObjectId(entityObjectId)
        ),
      },
    });
  }

  const parsedProductsIds = productsIds?.split(',') ?? [];

  if (parsedProductsIds.length > 0) {
    if (!('$or' in matchFilter)) {
      matchFilter.$or = [];
    }

    matchFilter.$or.push({
      purchaseType: {
        $in: PRODUCT_PURCHASE_TYPES,
      },
      entityObjectId: {
        $in: parsedProductsIds.map(
          (entityObjectId) => new ObjectId(entityObjectId)
        ),
      },
    });
  }

  const parsedChallengesIds = challengesIds?.split(',') ?? [];

  if (parsedChallengesIds.length > 0) {
    if (!('$or' in matchFilter)) {
      matchFilter.$or = [];
    }

    matchFilter.$or.push({
      purchaseType: PURCHASE_TYPE.CHALLENGE,
      entityObjectId: {
        $in: parsedChallengesIds.map(
          (entityObjectId) => new ObjectId(entityObjectId)
        ),
      },
    });
  }

  const parsedDiscountCodes = discountCodes?.split(',') ?? [];

  if (parsedDiscountCodes.length > 0) {
    matchFilter.discountCode = {
      $in: parsedDiscountCodes,
    };
    matchFilter.originalDiscountAmount = { $gt: 0 };
  }

  if (payoutId) {
    if (payoutId === 'currentBalance') {
      matchFilter.payoutObjectId = { $exists: false };
    } else {
      matchFilter.payoutObjectId = new ObjectId(payoutId);
    }
  }

  const searchWithEscapedRegexSign = RegexUtils.escapeRegExp(search);

  if (searchWithEscapedRegexSign !== '') {
    const matchSearchFilter = [];

    matchSearchFilter.push({
      email: {
        $regex: `^${searchWithEscapedRegexSign}`,
        $options: 'i',
      },
    });

    matchSearchFilter.push({
      discountCode: {
        $regex: `^${searchWithEscapedRegexSign}`,
        $options: 'i',
      },
    });

    const membershipWords = 'MEMBERSHIP';

    if (
      searchWithEscapedRegexSign.length <= membershipWords.length &&
      membershipWords.startsWith(searchWithEscapedRegexSign)
    ) {
      matchSearchFilter.push({
        purchaseType: PURCHASE_TYPE.SUBSCRIPTION,
      });
    }

    if (/^\d+(\.?)$/.test(search)) {
      const amount = +search;
      const minAmount = Math.floor(amount * CENTS_PER_DOLLAR);
      const maxAmount = Math.ceil((amount + 1) * CENTS_PER_DOLLAR);
      matchSearchFilter.push({
        amountInUsd: {
          $gte: minAmount,
          $lt: maxAmount,
        },
      });
    } else if (/^\d+(\.\d{2})?$/.test(search)) {
      const exactAmount = Math.round(+search * CENTS_PER_DOLLAR);
      matchSearchFilter.push({
        amountInUsd: exactAmount,
      });
    } else if (/^\d+(\.\d+)?$/.test(search)) {
      const amount = +search;
      const minAmount = Math.floor(amount * CENTS_PER_DOLLAR);
      const maxAmount = Math.ceil((amount + 0.1) * CENTS_PER_DOLLAR);
      matchSearchFilter.push({
        amountInUsd: {
          $gte: minAmount,
          $lt: maxAmount,
        },
      });
    }

    const [
      learnerObjectIds,
      purchaseTransactionIds,
      eventObjectIds,
      productObjectIds,
      challengeObjectIds,
      zeroLinkObjectIds,
    ] = await Promise.all([
      retrieveLearnerObjectIdViaSearch(
        searchWithEscapedRegexSign,
        communityId
      ),
      retrieveSubscriptionByIntervalType(searchWithEscapedRegexSign, code),
      retrieveEventObjectIdsByTitle(
        searchWithEscapedRegexSign,
        communityId
      ),
      retrieveProductObjectIdsByTitle(
        searchWithEscapedRegexSign,
        communityId
      ),
      retrieveChallengeObjectIdsByTitle(
        searchWithEscapedRegexSign,
        communityId
      ),
      retrieveZeroLinkObjectIdsByTitle(
        searchWithEscapedRegexSign,
        communityId
      ),
    ]);

    if (PRODUCT_PURCHASE_TYPES.includes(purchaseType)) {
      matchSearchFilter.push({
        purchaseType,
        entityObjectId: {
          $in: productObjectIds.map((entity) => entity._id),
        },
      });
    } else {
      switch (purchaseType) {
        case PURCHASE_TYPE.SUBSCRIPTION:
          matchSearchFilter.push({
            learnerObjectId: {
              $in: learnerObjectIds,
            },
          });
          matchSearchFilter.push({
            purchaseType: PURCHASE_TYPE.SUBSCRIPTION,
            purchasedId: {
              $in: purchaseTransactionIds.map(({ id }) => id),
            },
          });
          break;
        case PURCHASE_TYPE.EVENT:
          matchSearchFilter.push({
            purchaseType: PURCHASE_TYPE.EVENT,
            entityObjectId: {
              $in: eventObjectIds.map((entity) => entity._id),
            },
          });
          break;
        case PURCHASE_TYPE.CHALLENGE:
          matchSearchFilter.push({
            purchaseType: PURCHASE_TYPE.CHALLENGE,
            entityObjectId: {
              $in: challengeObjectIds.map((entity) => entity._id),
            },
          });
          break;
        case PURCHASE_TYPE.ZERO_LINK:
          matchSearchFilter.push({
            purchaseType: PURCHASE_TYPE.ZERO_LINK,
            entityObjectId: {
              $in: zeroLinkObjectIds,
            },
          });
          break;
        default:
          matchSearchFilter.push({
            learnerObjectId: {
              $in: learnerObjectIds,
            },
          });
          matchSearchFilter.push({
            purchaseType: PURCHASE_TYPE.SUBSCRIPTION,
            purchasedId: {
              $in: purchaseTransactionIds.map(({ id }) => id),
            },
          });
          matchSearchFilter.push({
            purchaseType: PURCHASE_TYPE.EVENT,
            entityObjectId: {
              $in: eventObjectIds.map((entity) => entity._id),
            },
          });
          matchSearchFilter.push({
            purchaseType: PURCHASE_TYPE.CHALLENGE,
            entityObjectId: {
              $in: challengeObjectIds.map((entity) => entity._id),
            },
          });
          matchSearchFilter.push({
            purchaseType: PURCHASE_TYPE.ZERO_LINK,
            entityObjectId: {
              $in: zeroLinkObjectIds,
            },
          });
          // Includes all product types
          PRODUCT_PURCHASE_TYPES.forEach((productPurchaseType) => {
            matchSearchFilter.push({
              purchaseType: productPurchaseType,
              entityObjectId: {
                $in: productObjectIds.map((entity) => entity._id),
              },
            });
          });

          break;
      }
    }

    if ('$or' in matchFilter) {
      matchFilter.$and = [
        { $or: matchFilter.$or },
        { $or: matchSearchFilter },
      ];
      delete matchFilter.$or;
    } else {
      matchFilter.$or = matchSearchFilter;
    }
  }

  if (
    PAYABLE_PURCHASE_TYPES.includes(purchaseType) ||
    purchaseType === PURCHASE_TYPE.REFERRAL_REWARD
  ) {
    matchFilter.purchaseType = purchaseType;
  }

  if (startDateInUtc && endDateInUtc) {
    matchFilter.transactionCreatedAt = {
      $gte: startDateInUtc,
      $lte: endDateInUtc,
    };
  }

  // Exclude new purchase type from transaction list for now
  if (!matchFilter.purchaseType) {
    matchFilter.purchaseType = {
      $nin: [
        PURCHASE_TYPE.PAYOUT,
        PURCHASE_TYPE.ADS_CAMPAIGN_RETURN,
        PURCHASE_TYPE.ADS_CAMPAIGN_TOPUP,
        PURCHASE_TYPE.ADS_CAMPAIGN_SPEND_REVERT,
        PURCHASE_TYPE.ADS_CAMPAIGN_SPEND,
        PURCHASE_TYPE.ADS_AVAILABLE_TO_OUTGOING,
        PURCHASE_TYPE.ADS_REWARD_ADJUSTMENT_ADD,
        PURCHASE_TYPE.ADS_REWARD_ADJUSTMENT_DEDUCT,
        PURCHASE_TYPE.CREDIT_BALANCE,
      ],
    };
  }
  return matchFilter;
};

exports.generateTransactionsPipelineQuery = async (
  communityId,
  purchaseType,
  startDateInUtc,
  endDateInUtc,
  pageNo,
  pageSize,
  sortOrder,
  sortBy,
  discountCodes,
  eventName,
  folderName,
  zeroLinkIds,
  eventsIds,
  productsIds,
  challengesIds,
  subscriptionIntervals,
  transactionType,
  search = '',
  payoutId = ''
) => {
  const matchFilter = await this.generateTransactionsMatchFilter(
    communityId,
    purchaseType,
    startDateInUtc,
    endDateInUtc,
    discountCodes,
    eventName,
    folderName,
    zeroLinkIds,
    eventsIds,
    productsIds,
    challengesIds,
    subscriptionIntervals,
    transactionType,
    search,
    payoutId
  );

  const pipelineQuery = [
    {
      $match: matchFilter,
    },
    {
      $sort: {
        [sortBy]: sortOrder,
      },
    },
    {
      $skip: (pageNo - 1) * pageSize,
    },
    {
      $limit: pageSize,
    },
    ...MongoDbUtils.lookupAndUnwind(
      'learners',
      'learnerObjectId',
      '_id',
      'learnerData'
    ),
    ...MongoDbUtils.lookupAndUnwind(
      'community_discount_transactions',
      'discountTransactionObjectId',
      '_id',
      'discountTransactionData'
    ),
    ...MongoDbUtils.conditionalLookupAndUnwind(
      'revenue_transactions',
      {
        transactionReferenceId: '$transactionReferenceId',
        transactionType: '$transactionType',
        paymentProvider: '$paymentProvider',
      },
      [
        {
          $match: {
            $expr: {
              $and: [
                { $eq: ['$paymentProvider', '$$paymentProvider'] },
                { $eq: ['$transactionType', TRANSACTION_TYPE.INBOUND] },
                {
                  $eq: [
                    '$refundedTransactionReferenceId',
                    '$$transactionReferenceId',
                  ],
                },
              ],
            },
          },
        },
      ],
      'inboundData'
    ),
    projectRevenueTransactionRequiredData(),
  ];

  return pipelineQuery;
};

function getRevenueShareFeeWaivedStatusAndAmount(
  revenueSharePercentage,
  revenueShareAmount,
  paymentProvider,
  passOnTakeRate,
  gstOnRevenue,
  currency = DEFAULT_CURRENCY,
  purchasedPriceDetails = {}
) {
  const waivedStatus =
    revenueSharePercentage === 0
      ? WAIVED_STATUS.WAIVED
      : WAIVED_STATUS.NONE;

  const isIndiaPaymentProvider = [
    PAYMENT_PROVIDER.STRIPE_INDIA,
    PAYMENT_PROVIDER.RAZORPAY,
  ].includes(paymentProvider);

  const totalAmount = revenueShareAmount + gstOnRevenue;

  return {
    passOnTakeRate,
    waivedStatus,
    totalAmount,
    currency,
    taxAmount: isIndiaPaymentProvider ? gstOnRevenue : undefined,
    gst: gstOnRevenue,
    passOnGst: !!purchasedPriceDetails?.checkoutAmount,
  };
}

function getPaymentGatewayFeeWaivedStatusAndAmount(
  fees,
  paymentProvider,
  gst,
  currency = DEFAULT_CURRENCY,
  passOnPaymentGatewayFee = false,
  hasInternationalFee = false
) {
  const isIndiaPaymentProvider = [
    PAYMENT_PROVIDER.STRIPE_INDIA,
    PAYMENT_PROVIDER.RAZORPAY,
  ].includes(paymentProvider);

  const totalFee = fees.reduce((acc, fee) => acc + fee, 0);

  const waivedStatus =
    totalFee === 0 ? WAIVED_STATUS.WAIVED : WAIVED_STATUS.NONE;

  return {
    passOnPaymentGatewayFee,
    waivedStatus,
    totalAmount: totalFee,
    taxAmount: isIndiaPaymentProvider ? gst : undefined,
    currency,
    gst,
    hasInternationalFee,
  };
}

function getWithholdingTaxFeeWaivedStatusAndAmount(
  whtFee,
  paymentProvider,
  currency = DEFAULT_CURRENCY
) {
  const isIndiaPaymentProvider = [
    PAYMENT_PROVIDER.STRIPE_INDIA,
    PAYMENT_PROVIDER.RAZORPAY,
  ].includes(paymentProvider);

  const waivedStatus =
    isIndiaPaymentProvider && whtFee === 0
      ? WAIVED_STATUS.WAIVED
      : WAIVED_STATUS.NONE;

  return isIndiaPaymentProvider
    ? {
        waivedStatus,
        amount: whtFee,
        currency,
      }
    : undefined;
}

function generateFeeBreakdown(
  finalAmountBreakdown,
  revenueSharePercentage,
  paymentProvider,
  passOnTakeRate = false,
  passOnPaymentGatewayFee = false,
  purchasedPriceDetails = {},
  inboundAmountBreakdown = {},
  affiliate = null
) {
  let amountBreakdown = finalAmountBreakdown;

  if (
    Object.keys(inboundAmountBreakdown).length > 0 &&
    passOnPaymentGatewayFee
  ) {
    amountBreakdown = inboundAmountBreakdown;
  }

  const isIndiaPaymentProvider = [
    PAYMENT_PROVIDER.STRIPE_INDIA,
    PAYMENT_PROVIDER.RAZORPAY,
  ].includes(paymentProvider);

  const paymentGatewayFees = [
    amountBreakdown.fee.gatewayFee,
    amountBreakdown.fee.gst,
  ];

  if (!isIndiaPaymentProvider) {
    paymentGatewayFees.push(amountBreakdown.fee.processingFee);
  }

  const hasInternationalFee = amountBreakdown.fee.internationalFee > 0;

  if (hasInternationalFee) {
    paymentGatewayFees.push(amountBreakdown.fee.internationalFee);
  }

  const feeBreakdown = {
    revenueShare: getRevenueShareFeeWaivedStatusAndAmount(
      revenueSharePercentage,
      amountBreakdown.revenueShareAmount,
      paymentProvider,
      passOnTakeRate,
      amountBreakdown.fee.gstOnRevenue,
      amountBreakdown.currency,
      purchasedPriceDetails
    ),
    paymentGatewayFee: getPaymentGatewayFeeWaivedStatusAndAmount(
      paymentGatewayFees,
      paymentProvider,
      amountBreakdown.fee.gst,
      amountBreakdown.currency,
      passOnPaymentGatewayFee,
      hasInternationalFee
    ),
    whtFee: getWithholdingTaxFeeWaivedStatusAndAmount(
      amountBreakdown.fee.whtFee,
      paymentProvider,
      amountBreakdown.currency
    ),
    refundProcessingFee: finalAmountBreakdown.fee.refundProcessingFee * -1,
    itemPrice: {
      amountAfterDiscount: amountBreakdown.discountedItemPrice,
      amount: amountBreakdown.itemPrice,
      currency: amountBreakdown.currency ?? DEFAULT_CURRENCY,
    },
  };

  if (amountBreakdown.itemPrice === amountBreakdown.discountedItemPrice) {
    delete feeBreakdown.itemPrice.amountAfterDiscount;
  }

  if (affiliate) {
    feeBreakdown.affiliate = {
      commissionPercentage: affiliate.commissionPercentage,
      amount: amountBreakdown.affiliateCommissionAmount,
      currency: amountBreakdown.currency ?? DEFAULT_CURRENCY,
    };
  }

  return feeBreakdown;
}

function generatePaid(
  originalPaidAmount,
  originalCurrency,
  finalAmountBreakdown,
  inboundAmountBreakdown = {}
) {
  let amountBreakdown = finalAmountBreakdown;
  if (Object.keys(inboundAmountBreakdown).length > 0) {
    amountBreakdown = inboundAmountBreakdown;
  }
  const currency = amountBreakdown.currency ?? DEFAULT_CURRENCY;

  const isSameCurrency = originalCurrency === currency;

  // eg. community base currency is USD, but transaction currency is MXN
  // we only have expected paid amount in USD, hence have to use original paid amount (MXN)
  const paidAmount = isSameCurrency
    ? amountBreakdown.expectedPaidAmount ?? amountBreakdown.paidAmount
    : originalPaidAmount;

  const paid = {
    amount: paidAmount,
    currency: originalCurrency,
  };

  if (!isSameCurrency) {
    paid.converted = {
      amount:
        amountBreakdown.expectedPaidAmount ?? amountBreakdown.paidAmount,
      currency,
    };
  }

  return paid;
}

function generatePayout(
  amountBreakdown,
  amountBreakdownInUsd,
  amountBreakdownInLocalCurrency,
  paymentProvider,
  payoutCurrency
) {
  // const isUsdPayoutCurrency =
  //   payoutCurrency.toUpperCase() === DEFAULT_CURRENCY;

  const currency = amountBreakdown.currency ?? DEFAULT_CURRENCY;

  const payout = {
    amount: amountBreakdown.netAmount,
    currency,
  };

  // No need to use converted amount here, since we will display amount in different currency
  // if (isUsdPayoutCurrency && currency !== DEFAULT_CURRENCY) {
  //   payout.converted = {
  //     amount: amountBreakdownInUsd.netAmount,
  //     currency: DEFAULT_CURRENCY,
  //   };
  // } else if (
  //   !isUsdPayoutCurrency &&
  //   currency !== amountBreakdownInLocalCurrency.currency
  // ) {
  //   payout.converted = {
  //     amount: amountBreakdownInLocalCurrency.netAmount,
  //     currency: amountBreakdownInLocalCurrency.currency,
  //   };
  // }

  return payout;
}

function getAmountBreakdown({
  isIndiaPaymentProvider,
  isLocalCurrency,
  amountBreakdownInLocalCurrency,
  amountBreakdownInUsd,
  amountBreakdownInOriginalCurrency,
}) {
  const selectedAmountBreakdown = isLocalCurrency
    ? amountBreakdownInLocalCurrency
    : amountBreakdownInUsd;

  let amountBreakdown = selectedAmountBreakdown;

  if (isIndiaPaymentProvider) {
    amountBreakdown = amountBreakdownInOriginalCurrency;
  }
  return {
    amountBreakdown,
    selectedAmountBreakdown,
  };
}

exports.retrieveMetadata = async (
  revenueTransactions,
  baseCurrency = DEFAULT_CURRENCY,
  payoutCurrency = DEFAULT_CURRENCY
) => {
  const zeroLinkAddOnIds = [];
  const purchaseIds = [];
  const eventAddonIds = [];
  const folderAddonIds = [];
  const challengeAddonIds = [];
  const inboundTransactionReferenceIds = [];
  const outboundTransactionReferenceIds = [];
  const paymentProviders = new Set();
  const communityDiscountObjectIds = [];
  const communityObjectIds = [];
  const learnerObjectIds = [];

  revenueTransactions.forEach((revenueTransaction) => {
    const {
      purchasedId,
      purchaseType,
      discountTransactionData,
      paymentProvider,
      transactionType,
      transactionReferenceId,
    } = revenueTransaction;

    paymentProviders.add(paymentProvider);

    if (transactionType === TRANSACTION_TYPE.INBOUND) {
      inboundTransactionReferenceIds.push(transactionReferenceId);
    } else {
      outboundTransactionReferenceIds.push(transactionReferenceId);
    }

    const communityDiscountObjectId =
      discountTransactionData?.communityDiscountObjectId;

    if (communityDiscountObjectId) {
      communityDiscountObjectIds.push(communityDiscountObjectId);
    }

    if (PRODUCT_PURCHASE_TYPES.includes(purchaseType)) {
      folderAddonIds.push(purchasedId);
    } else {
      switch (purchaseType) {
        case PURCHASE_TYPE.SUBSCRIPTION:
          purchaseIds.push(purchasedId);
          break;
        case PURCHASE_TYPE.EVENT:
          eventAddonIds.push(purchasedId);
          break;
        case PURCHASE_TYPE.CHALLENGE:
          challengeAddonIds.push(purchasedId);
          break;
        case PURCHASE_TYPE.ZERO_LINK:
          zeroLinkAddOnIds.push(purchasedId);
          break;
        case PURCHASE_TYPE.REFERRAL_REWARD:
          learnerObjectIds.push(
            revenueTransaction.metadata.refereeLearnerObjectId
          );
          communityObjectIds.push(
            revenueTransaction.metadata.refereeCommunityObjectId
          );
          break;
        default:
          break;
      }
    }
  });

  const [
    purchaseTransactions,
    zeroLinks,
    events,
    folders,
    challenges,
    discounts,
    requestedRefunds,
    requestedRefunded,
    learners,
    communities,
  ] = await Promise.all([
    CommunityPurchaseTransactionsModel.find(
      {
        _id: { $in: purchaseIds },
      },
      { interval: 1, interval_count: 1, country: 1, priceDetails: 1 }
    ).lean(),
    AddonTransactionsModel.aggregate([
      {
        $match: {
          _id: {
            $in: zeroLinkAddOnIds,
          },
        },
      },
      ...MongoDbUtils.lookupAndUnwind(
        'zero_link',
        'entityObjectId',
        '_id',
        'info'
      ),
      {
        $project: {
          'info.title': 1,
          'info.status': 1,
          'info.slug': 1,
          'info.pricingConfig': 1,
          'info.amount': 1,
          'info.currency': 1,
          'info.country': '$country',
          'info.priceDetails': '$priceDetails',
          'info.quantity': '$quantity',
        },
      },
    ]),
    AddonTransactionsModel.aggregate([
      {
        $match: {
          _id: {
            $in: eventAddonIds,
          },
        },
      },
      ...MongoDbUtils.lookupAndUnwind(
        'community_events',
        'entityObjectId',
        '_id',
        'info'
      ),
      {
        $project: {
          'info.title': 1,
          'info.status': 1,
          'info.type': 1,
          'info.slug': 1,
          'info.country': '$country',
          'info.priceDetails': '$priceDetails',
          'info.quantity': '$quantity',
        },
      },
    ]),
    AddonTransactionsModel.aggregate([
      {
        $match: {
          _id: {
            $in: folderAddonIds,
          },
        },
      },
      ...MongoDbUtils.lookupAndUnwind(
        'community_folders',
        'entityObjectId',
        '_id',
        'info'
      ),
      {
        $project: {
          'info.title': 1,
          'info.status': 1,
          'info.resourceSlug': 1,
          'info.country': '$country',
          'info.priceDetails': '$priceDetails',
          'info.quantity': '$quantity',
        },
      },
    ]),
    AddonTransactionsModel.aggregate([
      {
        $match: {
          _id: {
            $in: challengeAddonIds,
          },
        },
      },
      ...MongoDbUtils.lookupAndUnwind(
        'program',
        'entityObjectId',
        '_id',
        'info'
      ),
      {
        $project: {
          'info.title': 1,
          'info.status': 1,
          'info.slug': 1,
          'info.country': '$country',
          'info.priceDetails': '$priceDetails',
          'info.quantity': '$quantity',
        },
      },
    ]),
    CommunityDiscountsModel.find(
      {
        _id: { $in: communityDiscountObjectIds },
      },
      {
        type: 1,
        value: 1,
        duration: 1,
        isActive: 1,
        maxRedemptions: 1,
        intervalCount: 1,
      }
    ).lean(),
    RequestedRefundModel.find({
      paymentProvider: {
        $in: [...paymentProviders],
      },
      transactionReferenceId: {
        $in: inboundTransactionReferenceIds,
      },
    }).lean(),
    RequestedRefundModel.find({
      paymentProvider: {
        $in: [...paymentProviders],
      },
      refundedTransactionReferenceId: {
        $in: outboundTransactionReferenceIds,
      },
    }).lean(),
    LearnerModel.find(
      {
        _id: {
          $in: learnerObjectIds,
        },
      },
      { email: 1 }
    ).lean(),
    CommunityModel.find(
      {
        _id: {
          $in: communityObjectIds,
        },
      },
      { title: 1, link: 1 }
    ).lean(),
  ]);

  const [
    purchaseTransactionsCache,
    zeroLinksCache,
    eventsCache,
    foldersCache,
    challengesCache,
    discountsCache,
    requestedRefundCache,
    requestedRefundedCache,
    learnersCache,
    communitiesCache,
  ] = await Promise.all([
    convertToCache(purchaseTransactions, '_id'),
    convertToCache(zeroLinks, '_id', 'info'),
    convertToCache(events, '_id', 'info'),
    convertToCache(folders, '_id', 'info'),
    convertToCache(challenges, '_id', 'info'),
    convertToCache(discounts, '_id'),
    convertToCacheWithTwoKey(
      requestedRefunds,
      'paymentProvider',
      'transactionReferenceId'
    ),
    convertToCacheWithTwoKey(
      requestedRefunded,
      'paymentProvider',
      'refundedTransactionReferenceId'
    ),
    convertToCache(learners, '_id'),
    convertToCache(communities, '_id'),
  ]);

  const isLocalCurrency = baseCurrency !== DEFAULT_CURRENCY;

  const revenueTransactionsWithMetadata = revenueTransactions.map(
    (revenueTransaction) => {
      const {
        purchasedId,
        purchaseType,
        originalPaidAmount,
        originalCurrency,
        discountTransactionData,
        originalDiscountAmount,
        amountBreakdownInUsd,
        amountBreakdownInLocalCurrency,
        amountBreakdownInOriginalCurrency,
        revenueSharePercentage,
        paymentProvider,
        transactionReferenceId,
        transactionType,
        passOnTakeRate = false,
        passOnPaymentGatewayFee = false,
        inboundData = {},
        affiliate,
        transactionCreatedAt,
      } = revenueTransaction;

      const {
        amountBreakdownInUsd: inboundAmountBreakdownInUsd,
        amountBreakdownInLocalCurrency:
          inboundAmountBreakdownInLocalCurrency,
        amountBreakdownInOriginalCurrency:
          inboundAmountBreakdownInOriginalCurrency,
      } = inboundData;

      if (inboundAmountBreakdownInUsd) {
        inboundAmountBreakdownInUsd.currency = DEFAULT_CURRENCY;
      }
      amountBreakdownInUsd.currency = DEFAULT_CURRENCY;
      const isIndiaPaymentProvider = [
        PAYMENT_PROVIDER.STRIPE_INDIA,
        PAYMENT_PROVIDER.RAZORPAY,
      ].includes(paymentProvider);

      const { amountBreakdown, selectedAmountBreakdown } =
        getAmountBreakdown({
          isIndiaPaymentProvider,
          isLocalCurrency,
          amountBreakdownInLocalCurrency,
          amountBreakdownInUsd,
          amountBreakdownInOriginalCurrency,
        });
      const {
        amountBreakdown: inboundAmountBreakdown,
        selectedAmountBreakdown: selectedInboundAmountBreakdown,
      } = getAmountBreakdown({
        isIndiaPaymentProvider,
        isLocalCurrency,
        amountBreakdownInLocalCurrency:
          inboundAmountBreakdownInLocalCurrency,
        amountBreakdownInUsd: inboundAmountBreakdownInUsd,
        amountBreakdownInOriginalCurrency:
          inboundAmountBreakdownInOriginalCurrency,
      });

      const revenueTransactionWithMetadata = {
        ...revenueTransaction,
      };
      delete revenueTransactionWithMetadata.inboundData;

      const communityDiscountObjectId =
        discountTransactionData?.communityDiscountObjectId;

      if (communityDiscountObjectId && originalDiscountAmount > 0) {
        revenueTransactionWithMetadata.discountData = discountsCache.get(
          communityDiscountObjectId.toString()
        );
      }
      let transaction;
      if (PRODUCT_PURCHASE_TYPES.includes(purchaseType)) {
        transaction = foldersCache.get(purchasedId.toString());
      } else {
        switch (purchaseType) {
          case PURCHASE_TYPE.SUBSCRIPTION:
            transaction = purchaseTransactionsCache.get(
              purchasedId.toString()
            );
            break;
          case PURCHASE_TYPE.EVENT:
            transaction = eventsCache.get(purchasedId.toString());
            break;
          case PURCHASE_TYPE.CHALLENGE:
            transaction = challengesCache.get(purchasedId.toString());
            break;
          case PURCHASE_TYPE.ZERO_LINK:
            transaction = zeroLinksCache.get(purchasedId.toString());
            break;
          default:
            break;
        }
      }

      const purchasedPriceDetails = transaction?.priceDetails ?? {};
      const transactionDetails = {
        quantity: transaction?.quantity ?? 1,
        country: transaction?.country,
        paid: generatePaid(
          originalPaidAmount,
          originalCurrency,
          isIndiaPaymentProvider
            ? selectedAmountBreakdown
            : amountBreakdown,

          isIndiaPaymentProvider
            ? selectedInboundAmountBreakdown
            : inboundAmountBreakdown
        ),
        ...generateFeeBreakdown(
          amountBreakdown,
          revenueSharePercentage,
          paymentProvider,
          passOnTakeRate,
          passOnPaymentGatewayFee,
          purchasedPriceDetails,
          inboundAmountBreakdown,
          affiliate
        ),
        payout: generatePayout(
          amountBreakdown,
          amountBreakdownInUsd,
          amountBreakdownInLocalCurrency,
          paymentProvider,
          payoutCurrency
        ),
      };

      if (transactionType === TRANSACTION_TYPE.INBOUND) {
        const requestedRefundKey = `${paymentProvider}-${transactionReferenceId}`;

        if (requestedRefundCache.has(requestedRefundKey)) {
          const requestedRefundInfo =
            requestedRefundCache.get(requestedRefundKey);
          const refundDetails = {
            status: requestedRefundInfo.status,
            requestedAt: requestedRefundInfo.createdAt,
            reason: requestedRefundInfo.refundReason,
            requestedBy: requestedRefundInfo.emailRequested,
          };

          transactionDetails.refundDetails = refundDetails;
        }
      } else {
        const requestedRefundedKey = `${paymentProvider}-${transactionReferenceId}`;
        const refundedDetails = {};
        if (requestedRefundedCache.has(requestedRefundedKey)) {
          const requestedRefundedInfo = requestedRefundedCache.get(
            requestedRefundedKey
          );

          refundedDetails.requestedAt = requestedRefundedInfo.createdAt;
          refundedDetails.reason = requestedRefundedInfo.refundReason;
          refundedDetails.requestedBy =
            requestedRefundedInfo.emailRequested;
        }

        const currency = amountBreakdown.currency ?? DEFAULT_CURRENCY;
        refundedDetails.currency = currency;
        refundedDetails.refundProcessingFee =
          amountBreakdown.fee?.refundProcessingFee ?? 0;
        refundedDetails.amount = amountBreakdown.netAmount;

        transactionDetails.refundedDetails = refundedDetails;
      }

      revenueTransactionWithMetadata.transactionDetails =
        transactionDetails;

      if (PRODUCT_PURCHASE_TYPES.includes(purchaseType)) {
        revenueTransactionWithMetadata.folderData = foldersCache.get(
          purchasedId.toString()
        );
      } else {
        switch (purchaseType) {
          case PURCHASE_TYPE.SUBSCRIPTION:
            revenueTransactionWithMetadata.subscriptionData =
              purchaseTransactionsCache.get(purchasedId.toString());

            if (
              (revenueTransactionWithMetadata.subscriptionData.interval ===
                MEMBERSHIP_PLAN_INTERVAL.YEAR &&
                revenueTransactionWithMetadata.subscriptionData
                  .interval_count === 1) ||
              (revenueTransactionWithMetadata.subscriptionData.interval ===
                MEMBERSHIP_PLAN_INTERVAL.MONTH &&
                revenueTransactionWithMetadata.subscriptionData
                  .interval_count === 12)
            ) {
              revenueTransactionWithMetadata.subscriptionData.membershipType =
                MEMBERSHIP_PLAN_TYPE.ANNUALLY;
            } else if (
              revenueTransactionWithMetadata.subscriptionData.interval ===
                MEMBERSHIP_PLAN_INTERVAL.MONTH &&
              revenueTransactionWithMetadata.subscriptionData
                .interval_count === 1
            ) {
              revenueTransactionWithMetadata.subscriptionData.membershipType =
                MEMBERSHIP_PLAN_TYPE.MONTHLY;
            } else if (
              revenueTransactionWithMetadata.subscriptionData.interval ===
                MEMBERSHIP_PLAN_INTERVAL.MONTH &&
              revenueTransactionWithMetadata.subscriptionData
                .interval_count === 3
            ) {
              revenueTransactionWithMetadata.subscriptionData.membershipType =
                MEMBERSHIP_PLAN_TYPE.QUARTERLY;
            } else if (
              revenueTransactionWithMetadata.subscriptionData.interval ===
                MEMBERSHIP_PLAN_INTERVAL.MONTH &&
              revenueTransactionWithMetadata.subscriptionData
                .interval_count === 6
            ) {
              revenueTransactionWithMetadata.subscriptionData.membershipType =
                MEMBERSHIP_PLAN_TYPE.SEMIANNUALLY;
            }
            break;
          case PURCHASE_TYPE.EVENT:
            revenueTransactionWithMetadata.eventData = eventsCache.get(
              purchasedId.toString()
            );
            break;
          case PURCHASE_TYPE.CHALLENGE:
            revenueTransactionWithMetadata.challengeData =
              challengesCache.get(purchasedId.toString());
            break;
          case PURCHASE_TYPE.ZERO_LINK:
            revenueTransactionWithMetadata.zeroLinkData =
              zeroLinksCache.get(purchasedId.toString());
            break;
          case PURCHASE_TYPE.REFERRAL_BONUS:
            revenueTransactionWithMetadata.bonusData = {
              title: 'Referral bonus',
            };
            revenueTransactionWithMetadata.email = '';
            break;
          case PURCHASE_TYPE.CAMPAIGN_REWARD:
            revenueTransactionWithMetadata.email = '';
            break;
          case PURCHASE_TYPE.REFERRAL_REWARD:
            revenueTransactionWithMetadata.email = '';
            revenueTransactionWithMetadata.refereeData = {
              title: revenueTransactionWithMetadata.metadata.title,
              email:
                learnersCache.get(
                  revenueTransactionWithMetadata.metadata.refereeLearnerObjectId.toString()
                )?.email ?? '',
              communityTitle:
                communitiesCache.get(
                  revenueTransactionWithMetadata.metadata.refereeCommunityObjectId.toString()
                )?.title ?? '',
              communityLink:
                communitiesCache.get(
                  revenueTransactionWithMetadata.metadata.refereeCommunityObjectId.toString()
                )?.link ?? '',
              originalPrice:
                revenueTransactionWithMetadata.metadata
                  .refereePlanOriginalPrice,
              originalCurrency:
                revenueTransactionWithMetadata.metadata
                  .refereePlanOriginalCurrency,
              interval: revenueTransactionWithMetadata.metadata.interval,
              intervalCount:
                revenueTransactionWithMetadata.metadata.intervalCount,
              nextBillingDate:
                revenueTransactionWithMetadata.metadata.nextBillingDate,
              subscriptionStartDate:
                revenueTransactionWithMetadata.metadata
                  .subscriptionStartDate,
              planType: revenueTransactionWithMetadata.metadata.planType,
            };

            if (
              revenueTransactionWithMetadata.transactionType ===
              TRANSACTION_TYPE.OUTBOUND
            ) {
              revenueTransactionWithMetadata.refereeData.previousInterval =
                revenueTransactionWithMetadata.metadata.previousInterval;

              revenueTransactionWithMetadata.refereeData.previousIntervalCount =
                revenueTransactionWithMetadata.metadata.previousIntervalCount;

              revenueTransactionWithMetadata.refereeData.previousPlanType =
                revenueTransactionWithMetadata.metadata.previousPlanType;

              revenueTransactionWithMetadata.refereeData.previousPlanOriginalPrice =
                revenueTransactionWithMetadata.metadata.previousPlanOriginalPrice;

              revenueTransactionWithMetadata.refereeData.previousPlanOriginalCurrency =
                revenueTransactionWithMetadata.metadata.previousPlanOriginalCurrency;

              const hasChangeReferral =
                revenueTransactionWithMetadata.metadata.newReferrerCommunityObjectId?.toString() !==
                revenueTransactionWithMetadata.communityObjectId.toString();
              revenueTransactionWithMetadata.refereeData.hasChangeReferral =
                hasChangeReferral;
              revenueTransactionWithMetadata.refereeData.changeDate =
                transactionCreatedAt;
            }
            break;
          default:
            break;
        }
      }

      return revenueTransactionWithMetadata;
    }
  );

  return revenueTransactionsWithMetadata;
};

exports.retrieveTransactionsCount = async (filter) => {
  return RevenueTransactionModel.countDocuments(filter);
};
