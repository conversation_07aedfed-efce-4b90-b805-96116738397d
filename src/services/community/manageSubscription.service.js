const CommunityModel = require('../../communitiesAPI/models/community.model');
const CommunitySubscriptionsModel = require('../../communitiesAPI/models/communitySubscriptions.model');
const {
  findOneOrCreateSubscription,
} = require('../../communitiesAPI/services/web/communitySubscriptions.service');
const {
  communityEnrolmentStatuses,
  userStatus,
} = require('../../constants/common');
const LearnerModel = require('../../models/learners.model');
const UserModel = require('../../models/users.model');
const EmailUtils = require('../../utils/email.util');
const PasswordUtils = require('../../utils/password.util');
const { ParamError } = require('../../utils/error.util');
const {
  randomUserImageGenerator,
} = require('../addRandomUserImage.service');
const CommunityRoleModel = require('../../communitiesAPI/models/communityRole.model');
const { aclRoles } = require('../../communitiesAPI/constants');

const retrieveOrCreateLearner = async (params = {}) => {
  const { email, isActive, learnerObjectId } = params;

  const learnerQuery = { email, isActive };
  if (learnerObjectId) {
    learnerQuery._id = learnerObjectId;
  }
  const existingLearner = await LearnerModel.findOne(learnerQuery).lean();

  if (existingLearner) {
    return existingLearner;
  }

  const doc = {};
  const keys = Object.keys({ ...params });
  for (let i = 0; i < keys.length; i++) {
    doc[keys[i]] = params[keys[i]];
  }

  const update = {
    $setOnInsert: {
      ...params,
      profileImage:
        params.profileImage || (await randomUserImageGenerator()),
      languagePreference: params.languagePreference || 'en',
      createdAt: params.lastModifiedTimeStamp,
    },
  };

  const options = {
    upsert: true,
    new: true,
    setDefaultsOnInsert: true,
    timestamps: false, // disable auto-updating timestamps, so that we can assign lastModifiedTimeStamp
  };

  try {
    const learner = await LearnerModel.findOneAndUpdate(
      { email, isActive },
      update,
      options
    ).lean();
    return learner;
  } catch (error) {
    throw new Error(`Cannot create Learner ${email}: ${error.message}`);
  }
};

const retrieveOrSetupAccount = async (params = {}) => {
  const { email } = params;

  const existingUser = await UserModel.findOne({
    email,
    isActive: true,
  }).lean();

  const currentTime = new Date();
  const learner = await retrieveOrCreateLearner({
    learnerObjectId: existingUser?.learner,
    email,
    firstName: '',
    lastName: '',
    isActive: true,
    lastModifiedTimeStamp: currentTime,
  });

  if (existingUser) {
    return {
      user: existingUser,
      learner,
    };
  }

  const defaultPassword = PasswordUtils.generateRandomPassword();

  const status = userStatus.PENDING;
  const setPassword = defaultPassword;

  const defaultAccessMethods = {
    type: 'password',
    id: learner.learnerId,
  };

  const accessMethods = existingUser?.accessMethods ?? [];

  if (
    !accessMethods.some(({ type }) => type === defaultAccessMethods.type)
  ) {
    accessMethods.push(defaultAccessMethods);
  }
  const user = {
    password: setPassword,
    status,
    user_id: learner.learnerId,
    learner: learner._id,
    learner_role: true,
    accessMethods,
  };

  const updatedUser = await UserModel.findOneAndUpdate(
    { email, isActive: true },
    {
      $set: user,
      $setOnInsert: { createdAt: currentTime },
    },
    { new: true, upsert: true }
  ).lean();

  return {
    user: updatedUser,
    learner,
  };
};

const retrieveActiveSubscription = async ({ email, communityCode }) => {
  const subscription = await CommunitySubscriptionsModel.findOne(
    {
      email,
      communityCode,
      $or: [
        {
          status: communityEnrolmentStatuses.CURRENT,
        },
        {
          status: communityEnrolmentStatuses.PENDING,
        },
        {
          status: communityEnrolmentStatuses.CANCELLED,
          cancelledAt: { $gte: new Date() },
        },
      ],
    },
    { email: 1, status: 1 }
  ).lean();

  return subscription;
};

exports.addFreeSubscriber = async ({
  email,
  communityId,
  toSendEmail,
}) => {
  const community = await CommunityModel.findById(communityId).lean();
  if (!community) {
    throw new ParamError('Community not found');
  }

  const existingSubscription = await retrieveActiveSubscription({
    email,
    communityCode: community.code,
  });
  if (existingSubscription) {
    throw new ParamError(
      `${email} is already a subscriber of the community ${community.code}`
    );
  }

  const validEmail = await EmailUtils.verifyEmailDomain(email);
  if (!validEmail) {
    throw new ParamError('Invalid email');
  }

  const { learner } = await retrieveOrSetupAccount({ email });

  const subscriptionDoc = {
    communityCode: community.code,
    learnerObjectId: learner._id,
    learnerId: learner.learnerId,
    email: learner.email,
  };

  const subscription = await findOneOrCreateSubscription(
    subscriptionDoc,
    community,
    { sendNotification: toSendEmail, bypassPendingApproval: true }
  );
  return subscription;
};

exports.addCommunityManager = async ({ email, communityId }) => {
  const community = await CommunityModel.findById(communityId).lean();
  if (!community) {
    throw new ParamError('Community not found');
  }

  const existingSubscription = await retrieveActiveSubscription({
    email,
    communityCode: community.code,
  });
  if (!existingSubscription) {
    throw new ParamError(
      `${email} needs to be a subscriber of the community ${community.code} before becoming a manager!`
    );
  }
  const { user } = await retrieveOrSetupAccount({ email });
  const communityRoleQuery = {
    userObjectId: user._id,
    communityObjectId: community._id,
    role: aclRoles.MANAGER,
  };
  let createManager = await CommunityRoleModel.findOne(
    communityRoleQuery
  ).lean();
  if (createManager) {
    throw new ParamError(
      `${email} is already a manager of ${community.code}`
    );
  }
  communityRoleQuery.email = email;
  communityRoleQuery.communityCode = community.code;
  communityRoleQuery.role = [
    aclRoles.ADMIN,
    aclRoles.MANAGER,
    aclRoles.MEMBER,
  ];

  createManager = await CommunityRoleModel.create(communityRoleQuery);
  return createManager;
};
