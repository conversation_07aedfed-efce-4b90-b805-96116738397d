const {
  retrievePaymentFeeStructure,
} = require('../config/paymentFeeStructureConfig.service');
const CommunityModel = require('../../communitiesAPI/models/community.model');
const FeeService = require('../../communitiesAPI/services/common/fee.service');
const { ParamError } = require('../../utils/error.util');

exports.getPaymentFeeRate = async ({ communityId, purchaseType = '' }) => {
  const community = await CommunityModel.findById(communityId).lean();

  if (!community) {
    throw new ParamError('Community not found');
  }

  // Get effective config from payoutFeeConfigs
  const effectivePayoutFeeConfig =
    FeeService.retrieveEffectivePayoutFeeConfig(
      community.payoutFeeConfigs,
      new Date(),
      purchaseType
    );

  if (effectivePayoutFeeConfig) {
    const { gatewayFeeInPercentage, processingFee, minGatewayFee } =
      effectivePayoutFeeConfig?.paymentProviderFee?.all?.ALL ?? {};
    const { purchaseTypeConfigs, paymentProviderFee, ...newObject } =
      effectivePayoutFeeConfig;
    return {
      gatewayFeeInPercentage,
      processingFee,
      minGatewayFee,
      ...newObject,
      baseCurrency: community.baseCurrency,
    };
  }

  const effectiveBasePayoutFeeConfig =
    FeeService.retrieveEffectivePayoutFeeConfig(
      community.basePayoutFeeConfigs,
      new Date(),
      purchaseType
    );

  if (effectiveBasePayoutFeeConfig) {
    const { purchaseTypeConfigs, ...newObject } =
      effectiveBasePayoutFeeConfig;
    return { ...newObject, baseCurrency: community.baseCurrency };
  }

  const paymentFeeStructure = await retrievePaymentFeeStructure({
    baseCurrency: community.baseCurrency,
    purchaseType,
    planType: community.config?.planType,
  });
  const {
    purchaseTypeConfigs,
    customLogicFee,
    fee,
    minGatewayFeeByBaseCurrency: minGatewayFee,
    processingFeeByBaseCurrency: processingFee,
    ...newObject
  } = paymentFeeStructure;
  return {
    ...fee,
    ...newObject,
    minGatewayFee,
    processingFee,
    baseCurrency: community.baseCurrency,
  };
};
