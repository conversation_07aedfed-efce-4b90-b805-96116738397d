const jwt = require('jsonwebtoken');
const httpContext = require('express-http-context');
const {
  retrievePaymentFeeStructure,
} = require('../config/paymentFeeStructureConfig.service');
const FeeService = require('../../communitiesAPI/services/common/fee.service');
const CommunityModel = require('../../communitiesAPI/models/community.model');
const { ENTITY_TYPE } = require('../plan/constants');
const {
  reallocateCustomPayoutFeeConfigs,
} = require('../plan/community.service');
const logger = require('../logger.service');
const {
  CURRENCY_WITH_NON_DECIMAL_POINTS,
} = require('../../constants/common');
const { LEARN_BACKEND_URL, COPS_JWT_SECRET } = require('../../config');
const schedulerHandler = require('../../handlers/scheduler.handler');
const {
  SCHEDULER_GROUP_NAME,
  TASK_TYPE,
} = require('../../constants/eventBridge');
const { GENERAL_PURPOSE_TASK_QUEUE_ARN } = require('../../config');

exports.updateFeeConfig = async (
  community,
  updateCustom,
  targetEffectiveStartTime = null
) => {
  const {
    _id: communityObjectId,
    code: communityCode,
    basePayoutFeeConfigs = [],
  } = community;

  const setQuery = {};

  if (updateCustom) {
    setQuery.payoutFeeConfigs = await reallocateCustomPayoutFeeConfigs({
      community,
      entityType: community.config?.planType,
    });
  } else {
    const paymentFeeStructure = await retrievePaymentFeeStructure({
      baseCurrency: community.baseCurrency,
      planType: community.config?.planType,
      targetEffectiveStartTime,
    });

    const { feeConfig } =
      await FeeService.retrieveBasePayoutFeeConfigAndOtherSettings({
        community,
        paymentFeeStructure,
        targetEffectiveStartTime,
      });

    if (!basePayoutFeeConfigs || basePayoutFeeConfigs.length === 0) {
      setQuery.basePayoutFeeConfigs = [feeConfig];
    } else {
      const updatedBasePayoutFeeConfigs = [...basePayoutFeeConfigs];

      const lastBasePayoutFeeConfig = updatedBasePayoutFeeConfigs.pop();
      lastBasePayoutFeeConfig.effectiveTimeEnd =
        feeConfig.effectiveTimeStart;

      updatedBasePayoutFeeConfigs.push(lastBasePayoutFeeConfig, feeConfig);

      setQuery.basePayoutFeeConfigs = updatedBasePayoutFeeConfigs;
    }
  }

  const updatedCommunity = await CommunityModel.findByIdAndUpdate(
    communityObjectId,
    setQuery,
    { new: true }
  ).lean();

  if (!updatedCommunity) {
    throw new Error(`${communityCode} not updated for fee configs`);
  }
};

const getFeeByPlanType = async ({ community, planType, customFee }) => {
  const paymentFeeStructure = await retrievePaymentFeeStructure({
    baseCurrency: community.baseCurrency,
    planType,
  });

  const { feeConfig } =
    await FeeService.retrieveBasePayoutFeeConfigAndOtherSettings({
      community,
      paymentFeeStructure,
    });

  const { purchaseTypeConfigs } = feeConfig;

  const { zeroLinkFee } = customFee;

  // The custom zerolink fee will always be 3 ranges
  // If this community only applicable for 2 ranges of zerolink,
  // system will only record first 2 ranges of given fee
  if (
    zeroLinkFee.length < purchaseTypeConfigs.ZERO_LINK.volumeTiers.length
  ) {
    throw new Error(
      `Zerolink fee range is incorrect, this community should have ${purchaseTypeConfigs.ZERO_LINK.volumeTiers.length} ranges for this planType ${planType}`
    );
  }

  // Update volumeTiers with fee details from zeroLinkFeeMap
  const updatedZeroLinkFee = purchaseTypeConfigs.ZERO_LINK.volumeTiers.map(
    (tier, index) => {
      const fee = zeroLinkFee[index];
      if (!fee) {
        throw new Error(
          `No fee config found for volume tier index ${index}`
        );
      }

      return {
        volumeMin: tier.volumeMin,
        volumeMax: tier.volumeMax,
        revenueShareInPercentage: fee.revenueShareInPercentage,
        gatewayFeeInPercentage: fee.gatewayFeeInPercentage,
        processingFee: fee.processingFee,
        minGatewayFee: fee.minGatewayFee,
      };
    }
  );

  const updatedPurchaseTypeConfig = {
    ZERO_LINK: {
      gatewayFeeInPercentage: zeroLinkFee[0].gatewayFeeInPercentage,
      processingFee: zeroLinkFee[0].processingFee,
      minGatewayFee: zeroLinkFee[0].minGatewayFee,
      revenueShareInPercentage: zeroLinkFee[0].revenueShareInPercentage,
      volumeTiers: updatedZeroLinkFee,
    },
  };

  // eslint-disable-next-line no-param-reassign
  delete customFee.zeroLinkFee;

  return {
    ...customFee,
    purchaseTypeConfigs: updatedPurchaseTypeConfig,
  };
};

const validateFeeRates = ({
  baseCurrency,
  freePlanFee,
  proPlanFee,
  platinumPlanFee,
}) => {
  if (!CURRENCY_WITH_NON_DECIMAL_POINTS.includes(baseCurrency)) {
    return;
  }

  const getAllFees = (obj) => {
    const fees = [];

    const collect = (feeObj) => {
      if (feeObj.processingFee != null) {
        fees.push({ label: 'processingFee', value: feeObj.processingFee });
      }
      if (feeObj.minGatewayFee != null) {
        fees.push({ label: 'minGatewayFee', value: feeObj.minGatewayFee });
      }

      if (Array.isArray(feeObj.zeroLinkFee)) {
        feeObj.zeroLinkFee.forEach((zf, index) => {
          if (zf.processingFee != null) {
            fees.push({
              label: `zeroLinkFee[${index}].processingFee`,
              value: zf.processingFee,
            });
          }
          if (zf.minGatewayFee != null) {
            fees.push({
              label: `zeroLinkFee[${index}].minGatewayFee`,
              value: zf.minGatewayFee,
            });
          }
        });
      }
    };

    collect(obj.freePlanFee);
    collect(obj.proPlanFee);
    collect(obj.platinumPlanFee);

    return fees;
  };

  const fees = getAllFees({ freePlanFee, proPlanFee, platinumPlanFee });

  // For the currency without decimal points, we shouldnt allow the incorrect minGatewayFee/processing fee
  const invalidFees = fees.filter((fee) => fee.value % 100 > 0);
  if (invalidFees && invalidFees.length > 0) {
    throw new Error(
      `Invalid minGatewayFee/processing fee, this community's base currency is [${baseCurrency}], which is not allowed decimal points`
    );
  }
};

const scheduleCustomFeeUpdateJob = async ({
  communityObjectId,
  startDate,
  operator,
}) => {
  logger.info(`scheduleCustomFeeUpdateJob`);

  // This is your user payload structure
  const userPayload = {
    user: {
      email: operator,
    },
  };

  // Generate token
  const token = jwt.sign(userPayload, COPS_JWT_SECRET);

  // Change to send to queue
  const updateDetails = {
    tasks: [
      {
        type: TASK_TYPE.CUSTOM_API,
        taskDetails: {
          path: `${LEARN_BACKEND_URL}api/v1/communities/${communityObjectId}/fee/cops`,
          method: 'patch',
          headers: {
            Authorization: `Bearer ${token}`,
          },
          bodyParam: { updatePayoutFeeConfigByPlanType: true },
        },
      },
    ],
  };

  const requestId = httpContext.get('reqId');
  const message = {
    ...updateDetails,
    requestor: 'Learning Portal Backend',
    requestId,
  };

  const scheduleName = `update_custom_fee_${communityObjectId}_${
    startDate.toISOString().split('T')[0]
  }`;
  await schedulerHandler.updateOrCreateOneTimeScheduleForSqs({
    sqsQueueArn: GENERAL_PURPOSE_TASK_QUEUE_ARN,
    eventTimeString: startDate.toISOString(),
    scheduleName, // Unique id for this schedule task
    groupName: SCHEDULER_GROUP_NAME.GENERAL_PURPOSE_QUEUE,
    message,
  });
  logger.info(
    `Schedule job for custom fee update update is created: ${scheduleName}`
  );
};

exports.updatePayoutFeeConfig = async ({
  communityObjectId,
  startDate,
  endDate,
  freePlanFee,
  proPlanFee,
  platinumPlanFee,
  operator,
  updatePayoutFeeConfigByPlanType,
}) => {
  if (endDate <= startDate) {
    throw new Error(`Invalid start/end date`);
  }

  const community = await CommunityModel.findById(
    communityObjectId
  ).lean();

  if (updatePayoutFeeConfigByPlanType) {
    // The start date will always be current date for this case
    await this.updateFeeConfig(community, true);
    return;
  }

  const now = new Date();
  if (startDate < now) {
    // eslint-disable-next-line no-param-reassign
    startDate = now;
  }

  validateFeeRates({
    baseCurrency: community.baseCurrency,
    freePlanFee,
    proPlanFee,
    platinumPlanFee,
  });

  const customProPlanFee = await getFeeByPlanType({
    community,
    planType: ENTITY_TYPE.PRO,
    customFee: proPlanFee,
  });
  const customPlatinumPlanFee = await getFeeByPlanType({
    community,
    planType: ENTITY_TYPE.PLATINUM,
    customFee: platinumPlanFee,
  });

  const newCustomPayoutFeeConfig = {
    effectiveTimeStart: startDate,
    effectiveTimeEnd: endDate ?? new Date('3000-01-01T00:00:00.000Z'),
    default: {
      ...freePlanFee,
    },
    [`${ENTITY_TYPE.PRO.toLowerCase()}`]: {
      ...customProPlanFee,
    },
    [`${ENTITY_TYPE.PLATINUM.toLowerCase()}`]: {
      ...customPlatinumPlanFee,
    },
    operator,
  };

  const existedCustomFeeConfigs = community.config.customFeeConfigs ?? [];

  const lastFeeConfig = existedCustomFeeConfigs.pop();
  if (lastFeeConfig) {
    // For the effectiveTimeEnd that is in very far away future (like 2300-01-01)
    // We will update the effectiveTimeEnd of last config to be payoutFeeConfig.effectiveTimeStart
    if (
      lastFeeConfig.effectiveTimeEnd >
      newCustomPayoutFeeConfig.effectiveTimeStart
    ) {
      lastFeeConfig.effectiveTimeEnd =
        newCustomPayoutFeeConfig.effectiveTimeStart;
    }
    existedCustomFeeConfigs.push(lastFeeConfig, newCustomPayoutFeeConfig);
  } else {
    existedCustomFeeConfigs.push(newCustomPayoutFeeConfig);
  }

  const updateQuery = {
    'config.customFeeConfigs': existedCustomFeeConfigs,
  };
  logger.info(`Update query: ${JSON.stringify(updateQuery)}`);

  const updatedCommunity = await CommunityModel.findOneAndUpdate(
    { _id: community._id },
    updateQuery,
    { new: true }
  ).lean();

  if (startDate > now) {
    // Setup schedule job to update payout fee config on the start date by planType
    await scheduleCustomFeeUpdateJob({
      communityObjectId: community._id,
      startDate,
      operator,
    });
  } else {
    await this.updateFeeConfig(updatedCommunity, true, startDate);
  }
};
