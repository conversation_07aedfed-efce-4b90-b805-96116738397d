/* eslint-disable no-await-in-loop */
const {
  CENTS_PER_DOLLAR,
  communityFolderTypesMap,
} = require('../../communitiesAPI/constants');
const {
  PAYMENT_PROVIDER,
  DEFAULT_CURRENCY,
  PRICE_TYPE,
  PURCHASE_TYPE,
} = require('../../constants/common');
const EarningAnalyticsModel = require('../../models/earningAnalytics.model');
const PredicatedEarningAnalyticsModel = require('../../models/predictedEarningAnalytics.model');
const MembershipModel = require('../../models/membership/membership.model');
const UpsellOfferModel = require('../../models/upsellOffers.model');
const CommunityModel = require('../../communitiesAPI/models/community.model');
const CountryCurrencyModel = require('../../models/countryInfoMapping.model');
const CommunityEventsModel = require('../../communitiesAPI/models/communityEvents.model');
const CommunityFoldersModel = require('../../communitiesAPI/models/communityFolders.model');
const CommunityProductModel = require('../../models/product/communityProduct.model');
const Program = require('../../models/program/program.model');
const CommunityDiscountsModel = require('../../communitiesAPI/models/communityDiscounts.model');
const RevenueTransactionModel = require('../../models/revenueTransaction.model');
const ReferralRewardModel = require('../../models/communityReferral/communityReferralReward.model');
const logger = require('../logger.service');
const PaymentBackendRpc = require('../../rpc/paymentBackend');
const CommunityService = require('../../communitiesAPI/services/common/community.service');
const CommunityMembershipPriceService = require('../../communitiesAPI/services/common/communityMemershipPrice.service');
const { ParamError } = require('../../utils/error.util');
const PaymentProviderUtil = require('../../utils/paymentProvider.util');
const PrimaryMongooseConnection = require('../../rpc/primaryMongooseConnection');
const FeeService = require('../../communitiesAPI/services/common/fee.service');
const {
  retrievePaymentFeeStructure,
} = require('../config/paymentFeeStructureConfig.service');
const currencyUtils = require('../../utils/currency.util');
const CommunityMagicReachEmailModel = require('../../models/magicReach/communityMagicReachEmail.model');
const { PRODUCT_TYPE } = require('../product/constants');

function retrieveCommunityPaymentMethods(paymentProvider) {
  switch (paymentProvider) {
    case PAYMENT_PROVIDER.STRIPE:
    case PAYMENT_PROVIDER.STRIPE_INDIA:
      return [
        {
          value: paymentProvider,
          label: 'stripe',
          icon: 'https://d2oi1rqwb0pj00.cloudfront.net/na-website/Payment/svg/bank.svg',
        },
      ];
    default:
      return [];
  }
}

async function migrateDiscount({
  community,
  stripeProductId,
  stripeUsProductId,
  discountPlatform,
  session,
}) {
  const discountsData = await CommunityDiscountsModel.find(
    {
      promoCodeStripeId: {
        $exists: true,
      },
      communityCode: community.code,
    },
    {
      type: 1,
      value: 1,
      isActive: 1,
      communityCode: 1,
      maxRedemptions: 1,
      promoCodeStripeId: 1,
      discountCouponStripeId: 1,
    }
  ).lean();

  logger.info(
    `migrateDiscount: no. of discounts to ${discountPlatform}: ${discountsData.length}`
  );
  const paymentBackendRpc = new PaymentBackendRpc();
  await paymentBackendRpc.init();

  // eslint-disable-next-line no-restricted-syntax
  for await (const discount of discountsData) {
    const {
      _id: discountObjectId,
      type,
      value,
      isActive,
      discountCouponStripeId,
      maxRedemptions,
    } = discount;

    const updateData = {};
    logger.info(`discountCouponStripeId: ${discountCouponStripeId}`);
    if (discountPlatform === PAYMENT_PROVIDER.STRIPE) {
      const stripeDiscount = await paymentBackendRpc.createStripeDiscount({
        type,
        value,
        maxRedemptions,
        stripeProductId,
        isActive,
        paymentProvider: PAYMENT_PROVIDER.STRIPE,
      });
      const stripeUsDiscount =
        await paymentBackendRpc.createStripeDiscount({
          type,
          value,
          maxRedemptions,
          isActive,
          stripeProductId: stripeUsProductId,
          paymentProvider: PAYMENT_PROVIDER.STRIPE_US,
        });

      updateData.discountCouponStripeId = stripeDiscount.stripeCouponId;
      updateData.communityStripeProductId = stripeProductId;
      updateData.promoCodeStripeId = stripeDiscount.stripePromotionCodeId;
      updateData.promoCodeStripe = stripeDiscount.stripePromotionCode;
      updateData.stripePromoCodeDetails = {
        promotionCode: stripeUsDiscount.stripePromotionCode,
        couponId: stripeUsDiscount.stripeCouponId,
        promotionCodeId: stripeUsDiscount.stripePromotionCodeId,
        stripeProductId: stripeUsProductId,
      };
    } else {
      const stripeDiscount = await paymentBackendRpc.createStripeDiscount({
        type,
        value,
        maxRedemptions,
        stripeProductId,
        paymentProvider: discountPlatform,
        isActive,
      });
      updateData.discountCouponStripeId = stripeDiscount.stripeCouponId;
      updateData.communityStripeProductId = stripeProductId;
      updateData.promoCodeStripeId = stripeDiscount.stripePromotionCodeId;
      updateData.promoCodeStripe = stripeDiscount.stripePromotionCode;
    }

    await CommunityDiscountsModel.findByIdAndUpdate(
      discountObjectId,
      updateData,
      { new: true, session }
    );
  }
}

async function migrateToStripe(
  community,
  communityPricesForUpdate,
  newCurrency,
  session
) {
  const { title, stripeProductId } = community;

  const { productId: newStripeProductId, communityPrices } =
    await CommunityMembershipPriceService.createProductWithPrices(
      title,
      communityPricesForUpdate,
      newCurrency,
      PAYMENT_PROVIDER.STRIPE,
      true
    );

  const { productId: newStripeUsProductId } =
    await CommunityMembershipPriceService.createProductWithPrices(
      title,
      communityPricesForUpdate,
      newCurrency,
      PAYMENT_PROVIDER.STRIPE_US,
      true
    );

  logger.info(`migrateToStripe: ${newStripeProductId}`);

  const updateData = {
    prices: communityPrices,
  };

  if (stripeProductId) {
    updateData.stripeProductId = newStripeProductId;
    updateData.stripeUsProductId = newStripeUsProductId;
  } else {
    throw new Error('Invalid field');
  }

  await migrateDiscount({
    community,
    stripeProductId: newStripeProductId,
    stripeUsProductId: newStripeUsProductId,
    discountPlatform: PAYMENT_PROVIDER.STRIPE,
    session,
  });

  return { updateData };
}

async function migrateToStripeIndia(
  community,
  communityPricesForUpdate,
  newCurrency,
  session
) {
  const { title, stripeProductId } = community;

  const { productId: newStripeIndiaProductId, communityPrices } =
    await CommunityMembershipPriceService.createProductWithPrices(
      title,
      communityPricesForUpdate,
      newCurrency,
      PAYMENT_PROVIDER.STRIPE_INDIA,
      true
    );

  logger.info(`migrateToStripeIndia: ${newStripeIndiaProductId}`);

  const updateData = { prices: communityPrices };

  if (stripeProductId) {
    updateData.stripeProductId = newStripeIndiaProductId;
  } else {
    throw new Error('Invalid field');
  }

  await migrateDiscount({
    community,
    stripeProductId: newStripeIndiaProductId,
    discountPlatform: PAYMENT_PROVIDER.STRIPE_INDIA,
    session,
  });

  return { updateData };
}

async function archivePrices(communityPrices) {
  const paymentBackendRpc = new PaymentBackendRpc();
  await paymentBackendRpc.init();
  for (const [key, value] of Object.entries(communityPrices)) {
    const communityPricesIdToArchive = value.map((price) => price.id);

    logger.info(
      `archivePrices: prices id: ${JSON.stringify(
        communityPricesIdToArchive
      )}`
    );

    await paymentBackendRpc.archiveStripeProductPricing({
      stripePriceIds: communityPricesIdToArchive,
      paymentProvider: key,
    });
  }
}

async function updateSubscriptionPrices(
  community,
  newCurrency,
  conversionRate,
  session
) {
  const {
    stripeProductId,
    payment_methods: paymentMethods,
    baseCurrency,
    prices,
  } = community;

  if (!stripeProductId) {
    return { updateData: {} };
  }

  const paymentProvider =
    await PaymentProviderUtil.retrievePaymentProvider(paymentMethods);

  const communityPricesForUpdate = prices.map((price) => ({
    currency: newCurrency,
    amount: currencyUtils.normalizeAndRoundAmountByCurrency(
      price.cmSetPrice * conversionRate,
      newCurrency
    ),
    interval: price.interval,
    intervalCount: price.intervalCount,
  }));

  logger.info(
    `updatePrice: communityPricesForUpdate: ${JSON.stringify(
      communityPricesForUpdate
    )}`
  );

  // Migration needed for INR to other currencies due to different payment platform
  if (baseCurrency === 'INR') {
    return migrateToStripe(
      community,
      communityPricesForUpdate,
      newCurrency,
      session
    );
  }

  // Migration needed for other currencies to INR due to different payment platform
  if (newCurrency === 'INR') {
    return migrateToStripeIndia(
      community,
      communityPricesForUpdate,
      newCurrency,
      session
    );
  }

  // Update prices when it is the same payment platform
  const updateData = {};

  if (
    [PAYMENT_PROVIDER.STRIPE, PAYMENT_PROVIDER.STRIPE_US].includes(
      paymentProvider
    )
  ) {
    updateData.prices = await CommunityMembershipPriceService.updatePrices(
      communityPricesForUpdate,
      newCurrency,
      [],
      true
    );
  } else {
    // No need to handle stripe india case, cus stripe india only for INR pricing
    throw new Error(`Invalid payment provider: ${paymentProvider}`);
  }

  // updateData: Contains the updated prices for the community.
  // archive: Contains the current community prices for archival purposes if there are no errors.
  return {
    updateData,
  };
}

async function updateMembershipLtv(
  community,
  newCurrency,
  conversionRate,
  session,
  batchSize = 10000
) {
  const { _id: communityObjectId, baseCurrency } = community;

  let page = 0;
  let hasMoreRecords = true;

  const filterMember = (member) => {
    const { lifeTimeValue = {} } = member;
    const { amount = 0, currency = DEFAULT_CURRENCY } = lifeTimeValue;

    return currency === baseCurrency && amount != null;
  };

  const generateBulkOperations = (member) => {
    const { _id: membershipObjectId, lifeTimeValue = {} } = member;
    const { amount = 0 } = lifeTimeValue;

    const newAmount = currencyUtils.normalizeAndRoundAmountByCurrency(
      amount * conversionRate,
      newCurrency
    );

    return {
      updateOne: {
        filter: { _id: membershipObjectId },
        update: {
          lifeTimeValue: {
            amount: newAmount,
            currency: newCurrency,
          },
        },
      },
    };
  };

  while (hasMoreRecords) {
    const members = await MembershipModel.find({ communityObjectId })
      .sort({ createdAt: 1 })
      .select('lifeTimeValue _id')
      .skip(page * batchSize)
      .limit(batchSize)
      .lean();

    hasMoreRecords = members.length === batchSize;
    page++;

    // Prepare bulk update operations
    const bulkOperations = members
      .filter(filterMember)
      .map(generateBulkOperations);

    // Perform bulk update
    if (bulkOperations.length > 0) {
      await MembershipModel.bulkWrite(bulkOperations);
    }
  }
}

function retrieveUpdatePriceQuery({
  amount,
  currency,
  baseCurrency,
  conversionRate,
  newCurrency,
  pricingConfig,
  entityType,
}) {
  let newAmount = amount ?? 0;

  if (currency !== baseCurrency) {
    return newAmount;
  }

  const updateQuery = {};

  if (pricingConfig?.priceType === PRICE_TYPE.FLEXIBLE) {
    updateQuery['pricingConfig.suggestedAmount'] =
      currencyUtils.normalizeAndRoundAmountByCurrency(
        pricingConfig.suggestedAmount * conversionRate,
        newCurrency
      );

    updateQuery['pricingConfig.minAmount'] =
      currencyUtils.normalizeAndRoundAmountByCurrency(
        pricingConfig.minAmount * conversionRate,
        newCurrency
      );

    if (entityType === PURCHASE_TYPE.CHALLENGE) {
      updateQuery['pricingConfig.currency'] = newCurrency;
    } else {
      updateQuery.currency = newCurrency;
    }
  } else {
    newAmount = currencyUtils.normalizeAndRoundAmountByCurrency(
      newAmount * conversionRate,
      newCurrency
    );

    if (entityType === PURCHASE_TYPE.CHALLENGE) {
      updateQuery['pricingConfig.amount'] = newAmount;
      updateQuery['pricingConfig.currency'] = newCurrency;
    } else {
      updateQuery.amount = newAmount;
      updateQuery.currency = newCurrency;
    }
  }

  return updateQuery;
}

async function updateCommunityProduct({ bulkOperations, session }) {
  const productUpdateOperations = bulkOperations.map(
    ({ updateOne, productType }) => {
      const entityObjectId = updateOne.filter._id;
      const update = updateOne.update;
      update.amount = update.amount || update['pricingConfig.amount'];
      update.currency =
        update.currency || update['pricingConfig.currency'];
      return {
        updateOne: {
          filter: {
            productType,
            entityObjectId,
          },
          update,
        },
      };
    }
  );
  await CommunityProductModel.bulkWrite(productUpdateOperations, {
    session,
  });
}

async function updateEventsPrice(
  community,
  newCurrency,
  conversionRate,
  earningsConversionRate,
  session
) {
  const { _id: communityObjectId, baseCurrency } = community;

  const communityEvents = await CommunityEventsModel.find({
    'communities.0': communityObjectId,
    currency: baseCurrency,
  }).lean();

  const bulkOperations = communityEvents.map((event) => {
    const {
      _id: eventObjectId,
      amount,
      currency = DEFAULT_CURRENCY,
      pricingConfig,
      earningAnalytics,
      affiliateEarningAnalytics,
    } = event;

    const updatePriceQuery = retrieveUpdatePriceQuery({
      amount,
      currency,
      baseCurrency,
      conversionRate,
      newCurrency,
      pricingConfig,
      entityType: PURCHASE_TYPE.EVENT,
    });

    const updateQuery = {
      ...updatePriceQuery,
    };

    if (earningAnalytics?.revenueInUsd) {
      updateQuery['earningAnalytics.revenueInLocalCurrency'] =
        currencyUtils.normalizeAndRoundAmountByCurrency(
          earningAnalytics.revenueInUsd * earningsConversionRate,
          newCurrency
        );
    }

    if (affiliateEarningAnalytics?.revenueInUsd) {
      updateQuery['affiliateEarningAnalytics.revenueInLocalCurrency'] =
        currencyUtils.normalizeAndRoundAmountByCurrency(
          affiliateEarningAnalytics.revenueInUsd * earningsConversionRate,
          newCurrency
        );
    }

    return {
      updateOne: {
        filter: { _id: eventObjectId },
        update: updateQuery,
      },
      productType: PRODUCT_TYPE.EVENT,
    };
  });

  if (bulkOperations.length === 0) {
    return;
  }

  await updateCommunityProduct({
    bulkOperations,
    session,
  });

  await CommunityEventsModel.bulkWrite(bulkOperations, { session });
}

async function updateFoldersPrice(
  community,
  newCurrency,
  conversionRate,
  earningsConversionRate,
  session
) {
  const { _id: communityObjectId, baseCurrency } = community;

  const communityFolders = await CommunityFoldersModel.find({
    communityObjectId,
  }).lean();

  const bulkOperations = communityFolders.map((folder) => {
    const {
      _id: folderObjectId,
      amount,
      currency = DEFAULT_CURRENCY,
      earningAnalytics,
      pricingConfig,
      affiliateEarningAnalytics,
      type,
    } = folder;

    const updatePriceQuery = retrieveUpdatePriceQuery({
      amount,
      currency,
      baseCurrency,
      conversionRate,
      newCurrency,
      pricingConfig,
      entityType: PURCHASE_TYPE.FOLDER,
    });

    const updateQuery = {
      ...updatePriceQuery,
    };

    if (earningAnalytics?.revenueInUsd) {
      updateQuery['earningAnalytics.revenueInLocalCurrency'] =
        currencyUtils.normalizeAndRoundAmountByCurrency(
          earningAnalytics.revenueInUsd * earningsConversionRate,
          newCurrency
        );
    }

    if (affiliateEarningAnalytics?.revenueInUsd) {
      updateQuery['affiliateEarningAnalytics.revenueInLocalCurrency'] =
        currencyUtils.normalizeAndRoundAmountByCurrency(
          affiliateEarningAnalytics.revenueInUsd * earningsConversionRate,
          newCurrency
        );
    }

    let productType;
    switch (type) {
      case communityFolderTypesMap.COURSE:
        productType = PRODUCT_TYPE.COURSE;
        break;
      case communityFolderTypesMap.SESSION:
        productType = PRODUCT_TYPE.SESSION;
        break;
      default:
        productType = PRODUCT_TYPE.DIGITAL_FILES;
    }

    return {
      updateOne: {
        filter: { _id: folderObjectId },
        update: updateQuery,
      },
      productType,
    };
  });

  if (bulkOperations.length === 0) {
    return;
  }

  await updateCommunityProduct({
    bulkOperations,
    session,
  });

  await CommunityFoldersModel.bulkWrite(bulkOperations, { session });
}

async function updateProgramPrice(
  community,
  newCurrency,
  conversionRate,
  earningsConversionRate,
  session
) {
  const { _id: communityObjectId, baseCurrency } = community;

  const programs = await Program.find({
    communityObjectId,
  }).lean();

  const bulkOperations = programs.map((program) => {
    const {
      _id: programObjectId,
      pricingConfig,
      earningAnalytics,
      affiliateEarningAnalytics,
    } = program;

    const updatePriceQuery = retrieveUpdatePriceQuery({
      amount: pricingConfig?.amount,
      currency: pricingConfig?.currency,
      baseCurrency,
      conversionRate,
      newCurrency,
      pricingConfig,
      entityType: PURCHASE_TYPE.CHALLENGE,
    });

    const updateQuery = {
      ...updatePriceQuery,
    };

    if (earningAnalytics?.revenueInUsd) {
      updateQuery['earningAnalytics.revenueInLocalCurrency'] =
        currencyUtils.normalizeAndRoundAmountByCurrency(
          earningAnalytics.revenueInUsd * earningsConversionRate,
          newCurrency
        );
    }

    if (affiliateEarningAnalytics?.revenueInUsd) {
      updateQuery['affiliateEarningAnalytics.revenueInLocalCurrency'] =
        currencyUtils.normalizeAndRoundAmountByCurrency(
          affiliateEarningAnalytics.revenueInUsd * earningsConversionRate,
          newCurrency
        );
    }

    return {
      updateOne: {
        filter: { _id: programObjectId },
        update: updateQuery,
      },
      productType: PRODUCT_TYPE.CHALLENGE,
    };
  });

  if (bulkOperations.length === 0) {
    return;
  }

  await updateCommunityProduct({
    bulkOperations,
    session,
  });

  await Program.bulkWrite(bulkOperations, { session });
}

async function updateReferralRewardEarnings(
  community,
  newCurrency,
  earningsConversionRate,
  session
) {
  const { _id: communityObjectId } = community;

  const referralRewards = await ReferralRewardModel.find(
    {
      referrerCommunityObjectId: communityObjectId,
    },
    { earningAnalytics: 1 }
  ).lean();

  const bulkOperations = referralRewards.map((referralReward) => {
    const { _id: referralRewardObjectId, earningAnalytics } =
      referralReward;

    const updateQuery = {
      currency: newCurrency,
    };

    if (earningAnalytics?.revenueInUsd) {
      updateQuery['earningAnalytics.revenueInLocalCurrency'] =
        currencyUtils.normalizeAndRoundAmountByCurrency(
          earningAnalytics.revenueInUsd * earningsConversionRate,
          newCurrency
        );
    }

    return {
      updateOne: {
        filter: { _id: referralRewardObjectId },
        update: updateQuery,
      },
    };
  });

  if (bulkOperations.length === 0) {
    return;
  }

  await ReferralRewardModel.bulkWrite(bulkOperations, { session });
}

async function updateUpsell(
  community,
  newCurrency,
  earningsConversionRate,
  session
) {
  const { _id: communityObjectId } = community;

  const upsells = await UpsellOfferModel.find({
    upsellCommunityObjectId: communityObjectId,
  }).lean();

  const bulkOperations = upsells.map((upsell) => {
    const {
      _id: upsellObjectId,
      earningAnalytics,
      affiliateEarningAnalytics,
    } = upsell;

    const updateQuery = {};

    if (earningAnalytics?.revenueInUsd) {
      updateQuery['earningAnalytics.revenueInLocalCurrency'] =
        currencyUtils.normalizeAndRoundAmountByCurrency(
          earningAnalytics.revenueInUsd * earningsConversionRate,
          newCurrency
        );
    }

    if (affiliateEarningAnalytics?.revenueInUsd) {
      updateQuery['affiliateEarningAnalytics.revenueInLocalCurrency'] =
        currencyUtils.normalizeAndRoundAmountByCurrency(
          affiliateEarningAnalytics.revenueInUsd * earningsConversionRate,
          newCurrency
        );
    }

    return {
      updateOne: {
        filter: { _id: upsellObjectId },
        update: updateQuery,
      },
    };
  });

  if (bulkOperations.length === 0) {
    return;
  }

  await UpsellOfferModel.bulkWrite(bulkOperations, { session });
}

async function updateMREarnings(
  community,
  newCurrency,
  earningsConversionRate,
  session
) {
  const { _id: communityObjectId } = community;
  const filter = {
    communityId: communityObjectId,
    'analyticsData.Email.earningsInfo.revenueInUsd': {
      $exists: true,
    },
  };
  const limit = 1000;
  let lastId;

  // eslint-disable-next-line no-constant-condition
  while (true) {
    if (lastId) {
      filter._id = { $gt: lastId };
    }

    const magicReachEmails = await CommunityMagicReachEmailModel.find(
      filter
    )
      .sort({ _id: 1 })
      .limit(limit)
      .select('_id analyticsData')
      .lean();

    if (!magicReachEmails.length) {
      break;
    }

    const pipeline = [];
    magicReachEmails.forEach((magicReach) => {
      const earningsInfo = magicReach.analyticsData?.Email?.earningsInfo;

      if (!earningsInfo?.revenueInUsd) {
        return;
      }

      const revenueInLocalCurrency =
        currencyUtils.normalizeAndRoundAmountByCurrency(
          earningsInfo.revenueInUsd * earningsConversionRate,
          newCurrency
        );

      const updateQuery = {
        'analyticsData.Email.earningsInfo.localCurrency': newCurrency,
        'analyticsData.Email.earningsInfo.revenueInLocalCurrency':
          revenueInLocalCurrency,
      };

      pipeline.push({
        updateOne: {
          filter: {
            _id: magicReach._id,
          },
          update: {
            $set: updateQuery,
          },
        },
      });
    });

    await CommunityMagicReachEmailModel.bulkWrite(pipeline, { session });

    lastId = magicReachEmails[magicReachEmails.length - 1]._id;
  }
}

function multiplyAndCeil(parent, field, rate) {
  return {
    $ceil: {
      $multiply: [`$${parent}.${field}`, rate],
    },
  };
}

// eslint-disable-next-line no-unused-vars
async function updateTransactions(
  community,
  newCurrency,
  conversionRate,
  session
) {
  const { _id: communityObjectId } = community;

  const aggregationPipeline = [
    {
      $set: {
        amountBreakdownInLocalCurrency: {
          $mergeObjects: [
            '$amountBreakdownInUsd',
            {
              exchangeRate: conversionRate,
              expectedPaidAmount: multiplyAndCeil(
                'amountBreakdownInUsd',
                'expectedPaidAmount',
                conversionRate
              ),
              itemPrice: multiplyAndCeil(
                'amountBreakdownInUsd',
                'itemPrice',
                conversionRate
              ),
              discountedItemPrice: multiplyAndCeil(
                'amountBreakdownInUsd',
                'discountedItemPrice',
                conversionRate
              ),
              originalAmount: multiplyAndCeil(
                'amountBreakdownInUsd',
                'originalAmount',
                conversionRate
              ),
              discountAmount: multiplyAndCeil(
                'amountBreakdownInUsd',
                'discountAmount',
                conversionRate
              ),
              paidAmount: multiplyAndCeil(
                'amountBreakdownInUsd',
                'paidAmount',
                conversionRate
              ),
              revenueShareAmount: multiplyAndCeil(
                'amountBreakdownInUsd',
                'revenueShareAmount',
                conversionRate
              ),
              totalFee: multiplyAndCeil(
                'amountBreakdownInUsd',
                'totalFee',
                conversionRate
              ),
              netAmount: multiplyAndCeil(
                'amountBreakdownInUsd',
                'netAmount',
                conversionRate
              ),
              referralShareAmount: multiplyAndCeil(
                'amountBreakdownInUsd',
                'referralShareAmount',
                conversionRate
              ),
              fee: {
                gatewayFee: multiplyAndCeil(
                  'amountBreakdownInUsd',
                  'fee.gatewayFee',
                  conversionRate
                ),
                gst: multiplyAndCeil(
                  'amountBreakdownInUsd',
                  'fee.gst',
                  conversionRate
                ),
                processingFee: multiplyAndCeil(
                  'amountBreakdownInUsd',
                  'fee.processingFee',
                  conversionRate
                ),
                gstOnRevenue: multiplyAndCeil(
                  'amountBreakdownInUsd',
                  'fee.gstOnRevenue',
                  conversionRate
                ),
                whtFee: multiplyAndCeil(
                  'amountBreakdownInUsd',
                  'fee.whtFee',
                  conversionRate
                ),
              },
              currency: newCurrency,
            },
          ],
        },
      },
    },
  ];

  logger.info(
    `updateTransactions: communityObjectId: ${communityObjectId}`
  );

  await RevenueTransactionModel.updateMany(
    { communityObjectId, payoutObjectId: { $exists: false } },
    aggregationPipeline,
    { session }
  );
}

async function updateEarnings(
  community,
  newCurrency,
  conversionRate,
  session
) {
  const { _id: communityObjectId } = community;

  const aggregationPipeline = [
    {
      $set: {
        subscriptions: {
          $map: {
            input: '$subscriptions',
            as: 'subscription',
            in: {
              $mergeObjects: [
                '$$subscription',
                {
                  localCurrency: newCurrency,
                  revenueInLocalCurrency: multiplyAndCeil(
                    '$subscription',
                    'revenueInUsd',
                    conversionRate
                  ),
                },
              ],
            },
          },
        },
        folders: {
          $map: {
            input: '$folders',
            as: 'folder',
            in: {
              $mergeObjects: [
                '$$folder',
                {
                  localCurrency: newCurrency,
                  revenueInLocalCurrency: multiplyAndCeil(
                    '$folder',
                    'revenueInUsd',
                    conversionRate
                  ),
                },
              ],
            },
          },
        },
        events: {
          $map: {
            input: '$events',
            as: 'event',
            in: {
              $mergeObjects: [
                '$$event',
                {
                  localCurrency: newCurrency,
                  revenueInLocalCurrency: multiplyAndCeil(
                    '$event',
                    'revenueInUsd',
                    conversionRate
                  ),
                },
              ],
            },
          },
        },
        sessions: {
          $map: {
            input: '$sessions',
            as: 'session',
            in: {
              $mergeObjects: [
                '$$session',
                {
                  localCurrency: newCurrency,
                  revenueInLocalCurrency: multiplyAndCeil(
                    '$session',
                    'revenueInUsd',
                    conversionRate
                  ),
                },
              ],
            },
          },
        },
        challenges: {
          $map: {
            input: '$challenges',
            as: 'challenge',
            in: {
              $mergeObjects: [
                '$$challenge',
                {
                  localCurrency: newCurrency,
                  revenueInLocalCurrency: multiplyAndCeil(
                    '$challenge',
                    'revenueInUsd',
                    conversionRate
                  ),
                },
              ],
            },
          },
        },
        zeroLinks: {
          $map: {
            input: '$zeroLinks',
            as: 'zeroLink',
            in: {
              $mergeObjects: [
                '$$zeroLink',
                {
                  localCurrency: newCurrency,
                  revenueInLocalCurrency: multiplyAndCeil(
                    '$zeroLink',
                    'revenueInUsd',
                    conversionRate
                  ),
                },
              ],
            },
          },
        },
        campaignRewards: {
          $map: {
            input: '$campaignRewards',
            as: 'campaignReward',
            in: {
              $mergeObjects: [
                '$$campaignReward',
                {
                  localCurrency: newCurrency,
                  revenueInLocalCurrency: multiplyAndCeil(
                    '$campaignReward',
                    'revenueInUsd',
                    conversionRate
                  ),
                },
              ],
            },
          },
        },
        referralRewards: {
          $map: {
            input: '$referralRewards',
            as: 'referralReward',
            in: {
              $mergeObjects: [
                '$$referralReward',
                {
                  localCurrency: newCurrency,
                  revenueInLocalCurrency: multiplyAndCeil(
                    '$referralReward',
                    'revenueInUsd',
                    conversionRate
                  ),
                },
              ],
            },
          },
        },
      },
    },
  ];

  logger.info(`updateEarnings: communityObjectId: ${communityObjectId}`);

  await EarningAnalyticsModel.updateMany(
    { communityObjectId },
    aggregationPipeline,
    { session }
  );

  await PredicatedEarningAnalyticsModel.updateMany(
    { communityObjectId },
    aggregationPipeline,
    { session }
  );
}

async function updateCommunity(
  community,
  basePayoutFeeConfig,
  newCurrency,
  updateData,
  earningsConversionRate,
  session
) {
  const {
    _id: communityObjectId,
    basePayoutFeeConfigs,
    earningAnalytics,
    affiliateEarningAnalytics,
  } = community;

  const communityUpdateData = { ...updateData };

  if (earningAnalytics?.revenueInUsd) {
    communityUpdateData['earningAnalytics.revenueInLocalCurrency'] =
      currencyUtils.normalizeAndRoundAmountByCurrency(
        earningAnalytics.revenueInUsd * earningsConversionRate,
        newCurrency
      );
  }

  if (affiliateEarningAnalytics?.revenueInUsd) {
    communityUpdateData[
      'affiliateEarningAnalytics.revenueInLocalCurrency'
    ] = currencyUtils.normalizeAndRoundAmountByCurrency(
      affiliateEarningAnalytics.revenueInUsd * earningsConversionRate,
      newCurrency
    );
  }

  const newPaymentProvider =
    newCurrency === 'INR'
      ? PAYMENT_PROVIDER.STRIPE_INDIA
      : PAYMENT_PROVIDER.STRIPE;

  const newPaymentMethods =
    retrieveCommunityPaymentMethods(newPaymentProvider);

  if (!basePayoutFeeConfigs || basePayoutFeeConfigs.length === 0) {
    communityUpdateData.basePayoutFeeConfigs = [basePayoutFeeConfig];
  } else {
    const updatedBasePayoutFeeConfigs = [...basePayoutFeeConfigs];

    const lastBasePayoutFeeConfig = updatedBasePayoutFeeConfigs.pop();
    lastBasePayoutFeeConfig.effectiveTimeEnd =
      basePayoutFeeConfig.effectiveTimeStart;

    updatedBasePayoutFeeConfigs.push(
      lastBasePayoutFeeConfig,
      basePayoutFeeConfig
    );

    communityUpdateData.basePayoutFeeConfigs = updatedBasePayoutFeeConfigs;
  }

  communityUpdateData.baseCurrency = newCurrency;
  communityUpdateData.payment_methods = newPaymentMethods;

  logger.info(
    `updateCommunity: communityUpdateData: ${JSON.stringify(
      communityUpdateData
    )}`
  );

  await CommunityModel.updateOne(
    { _id: communityObjectId },
    communityUpdateData,
    { session }
  );
}

exports.changeCommunityBaseCurrency = async ({
  communityId,
  newCurrency,
}) => {
  const [community, currency] = await Promise.all([
    CommunityModel.findById(communityId, {
      code: 1,
      baseCurrency: 1,
      stripeProductId: 1,
      stripeUsProductId: 1,
      payment_methods: 1,
      passOnTakeRate: 1,
      passOnPaymentGatewayFee: 1,
      prices: 1,
      payoutFeeConfigs: 1,
      title: 1,
      createdAt: 1,
      countryCreatedIn: 1,
      basePayoutFeeConfigs: 1,
      earningAnalytics: 1,
      affiliateEarningAnalytics: 1,
      isFreeCommunity: 1,
      config: 1,
    }).lean(),
    CountryCurrencyModel.findOne({
      currencyCode: newCurrency,
      localisePrice: true,
    }).lean(),
  ]);

  if (!community) {
    throw new ParamError('Invalid community id');
  }

  if (!currency) {
    throw new ParamError('Currency not supported');
  }

  const { baseCurrency } = community;

  if (baseCurrency === newCurrency) {
    throw new ParamError(
      `Selected currency ${newCurrency} is the same as community base currency ${baseCurrency}`
    );
  }

  const newCommunity = {
    ...community,
    baseCurrency: newCurrency,
  };

  const paymentFeeStructure = await retrievePaymentFeeStructure({
    baseCurrency: newCommunity.baseCurrency,
    planType: newCommunity.config?.planType,
  });

  const { feeConfig: basePayoutFeeConfig } =
    await FeeService.retrieveBasePayoutFeeConfigAndOtherSettings({
      community: newCommunity,
      paymentFeeStructure,
    });

  const paymentBackendRpc = new PaymentBackendRpc();
  await paymentBackendRpc.init();

  const [conversionRateData, earningsConversionRateData] =
    await Promise.all([
      paymentBackendRpc.getConversionRate(baseCurrency, newCurrency),
      paymentBackendRpc.getConversionRate(DEFAULT_CURRENCY, newCurrency),
    ]);

  const conversionRate = conversionRateData.conversionRate;
  const earningsConversionRate = earningsConversionRateData.conversionRate;

  let archive;
  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  try {
    const subscriptionActions = await updateSubscriptionPrices(
      community,
      newCurrency,
      conversionRate,
      session
    );

    const updateData = subscriptionActions.updateData;
    archive = subscriptionActions.archive;

    await updateMembershipLtv(
      community,
      newCurrency,
      conversionRate,
      session
    );

    await updateEventsPrice(
      community,
      newCurrency,
      conversionRate,
      earningsConversionRate,
      session
    );

    await updateFoldersPrice(
      community,
      newCurrency,
      conversionRate,
      earningsConversionRate,
      session
    );

    await updateProgramPrice(
      community,
      newCurrency,
      conversionRate,
      earningsConversionRate,
      session
    );

    await updateReferralRewardEarnings(
      community,
      newCurrency,
      earningsConversionRate,
      session
    );

    // await updateTransactions(
    //   community,
    //   newCurrency,
    //   earningsConversionRate,
    //   session
    // );

    await updateUpsell(
      community,
      newCurrency,
      earningsConversionRate,
      session
    );

    await updateEarnings(
      community,
      newCurrency,
      earningsConversionRate,
      session
    );

    await updateMREarnings(
      community,
      newCurrency,
      earningsConversionRate,
      session
    );

    await updateCommunity(
      community,
      basePayoutFeeConfig,
      newCurrency,
      updateData,
      earningsConversionRate,
      session
    );

    await session.commitTransaction();
  } catch (err) {
    await session.abortTransaction();
    throw err;
  } finally {
    await session.endSession();
  }

  // Archive prices from previous currency when it is the same payment platform
  if (archive) {
    try {
      await archivePrices(archive);
    } catch (err) {
      logger.error('Error archiving prices:', err.message, err.stack);
    }
  }
};
