const { handlers } = require('./handlers');
const CommunityProductModel = require('../../models/product/communityProduct.model');
const logger = require('../logger.service');
const { PRODUCT_STATUS } = require('./constants');

exports.syncProductData = async ({ productType, entity, session }) => {
  const handler = handlers[productType];
  if (!handler) {
    throw new Error(`No handler found for product type: ${productType}`);
  }
  logger.info(`Sync data to product table: ${productType}-${entity._id}`);

  // Handler processes the entity and returns update data
  const updateData = await handler.processEntity(entity);

  const {
    entityObjectId,
    productType: tmpProductType,
    ...otherUpdateFields
  } = updateData;

  const existedProduct = await CommunityProductModel.findOne({
    entityObjectId,
    productType,
  }).lean();

  if (existedProduct) {
    if (otherUpdateFields.status === PRODUCT_STATUS.DELETED) {
      // Since we will always have data in the original product collections (event,folder,..)
      // For delete status, we will just delete the record from this unified product collection
      await CommunityProductModel.deleteOne(
        { _id: existedProduct._id },
        { session }
      );
    } else {
      await CommunityProductModel.updateOne(
        { _id: existedProduct._id }, // or whatever your filter criteria is
        otherUpdateFields,
        { session }
      );
    }
  } else {
    await CommunityProductModel.create([updateData], { session });
  }
};

exports.bulkCreateData = async ({ productType, entityList }) => {
  const handler = handlers[productType];
  if (!handler) {
    throw new Error(`No handler found for product type: ${productType}`);
  }

  // Handler processes the entity and returns update data
  const dataList = await Promise.all(
    entityList.map(async (entity) => {
      const updateData = await handler.processEntity(entity);
      return (
        updateData && {
          insertOne: {
            document: updateData,
          },
        }
      );
    })
  );

  await CommunityProductModel.bulkWrite(dataList);
};
