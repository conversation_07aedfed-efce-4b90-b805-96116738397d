const { PRODUCT_TYPE } = require('../constants');
const EventHandler = require('./event.handler');
const DigitalFilesHandler = require('./digitalFiles.handler');
const CourseHandler = require('./course.handler');
const SessionHandler = require('./session.handler');
const ChallengeHandler = require('./challenge.handler');

const handlers = {
  [`${PRODUCT_TYPE.EVENT}`]: <PERSON>Hand<PERSON>,
  [`${PRODUCT_TYPE.DIGITAL_FILES}`]: DigitalFilesHandler,
  [`${PRODUCT_TYPE.COURSE}`]: <PERSON><PERSON><PERSON><PERSON>,
  [`${PRODUCT_TYPE.SESSION}`]: <PERSON><PERSON><PERSON><PERSON>,
  [`${PRODUCT_TYPE.CHALLENGE}`]: ChallengeHandler,
};

module.exports = {
  handlers,
};
