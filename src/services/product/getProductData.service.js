const ObjectId = require('mongoose').Types.ObjectId;
const CommunityProductModel = require('../../models/product/communityProduct.model');
const {
  PRODUCT_TYPE,
  PRODUCT_STATUS,
  PRODUCT_DATA_QUERY_GROUP,
  PRODUCT_SORT_FIELDS,
} = require('./constants');
const { PROGRAM_CHALLENGE_TYPE } = require('../program/constants');
const { handlers } = require('./handlers');
const regexUtils = require('../../utils/regex.util');

const getSortQuery = ({ sortBy, sortOrder }) => {
  if (!sortBy || !sortOrder) {
    return null;
  }
  if (sortBy === PRODUCT_SORT_FIELDS.EARNINGS) {
    return { 'earningAnalytics.revenueInLocalCurrency': sortOrder };
  }
  return { [sortBy]: sortOrder };
};

const enrichResults = async (results) => {
  // For each result, enrich with memberCount if type is EVENT
  const enrichedResults = await Promise.all(
    results.map(async (item) => {
      const handler = handlers[item.productType];
      const memberCount = await handler.getMemberCount(
        item.entityObjectId
      );
      const otherMetadata = await handler.getMetadata(item);
      return {
        ...item,
        memberCount,
        ...otherMetadata,
      };
    })
  );
  return enrichedResults;
};

const getQueryByPurchaseType = ({ productType, timingStatus, now }) => {
  // Notes: always sort by status, then time (updateTime, startTime)
  // To always return published items first
  switch (productType) {
    case PRODUCT_TYPE.COURSE:
    case PRODUCT_TYPE.DIGITAL_FILES:
    case PRODUCT_TYPE.SESSION:
      return {
        query: {
          productType,
        },
        sort: { status: 1, updatedAt: -1 },
      };

    case PRODUCT_TYPE.EVENT:
      if (timingStatus === PRODUCT_DATA_QUERY_GROUP.PAST) {
        return {
          query: {
            productType,
            'metadata.endTime': { $lt: now },
          },
          sort: { status: 1, 'metadata.startTime': -1 },
        };
      }
      return {
        query: {
          productType,
          'metadata.endTime': { $gte: now },
        },
        sort: { status: 1, 'metadata.startTime': 1 },
      };
    case PRODUCT_TYPE.CHALLENGE:
      if (timingStatus === PRODUCT_DATA_QUERY_GROUP.PAST) {
        return {
          query: {
            productType,
            'metadata.challengeType': PROGRAM_CHALLENGE_TYPE.FIXED,
            'metadata.endTime': { $lt: now },
          },
          sort: { status: 1, 'metadata.startTime': -1 },
        };
      }
      if (timingStatus === PRODUCT_DATA_QUERY_GROUP.ALWAYS_ON) {
        return {
          query: {
            productType,
            'metadata.challengeType': PROGRAM_CHALLENGE_TYPE.ALWAYS_ON,
          },
          sort: { status: 1, updatedAt: -1 },
        };
      }
      return {
        query: {
          productType,
          'metadata.challengeType': PROGRAM_CHALLENGE_TYPE.FIXED,
          'metadata.endTime': { $gte: now },
        },
        sort: { status: 1, 'metadata.startTime': 1 },
      };

    default:
      throw new Error(`Invalid product type: ${productType}`);
  }
};
const getQueriesByGroup = async ({
  communityObjectId,
  productType,
  status,
  priceTypes,
  timingStatus,
  now,
  sortBy,
  sortOrder,
}) => {
  const baseQuery = {
    communityObjectId: new ObjectId(communityObjectId),
    ...(priceTypes?.length && { priceType: { $in: priceTypes } }),
    ...(status && { status }),
  };
  const sortQuery = getSortQuery({ sortBy, sortOrder });

  let queriesByGroup;
  if (productType) {
    const queryByPurchaseType = getQueryByPurchaseType({
      productType,
      timingStatus,
      now,
    });
    queriesByGroup = {
      [PRODUCT_DATA_QUERY_GROUP.BY_PRODUCT_TYPE]: {
        query: {
          ...baseQuery,
          ...queryByPurchaseType.query,
        },
        sort: sortQuery || queryByPurchaseType.sort,
      },
    };
  } else if (sortBy && sortBy) {
    queriesByGroup = {
      [PRODUCT_DATA_QUERY_GROUP.BY_PRODUCT_TYPE]: {
        query: {
          ...baseQuery,
        },
        sort: sortQuery,
      },
    };
  } else {
    queriesByGroup = {
      [PRODUCT_DATA_QUERY_GROUP.UPCOMING]: {
        query: {
          ...baseQuery,
          status: PRODUCT_STATUS.PUBLISHED,
          $or: [
            { productType: PRODUCT_TYPE.EVENT },
            {
              productType: PRODUCT_TYPE.CHALLENGE,
              'metadata.challengeType': PROGRAM_CHALLENGE_TYPE.FIXED,
            },
          ],
          'metadata.endTime': { $gte: now },
        },
        sort: { 'metadata.startTime': 1 },
      },
      [PRODUCT_DATA_QUERY_GROUP.ALWAYS_ON]: {
        query: {
          ...baseQuery,
          status: PRODUCT_STATUS.PUBLISHED,
          $or: [
            {
              productType: {
                $in: [
                  PRODUCT_TYPE.COURSE,
                  PRODUCT_TYPE.DIGITAL_FILES,
                  PRODUCT_TYPE.SESSION,
                ],
              },
            },
            {
              productType: PRODUCT_TYPE.CHALLENGE,
              'metadata.challengeType': PROGRAM_CHALLENGE_TYPE.ALWAYS_ON,
            },
          ],
        },
        sort: { updatedAt: -1 },
      },
      [PRODUCT_DATA_QUERY_GROUP.PAST]: {
        query: {
          ...baseQuery,
          status: PRODUCT_STATUS.PUBLISHED,
          $or: [
            { productType: PRODUCT_TYPE.EVENT },
            {
              productType: PRODUCT_TYPE.CHALLENGE,
              'metadata.challengeType': PROGRAM_CHALLENGE_TYPE.FIXED,
            },
          ],
          'metadata.endTime': { $lt: now },
        },
        sort: { 'metadata.startTime': -1 },
      },
      [PRODUCT_DATA_QUERY_GROUP.UNPUBLISHED]: {
        query: {
          ...baseQuery,
          status: PRODUCT_STATUS.UNPUBLISHED,
        },
        sort: { updatedAt: -1 },
      },
    };
    if (status === PRODUCT_STATUS.PUBLISHED) {
      delete queriesByGroup[PRODUCT_DATA_QUERY_GROUP.UNPUBLISHED];
    } else if (status === PRODUCT_STATUS.UNPUBLISHED) {
      delete queriesByGroup[PRODUCT_DATA_QUERY_GROUP.UPCOMING];
      delete queriesByGroup[PRODUCT_DATA_QUERY_GROUP.PAST];
      delete queriesByGroup[PRODUCT_DATA_QUERY_GROUP.ALWAYS_ON];
    }
  }

  return queriesByGroup;
};

const buildProductSearchStage = async ({
  communityObjectId,
  searchString,
  status,
}) => {
  const searchStage = {
    index: 'unified_product_title_index',
    compound: {
      should: [],
      filter: [
        {
          equals: {
            path: 'communityObjectId',
            value: new ObjectId(communityObjectId),
          },
        },
      ],
    },
  };

  if (searchString) {
    const searchWithEscapedRegexSign =
      regexUtils.escapeRegExp(searchString);
    searchStage.compound.should.push(
      // For the title with lots of words case, ngram might not work fine to get correct data
      // So we add phrase search with higher boost score to get more accurate data

      // Exact phrase match (works better with your nGram setup)
      {
        phrase: {
          path: 'title',
          query: searchWithEscapedRegexSign,
          slop: 0, // No word gaps allowed
          score: {
            boost: { value: 10 },
          },
        },
      },
      // Text search - normal priority
      {
        text: {
          path: 'title',
          query: searchWithEscapedRegexSign,
          score: {
            boost: { value: 1.0 }, // Normal importance
          },
        },
      }
    );
  }

  if (status) {
    searchStage.compound.filter.push({
      text: {
        path: 'status',
        query: status,
      },
    });
  }

  return searchStage;
};

const queryProductWithSearchString = async ({
  communityObjectId,
  pageNo,
  pageSize,
  sortBy,
  sortOrder,
  searchString,
  productType,
  status,
  priceTypes,
  timingStatus,
}) => {
  const now = new Date();
  const skip = (pageNo - 1) * pageSize;
  const searchStage = await buildProductSearchStage({
    communityObjectId,
    searchString,
    status,
  });
  let queryByPurchaseType = {
    query: {},
    sort: { status: 1, updatedAt: -1 },
  };
  if (productType) {
    queryByPurchaseType = getQueryByPurchaseType({
      productType,
      timingStatus,
      now,
    });
  }
  if (priceTypes && priceTypes.length > 0) {
    queryByPurchaseType.query = {
      ...queryByPurchaseType.query,
      priceType: { $in: priceTypes },
    };
  }

  const searchQuery = [
    {
      $search: searchStage,
    },
    {
      $addFields: {
        searchScore: { $meta: 'searchScore' },
      },
    },
    {
      $match: {
        // Filter out low-scoring matches
        searchScore: { $gte: 5 }, // Adjust based on testing
      },
    },
    ...(queryByPurchaseType
      ? [{ $match: queryByPurchaseType.query }]
      : []),
  ];
  const sortQuery = getSortQuery({ sortBy, sortOrder });

  const sort = sortQuery || queryByPurchaseType?.sort;
  const pipeline = [
    ...searchQuery,
    {
      $sort: sort,
    },
    {
      $skip: skip,
    },
    {
      $limit: pageSize,
    },
  ];

  const results = await CommunityProductModel.aggregate(pipeline);
  // For each result, enrich with memberCount, folderItemCounts
  const enrichedResults = await enrichResults(results);

  if (enrichedResults.length === 0) {
    return {
      meta: {
        limit: pageSize,
        page: pageNo,
        pages: 0,
        total: 0,
      },
      products: [],
    };
  }

  const totalCountResult = await CommunityProductModel.aggregate([
    ...searchQuery,
    {
      $count: 'total',
    },
  ]);

  const total = totalCountResult[0].total;

  return {
    meta: {
      limit: pageSize,
      page: pageNo,
      pages: Math.ceil(total / pageSize),
      total,
    },
    products: enrichedResults,
  };
};

const queryProductWithoutSearchString = async ({
  communityObjectId,
  pageNo,
  pageSize,
  sortBy,
  sortOrder,
  productType,
  status,
  priceTypes,
  timingStatus,
}) => {
  const now = new Date();
  const skip = (pageNo - 1) * pageSize;
  const queriesByGroup = await getQueriesByGroup({
    communityObjectId,
    productType,
    status,
    priceTypes,
    timingStatus,
    now,
    sortBy,
    sortOrder,
  });

  // Step 1: Count items in each group
  const ranges = [];

  // Must do it in order, cus this will affect soring logic
  for await (const [group, { query }] of Object.entries(queriesByGroup)) {
    const count = await CommunityProductModel.countDocuments(query);
    ranges.push({ group, count });
  }

  let cumulative = 0;
  ranges.forEach((r) => {
    r.from = cumulative;
    r.to = cumulative + r.count - 1;
    cumulative += r.count;
  });
  if (cumulative === 0) {
    return {
      meta: {
        limit: pageSize,
        page: pageNo,
        pages: 0,
        total: 0,
      },
      products: [],
    };
  }

  // startIndex = 0
  // endIndex = 4
  const startIndex = skip;
  const endIndex = skip + pageSize - 1;

  /*
     groupsToQuery = [
      {
        "group": "upcoming",
        "count": 4,
        "from": 0,
        "to": 3
      },
      {
        "group": "alwaysOn",
        "count": 0,
        "from": 4,
        "to": 3
      },
      {
        "group": "past",
        "count": 20,
        "from": 4,
        "to": 23
      }
    ]
   */
  const groupsToQuery = ranges.filter(
    (r) => !(r.to < startIndex || r.from > endIndex)
  );

  // - First group
  //    groupSkip = 0, groupLimit = 3 - 0 + 1 = 4
  // - Second group: dont have item, skip
  // - Third group
  //    groupSkip = 0, groupLimit = 4 - 4 + 1 = 1
  const queries = await Promise.all(
    groupsToQuery.map(async (group) => {
      const groupSkip = Math.max(startIndex - group.from, 0);
      const groupLimit =
        Math.min(group.to, endIndex) - (group.from + groupSkip) + 1;

      const { query, sort } = queriesByGroup[group.group];
      const results = await CommunityProductModel.find(query)
        .sort(sort)
        .skip(groupSkip)
        .limit(groupLimit)
        .lean();

      // For each result, enrich with memberCount, folderItemCounts
      const enrichedResults = await enrichResults(results);

      return enrichedResults;
    })
  );

  const paginatedResults = queries.flat();

  return {
    meta: {
      limit: pageSize,
      page: pageNo,
      pages: Math.ceil(cumulative / pageSize),
      total: cumulative,
    },
    products: paginatedResults,
  };
};

exports.getProducts = async ({
  communityObjectId,
  pageNo,
  pageSize,
  sortBy,
  sortOrder,
  searchString,
  productType,
  status,
  priceTypes,
  timingStatus,
}) => {
  const priceTypeArr = priceTypes ? priceTypes.split(',') : null;
  if (searchString) {
    return queryProductWithSearchString({
      communityObjectId,
      pageNo,
      pageSize,
      sortBy,
      sortOrder,
      searchString,
      productType,
      status,
      priceTypes: priceTypeArr,
      timingStatus,
    });
  }
  return queryProductWithoutSearchString({
    communityObjectId,
    pageNo,
    pageSize,
    sortBy,
    sortOrder,
    productType,
    status,
    priceTypes: priceTypeArr,
    timingStatus,
  });
};
