const PRODUCT_STATUS = {
  UNPUBLISHED: 'unpublished', // for draft/unpublished
  PUBLISHED: 'published',
  DELETED: 'deleted',
};

const PRODUCT_TYPE = {
  EVENT: 'EVENT',
  DIGITAL_FILES: 'DIGITAL_FILES',
  COURSE: 'COURSE',
  CHALLENGE: 'CHALLENGE',
  SESSION: 'SESSION',
};

const PRODUCT_PRICE_TYPE = {
  FREE: 'FREE',
  PAID: 'PAID',
  FLEXIBLE: 'FLEXIBLE',
};

const PRODUCT_DATA_QUERY_GROUP = {
  UPCOMING: 'UPCOMING',
  ALWAYS_ON: 'ALWAYS_ON',
  PAST: 'PAST',
  UNPUBLISHED: 'UNPUBLISHED',
  BY_PRODUCT_TYPE: 'BY_PRODUCT_TYPE',
};

const PRODUCT_SORT_FIELDS = {
  TITLE: 'title',
  PRODUCT_TYPE: 'productType',
  EARNINGS: 'earnings',
};

module.exports = {
  PRODUCT_STATUS,
  PRODUCT_TYPE,
  PRODUCT_PRICE_TYPE,
  PRODUCT_DATA_QUERY_GROUP,
  PRODUCT_SORT_FIELDS,
};
