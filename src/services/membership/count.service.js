const ObjectId = require('mongoose').Types.ObjectId;

const Community = require('../../communitiesAPI/models/community.model');
const Membership = require('../../models/membership/membership.model');
const CommunityRoleModel = require('../../communitiesAPI/models/communityRole.model');

const subscriptionUtils = require('./utils/subscription.utils');
const whatsappUtils = require('./utils/whatsapp.utils');
const membershipSearchUtils = require('./utils/membershipSearch.utils');
const commonInternalMemberUtils = require('../../utils/checkInternalMember.util');

const {
  ParamError,
  ResourceNotFoundError,
} = require('../../utils/error.util');
const { CHAT_PLATFORMS } = require('../../constants/common');

const { getMongoServerTimeoutConfig } = require('../config.service');

const ActivityService = require('../activity');

const {
  MEMBERSHIP_STATUS,
  FILTERING_FIELDS,
  COUNT_TYPES,
} = require('./constants');
const { aclRoles } = require('../../communitiesAPI/constants');

/**
 * Retrieve member usage
 * @param {{str}} communityId // optional retrieve from community._id
 * @param {{str}} communityCode // optional retrieve from community.code
 * @param {{object}} community // optional full community Object
 * @returns {object} community // optional full community Object
 */
async function getCommunityFromParams(params) {
  let community = params.community;
  if (!params.community && params.communityId) {
    community = await Community.findOne({
      _id: params.communityId,
    }).lean();
  }
  if (!params.community && params.communityCode) {
    community = await Community.findOne({
      code: params.communityCode,
    }).lean();
  }
  if (!community) {
    throw new ResourceNotFoundError('Community not found');
  }
  return community;
}

// eslint-disable-next-line no-unused-vars
async function countMembersFromIndividualSources(community) {
  const subscriptionCount = await subscriptionUtils.countSubscribedMembers(
    {
      communityCode: community.code,
    }
  );
  let result = subscriptionCount;

  const hasWhatsappBot = community.bots?.find(
    (bot) => bot?.type === CHAT_PLATFORMS.WHATSAPP
  );
  if (
    (community.isWhatsappExperienceCommunity ?? false) &&
    hasWhatsappBot
  ) {
    const whatsappNonNasCount =
      await whatsappUtils.countWhatsappNonMemberUsers({
        communityId: community._id,
      });
    result += whatsappNonNasCount;
  }

  return result;
}

async function countCommunityMembersByPaymentInterval({
  communityId,
  communityRole = [],
}) {
  const community = await Community.findOne({
    _id: new ObjectId(communityId),
  });
  if (!community) {
    throw new ParamError('Community not found');
  }

  const allIntervals = await Membership.find({}).distinct(
    'subscriptionInfo.paymentInfo.paymentIntervalString'
  );
  const promises = [];
  const results = {};
  allIntervals.forEach((interval) => {
    if (!interval) {
      return;
    }
    promises.push(async () => {
      const count = await this.countCommunityMembersWithFilters({
        communityData: community,
        communityRole,
        status: [MEMBERSHIP_STATUS.SUBSCRIBED],
        otherFilters: {
          [FILTERING_FIELDS.PAYMENT_INTERVAL.replace(/_/g, '.')]: interval,
        },
      });
      results[interval] = count;
    });
  });

  await Promise.all(promises.map((p) => p()));
  return results;
}

async function getMemberCountBreakdownInfo({
  community,
  countTypes = [],
}) {
  const pipelineMap = {
    // includes manager
    subscribed: membershipSearchUtils.buildSearchMetaPipeline({
      community,
      status: [MEMBERSHIP_STATUS.SUBSCRIBED],
    }),
    notOnNasIO: membershipSearchUtils.buildSearchMetaPipeline({
      community,
      status: [MEMBERSHIP_STATUS.NOT_ON_NASIO],
    }),
  };

  if (countTypes.includes(COUNT_TYPES.STATUS)) {
    pipelineMap.pendingApproval =
      membershipSearchUtils.buildSearchMetaPipeline({
        community,
        status: [MEMBERSHIP_STATUS.PENDING_APPROVAL],
      });
    pipelineMap.inWhatsappGroup =
      membershipSearchUtils.buildSearchMetaPipeline({
        community,
        otherFilters: {
          [FILTERING_FIELDS.IS_IN_WHATSAPP_GROUP.replace(/_/g, '.')]: true,
        },
      });
  }
  const counts = {};
  const promises = [];
  const maxTimeMS = getMongoServerTimeoutConfig({
    collectionName: 'membership',
    operation: 'aggregate',
  });
  Object.keys(pipelineMap).forEach((key) => {
    promises.push(async () => {
      const meta = await Membership.aggregate(pipelineMap[key])
        .option({ maxTimeMS })
        .read('sp');
      counts[key] = meta?.[0]?.count?.total ?? 0;
    });
  });
  await Promise.all(promises.map((p) => p()));
  return counts;
}

/**
 * Get manager count breakdown information
 * @param {str} communityCode // community.code
 * @returns {Object} An object of different manager related counts
 */
const getManagerCountBreakdownInfo = async (communityCode) => {
  const [owner, managerEmails] = await Promise.all([
    CommunityRoleModel.findOne(
      {
        communityCode,
        role: aclRoles.OWNER,
      },
      { email: 1 }
    ).lean(),
    CommunityRoleModel.find({
      communityCode,
    }).distinct('email'),
  ]);

  const creatorCount = 1;
  const managerCount = managerEmails.length - creatorCount;
  const internalManagerCount = managerEmails.filter(
    (managerEmail) =>
      commonInternalMemberUtils.hasInternalDomain(managerEmail) &&
      owner.email !== managerEmail
  ).length;

  return {
    // Managers (Includes internal emails)
    managerCount,
    // internal emails (Excluding creator)
    internalManagerCount,
    creatorCount,
  };
};

/**
 * Calculate total active membership
 * @param {object} membershipCountInfo // retrieve from getMemberCountBreakdownInfo function
 * @returns {int} total active membership
 */
function calculateTotalActiveMembership(membershipCountInfo = {}) {
  const { subscribed, notOnNasIO } = membershipCountInfo;
  return subscribed + notOnNasIO;
}

/**
 * Calculate member usage
 * @param {object} membershipCountInfo // retrieve from getMemberCountBreakdownInfo function
 * @param {object} managerCountInfo // retrieve from getManagerCountBreakdownInfo function
 * @returns {int} managerUsage
 */
function calculateMemberUsage(membershipCountInfo, managerCountInfo = {}) {
  const { subscribed, notOnNasIO } = membershipCountInfo;
  const { managerCount, creatorCount } = managerCountInfo;
  return subscribed + notOnNasIO - managerCount - creatorCount;
}

/**
 * Calculate manager usage
 * @param {object} managerCountInfo // retrieve from getManagerCountBreakdownInfo function
 * @returns {int} managerUsage
 */
function calculateManagerUsage(managerCountInfo = {}) {
  const { managerCount, internalManagerCount } = managerCountInfo;
  return managerCount - internalManagerCount;
}

exports.countCommunityMembersWithFilters = async ({
  communityData,
  communityId,
  searchString,
  communityRole,
  status,
  reachFilters,
  otherFilters,
}) => {
  let community = communityData;
  if (!communityData) {
    community = await Community.findOne({ _id: communityId }).lean();
    if (!community) {
      throw new ParamError('Community not found');
    }
  }
  const searchMetaPipeline = membershipSearchUtils.buildSearchMetaPipeline(
    {
      community,
      searchString,
      communityRole,
      status,
      reachFilters,
      otherFilters,
    }
  );
  const maxTimeMS = getMongoServerTimeoutConfig({
    collectionName: 'membership',
    operation: 'aggregate',
  });
  const meta = await Membership.aggregate(searchMetaPipeline)
    .option({
      maxTimeMS,
    })
    .read('sp');
  return meta?.[0]?.count?.total ?? 0;
};

exports.countCommunityMembers = async (params) => {
  const community = await getCommunityFromParams(params);
  const countTypes = params.countTypes || [COUNT_TYPES.STATUS];

  const [membershipCountInfo, managerCountInfo] = await Promise.all([
    getMemberCountBreakdownInfo({ community, countTypes }),
    getManagerCountBreakdownInfo(community.code),
  ]);
  const totalActiveCount = calculateTotalActiveMembership(
    membershipCountInfo
  );
  const results = {
    count: totalActiveCount,
    summary: {
      ...managerCountInfo,
      selectedManagerCount: calculateManagerUsage(managerCountInfo),
      memberCount: calculateMemberUsage(
        membershipCountInfo,
        managerCountInfo
      ),
    },
  };

  if (countTypes.includes(COUNT_TYPES.STATUS)) {
    results.statusBreakdown = membershipCountInfo;
  }
  if (countTypes.includes(COUNT_TYPES.PAYMENT_INTERVAL)) {
    const paymentIntervalCount =
      await countCommunityMembersByPaymentInterval({
        communityId: community._id,
        communityRole: [aclRoles.MEMBER],
        excludeManagers: true,
      });

    results.paymentIntervalBreakdown = paymentIntervalCount;

    results.paidCount = Object.values(paymentIntervalCount).reduce(
      (acc, paymentCount) => acc + paymentCount,
      0
    );
  }

  if (countTypes.includes(COUNT_TYPES.LAST_30_DAYS)) {
    const last30Days = await ActivityService.retrieveLastNDays({
      noOfDays: 30,
      communityObjectId: community._id,
      excludeManagers: true,
    });

    results.last30Days = last30Days;
  }

  return results;
};

/**
 * Retrieve manager usage
 * @param {{str}} communityId // optional retrieve from community._id
 * @param {{str}} communityCode // optional retrieve from community.code
 * @param {{object}} community // optional full community Object
 * @param {{object}} managerCountInfo // retrieve from getManagerCountBreakdownInfo function
 * @returns {int} managerUsage
 */
exports.retrieveManagerUsage = async (params) => {
  const { managerCountInfo } = params;
  const community = await getCommunityFromParams(params);
  const finalManagerCountInfo =
    managerCountInfo ||
    (await getManagerCountBreakdownInfo(community.code));

  return calculateManagerUsage(finalManagerCountInfo);
};

/**
 * Retrieve member usage
 * @param {{str}} communityId // optional retrieve from community._id
 * @param {{str}} communityCode // optional retrieve from community.code
 * @param {{object}} community // optional full community Object
 * @param {{object}} membershipCountInfo // retrieve from getMemberCountBreakdownInfo function
 * @param {{object}} managerCountInfo // retrieve from getManagerCountBreakdownInfo function
 * @returns {int} managerUsage
 */
exports.retrieveMemberUsage = async (params) => {
  const { managerCountInfo, membershipCountInfo } = params;
  const community = await getCommunityFromParams(params);

  const [finalManagerCountInfo, finalMembershipCountInfo] =
    await Promise.all([
      !managerCountInfo
        ? getManagerCountBreakdownInfo(community.code)
        : { ...managerCountInfo },
      !membershipCountInfo
        ? getMemberCountBreakdownInfo({ community })
        : { ...membershipCountInfo },
    ]);

  return calculateMemberUsage(
    finalMembershipCountInfo,
    finalManagerCountInfo
  );
};

/**
 * Retrieve membership usage sumamry
 * @param {{str}} communityId // optional retrieve from community._id
 * @param {{str}} communityCode // optional retrieve from community.code
 * @param {{object}} community // optional full community Object
 * @param {{object}} membershipCountInfo // retrieve from getMemberCountBreakdownInfo function
 * @param {{object}} managerCountInfo // retrieve from getManagerCountBreakdownInfo function
 * @returns {object} managerUsage + memberUsage
 */
exports.retrieveMembershipUsageSummary = async (params) => {
  const { managerCountInfo, membershipCountInfo } = params;
  const community = await getCommunityFromParams(params);
  const [finalManagerCountInfo, finalMembershipCountInfo] =
    await Promise.all([
      !managerCountInfo
        ? getManagerCountBreakdownInfo(community.code)
        : { ...managerCountInfo },
      !membershipCountInfo
        ? getMemberCountBreakdownInfo({ community })
        : { ...membershipCountInfo },
    ]);

  const managerUsage = calculateManagerUsage(finalManagerCountInfo);
  const memberUsage = calculateMemberUsage(
    finalMembershipCountInfo,
    finalManagerCountInfo
  );

  return {
    managerUsage,
    memberUsage,
  };
};
