const { DateTime } = require('luxon');
const ObjectId = require('mongoose').Types.ObjectId;

const CommunityRoles = require('../../../communitiesAPI/models/communityRole.model');
const CommunityApplication = require('../../../communitiesAPI/models/communityApplications.model');
const Learner = require('../../../models/learners.model');
const CommunityPurchaseTransaction = require('../../../communitiesAPI/models/communityPurchaseTransactions.model');

const BaseChangeStreamHandler = require('./base.handler');

const countryInfoMappingService = require('../../countryInfoMapping/countryInfoMapping.service');
const learnerUtils = require('../../user/utils/learner.utils');

const updateService = require('../update.service');

const { getLock } = require('../../../redisLock');
const logger = require('../../logger.service');

const {
  COMMUNITY_SUBSCRIPTION_STATUSES,
  COMMUNITY_APPLICATION_STATUS,
} = require('../../../constants/common');

const {
  MEMBERSHIP_STATUS,
  MEMBERSHIP_PAYMENT_STATUS,
  MEMBERSHIP_TYPE,
  MEMBERSHIP_COMMUNITY_ROLE,
  MEMBERSHIP_DISCOUNT_TYPE,
  INSERT_LOCK_TTL,
} = require('../constants');

class CommunitySubscriptionHandler extends BaseChangeStreamHandler {
  async handle() {
    await this.getCommunity();
    this.eventIdKey = 'lastChangeStreamEventIds.communitySubscription';
    this.eventDateKey = 'lastChangeStreamEventDates.communitySubscription';
    await this.configureIdentifiers();
    await super.handle();
  }

  async insertHandler() {
    await super.insertHandler();
    logger.info(
      `CommunitySubscriptionHandler: new subscription`,
      `identifierFilter=${JSON.stringify(
        this.identifierFilter
      )}|communityCode=${this.fullDocument.communityCode}`
    );

    await this.getLearnerAndCountry();
    await this.getCommunityRole();
    await this.getSubscriptionSetter();
    await this.getApplicationSetter();
    await this.getAccountExtraInfoSetter();

    const lock = await getLock();
    const releaseLock = await lock(
      `membership_upsert_${this.community.code}_${this.lockIdentifier}`,
      INSERT_LOCK_TTL
    );
    try {
      await this.getExistingMembership();
      if (this.existingMembership) {
        await this.insertExistingMembershipHandler();
      } else {
        await this.insertNewMembershipHandler();
      }
    } finally {
      await releaseLock();
    }
  }

  async insertExistingMembershipHandler() {
    const set = {
      isDemo: this.fullDocument.isDemo || false,
      learnerObjectId: this.fullDocument.learnerObjectId,
      ...this.learnerRelatedInfo,
      subscriptionObjectId: this.fullDocument._id,
      ...this.subscriptionSetter,
      ...this.applicationSetter,
      ...this.accountExtraInfoSetter,
      communityRole: this.communityRole,
      reachInfo: {
        optOutFromEmail: false,
        optOutFromWhatsApp: false,
      },
      status: this.getMembershipStatus(),
      [this.eventIdKey]: this.eventId,
      [this.eventDateKey]: DateTime.utc(),
      ...this.toSetIdentifier,
    };
    if (this.countryInfo) {
      set.countryInfo = this.countryInfo;
    }
    if (
      this.fullDocument.status === COMMUNITY_SUBSCRIPTION_STATUSES.CURRENT
    ) {
      set['subscriptionInfo.subscriptionStartDate'] = DateTime.utc();
    }
    logger.info(
      'CommunitySubscriptionHandler: set payload',
      JSON.stringify(set)
    );

    const result = await updateService.upsertMembership({
      filters: {
        communityObjectId: this.community._id,
        communityCode: this.community.code,
        ...this.identifierFilter,
      },
      set,
    });
    if (result) {
      await this.deleteDuplicate(result._id);
    }
  }

  async insertNewMembershipHandler() {
    logger.info('CommunitySubscriptionHandler: inserting new membership');
    const set = {
      isDemo: this.fullDocument.isDemo || false,
      learnerObjectId: this.fullDocument.learnerObjectId,
      ...this.learnerRelatedInfo,
      subscriptionObjectId: this.fullDocument._id,
      ...this.subscriptionSetter,
      ...this.applicationSetter,
      ...this.accountExtraInfoSetter,
      communityRole: this.communityRole,
      reachInfo: {
        optOutFromEmail: false,
        optOutFromWhatsApp: false,
      },
      status: this.getMembershipStatus(),
      [this.eventIdKey]: this.eventId,
      [this.eventDateKey]: DateTime.utc(),
      ...this.toSetIdentifier,
      createdAt: DateTime.utc(),
    };
    if (this.countryInfo) {
      set.countryInfo = this.countryInfo;
    }
    if (
      this.fullDocument.status === COMMUNITY_SUBSCRIPTION_STATUSES.CURRENT
    ) {
      set['subscriptionInfo.subscriptionStartDate'] = DateTime.utc();
    }
    logger.info(
      'CommunitySubscriptionHandler: set payload',
      JSON.stringify(set)
    );

    const result = await updateService.upsertMembership({
      filters: {
        communityObjectId: this.community._id,
        communityCode: this.community.code,
        ...this.identifierFilter,
      },
      set,
    });
    if (result) {
      await this.deleteDuplicate(result._id);
    }
  }

  async updateHandler() {
    await super.updateHandler();
    await this.getExistingMembership();
    logger.info(
      `CommunitySubscriptionHandler: subscription updated`,
      `identifierFilters=${JSON.stringify(
        this.identifierFilter
      )}|communityCode=${this.fullDocument.communityCode}`
    );
    const newStatus = this.fullDocument.status;
    const oldStatus = this.fullDocumentBeforeChange.status;
    const filters = {
      communityObjectId: this.community._id,
      communityCode: this.community.code,
      subscriptionObjectId: new ObjectId(this.fullDocument._id),
      ...this.identifierFilter,
    };
    if (newStatus === oldStatus) {
      await this.sameStatusHandler(filters);
    } else {
      logger.info(
        `CommunitySubscriptionHandler: status change from ${oldStatus} to ${newStatus}`
      );
      switch (newStatus) {
        case COMMUNITY_SUBSCRIPTION_STATUSES.PENDING:
          await this.toPendingHandler(filters);
          break;
        case COMMUNITY_SUBSCRIPTION_STATUSES.REJECTED:
          await this.toRejectedHandler(filters);
          break;
        case COMMUNITY_SUBSCRIPTION_STATUSES.CANCELLED:
          await this.toCancelledHandler(filters);
          break;
        case COMMUNITY_SUBSCRIPTION_STATUSES.CURRENT:
          await this.toSubscribedHandler(filters);
          break;
        case COMMUNITY_SUBSCRIPTION_STATUSES.REMOVED:
          await this.toRemovedHandler(filters);
          break;
        default:
          logger.info(
            `CommunitySubscriptionHandler: status change from ${oldStatus} to ${newStatus} not handled`
          );
          break;
      }
    }
    logger.info(
      'CommunitySubscriptionHandler: existing membership upserted'
    );
  }

  async sameStatusHandler(filters) {
    logger.info(
      `CommunitySubscriptionHandler: no status change, updating subscription info`
    );

    const set = {
      'subscriptionInfo.paymentInfo.nextBillingDate':
        this.fullDocument.nextBillingDate,
      'subscriptionInfo.paymentInfo.billingCycle':
        this.fullDocument.billingCycle,
      [this.eventIdKey]: this.eventId,
      [this.eventDateKey]: DateTime.utc(),
      ...this.toSetIdentifier,
    };
    if (!this.existingMembership?.status) {
      set.status = this.getMembershipStatus();
    }
    if (
      this.fullDocument.status === COMMUNITY_SUBSCRIPTION_STATUSES.CURRENT
    ) {
      // if there's a change to a subscription with status current always make it the primary subscription
      set.status = MEMBERSHIP_STATUS.SUBSCRIBED;
    } else if (
      this.fullDocumentBeforeChange.status ===
      COMMUNITY_SUBSCRIPTION_STATUSES.CANCELLED
    ) {
      // When a user cancels during the grace period, we need to ensure that `subscriptionEndDate` is correctly updated.
      //
      // This involves handling two separate events:
      // 1. The user initiates a cancellation. At this point:
      //    - `cancelledAt` is wrongly set to the next billing date.
      //    - The subscription status is updated to "cancelled".
      // 2. Later, Stripe sends a cancellation event. At this point:
      //    - `cancelledAt` is updated to the actual cancellation date (the time the user initiated the cancellation),
      //      which is the correct value we should use.
      set['subscriptionInfo.unsubscribedInfo.subscriptionEndDate'] =
        this.fullDocument.cancelledAt || this.fullDocument.nextBillingDate;
    }
    await updateService.updateMembership({
      filters,
      set,
    });
  }

  async toPendingHandler(filters) {
    logger.info('CommunitySubscriptionHandler: status change to pending');
    logger.info(
      'Only updating status here and rely on community application handler for applicationInfo'
    );
    if (this.existingMembership?.status === MEMBERSHIP_STATUS.SUBSCRIBED) {
      // shall not make status go backward in case of race condition
      return;
    }
    const set = {
      status: MEMBERSHIP_STATUS.PENDING_APPROVAL,
      [this.eventIdKey]: this.eventId,
      [this.eventDateKey]: DateTime.utc(),
      ...this.toSetIdentifier,
    };
    await updateService.updateMembership({
      filters,
      set,
    });
  }

  async toRejectedHandler(filters) {
    logger.info('CommunitySubscriptionHandler: status change to rejected');
    logger.info(
      'Only updating status here and rely on community application handler for applicationInfo'
    );
    const set = {
      status: MEMBERSHIP_STATUS.REJECTED,
      [this.eventIdKey]: this.eventId,
      [this.eventDateKey]: DateTime.utc(),
      ...this.toSetIdentifier,
    };
    const unset = {
      'subscriptionInfo.paymentInfo.nextBillingDate': 1,
    };
    await updateService.updateMembership({
      filters,
      set,
      unset,
    });
  }

  async toCancelledHandler(filters) {
    logger.info(
      'CommunitySubscriptionHandler: status change to cancelled'
    );
    const unsubscribedInfo = {
      unsubscribedDate: this.fullDocument.unsubscribedAt || DateTime.utc(),
      unsubscribedReason: this.fullDocument.cancellationReason,
      subscriptionEndDate:
        this.fullDocument.cancelledAt || this.fullDocument.nextBillingDate,
    };
    const set = {
      status: MEMBERSHIP_STATUS.UNSUBSCRIBED,
      'subscriptionInfo.unsubscribedInfo': unsubscribedInfo,
      'subscriptionInfo.paymentInfo.paymentStatus':
        MEMBERSHIP_PAYMENT_STATUS.CANCELLED,
      [this.eventIdKey]: this.eventId,
      [this.eventDateKey]: DateTime.utc(),
      ...this.toSetIdentifier,
    };
    const unset = {
      'subscriptionInfo.paymentInfo.nextBillingDate': 1,
    };
    await updateService.updateMembership({
      filters,
      set,
      unset,
    });
  }

  async toSubscribedHandler(filters) {
    logger.info(
      'CommunitySubscriptionHandler: status change to subscribed'
    );
    // either application approved or manual update
    logger.info(
      'If application approved, rely on community application handler for applicationInfo'
    );
    const set = {
      'subscriptionInfo.subscriptionStartDate': DateTime.utc(),
      status: MEMBERSHIP_STATUS.SUBSCRIBED,
      [this.eventIdKey]: this.eventId,
      [this.eventDateKey]: DateTime.utc(),
      ...this.toSetIdentifier,
    };
    await updateService.updateMembership({
      filters,
      set,
    });
  }

  async toRemovedHandler(filters) {
    logger.info('CommunitySubscriptionHandler: status change to removed');
    const removalInfo = {
      removalDate: this.fullDocument.removedAt,
      removedBy: this.fullDocument.removedBy,
      removalReason: this.fullDocument.removalReason,
    };
    const set = {
      status: MEMBERSHIP_STATUS.REMOVED,
      'subscriptionInfo.removalInfo': removalInfo,
      [this.eventIdKey]: this.eventId,
      [this.eventDateKey]: DateTime.utc(),
      ...this.toSetIdentifier,
    };
    const unset = {
      'subscriptionInfo.paymentInfo.nextBillingDate': 1,
    };
    await updateService.updateMembership({
      filters,
      set,
      unset,
    });
  }

  async deleteHandler() {
    super.deleteHandler();
    logger.info(
      `CommunitySubscriptionHandler: subscription deleted for ${JSON.stringify(
        this.identifierFilter
      )}`
    );
    const set = {
      status: MEMBERSHIP_STATUS.DELETED,
      [this.eventIdKey]: this.eventId,
      [this.eventDateKey]: DateTime.utc(),
      ...this.toSetIdentifier,
    };
    const filters = {
      communityObjectId: this.community._id,
      communityCode: this.community.code,
      subscriptionObjectId: new ObjectId(
        this.fullDocumentBeforeChange._id
      ),
      ...this.identifierFilter,
    };
    await updateService.updateMembership({
      filters,
      set,
    });
    logger.info(
      'CommunitySubscriptionHandler: existing membership upserted'
    );
  }

  async getSubscriptionSetter() {
    let paymentInfo = {};
    if (this.fullDocument.memberType !== MEMBERSHIP_TYPE.FREE) {
      paymentInfo = {
        'subscriptionInfo.paymentInfo.paymentInterval':
          this.fullDocument.interval,
        'subscriptionInfo.paymentInfo.paymentIntervalCount':
          this.fullDocument.intervalCount,
        'subscriptionInfo.paymentInfo.nextBillingDate':
          this.fullDocument.nextBillingDate,
        'subscriptionInfo.paymentInfo.basePrice': this.fullDocument.amount,
        'subscriptionInfo.paymentInfo.baseCurrency':
          this.fullDocument.currency,
        'subscriptionInfo.paymentInfo.paymentProvider':
          this.fullDocument.paymentProvider,
        'subscriptionInfo.paymentInfo.paymentProviderCurrenc':
          this.fullDocument[
            `${this.fullDocument.paymentProvider}Currency`
          ] || this.fullDocument.stripeCurrency,
        'subscriptionInfo.paymentInfo.localCurrency':
          this.fullDocument.localCurrency ||
          this.fullDocument.stripeCurrency,
        'subscriptionInfo.paymentInfo.localPrice':
          this.fullDocument.localPrice || this.fullDocument.stripePrice,
      };
      if (this.fullDocument.interval) {
        paymentInfo[
          'subscriptionInfo.paymentInfo.paymentIntervalString'
        ] = `${this.fullDocument.intervalCount}-${this.fullDocument.interval}`;
      }
    }
    this.subscriptionSetter = {
      'subscriptionInfo.signUpDate': this.fullDocument.createdAt,
      'subscriptionInfo.memberType': this.fullDocument.memberType,
      ...paymentInfo,
    };
    if (this.fullDocument.communitySignupId) {
      this.subscriptionSetter[
        'subscriptionInfo.purchaseTransactionObjectId'
      ] = new ObjectId(this.fullDocument.communitySignupId);
      const purchaseTransaction =
        await CommunityPurchaseTransaction.findOne(
          {
            _id: new ObjectId(this.fullDocument.communitySignupId),
          },
          {
            promoCodeStripeId: 1,
            isFreeTrial: 1,
          }
        );
      if (purchaseTransaction?.promoCodeStripeId) {
        const discountDetails =
          await BaseChangeStreamHandler.getDiscountSetter({
            discountCode: purchaseTransaction.promoCodeStripeId,
            communityCode: this.community.code,
          });
        if (
          discountDetails?.[
            'subscriptionInfo.paymentInfo.discountCodeUsedType'
          ] === MEMBERSHIP_DISCOUNT_TYPE.PERCENTAGE &&
          discountDetails?.[
            'subscriptionInfo.paymentInfo.discountCodeUsedValue'
          ] === 100
        ) {
          logger.info(
            '100% discount detected, setting payment status to success'
          );
          this.subscriptionSetter[
            'subscriptionInfo.paymentInfo.paymentStatus'
          ] = MEMBERSHIP_PAYMENT_STATUS.SUCCESS;
          this.subscriptionSetter[
            'subscriptionInfo.paymentInfo.lastSuccessfulPaymentDate'
          ] = DateTime.utc();
        }
        Object.assign(this.subscriptionSetter, discountDetails);
      }
    }
  }

  getMembershipStatus() {
    if (this.application) {
      // handling auto-approval
      switch (this.application.status) {
        case COMMUNITY_APPLICATION_STATUS.APPROVED:
          return MEMBERSHIP_STATUS.SUBSCRIBED;
        case COMMUNITY_APPLICATION_STATUS.REJECTED:
          return MEMBERSHIP_STATUS.REJECTED;
        default:
          break;
      }
    }
    const subscriptionStatus = this.fullDocument.status;
    switch (subscriptionStatus) {
      case COMMUNITY_SUBSCRIPTION_STATUSES.PENDING:
        return MEMBERSHIP_STATUS.PENDING_APPROVAL;
      case COMMUNITY_SUBSCRIPTION_STATUSES.REJECTED:
        return MEMBERSHIP_STATUS.REJECTED;
      case COMMUNITY_SUBSCRIPTION_STATUSES.CURRENT:
        return MEMBERSHIP_STATUS.SUBSCRIBED;
      case COMMUNITY_SUBSCRIPTION_STATUSES.CANCELLED:
        return MEMBERSHIP_STATUS.UNSUBSCRIBED;
      case COMMUNITY_SUBSCRIPTION_STATUSES.REMOVED:
        return MEMBERSHIP_STATUS.REMOVED;
      default:
        return MEMBERSHIP_STATUS.SUBSCRIBED;
    }
  }

  async getLearnerAndCountry() {
    await this.getLearner();
    if (this.learner) {
      logger.info('CommunitySubscriptionHandler: learner found');
      this.learnerRelatedInfo = {
        profileImage: this.learner.profileImage,
      };
      if (!this.identifierFilter.phoneNumber && this.learner.phoneNumber) {
        this.learnerRelatedInfo.phoneNumber =
          BaseChangeStreamHandler.formatPhoneNumber(
            this.learner.phoneNumber
          );
      }
      const fullName = learnerUtils.getFullName(this.learner);
      if (fullName) {
        this.learnerRelatedInfo.name = fullName;
      }
      if (this.learner.countryId) {
        this.getCountry();
      }
    } else {
      this.learnerRelatedInfo = {};
    }
  }

  async getLearner() {
    const projection = {
      _id: 1,
      firstName: 1,
      lastName: 1,
      countryId: 1,
      profileImage: 1,
      phoneNumber: 1,
      socialMedia: 1,
    };
    if (this.fullDocument.learnerObjectId) {
      this.learner = await Learner.findOne(
        { _id: new ObjectId(this.fullDocument.learnerObjectId) },
        projection
      );
    }
    if (!this.learner && this.fullDocument.learnerId) {
      this.learner = await Learner.findOne(
        { learnerId: this.fullDocument.learnerId },
        projection
      );
    }
    if (!this.learner) {
      this.learner = await Learner.findOne(
        { email: this.fullDocument.email },
        projection
      );
    }
  }

  getCountry() {
    if (this.learner && this.learner.countryId) {
      logger.info('CommunitySubscriptionHandler: country found');
      const countryFromCache =
        countryInfoMappingService.getCountryInfoByIdFromMemoryCache(
          this.learner.countryId
        );
      if (countryFromCache) {
        this.countryInfo = {
          name: countryFromCache.country,
          id: countryFromCache.countryId,
          code: countryFromCache.countryCode,
        };
      } else {
        this.countryInfo = {
          id: this.learner.countryId,
        };
      }
    }
  }

  async getCommunityRole() {
    const communityRole = await CommunityRoles.findOne({
      communityObjectId: this.community._id,
      email: this.fullDocument.email,
    });
    if (communityRole) {
      logger.info('CommunitySubscriptionHandler: community role found');
      this.communityRole = communityRole.role;
    } else {
      this.communityRole = [MEMBERSHIP_COMMUNITY_ROLE.MEMBER];
    }
  }

  async getApplicationSetter() {
    const application = await CommunityApplication.findOne({
      communityCode: this.community.code,
      email: this.fullDocument.email,
      subscriptionObjectId: new ObjectId(this.fullDocument._id),
    });
    if (application) {
      this.application = application;
      logger.info('CommunitySubscriptionHandler: application found');
      this.applicationSetter = {
        'applicationInfo.applicationObjectId': application._id,
        'applicationInfo.applicationSubmittedDate': application.createdAt,
      };
    } else {
      this.applicationSetter = {};
    }
  }

  async getAccountExtraInfoSetter() {
    this.accountExtraInfoSetter = {};
    if (!this.learner) {
      return;
    }
    const { profileImage, socialMedia } = this.learner;

    const hasImage =
      profileImage &&
      !(
        profileImage.includes('default-user-image') ||
        profileImage.includes('randomProfileImage')
      );
    if (hasImage) {
      this.accountExtraInfoSetter[
        'accountExtraInfo.hasCustomProfileImage'
      ] = true;
    }
    if (socialMedia?.length > 0) {
      this.accountExtraInfoSetter[
        'accountExtraInfo.hasSocialMediaLinks'
      ] = true;
    }
  }
}

module.exports = CommunitySubscriptionHandler;
