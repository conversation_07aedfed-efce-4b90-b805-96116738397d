/* eslint-disable no-unused-vars */
const { DateTime } = require('luxon');

const CommunitySubscriptions = require('../../../communitiesAPI/models/communitySubscriptions.model');

const {
  buildLearnerMatchStage,
} = require('../../user/utils/learner.utils');

const {
  COMMUNITY_SUBSCRIPTION_STATUSES,
} = require('../../../constants/common');

const mongodbUtils = require('../../../utils/mongodb.util');

/**
 * Get a list of subscription ids that are Cancelled but has a newer Current record
 * @param {string} communityCode
 * @returns {Promise<Array<ObjectId>>}
 */
const getFalsePositiveUnsubscribedEmails = async (communityCode) => {
  const pipeline = [
    {
      $match: {
        communityCode,
      },
    },
    {
      $group: {
        _id: '$email',
        count: {
          $sum: 1,
        },
        statuses: {
          $addToSet: '$status',
        },
      },
    },
    {
      $match: {
        count: {
          $gt: 1,
        },
      },
    },
  ];

  const subscriptions = await CommunitySubscriptions.aggregate(pipeline);
  if (!subscriptions?.length) {
    return [];
  }
  const results = [];
  for (const subscription of subscriptions) {
    if (
      subscription.statuses?.includes(
        COMMUNITY_SUBSCRIPTION_STATUSES.CURRENT
      ) ||
      subscription.statuses?.includes(
        COMMUNITY_SUBSCRIPTION_STATUSES.PENDING
      )
    ) {
      results.push(subscription._id);
    }
  }
  return results;
};

/**
 * Build subscription match stage
 * @param {*} param excludeEmails can be managers or false positive unsubscribed members
 * @returns Mongo pipeline match stage
 */
function buildSubscriptionMatchStage({
  communityCode,
  status,
  searchString,
  excludeEmails = [],
  excludePhoneNumbers = [],
  startObjectId = null,
  endObjectId = null,
}) {
  const objectIdRangeMatchCondition =
    startObjectId && endObjectId
      ? {
          _id: {
            $gte: mongodbUtils.toObjectId(startObjectId),
            $lte: mongodbUtils.toObjectId(endObjectId),
          },
        }
      : {};

  const now = DateTime.utc();
  let statusMatchCondition = {};
  if (status === COMMUNITY_SUBSCRIPTION_STATUSES.CURRENT) {
    statusMatchCondition = {
      $or: [
        { status: COMMUNITY_SUBSCRIPTION_STATUSES.CURRENT },
        {
          $and: [
            { status: COMMUNITY_SUBSCRIPTION_STATUSES.CANCELLED },
            { cancelledAt: { $gte: now } },
          ],
        },
      ],
    };
  } else if (status === COMMUNITY_SUBSCRIPTION_STATUSES.CANCELLED) {
    statusMatchCondition = {
      $and: [
        { status: COMMUNITY_SUBSCRIPTION_STATUSES.CANCELLED },
        { cancelledAt: { $lt: now } },
      ],
    };
  }

  const emailMatchCondition = {};
  if (excludeEmails?.length) {
    emailMatchCondition.email = { $nin: excludeEmails };
  }
  const phoneNumberMatchCondition = {};
  if (excludePhoneNumbers?.length) {
    phoneNumberMatchCondition.phoneNumber = { $nin: excludePhoneNumbers };
  }

  return {
    communityCode,
    ...objectIdRangeMatchCondition,
    ...statusMatchCondition,
    ...emailMatchCondition,
    ...phoneNumberMatchCondition,
  };
}

/**
 * Build subscription pipeline based on filter & search condition
 * @param {*} param exclude emails can be managers or false positive unsubscribed members
 * @returns Mongo pipeline
 */
const buildSubscriptionWithLearnerPipeline = ({
  communityCode,
  searchString,
  status,
  excludeEmails,
  excludePhoneNumbers,
  learnerCondition,
  skip,
  limit,
  projectStage,
  startObjectId = null,
  endObjectId = null,
}) => {
  const pipeline = [];
  const matchStage = buildSubscriptionMatchStage({
    communityCode,
    status,
    excludeEmails,
    excludePhoneNumbers,
    startObjectId,
    endObjectId,
  });

  pipeline.push({ $match: matchStage });
  pipeline.push({ $sort: { createdAt: -1 } });
  pipeline.push(
    {
      $lookup: {
        from: 'learners',
        localField: 'learnerId',
        foreignField: 'learnerId',
        as: 'learner',
      },
    },
    {
      $unwind: {
        path: '$learner',
        preserveNullAndEmptyArrays: true,
      },
    }
  );

  const learnerMatchStage = buildLearnerMatchStage({
    searchString,
    learnerCondition,
  });
  if (learnerMatchStage) {
    pipeline.push({ $match: learnerMatchStage });
  }
  if (projectStage !== undefined) {
    pipeline.push({ $project: projectStage });
  } else {
    pipeline.push({
      $project: {
        email: 1,
        learnerId: 1,
        phoneNumber: '$learner.phoneNumber',
        firstName: '$learner.firstName',
        lastName: '$learner.lastName',
        profileImage: '$learner.profileImage',
        subscriptionId: 1,
        status: 1,
        cancelledAt: 1,
        _id: 0,
      },
    });
  }

  if (startObjectId && endObjectId) {
    pipeline.push({
      $facet: {
        meta: [{ $count: 'total' }],
        data: [{ $limit: limit }],
      },
    });
    pipeline.push({ $unwind: '$meta' });
  } else if (skip !== undefined && limit !== undefined) {
    pipeline.push({
      $facet: {
        meta: [{ $count: 'total' }],
        data: [{ $skip: skip }, { $limit: limit }],
      },
    });
    pipeline.push({ $unwind: '$meta' });
  }
  return pipeline;
};

const countSubscribedMembers = async ({
  communityCode,
  searchString,
  excludeEmails,
  excludePhoneNumbers,
}) => {
  const matchStage = buildSubscriptionMatchStage({
    communityCode,
    status: COMMUNITY_SUBSCRIPTION_STATUSES.CURRENT,
    searchString,
    excludeEmails,
    excludePhoneNumbers,
  });
  const count = await CommunitySubscriptions.countDocuments(matchStage);
  return count;
};

module.exports = {
  getFalsePositiveUnsubscribedEmails,
  buildSubscriptionWithLearnerPipeline,
  buildSubscriptionMatchStage,
  countSubscribedMembers,
};
