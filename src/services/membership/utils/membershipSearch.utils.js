const ObjectId = require('mongoose').Types.ObjectId;
const { DateTime } = require('luxon');

const Segment = require('../../../models/membership/segment.model');
const Membership = require('../../../models/membership/membership.model');

const { getConfigByTypeFromCache } = require('../../config.service');
const {
  CONFIG_TYPES,
  CHAT_PLATFORMS,
} = require('../../../constants/common');
const { aclRoles } = require('../../../communitiesAPI/constants');

const {
  MEMBERSHIP_STATUS,
  OTHER_FILTERING_FIELDS,
  ARRAY_FILTERING_FIELDS,
  FILTERING_FIELDS,
} = require('../constants');

const RegexUtils = require('../../../utils/regex.util');

const transformOtherFiltersFromQueryParams = (queryParams) => {
  const otherFilters = {};
  for (const filter of OTHER_FILTERING_FIELDS) {
    if (
      Object.keys(queryParams).includes(filter) &&
      queryParams[filter] !== ''
    ) {
      const processedKey = filter.replace(/_/g, '.');
      otherFilters[processedKey] = queryParams[filter];
      if (
        filter ===
        FILTERING_FIELDS.ABANDONED_CHECKOUT_PRODUCTS_NOT_PURCHASED_ENTITY_OBJECTID
      ) {
        if (Array.isArray(queryParams[filter])) {
          if (queryParams[filter][0] === 'All') {
            otherFilters[processedKey] = 'All';
          } else {
            const newArray = queryParams[filter].map(
              (i) => new ObjectId(i)
            );
            otherFilters[processedKey] = newArray;
          }
        }
      }
    }
  }
  return otherFilters;
};

function processQueryParamValue(value) {
  const numericValue = Number(value);
  if (!Number.isNaN(numericValue)) {
    return numericValue;
  }
  if (value.toLowerCase() === 'true') {
    return true;
  }
  if (value.toLowerCase() === 'false') {
    return false;
  }
  return value;
}

const buildSearchStringCondition = ({
  searchString,
  searchIn = ['email', 'name', 'phoneNumber'],
}) => {
  const path = [];
  const searchWithEscapedRegexSign = RegexUtils.escapeRegExp(searchString);
  if (searchIn.includes('email')) {
    const config = getConfigByTypeFromCache(
      CONFIG_TYPES.LEARN_PORTAL_BACKEND_ENV_CONFIG_TYPE
    );
    // eslint-disable-next-line no-unused-vars
    const minGram = config?.envVarData?.MEMBERSHIP_SEARCH_MIN_GRAM || 3;
    const maxGram = config?.envVarData?.MEMBERSHIP_SEARCH_MAX_GRAM || 15;

    if (searchWithEscapedRegexSign.length > maxGram) {
      path.push({ value: 'email', multi: 'keywordAnalyzer' });
    } else {
      path.push('email');
    }
  }
  if (searchIn.includes('name')) {
    path.push('name');
  }
  if (searchIn.includes('phoneNumber')) {
    path.push('phoneNumber');
  }
  return {
    regex: {
      query: searchWithEscapedRegexSign,
      path,
      allowAnalyzedField: true,
    },
  };
};

const retrieveLearnerObjectIdViaSearch = async (
  search,
  communityObjectId
) => {
  const config = await getConfigByTypeFromCache(
    CONFIG_TYPES.LEARN_PORTAL_BACKEND_ENV_CONFIG_TYPE
  );

  const maxGram = config?.envVarData?.MEMBERSHIP_SEARCH_MAX_GRAM || 15;

  let path;
  if (search.length > maxGram) {
    path = [{ value: 'email', multi: 'keywordAnalyzer' }, 'name'];
  } else {
    path = ['name', 'email'];
  }

  const memberships = await Membership.aggregate([
    {
      $search: {
        index: 'membershipIndex',
        compound: {
          must: [
            {
              regex: {
                query: search,
                path,
                allowAnalyzedField: true,
              },
            },
          ],
          filter: [
            {
              equals: {
                path: 'communityObjectId',
                value: new ObjectId(communityObjectId),
              },
            },
          ],
        },
      },
    },
    {
      $project: {
        _id: 0,
        learnerObjectId: 1,
      },
    },
  ]);

  return memberships.map(({ learnerObjectId }) => learnerObjectId);
};

const getCommunityChatStatus = (
  bots = [],
  isWhatsappExperienceCommunity = false
) => {
  const results = {};
  const hasWhatsappBot = bots.find(
    (bot) => bot?.type === CHAT_PLATFORMS.WHATSAPP
  );
  if (hasWhatsappBot && isWhatsappExperienceCommunity) {
    results[CHAT_PLATFORMS.WHATSAPP] = true;
  } else {
    results[CHAT_PLATFORMS.WHATSAPP] = false;
  }
  return results;
};

function getChatGroupNotOnNasIOCondition(platform) {
  switch (platform) {
    case CHAT_PLATFORMS.WHATSAPP:
      return {
        compound: {
          filter: [
            {
              text: {
                query: MEMBERSHIP_STATUS.NOT_ON_NASIO,
                path: 'status',
              },
            },
            {
              equals: {
                value: true,
                path: 'whatsappInfo.isInChatGroup',
              },
            },
          ],
        },
      };
    default:
      break;
  }
}

function getUnsubscribedCondition() {
  return {
    compound: {
      filter: [
        {
          text: {
            query: MEMBERSHIP_STATUS.UNSUBSCRIBED,
            path: 'status',
          },
        },
        {
          range: {
            path: 'subscriptionInfo.unsubscribedInfo.subscriptionEndDate',
            lt: DateTime.utc(),
          },
        },
      ],
    },
  };
}

function getSubscribedCondition() {
  return {
    compound: {
      should: [
        {
          text: {
            query: MEMBERSHIP_STATUS.SUBSCRIBED,
            path: 'status',
          },
        },
        {
          compound: {
            filter: [
              {
                text: {
                  query: MEMBERSHIP_STATUS.UNSUBSCRIBED,
                  path: 'status',
                },
              },
              {
                range: {
                  path: 'subscriptionInfo.unsubscribedInfo.subscriptionEndDate',
                  gte: DateTime.utc(),
                },
              },
            ],
          },
        },
      ],
      minimumShouldMatch: 1,
    },
  };
}

const buildStatusCondition = ({
  status,
  bots = [],
  isWhatsappExperienceCommunity = false,
}) => {
  const conditions = [];
  for (const s of status) {
    switch (s) {
      case MEMBERSHIP_STATUS.NOT_ON_NASIO: {
        const chatStatus = getCommunityChatStatus(
          bots,
          isWhatsappExperienceCommunity
        );
        for (const [platform, value] of Object.entries(chatStatus)) {
          if (value) {
            const condition = getChatGroupNotOnNasIOCondition(platform);
            if (condition) {
              conditions.push(condition);
            }
          } else {
            conditions.push({
              text: {
                query: 'not_exist',
                path: 'status',
              },
            });
          }
        }
        break;
      }
      case MEMBERSHIP_STATUS.UNSUBSCRIBED:
        conditions.push(getUnsubscribedCondition());
        break;
      case MEMBERSHIP_STATUS.SUBSCRIBED:
        conditions.push(getSubscribedCondition());
        break;
      default:
        conditions.push({
          text: {
            query: s,
            path: 'status',
          },
        });
        break;
    }
  }

  return conditions;
};

const processDateConditionValue = ({ value, toEndOfDay = false }) => {
  let result;
  if (!(typeof value === 'string')) {
    return value;
  }
  if (value === 'NOW()') {
    result = DateTime.utc();
  } else if (value.startsWith('DAYS(')) {
    const days = parseInt(value.replace('DAYS(', '').replace(')', ''), 10);
    result = DateTime.utc().plus({ days });
  } else if (value.startsWith('MONTHS(')) {
    const months = parseInt(
      value.replace('MONTHS(', '').replace(')', ''),
      10
    );
    if (months === 0) {
      result = DateTime.utc().startOf('month');
    } else {
      result = DateTime.utc().minus({ months });
    }
  } else {
    result = DateTime.fromISO(value).toUTC();
  }
  if (toEndOfDay) {
    result = result.endOf('day');
  }
  return result;
};

const buildReachFiltersConditions = (reachFilters) => {
  const results = [];
  if (reachFilters.optOutFromEmail === false) {
    results.push({
      compound: {
        should: [
          {
            compound: {
              mustNot: {
                exists: {
                  path: 'reachInfo.optOutFromEmail',
                },
              },
            },
          },
          {
            equals: {
              value: false,
              path: 'reachInfo.optOutFromEmail',
            },
          },
        ],
        minimumShouldMatch: 1,
      },
    });
  }
  if (reachFilters.optOutFromEmail === true) {
    results.push({
      equals: {
        value: true,
        path: 'reachInfo.optOutFromEmail',
      },
    });
  }
  if (reachFilters.optOutFromWhatsapp === false) {
    results.push({
      compound: {
        should: [
          {
            compound: {
              mustNot: {
                exists: {
                  path: 'reachInfo.optOutFromWhatsapp',
                },
              },
            },
          },
          {
            equals: {
              value: false,
              path: 'reachInfo.optOutFromWhatsapp',
            },
          },
        ],
        minimumShouldMatch: 1,
      },
    });
  }
  if (reachFilters.optOutFromWhatsapp === true) {
    results.push({
      equals: {
        value: true,
        path: 'reachInfo.optOutFromWhatsapp',
      },
    });
  }
  return results;
};

const buildOtherFiltersConditions = (otherFilters) => {
  const filters = [];
  for (const [key, value] of Object.entries(otherFilters)) {
    if (key.endsWith('.from')) {
      filters.push({
        range: {
          gte: processDateConditionValue({ value, toEndOfDay: false }),
          path: key.replace('.from', ''),
        },
      });
    } else if (key.endsWith('.to')) {
      filters.push({
        range: {
          lte: processDateConditionValue({ value, toEndOfDay: true }),
          path: key.replace('.to', ''),
        },
      });
    } else if (value instanceof Array) {
      if (value.length > 0) {
        if (typeof value[0] === 'string') {
          filters.push({
            text: {
              query: value,
              path: key,
            },
          });
        } else {
          const conditions = {
            compound: {
              should: [],
              minimumShouldMatch: 1,
            },
          };
          value.forEach((v) => {
            conditions.compound.should.push({
              equals: {
                value: v,
                path: key,
              },
            });
          });
          filters.push(conditions);
        }
      }
    } else if (typeof value === 'string') {
      filters.push({
        text: {
          query: value,
          path: key,
        },
      });
    } else if (typeof value === 'boolean') {
      if (value === true) {
        filters.push({
          equals: {
            value: true,
            path: key,
          },
        });
      } else {
        filters.push({
          compound: {
            should: [
              {
                compound: {
                  mustNot: {
                    exists: {
                      path: key,
                    },
                  },
                },
              },
              {
                equals: {
                  value: false,
                  path: key,
                },
              },
            ],
            minimumShouldMatch: 1,
          },
        });
      }
    } else {
      filters.push({
        equals: {
          value,
          path: key,
        },
      });
    }
  }
  return filters;
};

const buildRoleCondition = (communityRole) => {
  const filter = [];
  const must = [];
  const should = [];
  const mustNot = [];

  if (
    communityRole === aclRoles.MEMBER ||
    (communityRole.length === 1 && communityRole[0] === aclRoles.MEMBER)
  ) {
    mustNot.push({
      text: {
        query: aclRoles.MANAGER,
        path: 'communityRole',
      },
    });
  } else {
    filter.push({
      text: {
        query: communityRole,
        path: 'communityRole',
      },
    });
  }
  return {
    should,
    filter,
    mustNot,
    must,
  };
};

const buildSortCondition = ({
  sortBy,
  sortOrder = 'asc',
  status,
  sortByAccountCompleteness,
  hasObjectIdRange = false,
}) => {
  if (hasObjectIdRange) {
    return {
      _id: sortOrder === 'asc' ? 1 : -1,
    };
  }

  const prioritizedSpecialSortConditions = {};
  if (sortByAccountCompleteness) {
    prioritizedSpecialSortConditions[
      'accountExtraInfo.hasCustomProfileImage'
    ] = -1;
    prioritizedSpecialSortConditions[
      'accountExtraInfo.hasSocialMediaLinks'
    ] = -1;
  }
  if (sortBy) {
    const processedSortBy = sortBy.replace(/_/g, '.');
    const processedSortOrder = sortOrder === 'asc' ? 1 : -1;
    return {
      ...prioritizedSpecialSortConditions,
      [processedSortBy]: processedSortOrder,
    };
  }
  if (
    !status.length ||
    status.length > 1 ||
    status.includes(MEMBERSHIP_STATUS.ALL)
  ) {
    return {
      ...prioritizedSpecialSortConditions,
      'subscriptionInfo.signUpDate': -1,
    };
  }
  const singleStatus = status[0];
  switch (singleStatus) {
    case MEMBERSHIP_STATUS.SUBSCRIBED:
      return {
        ...prioritizedSpecialSortConditions,
        'subscriptionInfo.signUpDate': -1,
      };
    case MEMBERSHIP_STATUS.UNSUBSCRIBED:
      return {
        ...prioritizedSpecialSortConditions,
        'subscriptionInfo.unsubscribedInfo.unsubscribedDate': -1,
      };
    case MEMBERSHIP_STATUS.PENDING_APPROVAL:
      return {
        ...prioritizedSpecialSortConditions,
        'applicationInfo.applicationSubmittedDate': -1,
      };
    case MEMBERSHIP_STATUS.REJECTED:
      return {
        ...prioritizedSpecialSortConditions,
        'applicationInfo.rejectedDate': -1,
      };
    case MEMBERSHIP_STATUS.REMOVED:
      return {
        ...prioritizedSpecialSortConditions,
        'subscriptionInfo.removalInfo.removalDate': -1,
      };
    case MEMBERSHIP_STATUS.INVITED:
      return {
        ...prioritizedSpecialSortConditions,
        'invitedInfo.invitedDate': -1,
      };
    case MEMBERSHIP_STATUS.LEAD:
      return {
        ...prioritizedSpecialSortConditions,
        'leadInfo.leadCreatedDate': -1,
      };
    default:
      return {
        ...prioritizedSpecialSortConditions,
        'subscriptionInfo.signUpDate': -1,
      };
  }
};

const buildAbandonedCheckoutCondition = () => {
  return {
    exists: {
      path: 'abandonedCheckoutInfo.firstAbandonedCheckout.entityObjectId',
    },
  };
};

const buildSearchStage = ({
  extraConditions = {},
  community,
  searchString,
  searchIn = ['email', 'name', 'phoneNumber'],
  communityRole = [],
  status = [],
  reachFilters,
  otherFilters,
  sortBy,
  sortOrder,
  returnStoredSource = true,
  sortByAccountCompleteness = false,
  startObjectId = null,
  endObjectId = null,
}) => {
  const hasObjectIdRange = !!startObjectId && !!endObjectId;

  const { mustNot = [], filter = [] } = extraConditions || {};

  const searchStage = {
    index: 'membershipIndex',
    compound: {
      mustNot: [...mustNot],
      must: [],
      should: [],
      filter: [
        {
          equals: {
            path: 'communityObjectId',
            value: community._id,
          },
        },
        ...(hasObjectIdRange
          ? [
              {
                range: {
                  gte: new ObjectId(startObjectId),
                  lte: new ObjectId(endObjectId),
                  path: '_id',
                },
              },
            ]
          : []),
        ...filter,
      ],
      minimumShouldMatch: 1,
    },
    returnStoredSource,
  };
  if (!(status.includes(MEMBERSHIP_STATUS.ALL) || status.length === 0)) {
    const conditions = buildStatusCondition({
      bots: community.bots,
      isWhatsappExperienceCommunity:
        community.isWhatsappExperienceCommunity,
      status,
    });
    searchStage.compound.should.push(...conditions);
  }
  if (searchString) {
    searchStage.compound.must.push(
      buildSearchStringCondition({
        searchString,
        searchIn,
      })
    );
  }

  if (communityRole?.length) {
    const roleConditions = buildRoleCondition(communityRole);
    if (roleConditions.should.length) {
      searchStage.compound.should.push(...roleConditions.should);
    }
    if (roleConditions.filter.length) {
      searchStage.compound.filter.push(...roleConditions.filter);
    }
    if (roleConditions.must.length) {
      searchStage.compound.must.push(...roleConditions.must);
    }
    if (roleConditions.mustNot.length) {
      searchStage.compound.mustNot.push(...roleConditions.mustNot);
    }
  }

  if (reachFilters) {
    searchStage.compound.filter.push(
      ...buildReachFiltersConditions(reachFilters)
    );
  }
  if (otherFilters) {
    const abandonedCheckoutFilter =
      FILTERING_FIELDS.ABANDONED_CHECKOUT_PRODUCTS_NOT_PURCHASED_ENTITY_OBJECTID.replace(
        /_/g,
        '.'
      );
    const filters = { ...otherFilters };
    if (otherFilters[abandonedCheckoutFilter]) {
      searchStage.compound.must.push(buildAbandonedCheckoutCondition());
      if (otherFilters[abandonedCheckoutFilter] === 'All') {
        // eslint-disable-next-line no-param-reassign
        delete filters[abandonedCheckoutFilter];
      }
    }
    searchStage.compound.filter.push(
      ...buildOtherFiltersConditions(filters)
    );
  }
  searchStage.sort = buildSortCondition({
    sortBy,
    sortOrder,
    status,
    sortByAccountCompleteness,
    hasObjectIdRange,
  });

  if (searchStage.compound.should.length === 0) {
    delete searchStage.compound.should;
    delete searchStage.compound.minimumShouldMatch;
  }

  return searchStage;
};

const buildSearchPipeline = ({
  community,
  searchString,
  searchIn,
  communityRole,
  status,
  otherFilters,
  reachFilters,
  projection = {
    primaryIdentifier: 0,
    lastChangeStreamEventIds: 0,
    lastChangeStreamEventDates: 0,
  },
  sortBy,
  sortOrder,
  skip,
  limit,
  returnStoredSource,
  extraConditions,
  sortByAccountCompleteness = false,
  startObjectId = null,
  endObjectId = null,
}) => {
  const pipeline = [
    {
      $search: buildSearchStage({
        extraConditions,
        community,
        searchString,
        searchIn,
        communityRole,
        status,
        otherFilters,
        reachFilters,
        sortBy,
        sortOrder,
        returnStoredSource,
        sortByAccountCompleteness,
        startObjectId,
        endObjectId,
      }),
    },
    {
      $project: projection,
    },
  ];

  const hasObjectIdRange = !!startObjectId && !!endObjectId;
  if (hasObjectIdRange) {
    return pipeline;
  }

  if (skip) {
    pipeline.push({
      $skip: skip,
    });
  }
  if (limit) {
    pipeline.push({
      $limit: limit,
    });
  }
  return pipeline;
};

const buildSearchMetaPipeline = ({
  extraConditions,
  community,
  searchString,
  searchIn,
  communityRole,
  status,
  reachFilters,
  otherFilters,
  startObjectId = null,
  endObjectId = null,
}) => {
  const pipeline = [
    {
      $searchMeta: {
        ...buildSearchStage({
          extraConditions,
          community,
          searchString,
          searchIn,
          communityRole,
          status,
          reachFilters,
          otherFilters,
          startObjectId,
          endObjectId,
        }),
        count: {
          type: 'total',
        },
      },
    },
  ];
  return pipeline;
};

const processSegmentOtherFilters = ({ otherFilters }) => {
  const results = transformOtherFiltersFromQueryParams(otherFilters);
  for (const [key, value] of Object.entries(results)) {
    if (key.endsWith('.from')) {
      results[key] = processDateConditionValue({
        value,
        toEndOfDay: false,
      });
    } else if (key.endsWith('.to')) {
      results[key] = processDateConditionValue({
        value,
        toEndOfDay: true,
      });
    } else {
      results[key] = value;
    }
  }
  return results;
};

const processGetMembersQueryParams = async (requestQuery) => {
  let queryParams = {};
  let otherFilters;

  // eslint-disable-next-line no-param-reassign
  delete requestQuery.excludeFilters;

  if (requestQuery.segment || requestQuery.bucketName) {
    let query = { name: requestQuery.segment };
    if (requestQuery.bucketName) {
      query = { bucketName: requestQuery.bucketName };
    }
    const segment = await Segment.findOne(query);
    if (segment?.filters) {
      queryParams = {
        status: segment.filters.status,
        role: segment.filters.role,
        searchString: segment.filters.searchString,
      };
      if (segment.filters.otherFilters) {
        otherFilters = processSegmentOtherFilters({
          otherFilters: segment.filters.otherFilters,
        });
      }
    }
  } else {
    Object.keys(requestQuery).forEach((key) => {
      if (
        ARRAY_FILTERING_FIELDS.indexOf(key) !== -1 &&
        requestQuery[key] !== ''
      ) {
        const values = requestQuery[key].split(',');
        queryParams[key] = [];
        values.forEach((value) => {
          if (value !== '') {
            queryParams[key].push(processQueryParamValue(value.trim()));
          }
        });
      } else if (requestQuery[key] !== '') {
        queryParams[key] = processQueryParamValue(
          requestQuery[key].trim()
        );
      }
    });
    otherFilters = transformOtherFiltersFromQueryParams(queryParams);
  }

  return {
    queryParams,
    otherFilters,
  };
};

module.exports = {
  getCommunityChatStatus,
  buildSearchPipeline,
  buildSearchMetaPipeline,
  transformOtherFiltersFromQueryParams,
  processGetMembersQueryParams,
  processDateConditionValue,
  processSegmentOtherFilters,
  retrieveLearnerObjectIdViaSearch,
};
