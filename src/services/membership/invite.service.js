/* eslint-disable no-await-in-loop */
const { DateTime } = require('luxon');
const bcrypt = require('bcrypt');
const ObjectId = require('mongoose').Types.ObjectId;
const axios = require('../../clients/axios.client');

const Community = require('../../communitiesAPI/models/community.model');
const EmailUnsubscribe = require('../../communitiesAPI/models/emailUnsubscribe.model');
const Membership = require('../../models/membership/membership.model');

const logger = require('../logger.service');
const { getLearner } = require('../learner.service');
const {
  learnerPhoneNumberService,
} = require('../../communitiesAPI/services/common/learner');

const learnerUtils = require('../user/utils/learner.utils');
const membershipGetService = require('./get.service');
// eslint-disable-next-line no-unused-vars
const { countInvitedMembers } = require('./utils/nonMembers.utils');
const CommonService = require('../communityNotification/email/common.service');
const fraudService = require('../fraud');
const { getConfigByTypeFromCacheSync } = require('../config.service');

const { CONFIG_TYPES } = require('../../constants/common');
const { ToUserError, ParamError } = require('../../utils/error.util');

const { NAS_IO_FRONTEND_URL } = require('../../config');
const {
  NOTIFICATION_URL,
  NOTIFICATION_AUTH,
  MAIN_PAYMENT_BACKEND_URL,
} = require('../../config');
const {
  DEFAULT_COMMUNITY_HOST_PROFILE_IMAGE,
  // eslint-disable-next-line no-unused-vars
  DEFAULT_MAX_INVITE_COUNT,
  BATCH_METADATA_MODEL_TYPE,
} = require('../../constants/common');
const { MEMBERSHIP_STATUS } = require('./constants');
const {
  MEMBERSHIP_ERROR,
  GENERIC_ERROR,
  FEATURE_PERMISSION_ERROR,
} = require('../../constants/errorCode');
const { LEARN_BACKEND_URL } = require('../../config/index');
const batchMetadataService = require('../batchMetadata');
const membershipUsageService = require('../featurePermissions/membershipUsage.service');

const sendEmails = async (emailData, authorLanguagePreference) => {
  try {
    const data = {
      mailType: 'COMMUNITY_MEMBERS_INVITE_NEW',
      mailSubject: `${
        emailData[0]?.invitedByName ?? emailData[0]?.invitedByEmail
      } invited you to join ${emailData[0]?.communityName}`,
      mailCourse: emailData?.communityCode,
      mailCourseOffer: '',
      toMail: [],
      toMailName: [],
      requesterServiceName: 'App.Nas.io',
      authorLanguagePreference,
      data: emailData,
    };
    const response = await axios.post(
      `${NOTIFICATION_URL}/api/v1/send-bulk-email`,
      data,
      {
        headers: {
          Authorization: `Bearer ${NOTIFICATION_AUTH}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    logger.error(
      'Error on axios post request to send invite email: ',
      error,
      error?.response?.data
    );
  }
};

function getMaxInvite({ memberCount }) {
  if (memberCount <= 50) {
    return 100;
  }
  if (memberCount <= 100) {
    return 1000;
  }
  if (memberCount <= 1000) {
    return 10000;
  }
  return 100000;
}

async function checkMaxInvite({ community, newInviteCount }) {
  const [currentInviteCount, currentMemberCount] = await Promise.all([
    membershipGetService.countCommunityMembersBySearchBasic({
      community,
      status: [MEMBERSHIP_STATUS.INVITED],
    }),
    membershipGetService.countCommunityMembersBySearchBasic({
      community,
      status: [MEMBERSHIP_STATUS.SUBSCRIBED],
    }),
  ]);
  let maxInviteCount = getMaxInvite({
    memberCount: currentMemberCount,
  });
  if (community.memberManagementConfig?.maxInviteCount) {
    maxInviteCount = community.memberManagementConfig.maxInviteCount;
  }

  logger.info(
    `Checking max invite`,
    `communityCode=${community.code}`,
    `currentMemberCount=${currentMemberCount}`,
    `currentInviteCount=${currentInviteCount}`,
    `newInviteCount=${newInviteCount}`,
    `communityLimit=${maxInviteCount}`
  );

  if (currentInviteCount + newInviteCount > maxInviteCount) {
    throw new ToUserError(
      'Max invite limit reached. <NAME_EMAIL> for more information.',
      MEMBERSHIP_ERROR.INVITE_LIMIT_REACHED,
      {
        currentInviteCount,
        newInviteCount,
        maxInviteCount,
      }
    );
  }
}

const getOptOutEmailsByCommunity = async (communityObjectId) => {
  return EmailUnsubscribe.find({ communityObjectId }).distinct('email');
};

async function getOptOutLink({ communtiyCode, email }) {
  const optOutLinkToken = await bcrypt.hash(
    `email: ${email}, communityCode: ${communtiyCode}`,
    10
  );
  const optOutLink = `${MAIN_PAYMENT_BACKEND_URL}/api/v1/email/unsubscribe?email=${encodeURIComponent(
    email
  )}&communityCode=${communtiyCode}&token=${optOutLinkToken}`;
  return optOutLink;
}

const sendInviteEmails = async (params) => {
  const {
    community,
    invitedBy,
    upsertedMembershipIds,
    authorLanguagePreference,
    fromPendingInvite = false,
  } = params;

  let sender = invitedBy;
  if (!invitedBy) {
    sender = await CommonService.retrieveCommunityOwnerInfo(
      community.code
    );
  }

  const optOutEmail = await getOptOutEmailsByCommunity(community._id);
  let description = community?.description ?? '';
  description = description.replace(/\n/g, '<br/>');

  const chunkSize = 10;
  for (let i = 0; i < upsertedMembershipIds.length; i += chunkSize) {
    try {
      const chunkedIds = upsertedMembershipIds.slice(i, i + chunkSize);
      const emailData = [];
      const memberships = await Membership.find(
        {
          _id: { $in: chunkedIds },
        },
        { email: 1, name: 1 }
      );
      for (const membership of memberships) {
        const email = membership.email;
        if (!optOutEmail.includes(email)) {
          let recipientName = membership.name;
          if (!recipientName) {
            recipientName = email.split('@')[0];
          }
          const optOutLink = await getOptOutLink({
            communtiyCode: community?.code,
            email,
          });

          emailData.push({
            communityName: community.title,
            communityCode: community.code,
            community_host: community.By,
            host_profile_image:
              sender.profileImage ?? DEFAULT_COMMUNITY_HOST_PROFILE_IMAGE,
            communityDescription: description,
            invitedByName:
              sender.name ??
              `${sender.firstName ?? ''} ${sender.lastName ?? ''}`.trim(),
            invitedByEmail: sender.email,
            communityLink: `${NAS_IO_FRONTEND_URL}${community?.link}`,
            email,
            name: recipientName,
            unsubscribed_link: optOutLink,
          });
        }
      }
      // eslint-disable-next-line no-unused-vars
      const response = await sendEmails(
        emailData,
        authorLanguagePreference
      );
      if (fromPendingInvite && response) {
        const filters = {
          communityCode: community.code,
          _id: { $in: upsertedMembershipIds },
        };

        const updateQuery = {
          $set: {
            'invitedInfo.pendingInviteEmail': false,
          },
        };

        const result = await Membership.updateMany(filters, updateQuery);
        logger.info('Members invited info successfully updated', result);
      }
    } catch (error) {
      logger.error(
        'Error in sending invite email',
        `chunkIndex=${i}`,
        error,
        error.stack
      );
    }
  }
};

const validateAndExtractData = async ({ communityId, user, data }) => {
  const community = await Community.findOne(
    {
      _id: new ObjectId(communityId),
    },
    {
      code: 1,
      title: 1,
      link: 1,
      description: 1,
      By: 1,
      memberManagementConfig: 1,
      config: 1,
      createdBy: 1,
      restrictedInfo: 1,
    }
  ).lean();
  if (!community) {
    throw new ParamError('Community Not found');
  }

  const userLearner = await getLearner(
    {
      _id: new ObjectId(user?.learner?._id),
    },
    {
      email: 1,
      firstName: 1,
      lastName: 1,
      languagePreference: 1,
      profileImage: 1,
    }
  );

  if (!userLearner) {
    throw new ParamError("User's Learner Not found!");
  }

  if (!data || data?.length === 0) {
    throw new ParamError('No emails provided');
  }

  const filteredRows = [];
  const deduplicateEmailSet = new Set();

  for (const row of data) {
    if (row?.email) {
      const email = row?.email?.toLowerCase().trim();
      if (!deduplicateEmailSet.has(email)) {
        deduplicateEmailSet.add(email);

        const filteredRow = { email };
        if (row.phoneNumber) {
          try {
            const phoneNumber =
              learnerPhoneNumberService.getCleanedPhoneNumber(
                row.phoneNumber
              );
            if (phoneNumber) {
              filteredRow.phoneNumber = phoneNumber;
            }
          } catch (e) {
            logger.info(`Phone number not valid ${row.phoneNumber}`, e);
          }
        }
        if (row.firstName || row.lastName) {
          const name = learnerUtils.getFullName({
            firstName: row.firstName,
            lastName: row.lastName,
          });
          filteredRow.name = name;
        }
        filteredRows.push(filteredRow);
      }
    }
  }

  return {
    community,
    userLearner,
    filteredRows,
  };
};

const checkCommunityEmailPreference = async (communityData) => {
  let toSendInviteEmail = true;
  if (communityData.config?.preferences?.emailPreference) {
    const emailPreference =
      communityData.config?.preferences?.emailPreference ?? [];
    emailPreference.forEach((mailPref) => {
      if (
        mailPref.mailType === 'COMMUNITY_MEMBERS_INVITE_NEW' &&
        !mailPref.isEnabled
      ) {
        toSendInviteEmail = false;
      }
    });
  }
  return toSendInviteEmail;
};

async function createInvitedMemberships({ community, rows, isFraud }) {
  const upsertOperations = Membership.collection.initializeOrderedBulkOp();
  const updateOperations =
    Membership.collection.initializeUnorderedBulkOp();
  for (const row of rows) {
    const { email, phoneNumber, name } = row;
    const upsertFilters = {
      communityObjectId: community._id,
      communityCode: community.code,
      email,
      primaryIdentifier: 'email',
    };
    const updateFilters = {
      communityObjectId: community._id,
      communityCode: community.code,
      email,
      status: MEMBERSHIP_STATUS.INVITED,
      primaryIdentifier: 'email',
    };
    const upsertSet = {
      status: MEMBERSHIP_STATUS.INVITED,
      invitedInfo: {
        invitedDate: DateTime.utc(),
        pendingInviteEmail: isFraud,
      },
    };
    const updateSet = {};
    if (phoneNumber) {
      upsertSet.phoneNumber = phoneNumber;
      updateSet.phoneNumber = phoneNumber;
    }
    if (name) {
      upsertSet.name = name;
      updateSet.name = name;
    }
    upsertOperations
      .find(upsertFilters)
      .upsert()
      .updateOne({ $setOnInsert: upsertSet });

    updateOperations.find(updateFilters).updateOne({ $set: updateSet });
  }
  const updateResults = await updateOperations.execute();
  const upsertResults = await upsertOperations.execute();

  const upsertedIds = Object.values(upsertResults.upsertedIds);

  await Promise.all(
    upsertedIds.map(async (upsertedId) =>
      batchMetadataService.add({
        batchMetadataModelType: BATCH_METADATA_MODEL_TYPE.MEMBERSHIP,
        entityObjectId: community._id,
        communityObjectId: community._id,
        community,
        addedObjectId: upsertedId,
      })
    )
  );

  return {
    updateResults,
    upsertResults,
  };
}

function needReviewOnFraud() {
  const config = getConfigByTypeFromCacheSync(
    CONFIG_TYPES.LEARN_PORTAL_BACKEND_ENV_CONFIG_TYPE
  );
  const restrictionToReviewMap =
    config?.envVarData?.RESTRICTION_TO_REVIEW_MAP;
  const restriction = fraudService.RESTRICTIONS.INVITE;
  if (!restrictionToReviewMap || !restrictionToReviewMap[restriction]) {
    return false;
  }
  return restrictionToReviewMap[restriction];
}

async function checkInviteForFraudNoReview({ community, inviteCount }) {
  const fraudEngine = new fraudService.FraudEngine({
    communityId: community._id,
    eventName: fraudService.INTERESTED_EVENTS.INVITE,
    entityType: 'community',
    entityId: community._id,
    data: {
      content: JSON.stringify(community.restrictedInfo),
      contentSource: 'community restricted information',
      restriction: 'invite',
      extraData: {
        inviteCount,
      },
    },
    checksToPerform: [fraudService.COMMON_FRAUD_CHECKS.RESTRICTION],
    autoConsequencesToApply: [],
  });
  try {
    const fraudResult = await fraudEngine.performCheck();
    return fraudResult.overallOutcome === fraudService.OUTCOMES.DETECTED;
  } catch (error) {
    logger.error('Error in fraud check:', error.message, error.stack);
  }
  return false;
}

async function checkInviteForFraudWithReview({
  community,
  inviteCount,
  toSendInviteEmail,
  user,
  userLearner,
}) {
  const fraudEngine = new fraudService.FraudEngine({
    communityId: community._id,
    eventName: fraudService.INTERESTED_EVENTS.INVITE,
    entityType: 'community',
    entityId: community._id,
    data: {
      content: JSON.stringify(community.restrictedInfo),
      contentSource: 'community restricted information',
      restriction: 'invite',
      extraData: {
        inviteCount,
      },
    },
    checksToPerform: [fraudService.COMMON_FRAUD_CHECKS.RESTRICTION],
    needReviewOnDetect: true,
    autoConsequencesToApply: [],
    callbackOnApprove: {
      url: `${LEARN_BACKEND_URL}api/v1/communities/resume-invite`,
      body: {
        communityData: community,
        user,
        userLearner,
        toSendInviteEmail,
      },
    },
  });
  try {
    const fraudResult = await fraudEngine.performCheck();
    return fraudResult.overallOutcome === fraudService.OUTCOMES.DETECTED;
  } catch (error) {
    logger.error('Error in fraud check:', error.message, error.stack);
  }
  return false;
}

const inviteMembers = async (params) => {
  if (Object.keys(params).length === 0 && params.constructor === Object) {
    throw new Error('Empty Params');
  }
  const { data, communityId, user } = params;
  const { community, userLearner, filteredRows } =
    await validateAndExtractData({
      communityId,
      user,
      data,
    });
  let toSendInviteEmail = params.sendEmail;
  if (toSendInviteEmail === undefined) {
    toSendInviteEmail = await checkCommunityEmailPreference(community);
  }
  const inviteCount = filteredRows.length;
  const limitCheck = await membershipUsageService.checkAddMemberLimit(
    communityId,
    inviteCount
  );
  if (!limitCheck.allowed) {
    throw new ToUserError(
      limitCheck.message,
      FEATURE_PERMISSION_ERROR[limitCheck.error]
    );
  }
  await checkMaxInvite({
    community,
    newInviteCount: inviteCount,
  });

  const reviewOnFraud = needReviewOnFraud();
  let isFraudDetected = false;
  if (reviewOnFraud === true) {
    isFraudDetected = await checkInviteForFraudWithReview({
      community,
      inviteCount,
      toSendInviteEmail,
      user,
      userLearner,
    });
  } else {
    isFraudDetected = await checkInviteForFraudNoReview({
      community,
      inviteCount,
      toSendInviteEmail,
      user,
      userLearner,
    });
    if (isFraudDetected) {
      throw new ToUserError(
        'Your action cannot be fulfilled. Please verify your identity <NAME_EMAIL> for assistance.',
        GENERIC_ERROR.RISKY_ACTION_ERROR
      );
    }
  }

  const { upsertResults, updateResults } = await createInvitedMemberships({
    community,
    rows: filteredRows,
    isFraud: isFraudDetected,
  });

  const results = {
    rowsReceived: data.length,
    newlyInvitedUsersCount: upsertResults.upsertedCount,
    invitesSentCount: upsertResults.upsertedCount,
    existingMembersCount: upsertResults.matchedCount,
    alreadyInvitedUsersCount: updateResults.matchedCount,
  };

  if (!isFraudDetected) {
    if (toSendInviteEmail) {
      sendInviteEmails({
        community,
        invitedBy: userLearner,
        upsertedMembershipIds: Object.values(upsertResults.upsertedIds),
        authorLanguagePreference: userLearner?.languagePreference,
      });
    }
  }
  return results;
};

const sendPendingInviteEmail = async ({
  communityData,
  userLearner,
  toSendInviteEmail,
}) => {
  const community = communityData;
  const communityId = community._id;
  const communityCode = community.code;
  const upsertedMembershipIds = await Membership.find(
    {
      'invitedInfo.pendingInviteEmail': true,
      communityCode,
    },
    { _id: 1 }
  );
  const authorLanguagePreference = userLearner?.languagePreference;
  if (toSendInviteEmail) {
    sendInviteEmails({
      communityId,
      community,
      invitedBy: userLearner,
      upsertedMembershipIds,
      authorLanguagePreference,
      fromPendingInvite: true,
    });
  }
};

module.exports = {
  inviteMembers,
  sendPendingInviteEmail,
};
