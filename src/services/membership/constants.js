const MEMBERSHIP_STATUS = {
  ALL: 'all',
  LEAD: 'lead',
  INVITED: 'invited',
  SUBSCRIBED: 'subscribed',
  UNSUBSCRIBED: 'unsubscribed',
  PENDING_APPROVAL: 'pending_approval',
  REJECTED: 'rejected',
  REMOVED: 'removed',
  DELETED: 'deleted',
  NOT_ON_NASIO: 'not_on_nasio',
  CHAT_VISITOR: 'chat_visitor',
};

const MEMBERSHIP_PAYMENT_STATUS = {
  INCOMPLETE: 'incomplete',
  SUCCESS: 'success',
  FAILED: 'failed',
  RENEWAL_FAILED: 'renewal_failed',
  CANCELLED: 'cancelled',
  REFUNDED: 'refunded',
  REFUNDED_FAILED: 'refunded_failed',
  FREE_TRIAL: 'free_trial',
};

const MEMBERSHIP_PAYMENT_TYPE = {
  FIRST_TIME: 'first_time',
  RENEWAL: 'renewal',
};

const MEMBERSHIP_TYPE = {
  PAID: 'paid',
  FREE: 'free',
};

const MEMBERSHIP_DISCOUNT_TYPE = {
  PERCENTAGE: 'percentage',
};

const MEMBERSHIP_INTERVAL = {
  MONTH: 'month',
  YEAR: 'year',
};

const IDENTIFIERS = {
  EMAIL: 'email',
  PHONE_NUMBER: 'phoneNumber',
};

const MEMBERSHIP_COMMUNITY_ROLE = {
  MEMBER: 'member',
  MANAGER: 'manager',
  OWNER: 'owner',
  ADMIN: 'admin',
};

const COMMUNITY_TYPE = {
  FREE: 'free',
  PAID: 'paid',
  WHATSAPP: 'whatsapp',
  WITH_APPLICATION: 'with_application',
  WITHOUT_APPLICATION: 'without_application',
};

const FILTERING_FIELDS = {
  ABANDONED_CHECKOUT_PRODUCTS_NOT_PURCHASED_ENTITY_OBJECTID:
    'abandonedCheckoutInfo_productsNotPurchased_entityObjectId',
  SIGN_UP_DATE_FROM: 'subscriptionInfo_signUpDate_from',
  SIGN_UP_DATE_TO: 'subscriptionInfo_signUpDate_to',
  LAST_PAYMENT_STATUS: 'subscriptionInfo_paymentInfo_paymentStatus',
  LAST_FAILED_PAYMENT_TYPE:
    'subscriptionInfo_paymentInfo_lastFailedPaymentType',
  LAST_FAILED_PAYMENT_DATE_FROM:
    'subscriptionInfo_paymentInfo_lastFailedPaymentDate_from',
  LAST_FAILED_PAYMENT_DATE_TO:
    'subscriptionInfo_paymentInfo_lastFailedPaymentDate_to',
  LAST_SUCCESSFUL_PAYMENT_DATE_FROM:
    'subscriptionInfo_paymentInfo_lastSuccessfulPaymentDate_from',
  LAST_SUCCESSFUL_PAYMENT_DATE_TO:
    'subscriptionInfo_paymentInfo_lastSuccessfulPaymentDate_to',
  PAYMENT_INTERVAL: 'subscriptionInfo_paymentInfo_paymentIntervalString',
  NEXT_BILLING_DATE_FROM:
    'subscriptionInfo_paymentInfo_nextBillingDate_from',
  NEXT_BILLING_DATE_TO: 'subscriptionInfo_paymentInfo_nextBillingDate_to',
  UNSUBSCRIBED_DATE_FROM:
    'subscriptionInfo_unsubscribedInfo_unsubscribedDate_from',
  UNSUBSCRIBED_DATE_TO:
    'subscriptionInfo_unsubscribedInfo_unsubscribedDate_to',
  IS_IN_WHATSAPP_GROUP: 'whatsappInfo_isInChatGroup',
  DISCOUNT_CODE_USED_VALUE:
    'subscriptionInfo_paymentInfo_discountCodeUsedValue',
  DISCOUNT_CODE_USED_TYPE:
    'subscriptionInfo_paymentInfo_discountCodeUsedType',
  IS_FREE_TRIAL: 'subscriptionInfo_isFreeTrial',
  COUNTRY_ID: 'countryInfo_id',
  COUNTRY_CODE: 'countryInfo_code',
  COUNTRY_NAME: 'countryInfo_name',
  STATUS: 'status',
  ROLE: 'role',
  SEGMENT: 'segment',
};

const ARRAY_FILTERING_FIELDS = [
  FILTERING_FIELDS.COUNTRY_ID,
  FILTERING_FIELDS.COUNTRY_CODE,
  FILTERING_FIELDS.COUNTRY_NAME,
  FILTERING_FIELDS.PAYMENT_INTERVAL,
  FILTERING_FIELDS.LAST_PAYMENT_STATUS,
  FILTERING_FIELDS.STATUS,
  FILTERING_FIELDS.ROLE,
  FILTERING_FIELDS.ABANDONED_CHECKOUT_PRODUCTS_NOT_PURCHASED_ENTITY_OBJECTID,
];

// these filters are for accepting the filters from front end
const OTHER_FILTERING_FIELDS = [
  FILTERING_FIELDS.SIGN_UP_DATE_FROM,
  FILTERING_FIELDS.SIGN_UP_DATE_TO,
  FILTERING_FIELDS.NEXT_BILLING_DATE_FROM,
  FILTERING_FIELDS.NEXT_BILLING_DATE_TO,
  FILTERING_FIELDS.UNSUBSCRIBED_DATE_FROM,
  FILTERING_FIELDS.UNSUBSCRIBED_DATE_TO,
  FILTERING_FIELDS.IS_IN_WHATSAPP_GROUP,
  FILTERING_FIELDS.PAYMENT_INTERVAL,
  FILTERING_FIELDS.COUNTRY_CODE,
  FILTERING_FIELDS.COUNTRY_ID,
  FILTERING_FIELDS.LAST_PAYMENT_STATUS,
  FILTERING_FIELDS.LAST_FAILED_PAYMENT_TYPE,
  FILTERING_FIELDS.LAST_FAILED_PAYMENT_DATE_FROM,
  FILTERING_FIELDS.LAST_FAILED_PAYMENT_DATE_TO,
  FILTERING_FIELDS.LAST_SUCCESSFUL_PAYMENT_DATE_FROM,
  FILTERING_FIELDS.LAST_SUCCESSFUL_PAYMENT_DATE_TO,
  FILTERING_FIELDS.IS_FREE_TRIAL,
  FILTERING_FIELDS.ABANDONED_CHECKOUT_PRODUCTS_NOT_PURCHASED_ENTITY_OBJECTID,
];

const SPECIAL_SORT_BY = {
  ACCOUNT_COMPLETENESS: 'accountCompleteness',
};

const INSERT_LOCK_TTL = 5000;

const MEMBERSHIP_STATUS_INCLUSIVE_FOR_COUNTRY_INCR = [
  MEMBERSHIP_STATUS.NOT_ON_NASIO,
  MEMBERSHIP_STATUS.SUBSCRIBED,
];

const COUNT_TYPES = {
  SUMMARY: 'summary',
  STATUS: 'status',
  PAYMENT_INTERVAL: 'payment_interval',
  LAST_30_DAYS: 'last_30_days',
};

module.exports = {
  MEMBERSHIP_STATUS_INCLUSIVE_FOR_COUNTRY_INCR,
  MEMBERSHIP_STATUS,
  MEMBERSHIP_PAYMENT_STATUS,
  MEMBERSHIP_TYPE,
  MEMBERSHIP_INTERVAL,
  IDENTIFIERS,
  MEMBERSHIP_COMMUNITY_ROLE,
  FILTERING_FIELDS,
  ARRAY_FILTERING_FIELDS,
  OTHER_FILTERING_FIELDS,
  MEMBERSHIP_DISCOUNT_TYPE,
  MEMBERSHIP_PAYMENT_TYPE,
  COMMUNITY_TYPE,
  SPECIAL_SORT_BY,
  INSERT_LOCK_TTL,
  COUNT_TYPES,
};
