const CommunityModel = require('../../communitiesAPI/models/community.model');
const CommunityPayoutAccountModel = require('../../communitiesAPI/models/communityPayoutAccount.model');
const { ParamError } = require('../../utils/error.util');
const PaymentBackendRpc = require('../../rpc/paymentBackend');
const {
  PAYMENT_PROVIDER,
  PAYOUT_GATEWAY,
} = require('../../constants/common');
const { COMMUNITY_PAYOUT_ACCOUNT_STATUS } = require('./constants');
const logger = require('../logger.service');
const { NAS_IO_FRONTEND_URL } = require('../../config');
const {
  getCommunityOwnerInfo,
} = require('../common/communityOwner.service');

exports.onboardStripeConnect = async ({
  communityId,
  refreshUrl,
  returnUrl,
}) => {
  const community = await CommunityModel.findById(communityId).lean();
  if (!community) {
    throw new ParamError(`Invalid community id: ${communityId}`);
  }
  const existingAccount = await CommunityPayoutAccountModel.findOne({
    communityObjectId: communityId,
    payoutGateway: PAYOUT_GATEWAY.STRIPE_US,
    status: {
      $nin: [
        // COMMUNITY_PAYOUT_ACCOUNT_STATUS.INIT,
        COMMUNITY_PAYOUT_ACCOUNT_STATUS.REJECTED,
      ],
    },
  }).lean();
  if (existingAccount) {
    throw new ParamError('Account existed');
  }

  const metadata = {
    communityCode: community.code,
    communityObjectId: communityId,
  };

  const owner = await getCommunityOwnerInfo(communityId);

  const paymentBackendRpc = new PaymentBackendRpc();
  await paymentBackendRpc.init();

  const result = await paymentBackendRpc.createStripeConnectAccount({
    email: owner.learner.email,
    communityName: community.title,
    communityLink: `${NAS_IO_FRONTEND_URL}${community.link}`,
    refreshUrl,
    returnUrl,
    metadata,
    paymentProvider: PAYOUT_GATEWAY.STRIPE_US,
  });

  const communityPayoutAccount = {
    communityObjectId: communityId,
    communityCode: community.code,
    payoutGateway: PAYOUT_GATEWAY.STRIPE_US,
    payoutGatewayAccountId: result.account.id,
    status: COMMUNITY_PAYOUT_ACCOUNT_STATUS.INIT,
  };

  const updateResult = await CommunityPayoutAccountModel.findOneAndUpdate(
    {
      communityObjectId: communityId,
      payoutGateway: PAYOUT_GATEWAY.STRIPE_US,
      status: {
        $in: [
          COMMUNITY_PAYOUT_ACCOUNT_STATUS.CONNECTED,
          COMMUNITY_PAYOUT_ACCOUNT_STATUS.PENDING_VERIFICATION,
          COMMUNITY_PAYOUT_ACCOUNT_STATUS.RESTRICTED,
        ],
      },
    },
    { $setOnInsert: communityPayoutAccount },
    {
      upsert: true,
      returnDocument: 'after',
    }
  ).lean();

  if (updateResult?.payoutGatewayAccountId !== result.account.id) {
    throw new ParamError('Account existed');
  }

  return {
    accountId: updateResult?._id,
    stripeAccountId: result.account.id,
    stripeAccountLink: result.accountLink.url,
  };
};

exports.getAccountLinkToUpdateStripeConnectAccount = async ({
  communityId,
  refreshUrl,
  returnUrl,
}) => {
  const community = await CommunityModel.findById(communityId).lean();
  if (!community) {
    throw new ParamError(`Invalid community id: ${communityId}`);
  }
  const existingAccount = await CommunityPayoutAccountModel.findOne({
    communityObjectId: communityId,
    payoutGateway: PAYOUT_GATEWAY.STRIPE_US,
    status: {
      $in: [
        COMMUNITY_PAYOUT_ACCOUNT_STATUS.INIT,
        COMMUNITY_PAYOUT_ACCOUNT_STATUS.CONNECTED,
        COMMUNITY_PAYOUT_ACCOUNT_STATUS.PENDING_VERIFICATION,
        COMMUNITY_PAYOUT_ACCOUNT_STATUS.RESTRICTED,
      ],
    },
  }).lean();
  if (!existingAccount) {
    throw new ParamError('Account not found');
  }

  const paymentBackendRpc = new PaymentBackendRpc();
  await paymentBackendRpc.init();

  const result = await paymentBackendRpc.getStripeConnectAccountLink({
    accountId: existingAccount.payoutGatewayAccountId,
    refreshUrl,
    returnUrl,
    paymentProvider: PAYMENT_PROVIDER.STRIPE_US,
  });

  return {
    stripeAccountLink: result.accountLink.url,
  };
};

exports.deleteStripeConnectAccount = async ({ communityId }) => {
  const existingAccount = await CommunityPayoutAccountModel.findOne({
    communityObjectId: communityId,
    payoutGateway: PAYOUT_GATEWAY.STRIPE_US,
    status: {
      $in: [
        COMMUNITY_PAYOUT_ACCOUNT_STATUS.INIT,
        COMMUNITY_PAYOUT_ACCOUNT_STATUS.CONNECTED,
        COMMUNITY_PAYOUT_ACCOUNT_STATUS.PENDING_VERIFICATION,
        COMMUNITY_PAYOUT_ACCOUNT_STATUS.RESTRICTED,
      ],
    },
  }).lean();
  if (!existingAccount) {
    throw new ParamError('Account not found');
  }

  const paymentBackendRpc = new PaymentBackendRpc();
  await paymentBackendRpc.init();

  const result = await paymentBackendRpc.rejectStripeConnectAccount({
    accountId: existingAccount.payoutGatewayAccountId,
    paymentProvider: PAYOUT_GATEWAY.STRIPE_US,
  });
  return result;
};

exports.createStripeConnectAccountSession = async ({ communityId }) => {
  const existingAccount = await CommunityPayoutAccountModel.findOne({
    communityObjectId: communityId,
    payoutGateway: PAYOUT_GATEWAY.STRIPE_US,
    status: {
      $in: [
        COMMUNITY_PAYOUT_ACCOUNT_STATUS.INIT,
        COMMUNITY_PAYOUT_ACCOUNT_STATUS.CONNECTED,
        COMMUNITY_PAYOUT_ACCOUNT_STATUS.PENDING_VERIFICATION,
        COMMUNITY_PAYOUT_ACCOUNT_STATUS.RESTRICTED,
      ],
    },
  }).lean();
  if (!existingAccount) {
    throw new ParamError('Account not found');
  }

  const paymentBackendRpc = new PaymentBackendRpc();
  await paymentBackendRpc.init();

  const result = await paymentBackendRpc.createStripeAccountSession({
    accountId: existingAccount.payoutGatewayAccountId,
    paymentProvider: PAYOUT_GATEWAY.STRIPE_US,
  });

  return {
    accountId: result.accountSession.account,
    clientSecret: result.accountSession.client_secret,
    expiresAt: new Date(result.accountSession.expires_at * 1000),
  };
};

exports.updateStripeConnectAccountByWebhookEvents = async ({
  communityId,
  stripeAccountId,
  accountDetails,
}) => {
  const externalAccount = await CommunityPayoutAccountModel.findOne({
    communityObjectId: communityId,
    payoutGatewayAccountId: stripeAccountId,
    payoutGateway: PAYMENT_PROVIDER.STRIPE_US,
  }).lean();

  if (
    !externalAccount ||
    externalAccount.status === COMMUNITY_PAYOUT_ACCOUNT_STATUS.REJECTED
  ) {
    logger.error('Cannot find the account in our database');
    return null;
  }

  let nextStatus = externalAccount.status;
  if (
    accountDetails.requirements?.disabled_reason &&
    accountDetails.requirements?.disabled_reason.startsWith('rejected.')
  ) {
    nextStatus = COMMUNITY_PAYOUT_ACCOUNT_STATUS.REJECTED;
  } else if (accountDetails.details_submitted) {
    if (
      accountDetails.requirements?.pending_verification &&
      accountDetails.requirements?.pending_verification.length > 0
    ) {
      nextStatus = COMMUNITY_PAYOUT_ACCOUNT_STATUS.PENDING_VERIFICATION;
    } else if (
      accountDetails.capabilities?.transfers !== 'active' ||
      !accountDetails?.payouts_enabled
    ) {
      nextStatus = COMMUNITY_PAYOUT_ACCOUNT_STATUS.RESTRICTED;
    } else {
      nextStatus = COMMUNITY_PAYOUT_ACCOUNT_STATUS.CONNECTED;
    }
  }

  // Check if any other connected account existed to prevent community has two connected account
  const otherExistedConnectAccount =
    await CommunityPayoutAccountModel.exists({
      communityObjectId: communityId,
      payoutGateway: PAYMENT_PROVIDER.STRIPE_US,
      status: COMMUNITY_PAYOUT_ACCOUNT_STATUS.CONNECTED,
      payoutGatewayAccountId: { $ne: stripeAccountId },
    });
  if (
    // here is to check the account status before we send request to stripe
    otherExistedConnectAccount &&
    nextStatus !== COMMUNITY_PAYOUT_ACCOUNT_STATUS.REJECTED
  ) {
    const paymentBackendRpc = new PaymentBackendRpc();
    await paymentBackendRpc.init();

    const result = await paymentBackendRpc.rejectStripeConnectAccount({
      accountId: stripeAccountId,
      paymentProvider: PAYOUT_GATEWAY.STRIPE_US,
    });
    return result;
  }

  await CommunityPayoutAccountModel.updateOne(
    {
      _id: externalAccount._id,
    },
    {
      status: nextStatus,
      payoutCurrency: accountDetails.default_currency.toUpperCase(),
      email: accountDetails.email,
      accountDetails,
    }
  );

  return externalAccount;
};
