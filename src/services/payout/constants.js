const PAYOUT_TYPE = {
  GENERAL: 'General',
  ADVANC<PERSON>: 'Advance',
};

const PAYOUT_STATUS = {
  PROCESSING: 'Processing',
  PAID: 'Paid',
  APPROVED: 'Approved',
  TRANSFERRED: 'Transferred',
  TRANSFER_FAILED: 'Transfer_Failed',
  PENDING_PAYOUT: 'Pending_Payout',
  PAYOUT_FAILED: 'Payout_Failed',
};

const PAYOUT_CHANNEL = {
  GLOBAL: 'GLOBAL',
  STRIPE_INDIA: 'STRIPE_INDIA',
  STRIPE_CONNECT: 'STRIPE_CONNECT',
};

const PAYOUT_ADJUSTMENT_TYPE = {
  ADJUSTMENT_ADD: 'ADJUSTMENT_ADD',
  ADJUSTMENT_DEDUCT: 'ADJUSTMENT_DEDUCT',
};

const PAYOUT_TARGET_TYPE = {
  COMMUNITY: 'COMMUNITY',
  MEMBER: 'MEMBER',
};

const COMMUNITY_PAYOUT_ACCOUNT_STATUS = {
  INIT: 'init',
  CONNECTED: 'connected',
  REJECTED: 'rejected',
  RESTRICTED: 'restricted',
  PENDING_VERIFICATION: 'pending_verification',
};

const COMMUNITY_PAYOUT_ACCOUNT_TYPE = {
  STRIPE_CONNECT: 'STRIPE_CONNECT',
  BANK_ACCOUNT: 'BANK_ACCOUNT',
};

const STRIPE_CONNECT_NOT_SUPPORTED_COUNTRIES = [
  'Brazil',
  'Andorra',
  'San Marino',
  'Vatican City',
  'Papua New Guinea',
  'Nicaragua',
];

module.exports = {
  PAYOUT_TYPE,
  PAYOUT_STATUS,
  PAYOUT_CHANNEL,
  PAYOUT_ADJUSTMENT_TYPE,
  PAYOUT_TARGET_TYPE,
  COMMUNITY_PAYOUT_ACCOUNT_STATUS,
  COMMUNITY_PAYOUT_ACCOUNT_TYPE,
  STRIPE_CONNECT_NOT_SUPPORTED_COUNTRIES,
};
