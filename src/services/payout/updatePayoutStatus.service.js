const { PAYOUT_TARGET_TYPE } = require('./constants');
const { ParamError } = require('../../utils/error.util');
const {
  updateCommunityPayout,
  updateCommunityPayoutByWebhook,
} = require('./updateCommunityPayout.service');
const { updateMemberPayout } = require('./updateMemberPayout.service');

exports.updatePayout = async (
  payoutIds,
  communityCodes,
  payoutTargetType,
  operator
) => {
  switch (payoutTargetType) {
    case PAYOUT_TARGET_TYPE.COMMUNITY:
      return updateCommunityPayout(payoutIds, communityCodes, operator);
    case PAYOUT_TARGET_TYPE.MEMBER:
      return updateMemberPayout(payoutIds, operator);
    default:
      throw new ParamError(
        `Invalid payout target type: ${payoutTargetType}`
      );
  }
};

exports.updatePayoutByWebhook = async ({
  payoutObjectId,
  status,
  failureCode,
  failureReason,
  stripePayoutStatus,
  payoutTargetType = PAYOUT_TARGET_TYPE.COMMUNITY,
  payoutDate,
}) => {
  switch (payoutTargetType) {
    case PAYOUT_TARGET_TYPE.COMMUNITY:
      return updateCommunityPayoutByWebhook(
        payoutObjectId,
        status,
        failureCode,
        failureReason,
        stripePayoutStatus,
        payoutDate
      );

    default:
      throw new ParamError(
        `Invalid payout target type: ${payoutTargetType}`
      );
  }
};
