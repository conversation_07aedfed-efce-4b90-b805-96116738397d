const logger = require('../logger.service');
const MemberPayoutModel = require('../../models/memberPayout/memberPayout.model');
const LearnerModel = require('../../models/learners.model');
const { PAYOUT_STATUS } = require('./constants');
const { NOTIFICATION_URL, NOTIFICATION_AUTH } = require('../../config');
const axios = require('../../clients/axios.client');
const { NAS_IO_FRONTEND_URL } = require('../../config');
const { getName } = require('../../utils/name.util');
const { PAYOUT_MAIL_TYPES } = require('../mail/constants');

const updateMemberPayoutToPaid = async ({ payoutId, operator }) => {
  await MemberPayoutModel.updateOne(
    {
      _id: payoutId,
      status: PAYOUT_STATUS.PROCESSING,
    },
    {
      status: PAYOUT_STATUS.PAID,
      payoutDate: new Date(),
      operator,
    }
  );

  const payout = await MemberPayoutModel.findById(payoutId).lean();
  if (!payout) {
    throw new Error('No payout found');
  }

  // Get learner mail
  const learner = await LearnerModel.findById(
    payout.learnerObjectId
  ).lean();

  const learnerName = getName(
    learner.firstName,
    learner.lastName,
    learner.email
  );

  const payoutAmount = payout.payoutAmountInLocalCurrency / 100;
  const payoutAmountDisplay = `${
    payout.payoutCurrency
  } ${payoutAmount.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })}`;

  const payoutMonth = payout.payoutTitle.slice(0, -4);

  const mailContent = {
    payout_month: payoutMonth,
    payout_month_and_year: payout.payoutTitle,
    payout_amount: payoutAmountDisplay,
    bank_account_name: payout.bankAccountInfo.accountName,
    bank_account_number: payout.bankAccountInfo.accountNumber,
    iban_number: !payout.bankAccountInfo.iban
      ? '--'
      : payout.bankAccountInfo.iban,
    payout_url: `${NAS_IO_FRONTEND_URL}/user/affiliate/payout`,
  };

  const data = {
    mailType: PAYOUT_MAIL_TYPES.MEMBER_PAYOUT_NOTIFICATION,
    mailCourse: 'All',
    toMail: [learner.email],
    toMailName: [learnerName ?? ''],
    requesterServiceName: 'LPBE',
    data: mailContent,
  };
  await axios.post(`${NOTIFICATION_URL}/api/v1/send-email`, data, {
    headers: {
      Authorization: `Bearer ${NOTIFICATION_AUTH}`,
    },
  });
};

exports.updateMemberPayout = async (payoutIds, operator) => {
  const resultList = [];
  if (payoutIds) {
    await Promise.all(
      payoutIds.map(async (payoutId) => {
        try {
          await updateMemberPayoutToPaid({ payoutId, operator });
        } catch (err) {
          resultList.push(`ID [${payoutId}] error: ${err.toString()}`);
        }
      })
    );
  }
  return resultList;
};
