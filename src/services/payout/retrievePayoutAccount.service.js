const CommunityModel = require('../../communitiesAPI/models/community.model');
const CommunityPayoutAccountModel = require('../../communitiesAPI/models/communityPayoutAccount.model');
const { ParamError } = require('../../utils/error.util');
const {
  COMMUNITY_PAYOUT_ACCOUNT_TYPE,
  COMMUNITY_PAYOUT_ACCOUNT_STATUS,
  STRIPE_CONNECT_NOT_SUPPORTED_COUNTRIES,
} = require('./constants');
const communityManagerPayoutService = require('../../communitiesAPI/services/common/communityManagerPayout.service');
const { PAYOUT_GATEWAY, CONFIG_TYPES } = require('../../constants/common');
const { getConfigByTypeFromCache } = require('../config.service');

const isMigrationPeriod = true;

const getStripePublicKey = async () => {
  const config = await getConfigByTypeFromCache(
    CONFIG_TYPES.PAYMENT_BACKEND_ENV_CONFIG_TYPE
  );
  return config.envVarData.STRIPE_US_PUBLIC_KEY;
};

exports.getCommunityPayoutAccounts = async ({ communityId }) => {
  const community = await CommunityModel.findById(communityId).lean();
  if (!community) {
    throw new ParamError('Invalid community id');
  }
  const bypassStripeConnectCommunityListConfig =
    await getConfigByTypeFromCache('bypassStripeConnectCommunityList');
  const communityCodesThatBypassStripeConnect =
    bypassStripeConnectCommunityListConfig.value?.communityCodes ?? [];

  // Three cases:
  // 1. INR community: only bank account
  // 2. Stripe connect not supported countries: stripe connect & bank account
  // 3. ROW: stripe connect

  const availableAccountTypes = new Set();

  if (
    community.baseCurrency === 'INR' ||
    communityCodesThatBypassStripeConnect.includes(community.code)
  ) {
    availableAccountTypes.add(COMMUNITY_PAYOUT_ACCOUNT_TYPE.BANK_ACCOUNT);
  } else if (
    STRIPE_CONNECT_NOT_SUPPORTED_COUNTRIES.includes(
      community.countryCreatedIn
    )
  ) {
    availableAccountTypes.add(COMMUNITY_PAYOUT_ACCOUNT_TYPE.BANK_ACCOUNT);
    availableAccountTypes.add(
      COMMUNITY_PAYOUT_ACCOUNT_TYPE.STRIPE_CONNECT
    );
  } else {
    availableAccountTypes.add(
      COMMUNITY_PAYOUT_ACCOUNT_TYPE.STRIPE_CONNECT
    );
  }

  // get whitelist config
  const config = await getConfigByTypeFromCache(
    'stripeConnectWhitelistedConfig'
  );
  if (config?.value?.isWhitelisted) {
    // When the whitelist mode is on, and the community id not in the list
    // Will remove the connect from availableAccountTypes
    if (
      !config.value.communityObjectIds.includes(
        community._id.toString()
      ) &&
      availableAccountTypes.has(
        COMMUNITY_PAYOUT_ACCOUNT_TYPE.STRIPE_CONNECT
      )
    ) {
      availableAccountTypes.delete(
        COMMUNITY_PAYOUT_ACCOUNT_TYPE.STRIPE_CONNECT
      );
      availableAccountTypes.add(
        COMMUNITY_PAYOUT_ACCOUNT_TYPE.BANK_ACCOUNT
      );
    }
  }

  const payoutAccountLists = [];

  // Get stripe connect account
  const stripeConnectAccount = await CommunityPayoutAccountModel.findOne({
    communityObjectId: communityId,
    payoutGateway: PAYOUT_GATEWAY.STRIPE_US,
    status: {
      $in: [
        COMMUNITY_PAYOUT_ACCOUNT_STATUS.INIT,
        COMMUNITY_PAYOUT_ACCOUNT_STATUS.CONNECTED,
        COMMUNITY_PAYOUT_ACCOUNT_STATUS.RESTRICTED,
        COMMUNITY_PAYOUT_ACCOUNT_STATUS.PENDING_VERIFICATION,
      ],
    },
  }).lean();
  if (stripeConnectAccount) {
    stripeConnectAccount.payoutAccountType =
      COMMUNITY_PAYOUT_ACCOUNT_TYPE.STRIPE_CONNECT;

    const externalAccounts =
      stripeConnectAccount.accountDetails?.external_accounts?.data ?? [];
    if (externalAccounts.length > 0) {
      const firstValidAccount = externalAccounts[0];

      stripeConnectAccount.bankName = firstValidAccount.bank_name;
      stripeConnectAccount.payoutCurrency = firstValidAccount.currency;
      stripeConnectAccount.accountNumber = `****${firstValidAccount.last4}`;
      stripeConnectAccount.bankAccountstatus = firstValidAccount.status;
    }

    delete stripeConnectAccount.accountDetails;

    // Get stripe us public key for FE to render banner
    stripeConnectAccount.stripePublicKey = await getStripePublicKey();
  }

  // Get bank accounts from our database
  const bankAccount =
    await communityManagerPayoutService.getPayoutBankAccount({
      communityId,
    });
  if (bankAccount) {
    bankAccount.payoutAccountType =
      COMMUNITY_PAYOUT_ACCOUNT_TYPE.BANK_ACCOUNT;
  }

  if (
    availableAccountTypes.has(COMMUNITY_PAYOUT_ACCOUNT_TYPE.STRIPE_CONNECT)
  ) {
    if (stripeConnectAccount) {
      payoutAccountLists.push(stripeConnectAccount);
    } else if (bankAccount && isMigrationPeriod) {
      // TODO: delete this part after migration period
      // If its in migration period, and community doesnt have stripe connect account yet
      // only have existing bank account, we will return bank account here
      availableAccountTypes.add(
        COMMUNITY_PAYOUT_ACCOUNT_TYPE.BANK_ACCOUNT
      );
    }
  }

  if (
    availableAccountTypes.has(
      COMMUNITY_PAYOUT_ACCOUNT_TYPE.BANK_ACCOUNT
    ) &&
    bankAccount
  ) {
    payoutAccountLists.push(bankAccount);
  }

  return {
    availableAccountTypes: [...availableAccountTypes],
    payoutAccountLists,
  };
};
