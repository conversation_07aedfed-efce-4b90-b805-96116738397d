const CommunityPayoutModel = require('../../communitiesAPI/models/communityPayouts.model');
const CommunityPayoutAccountModel = require('../../communitiesAPI/models/communityPayoutAccount.model');
const CommunityManagerPayoutModel = require('../../communitiesAPI/models/communityManagerPayout.model');
const {
  PAYOUT_CHANNEL,
  COMMUNITY_PAYOUT_ACCOUNT_STATUS,
} = require('./constants');

async function getStripeConnectBankAccountInfo(communityObjectId) {
  const payoutAccount = await CommunityPayoutAccountModel.findOne({
    communityObjectId,
    status: COMMUNITY_PAYOUT_ACCOUNT_STATUS.CONNECTED,
  }).lean();
  if (!payoutAccount) return null;
  const firstValidAccount =
    payoutAccount.accountDetails?.external_accounts?.data?.find(
      (account) =>
        ['new', 'validated', 'verified'].includes(account.status)
    );
  if (!firstValidAccount) return null;
  return {
    _id: payoutAccount._id,
    accountId: payoutAccount.payoutGatewayAccountId,
    externalAccountId: firstValidAccount.id,
    country: firstValidAccount.country,
    payoutCurrency: firstValidAccount.currency?.toUpperCase(),
    bankName: firstValidAccount.bank_name,
    accountName: firstValidAccount.account_holder_name,
    accountNumber: `****${firstValidAccount.last4}`,
    email: payoutAccount.email,
  };
}

async function getManagerBankAccountInfo(communityObjectId) {
  return CommunityManagerPayoutModel.findOne({
    communityId: communityObjectId,
  }).lean();
}

async function updateBankAccountForPayout(payout, operator) {
  let bankAccountInfo = null;
  if (payout.payoutChannel === PAYOUT_CHANNEL.STRIPE_CONNECT) {
    bankAccountInfo = await getStripeConnectBankAccountInfo(
      payout.communityObjectId
    );
  } else if (
    payout.payoutChannel === PAYOUT_CHANNEL.GLOBAL ||
    payout.payoutChannel === PAYOUT_CHANNEL.STRIPE_INDIA
  ) {
    bankAccountInfo = await getManagerBankAccountInfo(
      payout.communityObjectId
    );
  }
  if (!bankAccountInfo) {
    throw new Error('No valid bank account info found');
  }
  await CommunityPayoutModel.findOneAndUpdate(
    { _id: payout._id },
    { $set: { bankAccountInfo, operator } },
    { new: true }
  );
}

exports.updatePayoutBankAccount = async (
  payoutIds,
  communityCodes,
  operator
) => {
  const resultList = [];
  if (payoutIds) {
    await Promise.all(
      payoutIds.map(async (payoutId) => {
        try {
          const payout = await CommunityPayoutModel.findById(
            payoutId
          ).lean();
          if (!payout) throw new Error('Payout not found');
          await updateBankAccountForPayout(payout, operator);
        } catch (err) {
          resultList.push(`ID [${payoutId}] error: ${err.toString()}`);
        }
      })
    );
  } else if (communityCodes) {
    await Promise.all(
      communityCodes.map(async (communityCode) => {
        try {
          const payout = await CommunityPayoutModel.findOne({
            communityCode,
          }).lean();
          if (!payout) throw new Error('Payout not found');
          await updateBankAccountForPayout(payout, operator);
        } catch (err) {
          resultList.push(`[${communityCode}] error: ${err.toString()}`);
        }
      })
    );
  }
  return resultList;
};
