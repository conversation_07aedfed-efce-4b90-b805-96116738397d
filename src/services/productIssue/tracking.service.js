const larkService = require('../../communitiesAPI/services/common/lark.service');
const logger = require('../logger.service');

const PRODUCT_ISSUE_BASE_ID = 'Q4CsbezhSaeTspsJWonu3HNDs3d';
const PRODUCT_ISSUE_TABLE_ID = 'tblkz7G8pY57u40c';
const PRODUCT_REPORT_STATUS = {
  OPEN: 'open',
  DONE: 'done',
  PENDING: 'pending',
  REJECTED: 'rejected',
};
const ALL_PRODUCT_REPORT_STATUSES = Object.values(
  PRODUCT_REPORT_STATUS
).map((value) => value.toLowerCase());

const REPORT_CATEGORIES = {
  ADHOC_FEATURE: 'adhoc',
  BUG: 'bug',
  DATA_FIX: 'data_fix',
  OPS: 'ops',
  OTHER: 'other',
  FEATURE_REQUEST: 'feature_request',
  FEATURE_QUESTION: 'feature_question',
};
const ALL_REPORT_CATEGORIES = Object.values(REPORT_CATEGORIES).map(
  (value) => value.toLowerCase()
);

const REPORT_COMPONENTS = {
  APP: 'app',
  WEB: 'web',
  BACKEND: 'backend',
  OTHER: 'other',
  NOT_APPLICABLE: 'not_applicable',
};
const ALL_REPORT_COMPONENTS = Object.values(REPORT_COMPONENTS).map(
  (value) => value.toLowerCase()
);

const PRODUCT_DOMAINS = {
  CHECKOUT: 'checkout',
  PAYMENT: 'payment',
  MEMBERSHIP: 'membership',
  PRO: 'pro',
  OTHER: 'other',
};
const ALL_PRODUCT_DOMAINS = Object.values(PRODUCT_DOMAINS).map((value) =>
  value.toLowerCase()
);

const ENGINEER_NAME_TO_USER_ID = {
  Edwin: 'ou_3c6934dc6667770360b309d8eb6f4c20',
  Bayan: 'ou_244e28d44ff554059d3021bddf5f9411',
  Minh: 'ou_8c2d4a7366e37894736ab61b3de65987',
  Nick: 'ou_27bf4e7b9a69fe12a48af219ee4fbfa7',
  Yahan: 'ou_5510d1bf145c96c16189be40c500181b',
  Aman: 'ou_389ea51fb2c9bb11eb57cc649364c2a9',
  MK: 'ou_0638dc52c1357195e74a1e867a01f788',
  Neil: 'ou_0c1e691278e71926a44f7aa9776edb36',
  Trent: 'ou_12a2c7f9a50474064f33b4fc4cb81681',
  Beatrice: 'ou_2b1862db35c19f56cc88627e1e2f9cf6',
  Joel: 'ou_cbadc5a3a02f8ba65ad11b9dd11063bb',
  Hussain: 'ou_feea94caa129cd563a3e5fe513fc3535',
  Leon: 'ou_d18bad2c111ea0fc34aef4b576a842bd',
  Sebastin: 'ou_7d8f9844a5b097443d792620002ec563',
};

const ENGINEER_USER_IDS = Object.values(ENGINEER_NAME_TO_USER_ID);

const ADMIN_PORTAL_URL_REGEX = /(cops-admin.nas.io|cops.nas.io)\/.*/gi;

async function createProductIssueRecord({
  tenantAccessToken,
  reporterId,
  messageId,
  threadId,
  content,
  createTime,
}) {
  try {
    const record = {
      fields: {
        reporter: [
          {
            id: reporterId,
          },
        ],
        description: content,
        message_id: messageId,
        thread_id: threadId,
        status: PRODUCT_REPORT_STATUS.OPEN,
        create_time: createTime,
      },
    };

    const response = await larkService.createBaseRecord({
      tenantAccessToken,
      baseId: PRODUCT_ISSUE_BASE_ID,
      tableId: PRODUCT_ISSUE_TABLE_ID,
      record,
    });

    if (response.code !== larkService.LARK_SUCCESS_CODE) {
      throw new Error(
        `Failed to create product issue record: ${response.msg}`
      );
    }
    return response;
  } catch (error) {
    logger.error('Error creating product issue record:', error);
    throw error;
  }
}

async function getIssueByMessageId(tenantAccessToken, parentMessageId) {
  const response = await larkService.queryBaseRecords({
    tenantAccessToken,
    baseId: PRODUCT_ISSUE_BASE_ID,
    tableId: PRODUCT_ISSUE_TABLE_ID,
    filter: `CurrentValue.[message_id] = "${parentMessageId}"`,
  });

  if (response.code !== larkService.LARK_SUCCESS_CODE) {
    throw new Error(`Failed to query issue record: ${response.msg}`);
  }

  const records = response.data?.items || [];

  if (!records.length) {
    logger.error(`No record found for message ID: ${parentMessageId}`);
    throw new Error(`No record found for message ID: ${parentMessageId}`);
  }

  return records[0];
}

async function updateFirstEngineerReply({
  tenantAccessToken,
  parentMessageId,
  senderId,
  replyTime,
}) {
  try {
    if (!ENGINEER_USER_IDS.includes(senderId)) {
      return;
    }

    const record = await getIssueByMessageId(
      tenantAccessToken,
      parentMessageId
    );
    if (record.fields.first_engineer_reply_time > 0) {
      return;
    }

    const updatedFields = {
      fields: {
        first_engineer_reply_user: [{ id: senderId }],
        first_engineer_reply_time: replyTime,
      },
    };

    const response = await larkService.updateBaseRecord({
      tenantAccessToken,
      baseId: PRODUCT_ISSUE_BASE_ID,
      tableId: PRODUCT_ISSUE_TABLE_ID,
      recordId: record.record_id,
      updatedFields,
    });

    if (response.code !== larkService.LARK_SUCCESS_CODE) {
      logger.error(`Failed to update engineer reply: ${response.msg}`);
    }

    return response;
  } catch (error) {
    logger.error('Error updating engineer reply:', error);
  }
}

function validateReportStatuses(status) {
  if (!ALL_PRODUCT_REPORT_STATUSES.includes(status.toLowerCase())) {
    throw new Error('Invalid issue status');
  }
}

function validateCategories(categories) {
  const invalidCategories = categories.filter(
    (cat) => !ALL_REPORT_CATEGORIES.includes(cat)
  );
  if (invalidCategories.length > 0) {
    throw new Error(
      `Invalid issue categories: ${invalidCategories.join(', ')}`
    );
  }
}

function validateComponents(components) {
  const invalidComponents = components.filter(
    (comp) => !ALL_REPORT_COMPONENTS.includes(comp)
  );
  if (invalidComponents.length > 0) {
    throw new Error(
      `Invalid issue components: ${invalidComponents.join(', ')}`
    );
  }
}

function validateProductDomains(productDomains) {
  const invalidProductDomains = productDomains.filter(
    (domain) => !ALL_PRODUCT_DOMAINS.includes(domain)
  );
  if (invalidProductDomains.length > 0) {
    throw new Error(
      `Invalid issue product domains: ${invalidProductDomains.join(', ')}`
    );
  }
}

function validateSolvableViaAdmin(solvableViaAdmin, linkToAdminPage) {
  if (solvableViaAdmin !== 'true' && solvableViaAdmin !== 'false') {
    throw new Error(
      'Invalid solvable via admin value. It must be true or false'
    );
  }

  const isSolvableViaAdmin = solvableViaAdmin.toLowerCase() === 'true';
  if (!isSolvableViaAdmin) {
    return;
  }

  if (linkToAdminPage.length === 0) {
    throw new Error(
      'Link to admin page is required when solvable via admin is true'
    );
  }

  if (!ADMIN_PORTAL_URL_REGEX.test(linkToAdminPage)) {
    throw new Error(
      `Invalid link to admin page. It must be a valid URL starting with the regex ${ADMIN_PORTAL_URL_REGEX}`
    );
  }
}

async function resolveIssue({
  tenantAccessToken,
  parentMessageId,
  status,
  resolverId,
  processedComponents,
  processedCategories,
  processedProductDomains,
  processedSolvableViaAdmin,
  linkToAdminPage,
  resolutionDetails,
  resolveTime,
}) {
  validateReportStatuses(status);
  validateCategories(processedCategories);
  validateComponents(processedComponents);
  validateProductDomains(processedProductDomains);
  validateSolvableViaAdmin(processedSolvableViaAdmin, linkToAdminPage);

  const record = await getIssueByMessageId(
    tenantAccessToken,
    parentMessageId
  );

  const isSolvableViaAdmin =
    processedSolvableViaAdmin.toLowerCase() === 'true';

  const updatedFields = {
    fields: {
      status,
      resolver: [{ id: resolverId }],
      resolve_time: resolveTime,
      report_category: processedCategories,
      components: processedComponents,
      product_domains: processedProductDomains,
      admin_solvable: isSolvableViaAdmin ? 'true' : 'false',
      admin_url: {
        text: 'Admin Portal',
        url: isSolvableViaAdmin ? linkToAdminPage.toLowerCase() : 'N/A',
      },
      resolution_details: resolutionDetails,
    },
  };

  const response = await larkService.updateBaseRecord({
    tenantAccessToken,
    baseId: PRODUCT_ISSUE_BASE_ID,
    tableId: PRODUCT_ISSUE_TABLE_ID,
    recordId: record.record_id,
    updatedFields,
  });

  if (response.code !== larkService.LARK_SUCCESS_CODE) {
    throw new Error(`Failed to resolve issue: ${response.msg}`);
  }

  return response;
}

module.exports = {
  createProductIssueRecord,
  updateFirstEngineerReply,
  resolveIssue,
  ALL_PRODUCT_REPORT_STATUSES,
  ALL_REPORT_CATEGORIES,
  ALL_REPORT_COMPONENTS,
  ALL_PRODUCT_DOMAINS,
};
