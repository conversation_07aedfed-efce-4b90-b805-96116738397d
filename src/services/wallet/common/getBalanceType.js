const {
  PURCHASE_TYPE,
  TRANSACTION_TYPE,
} = require('../../../constants/common');

exports.getBalanceType = (transactionType) => {
  switch (transactionType) {
    case `${PURCHASE_TYPE.CREDIT_BALANCE}_${TRANSACTION_TYPE.INBOUND}`:
    case `${PURCHASE_TYPE.CREDIT_BALANCE}_${TRANSACTION_TYPE.OUTBOUND}`:
      return 'creditBalance';
    case PURCHASE_TYPE.ADS_CAMPAIGN_TOPUP:
    case PURCHASE_TYPE.ADS_CAMPAIGN_SPEND:
    case PURCHASE_TYPE.ADS_CAMPAIGN_SPEND_REVERT:
    case PURCHASE_TYPE.ADS_CAMPAIGN_RETURN:
    case PURCHASE_TYPE.ADS_AVAILABLE_TO_OUTGOING:
    case PURCHASE_TYPE.ADS_REWARD_ADJUSTMENT_ADD:
    case PURCHASE_TYPE.ADS_REWARD_ADJUSTMENT_DEDUCT:
      return 'adsBalance';
    default:
      return 'balance';
  }
};
