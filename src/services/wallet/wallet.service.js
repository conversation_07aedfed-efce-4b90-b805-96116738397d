const ObjectId = require('mongoose').Types.ObjectId;
const PrimaryMongooseConnection = require('../../rpc/primaryMongooseConnection');
const logger = require('../logger.service');
const { getOrCreateWallet } = require('./common/createWallet');
const {
  PURCHASE_TYPE,
  WALLET_TYPE,
  TRANSACTION_TYPE,
} = require('../../constants/common');
const {
  InboundTransactionHandler,
  RefundTransactionHandler,
  PayoutTransactionHandler,
  ResetTransactionHandler,
  AdsCampaignTransactionHandler,
  CreditBalanceTransactionHandler,
} = require('./handlers');
const { ParamError } = require('../../utils/error.util');
const WalletModel = require('../../models/wallet/wallet.model');
const CommunityModel = require('../../communitiesAPI/models/community.model');
const LearnerModel = require('../../models/learners.model');

const updateWalletByTransactionType = async (
  communityObjectId,
  learnerObjectId,
  walletObjectId,
  transactionId,
  transactionType,
  paymentProvider,
  amountBreakdownInBaseCurrency,
  amountBreakdownInUsd,
  transactionCreatedAt,
  affiliate,
  revenueTransactionPurchasedId,
  targetWalletType,
  databaseSession
) => {
  switch (transactionType) {
    case PURCHASE_TYPE.SUBSCRIPTION:
    case PURCHASE_TYPE.EVENT:
    case PURCHASE_TYPE.FOLDER:
    case PURCHASE_TYPE.SESSION:
    case PURCHASE_TYPE.CHALLENGE:
    case PURCHASE_TYPE.CAMPAIGN_REWARD:
    case PURCHASE_TYPE.REFERRAL_BONUS:
    case PURCHASE_TYPE.ADJUSTMENT_ADD:
    case PURCHASE_TYPE.ZERO_LINK:
    case PURCHASE_TYPE.REFERRAL_REWARD:
      await InboundTransactionHandler.updateBalance(
        walletObjectId,
        transactionId,
        transactionType,
        paymentProvider,
        amountBreakdownInBaseCurrency,
        amountBreakdownInUsd,
        transactionCreatedAt,
        affiliate,
        targetWalletType,
        databaseSession
      );
      break;

    case PURCHASE_TYPE.REFUND:
    case PURCHASE_TYPE.ADJUSTMENT_DEDUCT:
    case PURCHASE_TYPE.CHARGEBACK:
      await RefundTransactionHandler.updateBalance(
        walletObjectId,
        transactionId,
        transactionType,
        paymentProvider,
        amountBreakdownInBaseCurrency,
        amountBreakdownInUsd,
        transactionCreatedAt,
        affiliate,
        targetWalletType,
        databaseSession
      );
      break;
    case PURCHASE_TYPE.PAYOUT:
      await PayoutTransactionHandler.updateBalance(
        walletObjectId,
        transactionId,
        transactionType,
        paymentProvider,
        amountBreakdownInBaseCurrency,
        amountBreakdownInUsd,
        transactionCreatedAt,
        WALLET_TYPE.COMMUNITY,
        databaseSession
      );
      break;
    case PURCHASE_TYPE.MEMBER_PAYOUT:
      await PayoutTransactionHandler.updateBalance(
        walletObjectId,
        transactionId,
        transactionType,
        paymentProvider,
        amountBreakdownInBaseCurrency,
        amountBreakdownInUsd,
        transactionCreatedAt,
        WALLET_TYPE.MEMBER,
        databaseSession
      );
      break;
    case PURCHASE_TYPE.ADS_CAMPAIGN_TOPUP:
    case PURCHASE_TYPE.ADS_CAMPAIGN_SPEND_REVERT:
    case PURCHASE_TYPE.ADS_CAMPAIGN_RETURN:
    case PURCHASE_TYPE.ADS_REWARD_ADJUSTMENT_ADD:
      await AdsCampaignTransactionHandler.updateInboundBalance(
        walletObjectId,
        transactionId,
        transactionType,
        paymentProvider,
        amountBreakdownInBaseCurrency,
        amountBreakdownInUsd,
        transactionCreatedAt,
        WALLET_TYPE.COMMUNITY,
        databaseSession
      );
      break;
    case PURCHASE_TYPE.ADS_CAMPAIGN_SPEND:
    case PURCHASE_TYPE.ADS_REWARD_ADJUSTMENT_DEDUCT:
      await AdsCampaignTransactionHandler.updateOutboundBalance(
        walletObjectId,
        transactionId,
        transactionType,
        paymentProvider,
        amountBreakdownInBaseCurrency,
        amountBreakdownInUsd,
        transactionCreatedAt,
        WALLET_TYPE.COMMUNITY,
        databaseSession
      );
      break;
    case PURCHASE_TYPE.ADS_AVAILABLE_TO_OUTGOING:
      await AdsCampaignTransactionHandler.movesToOutgoingWallet(
        walletObjectId,
        transactionId,
        transactionType,
        paymentProvider,
        amountBreakdownInBaseCurrency,
        amountBreakdownInUsd,
        transactionCreatedAt,
        WALLET_TYPE.COMMUNITY,
        databaseSession
      );
      break;
    case `${PURCHASE_TYPE.CREDIT_BALANCE}_${TRANSACTION_TYPE.INBOUND}`:
      await CreditBalanceTransactionHandler.updateInboundBalance(
        walletObjectId,
        transactionId,
        transactionType,
        paymentProvider,
        amountBreakdownInBaseCurrency,
        amountBreakdownInUsd,
        transactionCreatedAt,
        WALLET_TYPE.COMMUNITY,
        revenueTransactionPurchasedId,
        databaseSession
      );
      break;
    case `${PURCHASE_TYPE.CREDIT_BALANCE}_${TRANSACTION_TYPE.OUTBOUND}`:
      await CreditBalanceTransactionHandler.updateOutboundBalance(
        walletObjectId,
        transactionId,
        transactionType,
        paymentProvider,
        amountBreakdownInBaseCurrency,
        amountBreakdownInUsd,
        transactionCreatedAt,
        WALLET_TYPE.COMMUNITY,
        revenueTransactionPurchasedId,
        databaseSession
      );
      break;
    default:
      throw new ParamError(`Unsupported transaction type`);
  }
};

exports.updateWalletBalance = async ({
  communityObjectId,
  learnerObjectId,
  transactionId,
  transactionType,
  paymentProvider,
  amountBreakdownInBaseCurrency,
  amountBreakdownInUsd,
  transactionCreatedAt,
  affiliate,
  revenueTransactionPurchasedId,
  targetWalletType,
  dbSession,
}) => {
  // Validate community object id and learner object id
  if (
    transactionType !== PURCHASE_TYPE.MEMBER_PAYOUT &&
    !(await CommunityModel.exists({
      _id: new ObjectId(communityObjectId),
    }))
  ) {
    throw new ParamError(
      `Invalid community object id ${communityObjectId}`
    );
  }
  if (
    !(await LearnerModel.exists({ _id: new ObjectId(learnerObjectId) }))
  ) {
    throw new ParamError(`Invalid learner object id ${learnerObjectId}`);
  }

  logger.info(
    `Update wallet balance by ${JSON.stringify({
      communityObjectId,
      learnerObjectId,
      transactionId,
      transactionType,
      paymentProvider,
      amountBreakdownInBaseCurrency,
      amountBreakdownInUsd,
      transactionCreatedAt,
      affiliate,
      targetWalletType,
    })}`
  );

  let session = dbSession;
  if (!dbSession) {
    const primaryMongooseConnection =
      await PrimaryMongooseConnection.connect();
    session = await primaryMongooseConnection.startSession();
    session.startTransaction();
  }

  try {
    // Get wallet id (create new wallet if not found)
    let walletId;
    if (transactionType === PURCHASE_TYPE.MEMBER_PAYOUT) {
      walletId = await getOrCreateWallet(
        communityObjectId,
        learnerObjectId,
        amountBreakdownInBaseCurrency.currency,
        WALLET_TYPE.MEMBER,
        session
      );
    } else {
      walletId = await getOrCreateWallet(
        communityObjectId,
        learnerObjectId,
        amountBreakdownInBaseCurrency.currency,
        WALLET_TYPE.COMMUNITY,
        session
      );
    }

    await updateWalletByTransactionType(
      communityObjectId,
      learnerObjectId,
      walletId,
      transactionId,
      transactionType,
      paymentProvider,
      amountBreakdownInBaseCurrency,
      amountBreakdownInUsd,
      transactionCreatedAt,
      affiliate,
      revenueTransactionPurchasedId,
      targetWalletType,
      session
    );
    if (!dbSession) {
      await session.commitTransaction();
    }

    return {};
  } catch (error) {
    if (!dbSession) {
      await session.abortTransaction();
    }
    if (error?.code !== 11000) {
      throw error;
    } else {
      logger.warn(`Ignore duplicate error: ${error}`);
    }
  } finally {
    if (!dbSession) {
      await session.endSession();
    }
  }
};

exports.resetWalletBalanceByTransaction = async ({
  transactionId,
  transactionType,
  targetWalletType,
  session,
}) => {
  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  logger.info(
    `Reset wallet balance by ${JSON.stringify({
      transactionId,
    })}`
  );

  let dbSession = session;
  if (!session) {
    dbSession = await primaryMongooseConnection.startSession();
    dbSession.startTransaction();
  }

  if (
    [WALLET_TYPE.COMMUNITY, WALLET_TYPE.MEMBER].includes(targetWalletType)
  ) {
    throw new Error(`Not support reset community/member wallet`);
  }

  let walletObjectId;
  if (targetWalletType) {
    const targetWallet = await WalletModel.findOne({
      type: targetWalletType,
    })
      .select('_id')
      .lean();
    walletObjectId = targetWallet?._id;
  }

  try {
    await ResetTransactionHandler.deleteFundMovementsAndUpdateWallet(
      transactionId,
      walletObjectId,
      transactionType,
      session
    );
    if (!session) {
      await dbSession.commitTransaction();
    }

    return {};
  } catch (error) {
    if (!session) {
      await dbSession.abortTransaction();
    }
    if (error?.code !== 11000) {
      throw error;
    } else {
      logger.warn(`Ignore duplicate error: ${error}`);
    }
  } finally {
    if (!session) {
      await dbSession.endSession();
    }
  }
};

exports.getWalletBalance = async (communityObjectId, learnerObjectId) => {
  const wallet = await WalletModel.findOne({
    communityObjectId: new ObjectId(communityObjectId),
    learnerObjectId,
    type: WALLET_TYPE.COMMUNITY,
  }).lean();
  return wallet;
};

exports.getCreditBalance = async (communityObjectId, learnerObjectId) => {
  const wallet = await WalletModel.findOne({
    communityObjectId: new ObjectId(communityObjectId.toString()),
    learnerObjectId: new ObjectId(learnerObjectId.toString()),
    type: WALLET_TYPE.COMMUNITY,
  }).lean();

  const creditBalance = {};
  if (wallet?.creditBalance) {
    const currencies = Object.keys(wallet.creditBalance);
    currencies.forEach((currency) => {
      if (wallet.creditBalance[currency].available > 0) {
        creditBalance[currency] = wallet.creditBalance[currency].available;
      }
    });
  }
  return creditBalance;
};
