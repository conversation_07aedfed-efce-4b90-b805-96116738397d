const {
  SUB_WALLET_TYPE,
  WALLET_TYPE,
  WALLET_BALANCE_TYPE,
  DEFAULT_CURRENCY,
} = require('../../../constants/common');
const logger = require('../../logger.service');
const {
  NasIOWalletService,
  PaymentProviderWalletService,
  WalletUpdateService,
} = require('../common');
const WalletModel = require('../../../models/wallet/wallet.model');

exports.updateInboundBalance = async (
  walletObjectId,
  transactionId,
  transactionType,
  paymentProvider,
  amountBreakdownInBaseCurrency,
  amountBreakdownInUsd,
  transactionCreatedAt,
  affiliate,
  databaseSession
) => {
  logger.info(
    `Update community wallet - walletId: ${walletObjectId.toString()}`
  );

  const amountBreakdown = {
    [SUB_WALLET_TYPE.AVAILABLE]: amountBreakdownInBaseCurrency.netAmount,
    [SUB_WALLET_TYPE.POOL]:
      -amountBreakdownInBaseCurrency.expectedPaidAmount,
    [SUB_WALLET_TYPE.ADS_CAMPAIGN_PROCESSING_FEE]: -(
      amountBreakdownInBaseCurrency.fee.adsCampaignProcessFee ?? 0
    ),
  };
  const approximateUSDAmountBreakdown = {
    [SUB_WALLET_TYPE.AVAILABLE]: amountBreakdownInUsd.netAmount,
    [SUB_WALLET_TYPE.POOL]: -amountBreakdownInUsd.expectedPaidAmount,
    [SUB_WALLET_TYPE.ADS_CAMPAIGN_PROCESSING_FEE]: -(
      amountBreakdownInUsd.fee.adsCampaignProcessFee ?? 0
    ),
  };

  await WalletUpdateService.createFundMovementsAndUpdateBalance({
    walletObjectId,
    transactionObjectId: transactionId,
    transactionType,
    walletType: WALLET_TYPE.COMMUNITY,
    amountBreakdown,
    currency: amountBreakdownInBaseCurrency.currency,
    approximateUSDAmountBreakdown,
    transactionCreatedAt,
    databaseSession,
  });

  // --- Update NAS IO wallet
  await NasIOWalletService.handleInboundTransaction(
    transactionId,
    transactionType,
    amountBreakdownInUsd,
    transactionCreatedAt,
    databaseSession
  );

  // --- Update Payment provider wallet
  await PaymentProviderWalletService.handleInboundTransaction(
    transactionId,
    transactionType,
    paymentProvider,
    amountBreakdownInBaseCurrency,
    amountBreakdownInUsd,
    transactionCreatedAt,
    databaseSession
  );
};

exports.updateOutboundBalance = async (
  walletObjectId,
  transactionId,
  transactionType,
  paymentProvider,
  amountBreakdownInBaseCurrency,
  amountBreakdownInUsd,
  transactionCreatedAt,
  affiliate,
  databaseSession
) => {
  logger.info(
    `Update community wallet - walletId: ${walletObjectId.toString()}`
  );

  // Get current balance
  const wallet = await WalletModel.findById(walletObjectId).lean();
  const outgoingBalance =
    wallet.adsBalance[DEFAULT_CURRENCY][SUB_WALLET_TYPE.OUTGOING] ?? 0;
  const availableBalance =
    wallet.adsBalance[DEFAULT_CURRENCY][SUB_WALLET_TYPE.AVAILABLE] ?? 0;
  if (
    outgoingBalance + availableBalance <
    amountBreakdownInBaseCurrency.netAmount
  ) {
    throw new Error(`The balance is not enough for campaign spend`);
  }
  // netAmount = 1000
  // outgoingAmount = 600
  // availableAmount = 400
  const outgoingAmount = Math.min(
    outgoingBalance,
    amountBreakdownInBaseCurrency.netAmount
  );
  const availableAmount =
    amountBreakdownInBaseCurrency.netAmount - outgoingAmount;

  const amountBreakdown = {
    [SUB_WALLET_TYPE.OUTGOING]: -outgoingBalance,
    [SUB_WALLET_TYPE.AVAILABLE]: -availableAmount,
    [SUB_WALLET_TYPE.POOL]:
      amountBreakdownInBaseCurrency.expectedPaidAmount,
    [SUB_WALLET_TYPE.ADS_CAMPAIGN_PROCESSING_FEE]:
      amountBreakdownInBaseCurrency.fee.adsCampaignProcessFee ?? 0,
  };
  const approximateUSDAmountBreakdown = {
    [SUB_WALLET_TYPE.OUTGOING]: -outgoingBalance,
    [SUB_WALLET_TYPE.AVAILABLE]: -availableAmount,
    [SUB_WALLET_TYPE.POOL]: amountBreakdownInUsd.expectedPaidAmount,
    [SUB_WALLET_TYPE.ADS_CAMPAIGN_PROCESSING_FEE]:
      amountBreakdownInUsd.fee.adsCampaignProcessFee ?? 0,
  };

  await WalletUpdateService.createFundMovementsAndUpdateBalance({
    walletObjectId,
    transactionObjectId: transactionId,
    transactionType,
    walletType: WALLET_TYPE.COMMUNITY,
    amountBreakdown,
    currency: amountBreakdownInBaseCurrency.currency,
    approximateUSDAmountBreakdown,
    transactionCreatedAt,
    databaseSession,
  });

  // Handle nas io wallet
  await NasIOWalletService.handleAdsCampaignSpendTransaction(
    transactionId,
    transactionType,
    amountBreakdownInUsd,
    transactionCreatedAt,
    databaseSession
  );
};

exports.movesToOutgoingWallet = async (
  walletObjectId,
  transactionId,
  transactionType,
  paymentProvider,
  amountBreakdownInBaseCurrency,
  amountBreakdownInUsd,
  transactionCreatedAt,
  affiliate,
  databaseSession
) => {
  logger.info(
    `Update community wallet - walletId: ${walletObjectId.toString()}`
  );

  const amountBreakdown = {
    [SUB_WALLET_TYPE.AVAILABLE]: -amountBreakdownInBaseCurrency.netAmount,
    [SUB_WALLET_TYPE.OUTGOING]:
      amountBreakdownInBaseCurrency.expectedPaidAmount,
  };
  const approximateUSDAmountBreakdown = {
    [SUB_WALLET_TYPE.AVAILABLE]: -amountBreakdownInUsd.netAmount,
    [SUB_WALLET_TYPE.OUTGOING]: amountBreakdownInUsd.expectedPaidAmount,
  };

  await WalletUpdateService.createFundMovementsAndUpdateBalance({
    walletObjectId,
    transactionObjectId: transactionId,
    transactionType,
    walletType: WALLET_TYPE.COMMUNITY,
    amountBreakdown,
    currency: amountBreakdownInBaseCurrency.currency,
    approximateUSDAmountBreakdown,
    transactionCreatedAt,
    databaseSession,
  });
};
