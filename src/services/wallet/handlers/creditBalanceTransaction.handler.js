const {
  SUB_WALLET_TYPE,
  WALLET_TYPE,
} = require('../../../constants/common');
const logger = require('../../logger.service');
const { WalletUpdateService } = require('../common');
const WalletModel = require('../../../models/wallet/wallet.model');
const PlanTransactionModel = require('../../../models/plan/communityPlanTransaction.model');

exports.updateInboundBalance = async (
  walletObjectId,
  transactionId,
  transactionType,
  paymentProvider,
  amountBreakdownInBaseCurrency,
  amountBreakdownInUsd,
  transactionCreatedAt,
  walletType,
  revenueTransactionPurchasedId,
  databaseSession
) => {
  logger.info(
    `Update community wallet - walletId: ${walletObjectId.toString()}`
  );

  // Overhere expectedPaidAmount and netAmount are essentially the same
  // as our fees are all 0.
  const amountBreakdown = {
    [SUB_WALLET_TYPE.AVAILABLE]: amountBreakdownInBaseCurrency.netAmount,
    [SUB_WALLET_TYPE.POOL]:
      -amountBreakdownInBaseCurrency.expectedPaidAmount,
  };
  const approximateUSDAmountBreakdown = {
    [SUB_WALLET_TYPE.AVAILABLE]: amountBreakdownInUsd.netAmount,
    [SUB_WALLET_TYPE.POOL]: -amountBreakdownInUsd.expectedPaidAmount,
  };

  await WalletUpdateService.createFundMovementsAndUpdateBalance({
    walletObjectId,
    transactionObjectId: transactionId,
    transactionType,
    walletType: WALLET_TYPE.COMMUNITY,
    amountBreakdown,
    currency: amountBreakdownInBaseCurrency.currency,
    approximateUSDAmountBreakdown,
    transactionCreatedAt,
    databaseSession,
  });

  await PlanTransactionModel.findByIdAndUpdate(
    revenueTransactionPurchasedId,
    {
      $set: { 'metadata.isCredited': true },
    },
    {
      session: databaseSession,
    }
  );
};

exports.updateOutboundBalance = async (
  walletObjectId,
  transactionId,
  transactionType,
  paymentProvider,
  amountBreakdownInBaseCurrency,
  amountBreakdownInUsd,
  transactionCreatedAt,
  walletType,
  revenueTransactionPurchasedId,
  databaseSession
) => {
  logger.info(
    `Update community wallet - walletId: ${walletObjectId.toString()}`
  );

  // Get current balance
  const wallet = await WalletModel.findById(walletObjectId).lean();
  const currencyCreditBalance =
    wallet.creditBalance[amountBreakdownInBaseCurrency.currency];
  const outgoingBalance =
    currencyCreditBalance[SUB_WALLET_TYPE.OUTGOING] ?? 0;
  const availableBalance =
    currencyCreditBalance[SUB_WALLET_TYPE.AVAILABLE] ?? 0;
  const outgoingUsdBalance =
    currencyCreditBalance.approximateUSD[SUB_WALLET_TYPE.OUTGOING] ?? 0;

  if (
    outgoingBalance + availableBalance <
    amountBreakdownInBaseCurrency.netAmount
  ) {
    throw new Error(
      `The balance is not enough for credit spend. Check the charge logic in payment backend`
    );
  }
  // netAmount = 1000
  // outgoingAmount = 600
  // availableAmount = 400
  const outgoingAmount = Math.min(
    outgoingBalance,
    amountBreakdownInBaseCurrency.netAmount
  );
  const outgoingUsdAmount = Math.min(
    outgoingBalance,
    amountBreakdownInUsd.netAmount
  );
  const availableAmount =
    amountBreakdownInBaseCurrency.netAmount - outgoingAmount;
  const availableUsdAmount =
    amountBreakdownInUsd.netAmount - outgoingUsdAmount;

  const amountBreakdown = {
    [SUB_WALLET_TYPE.OUTGOING]: -outgoingBalance,
    [SUB_WALLET_TYPE.AVAILABLE]: -availableAmount,
    [SUB_WALLET_TYPE.POOL]:
      amountBreakdownInBaseCurrency.expectedPaidAmount,
  };
  const approximateUSDAmountBreakdown = {
    [SUB_WALLET_TYPE.OUTGOING]: -outgoingUsdBalance,
    [SUB_WALLET_TYPE.AVAILABLE]: -availableUsdAmount,
    [SUB_WALLET_TYPE.POOL]: amountBreakdownInUsd.expectedPaidAmount,
  };
  await WalletUpdateService.createFundMovementsAndUpdateBalance({
    walletObjectId,
    transactionObjectId: transactionId,
    transactionType,
    walletType: WALLET_TYPE.COMMUNITY,
    amountBreakdown,
    currency: amountBreakdownInBaseCurrency.currency,
    approximateUSDAmountBreakdown,
    transactionCreatedAt,
    databaseSession,
  });

  const arrayFilters = [
    { 'element.creditCurrency': amountBreakdownInBaseCurrency.currency },
  ];
  await PlanTransactionModel.findByIdAndUpdate(
    revenueTransactionPurchasedId,
    {
      $set: { 'paidCredits.$[element].isCharged': true },
    },
    {
      arrayFilters,
      session: databaseSession,
    }
  );
};
