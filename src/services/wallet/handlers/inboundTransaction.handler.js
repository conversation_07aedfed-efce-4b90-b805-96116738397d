const {
  SUB_WALLET_TYPE,
  WALLET_TYPE,
} = require('../../../constants/common');
const logger = require('../../logger.service');
const {
  NasIOWalletService,
  PaymentProviderWalletService,
  WalletUpdateService,
  MemberWalletService,
} = require('../common');

exports.updateBalance = async (
  walletObjectId,
  transactionId,
  transactionType,
  paymentProvider,
  amountBreakdownInBaseCurrency,
  amountBreakdownInUsd,
  transactionCreatedAt,
  affiliate,
  targetWalletType,
  databaseSession
) => {
  logger.info(
    `Update community wallet - walletId: ${walletObjectId.toString()}`
  );

  const amountBreakdown = {
    [SUB_WALLET_TYPE.AVAILABLE]: amountBreakdownInBaseCurrency.netAmount,
    [SUB_WALLET_TYPE.POOL]: -(
      amountBreakdownInBaseCurrency.expectedPaidAmount ??
      amountBreakdownInBaseCurrency.paidAmount
    ),
    [SUB_WALLET_TYPE.GATEWAY_FEE]:
      amountBreakdownInBaseCurrency.fee.gatewayFee +
      amountBreakdownInBaseCurrency.fee.processingFee +
      amountBreakdownInBaseCurrency.fee.internationalFee,
    [SUB_WALLET_TYPE.GST_ON_GATEWAY_FEE]:
      amountBreakdownInBaseCurrency.fee.gst,
    [SUB_WALLET_TYPE.NASIO_FEE]:
      amountBreakdownInBaseCurrency.revenueShareAmount,
    [SUB_WALLET_TYPE.GST_ON_NASIO_FEE]:
      amountBreakdownInBaseCurrency.fee.gstOnRevenue,
    [SUB_WALLET_TYPE.WHT]: amountBreakdownInBaseCurrency.fee.whtFee,
    [SUB_WALLET_TYPE.AFFILIATE_COMMISSION_AMOUNT]:
      amountBreakdownInBaseCurrency.affiliateCommissionAmount ?? 0,
  };
  const approximateUSDAmountBreakdown = {
    [SUB_WALLET_TYPE.AVAILABLE]: amountBreakdownInUsd.netAmount,
    [SUB_WALLET_TYPE.POOL]: -(
      amountBreakdownInUsd.expectedPaidAmount ??
      amountBreakdownInUsd.paidAmount
    ),
    [SUB_WALLET_TYPE.GATEWAY_FEE]:
      amountBreakdownInUsd.fee.gatewayFee +
      amountBreakdownInUsd.fee.processingFee +
      amountBreakdownInUsd.fee.internationalFee,
    [SUB_WALLET_TYPE.GST_ON_GATEWAY_FEE]: amountBreakdownInUsd.fee.gst,
    [SUB_WALLET_TYPE.NASIO_FEE]: amountBreakdownInUsd.revenueShareAmount,
    [SUB_WALLET_TYPE.GST_ON_NASIO_FEE]:
      amountBreakdownInUsd.fee.gstOnRevenue,
    [SUB_WALLET_TYPE.WHT]: amountBreakdownInUsd.fee.whtFee,
    [SUB_WALLET_TYPE.AFFILIATE_COMMISSION_AMOUNT]:
      amountBreakdownInUsd.affiliateCommissionAmount ?? 0,
  };

  switch (targetWalletType) {
    case WALLET_TYPE.NASIO:
      await NasIOWalletService.handleInboundTransaction(
        transactionId,
        transactionType,
        amountBreakdownInUsd,
        transactionCreatedAt,
        databaseSession
      );
      return;
    case WALLET_TYPE.STRIPE:
    case WALLET_TYPE.STRIPE_US:
    case WALLET_TYPE.STRIPE_INDIA:
    case WALLET_TYPE.EBANX:
    case WALLET_TYPE.RAZORPAY:
    case WALLET_TYPE.XENDIT:
      await PaymentProviderWalletService.handleInboundTransaction(
        transactionId,
        transactionType,
        paymentProvider,
        amountBreakdownInBaseCurrency,
        amountBreakdownInUsd,
        transactionCreatedAt,
        databaseSession
      );
      return;
    default:
  }

  await WalletUpdateService.createFundMovementsAndUpdateBalance({
    walletObjectId,
    transactionObjectId: transactionId,
    transactionType,
    walletType: WALLET_TYPE.COMMUNITY,
    amountBreakdown,
    currency: amountBreakdownInBaseCurrency.currency,
    approximateUSDAmountBreakdown,
    transactionCreatedAt,
    databaseSession,
  });

  // --- Update NAS IO wallet
  await NasIOWalletService.handleInboundTransaction(
    transactionId,
    transactionType,
    amountBreakdownInUsd,
    transactionCreatedAt,
    databaseSession
  );

  // --- Update Payment provider wallet
  await PaymentProviderWalletService.handleInboundTransaction(
    transactionId,
    transactionType,
    paymentProvider,
    amountBreakdownInBaseCurrency,
    amountBreakdownInUsd,
    transactionCreatedAt,
    databaseSession
  );

  // --- Update member wallet for affiliate
  await MemberWalletService.handleInboundTransaction(
    affiliate?.learnerObjectId,
    transactionId,
    transactionType,
    amountBreakdownInBaseCurrency,
    amountBreakdownInUsd,
    transactionCreatedAt,
    databaseSession
  );
};
