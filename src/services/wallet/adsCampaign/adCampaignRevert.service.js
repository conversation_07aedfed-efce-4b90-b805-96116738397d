const CommunityTopupOrderModel = require('../../../models/order/communityTopupOrder.model');
const RevenueTransactionModel = require('../../../models/revenueTransaction.model');
const {
  PURCHASE_TYPE,
  TRANSACTION_TYPE,
  DEFAULT_CURRENCY,
} = require('../../../constants/common');
const service = require('../../transaction');
const { PAYMENT_STATUSES } = require('../../../communitiesAPI/constants');

// When campaign got rejected, the funds should be returned to CM's balance
exports.revertAdsCampaignFunds = async ({ campaignId, session }) => {
  const topupOrder = await CommunityTopupOrderModel.findOne({
    entityObjectId: campaignId,
    topupType: PURCHASE_TYPE.ADS_CAMPAIGN_TOPUP,
    status: PAYMENT_STATUSES.SUCCESS,
  }).lean();
  if (!topupOrder) {
    throw new Error(
      `Cannot find topup order for this campaign ${campaignId}`
    );
  }

  // get ads campaign spend transaction
  const spendTransaction = await RevenueTransactionModel.findOne({
    purchasedId: topupOrder._id,
    purchaseType: PURCHASE_TYPE.ADS_CAMPAIGN_SPEND,
    transactionReferenceId: `${topupOrder._id}_spend`,
  }).lean();
  if (!spendTransaction) {
    throw new Error(
      `Cannot find campaign spend transaction for this topup order ${topupOrder._id}`
    );
  } else if (
    spendTransaction?.metadata?.campaignBudgetReturnTransactionId
  ) {
    throw new Error(
      `Budget for this campaign has returned to ads wallet, transaction id=${spendTransaction?.metadata?.campaignBudgetReturnTransactionId}`
    );
  }

  // create ads campaign revert transaction
  const revertTransaction = {
    ...spendTransaction,
    purchaseType: PURCHASE_TYPE.ADS_CAMPAIGN_SPEND_REVERT,
    transactionType: TRANSACTION_TYPE.INBOUND, // money back to CM's ads balance
    transactionCreatedAt: new Date(),
    transactionReferenceId: `${topupOrder._id}_revert`,
    metadata: {
      ...spendTransaction.metadata,
      campaignBudgetSpendTransactionId: spendTransaction._id,
    },
  };

  const transactionResult =
    await service.TransactionCreateService.createTransaction({
      ...revertTransaction,
      session,
    });

  await RevenueTransactionModel.updateOne(
    { _id: spendTransaction._id },
    {
      'metadata.campaignBudgetReturnTransactionId': transactionResult._id,
    },
    { session }
  );
};

// When campaign terminated halfway
exports.returnAdsCampaignFunds = async ({
  campaignId,
  remainingFunds,
  session,
}) => {
  if (remainingFunds === 0) {
    return;
  }
  const topupOrder = await CommunityTopupOrderModel.findOne({
    entityObjectId: campaignId,
    topupType: PURCHASE_TYPE.ADS_CAMPAIGN_TOPUP,
    status: PAYMENT_STATUSES.SUCCESS,
  }).lean();
  if (!topupOrder) {
    throw new Error(
      `Cannot find topup order for this campaign ${campaignId}`
    );
  }

  // get ads campaign spend transaction
  const spendTransaction = await RevenueTransactionModel.findOne({
    purchasedId: topupOrder._id,
    purchaseType: PURCHASE_TYPE.ADS_CAMPAIGN_SPEND,
    transactionReferenceId: `${topupOrder._id}_spend`,
  }).lean();
  if (!spendTransaction) {
    throw new Error(
      `Cannot find campaign spend transaction for this topup order ${topupOrder._id}`
    );
  } else if (
    spendTransaction?.metadata?.campaignBudgetReturnTransactionId
  ) {
    throw new Error(
      `Budget for this campaign has returned to ads wallet, transaction id=${spendTransaction?.metadata?.campaignBudgetReturnTransactionId}`
    );
  }

  // create ads campaign revert transaction
  const amountBreakdownInUsd = {
    expectedPaidAmount: remainingFunds,
    itemPrice: remainingFunds,
    discountedItemPrice: remainingFunds,
    exchangeRate: 1,
    originalAmount: remainingFunds,
    discountAmount: 0,
    paidAmount: remainingFunds,
    fee: {},
    calculatedRawFee: {},
    netAmount: remainingFunds,
  };
  const returnTransaction = {
    ...spendTransaction,
    purchaseType: PURCHASE_TYPE.ADS_CAMPAIGN_RETURN,
    transactionType: TRANSACTION_TYPE.INBOUND, // money back to CM's ads balance
    transactionCreatedAt: new Date(),
    transactionReferenceId: `${topupOrder._id}_return`,
    amountBreakdownInUsd,
    amountBreakdownInLocalCurrency: {
      ...amountBreakdownInUsd,
      currency: DEFAULT_CURRENCY,
    },
    metadata: {
      ...spendTransaction.metadata,
      campaignBudgetSpendTransactionId: spendTransaction._id,
    },
  };

  const transactionResult =
    await service.TransactionCreateService.createTransaction({
      ...returnTransaction,
      session,
    });

  await RevenueTransactionModel.updateOne(
    { _id: spendTransaction._id },
    {
      'metadata.campaignBudgetReturnTransactionId': transactionResult._id,
    },
    { session }
  );
};
