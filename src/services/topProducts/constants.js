const PRODUCT_TYPES = [
  'subscription',
  'program',
  'community_events',
  // 'session',
  'course',
  'digital_product',
];

const PRIMARY_GROUPS = [
  { name: 'event', buckets: ['community_events'] },
  { name: 'challenge', buckets: ['program'] },
  { name: 'membership', buckets: ['subscription'] },
  { name: 'courseOrFile', buckets: ['course', 'digital_product'] }, // choose 1 of these
];

const PAGE_LIMIT = 12; // UI shows 12 cards
const BUCKET_FETCH_SIZE = 24; // grab extras for “all” shuffle
const PRODUCTS_PER_GROUP = 6;

module.exports = {
  PRODUCT_TYPES,
  PRIMARY_GROUPS,
  PAGE_LIMIT,
  BUCKET_FETCH_SIZE,
  PRODUCTS_PER_GROUP,
};
