const IMAGE_QUALITY = {
  SMALL: 1,
  MEDIUM: 25,
  <PERSON><PERSON><PERSON>: 50,
  ORIGINAL: 100,
};

const IMAGE_TYPE = {
  THUMBNAIL: 'thumbnail',
  REGULAR: 'regular',
};

const IMAGE_EXTENSION = {
  PNG: 'png',
  WEBP: 'webp',
  JPG: 'jpg',
};

const UPLOAD_USE_CASE = {
  CHAT: 'chat',
  COMMUNITY_CREATION: 'community_creation',
  ANNOUNCEMENT: 'announcement',
  AI_COFOUNDER: 'ai_cofounder',
};

const IMAGE_VARIANT_IDENTIFIER = {
  PREFIX: 'nio_',
  SUFFIX: '_GV',
};

module.exports = {
  IMAGE_QUALITY,
  IMAGE_TYPE,
  IMAGE_EXTENSION,
  UPLOAD_USE_CASE,
  IMAGE_VARIANT_IDENTIFIER,
};
