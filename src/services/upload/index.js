const aws = require('aws-sdk');
const { v4: uuidV4 } = require('uuid');
const mime = require('mime-types');

const {
  awsSecretKey,
  awsAccessKey,
  awsRegion,
  awsAccessLevel,
} = require('../../config');
const {
  IMAGEASSETBUCKET,
  IMAGEASSETBUCKET_CLOUDFRONT_BASE_URL,
} = require('../../constants/common');
const logger = require('../logger.service');
const {
  UPLOAD_USE_CASE,
  IMAGE_VARIANT_IDENTIFIER,
} = require('./constants');
const { ParamError } = require('../../utils/error.util');

const config = awsSecretKey
  ? {
      secretAccessKey: awsSecretKey,
      accessKeyId: awsAccessKey,
    }
  : { credentialProvider: new aws.CredentialProviderChain() };

aws.config.update({
  region: awsRegion,
  ...config,
});

const s3 = new aws.S3();

const getUploadedFileLink = async (
  bucketName,
  destinationUrl,
  keyName,
  body,
  mimeType
) => {
  try {
    const uploadOptions = {
      Bucket: bucketName + destinationUrl,
      Key: keyName,
      Body: body,
      ContentType: mimeType,
      ACL: awsAccessLevel,
    };

    const result = await s3.upload(uploadOptions).promise();
    return result?.Location;
  } catch (error) {
    logger.error('Could not upload file to s3: ', error);
    throw error;
  }
};

const getUploadedFileLinkFromUploadOptions = async (uploadOptions) => {
  try {
    const result = await s3.upload(uploadOptions).promise();
    return result?.Location;
  } catch (error) {
    logger.error('Could not upload file to s3: ', error);
    throw error;
  }
};

function retrieveBucketAndCloudfront(useCase) {
  switch (useCase) {
    case UPLOAD_USE_CASE.ANNOUNCEMENT:
    case UPLOAD_USE_CASE.CHAT:
    case UPLOAD_USE_CASE.COMMUNITY_CREATION:
    case UPLOAD_USE_CASE.AI_COFOUNDER:
      return {
        bucketName: IMAGEASSETBUCKET,
        cloudfront: IMAGEASSETBUCKET_CLOUDFRONT_BASE_URL,
      };
    default:
      throw new ParamError(`${useCase} not supported`);
  }
}

function retrieveFileName(fileExtension) {
  const isNotGif = fileExtension !== 'gif';
  const dateString = Date.now().toString();

  const finalFileName = `${
    isNotGif ? IMAGE_VARIANT_IDENTIFIER.PREFIX : ''
  }${dateString}_${uuidV4()}${
    isNotGif ? IMAGE_VARIANT_IDENTIFIER.SUFFIX : ''
  }`;

  const filename = `${finalFileName}${
    isNotGif ? '' : '.' + fileExtension.toLowerCase()
  }`;
  return filename;
}

function retrieveKey({ useCase, communityObjectId, filename }) {
  const paths = [useCase];

  if (communityObjectId) {
    paths.push(communityObjectId);
  }

  paths.push(filename);

  return paths.join('/');
}

function retrieveConditions({
  minFileSizeInBytes,
  maxFileSizeInBytes,
  contentType,
}) {
  const conditions = [
    ['content-length-range', minFileSizeInBytes, maxFileSizeInBytes],
    ['starts-with', '$key', ''], // Ensures key exists
    ['eq', '$Content-Type', contentType],
  ];

  return conditions;
}

const getContentType = (fileName) => {
  return mime.lookup(fileName) || 'application/octet-stream'; // Default if unknown
};

const generatePresignedPost = async ({
  useCase,
  communityObjectId,
  fileExtension,
  expiresInSec = 60,
  allowedExtensions = ['jpg', 'jpeg', 'png', 'gif'],
}) => {
  if (!allowedExtensions.includes(fileExtension)) {
    throw new ParamError(`Invalid file extension for ${fileExtension}`);
  }

  const minFileSizeInBytes = 1;
  let maxFileSizeInBytes = 10485760; // 10 MB
  if (useCase === UPLOAD_USE_CASE.ANNOUNCEMENT) {
    maxFileSizeInBytes = 20971520; // 20 MB
  }

  const { bucketName, cloudfront } = retrieveBucketAndCloudfront(useCase);

  const filename = retrieveFileName(fileExtension);
  const key = retrieveKey({ useCase, communityObjectId, filename });
  const contentType = getContentType(fileExtension);

  const conditions = retrieveConditions({
    minFileSizeInBytes,
    maxFileSizeInBytes,
    contentType,
  });

  const params = {
    Bucket: bucketName,
    Fields: {
      key,
      'Content-Type': contentType,
    },
    Expires: expiresInSec,
    Conditions: conditions,
  };

  const cloudfrontUrl = `${cloudfront}/${key}`;

  const postData = await s3.createPresignedPost(params);

  return {
    imageUrl: cloudfrontUrl,
    filename,
    ...postData,
  };
};

module.exports = {
  getUploadedFileLink,
  getUploadedFileLinkFromUploadOptions,
  generatePresignedPost,
};
