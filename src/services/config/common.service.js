const { getConfigByTypeFromCache } = require('../config.service');

exports.isCommunityIdWhitelisted = async (configType, community) => {
  const config = await getConfigByTypeFromCache(configType);
  const {
    blacklistedBaseCurrency = [],
    isWhitelistMode,
    planTypes = [],
    communitiesInWhitelist = [],
  } = config?.value ?? {};

  if (
    blacklistedBaseCurrency.length > 0 &&
    blacklistedBaseCurrency.includes(community.baseCurrency)
  ) {
    return false;
  }

  if (!isWhitelistMode) {
    // Authorised for all kinds of communities if whitelist mode is not enabled
    return true;
  }

  if (planTypes.includes(community.config?.planType)) {
    return true;
  }

  return (
    communitiesInWhitelist?.includes(community._id.toString()) ?? false
  );
};
