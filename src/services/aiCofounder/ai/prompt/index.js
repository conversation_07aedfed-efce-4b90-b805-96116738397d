const TemplatePromptModel = require('../../../../models/ai/aiTemplatePrompts.model');
const { TEMPLATE_SOURCE_TYPE } = require('../../../../constants/common');
const { ParamError } = require('../../../../utils/error.util');
const {
  MESSAGE_INTENT_TYPE,
  AI_TEMPLATE_PROMPT_TYPE,
} = require('../constants');
const { NAS_IO_FRONTEND_URL } = require('../../../../config');

exports.retrievePromptTemplate = async ({ templateType }) => {
  const templatePrompt = await TemplatePromptModel.findOne({
    type: templateType,
    source: TEMPLATE_SOURCE_TYPE.AI_COFOUNDER,
    isActive: true,
  })
    .sort({ version: -1 })
    .lean();

  if (!templatePrompt) {
    throw new ParamError('Template prompt not found');
  }

  return templatePrompt;
};

exports.replaceVariablesInPrompt = ({ prompt, variables = {} }) => {
  const processedTemplatePrompt = prompt
    .replace(/\{([a-zA-Z_][a-zA-Z0-9_]*)\}/g, (_, key) => {
      const value = variables[key];
      return typeof value === 'string' || typeof value === 'number' // To prevent return undefined or null in the prompt
        ? String(value)
        : '';
    })
    .trim();

  return processedTemplatePrompt;
};

exports.getProcessedPromptAndModel = async ({
  templateType,
  variables = {},
}) => {
  const templatePrompt = await this.retrievePromptTemplate({
    templateType,
  });

  const processedTemplatePrompt = this.replaceVariablesInPrompt({
    prompt: templatePrompt.prompt,
    variables,
  });

  return {
    prompt: processedTemplatePrompt,
    aiModel: templatePrompt.aiModel,
  };
};

exports.retrievePromptViaMessageIntent = async ({
  messageIntent,
  variables = {},
}) => {
  switch (messageIntent) {
    case MESSAGE_INTENT_TYPE.MESSAGE_INTENT_CLASSIFIER.type:
      return this.getProcessedPromptAndModel({
        templateType: AI_TEMPLATE_PROMPT_TYPE.MESSAGE_INTENT_CLASSIFIER,
        variables,
      });
    case MESSAGE_INTENT_TYPE.PRODUCT_TEMPLATE.type:
      return this.getProcessedPromptAndModel({
        templateType: AI_TEMPLATE_PROMPT_TYPE.PRODUCT_TEMPLATE,
        variables,
      });
    case MESSAGE_INTENT_TYPE.CHANGE_TO_SPECIFIC_PRODUCT_TYPE.type:
      return this.getProcessedPromptAndModel({
        templateType:
          AI_TEMPLATE_PROMPT_TYPE.CHANGE_TO_SPECIFIC_PRODUCT_TYPE,
        variables,
      });
    case MESSAGE_INTENT_TYPE.CHANGE_SOME_INFO_IN_PRODUCT_TEMPLATE.type:
      return this.getProcessedPromptAndModel({
        templateType:
          AI_TEMPLATE_PROMPT_TYPE.CHANGE_SOME_INFO_IN_PRODUCT_TEMPLATE,
        variables,
      });
    case MESSAGE_INTENT_TYPE.CHANGE_PRODUCT_TYPE.type:
      return this.getProcessedPromptAndModel({
        templateType: AI_TEMPLATE_PROMPT_TYPE.CHANGE_PRODUCT_TYPE,
        variables,
      });
    case MESSAGE_INTENT_TYPE.HOW_TO_SELL.type:
      return this.getProcessedPromptAndModel({
        templateType: AI_TEMPLATE_PROMPT_TYPE.HOW_TO_SELL,
        variables: {
          ...variables,
          discountCodeLink: `${NAS_IO_FRONTEND_URL}/portal/promotions`,
        },
      });
    case MESSAGE_INTENT_TYPE.REFINE.type:
      return this.getProcessedPromptAndModel({
        templateType: AI_TEMPLATE_PROMPT_TYPE.REFINE,
        variables,
      });
    case MESSAGE_INTENT_TYPE.ABOUT_NAS_IO.type:
      return this.getProcessedPromptAndModel({
        templateType: AI_TEMPLATE_PROMPT_TYPE.ABOUT_NAS_IO,
        variables,
      });
    case MESSAGE_INTENT_TYPE.ABOUT_DIGITAL_BUSINESS.type:
      return this.getProcessedPromptAndModel({
        templateType: AI_TEMPLATE_PROMPT_TYPE.ABOUT_DIGITAL_BUSINESS,
        variables,
      });
    case MESSAGE_INTENT_TYPE.OTHER_COMMUNITIES_OR_PRODUCTS.type:
      return this.getProcessedPromptAndModel({
        templateType:
          AI_TEMPLATE_PROMPT_TYPE.OTHER_COMMUNITIES_OR_PRODUCTS,
        variables,
      });
    case MESSAGE_INTENT_TYPE.SENSITIVE_QUESTIONS.type:
      return this.getProcessedPromptAndModel({
        templateType: AI_TEMPLATE_PROMPT_TYPE.SENSITIVE_QUESTIONS,
        variables,
      });
    default:
      return this.getProcessedPromptAndModel({
        templateType: AI_TEMPLATE_PROMPT_TYPE.UNKNOWN,
        variables,
      });
  }
};
