const client = require('./client');
const tools = require('./tool');
const { handleStreamEvent } = require('./handler');
const promptService = require('./prompt');
const AIContextBuilder = require('./model/AIContextBuilder');
const {
  AI_MODEL,
  MAX_OUTPUT_TOKEN,
  MESSAGE_INTENT_TYPE,
  ALLOWED_MESSAGE_INTENT_TYPES,
  AI_TEMPLATE_PROMPT_TYPE,
  INPUT_ROLE_TYPE,
  TOOL_CHOICE_TYPE,
} = require('./constants');
const { INTENT_TYPE } = require('../../chat/constants');
const { ParamError } = require('../../../utils/error.util');

const allowedIntentTypes = new Set(
  ALLOWED_MESSAGE_INTENT_TYPES.map((intentType) => intentType.type)
);

async function retrieveMessageIntent({ chatMessage, responseId }) {
  const {
    message,
    communityObjectId,
    chatObjectId,
    intentType,
    actionType,
  } = chatMessage;

  if (
    intentType === INTENT_TYPE.ACTION &&
    allowedIntentTypes.has(actionType)
  ) {
    return actionType;
  }

  if (intentType === INTENT_TYPE.ACTION) {
    throw new ParamError(`Invalid action type ${actionType}`);
  }

  const selectedToolsSchema = tools.retrieveAllToolsSchema(
    MESSAGE_INTENT_TYPE.MESSAGE_INTENT_CLASSIFIER.type
  );

  const { prompt, aiModel } =
    await promptService.getProcessedPromptAndModel({
      templateType: AI_TEMPLATE_PROMPT_TYPE.MESSAGE_INTENT_CLASSIFIER,
      variables: {
        intentList: JSON.stringify(ALLOWED_MESSAGE_INTENT_TYPES),
      },
    });

  const model = aiModel?.model ?? AI_MODEL.MESSAGE_INTENT;

  const response = await client.responses.create({
    model,
    previous_response_id: responseId,
    input: [
      {
        role: INPUT_ROLE_TYPE.SYSTEM,
        content: prompt,
      },
      {
        role: INPUT_ROLE_TYPE.USER,
        content: message,
      },
    ],
    tools: selectedToolsSchema,
    tool_choice: TOOL_CHOICE_TYPE.REQUIRED,
    user: communityObjectId,
    max_output_tokens: MAX_OUTPUT_TOKEN,
    metadata: {
      communityObjectId,
      chatObjectId,
    },
    store: false,
  });

  const responseOutput = response?.output?.[0];

  const functionCallData = await tools.retrieveAndExecuteTool(
    responseOutput?.name,
    responseOutput?.arguments,
    responseOutput?.call_id,
    communityObjectId,
    chatObjectId
  );

  return functionCallData.functionCallOutput;
}

function getRandomItem(arr) {
  return arr[Math.floor(Math.random() * arr.length)];
}

function getRandomProduct(products, durations) {
  const product = getRandomItem(products);
  let label = product;

  if (product === 'challenge') {
    const duration = getRandomItem(durations);
    label = `${duration} ${product}`;
  }

  return label;
}

async function generateInitialPrompt({
  community,
  variables,
  promptTemplate,
}) {
  const prompt = promptService.replaceVariablesInPrompt({
    prompt: promptTemplate.prompt,
    variables,
  });

  const model = promptTemplate.aiModel?.model ?? AI_MODEL.DEFAULT;

  const selectedToolsSchema = tools.retrieveAllToolsSchema(
    MESSAGE_INTENT_TYPE.SHUFFLE_PROMPT.type
  );

  const response = await client.responses.create({
    model,
    input: [
      {
        role: INPUT_ROLE_TYPE.SYSTEM,
        content: prompt,
      },
    ],
    tools: selectedToolsSchema,
    tool_choice: TOOL_CHOICE_TYPE.REQUIRED,
    user: community._id,
    max_output_tokens: MAX_OUTPUT_TOKEN,
    metadata: {
      communityObjectId: community._id,
    },
    store: false,
  });

  const responseOutput = response?.output?.[0];

  const functionCallData = await tools.retrieveAndExecuteTool(
    responseOutput?.name,
    responseOutput?.arguments,
    responseOutput?.call_id,
    community._id
  );

  return functionCallData.functionCallOutput;
}

exports.shuffleInitialPrompt = async ({
  community,
  languagePreference,
}) => {
  const aiContextBuilder = new AIContextBuilder({
    community,
  });

  const [promptTemplate] = await Promise.all([
    promptService.retrievePromptTemplate({
      templateType: AI_TEMPLATE_PROMPT_TYPE.SHUFFLE_INITIAL_PROMPT,
    }),
    aiContextBuilder.buildContext(),
  ]);

  const productWeights = {
    challenge: 4, // Most frequent
    'digital file': 2,
    course: 2,
    event: 2,
    membership: 1,
    '1:1 session': 1, // Least frequent
  };

  const products = Object.entries(productWeights).flatMap(
    ([product, weight]) =>
      !community.isPaidCommunity && product === 'membership'
        ? []
        : Array(weight).fill(product)
  );

  const durations = ['7-day', '14-day', '30-day'];

  const variables = {
    communityInfo: JSON.stringify(aiContextBuilder.communityInfo),
    learnerInfo: JSON.stringify(aiContextBuilder.learnerInfo),
    languagePreference,
  };

  if (aiContextBuilder.learnerSocials) {
    variables.socialInfo = JSON.stringify(aiContextBuilder.learnerSocials);
  }

  if (aiContextBuilder.topProductsInfo) {
    variables.topProductsInfo = JSON.stringify(
      aiContextBuilder.topProductsInfo
    );
  }

  const randomProduct = getRandomProduct(products, durations);

  const newVariables = {
    ...variables,
    product: randomProduct,
  };

  return generateInitialPrompt({
    community,
    variables: newVariables,
    promptTemplate,
  });
};

exports.processAiStream = async ({
  messageCountInfo,
  community,
  chatMessage,
  selectedTemplate,
  responseId,
  abortController,
  onData,
  onDone,
  onError,
}) => {
  const messageIntent = await retrieveMessageIntent({
    chatMessage,
    responseId,
  });

  const isFirstMessage = !responseId;

  const [promptAndModel, initialInstructionAndModel] = await Promise.all([
    promptService.retrievePromptViaMessageIntent({
      messageIntent,
    }),
    isFirstMessage
      ? promptService.getProcessedPromptAndModel({
          templateType: AI_TEMPLATE_PROMPT_TYPE.INITIAL_INSTRUCTION,
        })
      : null,
  ]);

  const input = [
    {
      role: INPUT_ROLE_TYPE.SYSTEM,
      content: promptAndModel.prompt,
    },
  ];

  if (isFirstMessage) {
    const aiContextBuilder = new AIContextBuilder({
      community,
    });
    await aiContextBuilder.buildContext();

    input.push(...aiContextBuilder.toSystemMessages());
  }

  if (selectedTemplate) {
    input.push({
      role: INPUT_ROLE_TYPE.SYSTEM,
      content: `Adjust based on the selected template:\n${JSON.stringify(
        selectedTemplate
      )}`,
    });
  }

  const selectedToolsSchema = tools.retrieveAllToolsSchema(messageIntent);

  input.push({
    role: INPUT_ROLE_TYPE.USER,
    content: chatMessage.message,
  });

  const model = promptAndModel.aiModel?.model ?? AI_MODEL.DEFAULT;

  try {
    const stream = await client.responses.create(
      {
        model,
        previous_response_id: responseId,
        instructions: initialInstructionAndModel?.prompt,
        input,
        stream: true,
        tools: selectedToolsSchema,
        store: true,
        tool_choice:
          selectedToolsSchema.length > 0
            ? TOOL_CHOICE_TYPE.AUTO
            : TOOL_CHOICE_TYPE.NONE,
        include: ['file_search_call.results'],
        user: chatMessage.communityObjectId, // for rate limiting and abuse tracking per user
        max_output_tokens: MAX_OUTPUT_TOKEN,
        metadata: {
          communityObjectId: chatMessage.communityObjectId,
          chatObjectId: chatMessage.chatObjectId,
          messageIntent,
          learnerObjectId: chatMessage.senderLearnerObjectId,
        },
        truncation: 'auto',
      },
      { signal: abortController.signal }
    );

    await handleStreamEvent({
      stream,
      onData,
      onDone,
      onError,
      communityObjectId: chatMessage.communityObjectId,
      chatObjectId: chatMessage.chatObjectId,
      learnerObjectId: chatMessage.senderLearnerObjectId,
      community,
      messageIntent,
      selectedTemplate,
      messageCountInfo,
      abortController,
    });
  } catch (err) {
    onError({ type: 'error', text: err.message });
  }
};

exports.generateChatTitleFromResponse = async ({
  communityObjectId,
  chatObjectId,
  responseId,
}) => {
  const model = AI_MODEL.MESSAGE_INTENT;

  const prompt = `Generate a short, clear, and engaging chat title (max 8 words) that summarizes the main topic or goal based on the user’s first message and the AI’s first response.
  Be specific, avoid generic titles like “Help Needed” or “Conversation”.`;

  const response = await client.responses.create({
    model,
    input: prompt,
    user: communityObjectId,
    previous_response_id: responseId,
    max_output_tokens: MAX_OUTPUT_TOKEN,
    metadata: {
      communityObjectId,
      chatObjectId,
    },
    store: false,
  });

  const responseOutput = response?.output?.[0]?.content?.[0]?.text?.trim();

  return responseOutput;
};
