const FunctionCall = require('../model/FunctionCall');
const FunctionCallResponse = require('../model/FunctionCallResponse');
const {
  FUNCTION_CALL_TYPE,
  ALLOWED_MESSAGE_INTENT_TYPES,
  MESSAGE_INTENT_TYPE,
} = require('../constants');

const functionCallType = FUNCTION_CALL_TYPE.MESSAGE_INTENT_CLASSIFIER;

const messageIntentClassifierFunctionCall = {
  type: 'function',
  name: MESSAGE_INTENT_TYPE.MESSAGE_INTENT_CLASSIFIER.type,
  description: MESSAGE_INTENT_TYPE.MESSAGE_INTENT_CLASSIFIER.description,
  parameters: {
    type: 'object',
    properties: {
      intentType: {
        type: 'string',
        description: 'The intent type of the message',
        enum: ALLOWED_MESSAGE_INTENT_TYPES.map((intent) => intent.type),
      },
    },
    required: ['intentType'],
    additionalProperties: false,
  },
};

const messageIntentClassifierFunctionCallHandler = async (args) => {
  const { intentType } = args;

  return new FunctionCallResponse(functionCallType, intentType);
};

const functionCall = new FunctionCall(
  functionCallType,
  messageIntentClassifierFunctionCall,
  messageIntentClassifierFunctionCallHandler
);

module.exports = functionCall;
