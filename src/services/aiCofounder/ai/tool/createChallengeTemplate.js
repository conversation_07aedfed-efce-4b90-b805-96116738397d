const FunctionCall = require('../model/FunctionCall');
const FunctionCallResponse = require('../model/FunctionCallResponse');
const {
  FUNCTION_CALL_TYPE,
  MESSAGE_INTENT_TYPE,
} = require('../constants');
const {
  QUESTION_TYPE,
  PROGRAM_CHALLENGE_TYPE,
} = require('../../../program/constants');
const { PRICE_TYPE } = require('../../../../constants/common');
const challengeProductTemplateService = require('../productTemplate/challenge.service');

const functionCallType = FUNCTION_CALL_TYPE.CHALLENGE_TEMPLATE;

const generateChallengeTemplateFunctionCall = {
  type: 'function',
  name: 'generate_challenge_template',
  description: 'Generate the challenge',
  parameters: {
    type: 'object',
    properties: {
      title: {
        type: 'string',
        description:
          'The title of the product in the same language as the user’s original input',
      },
      description: {
        type: 'string',
        description:
          'The description of what the participants will do in the same language as the user’s original input.',
      },
      unsplashImageSearchTitle: {
        type: 'string',
        description:
          'A short keyword or phrase that best represents the theme or activity of the challenge. It will be used to search for a relevant image on Unsplash. Keep it concise and descriptive, in the same language as the user’s original input.',
      },
      challengeType: {
        type: 'string',
        description: 'The type of the challenge, time-based or always on',
        enum: Object.values(PROGRAM_CHALLENGE_TYPE),
      },
      challengeDuration: {
        type: 'number',
        description:
          'Total duration in days (number), calculated as checkpointInterval multiplied by the number of checkpoints.',
      },
      checkpointInterval: {
        type: 'number',
        description:
          'Interval between checkpoints in days (number), e.g., 7 for weekly checkpoints.',
      },
      checkpoints: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            title: {
              type: 'string',
              description:
                'The title of the checkpoint perfectly align with the challenge duration in the same language as the user’s original input.',
            },
            description: {
              type: 'string',
              description:
                'The description of the checkpoint in the same language as the user’s original input.',
            },
            // submissionQuestions: {
            //   type: 'array',
            //   items: {
            //     type: 'object',
            //     properties: {
            //       questionText: {
            //         type: 'string',
            //         description:
            //           'The question of the submission form for participants in the same language as the user’s original input.',
            //       },
            //       type: {
            //         type: 'string',
            //         description:
            //           'The answer format of the submission form',
            //         enum: Object.values(QUESTION_TYPE),
            //       },
            //       required: {
            //         type: 'boolean',
            //         description: 'Whether the question is required',
            //       },
            //     },
            //     required: ['questionText', 'type', 'required'],
            //     additionalProperties: false,
            //   },
            //   maxItems: 3,
            //   description:
            //     'The submission questions of the checkpoint if needed',
            // },
          },
          required: ['title', 'description'],
          additionalProperties: false,
        },
        description:
          'The checkpoints of the challenge based on challenge duration and checkpoint interval in the same language as the user’s original input.',
      },
      priceType: {
        type: 'string',
        description: 'The price type of the product',
        enum: Object.values(PRICE_TYPE),
      },
      minAmount: {
        type: 'number',
        description:
          'Minimum payment in local currency in cents which required for priceType FLEXIBLE',
      },
      localCurrency: {
        type: 'string',
        description:
          'currency of the suggested amount, should be the same as community currency',
      },
      suggestedAmount: {
        type: 'number',
        description: 'Recommended payment in local currency in cents',
      },
      possibleActions: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            action: {
              type: 'string',
              description: 'The action to take',
              enum: [
                MESSAGE_INTENT_TYPE.CHANGE_PRODUCT_TYPE.type,
                MESSAGE_INTENT_TYPE.REFINE.type,
                MESSAGE_INTENT_TYPE.HOW_TO_SELL.type,
              ],
            },
          },
          required: ['action'],
          additionalProperties: false,
        },
        minItems: 3,
        maxItems: 3,
        description:
          'The possible actions to take after generating the challenge',
      },
    },
    required: [
      'title',
      'description',
      'unsplashImageSearchTitle',
      'challengeType',
      'challengeDuration',
      'checkpointInterval',
      'checkpoints',
      'priceType',
      'localCurrency',
      'suggestedAmount',
      'possibleActions',
    ],
    additionalProperties: false,
  },
};

const generateChallengeTemplateFunctionCallHandler = async (
  args,
  communityObjectId,
  chatObjectId
) => {
  const {
    title,
    description,
    unsplashImageSearchTitle,
    challengeType,
    challengeDuration,
    checkpointInterval,
    checkpoints,
    priceType,
    minAmount,
    suggestedAmount,
    localCurrency,
    possibleActions,
  } = args;

  const challengeTemplate = {
    title,
    description,
    unsplashImageSearchTitle,
    challengeType,
    challengeDuration,
    checkpointInterval,
    checkpoints,
    priceType,
    minAmount,
    suggestedAmount,
    localCurrency,
    possibleActions,
  };

  const createdChallengeTemplate =
    await challengeProductTemplateService.createTemplate({
      data: challengeTemplate,
      communityObjectId,
      chatObjectId,
    });

  return new FunctionCallResponse(
    functionCallType,
    createdChallengeTemplate
  );
};

const functionCall = new FunctionCall(
  functionCallType,
  generateChallengeTemplateFunctionCall,
  generateChallengeTemplateFunctionCallHandler
);

module.exports = functionCall;
