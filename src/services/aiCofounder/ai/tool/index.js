const createEventTemplate = require('./createEventTemplate');
const createFolderTemplate = require('./createFolderTemplate');
const createCourseTemplate = require('./createCourseTemplate');
const createSessionTemplate = require('./createSessionTemplate');
const createChallengeTemplate = require('./createChallengeTemplate');
const createMembershipTemplate = require('./createMembershipTemplate');
const messageIntentClassifier = require('./messageIntentClassifier');
const shufflePrompt = require('./shufflePrompt');
const nasioKnowledgeBase = require('./nasioKnowledgeBase');

const { MESSAGE_INTENT_TYPE } = require('../constants');

const tools = {
  [MESSAGE_INTENT_TYPE.PRODUCT_TEMPLATE.type]: [
    createEventTemplate,
    createFolderTemplate,
    createCourseTemplate,
    createSessionTemplate,
    createChallengeTemplate,
    createMembershipTemplate,
  ],
  [MESSAGE_INTENT_TYPE.CHANGE_TO_SPECIFIC_PRODUCT_TYPE.type]: [
    createEventTemplate,
    createFolderTemplate,
    createCourseTemplate,
    createSessionTemplate,
    createChallengeTemplate,
    createMembershipTemplate,
  ],
  [MESSAGE_INTENT_TYPE.CHANGE_SOME_INFO_IN_PRODUCT_TEMPLATE.type]: [
    createEventTemplate,
    createFolderTemplate,
    createCourseTemplate,
    createSessionTemplate,
    createChallengeTemplate,
    createMembershipTemplate,
  ],
  [MESSAGE_INTENT_TYPE.REFINE.type]: [
    createEventTemplate,
    createFolderTemplate,
    createCourseTemplate,
    createSessionTemplate,
    createChallengeTemplate,
    createMembershipTemplate,
  ],
  [MESSAGE_INTENT_TYPE.MESSAGE_INTENT_CLASSIFIER.type]: [
    messageIntentClassifier,
  ],
  [MESSAGE_INTENT_TYPE.SHUFFLE_PROMPT.type]: [shufflePrompt],
  [MESSAGE_INTENT_TYPE.ABOUT_NAS_IO.type]: [nasioKnowledgeBase],
};

exports.retrieveAllToolsSchema = (messageIntentType) => {
  if (!tools[messageIntentType]) {
    return [];
  }

  return tools[messageIntentType].map((tool) => tool.schema);
};

exports.retrieveTool = (name) => {
  const selectedTool = Object.values(tools)
    .flat()
    .find((tool) => tool.schema.name === name);

  return selectedTool;
};

exports.retrieveAndExecuteTool = async (
  name,
  args,
  callId,
  communityObjectId,
  chatObjectId = null
) => {
  if (!name || !args || !callId || !communityObjectId) {
    throw new Error('Invalid arguments');
  }

  const tool = this.retrieveTool(name);

  if (!tool) {
    throw new Error(`Invalid or unrecognized intent: ${name}`);
  }

  const argumentInJson = JSON.parse(args);

  const result = await tool.execute(
    argumentInJson,
    communityObjectId,
    chatObjectId
  );

  return {
    functionCallType: result.functionCallType,
    functionCallId: callId,
    functionCallOutput: result.response,
    templateObjectId: result.templateObjectId,
    communityObjectId,
    chatObjectId,
  };
};
