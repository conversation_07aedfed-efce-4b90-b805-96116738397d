const FunctionCall = require('../model/FunctionCall');
const FunctionCallResponse = require('../model/FunctionCallResponse');
const {
  FUNCTION_CALL_TYPE,
  MESSAGE_INTENT_TYPE,
} = require('../constants');
const { PRICE_TYPE } = require('../../../../constants/common');
const eventProductTemplateService = require('../productTemplate/event.service');

const functionCallType = FUNCTION_CALL_TYPE.EVENT_TEMPLATE;

const generateEventTemplateFunctionCall = {
  type: 'function',
  name: 'generate_event_template',
  description: 'Generate the event',
  parameters: {
    type: 'object',
    properties: {
      title: {
        type: 'string',
        description:
          'The title of the product the same language as the user’s original input.',
      },
      description: {
        type: 'string',
        description:
          'The description of the product the same language as the user’s original input.',
      },
      unsplashImageSearchTitle: {
        type: 'string',
        description:
          'A short keyword or phrase that best represents the theme or activity of the event. It will be used to search for a relevant image on Unsplash. Keep it concise and descriptive, in the same language as the user’s original input.',
      },
      durationInMinutes: {
        type: 'number',
        description:
          'Total duration of the event in minutes (number, e.g., 45).',
      },
      location: {
        type: 'string',
        description: 'Location of the event',
        enum: ['online', 'live'],
      },
      eventAgenda: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            title: {
              type: 'string',
              description:
                'The title of the agenda item the same language as the user’s original input.',
            },
            description: {
              type: 'string',
              description:
                'The description of the agenda item the same language as the user’s original input.',
            },
            durationInMinutes: {
              type: 'number',
              description:
                'Total duration of the agenda item in minutes (number, e.g., 45).',
            },
          },
          required: ['title', 'description', 'durationInMinutes'],
          additionalProperties: false,
        },
        minItems: 3,
        maxItems: 5,
        description:
          'The possible agenda of the event the same language as the user’s original input.',
      },
      priceType: {
        type: 'string',
        description: 'The price type of the product',
        enum: Object.values(PRICE_TYPE),
      },
      minAmount: {
        type: 'number',
        description:
          'Minimum payment in local currency in cents which required for priceType FLEXIBLE',
      },
      localCurrency: {
        type: 'string',
        description:
          'Local currency of the suggested amount, must be same as community currency',
      },
      suggestedAmount: {
        type: 'number',
        description: 'Recommended payment in local currency in cents',
      },
      possibleActions: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            action: {
              type: 'string',
              description: 'The action to take',
              enum: [
                MESSAGE_INTENT_TYPE.CHANGE_PRODUCT_TYPE.type,
                MESSAGE_INTENT_TYPE.REFINE.type,
                MESSAGE_INTENT_TYPE.HOW_TO_SELL.type,
              ],
            },
          },
          required: ['action'],
          additionalProperties: false,
        },
        minItems: 3,
        maxItems: 3,
        description:
          'The possible actions to take after generating the event',
      },
    },
    required: [
      'title',
      'description',
      'unsplashImageSearchTitle',
      'durationInMinutes',
      'location',
      'eventAgenda',
      'priceType',
      'localCurrency',
      'suggestedAmount',
      'possibleActions',
    ],
    additionalProperties: false,
  },
};

const generateEventTemplateFunctionCallHandler = async (
  args,
  communityObjectId,
  chatObjectId
) => {
  const {
    title,
    description,
    unsplashImageSearchTitle,
    durationInMinutes,
    location,
    eventAgenda,
    priceType,
    minAmount,
    suggestedAmount,
    localCurrency,
    possibleActions,
  } = args;

  const eventTemplate = {
    title,
    description,
    unsplashImageSearchTitle,
    durationInMinutes,
    location,
    eventAgenda,
    priceType,
    minAmount,
    suggestedAmount,
    localCurrency,
    possibleActions,
  };

  const createdEventTemplate =
    await eventProductTemplateService.createTemplate({
      data: eventTemplate,
      communityObjectId,
      chatObjectId,
    });

  return new FunctionCallResponse(functionCallType, createdEventTemplate);
};

const functionCall = new FunctionCall(
  functionCallType,
  generateEventTemplateFunctionCall,
  generateEventTemplateFunctionCallHandler
);

module.exports = functionCall;
