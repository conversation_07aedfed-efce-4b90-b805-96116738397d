const FunctionCall = require('../model/FunctionCall');
const FunctionCallResponse = require('../model/FunctionCallResponse');
const {
  FUNCTION_CALL_TYPE,
  MESSAGE_INTENT_TYPE,
} = require('../constants');
const { PRICE_TYPE } = require('../../../../constants/common');
const folderProductTemplateService = require('../productTemplate/folder.service');

const functionCallType = FUNCTION_CALL_TYPE.DIGITAL_FILE_TEMPLATE;

const generateDigitalFileTemplateFunctionCall = {
  type: 'function',
  name: 'generate_digital_file_template',
  description: 'Generate the digital file',
  parameters: {
    type: 'object',
    properties: {
      title: {
        type: 'string',
        description:
          'The title of the product in the same language as the user’s original input',
      },
      description: {
        type: 'string',
        description:
          'The description of the product in the same language as the user’s original input.',
      },
      unsplashImageSearchTitle: {
        type: 'string',
        description:
          'A short keyword or phrase that best represents the theme or activity of the product. It will be used to search for a relevant image on Unsplash. Keep it concise and descriptive, in the same language as the user’s original input.',
      },
      sections: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            title: {
              type: 'string',
              description:
                'The title of the section in the same language as the user’s original input.',
            },
            description: {
              type: 'string',
              description:
                'The description of the section in the same language as the user’s original input.',
            },
          },
          required: ['title', 'description'],
          additionalProperties: false,
        },
        description:
          'The sections of the product in the same language as the user’s original input.',
      },
      priceType: {
        type: 'string',
        description: 'The price type of the product',
        enum: Object.values(PRICE_TYPE),
      },
      minAmount: {
        type: 'number',
        description:
          'Minimum payment in local currency in cents which required for priceType FLEXIBLE',
      },
      localCurrency: {
        type: 'string',
        description:
          'Local currency of the suggested amount, must be same as community currency',
      },
      suggestedAmount: {
        type: 'number',
        description: 'Recommended payment in local currency in cents',
      },
      possibleActions: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            action: {
              type: 'string',
              description: 'The action to take',
              enum: [
                MESSAGE_INTENT_TYPE.CHANGE_PRODUCT_TYPE.type,
                MESSAGE_INTENT_TYPE.REFINE.type,
                MESSAGE_INTENT_TYPE.HOW_TO_SELL.type,
              ],
            },
          },
          required: ['action'],
          additionalProperties: false,
        },
        minItems: 3,
        maxItems: 3,
        description:
          'The possible actions to take after generating the digital file',
      },
    },
    required: [
      'title',
      'description',
      'unsplashImageSearchTitle',
      'sections',
      'priceType',
      'localCurrency',
      'suggestedAmount',
      'possibleActions',
    ],
    additionalProperties: false,
  },
};

const generateDigitalFileTemplateFunctionCallHandler = async (
  args,
  communityObjectId,
  chatObjectId
) => {
  const {
    title,
    description,
    unsplashImageSearchTitle,
    sections,
    priceType,
    minAmount,
    suggestedAmount,
    localCurrency,
    possibleActions,
  } = args;

  const digitalFileTemplate = {
    title,
    description,
    unsplashImageSearchTitle,
    sections,
    priceType,
    minAmount,
    suggestedAmount,
    localCurrency,
    possibleActions,
  };

  const createdDigitalFileTemplate =
    await folderProductTemplateService.createTemplate({
      data: digitalFileTemplate,
      communityObjectId,
      chatObjectId,
    });

  return new FunctionCallResponse(
    functionCallType,
    createdDigitalFileTemplate
  );
};

const functionCall = new FunctionCall(
  functionCallType,
  generateDigitalFileTemplateFunctionCall,
  generateDigitalFileTemplateFunctionCallHandler
);

module.exports = functionCall;
