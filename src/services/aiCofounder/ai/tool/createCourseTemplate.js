const FunctionCall = require('../model/FunctionCall');
const FunctionCallResponse = require('../model/FunctionCallResponse');
const {
  FUNCTION_CALL_TYPE,
  MESSAGE_INTENT_TYPE,
} = require('../constants');
const { PRICE_TYPE } = require('../../../../constants/common');
const courseProductTemplateService = require('../productTemplate/course.service');

const functionCallType = FUNCTION_CALL_TYPE.COURSE_TEMPLATE;

const generateCourseTemplateFunctionCall = {
  type: 'function',
  name: 'generate_course_template',
  description: 'Generate the course',
  parameters: {
    type: 'object',
    properties: {
      title: {
        type: 'string',
        description:
          'The title of the product in the same language as the user’s original input',
      },
      description: {
        type: 'string',
        description:
          'The description of the product in the same language as the user’s original input.',
      },
      unsplashImageSearchTitle: {
        type: 'string',
        description:
          'A short keyword or phrase that best represents the theme or activity of the course. It will be used to search for a relevant image on Unsplash. Keep it concise and descriptive, in the same language as the user’s original input.',
      },
      sections: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            title: {
              type: 'string',
              description:
                'The title of the section in the same language as the user’s original input.',
            },
            description: {
              type: 'string',
              description:
                'The description of the section in the same language as the user’s original input.',
            },
          },
          required: ['title', 'description'],
          additionalProperties: false,
        },
        description:
          'The sections of the course in the same language as the user’s original input.',
      },
      priceType: {
        type: 'string',
        description: 'The price type of the product',
        enum: Object.values(PRICE_TYPE),
      },
      minAmount: {
        type: 'number',
        description:
          'Minimum payment in local currency in cents which required for priceType FLEXIBLE',
      },
      localCurrency: {
        type: 'string',
        description:
          'Local currency of the suggested amount, must be same as community currency',
      },
      suggestedAmount: {
        type: 'number',
        description: 'Recommended payment in local currency in cents',
      },
      possibleActions: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            action: {
              type: 'string',
              description: 'The action to take',
              enum: [
                MESSAGE_INTENT_TYPE.CHANGE_PRODUCT_TYPE.type,
                MESSAGE_INTENT_TYPE.REFINE.type,
                MESSAGE_INTENT_TYPE.HOW_TO_SELL.type,
              ],
            },
          },
          required: ['action'],
          additionalProperties: false,
        },
        minItems: 3,
        maxItems: 3,
        description:
          'The possible actions to take after generating the course',
      },
    },
    required: [
      'title',
      'description',
      'unsplashImageSearchTitle',
      'sections',
      'priceType',
      'localCurrency',
      'suggestedAmount',
      'possibleActions',
    ],
    additionalProperties: false,
  },
};

const generateCourseTemplateFunctionCallHandler = async (
  args,
  communityObjectId,
  chatObjectId
) => {
  const {
    title,
    description,
    unsplashImageSearchTitle,
    sections,
    priceType,
    minAmount,
    suggestedAmount,
    localCurrency,
    possibleActions,
  } = args;

  const courseTemplate = {
    title,
    description,
    unsplashImageSearchTitle,
    sections,
    priceType,
    minAmount,
    suggestedAmount,
    localCurrency,
    possibleActions,
  };

  const createdCourseTemplate =
    await courseProductTemplateService.createTemplate({
      data: courseTemplate,
      communityObjectId,
      chatObjectId,
    });

  return new FunctionCallResponse(functionCallType, createdCourseTemplate);
};

const functionCall = new FunctionCall(
  functionCallType,
  generateCourseTemplateFunctionCall,
  generateCourseTemplateFunctionCallHandler
);

module.exports = functionCall;
