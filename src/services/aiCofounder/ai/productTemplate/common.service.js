const { TEMPLATE_SOURCE_TYPE } = require('../../../../constants/common');
const TemplateLibraryModel = require('../../../../models/getInspired/templateLibrary.model');
const unsplashRpc = require('../../../../rpc/unsplash.rpc');
const { ParamError } = require('../../../../utils/error.util');

exports.retrieveTemplate = async ({
  communityObjectId,
  templateObjectId,
}) => {
  const template = await TemplateLibraryModel.findOne({
    _id: templateObjectId,
    communityObjectId,
  }).lean();

  if (!template) {
    throw new ParamError('Template not found');
  }

  return template;
};

exports.retrieveTemplateViaVersion = async ({
  communityObjectId,
  chatObjectId,
  version,
}) => {
  const template = await TemplateLibraryModel.findOne({
    sourceObjectId: chatObjectId,
    source: TEMPLATE_SOURCE_TYPE.AI_COFOUNDER,
    communityObjectId,
    version,
  }).lean();

  if (!template) {
    throw new ParamError('Template not found');
  }

  return template;
};

exports.addNewTemplateVersion = async ({
  communityObjectId,
  chatObjectId,
  version,
  session = undefined,
}) => {
  const [template, latestTemplate] = await Promise.all([
    TemplateLibraryModel.findOne(
      {
        sourceObjectId: chatObjectId,
        source: TEMPLATE_SOURCE_TYPE.AI_COFOUNDER,
        communityObjectId,
        version,
      },
      { createdAt: 0, updatedAt: 0 }
    ).lean(),
    TemplateLibraryModel.findOne(
      {
        sourceObjectId: chatObjectId,
        source: TEMPLATE_SOURCE_TYPE.AI_COFOUNDER,
        communityObjectId,
      },
      { version: 1 }
    )
      .sort({ version: -1 })
      .lean(),
  ]);

  if (!template || !latestTemplate) {
    throw new ParamError('Template not found');
  }

  const { _id: templateObjectId, ...rest } = template;

  const newTemplate = {
    ...rest,
    version: latestTemplate.version + 1,
    copyFrom: {
      templateObjectId,
      version: template.version,
    },
  };

  const [createdTemplate] = await TemplateLibraryModel.create(
    [newTemplate],
    { session }
  );

  return {
    newTemplate: createdTemplate.toObject(),
    oldTemplate: template,
  };
};

exports.retrieveAiCofounderTemplatesCache = async ({
  communityObjectId,
  templateObjectIds,
}) => {
  if (!templateObjectIds.length) {
    return new Map();
  }

  const templates = await TemplateLibraryModel.find({
    communityObjectId,
    source: TEMPLATE_SOURCE_TYPE.AI_COFOUNDER,
    isAIGenerated: true,
    _id: { $in: templateObjectIds },
  }).lean();

  const templatesCache = templates.reduce((acc, template) => {
    acc.set(template._id.toString(), template);
    return acc;
  }, new Map());

  return templatesCache;
};

exports.createTemplate = async ({
  chatObjectId,
  communityObjectId,
  template,
}) => {
  let version = 1;

  const newTemplate = { ...template };

  const unsplashImages = await unsplashRpc.retrieveUnsplashImages(
    newTemplate.metadata.unsplashImageSearchTitle,
    1
  );

  if (unsplashImages?.length) {
    newTemplate.thumbnailImgSrc = unsplashImages[0].url;
  }

  const latestTemplate = await TemplateLibraryModel.findOne(
    {
      sourceObjectId: chatObjectId,
      source: TEMPLATE_SOURCE_TYPE.AI_COFOUNDER,
      communityObjectId,
    },
    { version: 1 }
  )
    .sort({ version: -1 })
    .lean();

  if (latestTemplate) {
    version = latestTemplate.version + 1;
  }

  const createdTemplate = await TemplateLibraryModel.create({
    ...newTemplate,
    version,
    sourceObjectId: chatObjectId,
    communityObjectId,
  });

  return createdTemplate.toObject();
};

exports.updateTemplate = async ({
  communityObjectId,
  templateObjectId,
  thumbnailImgSrc,
  metadata,
  pricingConfig,
}) => {
  const updatedTemplate = await TemplateLibraryModel.findOneAndUpdate(
    {
      _id: templateObjectId,
      communityObjectId,
    },
    {
      $set: {
        thumbnailImgSrc,
        metadata,
        pricingConfig,
      },
    },
    { new: true }
  ).lean();

  return updatedTemplate;
};

exports.retrieveLatestTemplate = async ({
  communityObjectId,
  chatObjectId,
}) => {
  const latestTemplate = await TemplateLibraryModel.findOne({
    sourceObjectId: chatObjectId,
    source: TEMPLATE_SOURCE_TYPE.AI_COFOUNDER,
    communityObjectId,
  })
    .sort({ version: -1 })
    .lean();

  return latestTemplate;
};
