const {
  AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES,
  PRICE_TYPE,
  TEMPLATE_SOURCE_TYPE,
} = require('../../../../constants/common');
const commonService = require('./common.service');

exports.createTemplate = async ({
  data,
  communityObjectId,
  chatObjectId,
}) => {
  const {
    title,
    description,
    unsplashImageSearchTitle,
    checkpointInterval,
    challengeType,
    checkpoints,
    challengeDuration,
    priceType,
    minAmount,
    localCurrency,
    suggestedAmount,
    predictedSales,
    possibleActions,
  } = data;

  const template = {
    communityObjectId,
    type: AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.CHALLENGE,
    isAIGenerated: true,
    thumbnailImgSrc: '',
    version: 1,
    predictedSales,
    localCurrency,
    pricingConfig: {
      priceType,
      suggestedAmount,
    },
    metadata: {
      title,
      description,
      checkpointInterval,
      challengeType,
      checkpoints,
      challengeDuration,
      unsplashImageSearchTitle,
    },
    source: TEMPLATE_SOURCE_TYPE.AI_COFOUNDER,
    sourceObjectId: chatObjectId,
  };

  if (priceType === PRICE_TYPE.FLEXIBLE) {
    template.pricingConfig.minAmount = minAmount;
  }

  const createdTemplate = await commonService.createTemplate({
    chatObjectId,
    communityObjectId,
    template,
  });

  return {
    template: createdTemplate,
    possibleActions,
  };
};
