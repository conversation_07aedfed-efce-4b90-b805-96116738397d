const { EVENT_TYPE, MESSAGE_INTENT_TYPE } = require('../constants');
const {
  AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES,
} = require('../../../../constants/common');
const { handleTextEvent } = require('./handleTextEvent');
const { handleFunctionCallEvent } = require('./handleFunctionCall');
const { handleFollowUp } = require('./handleFollowup');
const toolService = require('../tool');

const AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES_WITH_RANKING = {
  [AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.CHALLENGE]: 1,
  [AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.EVENT]: 2,
  [AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.COURSE]: 3,
  [AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.FOLDER]: 4,
  [AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.MEMBERSHIP]: 5,
  [AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.SESSION]: 6,
};

function retrievePossibleActions({
  community,
  messageIntent,
  selectedTemplate,
  functionCallData,
}) {
  if (functionCallData) {
    return functionCallData.functionCallOutput.possibleActions;
  }

  if (messageIntent === MESSAGE_INTENT_TYPE.CHANGE_PRODUCT_TYPE.type) {
    const templateType = selectedTemplate?.type;

    const hasSetupPaidSubscriptions = community.isPaidCommunity;

    const possibleActions = Object.values(
      AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES
    )
      .filter(
        (type) =>
          type !== templateType &&
          (!hasSetupPaidSubscriptions ||
            type !== AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES.MEMBERSHIP)
      )
      .map((type) => ({
        action: MESSAGE_INTENT_TYPE.CHANGE_TO_SPECIFIC_PRODUCT_TYPE.type,
        targetProductType: type,
      }))
      .sort(
        (a, b) =>
          AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES_WITH_RANKING[
            a.targetProductType
          ] -
          AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES_WITH_RANKING[
            b.targetProductType
          ]
      );

    return possibleActions;
  }

  return null;
}

exports.handleStreamEvent = async ({
  stream,
  onData,
  onDone,
  onError,
  communityObjectId,
  chatObjectId,
  learnerObjectId,
  community,
  messageIntent,
  selectedTemplate,
  messageCountInfo,
  abortController,
}) => {
  let lastResponseId;
  let templateObjectId;
  let usage;
  let fullText = '';
  let hasSentMessageCount = false;
  const functionCallDataList = [];

  try {
    for await (const event of stream) {
      if (event.type === EVENT_TYPE.TEXT.event) {
        fullText += await handleTextEvent(event, onData);

        if (!hasSentMessageCount) {
          // eslint-disable-next-line no-param-reassign
          messageCountInfo.count += 1;
          onData({
            type: EVENT_TYPE.MESSAGE_COUNT.type,
            messageCountInfo,
          });
          hasSentMessageCount = true;
        }
      } else if (
        event.type === EVENT_TYPE.FILE_SEARCH_START.event &&
        event.item.type === EVENT_TYPE.FILE_SEARCH_START.itemType
      ) {
        onData({
          type: EVENT_TYPE.FILE_SEARCH_START.type,
        });
      } else if (
        event.type === EVENT_TYPE.FUNCTION_CALL_START.event &&
        event.item.type === EVENT_TYPE.FUNCTION_CALL_START.itemType
      ) {
        const tool = toolService.retrieveTool(event.item.name);
        onData({
          type: EVENT_TYPE.FUNCTION_CALL_START.type,
          functionCallType: tool?.type,
        });
      } else if (
        event.type === EVENT_TYPE.FUNCTION_CALL_READY.event &&
        event.item.type === EVENT_TYPE.FUNCTION_CALL_READY.itemType
      ) {
        const functionCallData = await handleFunctionCallEvent(
          event,
          communityObjectId,
          chatObjectId
        );
        functionCallDataList.push(functionCallData);
      } else if (event.type === EVENT_TYPE.RESPONSE_COMPLETED.event) {
        lastResponseId = event.response.id;
        usage = event.response.usage;

        if (functionCallDataList.length > 0) {
          const latestFunctionCallData =
            functionCallDataList[functionCallDataList.length - 1];

          if (!latestFunctionCallData) {
            return;
          }

          templateObjectId =
            latestFunctionCallData.functionCallOutput.template._id;

          await handleFollowUp({
            lastResponseId,
            learnerObjectId,
            functionCallData: latestFunctionCallData,
            functionCallDataList,
            messageIntent,
            messageCountInfo,
            functionCallUsage: usage,
            abortController,
            onData,
            onDone,
          });

          return;
        }
      }
    }

    const latestFunctionCallData =
      functionCallDataList[functionCallDataList.length - 1];

    const possibleActions = retrievePossibleActions({
      community,
      messageIntent,
      selectedTemplate,
      functionCallData: latestFunctionCallData,
    });

    await onDone({
      type: EVENT_TYPE.RESPONSE_COMPLETED.type,
      responseId: lastResponseId,
      fullText,
      usage,
      templateObjectId,
      messageIntent,
      possibleActions,
    });
  } catch (error) {
    onError({ type: 'error', text: error.message });
  }
};
