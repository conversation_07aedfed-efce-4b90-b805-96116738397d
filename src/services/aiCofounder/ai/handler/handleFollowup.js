const client = require('../client');
const {
  EVENT_TYPE,
  FUNCTION_CALL_TYPE,
  AI_MODEL,
  MAX_OUTPUT_TOKEN,
  INPUT_ROLE_TYPE,
  FOLLOW_UP_TEMPLATE_TYPE_MAPPER,
} = require('../constants');
const { handleTextEvent } = require('./handleTextEvent');
const promptService = require('../prompt');

function transformTemplate(template) {
  // Deep clone the entire template object to avoid mutating the original.
  // Note: Using the spread operator ({ ...template }) only creates a shallow copy,
  // meaning nested objects like `pricingConfig` would still reference the same objects
  // as in the original template. Modifying them would affect the original object.
  // structuredClone performs a full deep copy, so nested objects are duplicated as well.
  // eslint-disable-next-line no-undef
  const transformedTemplate = structuredClone(template);

  // For AI to response in dollar rather than cents
  if (transformedTemplate.pricingConfig.minAmount) {
    transformedTemplate.pricingConfig.minAmount /= 100;
  }

  // For AI to response in dollar rather than cents
  if (transformedTemplate.pricingConfig.suggestedAmount) {
    transformedTemplate.pricingConfig.suggestedAmount /= 100;
  }

  return transformedTemplate;
}

exports.handleFollowUp = async ({
  lastResponseId,
  learnerObjectId,
  functionCallData,
  functionCallDataList,
  messageIntent,
  messageCountInfo,
  abortController,
  functionCallUsage,
  onData,
  onDone,
}) => {
  if (!functionCallData) {
    return;
  }

  const input = [];

  let model = AI_MODEL.FOLLOW_UP;

  const existsFunctionCallType = Object.values(
    FUNCTION_CALL_TYPE
  ).includes(functionCallData.functionCallType);

  const followUpTemplateType =
    FOLLOW_UP_TEMPLATE_TYPE_MAPPER[messageIntent];

  if (existsFunctionCallType && followUpTemplateType) {
    const { prompt, aiModel } =
      await promptService.getProcessedPromptAndModel({
        templateType: followUpTemplateType,
      });

    input.push({
      role: INPUT_ROLE_TYPE.SYSTEM,
      content: prompt,
    });

    model = aiModel?.model ?? AI_MODEL.FOLLOW_UP;
  }

  for (const selectedFunctionCallData of functionCallDataList) {
    const transformedTemplate = transformTemplate(
      selectedFunctionCallData.functionCallOutput.template
    );

    input.push({
      type: 'function_call_output',
      call_id: selectedFunctionCallData.functionCallId,
      output: JSON.stringify(transformedTemplate),
    });
  }

  const followUpStream = await client.responses.create(
    {
      model,
      previous_response_id: lastResponseId,
      input,
      stream: true,
      user: functionCallData.communityObjectId,
      max_output_tokens: MAX_OUTPUT_TOKEN,
      metadata: {
        communityObjectId: functionCallData.communityObjectId,
        chatObjectId: functionCallData.chatObjectId,
        messageIntent,
        learnerObjectId,
      },
      truncation: 'auto',
    },
    { signal: abortController.signal }
  );

  const templateObjectId =
    functionCallData.functionCallOutput.template._id;

  let fullText = '';
  let responseId;
  let usage;
  let hasSentMessageCount = false;

  for await (const event of followUpStream) {
    if (event.type === EVENT_TYPE.TEXT.event) {
      fullText += await handleTextEvent(event, onData);
      if (!hasSentMessageCount) {
        // eslint-disable-next-line no-param-reassign
        messageCountInfo.count += 1;
        onData({
          type: EVENT_TYPE.MESSAGE_COUNT.type,
          messageCountInfo,
        });
        hasSentMessageCount = true;
      }
    } else if (event.type === EVENT_TYPE.RESPONSE_COMPLETED.event) {
      responseId = event.response.id;
      usage = event.response.usage;

      onData({
        type: EVENT_TYPE.FUNCTION_CALL_READY.type,
        metadata: functionCallData.functionCallOutput.template,
        functionCallType: functionCallData.functionCallType,
        templateObjectId,
      });
    }
  }

  await onDone({
    type: EVENT_TYPE.RESPONSE_COMPLETED.type,
    responseId,
    fullText,
    usage,
    functionCallUsage,
    templateObjectId,
    messageIntent,
    possibleActions: functionCallData.functionCallOutput.possibleActions,
  });
};
