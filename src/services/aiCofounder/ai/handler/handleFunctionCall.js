const { EVENT_TYPE } = require('../constants');
const tools = require('../tool');
const logger = require('../../../logger.service');

exports.handleFunctionCallEvent = async (
  event,
  communityObjectId,
  chatObjectId
) => {
  if (event.item.type !== EVENT_TYPE.FUNCTION_CALL_READY.itemType)
    return null;

  const { name, arguments: argsRaw, status, call_id: callId } = event.item;

  if (status !== 'completed') {
    logger.warn(`Function call ${name} not completed.`);
    return null;
  }

  const functionCallData = await tools.retrieveAndExecuteTool(
    name,
    argsRaw,
    callId,
    communityObjectId,
    chatObjectId
  );

  return functionCallData;
};
