const messageService = require('./message.service');
const chatCommonService = require('../chat/common.service');
const productTemplateCommonService = require('./ai/productTemplate/common.service');
const aiService = require('./ai');
const commonService = require('./common.service');
const PrimaryMongooseConnection = require('../../rpc/primaryMongooseConnection');
const chatService = require('./chat.service');

async function retrieveSelectedTemplate({
  communityObjectId,
  templateObjectId,
  chatObjectId,
}) {
  if (templateObjectId) {
    return productTemplateCommonService.retrieveTemplate({
      communityObjectId,
      templateObjectId,
    });
  }

  return productTemplateCommonService.retrieveLatestTemplate({
    communityObjectId,
    chatObjectId,
  });
}

exports.sendMessage = async ({
  community,
  chat,
  message,
  attachments,
  intentType,
  responseId,
  learnerObjectId,
  templateObjectId,
  actionType,
  abortController,
  onData,
  onDone,
  onError,
}) => {
  chatCommonService.validateChatIsActive(chat);

  const messageCountInfo = await messageService.retrieveMessageCountInfo({
    communityObjectId: community._id,
    community,
  });

  messageService.validateMessageCountWithinLimit({ messageCountInfo });

  const [chatMessage, selectedTemplate] = await Promise.all([
    messageService.addMessage({
      community,
      chat,
      message,
      learnerObjectId,
      attachments,
      intentType,
      isAiGenerated: false,
      actionType,
    }),
    retrieveSelectedTemplate({
      communityObjectId: community._id,
      templateObjectId,
      chatObjectId: chat._id,
    }),
  ]);

  await aiService.processAiStream({
    messageCountInfo,
    community,
    chatMessage,
    selectedTemplate,
    responseId,
    abortController,
    onData,
    onDone,
    onError,
  });
};

exports.addAiMessage = async ({
  community,
  chat,
  message,
  learnerObjectId,
  usage,
  functionCallUsage,
  responseId,
  templateObjectId,
  possibleActions,
  messageIntent,
}) => {
  const metadata = {
    usage,
    functionCallUsage,
    responseId,
    templateObjectId,
    messageIntent,
    possibleActions,
  };

  const chatMessage = await messageService.addMessage({
    community,
    chat,
    message,
    learnerObjectId,
    isAiGenerated: true,
    metadata,
  });

  return chatMessage;
};

exports.retrieveTemplateViaVersion = async ({
  communityObjectId,
  chatObjectId,
  version,
}) => {
  return productTemplateCommonService.retrieveTemplateViaVersion({
    communityObjectId,
    chatObjectId,
    version,
  });
};

exports.addNewTemplateVersion = async ({
  community,
  chat,
  version,
  learnerObjectId,
}) => {
  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  try {
    const { newTemplate, oldTemplate } =
      await productTemplateCommonService.addNewTemplateVersion({
        communityObjectId: community._id,
        chatObjectId: chat._id,
        version,
        session,
      });

    const templateMessage =
      await messageService.retrieveMessageViaTemplate({
        templateObjectId: oldTemplate._id,
        communityObjectId: community._id,
        chatObjectId: chat._id,
      });

    const { responseId, messageIntent, possibleActions } =
      templateMessage.metadata;

    const metadata = {
      responseId,
      templateObjectId: newTemplate._id,
      messageIntent,
      possibleActions,
      copyFrom: newTemplate.copyFrom,
    };

    const createdChatMessage = await messageService.addMessage({
      community,
      chat,
      message: '',
      learnerObjectId,
      isAiGenerated: false,
      isRestoredVersion: true,
      metadata,
      session,
    });

    await session.commitTransaction();

    createdChatMessage.template = newTemplate;

    return createdChatMessage;
  } catch (err) {
    await session.abortTransaction();
    throw err;
  } finally {
    await session.endSession();
  }
};

exports.updateTemplate = async ({
  communityObjectId,
  templateObjectId,
  thumbnailImgSrc,
  metadata,
  pricingConfig,
}) => {
  return productTemplateCommonService.updateTemplate({
    communityObjectId,
    templateObjectId,
    thumbnailImgSrc,
    metadata,
    pricingConfig,
  });
};

exports.shuffleInitialPrompt = async ({
  communityObjectId,
  languagePreference,
}) => {
  const community = await commonService.retrieveActiveCommunity(
    communityObjectId
  );

  const variables = await aiService.shuffleInitialPrompt({
    community,
    languagePreference,
  });

  return { variables };
};

exports.updateChatTitleWithSummary = async ({
  community,
  chat,
  responseId,
}) => {
  if (!responseId) return;

  const title = await aiService.generateChatTitleFromResponse({
    communityObjectId: community._id,
    chatObjectId: chat._id,
    responseId,
  });

  if (!title) {
    return;
  }

  await chatService.updateChatTitle({
    chatObjectId: chat._id,
    title,
  });
};
