const ChatModel = require('../../models/chat/chat.model');
const { CHAT_SOURCE_TYPE, CHAT_STATUS } = require('../chat/constants');

exports.retrieveChats = async ({
  communityObjectId,
  batchSize,
  lastObjectId,
  messageCountInfo,
}) => {
  const matchFilter = {
    communityObjectId,
    status: CHAT_STATUS.ACTIVE,
    source: CHAT_SOURCE_TYPE.AI_COFOUNDER,
  };

  if (lastObjectId) {
    matchFilter['lastSentMessage.messageObjectId'] = { $lt: lastObjectId };
  }

  const chats = await ChatModel.find(matchFilter)
    .sort({ 'lastSentMessage.messageObjectId': -1 })
    .limit(batchSize)
    .lean();

  return {
    chats,
    metadata: {
      lastObjectId:
        chats[chats.length - 1]?.lastSentMessage?.messageObjectId,
      messageCountInfo,
    },
  };
};

exports.updateChatLastSendAndReadMessage = async ({
  chatMessage,
  session = undefined,
}) => {
  await ChatModel.updateOne(
    { _id: chatMessage.chatObjectId },
    {
      $set: {
        lastSentMessage: {
          messageObjectId: chatMessage._id,
          datetime: chatMessage.createdAt,
        },
        lastReadMessageByCommunity: {
          messageObjectId: chatMessage._id,
          datetime: chatMessage.createdAt,
        },
      },
    },
    { session }
  );
};

exports.updateChatTitle = async ({ chatObjectId, title }) => {
  return ChatModel.updateOne({ _id: chatObjectId }, { $set: { title } });
};
