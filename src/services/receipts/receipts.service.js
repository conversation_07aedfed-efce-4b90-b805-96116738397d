const {
  FILEASSETBUCKET,
  FILEASSETBUCKET_CLOUDFRONT_BASE_URL,
} = require('../../constants/common');
const { uploadToS3 } = require('../../utils/s3');
const { createReceipt } = require('./receiptGenerationPDF.service');
const { getReceiptFilePath } = require('./receiptUtils.service');
const RawTransactionModel = require('../../models/rawTransaction.model');

const generateReceiptConfig = ({
  purchasedId,
  purchaseType,
  entityObjectId,
  communityObjectId,
  learnerObjectId,
  generateReceipt,
}) => {
  if (!generateReceipt || !purchasedId) {
    return {};
  }

  return {
    purchasedId,
    purchaseType,
    entityObjectId,
    communityObjectId,
    learnerObjectId,
    generateReceipt,
  };
};

const generateReceipt = async (createReceiptSchema) => {
  const { receiptId } = createReceiptSchema;
  const languages = ['en', 'pt-br', 'es-mx', 'ja'];

  // Generate receipts concurrently for all languages.
  const receiptBuffers = await Promise.all(
    languages.map((lang) => createReceipt(createReceiptSchema, lang))
  );

  // Map each language to its receipt file path.
  const receiptFilePaths = languages.reduce((paths, lang) => {
    // eslint-disable-next-line no-param-reassign
    paths[lang] = getReceiptFilePath({
      languagePreference: lang,
      receiptId,
    });
    return paths;
  }, {});

  // Upload all receipts concurrently and map results by language.
  const uploadResults = await Promise.all(
    languages.map((lang, index) =>
      uploadToS3({
        bucketName: FILEASSETBUCKET,
        filePath: receiptFilePaths[lang],
        buffer: receiptBuffers[index],
        contentType: 'application/pdf',
        accessLevel: 'public-read',
      })
    )
  );

  // Construct CloudFront links using the uploaded keys.
  const cloudfrontLinks = languages.reduce((links, lang, index) => {
    // eslint-disable-next-line no-param-reassign
    links[
      lang
    ] = `${FILEASSETBUCKET_CLOUDFRONT_BASE_URL}/${uploadResults[index].Key}`;
    return links;
  }, {});

  return cloudfrontLinks;
};

const regenerateReceipt = async ({
  rawTransactionObjectId,
  saveToRawTransaction,
}) => {
  const rawTransaction = await RawTransactionModel.findById(
    rawTransactionObjectId
  ).lean();

  const receiptGeneratedPayload =
    rawTransaction?.receiptInfo?.receiptGeneratedPayload;

  if (!receiptGeneratedPayload) {
    throw new Error('Receipt generated payload not found');
  }

  const receiptGenerated = await generateReceipt(receiptGeneratedPayload);

  if (saveToRawTransaction) {
    await RawTransactionModel.updateOne(
      {
        _id: rawTransactionObjectId,
      },
      {
        'receiptInfo.receiptLink': receiptGenerated,
      }
    );
  }

  return receiptGenerated;
};

module.exports = {
  generateReceiptConfig,
  generateReceipt,
  regenerateReceipt,
};
