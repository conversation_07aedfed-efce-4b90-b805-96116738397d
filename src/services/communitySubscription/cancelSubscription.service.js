const jwt = require('jsonwebtoken');
const axios = require('axios');
const httpContext = require('express-http-context');
const {
  MAIN_PAYMENT_BACKEND_URL,
  JWT_SECRET_KEY,
} = require('../../config');
const logger = require('../logger.service');
const CommunitySubscriptionsModel = require('../../communitiesAPI/models/communitySubscriptions.model');
const CommunityModel = require('../../communitiesAPI/models/community.model');
const WhatsappParticipantsModel = require('../../communitiesAPI/models/whatsappParticipants.model');
const LearnerModel = require('../../models/learners.model');
const CommunityNotificationService = require('../communityNotification');
const {
  COMMUNITY_SUBSCRIPTION_STATUSES,
  PAYMENT_PROVIDER,
  TRANSACTION_TYPE,
  PURCHASE_TYPE,
  communityEnrolmentStatuses,
  CONFIG_TYPES,
  EBANX_INTEGRATION_KEY,
  SUBSCRIPTION_ACTION_TYPE,
} = require('../../constants/common');
const {
  NASIO_AI_SCHOOL_COMMUNITY_CODE,
  ENTITY_TYPE: PLAN_TYPE,
} = require('../plan/constants');
const PaymentBackendRpc = require('../../rpc/paymentBackend');
const { getConfigByType } = require('../config.service');
const {
  InternalError,
  ResourceNotFoundError,
} = require('../../utils/error.util');
const RawTransactionModel = require('../../models/rawTransaction.model');
const actionEventService = require('../actionEvent');
const planOrderService = require('../plan/planOrder.service');
const schedulerHandler = require('../../handlers/scheduler.handler');
const { SUBSCRIPTION_UPDATER_QUEUE_ARN } = require('../../config');
const { SCHEDULER_GROUP_NAME } = require('../../constants/eventBridge');

async function scheduleSubscriptionCancelledEvent(subscription) {
  const requestId = httpContext.get('reqId');

  const data = {
    purchaseTransactionObjectId: subscription.communitySignupId,
    actionType: SUBSCRIPTION_ACTION_TYPE.CANCEL,
    paymentProvider: subscription.paymentProvider,
    eventData: {
      created: Math.floor(subscription.cancelledAt.getTime() / 1000),
      cancelledAt: subscription.cancelledAt,
      scheduledCancellation: false,
      unsubscribedAt: subscription.unsubscribedAt,
    },
  };

  const message = {
    data,
    requestor: 'Learning Portal Backend',
    requestId,
  };

  await schedulerHandler.createOneTimeScheduleForSqs({
    sqsQueueArn: SUBSCRIPTION_UPDATER_QUEUE_ARN,
    eventTimeString: subscription.cancelledAt.toISOString(),
    scheduleName: `${subscription._id}_${requestId}`,
    groupName: SCHEDULER_GROUP_NAME.SUBSCRIPTION_UPDATER_QUEUE,
    message,
  });
}

async function cancelPaidSubscriptionViaStripe(
  stripeSubscriptionId,
  cancellationReason,
  ip
) {
  // Sample payload for the token
  const payload = {
    stripeSubscriptionId,
  };

  // Options for the token
  const options = {
    algorithm: 'HS256', // HMAC SHA-256
    expiresIn: '1h', // Optional: Token expiration time
  };

  // Creating the token
  const token = jwt.sign(payload, JWT_SECRET_KEY, options);

  // TODO: change MBE to payment backend to cancel subscription
  const result = await axios
    .post(
      `${MAIN_PAYMENT_BACKEND_URL}/api/v1/community-cancel`,
      {
        cancellationReason,
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'x-forwarded-for': ip,
        },
      }
    )
    .then((res) => res.data);

  logger.info(`community-cancel result: ${JSON.stringify(result)}`);
}

async function updateSubscriptionWebhookTriggered(
  subscriptionObjectId,
  cancellationReason
) {
  await CommunitySubscriptionsModel.findByIdAndUpdate(
    subscriptionObjectId,
    { webhookTriggered: false, cancellationReason }
  );
}

async function removeSubscriptionWebhookTriggered(subscriptionObjectId) {
  await CommunitySubscriptionsModel.findByIdAndUpdate(
    subscriptionObjectId,
    { $unset: { webhookTriggered: 1, cancellationReason: 1 } }
  );
}

async function updateCancelledSubscriptionStatus(
  subscriptionObjectId,
  cancellationReason,
  cancelledAt
) {
  const currentDateTime = new Date();

  const updateQuery = {
    status: COMMUNITY_SUBSCRIPTION_STATUSES.CANCELLED,
    cancellationReason,
    unsubscribedAt: currentDateTime,
    cancelledAt,
  };

  return CommunitySubscriptionsModel.findByIdAndUpdate(
    subscriptionObjectId,
    updateQuery,
    { new: true }
  ).lean();
}

async function cancelPaidSubscriptionViaNonStripe(
  subscription,
  community,
  learner,
  cancellationReason
) {
  const {
    _id: subscriptionObjectId,
    stripeSubscriptionId,
    paymentProvider,
    nextBillingDate,
  } = subscription;

  await updateSubscriptionWebhookTriggered(
    subscriptionObjectId,
    cancellationReason
  );

  let cancelledAt = nextBillingDate;

  let scheduleEvent = false;

  try {
    const paymentBackendRpc = new PaymentBackendRpc();
    await paymentBackendRpc.init();

    switch (paymentProvider) {
      case PAYMENT_PROVIDER.RAZORPAY: {
        const cancelledSubscription =
          await paymentBackendRpc.cancelRazorpaySubscription(
            stripeSubscriptionId,
            true
          );

        if (cancelledSubscription?.current_end) {
          cancelledAt = new Date(cancelledSubscription.current_end * 1000);
        }

        break;
      }
      case PAYMENT_PROVIDER.EBANX: {
        scheduleEvent = true;
        break;
      }
      case PAYMENT_PROVIDER.PAYPAL: {
        if (subscription.createdAt < new Date('2024-11-11')) {
          // cancel old paypal account subscription
          await paymentBackendRpc.cancelPaypalSubscription(
            stripeSubscriptionId,
            cancellationReason,
            'PAYPAL_CLIENT_ID'
          );
        } else {
          await paymentBackendRpc.cancelPaypalSubscription(
            stripeSubscriptionId,
            cancellationReason
          );
        }
        break;
      }
      case PAYMENT_PROVIDER.STRIPE_US: {
        await paymentBackendRpc.cancelStripeSubscription(
          stripeSubscriptionId,
          cancellationReason,
          paymentProvider,
          false,
          true
        );
        break;
      }
      default:
        throw new InternalError(
          `Payment provider for ${paymentProvider} is not supported`
        );
    }
  } catch (err) {
    logger.error(`cancelPaidSubscriptionViaNonStripe: ${err.message}`);
    await removeSubscriptionWebhookTriggered(subscriptionObjectId);
    throw err;
  }

  const updatedSubscription = await updateCancelledSubscriptionStatus(
    subscriptionObjectId,
    cancellationReason,
    cancelledAt
  );

  if (!updatedSubscription) {
    throw new InternalError(`Subscription not updated`);
  }

  if (scheduleEvent) {
    await scheduleSubscriptionCancelledEvent(updatedSubscription);
  }

  await actionEventService.sendUnsubscribedPaidMembershipActionEvent({
    subscription: updatedSubscription,
    community,
  });

  const { _id: communityObjectId, isWhatsappExperienceCommunity = false } =
    community;

  if (isWhatsappExperienceCommunity) {
    await WhatsappParticipantsModel.updateMany(
      {
        communityObjectId,
        isInWhatsApp: true,
        subscriptionObjectId,
      },
      {
        removeWhatsappDate: cancelledAt,
        removeWhatsappStatus: false,
      }
    );
  }

  await CommunityNotificationService.sendCancelSubscriptionNotification({
    subscription: updatedSubscription,
    community,
    learner,
  });
}

exports.cancelPaidSubscription = async (
  subscription,
  cancellationReason,
  ip
) => {
  const {
    stripeSubscriptionId,
    paymentProvider = PAYMENT_PROVIDER.STRIPE,
    communityCode,
    learnerObjectId,
  } = subscription;

  const [community, learner] = await Promise.all([
    CommunityModel.findOne({
      code: communityCode,
    }).lean(),
    LearnerModel.findById(learnerObjectId).lean(),
  ]);

  if (!community) {
    throw new ResourceNotFoundError(
      `Community for ${communityCode} not found`
    );
  }

  switch (paymentProvider) {
    case PAYMENT_PROVIDER.STRIPE:
    case PAYMENT_PROVIDER.STRIPE_INDIA:
      await cancelPaidSubscriptionViaStripe(
        stripeSubscriptionId,
        cancellationReason,
        ip
      );
      break;
    default:
      await cancelPaidSubscriptionViaNonStripe(
        subscription,
        community,
        learner,
        cancellationReason
      );
      break;
  }
};

async function retrieveLatestSubscriptionRawTransaction(
  subscription,
  community
) {
  const { learnerObjectId, paymentProvider } = subscription;

  const { _id: communityObjectId } = community;

  const rawTransaction = await RawTransactionModel.findOne(
    {
      transactionType: TRANSACTION_TYPE.INBOUND,
      purchaseType: PURCHASE_TYPE.SUBSCRIPTION,
      communityObjectId,
      learnerObjectId,
      paymentProvider,
    },
    {
      transactionReferenceId: 1,
      originalAmount: 1,
      originalCurrency: 1,
      purchasedId: 1,
      purchaseType: 1,
    }
  )
    .sort({ transactionCreatedAt: -1 })
    .lean();

  return rawTransaction;
}

exports.removeSubscriptionProcess = async ({
  subscription,
  stripeSubscriptionId,
  removalReason,
  removedBy,
}) => {
  if (
    [
      PAYMENT_PROVIDER.RAZORPAY,
      PAYMENT_PROVIDER.PAYPAL,
      PAYMENT_PROVIDER.STRIPE_US,
    ].includes(subscription.paymentProvider)
  ) {
    await this.removeSubscriptionForNonStripe(
      subscription,
      removalReason ?? 'Removed by community manager'
    );
  }

  const { envVarData = null } = await getConfigByType(
    CONFIG_TYPES.LEARN_PORTAL_BACKEND_ENV_CONFIG_TYPE
  );
  const LP_BE_MW_AUTH_KEY = envVarData?.LP_MAIN_WEBSITE_AUTH_KEY;

  const removeSubscription = await axios.post(
    `${MAIN_PAYMENT_BACKEND_URL}/api/v1/remove-subscription`,
    {
      stripeSubscriptionId,
      removalReason,
      removedBy,
    },
    {
      headers: {
        Authorization: `Bearer ${LP_BE_MW_AUTH_KEY}`,
      },
    }
  );

  if (
    subscription.paymentProvider === PAYMENT_PROVIDER.EBANX &&
    subscription.communityCode === NASIO_AI_SCHOOL_COMMUNITY_CODE
  ) {
    // Cancel all plan adhoc records for only pro plan (requested by pm)
    await planOrderService.cancelAllPlanAdhocRecords({
      email: subscription.email,
      entityType: PLAN_TYPE.PRO,
    });
  }

  return removeSubscription;
};

exports.removeSubscriptionForNonStripe = async (
  subscription,
  cancellationReason = ''
) => {
  const {
    _id: subscriptionObjectId,
    stripeSubscriptionId,
    paymentProvider,
    status,
  } = subscription;

  if (!stripeSubscriptionId) {
    // Do not handle free subscription
    return;
  }

  const paymentBackendRpc = new PaymentBackendRpc();

  await paymentBackendRpc.init();

  await updateSubscriptionWebhookTriggered(
    subscriptionObjectId,
    cancellationReason
  );

  try {
    switch (paymentProvider) {
      case PAYMENT_PROVIDER.RAZORPAY:
        if (status === communityEnrolmentStatuses.CURRENT) {
          await paymentBackendRpc.cancelRazorpaySubscription(
            stripeSubscriptionId,
            false
          );
        }
        break;
      case PAYMENT_PROVIDER.EBANX:
        // Do not need to handle as recurring charge is handled by scheduler
        break;
      case PAYMENT_PROVIDER.STRIPE:
      case PAYMENT_PROVIDER.STRIPE_INDIA:
      case PAYMENT_PROVIDER.XENDIT:
        // This will be handled in main website backend for application reject
        break;
      case PAYMENT_PROVIDER.PAYPAL:
        if (subscription.createdAt < new Date('2024-11-11')) {
          // cancel old paypal account subscription
          await paymentBackendRpc.cancelPaypalSubscription(
            stripeSubscriptionId,
            cancellationReason,
            'PAYPAL_CLIENT_ID'
          );
        } else {
          await paymentBackendRpc.cancelPaypalSubscription(
            stripeSubscriptionId,
            cancellationReason
          );
        }
        break;
      case PAYMENT_PROVIDER.STRIPE_US: {
        await paymentBackendRpc.cancelStripeSubscription(
          stripeSubscriptionId,
          cancellationReason,
          paymentProvider,
          false,
          false
        );
        break;
      }
      default:
        throw new InternalError(
          `Payment provider for ${paymentProvider} is not supported`
        );
    }
  } catch (err) {
    logger.error(
      `removeSubscriptionForNonStripe: remove subscription error ${err.message}`
    );
    await removeSubscriptionWebhookTriggered(subscriptionObjectId);
    throw err;
  }
};

exports.cancelSubscriptionAndRefundForNonStripe = async (
  subscription,
  community,
  cancellationReason
) => {
  const {
    _id: subscriptionObjectId,
    stripeSubscriptionId,
    paymentProvider,
  } = subscription;

  if (!stripeSubscriptionId) {
    // Do not handle free subscription
    return;
  }

  const paymentBackendRpc = new PaymentBackendRpc();

  const [rawTransaction] = await Promise.all([
    retrieveLatestSubscriptionRawTransaction(subscription, community),
    paymentBackendRpc.init(),
  ]);

  await updateSubscriptionWebhookTriggered(
    subscriptionObjectId,
    cancellationReason
  );

  try {
    switch (paymentProvider) {
      case PAYMENT_PROVIDER.RAZORPAY:
        await paymentBackendRpc.cancelRazorpaySubscription(
          stripeSubscriptionId,
          false
        );

        if (rawTransaction) {
          await paymentBackendRpc.refundRazorpayPayment(
            rawTransaction.transactionReferenceId
          );
        }
        break;
      case PAYMENT_PROVIDER.EBANX:
        await paymentBackendRpc.refundEbanxPayment(
          rawTransaction.transactionReferenceId,
          rawTransaction.originalAmount,
          'Application got rejected',
          rawTransaction.purchasedId,
          rawTransaction.purchaseType,
          EBANX_INTEGRATION_KEY.RECURRING
        );

        break;

      case PAYMENT_PROVIDER.PAYPAL:
        if (!rawTransaction) {
          throw new Error(
            `Application is not able to reject now! Please try again later!`
          );
        }
        await paymentBackendRpc.cancelPaypalSubscription(
          stripeSubscriptionId,
          'Application got rejected'
        );

        await paymentBackendRpc.refundPaypalOrder(
          rawTransaction.originalAmount,
          rawTransaction.originalCurrency,
          rawTransaction.transactionReferenceId,
          'Application got rejected'
        );

        break;
      case PAYMENT_PROVIDER.STRIPE_US:
        // Cancel and refund stripe us subscription
        await paymentBackendRpc.cancelStripeSubscription(
          stripeSubscriptionId,
          'Application got rejected',
          paymentProvider,
          true,
          false
        );

        break;
      case PAYMENT_PROVIDER.STRIPE:
      case PAYMENT_PROVIDER.STRIPE_INDIA:
      case PAYMENT_PROVIDER.XENDIT:
        // This will be handled in main website backend for application reject
        break;
      default:
        throw new InternalError(
          `Payment provider for ${paymentProvider} is not supported`
        );
    }
  } catch (err) {
    logger.error(
      `cancelSubscriptionAndRefundForNonStripe: cancel subscription error ${err.message}`
    );
    await removeSubscriptionWebhookTriggered(subscriptionObjectId);
    throw err;
  }
};
