const ObjectId = require('mongoose').Types.ObjectId;

const ZeroLinkModel = require('../../models/zerolink/zerolink.model');
const CommunityModel = require('../../communitiesAPI/models/community.model');
const RevenueTransactionModel = require('../../models/revenueTransaction.model');
const {
  retrieveCommunityBaseCurrencies,
} = require('../currency/retrieveSupportedCurrencies.service');
const commonService = require('./common.service');
const fraudService = require('../fraud');
const logger = require('../logger.service');

const slugUtil = require('../../utils/slug.util');
const { ParamError, ToUserError } = require('../../utils/error.util');
const { ZERO_LINK_STATUS } = require('./constants');
const {
  PURCHASE_TYPE,
  TRANSACTION_TYPE,
  PRICE_TYPE,
} = require('../../constants/common');
const { ZERO_LINK_ERROR } = require('../../constants/errorCode');

const validateZeroLink = async (zeroLink) => {
  const supportedCurrencies = await retrieveCommunityBaseCurrencies();
  const currencyCodes = supportedCurrencies.map(
    (currency) => currency.code
  );
  if (zeroLink.currency && !currencyCodes.includes(zeroLink.currency)) {
    throw new ParamError(`Invalid currency: ${zeroLink.currency}`);
  }

  if (zeroLink.passOnTakeRate !== zeroLink.passOnPaymentGatewayFee) {
    throw new ParamError(
      `passOnTakeRate and passOnPaymentGatewayFee must be either both true or both false`
    );
  }

  if (
    zeroLink.pricingConfig?.priceType === PRICE_TYPE.FIXED &&
    !zeroLink.amount
  ) {
    throw new ParamError(
      `Fixed pricing product should have amount to be set`
    );
  }

  // INR community doesnt have zero link feature in this phase
  const community = await CommunityModel.findById(
    zeroLink.communityObjectId
  )
    .select('baseCurrency')
    .lean();
  if (community && community.baseCurrency === 'INR') {
    throw new Error(`INR community is now allowed to use zerolink`);
  }
};

// eslint-disable-next-line no-unused-vars
async function checkZeroLinkForFraud({
  updatePayload,
  existingZeroLink,
  communityId,
  zeroLinkId,
}) {
  try {
    const contentList = [];
    const contentSourceList = [];
    if (updatePayload?.title) {
      contentList.push(updatePayload.title);
      contentSourceList.push('title');
    }
    if (updatePayload.message) {
      contentList.push(updatePayload.message);
      contentSourceList.push('message');
    }
    if (updatePayload.redirectLink) {
      contentList.push(updatePayload.redirectLink);
      contentSourceList.push('redirectLink');
    }

    const fraudEngine = new fraudService.FraudEngine({
      communityId,
      eventName: fraudService.INTERESTED_EVENTS.UPDATE_CONTENT,
      entityType: PURCHASE_TYPE.ZERO_LINK.toLocaleLowerCase(),
      entityId: zeroLinkId,
      data: {
        content: contentList.join(', '),
        contentSource: contentSourceList.join(' & '),
        zeroLinkMetadata: {
          existingZeroLink,
          updatePayload,
        },
      },
      checksToPerform: [
        fraudService.COMMON_FRAUD_CHECKS.FREE_INPUT,
        fraudService.COMMON_FRAUD_CHECKS.CUSTOM_CREATE_ZERO_LINK_CHECKS,
      ],
      autoConsequencesToApply: [
        fraudService.COMMON_CONSEQUENCES.RESTRICT_CHECKOUT,
        fraudService.COMMON_CONSEQUENCES.RESTRICT_CUSTOM_EMAIL,
        fraudService.COMMON_CONSEQUENCES.RESTRICT_MAGIC_REACH,
        fraudService.COMMON_CONSEQUENCES.NOT_INDEXABLE,
        fraudService.COMMON_CONSEQUENCES.RECOMMEND_DEACTIVATE,
      ],
    });
    await fraudEngine.performCheck();
  } catch (error) {
    logger.error('Error in fraud check:', error.message, error.stack);
  }
}

exports.createZeroLink = async ({
  communityObjectId,
  amount,
  currency,
  status,
  pricingConfig,
  coverImg,
  title,
  message,
  redirectLink,
  passOnTakeRate,
  passOnPaymentGatewayFee,
}) => {
  const updatedPricingConfig = {
    ...pricingConfig,
    ...(pricingConfig.priceType === PRICE_TYPE.FLEXIBLE
      ? { minAmount: 0 } // Might need to change to min gateway fee for each currency
      : {}),
  };
  const zeroLink = {
    communityObjectId,
    amount,
    currency,
    status,
    pricingConfig: updatedPricingConfig,
    coverImg,
    title,
    message,
    redirectLink,
    passOnTakeRate,
    passOnPaymentGatewayFee,
  };

  await validateZeroLink(zeroLink);

  // generate slug
  const slug = await slugUtil.generateSlug(title, ZeroLinkModel, {
    status: { $ne: ZERO_LINK_STATUS.DELETED },
    communityObjectId,
  });
  zeroLink.slug = slug;
  zeroLink.searchTitle = commonService.formatSearchTitle(
    zeroLink.title,
    zeroLink
  );
  zeroLink.earningAnalytics = {
    quantity: 0,
    revenueInUsd: 0,
    revenueInLocalCurrency: 0,
  };

  const [result] = await ZeroLinkModel.create([zeroLink]);
  const finalZeroLink = result.toObject();

  // to move to worker pool
  checkZeroLinkForFraud({
    updatePayload: zeroLink,
    existingZeroLink: null,
    communityId: communityObjectId.toString(),
    zeroLinkId: finalZeroLink._id.toString(),
  });
  return finalZeroLink;
};

exports.updateZeroLink = async ({
  communityObjectId,
  zeroLinkObjectId,
  params,
}) => {
  const existingZeroLink = await ZeroLinkModel.findById(zeroLinkObjectId, {
    title: 1,
    amount: 1,
    currency: 1,
    pricingConfig: 1,
  }).lean();
  const updatePayload = {};

  Object.keys(params).forEach((key) => {
    if (params[key] !== undefined) {
      updatePayload[key] = params[key];
    }
  });

  // Ensure there's something to update
  if (Object.keys(updatePayload).length === 0) {
    throw new Error('No valid fields provided for update.');
  }

  await validateZeroLink(updatePayload);

  if (updatePayload.title) {
    updatePayload.searchTitle = commonService.formatSearchTitle(
      updatePayload.title,
      existingZeroLink
    );
  }

  const result = await ZeroLinkModel.findOneAndUpdate(
    { _id: zeroLinkObjectId, communityObjectId },
    updatePayload,
    { new: true }
  ).lean();

  if (!result) {
    throw new ParamError('Invalid ID of Zero link');
  }
  // to move to worker pool
  checkZeroLinkForFraud({
    updatePayload,
    existingZeroLink,
    communityId: communityObjectId.toString(),
    zeroLinkId: zeroLinkObjectId.toString(),
  });
  return result;
};

exports.deleteZeroLink = async (communityId, zeroLinkId) => {
  const communityObjectId = new ObjectId(communityId);
  const hasTransaction = await RevenueTransactionModel.findOne({
    transactionType: TRANSACTION_TYPE.INBOUND,
    purchaseType: PURCHASE_TYPE.ZERO_LINK,
    communityObjectId,
    entityObjectId: new ObjectId(zeroLinkId),
  }).lean();
  if (hasTransaction) {
    throw new ToUserError(
      'Cannot delete links with transactions',
      ZERO_LINK_ERROR.ZERO_LINK_HAS_TRANSACTIONS
    );
  }
  await ZeroLinkModel.updateOne(
    { _id: new ObjectId(zeroLinkId) },
    {
      status: ZERO_LINK_STATUS.DELETED,
    }
  );
};
