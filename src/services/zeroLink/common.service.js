const {
  PRICE_TYPE,
  MEMBERSHIP_ACTIVITY_KEYS,
} = require('../../constants/common');

exports.formatSearchTitle = (
  title,
  existingZeroLink,
  localization = {}
) => {
  if (title && title !== '') {
    return title;
  }
  if (existingZeroLink.pricingConfig?.priceType === PRICE_TYPE.FLEXIBLE) {
    return `Zero link: ${
      localization[MEMBERSHIP_ACTIVITY_KEYS.FLEXIBLE_PAYMENT] ??
      'Flexible Payment'
    }`;
  }
  return `Zero link: ${existingZeroLink.currency} ${parseFloat(
    (existingZeroLink.amount ?? 0) / 100
  )}`;
};
