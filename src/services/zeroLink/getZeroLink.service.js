const ZeroLinkModel = require('../../models/zerolink/zerolink.model');
const communityService = require('../community');

const {
  ParamError,
  ResourceNotFoundError,
} = require('../../utils/error.util');
const { ZERO_LINK_STATUS } = require('./constants');
const {
  getAddonPriceInLocalCurrency,
} = require('../../communitiesAPI/services/common/communityAddonPrice.service');
const { PURCHASE_TYPE } = require('../../constants/common');

exports.getZeroLinkBySlug = async ({ communityLink, slug }) => {
  if (!communityLink) {
    throw new ParamError('Community link is required');
  }
  const community =
    await communityService.getCommunityService.getCommunityByLink({
      link: communityLink,
    });
  if (!community) {
    throw new ResourceNotFoundError('Community not found');
  }

  const result = await ZeroLinkModel.findOne({
    communityObjectId: community._id,
    status: { $in: [ZERO_LINK_STATUS.ACTIVE, ZERO_LINK_STATUS.INACTIVE] },
    slug,
  }).lean();
  return result;
};

exports.getZeroLinkById = async ({
  communityId,
  zeroLinkId,
  selectedAmount,
  paymentMethodCountryCode,
  paymentProvider,
}) => {
  const zeroLink = await ZeroLinkModel.findOne({
    _id: zeroLinkId,
    communityObjectId: communityId,
  }).lean();

  const priceDetail = await getAddonPriceInLocalCurrency({
    addon: zeroLink,
    communityObjectId: communityId,
    selectedAmount,
    purchaseType: PURCHASE_TYPE.ZERO_LINK,
    paymentMethodCountryCode,
    paymentProvider,
  });
  zeroLink.priceDetail = priceDetail;

  return zeroLink;
};

exports.getZeroLinks = async ({
  community,
  status,
  pageSize = 10,
  pageNo = 1,
  sortOrder,
  sortBy,
}) => {
  const zeroLinkQuery = {
    communityObjectId: community._id,
    status: { $in: [ZERO_LINK_STATUS.ACTIVE, ZERO_LINK_STATUS.INACTIVE] },
  };
  if (status) {
    zeroLinkQuery.status = status;
  }

  const [zeroLinks, totalCount, activeCount, inactiveCount] =
    await Promise.all([
      ZeroLinkModel.find(zeroLinkQuery, {
        status: 1,
        amount: 1,
        currency: 1,
        pricingConfig: 1,
        coverImg: 1,
        title: 1,
        slug: 1,
        redirectLink: 1,
        createdAt: 1,
        earningAnalytics: 1,
        passOnTakeRate: 1,
        passOnPaymentGatewayFee: 1,
      })
        .sort({ [sortBy]: sortOrder })
        .skip((pageNo - 1) * pageSize)
        .limit(pageSize)
        .lean(),
      ZeroLinkModel.countDocuments(zeroLinkQuery),
      ZeroLinkModel.countDocuments({
        ...zeroLinkQuery,
        status: ZERO_LINK_STATUS.ACTIVE,
      }),
      ZeroLinkModel.countDocuments({
        ...zeroLinkQuery,
        status: ZERO_LINK_STATUS.INACTIVE,
      }),
    ]);

  const formattedLinks = zeroLinks.map((link) => {
    return {
      ...link,
      localCurrency: community.baseCurrency,
    };
  });

  return {
    summary: {
      active: activeCount,
      inactive: inactiveCount,
      all: inactiveCount + activeCount,
    },
    links: formattedLinks,
    metadata: {
      total: totalCount,
      limit: pageSize,
      page: pageNo,
      pages: Math.ceil(totalCount / pageSize),
    },
  };
};
