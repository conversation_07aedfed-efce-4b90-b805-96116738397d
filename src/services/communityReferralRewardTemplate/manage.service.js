const { DateTime } = require('luxon');

const ObjectId = require('mongoose').Types.ObjectId;

const CommunityModel = require('../../communitiesAPI/models/community.model');
const CommunityPlanModel = require('../../models/plan/communityPlan.model');
const CommunityReferralRewardTemplateModel = require('../../models/communityReferral/communityReferralRewardTemplate.model');

const PrimaryMongooseConnection = require('../../rpc/primaryMongooseConnection');

const {
  DEFAULT_CURRENCY,
  PAYMENT_PROVIDER,
} = require('../../constants/common');
const {
  COMMUNITY_REFERRAL_REWARD_PLAN_TYPE,
  ALLOWED_CURRENCIES,
} = require('./constants');
const { ParamError, ToUserError } = require('../../utils/error.util');
const PaymentBackendRpc = require('../../rpc/paymentBackend');
const { PLAN_FEATURE, PLAN_BILLING_MODEL } = require('../plan/constants');
const { COMMUNITY_REFERRAL_ERROR } = require('../../constants/errorCode');

// ---------------------
// VALIDATION FUNCTIONS
// ---------------------
function validateEffectiveDuration(
  startTime,
  endTime,
  existingStartTime,
  existingEndTime
) {
  const currentDate = DateTime.utc();

  if (endTime && endTime < currentDate) {
    throw new ParamError('End time cannot be in past!');
  }

  if (startTime && startTime > (endTime ?? existingEndTime)) {
    throw new ParamError('Start time cannot be after the end time!');
  }

  if (endTime && endTime < (startTime ?? existingStartTime)) {
    throw new ParamError('End Time cannot be before the start time!');
  }
}

function validateCurrencies(recurringReward) {
  const recurringRewardKeys = Object.keys(recurringReward);
  const currencyMap = new Map();
  recurringRewardKeys.forEach((key) => {
    const [interval, intervalCount] = key.split('-');
    const currencies = recurringReward[key];
    if (currencies.length === 0) {
      return;
    }
    let hasUsdCurrency = false;
    currencies.forEach((currency) => {
      let count = currencyMap.get(currency.currency) ?? 0;
      count += 1;
      currencyMap.set(currency.currency, count);
      if (currency.currency === DEFAULT_CURRENCY) {
        hasUsdCurrency = true;
      }
    });
    if (!hasUsdCurrency) {
      throw new ParamError(
        `Recurring reward for ${interval} ${intervalCount} must have USD currency`
      );
    }
  });
  const usdCurrency = [...currencyMap.values()].filter(
    (count) => count !== recurringRewardKeys.length
  );
  if (usdCurrency.length) {
    throw new ParamError(
      `You must specify the currency amount for all intervals`
    );
  }
}

async function validateIfCommunityExistsInActiveTemplate({
  communityMap,
  communityObjectIds,
  existingTemplateObjectId,
  effectiveTimeStart,
  effectiveTimeEnd,
  planType,
}) {
  const query = {
    isDisabled: false,
    applyToAll: false,
    communityObjectIds: { $in: communityObjectIds },
    effectiveTimeStart: { $lte: effectiveTimeEnd },
    effectiveTimeEnd: { $gte: effectiveTimeStart },
    planType,
  };
  if (existingTemplateObjectId) {
    query._id = { $ne: existingTemplateObjectId };
  }
  const existingActiveTemplates =
    await CommunityReferralRewardTemplateModel.find(query, {
      _id: 1,
      communityObjectIds: 1,
      name: 1,
    }).lean();

  if (existingActiveTemplates.length) {
    const duplicatedTemplates = new Map();
    let templateNames = '';
    existingActiveTemplates.forEach((template) => {
      const duplicatedCommunityCodes = new Set();
      templateNames += `${template.name}, `;
      template.communityObjectIds.forEach((id) => {
        const communityCode = communityMap.get(id.toString());
        if (communityCode) {
          duplicatedCommunityCodes.add(communityCode);
        }
      });
      duplicatedTemplates.set(template._id.toString(), {
        templateId: template._id.toString(),
        templateName: template.name,
        communityCodes: Array.from(duplicatedCommunityCodes),
      });
    });

    const data = { templates: [...duplicatedTemplates.values()] };

    const errorMessage = `Communities specified is/are already used in ${existingActiveTemplates.length} other active templates: ${templateNames}`;
    const errorDetailObject =
      COMMUNITY_REFERRAL_ERROR.COMMUNITY_ALREADY_EXISTS_IN_ANOTHER_TEMPLATE;
    errorDetailObject.errorData = data;
    throw new ToUserError(errorMessage, errorDetailObject);
  }
}

async function validateAndRetrieveFormattedPayload({
  payload,
  existing = null,
}) {
  if (Object.keys(payload).length === 0) {
    throw new ParamError('Payload cannot be empty');
  }
  if ('communityCodes' in payload && 'communityObjectIds' in payload) {
    throw new ParamError(
      'Community Codes and Community Object Ids cannot be requested in the payload together'
    );
  }

  // validate changes to existing default template
  if (existing?.applyToAll) {
    if (
      'isDisabled' in payload ||
      'applyToAll' in payload ||
      'effectiveTimeStart' in payload ||
      'effectiveTimeEnd' in payload ||
      'planType' in payload ||
      'communityObjectIds' in payload ||
      'name' in payload
    ) {
      throw new ParamError(
        'You can only update prices for existing default template'
      );
    }
  }

  const selectedPlanType = existing?.planType ?? payload.planType;

  if (
    selectedPlanType &&
    !Object.values(COMMUNITY_REFERRAL_REWARD_PLAN_TYPE).includes(
      selectedPlanType
    )
  ) {
    throw new ParamError(
      'Only Pro and Platinum Plan accepted at the moment'
    );
  }

  if (
    selectedPlanType === COMMUNITY_REFERRAL_REWARD_PLAN_TYPE.PLATINUM &&
    payload.isRefereeTrialBillingPlanEnabled
  ) {
    throw new ParamError(
      'Platinum Plan does not support trial billing plan'
    );
  }

  if (
    payload.effectiveTimeStart &&
    payload.effectiveTimeStart < new Date()
  ) {
    // eslint-disable-next-line no-param-reassign
    payload.effectiveTimeStart = new Date();
  }
  validateEffectiveDuration(
    payload.effectiveTimeStart,
    payload.effectiveTimeEnd,
    existing?.effectiveTimeStart,
    existing?.effectiveTimeEnd
  );
  const effectiveTimeStart =
    payload.effectiveTimeStart ?? existing?.effectiveTimeStart;
  const effectiveTimeEnd =
    payload.effectiveTimeEnd ?? existing?.effectiveTimeEnd;

  const applyToAll = payload.applyToAll ?? existing?.applyToAll;
  const isDisabled = payload.isDisabled ?? existing?.isDisabled;

  if (applyToAll) {
    if (effectiveTimeStart && effectiveTimeEnd) {
      throw new ParamError(
        'Effective time start and end must be set to null (infinity) if applyToAll is set to true'
      );
    }

    const query = {
      isDisabled: false,
      applyToAll: true,
      planType: payload.planType,
    };
    if (existing) {
      query._id = { $ne: existing._id };
    }
    const existingActiveDefaultTemplate =
      await CommunityReferralRewardTemplateModel.findOne(query, {
        _id: 1,
      }).lean();
    if (existingActiveDefaultTemplate) {
      throw new ParamError(
        'Default Template already exists. Cannot set applyToAll to true'
      );
    }
  }

  const finalPayload = { ...payload };
  const communityMap = new Map();
  if ('communityCodes' in payload) {
    const communities = await CommunityModel.find(
      { code: { $in: payload.communityCodes } },
      { _id: 1, code: 1 }
    ).lean();
    finalPayload.communityObjectIds = communities.map((community) => {
      communityMap.set(community._id.toString(), community.code);
      return community._id;
    });
    delete finalPayload.communityCodes;
  } else if ('communityObjectIds' in payload) {
    const payloadCommunityObjectIds = payload.communityObjectIds.map(
      (id) => new ObjectId(id)
    );
    const communities = await CommunityModel.find(
      { _id: { $in: payloadCommunityObjectIds } },
      { _id: 1, code: 1 }
    ).lean();
    finalPayload.communityObjectIds = communities.map((community) => {
      communityMap.set(community._id.toString(), community.code);
      return community._id;
    });
  }

  const communityObjectIds =
    finalPayload.communityObjectIds ?? existing?.communityObjectIds;

  if (
    isDisabled === false &&
    applyToAll === false &&
    communityObjectIds.length
  ) {
    if (!effectiveTimeStart || !effectiveTimeEnd) {
      throw new ParamError(
        'Effective time start and end must be set when applyToAll is set to false'
      );
    }
    await validateIfCommunityExistsInActiveTemplate({
      communityMap,
      communityObjectIds,
      existingTemplateObjectId: existing?._id,
      effectiveTimeStart,
      effectiveTimeEnd,
      planType: payload.planType,
    });
  }
  if (payload.refereeRecurringReward) {
    validateCurrencies(payload.refereeRecurringReward);
  } else {
    delete finalPayload.refereeRecurringReward;
  }
  if (payload.referrerRecurringReward) {
    validateCurrencies(payload.referrerRecurringReward);
  } else {
    delete finalPayload.referrerRecurringReward;
  }
  if (payload.referrerUpfrontReward) {
    validateCurrencies(payload.referrerUpfrontReward);
  } else {
    delete finalPayload.referrerUpfrontReward;
  }
  return finalPayload;
}

// --------------------------------------------------
// FORMAT TEMPLATE PRICES FOR PLAN'S STRIPE PRODUCTS
// --------------------------------------------------
function retrievePriceBreakdownFromCurrencies(currencies) {
  const currencyOptions = {};
  let amountInUsd;
  let amountInInr;
  currencies.forEach((currency) => {
    if (currency.currency === DEFAULT_CURRENCY) {
      amountInUsd = currency.amount;
    } else if (currency.currency === ALLOWED_CURRENCIES.INR) {
      amountInInr = currency.amount;
    }
    currencyOptions[currency.currency.toLowerCase()] = {
      unit_amount: currency.amount,
    };
  });
  return {
    currencyOptions,
    amountInUsd,
    amountInInr,
  };
}

function formatStripePrices({
  defaultCurrency,
  defaultAmount,
  interval,
  intervalCount,
  currencyOptions,
}) {
  return {
    currency: defaultCurrency,
    unit_amount: defaultAmount,
    recurring: {
      interval,
      interval_count: intervalCount,
    },
    currency_options: currencyOptions,
  };
}

function getPaymentProviderPrices(recurringReward) {
  const refereeRecurringRewardKeys = Object.keys(recurringReward);

  const paymentProviderPrices = {
    [PAYMENT_PROVIDER.STRIPE_US]: [],
    [PAYMENT_PROVIDER.STRIPE_INDIA]: [],
  };

  refereeRecurringRewardKeys.forEach((key) => {
    const [interval, intervalCount] = key.split('-');
    const { currencyOptions, amountInUsd, amountInInr } =
      retrievePriceBreakdownFromCurrencies(recurringReward[key]);

    if (amountInUsd) {
      delete currencyOptions[DEFAULT_CURRENCY.toLocaleLowerCase()];
      const price = formatStripePrices({
        defaultCurrency: DEFAULT_CURRENCY,
        defaultAmount: amountInUsd,
        interval,
        intervalCount,
        currencyOptions,
      });
      const priceList = paymentProviderPrices[PAYMENT_PROVIDER.STRIPE_US];
      priceList.push(price);
      paymentProviderPrices[PAYMENT_PROVIDER.STRIPE_US] = priceList;
    }

    if (amountInInr) {
      const price = formatStripePrices({
        defaultCurrency: ALLOWED_CURRENCIES.INR,
        defaultAmount: amountInInr,
        interval,
        intervalCount,
      });
      const priceList =
        paymentProviderPrices[PAYMENT_PROVIDER.STRIPE_INDIA];
      priceList.push(price);
      paymentProviderPrices[PAYMENT_PROVIDER.STRIPE_INDIA] = priceList;
    }
  });
  return paymentProviderPrices;
}

async function setupPlanPaymentProvidersFromRecurringReward({
  recurringReward,
  title,
  paymentBackendRpc,
}) {
  const paymentProviderPrices = getPaymentProviderPrices(recurringReward);
  const paymentProviders = Object.keys(paymentProviderPrices);
  const planPaymentProviders = [];

  await Promise.all(
    paymentProviders.map(async (paymentProvider) => {
      if (!paymentProviderPrices[paymentProvider].length) {
        return;
      }
      const data = {
        name: title,
        description: title,
        prices: paymentProviderPrices[paymentProvider],
        paymentProvider,
      };

      const result = await paymentBackendRpc.createStripeProduct(data);
      if (result?.product?.id) {
        planPaymentProviders.push({
          provider: paymentProvider,
          productId: result?.product?.id,
        });
      }
    })
  );
  return planPaymentProviders;
}

// ----------------------------------
// SET UP PLANS FOR REWARD TEMPLATE
// ----------------------------------
async function createRecurringBasePlan({
  recurringReward,
  title,
  entityType,
  paymentBackendRpc,
  session,
}) {
  const planPaymentProviders =
    await setupPlanPaymentProvidersFromRecurringReward({
      recurringReward,
      title,
      paymentBackendRpc,
    });

  const recurringPlanData = {
    type: title,
    entityType,
    isActive: true,
    paymentProviders: planPaymentProviders,
    billingModel: PLAN_BILLING_MODEL.RECURRING,
    features: [
      {
        type: PLAN_FEATURE.STORAGE_UPGRADE_IN_GB,
        value: 50,
      },
    ],
    index: 1,
  };
  const recurringPlan = (
    await CommunityPlanModel.create([recurringPlanData], {
      session,
    })
  )[0].toObject();
  return recurringPlan;
}

async function createFirstBillingTrialPlan({
  title,
  entityType,
  nextBillingPlanObjectId,
  session,
}) {
  const defaultPlan = await CommunityPlanModel.findOne({
    entityType,
    isDefault: true,
    nextBillingPlanObjectId: { $exists: true },
  }).lean();

  if (!defaultPlan || !defaultPlan.nextBillingPlanObjectId) {
    throw new ParamError('Default first trial plan not found!');
  }

  const recurringPlanData = {
    type: title,
    entityType,
    isActive: true,
    paymentProviders: defaultPlan.paymentProviders,
    billingModel: PLAN_BILLING_MODEL.RECURRING,
    features: [
      {
        type: PLAN_FEATURE.STORAGE_UPGRADE_IN_GB,
        value: 50,
      },
    ],
    index: 0,
    nextBillingPlanObjectId,
    isOnePriceForAllNextBillingPlanPrices:
      defaultPlan.isOnePriceForAllNextBillingPlanPrices,
  };

  const plan = (
    await CommunityPlanModel.create([recurringPlanData], {
      session,
    })
  )[0].toObject();
  return plan;
}

const createPlanForRewardTemplate = async ({
  refereeRecurringReward,
  isRefereeTrialBillingPlanEnabled,
  name,
  planType,
  session,
}) => {
  const paymentBackendRpc = new PaymentBackendRpc();
  await paymentBackendRpc.init();

  const recurringPlan = await createRecurringBasePlan({
    paymentBackendRpc,
    title: `${planType}_REFERRAL_REWARD ${name}`,
    entityType: planType,
    recurringReward: refereeRecurringReward,
    session,
  });

  if (isRefereeTrialBillingPlanEnabled) {
    const firstBillingPlan = await createFirstBillingTrialPlan({
      title: `${planType}_REFERRAL_REWARD_FIRST_BILLING ${name}`,
      entityType: planType,
      nextBillingPlanObjectId: recurringPlan._id,
      session,
    });
    return firstBillingPlan._id;
  }

  return recurringPlan._id;
};

exports.createCommunityReferralRewardTemplate = async (payload = {}) => {
  const formattedPayload = await validateAndRetrieveFormattedPayload({
    payload,
  });

  const templateToCreate = { ...formattedPayload };
  const {
    refereeRecurringReward,
    isRefereeTrialBillingPlanEnabled,
    name,
    planType,
  } = formattedPayload;

  if (!refereeRecurringReward) {
    throw new ParamError(
      'Cannot create template without Referee Recurring Reward specified'
    );
  }
  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();
  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  try {
    const planObjectId = await createPlanForRewardTemplate({
      refereeRecurringReward,
      isRefereeTrialBillingPlanEnabled,
      name,
      planType,
      session,
    });
    templateToCreate.planObjectId = planObjectId;

    const newTemplate = (
      await CommunityReferralRewardTemplateModel.create(
        [templateToCreate],
        { session }
      )
    )[0].toObject();

    await CommunityPlanModel.findByIdAndUpdate(
      planObjectId,
      { referralRewardTemplateObjectId: newTemplate._id },
      { session }
    );
    await session.commitTransaction();
    return newTemplate;
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }
};

/**
 * This function assumes that no changes has been made to refereeRecurringReward
 * @param {*} param0
 */
async function handleIsRefereeTrialBillingPlanEnabledToggle({
  isRefereeTrialBillingPlanEnabled,
  existingPlan,
  name,
  planType,
  session,
}) {
  let planObjectId = existingPlan._id;
  if (isRefereeTrialBillingPlanEnabled) {
    if (existingPlan.nextBillingPlanObjectId) {
      throw new ParamError(
        'Existing Plan already has nextBillingPlanObjectId, which means trial is already enabled'
      );
    }
    const firstBillingPlan = await createFirstBillingTrialPlan({
      title: `${planType}_REFERRAL_REWARD_FIRST_BILLING ${name}`,
      entityType: planType,
      nextBillingPlanObjectId: existingPlan._id,
      session,
    });

    planObjectId = firstBillingPlan._id;
  } else if (existingPlan.nextBillingPlanObjectId) {
    planObjectId = existingPlan.nextBillingPlanObjectId;
    await CommunityPlanModel.updateMany(
      { _id: { $in: [existingPlan._id] } },
      { isActive: false },
      { session }
    );
  } else {
    throw new ParamError(
      'Existing Plan does not have nextBillingPlanObjectId, which means trial is already disabled'
    );
  }
  return { planObjectId };
}

async function handleRefereeRewardUpdates({ payload, existing, session }) {
  const hasRefereeRecurringRewardChanges =
    'refereeRecurringReward' in payload;
  const hasIsRefereeTrialBillingPlanEnabledChanges =
    'isRefereeTrialBillingPlanEnabled' in payload;
  if (
    !hasRefereeRecurringRewardChanges &&
    !hasIsRefereeTrialBillingPlanEnabledChanges
  ) {
    return {};
  }
  // Ensure that referee reward setup is based on final update
  const refereeRecurringReward =
    payload.refereeRecurringReward ?? existing.refereeRecurringReward;
  const isRefereeTrialBillingPlanEnabled =
    payload.isRefereeTrialBillingPlanEnabled ??
    existing.isRefereeTrialBillingPlanEnabled;
  const name = payload.name ?? existing.name;
  const planType = payload.planType ?? existing.planType;
  const dataToUpdate = {
    refereeRecurringReward,
    isRefereeTrialBillingPlanEnabled,
    name,
    planType,
  };
  const existingPlan = existing.planObjectId
    ? await CommunityPlanModel.findOne({
        _id: existing.planObjectId,
        isActive: true,
      }).lean()
    : null;

  if (
    !hasRefereeRecurringRewardChanges &&
    hasIsRefereeTrialBillingPlanEnabledChanges
  ) {
    const { planObjectId } =
      await handleIsRefereeTrialBillingPlanEnabledToggle({
        isRefereeTrialBillingPlanEnabled,
        existingPlan,
        name,
        planType,
        session,
      });
    dataToUpdate.planObjectId = planObjectId;
  } else {
    const planObjectId = await createPlanForRewardTemplate({
      refereeRecurringReward,
      isRefereeTrialBillingPlanEnabled,
      name,
      planType,
      session,
    });
    dataToUpdate.planObjectId = planObjectId;
    const plansToDisable = [existingPlan._id];
    if (existingPlan.nextBillingPlanObjectId) {
      plansToDisable.push(existingPlan.nextBillingPlanObjectId);
    }
    await CommunityPlanModel.updateMany(
      { _id: { $in: plansToDisable } },
      { isActive: false },
      { session }
    );
  }

  return dataToUpdate;
}

exports.updateCommunityReferralRewardTemplate = async (
  templateObjectId,
  payload = {}
) => {
  const existingTemplate =
    await CommunityReferralRewardTemplateModel.findById(
      templateObjectId
    ).lean();

  if (!existingTemplate) {
    throw new ParamError(
      `Community Referral Reward Template with id ${templateObjectId} not found`
    );
  }
  const formattedPayload = await validateAndRetrieveFormattedPayload({
    payload,
    existing: existingTemplate,
  });

  const { isRefereeTrialBillingPlanEnabled, refereeRecurringReward } =
    formattedPayload;

  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();
  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  let dataToUpdate = { ...formattedPayload };

  try {
    if (refereeRecurringReward || isRefereeTrialBillingPlanEnabled) {
      const additionalData = await handleRefereeRewardUpdates({
        payload: formattedPayload,
        existing: existingTemplate,
        session,
      });
      dataToUpdate = {
        ...dataToUpdate,
        ...additionalData,
      };
    }

    const newTemplate =
      await CommunityReferralRewardTemplateModel.findByIdAndUpdate(
        templateObjectId,
        dataToUpdate,
        { session }
      );

    await session.commitTransaction();
    return newTemplate;
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }
};
