const NodeCache = require('node-cache');
const CountryInfoMappingModel = require('../../models/countryInfoMapping.model');
const logger = require('../logger.service');

const cache = new NodeCache();

const COMMUNITY_BASE_CURRENCIES_LIST = 'COMMUNITY_BASE_CURRENCIES_LIST';
const COMMUNITY_PAYOUT_CURRENCIES_LIST =
  'COMMUNITY_PAYOUT_CURRENCIES_LIST';

async function retrieveSupportedCurrencies(matchFilter) {
  const currencies = await CountryInfoMappingModel.aggregate([
    {
      $match: matchFilter,
    },
    {
      $group: {
        _id: {
          currencyCode: '$currencyCode',
          currencyName: '$currency',
          currencyFlag: '$currencyFlag',
        },
      },
    },
    {
      $sort: {
        '_id.currencyCode': 1,
      },
    },
    {
      $project: {
        _id: 0,
        code: '$_id.currencyCode',
        name: '$_id.currencyName',
        flag: '$_id.currencyFlag',
      },
    },
  ]);

  return currencies.map((currency) => {
    const newCurrency = {
      ...currency,
    };

    newCurrency.key = newCurrency.name.toLowerCase().replace(/ /g, '-');
    return newCurrency;
  });
}

exports.retrieveCommunityBaseCurrencies = async () => {
  let currencies = cache.get(COMMUNITY_BASE_CURRENCIES_LIST);
  if (currencies) {
    return currencies;
  }
  logger.info('Fetching community base currencies');
  const matchFilter = { localisePrice: true };
  currencies = await retrieveSupportedCurrencies(matchFilter);
  cache.set(COMMUNITY_BASE_CURRENCIES_LIST, currencies);

  return currencies;
};

exports.retrievePayoutCurrencies = async () => {
  let currencies = cache.get(COMMUNITY_PAYOUT_CURRENCIES_LIST);
  if (currencies) {
    return currencies;
  }

  const matchFilter = {
    payoutCurrencySupported: true,
  };
  currencies = await retrieveSupportedCurrencies(matchFilter);
  cache.set(COMMUNITY_PAYOUT_CURRENCIES_LIST, currencies);

  return currencies;
};

exports.refreshCache = async () => {
  cache.del(COMMUNITY_BASE_CURRENCIES_LIST);
  cache.del(COMMUNITY_PAYOUT_CURRENCIES_LIST);

  await this.retrieveCommunityBaseCurrencies();
  await this.retrievePayoutCurrencies();
};
