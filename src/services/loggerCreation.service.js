const winston = require('winston');
const httpContext = require('express-http-context');

const { combine, timestamp, printf, splat, json, errors } = winston.format;

// Environment configuration
const LOG_FORMAT = process.env.LOG_FORMAT || 'json'; // 'legacy' or 'json'
const NODE_ENV = process.env.NODE_ENV || 'development';

let silent;
switch (NODE_ENV) {
  case 'test':
    silent = true;
    break;
  default:
    silent = false;
    break;
}

// Legacy format for backward compatibility
const legacyFormat = printf((info) => {
  const reqId = httpContext.get('reqId');
  const reqIp = httpContext.get('reqIp');
  const logType = 'reqId' in info ? 'LOG' : 'API';
  const version = 2;

  return `lpbe|${version}|${info?.reqId ?? reqId}|${
    info?.reqIp ?? reqIp
  }|${info.timestamp}|${info.level}|${logType}|${info.message.replace(
    /\n/g,
    ' '
  )}`;
});

// New structured format
const structuredFormat = printf((info) => {
  const reqId = httpContext.get('reqId') || info.reqId;
  const sessionId = httpContext.get('sessionId') || info.sessionId;
  const userId = httpContext.get('userId') || info.userId;
  const reqIp = httpContext.get('reqIp') || info.reqIp;

  // Build structured log object
  const logObject = {
    timestamp: info.timestamp,
    level: info.level,
    logType: 'reqId' in info ? 'LOG' : 'API',
    correlationId: reqId,
    sessionId,
    userId,
    clientIp: reqIp,
    message: info.message,
    service: process.env.SERVICE_NAME || 'learn-portal-backend',
    environment: NODE_ENV,
    version: '2.0',
    ...info.metadata,
  };

  // Create a new WeakSet for each stringify call to avoid memory leaks
  const seen = new WeakSet();

  // Remove circular references and undefined values
  return JSON.stringify(logObject, (key, value) => {
    if (value === undefined) return undefined;
    if (typeof value === 'object' && value !== null) {
      if (seen.has(value)) return '[Circular]';
      seen.add(value);
    }
    return value;
  });
});

// Choose format based on environment variable
const getLogFormat = () => {
  if (LOG_FORMAT === 'json') {
    return combine(
      errors({ stack: true }),
      timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
      splat(),
      structuredFormat
    );
  }

  // Default to legacy format
  return combine(
    json(),
    timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
    splat(),
    legacyFormat
  );
};

// Console transport options
const consoleOptions = {
  silent,
  handleExceptions: true,
  json: LOG_FORMAT === 'json',
  colorize: NODE_ENV === 'development' && LOG_FORMAT !== 'json',
};

// Create logger instance
const logger = winston.createLogger({
  format: getLogFormat(),
  level: process.env.LOG_LEVEL || 'info',
  transports: [new winston.transports.Console(consoleOptions)],
  exitOnError: false, // do not exit on handled exceptions
});

// Stream for Morgan integration
logger.stream = {
  write(message) {
    // Remove trailing newline from Morgan
    const cleanMessage = message.replace(/\n$/, '');
    logger.info(cleanMessage);
  },
};

module.exports = logger;
