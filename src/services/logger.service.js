const httpContext = require('express-http-context');

const loggerFunc = require('./loggerCreation.service');
const logConfig = require('./logConfig.service');

// Sensitive data masking function
const maskSensitiveData = (
  obj,
  sensitiveFields = logConfig.sensitiveFields
) => {
  if (!obj || typeof obj !== 'object') return obj;

  // Clone the object to avoid mutation

  const cloneObject = (item, seen = new WeakSet()) => {
    if (item === null || item === undefined) return item;

    // Check for circular references
    if (typeof item === 'object') {
      if (seen.has(item)) return '[Circular]';
      seen.add(item);
    }

    if (Array.isArray(item))
      return item.map((el) => cloneObject(el, seen));
    if (typeof item !== 'object') return item;

    const cloned = {};
    for (const [key, value] of Object.entries(item)) {
      cloned[key] = cloneObject(value, seen);
    }
    return cloned;
  };
  const masked = cloneObject(obj);

  const shouldMaskField = (fieldName) => {
    const lowerFieldName = fieldName.toLowerCase();
    return sensitiveFields.some((sensitiveField) => {
      const lowerSensitiveField = sensitiveField.toLowerCase();
      // Exact match
      if (lowerFieldName === lowerSensitiveField) return true;
      // For fields like 'user_id', also match 'userId' (camelCase)
      const camelCase = lowerSensitiveField.replace(/_([a-z])/g, (g) =>
        g[1].toUpperCase()
      );
      if (lowerFieldName === camelCase) return true;
      // For fields like 'userId', also match 'user_id' (snake_case)
      const snakeCase = lowerSensitiveField.replace(
        /[A-Z]/g,
        (letter) => `_${letter.toLowerCase()}`
      );
      if (lowerFieldName === snakeCase) return true;
      return false;
    });
  };

  const recursiveMask = (item, path = '', visited = new WeakSet()) => {
    if (item === null || item === undefined) return item;

    // Check for circular references
    if (typeof item === 'object') {
      if (visited.has(item)) return '[Circular]';
      visited.add(item);
    }

    if (Array.isArray(item)) {
      return item.map((el, idx) =>
        recursiveMask(el, `${path}[${idx}]`, visited)
      );
    }

    if (typeof item === 'object') {
      const result = {};
      for (const [key, value] of Object.entries(item)) {
        if (shouldMaskField(key)) {
          result[key] = '[REDACTED]';
        } else if (value !== null && typeof value === 'object') {
          result[key] = recursiveMask(
            value,
            path ? `${path}.${key}` : key,
            visited
          );
        } else {
          result[key] = value;
        }
      }
      return result;
    }

    return item;
  };

  return recursiveMask(masked);
};

// Format message with masking
const formatMessage = (messages) => {
  let reqId;
  let sessionId;
  let userId;
  let reqIp;

  try {
    reqId = httpContext.get('reqId');
    sessionId = httpContext.get('sessionId');
    userId = httpContext.get('userId');
    reqIp = httpContext.get('reqIp');
  } catch (err) {
    // If context is not available, use defaults
    reqId = undefined;
    sessionId = undefined;
    userId = undefined;
    reqIp = undefined;
  }
  // Process all messages and apply masking
  const processedMessages = messages.map((message) => {
    if (!message) return message;

    if (message instanceof Error) {
      return {
        message: message.message,
        stack: logConfig.enableStackTrace ? message.stack : undefined,
        code: message.code,
        name: message.name,
      };
    }

    if (typeof message === 'object') {
      try {
        return maskSensitiveData(message);
      } catch (err) {
        return {
          error: 'Failed to mask sensitive data',
          originalError: err.message,
        };
      }
    }

    return message;
  });

  // For JSON format
  if (logConfig.logFormat === 'json') {
    // Combine all messages into a single message string
    const combinedMessage = processedMessages
      .map((msg) => {
        if (typeof msg === 'object') {
          try {
            return JSON.stringify(msg);
          } catch (err) {
            return '[Unstringifiable Object]';
          }
        }
        return msg;
      })
      .join(' ');

    const messageData = {
      reqId,
      sessionId,
      userId,
      reqIp,
      message: combinedMessage,
      metadata: {
        sessionId,
        userId,
      },
    };
    return messageData;
  }

  // Legacy format
  const messageData = {
    reqId,
    sessionId,
    reqIp,
    message: processedMessages
      .map((message) => {
        if (typeof message === 'object') {
          try {
            return JSON.stringify(message);
          } catch (err) {
            return '[Unstringifiable Object]';
          }
        }
        return message;
      })
      .join(' '),
  };
  return messageData;
};

// Logger object with all methods
const logger = {
  log(level, ...message) {
    if (!logConfig.shouldLog(level)) return;
    if (level !== 'error' && !logConfig.shouldSample()) return;
    loggerFunc.log(level, formatMessage(message));
  },
  error(...message) {
    if (!logConfig.shouldLog('error')) return;
    loggerFunc.error(formatMessage(message));
  },
  warn(...message) {
    if (!logConfig.shouldLog('warn')) return;
    if (!logConfig.shouldSample()) return;
    loggerFunc.warn(formatMessage(message));
  },
  verbose(...message) {
    if (!logConfig.shouldLog('verbose')) return;
    if (!logConfig.shouldSample()) return;
    loggerFunc.verbose(formatMessage(message));
  },
  info(...message) {
    if (!logConfig.shouldLog('info')) return;
    if (!logConfig.shouldSample()) return;
    loggerFunc.info(formatMessage(message));
  },
  debug(...message) {
    if (!logConfig.shouldLog('debug')) return;
    if (!logConfig.shouldSample()) return;
    loggerFunc.debug(formatMessage(message));
  },
  silly(...message) {
    if (!logConfig.shouldLog('silly')) return;
    if (!logConfig.shouldSample()) return;
    loggerFunc.silly(formatMessage(message));
  },

  // Export maskSensitiveData so Morgan and other modules can use it
  maskSensitiveData,
};

module.exports = logger;
