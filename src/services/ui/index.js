const CommunityModel = require('../../communitiesAPI/models/community.model');
const constants = require('./constants');

exports.dismissUIConfig = async ({ communityId, ui, hide }) => {
  const updateQuery = {};

  switch (ui) {
    case constants.UI_CONFIG_CASES.NEXT_THINGS_TO_DO: {
      updateQuery['config.hideNextThingsToDo'] = hide;
      break;
    }
    case constants.UI_CONFIG_CASES.GET_INSPIRED_BANNER: {
      updateQuery['config.hideGetInspiredBanner'] = hide;
      break;
    }
    default:
      break;
  }
  const community = await CommunityModel.findByIdAndUpdate(
    communityId,
    updateQuery
  )
    .select('config')
    .lean();
  return community;
};
