const CommunityModel = require('../../communitiesAPI/models/community.model');
const CommunityRoleModel = require('../../communitiesAPI/models/communityRole.model');
const UserModel = require('../../models/users.model');
const LearnerModel = require('../../models/learners.model');
const RegionAnalyticsModel = require('../../models/getInspired/regionAnalytics.model');

const commonConfigService = require('../config/common.service');
const countryInfoMappingService = require('../countryInfoMapping/countryInfoMapping.service');
const { countCommunityMembers } = require('../membership/count.service');
const PaymentBackendRpc = require('../../rpc/paymentBackend');
const { normalizeAmountByCurrency } = require('../../utils/currency.util');
const { ResourceNotFoundError } = require('../../utils/error.util');
const { aclRoles } = require('../../communitiesAPI/constants');
const {
  // eslint-disable-next-line no-unused-vars
  MEMBERSHIP_TEMPLATE_DEFAULTS,
  AI_TEMPLATE_GENERATION_STATUS,
  REGION_ANALYTICS_CATEGORY,
  COUNTRY_REGIONS,
  DEFAULT_CURRENCY,
  CONFIG_TYPES,
} = require('../../constants/common');

exports.formatMembershipAITemplateResponse = (template) => {
  return template;
  // comment this out in case pm change req again
  // const refinedTemplate = { ...template };
  // refinedTemplate.thumbnailImgSrc = MEMBERSHIP_TEMPLATE_DEFAULTS.THUMBNAIL;
  // if (!refinedTemplate.metadata) {
  //   refinedTemplate.metadata = {};
  // }
  // refinedTemplate.metadata.title = MEMBERSHIP_TEMPLATE_DEFAULTS.TITLE;
  // return refinedTemplate;
};

exports.formatPendingAITemplatesResponse = (templates) => {
  const THRESHOLD = 3;
  return templates.length < THRESHOLD ? [] : templates;
};

exports.formatDigitalProductAITemplateResponse = (
  folders = [],
  sessions = []
) => {
  let digitalProducts = [];
  if (folders.length >= 2) {
    digitalProducts = folders.slice(0, 2);
    if (sessions.length >= 1) {
      digitalProducts.push(sessions[0]);
    }
  }
  return digitalProducts;
};

exports.formatGetInspiredTemplatesResponse = async ({
  baseCurrency,
  challenges = [],
  events = [],
  memberships = [],
  folders = [],
  sessions = [],
}) => {
  let localCurrency;
  if (challenges.length) {
    localCurrency = challenges[0].localCurrency;
  } else if (events.length) {
    localCurrency = events[0].localCurrency;
  } else if (folders.length) {
    localCurrency = folders[0].localCurrency;
  } else if (sessions.length) {
    localCurrency = sessions[0].localCurrency;
  } else if (memberships.length) {
    localCurrency = memberships[0].localCurrency;
  }

  const digitalProducts = this.formatDigitalProductAITemplateResponse(
    folders,
    sessions
  );

  const [
    formattedChallenges,
    formattedEvents,
    formattedMemberships,
    formattedDigitalProducts,
  ] = await Promise.all([
    this.convertTemplatesCurrency(challenges, baseCurrency, localCurrency),
    this.convertTemplatesCurrency(events, baseCurrency, localCurrency),
    this.convertTemplatesCurrency(
      memberships,
      baseCurrency,
      localCurrency
    ),
    this.convertTemplatesCurrency(
      digitalProducts,
      baseCurrency,
      localCurrency
    ),
  ]);

  return {
    challenges: formattedChallenges,
    events: formattedEvents,
    memberships: formattedMemberships.slice(0, 2), // Limit to 2 memberships
    digitalProducts: formattedDigitalProducts,
  };
};

exports.retrieveTemplateVersionForQuerying = (
  generationStatus,
  generationVersion,
  latestTemplateVersion
) => {
  let isDefault = true;
  let version = generationVersion;
  if (generationStatus === AI_TEMPLATE_GENERATION_STATUS.PROCESSING) {
    isDefault = false;
    if (generationVersion === latestTemplateVersion) {
      version = (latestTemplateVersion ?? 0) + 1;
    } else {
      version = latestTemplateVersion ?? 0;
    }
  } else if (version) {
    isDefault = false;
  }
  return {
    isDefault,
    version,
  };
};

exports.retrieveAuthorizedCommunityObjectIdsBelongingToOwner = async (
  userObjectId
) => {
  const communityObjectIds = await CommunityRoleModel.find({
    userObjectId,
    role: aclRoles.OWNER,
  }).distinct('communityObjectId');

  const communities = await CommunityModel.find({
    _id: { $in: communityObjectIds },
  })
    .select('_id config')
    .lean();

  const filteredCommunityObjectIds = [];
  await Promise.all(
    communities.map(async (community) => {
      const isAuthorized =
        await commonConfigService.isCommunityIdWhitelisted(
          CONFIG_TYPES.GET_INSPIRED_WHITELIST_CONFIG_TYPE,
          community
        );
      if (isAuthorized) {
        filteredCommunityObjectIds.push(community._id);
      }
    })
  );
  return filteredCommunityObjectIds;
};

exports.retrieveCommunityOwnerDetails = async (communityObjectId) => {
  const communityRole = await CommunityRoleModel.findOne(
    {
      communityObjectId,
      role: aclRoles.OWNER,
    },
    { userObjectId: 1 }
  ).lean();

  if (!communityRole) {
    throw new ResourceNotFoundError('Community owner not found');
  }

  const user = await UserModel.findOne(
    {
      _id: communityRole.userObjectId,
    },
    { learner: 1 }
  ).lean();

  if (!user) {
    throw new ResourceNotFoundError('User not found');
  }

  const learner = await LearnerModel.findById(user.learner)
    .select(
      'firstName lastName profileImage socialMedia countryId countryCode'
    )
    .lean();

  if (!learner) {
    throw new ResourceNotFoundError('Learner not found');
  }

  let countryCode = learner.countryCode;
  if (!countryCode) {
    const countryInfo =
      await countryInfoMappingService.getCountryInfoByIdFromMemoryCache(
        learner.countryId
      );
    countryCode = countryInfo?.countryCode;
  }

  return {
    userObjectId: communityRole.userObjectId,
    learnerObjectId: user.learner,
    ...learner,
    countryCode,
  };
};

const getRegionAnalyticsCategory = (memberCount) => {
  if (memberCount < 100) {
    return REGION_ANALYTICS_CATEGORY.LESS_THAN_100;
  }
  if (memberCount <= 1000) {
    return REGION_ANALYTICS_CATEGORY.FROM_100_TO_1000;
  }
  return REGION_ANALYTICS_CATEGORY.MORE_THAN_1000;
};

exports.retrieveDefaultCommunityTemplate = async (community) => {
  const owner = await this.retrieveCommunityOwnerDetails(community._id);
  const [countryInfo, memberCountInfo] = await Promise.all([
    countryInfoMappingService.getCountryInfoByCodeFromDBorCache(
      owner.countryCode
    ),
    countCommunityMembers({
      communityId: community._id,
    }),
  ]);
  const region = countryInfo?.region ?? COUNTRY_REGIONS.REST_OF_WORLD;
  const memberCount = memberCountInfo?.summary.memberCount ?? 0;
  const category = getRegionAnalyticsCategory(memberCount);

  const analytics = await RegionAnalyticsModel.findOne({
    region,
    category,
  }).lean();

  const { averageMemberCount = 0, averageGMVInUsd = 0 } = analytics ?? {};

  const communityTemplate = {};
  if (
    category === REGION_ANALYTICS_CATEGORY.LESS_THAN_100 ||
    memberCount < averageMemberCount
  ) {
    communityTemplate.predictedEarningsInUsd = averageGMVInUsd;
  } else if (category === REGION_ANALYTICS_CATEGORY.FROM_100_TO_1000) {
    const moreThan1000Analytics = await RegionAnalyticsModel.findOne({
      region,
      category: REGION_ANALYTICS_CATEGORY.MORE_THAN_1000,
    }).lean();
    communityTemplate.predictedEarningsInUsd =
      moreThan1000Analytics.averageGMVInUsd;
  } else {
    communityTemplate.predictedEarningsInUsd =
      (memberCount / averageMemberCount) * averageGMVInUsd;
  }

  communityTemplate.predictedEarningsInLocalCurrency =
    communityTemplate.predictedEarningsInUsd;
  communityTemplate.localCurrency = DEFAULT_CURRENCY;
  return communityTemplate;
};

exports.convertTemplatesCurrency = async (
  templates,
  baseCurrency,
  localCurrency = DEFAULT_CURRENCY
) => {
  if (localCurrency === baseCurrency) {
    return templates;
  }
  const paymentBackendRpc = new PaymentBackendRpc();
  await paymentBackendRpc.init();

  const conversionRateData = await paymentBackendRpc.getConversionRate(
    localCurrency,
    baseCurrency
  );

  const finalTemplates = await Promise.all(
    templates.map(async (template) => {
      let conversionRate = conversionRateData.conversionRate;
      if (template.localCurrency !== localCurrency) {
        const newConversionRateData =
          await paymentBackendRpc.getConversionRate(
            template.localCurrency,
            baseCurrency
          );
        conversionRate = newConversionRateData.conversionRate;
      }
      const newTemplate = { ...template };
      if (localCurrency !== baseCurrency) {
        newTemplate.localCurrency = baseCurrency;
        newTemplate.predictedEarningsInLocalCurrency =
          normalizeAmountByCurrency(
            Math.ceil(
              template.predictedEarningsInLocalCurrency * conversionRate
            )
          );
        if (template.pricingConfig?.minAmount) {
          newTemplate.pricingConfig.minAmount = normalizeAmountByCurrency(
            Math.ceil(template.pricingConfig.minAmount * conversionRate)
          );
        }
        if (template.pricingConfig?.suggestedAmount) {
          newTemplate.pricingConfig.suggestedAmount =
            normalizeAmountByCurrency(
              Math.ceil(
                template.pricingConfig.suggestedAmount * conversionRate
              )
            );
        }
      }
      return newTemplate;
    })
  );
  return finalTemplates;
};
