const ObjectId = require('mongoose').Types.ObjectId;

const CommunityReferralReward = require('../../models/communityReferral/communityReferralReward.model');
const CommunityPlanOrderModel = require('../../models/plan/communityPlanOrder.model');
const CommunityModel = require('../../communitiesAPI/models/community.model');

const {
  GET_REFEREE_STATUS_FILTER,
  GET_REFEREE_SORT_BY,
  REFERRAL_REWARD_PLAN_ORDER_STATUS,
} = require('./constants');
const { ORDER_STATUS } = require('../plan/constants');

function getRefereeFilterQuery({ communityId, status, currentDate }) {
  const filter = { referrerCommunityObjectId: new ObjectId(communityId) };
  switch (status) {
    case GET_REFEREE_STATUS_FILTER.ACTIVE: {
      filter.$or = [
        { planOrderStatus: ORDER_STATUS.CURRENT },
        {
          planOrderStatus: ORDER_STATUS.CANCELLED,
          cancelledAt: { $gt: currentDate },
        },
      ];
      break;
    }
    case GET_REFEREE_STATUS_FILTER.CANCELLED: {
      filter.planOrderStatus = ORDER_STATUS.CANCELLED;
      filter.cancelledAt = { $lte: currentDate };
      break;
    }
    case GET_REFEREE_STATUS_FILTER.ALL:
    default:
      break;
  }
  return filter;
}

function getRefereeSortQuery({ sortBy, sortOrder }) {
  let finalSortBy = 'createdAt';
  switch (sortBy) {
    case GET_REFEREE_SORT_BY.REFERRAL_EARNING: {
      finalSortBy = 'earningAnalytics.revenueInLocalCurrency';
      break;
    }
    case GET_REFEREE_SORT_BY.JOIN_DATE:
    default:
      break;
  }
  return { [finalSortBy]: sortOrder };
}

async function formatRefereeList(
  referralRewards,
  currentDate = new Date()
) {
  const planOrderObjectIds = [];
  const communityObjectIds = [];

  referralRewards.forEach((reward) => {
    planOrderObjectIds.push(reward.refereePlanOrderObjectId);
    communityObjectIds.push(reward.refereeCommunityObjectId);
  });

  const [planOrders, communities] = await Promise.all([
    CommunityPlanOrderModel.find(
      { _id: { $in: planOrderObjectIds } },
      {
        interval: 1,
        intervalCount: 1,
        entityType: 1,
        planHistory: 1,
        billingCycle: 1,
      }
    ).lean(),
    CommunityModel.find(
      { _id: { $in: communityObjectIds } },
      { title: 1, link: 1, thumbnailImgData: 1 }
    ).lean(),
  ]);

  const planOrderMap = new Map();
  const communityMap = new Map();
  planOrders.forEach((planOrder) =>
    planOrderMap.set(planOrder._id.toString(), planOrder)
  );
  communities.forEach((community) =>
    communityMap.set(community._id.toString(), community)
  );

  const referees = referralRewards.map((referee) => {
    const planOrder = planOrderMap.get(
      referee.refereePlanOrderObjectId.toString()
    );
    const community = communityMap.get(
      referee.refereeCommunityObjectId.toString()
    );

    const referralPlanStatus =
      referee.planOrderStatus === ORDER_STATUS.CANCELLED &&
      referee.cancelledAt.toISOString() <= currentDate.toISOString()
        ? REFERRAL_REWARD_PLAN_ORDER_STATUS.CANCELLED
        : REFERRAL_REWARD_PLAN_ORDER_STATUS.ACTIVE;

    const selectedPlanHistory = planOrder.planHistory?.find(
      (history) => history.billingCycle === planOrder.billingCycle
    );

    const interval = selectedPlanHistory?.interval ?? planOrder.interval;
    const intervalCount =
      selectedPlanHistory?.intervalCount ?? planOrder.intervalCount;

    return {
      communityProfileImage:
        community.thumbnailImgData?.mobileImgData?.src,
      communityTitle: community.title,
      communityLink: community.link,
      joinedDate: referee.createdAt,
      planInterval: interval,
      planIntervalCount: intervalCount,
      planType: planOrder.entityType,
      referralEarnings: {
        currency: referee.currency,
        amount: referee.earningAnalytics.revenueInLocalCurrency,
      },
      referralPlanStatus,
    };
  });
  return referees;
}

exports.getRefereeList = async ({
  communityId,
  status,
  pageSize = 10,
  pageNo = 1,
  sortBy,
  sortOrder,
}) => {
  const currentDate = new Date();
  const filterQuery = getRefereeFilterQuery({
    communityId,
    status,
    currentDate,
  });
  const sortQuery = getRefereeSortQuery({
    sortBy,
    sortOrder,
  });
  const [
    referralRewards,
    total,
    allReferees,
    activeReferees,
    cancelledReferees,
  ] = await Promise.all([
    CommunityReferralReward.aggregate([
      {
        $match: filterQuery,
      },
      {
        $sort: sortQuery,
      },
      {
        $skip: (pageNo - 1) * pageSize,
      },
      {
        $limit: pageSize,
      },
    ]),
    CommunityReferralReward.countDocuments(filterQuery),
    CommunityReferralReward.countDocuments({
      referrerCommunityObjectId: new ObjectId(communityId),
    }),
    CommunityReferralReward.countDocuments({
      referrerCommunityObjectId: new ObjectId(communityId),
      $or: [
        { planOrderStatus: ORDER_STATUS.CURRENT },
        {
          planOrderStatus: ORDER_STATUS.CANCELLED,
          cancelledAt: { $gt: currentDate },
        },
      ],
    }),
    CommunityReferralReward.countDocuments({
      referrerCommunityObjectId: new ObjectId(communityId),
      cancelledAt: { $lte: currentDate },
      planOrderStatus: ORDER_STATUS.CANCELLED,
    }),
  ]);

  const referees = await formatRefereeList(referralRewards, currentDate);
  const metadata = {
    total,
    limit: pageSize,
    page: pageNo,
    pages: Math.ceil(total / pageSize),
  };

  return {
    overview: {
      allReferees,
      activeReferees,
      cancelledReferees,
    },
    referees,
    metadata,
  };
};
