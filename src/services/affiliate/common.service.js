const { ObjectId } = require('mongoose').Types;

const CommunityModel = require('../../communitiesAPI/models/community.model');
const LearnerModel = require('../../models/learners.model');
const CommunityRoleModel = require('../../communitiesAPI/models/communityRole.model');
const SubscriptionModel = require('../../communitiesAPI/models/communitySubscriptions.model');
const CountryInfoMappingModel = require('../../models/countryInfoMapping.model');
const AffiliateProductModel = require('../../models/affiliate/affiliateProducts.model');
const communityProductService = require('../common/communityProducts.service');
const { ResourceNotFoundError } = require('../../utils/error.util');
const {
  COMMUNITY_SUBSCRIPTION_STATUSES,
  PURCHASE_TYPE,
} = require('../../constants/common');
const { aclRoles } = require('../../communitiesAPI/constants');
const nameUtils = require('../../utils/name.util');
const { PRODUCT_TYPE } = require('../product/constants');

exports.retrieveAffiliateProductsSummary = async ({
  communityObjectId,
}) => {
  const affiliateProductsSummary = await AffiliateProductModel.aggregate([
    {
      $match: {
        communityObjectId: new ObjectId(communityObjectId),
        isActive: true,
      },
    },
    {
      $group: {
        _id: null,
        totalProducts: {
          $sum: 1,
        },
        minCommissionPercentage: {
          $min: '$config.commissionPercentage',
        },
        maxCommissionPercentage: {
          $max: '$config.commissionPercentage',
        },
      },
    },
    {
      $project: {
        _id: 0,
      },
    },
  ]);

  return affiliateProductsSummary[0];
};

exports.retrieveActiveCommunity = async (communityObjectId) => {
  const community = await CommunityModel.findOne({
    _id: communityObjectId,
    isActive: true,
  }).lean();

  if (!community) {
    throw new ResourceNotFoundError('Community not found');
  }

  return community;
};

exports.retrieveOwnerRole = async (communityObjectId) => {
  const role = await CommunityRoleModel.findOne(
    {
      communityObjectId,
      role: aclRoles.OWNER,
    },
    { email: 1, role: 1 }
  ).lean();

  return role;
};

exports.retrieveActiveLearner = async (learnerObjectId) => {
  const learner = await LearnerModel.findOne({
    _id: learnerObjectId,
    isActive: true,
  }).lean();

  if (!learner) {
    throw new ResourceNotFoundError(
      `Learner not found for ${learnerObjectId}`
    );
  }

  return learner;
};

exports.retrieveActiveSubscription = async ({
  subscriptionObjectId = null,
  learnerObjectId = null,
  email,
  communityCode,
}) => {
  const matchFilter = {
    email,
    communityCode,
    $or: [
      { status: COMMUNITY_SUBSCRIPTION_STATUSES.CURRENT },
      {
        status: COMMUNITY_SUBSCRIPTION_STATUSES.CANCELLED,
        cancelledAt: { $gte: new Date() },
      },
    ],
  };

  if (subscriptionObjectId) {
    matchFilter._id = subscriptionObjectId;
  } else if (learnerObjectId) {
    matchFilter.learnerObjectId = learnerObjectId;
  } else {
    throw new Error('Missing argument');
  }

  const subscription = await SubscriptionModel.findOne(matchFilter).lean();

  if (!subscription) {
    throw new ResourceNotFoundError(`Subscription not found for ${email}`);
  }

  return subscription;
};

exports.retrieveSubscription = async ({
  learnerObjectId,
  communityCode,
}) => {
  const matchFilter = {
    learnerObjectId,
    communityCode,
  };

  const subscription = await SubscriptionModel.findOne(matchFilter, {
    learnerObjectId: 1,
  }).lean();

  if (!subscription) {
    throw new ResourceNotFoundError('Subscription not found');
  }

  return subscription;
};

exports.retrieveIsManager = async (email, affiliate) => {
  const { communityObjectId } = affiliate;

  const managerRole = await CommunityRoleModel.findOne({
    communityObjectId,
    email,
    role: aclRoles.MANAGER,
  }).lean();

  return !!managerRole;
};

exports.retrieveLearnerCache = async (learnerObjectIds) => {
  const learners = await LearnerModel.find(
    {
      _id: { $in: learnerObjectIds },
    },
    { profileImage: 1, firstName: 1, lastName: 1, email: 1, countryId: 1 }
  ).lean();

  const countryInfoMappings = await CountryInfoMappingModel.find(
    {
      countryId: { $in: learners.map(({ countryId }) => countryId) },
    },
    { countryId: 1, countryCode: 1, country: 1 }
  ).lean();

  const countryInfoMappingCache = countryInfoMappings.reduce(
    (acc, countryInfoMapping) => {
      acc.set(countryInfoMapping.countryId, countryInfoMapping);
      return acc;
    },
    new Map()
  );

  const learnerCache = learners.reduce((acc, learner) => {
    const {
      profileImage,
      firstName,
      lastName,
      email,
      _id: learnerObjectId,
      countryId,
    } = learner;

    const fullName = nameUtils.getName(firstName, lastName);

    const countryInfo = countryInfoMappingCache.get(countryId);

    acc.set(learnerObjectId.toString(), {
      profileImage,
      fullName,
      email,
      country: countryInfo.country,
      countryCode: countryInfo.countryCode,
    });

    return acc;
  }, new Map());

  return learnerCache;
};

exports.retrieveEntitiesCache = async (affiliateProducts) => {
  const subscriptionObjectIds = [];
  const eventObjectIds = [];
  const productObjectIds = [];
  const programObjectIds = [];

  affiliateProducts.forEach((affiliateProduct) => {
    const { entityType, entityObjectId } = affiliateProduct;

    switch (entityType) {
      case PURCHASE_TYPE.SUBSCRIPTION:
        subscriptionObjectIds.push(entityObjectId);
        break;
      case PURCHASE_TYPE.EVENT:
        eventObjectIds.push(entityObjectId);
        break;
      case PURCHASE_TYPE.FOLDER:
      case PURCHASE_TYPE.SESSION:
      case PRODUCT_TYPE.DIGITAL_FILES:
      case PRODUCT_TYPE.COURSE:
        productObjectIds.push(entityObjectId);
        break;
      case PURCHASE_TYPE.CHALLENGE:
        programObjectIds.push(entityObjectId);
        break;
      default:
        break;
    }
  });

  const entitiesCache =
    await communityProductService.retrieveEntitiesCache({
      subscriptionObjectIds,
      eventObjectIds,
      productObjectIds,
      programObjectIds,
    });

  return entitiesCache;
};
