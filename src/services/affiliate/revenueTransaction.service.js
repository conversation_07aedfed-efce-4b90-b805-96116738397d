const { ObjectId } = require('mongoose').Types;
const { PRODUCT_TYPE } = require('@services/product/constants');
const { PURCHASE_TYPE } = require('@constants/common');
const { PAYABLE_PURCHASE_TYPES } = require('../../constants/common');
const RevenueTransactionModel = require('../../models/revenueTransaction.model');

exports.retrieveAffiliateCommissionEarningsAndSalesCache = async ({
  communityObjectId = null,
  learnerObjectId,
}) => {
  const matchFilter = {
    'affiliate.learnerObjectId': new ObjectId(learnerObjectId),
    purchaseType: {
      $in: PAYABLE_PURCHASE_TYPES,
    },
  };

  if (communityObjectId) {
    matchFilter.communityObjectId = new ObjectId(communityObjectId);
  }

  const addFields = {
    multiplier: {
      $cond: {
        if: {
          $eq: ['$transactionType', 'INBOUND'],
        },
        then: 1,
        else: -1,
      },
    },
  };

  const groupEntityQuery = {
    _id: {
      purchaseType: '$purchaseType',
      entityObjectId: '$entityObjectId',
      currency: '$amountBreakdownInLocalCurrency.currency',
    },
    sales: {
      $sum: '$multiplier',
    },
    commissionEarning: {
      $sum: '$amountBreakdownInLocalCurrency.affiliateCommissionAmount',
    },
  };

  const groupCurrencyQuery = {
    _id: {
      purchaseType: '$_id.purchaseType',
      entityObjectId: '$_id.entityObjectId',
      sales: '$sales',
    },
    commissionEarnings: {
      $push: {
        currency: '$_id.currency',
        amount: '$commissionEarning',
      },
    },
  };

  const projection = {
    _id: 0,
    purchaseType: '$_id.purchaseType',
    entityObjectId: '$_id.entityObjectId',
    sales: '$_id.sales',
    commissionEarnings: '$commissionEarnings',
  };

  const commissionEarningsAndSales =
    await RevenueTransactionModel.aggregate([
      {
        $match: matchFilter,
      },
      {
        $addFields: addFields,
      },
      {
        $group: groupEntityQuery,
      },
      {
        $group: groupCurrencyQuery,
      },
      {
        $project: projection,
      },
    ]);

  const commissionEarningsAndSalesCache =
    commissionEarningsAndSales.reduce((acc, data) => {
      const { purchaseType, entityObjectId, sales, commissionEarnings } =
        data;

      const key = `${purchaseType}-${entityObjectId}`;
      const value = { sales, commissionEarnings };

      acc.set(key, value);

      // Add value for folder for backward compatibility
      // - FOLDER: for old affiliate earnings
      // - DIGITAL_FILES, COURSE: for new affiliate earnings
      if (purchaseType === PURCHASE_TYPE.FOLDER) {
        acc.set(`${PRODUCT_TYPE.DIGITAL_FILES}-${entityObjectId}`, value);
        acc.set(`${PRODUCT_TYPE.COURSE}-${entityObjectId}`, value);
      }
      return acc;
    }, new Map());

  return commissionEarningsAndSalesCache;
};
