/* eslint-disable no-param-reassign */
const { PRODUCT_TYPE } = require('../product/constants');
const AffiliateModel = require('../../models/affiliate/affiliates.model');
const AffiliateProductModel = require('../../models/affiliate/affiliateProducts.model');
const CommunityModel = require('../../communitiesAPI/models/community.model');
const { ParamError } = require('../../utils/error.util');
const communityProductService = require('../common/communityProducts.service');
const revenueTransactionService = require('./revenueTransaction.service');
const commonService = require('./common.service');
const paginationUtils = require('../../utils/pagination.util');
const {
  PURCHASE_TYPE,
  DEFAULT_CURRENCY,
  AFFILIATE_STATUS,
} = require('../../constants/common');
const {
  retrievePaymentFeeStructure,
} = require('../config/paymentFeeStructureConfig.service');
const FeeService = require('../../communitiesAPI/services/common/fee.service');

const getCommunityFee = async (community) => {
  const paymentFeeStructure = await retrievePaymentFeeStructure({
    baseCurrency: community.baseCurrency,
    planType: community.config?.planType,
  });
  const effectivePayoutFeeConfig =
    FeeService.retrieveEffectivePayoutFeeConfig(
      community.payoutFeeConfigs
    );

  const effectiveBasePayoutFeeConfig =
    FeeService.retrieveEffectivePayoutFeeConfig(
      community.basePayoutFeeConfigs
    );

  const paymentDetails = FeeService.getCommunityPaymentFeeDetails({
    currency: community.baseCurrency,
    communityCreatedAt: community.createdAt,
    paymentFeeStructure,
    customPayoutFeeConfig: effectivePayoutFeeConfig,
    basePayoutFeeConfig: effectiveBasePayoutFeeConfig,
    communityCountry: community.countryCreatedIn,
    baseCurrency: community.baseCurrency,
  });

  return paymentDetails;
};

exports.retrieveMemberAffiliateProducts = async ({
  pageNo,
  pageSize,
  learnerObjectId,
}) => {
  const affiliates = await AffiliateModel.find({ learnerObjectId })
    .sort({ communityObjectId: 1 })
    .limit(pageNo * pageSize)
    .lean();

  if (affiliates.length === 0) {
    return {
      summary: {
        sales: 0,
        commissionEarnings: [{ currency: DEFAULT_CURRENCY, amount: 0 }],
      },
      communities: [],
      entities: [],
      metadata: paginationUtils.getPaginationMetadata(pageNo, pageSize, 0),
    };
  }

  const communityObjectIds = [];

  const summary = {
    sales: 0,
  };

  const commissionEarningsCache = new Map();

  const affiliateCache = new Map();

  affiliates.forEach((affiliate) => {
    const { communityObjectId, sales, commissionEarnings } = affiliate;

    affiliateCache.set(communityObjectId.toString(), affiliate);

    communityObjectIds.push(communityObjectId);
    summary.sales += sales;

    commissionEarnings.forEach(({ currency, amount }) => {
      if (commissionEarningsCache.has(currency)) {
        const currentAmount = commissionEarningsCache.get(currency);
        commissionEarningsCache.set(currency, currentAmount + amount);
      } else {
        commissionEarningsCache.set(currency, amount);
      }
    });
  });

  summary.commissionEarnings = [...commissionEarningsCache.entries()].map(
    ([currency, amount]) => {
      const commissionEarning = { currency, amount };
      return commissionEarning;
    }
  );

  const [
    affiliateProducts,
    totalAffiliateProductSize,
    communities,
    commissionEarningsAndSalesCache,
  ] = await Promise.all([
    AffiliateProductModel.find({
      communityObjectId: {
        $in: communityObjectIds,
      },
    })
      .sort({ communityObjectId: 1 })
      .skip((pageNo - 1) * pageSize)
      .limit(pageSize)
      .lean(),
    AffiliateProductModel.countDocuments({
      communityObjectId: {
        $in: communityObjectIds,
      },
    }),
    CommunityModel.find(
      { _id: { $in: communityObjectIds } },
      {
        baseCurrency: 1,
        title: 1,
        link: 1,
        thumbnailImgData: 1,
        fullScreenBannerImgData: 1,
        By: 1,
        createdBy: 1,
        createdAt: 1,
        code: 1,
        isWhatsappExperienceCommunity: 1,
        payoutFeeConfigs: 1,
        basePayoutFeeConfigs: 1,
        passOnPaymentGatewayFee: 1,
        passOnTakeRate: 1,
        countryCreatedIn: 1,
        config: 1,
      }
    ).lean(),
    revenueTransactionService.retrieveAffiliateCommissionEarningsAndSalesCache(
      { learnerObjectId }
    ),
  ]);

  const communityCache = communities.reduce((acc, community) => {
    acc.set(community._id.toString(), community);
    return acc;
  }, new Map());

  await Promise.all(
    communities.map(async (community) => {
      const paymentDetails = await getCommunityFee(community);
      community.paymentDetails = paymentDetails;
      delete community.payoutFeeConfigs;
      delete community.basePayoutFeeConfigs;
      communityCache.set(community._id.toString(), community);
    })
  );

  const entitiesCache = await commonService.retrieveEntitiesCache(
    affiliateProducts
  );

  const affiliateProductsWithEntityInfo = affiliateProducts
    .map((affiliateProduct) => {
      const { entityType, entityObjectId, communityObjectId } =
        affiliateProduct;

      const key = `${entityType}-${entityObjectId}`;
      const entity = entitiesCache.get(key) ?? {};

      const community = communityCache.get(communityObjectId.toString());
      const affiliate = affiliateCache.get(communityObjectId.toString());

      const {
        sales = 0,
        commissionEarnings = [
          { currency: community.baseCurrency, amount: 0 },
        ],
      } = commissionEarningsAndSalesCache.get(key) ?? {};

      const affiliateProductWithEntityInfo = {
        ...entity,
        ...affiliateProduct,
        sales,
        commissionEarnings,
        affiliateStatus: affiliate.status,
      };

      if (affiliate.status === AFFILIATE_STATUS.ACTIVE) {
        affiliateProductWithEntityInfo.affiliateCode =
          affiliate.affiliateCode;
      }

      const customConfig = affiliate.customConfig ?? [];
      const affiliateProductCustomConfig = customConfig.find(
        (config) =>
          config.affiliateProductObjectId.toString() ===
          affiliateProduct._id.toString()
      );

      if (affiliateProductCustomConfig?.commissionPercentage != null) {
        affiliateProductWithEntityInfo.config.commissionPercentage =
          affiliateProductCustomConfig.commissionPercentage;
      }

      return affiliateProductWithEntityInfo;
    })
    .filter((product) => product !== null);

  return {
    summary,
    communities,
    entities: affiliateProductsWithEntityInfo,
    metadata: paginationUtils.getPaginationMetadata(
      pageNo,
      pageSize,
      totalAffiliateProductSize
    ),
  };
};

exports.retrieveAffiliateAndNonProducts = async ({
  pageNo,
  pageSize,
  communityObjectId,
}) => {
  const totalPageSize = pageNo * pageSize;

  const [
    affiliateProducts,
    communityProducts,
    earningsAndSales,
    totalEntitiesSize,
  ] = await Promise.all([
    AffiliateProductModel.find({
      communityObjectId,
    }).lean(),
    communityProductService.retrieveAllProducts({
      communityObjectId,
      withSubscriptionType: true,
      onlyPublished: true,
      withAffiliateEarnings: true,
      pageSize: totalPageSize,
    }),
    communityProductService.aggregateAffiliateEarningsAndSales(
      communityObjectId
    ),
    communityProductService.retrieveTotalEntitiesSize({
      communityObjectId,
      withSubscriptionType: true,
      onlyPublished: true,
      withAffiliateEarnings: true,
    }),
  ]);

  const affiliateProductCache = affiliateProducts.reduce(
    (acc, affiliateProduct) => {
      const key = `${affiliateProduct.entityType}-${affiliateProduct.entityObjectId}`;
      acc.set(key, affiliateProduct);

      // There are retriever of cache for digital files due to community folder type
      // Add additional digital file type to cater for affiliate product
      if (affiliateProduct.entityType === PURCHASE_TYPE.FOLDER) {
        const digitalFilesKey = `${PRODUCT_TYPE.DIGITAL_FILES}-${affiliateProduct.entityObjectId}`;
        acc.set(digitalFilesKey, affiliateProduct);
      }
      return acc;
    },
    new Map()
  );

  const start = (pageNo - 1) * pageSize;
  const end = start + pageSize;

  const communityProductsWithAffiliateSettings = communityProducts.products
    .slice(start, end)
    .map((communityProduct) => {
      const key = `${communityProduct.type}-${communityProduct.entityObjectId}`;
      const affiliateProduct = affiliateProductCache.get(key);

      const communityProductWithAffiliateSettings = {
        ...communityProduct,
        isActive: affiliateProduct?.isActive ?? false,
        sales: communityProduct.sales ?? 0,
        earnings: communityProduct.earnings ?? 0,
      };

      if (affiliateProduct) {
        communityProductWithAffiliateSettings.affiliateProductObjectId =
          affiliateProduct._id;
        communityProductWithAffiliateSettings.config =
          affiliateProduct.config;
      }

      return communityProductWithAffiliateSettings;
    });

  return {
    summary: earningsAndSales,
    entities: communityProductsWithAffiliateSettings,
    metadata: paginationUtils.getPaginationMetadata(
      pageNo,
      pageSize,
      totalEntitiesSize
    ),
  };
};

function retrieveEntityObjectIds(affiliateProducts) {
  const subscriptionObjectIds = [];
  const eventObjectIds = [];
  const productObjectIds = [];
  const programObjectIds = [];

  affiliateProducts.forEach((affiliateProduct) => {
    const { entityType, entityObjectId } = affiliateProduct;

    switch (entityType) {
      case PURCHASE_TYPE.SUBSCRIPTION:
        subscriptionObjectIds.push(entityObjectId);
        break;
      case PURCHASE_TYPE.EVENT:
        eventObjectIds.push(entityObjectId);
        break;
      case PURCHASE_TYPE.FOLDER:
      case PURCHASE_TYPE.SESSION:
      case PRODUCT_TYPE.COURSE:
      case PRODUCT_TYPE.DIGITAL_FILES:
        productObjectIds.push(entityObjectId);
        break;
      case PURCHASE_TYPE.CHALLENGE:
        programObjectIds.push(entityObjectId);
        break;
      default:
        throw new Error(`${entityType} not supported`);
    }
  });

  return {
    subscriptionObjectIds,
    eventObjectIds,
    productObjectIds,
    programObjectIds,
  };
}

exports.retrieveAffiliateProducts = async ({
  pageNo,
  pageSize,
  communityObjectId,
  affiliate,
}) => {
  const totalPageSize = pageNo * pageSize;

  const matchFilter = {
    communityObjectId,
  };

  const [
    affiliateProducts,
    totalAffiliateProductSize,
    commissionEarningsAndSalesCache,
    community,
  ] = await Promise.all([
    AffiliateProductModel.find(matchFilter).limit(totalPageSize).lean(),
    AffiliateProductModel.countDocuments(matchFilter),
    revenueTransactionService.retrieveAffiliateCommissionEarningsAndSalesCache(
      { communityObjectId, learnerObjectId: affiliate.learnerObjectId }
    ),
    commonService.retrieveActiveCommunity(communityObjectId),
  ]);

  const {
    subscriptionObjectIds,
    eventObjectIds,
    productObjectIds,
    programObjectIds,
  } = retrieveEntityObjectIds(affiliateProducts);

  const entitiesCache =
    await communityProductService.retrieveEntitiesCache({
      subscriptionObjectIds,
      eventObjectIds,
      productObjectIds,
      programObjectIds,
    });

  const { customConfig = [] } = affiliate;

  const affiliateProductsWithAdditionalInfo = affiliateProducts
    .slice((pageNo - 1) * pageSize, totalPageSize)
    .map((affiliateProduct) => {
      const {
        entityType,
        entityObjectId,
        _id: affiliateProductObjectId,
      } = affiliateProduct;

      const key = `${entityType}-${entityObjectId}`;
      const entity = entitiesCache.get(key) ?? {};

      const {
        sales = 0,
        commissionEarnings = [
          { currency: community.baseCurrency, amount: 0 },
        ],
      } = commissionEarningsAndSalesCache.get(key) ?? {};

      const affiliateProductWithEntityInfo = {
        ...entity,
        ...affiliateProduct,
        sales,
        commissionEarnings,
      };

      const affiliateProductCustomConfig = customConfig.find(
        (config) =>
          config.affiliateProductObjectId.toString() ===
          affiliateProductObjectId.toString()
      );

      if (affiliateProductCustomConfig?.commissionPercentage != null) {
        affiliateProductWithEntityInfo.config.commissionPercentage =
          affiliateProductCustomConfig.commissionPercentage;
      }

      return affiliateProductWithEntityInfo;
    });

  return {
    affiliateProducts: affiliateProductsWithAdditionalInfo,
    metadata: paginationUtils.getPaginationMetadata(
      pageNo,
      pageSize,
      totalAffiliateProductSize
    ),
  };
};

exports.addAffiliateProduct = async ({
  entityObjectId,
  entityType,
  commissionPercentage,
  messageForAffiliates,
  communityObjectId,
}) => {
  let affiliateProductEntityType = entityType;

  if ([PRODUCT_TYPE.DIGITAL_FILES].includes(entityType)) {
    affiliateProductEntityType = PURCHASE_TYPE.FOLDER;
  }

  const createdAffiliateProduct =
    await AffiliateProductModel.findOneAndUpdate(
      {
        entityObjectId,
        entityType: affiliateProductEntityType,
        communityObjectId,
        isActive: { $ne: true },
      },
      {
        config: {
          commissionPercentage,
          messageForAffiliates,
        },
        isActive: true,
      },
      {
        new: true,
        upsert: true,
      }
    ).lean();

  return createdAffiliateProduct;
};

exports.updateAffiliateProduct = async ({
  affiliateProductObjectId,
  isActive,
  commissionPercentage,
  messageForAffiliates,
  communityObjectId,
}) => {
  if (isActive) {
    throw new ParamError(
      'Currently not supported to update isActive true'
    );
  }

  const updateQuery = {};

  if (isActive != null) {
    updateQuery.isActive = isActive;
  }

  if (commissionPercentage != null) {
    updateQuery['config.commissionPercentage'] = commissionPercentage;
  }

  if (messageForAffiliates != null) {
    updateQuery['config.messageForAffiliates'] = messageForAffiliates;
  }

  const updatedAffiliateProduct =
    await AffiliateProductModel.findOneAndUpdate(
      { _id: affiliateProductObjectId, communityObjectId, isActive: true },
      updateQuery,
      {
        new: true,
      }
    ).lean();

  if (!updatedAffiliateProduct) {
    throw new ParamError('Affiliate product is not updated');
  }

  return updatedAffiliateProduct;
};

exports.disableAffiliateProduct = async ({
  communityObjectId,
  entityType,
  entityObjectId,
  session = undefined,
}) => {
  const affiliateResult = await AffiliateProductModel.updateOne(
    {
      communityObjectId,
      entityType,
      entityObjectId,
      isActive: true,
    },
    {
      isActive: false,
    },
    { session }
  );

  return affiliateResult;
};

exports.disableAffiliateProducts = async ({
  communityObjectId,
  session = undefined,
}) => {
  const affiliateResult = await AffiliateProductModel.updateMany(
    {
      communityObjectId,
      isActive: true,
    },
    {
      isActive: false,
    },
    { session }
  );

  return affiliateResult;
};
