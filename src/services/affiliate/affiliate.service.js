const { ObjectId } = require('mongoose').Types;
const { Readable } = require('stream');

const {
  AFFILIATE_STATUS,
  PURCHASE_TYPE,
} = require('../../constants/common');
const PrimaryMongooseConnection = require('../../rpc/primaryMongooseConnection');
const AffiliateModel = require('../../models/affiliate/affiliates.model');
const AffiliateProductModel = require('../../models/affiliate/affiliateProducts.model');
const LearnerModel = require('../../models/learners.model');
const regexUtils = require('../../utils/regex.util');
const paginationUtils = require('../../utils/pagination.util');
const commonService = require('./common.service');
const codeGenerationUtil = require('../../utils/codeGeneration.util');
const affiliateMailService = require('./affiliateMail.service');
const {
  ResourceNotFoundError,
  ParamError,
  InternalError,
} = require('../../utils/error.util');
const {
  retrieveLearnerObjectIdViaSearch,
} = require('../membership/utils/membershipSearch.utils');
const { PRODUCT_TYPE } = require('../product/constants');

exports.generateRetrieveAffiliatesMatchFilter = async ({
  search = '',
  status,
  communityObjectId,
}) => {
  const matchFilter = {
    communityObjectId: new ObjectId(communityObjectId),
  };

  const searchWithEscapedRegexSign = regexUtils.escapeRegExp(search);

  if (searchWithEscapedRegexSign !== '') {
    const learnerObjectIds = await retrieveLearnerObjectIdViaSearch(
      searchWithEscapedRegexSign,
      communityObjectId
    );

    matchFilter.learnerObjectId = { $in: learnerObjectIds };
  }

  if (status && status !== 'ALL') {
    matchFilter.status = status;
  }

  return matchFilter;
};

function addLearnerInfoToAffiliate(affiliate, learnerCache, community) {
  const learner =
    learnerCache.get(affiliate.learnerObjectId.toString()) ?? {};

  const {
    _id,
    sales,
    commissionEarnings = [],
    status: affiliateStatus,
    joinedDate,
    learnerObjectId,
    customConfig,
  } = affiliate;

  const { baseCurrency } = community;

  if (commissionEarnings.length === 0) {
    commissionEarnings.push({ currency: baseCurrency, amount: 0 });
  }

  const affiliateWithLearner = {
    ...learner,
    _id,
    sales,
    commissionEarnings,
    customConfig,
    status: affiliateStatus,
    joinedDate,
    learnerObjectId,
  };

  return affiliateWithLearner;
}

exports.existsActiveAffiliate = async ({
  communityObjectId,
  learnerObjectId,
}) => {
  const affiliate = await AffiliateModel.findOne({
    communityObjectId,
    learnerObjectId,
    status: AFFILIATE_STATUS.ACTIVE,
  }).lean();

  return !!affiliate;
};

exports.retrieveAffiliates = async ({
  pageNo,
  pageSize,
  search,
  status,
  communityObjectId,
}) => {
  const matchFilter = await this.generateRetrieveAffiliatesMatchFilter({
    search,
    status,
    communityObjectId,
  });

  const [affiliates, totalAffiliateCount, community] = await Promise.all([
    AffiliateModel.find(matchFilter)
      .skip((pageNo - 1) * pageSize)
      .limit(pageSize)
      .lean(),
    AffiliateModel.countDocuments(matchFilter),
    commonService.retrieveActiveCommunity(communityObjectId),
  ]);

  const learnerObjectIds = affiliates.map(
    (affiliate) => affiliate.learnerObjectId
  );

  const learnerCache = await commonService.retrieveLearnerCache(
    learnerObjectIds
  );

  const affiliatesWithLearner = affiliates.map((affiliate) =>
    addLearnerInfoToAffiliate(affiliate, learnerCache, community)
  );

  return {
    affiliates: affiliatesWithLearner,
    metadata: paginationUtils.getPaginationMetadata(
      pageNo,
      pageSize,
      totalAffiliateCount
    ),
  };
};

async function generateAffiliateCode(input, attempts = 5) {
  if (attempts <= 0) {
    throw new InternalError(
      'Unable to generate a unique affiliate code after multiple attempts'
    );
  }

  const randomCode = codeGenerationUtil.generateCode(4);
  const baseCode = `${encodeURIComponent(input.trim().replace(/ /g, '_'))
    .toString()
    .toLocaleUpperCase()
    .replace(/[^\w_]/g, '')}`;

  const affiliateCode = `${baseCode}_${randomCode}`;

  const existsAffiliate = await AffiliateModel.findOne({
    affiliateCode,
  })
    .read('primary')
    .lean();

  if (existsAffiliate) {
    return generateAffiliateCode(input, attempts - 1);
  }

  return affiliateCode;
}

async function getOrGenerateAffiliateCode(learner) {
  if (!learner) {
    throw new ResourceNotFoundError('Learner not found');
  }

  const existingAffiliate = await AffiliateModel.findOne(
    {
      learnerObjectId: learner._id,
    },
    { affiliateCode: 1 }
  ).lean();

  if (existingAffiliate?.affiliateCode) {
    return existingAffiliate.affiliateCode;
  }

  const affiliateCode = await generateAffiliateCode(
    learner.firstName || learner.lastName || learner.email?.split('@')?.[0]
  );

  return affiliateCode;
}

async function addAffiliate({ communityObjectId, learner }) {
  const affiliateCode = await getOrGenerateAffiliateCode(learner);

  const matchFilter = {
    communityObjectId,
    learnerObjectId: learner._id,
  };

  const existingAffiliate = await AffiliateModel.findOne(
    matchFilter
  ).lean();

  if (existingAffiliate?.status === AFFILIATE_STATUS.ACTIVE) {
    return null;
  }

  const affiliate = {
    communityObjectId,
    learnerObjectId: learner._id,
    sales: 0,
    affiliateCode,
  };

  const newAffiliate = await AffiliateModel.findOneAndUpdate(
    matchFilter,
    {
      $setOnInsert: affiliate,
      $set: {
        status: AFFILIATE_STATUS.ACTIVE,
        joinedDate: new Date(),
        customConfig: [],
      },
    },
    { upsert: true, new: true }
  ).lean();

  return newAffiliate;
}

exports.addAffiliates = async ({
  communityObjectId,
  affiliatesToBeAdded,
}) => {
  const [community, ownerRole, affiliateProductsSummary] =
    await Promise.all([
      commonService.retrieveActiveCommunity(communityObjectId),
      commonService.retrieveOwnerRole(communityObjectId),
      commonService.retrieveAffiliateProductsSummary({
        communityObjectId,
      }),
    ]);

  const addedAffiliates = [];

  for await (const affiliateToBeAdded of affiliatesToBeAdded) {
    const { email, subscriptionObjectId } = affiliateToBeAdded;

    if (ownerRole?.email === email) {
      throw new ParamError('Owner cannot apply as affiliate');
    }

    const subscription = await commonService.retrieveActiveSubscription({
      subscriptionObjectId,
      email,
      communityCode: community.code,
    });

    const learner = await commonService.retrieveActiveLearner(
      subscription.learnerObjectId
    );

    const createdAffiliate = await addAffiliate({
      communityObjectId,
      learner,
    });

    if (createdAffiliate) {
      await affiliateMailService.formatAndSendNewAffiliateEmail({
        subscription,
        learner,
        community,
        affiliateProductsSummary,
      });

      addedAffiliates.push(createdAffiliate);
    }
  }

  return addedAffiliates;
};

exports.retrieveAffiliateInfo = async ({
  communityObjectId,
  affiliateCode = null,
  entityObjectId,
  entityType,
}) => {
  let entityTypeList = [entityType];
  // For backward compatibility, we will search folder/course/digital files in affiliate products
  const folderTypeList = [
    PURCHASE_TYPE.FOLDER,
    PRODUCT_TYPE.COURSE,
    PRODUCT_TYPE.DIGITAL_FILES,
  ];
  if (folderTypeList.includes(entityType)) {
    entityTypeList = folderTypeList;
  }

  const [affiliate, affiliateProduct, communityHasActiveAffiliateProduct] =
    await Promise.all([
      affiliateCode
        ? AffiliateModel.findOne(
            {
              affiliateCode,
              communityObjectId,
              status: AFFILIATE_STATUS.ACTIVE,
            },
            {
              learnerObjectId: 1,
              customConfig: 1,
              affiliateCode: 1,
              status: 1,
            }
          ).lean()
        : null,
      AffiliateProductModel.findOne(
        {
          entityObjectId,
          entityType: { $in: entityTypeList },
          communityObjectId,
          isActive: true,
        },
        { config: 1 }
      ).lean(),
      AffiliateProductModel.findOne(
        {
          communityObjectId,
          isActive: true,
        },
        { _id: 1 }
      ).lean(),
    ]);

  const affiliateInfo = {
    isAffiliateProduct: !!affiliateProduct,
    communityHasActiveAffiliateProduct:
      !!communityHasActiveAffiliateProduct,
  };

  if (affiliate) {
    const learner = await LearnerModel.findOne(
      { _id: affiliate.learnerObjectId, isActive: true },
      {
        firstName: 1,
        lastName: 1,
        email: 1,
        profileImage: 1,
      }
    ).lean();

    affiliateInfo.learner = learner;
  }

  return affiliateInfo;
};

exports.retrieveAffiliateByLearner = async ({
  communityObjectId,
  learnerObjectId,
}) =>
  AffiliateModel.findOne({
    communityObjectId,
    learnerObjectId,
  }).lean();

exports.retrieveAffiliate = async ({
  communityObjectId,
  affiliateObjectId,
}) => {
  const [affiliate, community] = await Promise.all([
    AffiliateModel.findOne({
      _id: affiliateObjectId,
      communityObjectId,
    }).lean(),
    commonService.retrieveActiveCommunity(communityObjectId),
  ]);

  if (!affiliate) {
    throw new ResourceNotFoundError('Affiliate not found');
  }

  const learnerCache = await commonService.retrieveLearnerCache([
    affiliate.learnerObjectId,
  ]);

  const affiliateWithLearner = addLearnerInfoToAffiliate(
    affiliate,
    learnerCache,
    community
  );

  const isManager = await commonService.retrieveIsManager(
    affiliateWithLearner.email,
    affiliate
  );

  affiliateWithLearner.isManager = isManager;

  return affiliateWithLearner;
};

function validateCommissionPercentage(affiliateCommissionsToBeUpdated) {
  const affiliateCommissionPercentageOutOfBound =
    affiliateCommissionsToBeUpdated.find(
      ({ commissionPercentage }) =>
        commissionPercentage <= 0 || commissionPercentage > 100
    );

  if (affiliateCommissionPercentageOutOfBound) {
    throw new ParamError(
      `${affiliateCommissionPercentageOutOfBound.affiliateProductObjectId}: ${affiliateCommissionPercentageOutOfBound.commissionPercentage}. It must be 0 < commission percentage <= 100`
    );
  }
}

exports.updateAffiliate = async ({
  communityObjectId,
  affiliateObjectId,
  affiliateCommissionsToBeUpdated,
}) => {
  validateCommissionPercentage(affiliateCommissionsToBeUpdated);

  const affiliateProductObjectIds = affiliateCommissionsToBeUpdated.map(
    ({ affiliateProductObjectId }) =>
      new ObjectId(affiliateProductObjectId)
  );

  const [affiliate, affiliateProducts, community] = await Promise.all([
    AffiliateModel.findOne({
      _id: affiliateObjectId,
      communityObjectId,
    }).lean(),
    AffiliateProductModel.find({
      _id: {
        $in: affiliateProductObjectIds,
      },
      communityObjectId,
      isActive: true,
    }).lean(),
    commonService.retrieveActiveCommunity(communityObjectId),
  ]);

  if (!affiliate) {
    throw new ResourceNotFoundError('Affiliate not found');
  }

  const affiliateProductCache = affiliateProducts.reduce(
    (acc, affiliateProduct) => {
      const { _id: affiliateProductObjectId } = affiliateProduct;
      acc.set(affiliateProductObjectId.toString(), affiliateProduct);
      return acc;
    },
    new Map()
  );

  const { customConfig = [], learnerObjectId } = affiliate;

  const [entitiesCache, learner] = await Promise.all([
    commonService.retrieveEntitiesCache(affiliateProducts),
    commonService.retrieveActiveLearner(learnerObjectId),
  ]);

  const customConfigCache = customConfig.reduce((acc, config) => {
    acc.set(config.affiliateProductObjectId.toString(), config);
    return acc;
  }, new Map());

  const newCustomConfig = [];
  const updates = {};
  const arrayFilters = [];
  const commissionPercentageChanges = [];

  affiliateCommissionsToBeUpdated.forEach(
    ({ affiliateProductObjectId, commissionPercentage }, index) => {
      const existsAffiliateProductCustomConfig = customConfigCache.get(
        affiliateProductObjectId.toString()
      );
      const affiliateProduct = affiliateProductCache.get(
        affiliateProductObjectId.toString()
      );

      if (!affiliateProduct) {
        throw new ResourceNotFoundError('Affiliate product not found');
      }

      const key = `${affiliateProduct.entityType}-${affiliateProduct.entityObjectId}`;
      const entity = entitiesCache.get(key);

      const commissionPercentageChangesInfo = {
        entity,
        fromPercentage: null,
        toPercentage: null,
      };

      if (existsAffiliateProductCustomConfig) {
        updates[`customConfig.$[elem${index}].commissionPercentage`] =
          commissionPercentage;

        arrayFilters.push({
          [`elem${index}.affiliateProductObjectId`]: new ObjectId(
            affiliateProductObjectId
          ),
        });

        commissionPercentageChangesInfo.fromPercentage =
          existsAffiliateProductCustomConfig.commissionPercentage;
      } else {
        newCustomConfig.push({
          affiliateProductObjectId,
          commissionPercentage,
          entityType: affiliateProduct.entityType,
          entityObjectId: affiliateProduct.entityObjectId,
        });

        commissionPercentageChangesInfo.fromPercentage =
          affiliateProduct.config.commissionPercentage;
      }

      commissionPercentageChangesInfo.toPercentage = commissionPercentage;

      commissionPercentageChanges.push(commissionPercentageChangesInfo);
    }
  );

  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  try {
    let updatedAffiliate;

    if (arrayFilters.length > 0) {
      updatedAffiliate = await AffiliateModel.findOneAndUpdate(
        {
          _id: affiliateObjectId,
          communityObjectId,
          'customConfig.affiliateProductObjectId': {
            $in: affiliateProductObjectIds,
          },
        },
        {
          $set: updates,
        },
        {
          arrayFilters,
          session,
          new: true,
        }
      ).lean();
    }

    if (newCustomConfig.length > 0) {
      updatedAffiliate = await AffiliateModel.findOneAndUpdate(
        {
          _id: affiliateObjectId,
          communityObjectId,
        },
        {
          $push: {
            customConfig: {
              $each: newCustomConfig,
            },
          },
        },
        {
          new: true,
          session,
        }
      ).lean();
    }

    await session.commitTransaction();

    await affiliateMailService.formatAndSendAffiliateCommissionChangesEmail(
      { learner, community, commissionPercentageChanges }
    );

    return updatedAffiliate;
  } catch (err) {
    await session.abortTransaction();
    throw err;
  } finally {
    await session.endSession();
  }
};

exports.updateAffiliateStatusToRemoved = async ({
  affiliateObjectId,
  communityObjectId,
}) => {
  const affiliate = await AffiliateModel.findOneAndUpdate(
    {
      _id: affiliateObjectId,
      communityObjectId,
      status: AFFILIATE_STATUS.ACTIVE,
    },
    { status: AFFILIATE_STATUS.REMOVED },
    { new: true }
  ).lean();

  if (!affiliate) {
    throw new ResourceNotFoundError('Affiliate not found');
  }

  const [learner, community] = await Promise.all([
    commonService.retrieveActiveLearner(affiliate.learnerObjectId),
    commonService.retrieveActiveCommunity(communityObjectId),
  ]);

  await affiliateMailService.formatAndSendAffiliateRemovedEmail({
    learner,
    community,
  });

  return affiliate;
};

exports.leaveAffiliate = async ({
  affiliateProductObjectId,
  learnerObjectId,
}) => {
  const affiliateProduct = await AffiliateProductModel.findById(
    affiliateProductObjectId
  ).lean();

  if (!affiliateProduct) {
    throw new ResourceNotFoundError('Affiliate product not found');
  }

  const [community, affiliate] = await Promise.all([
    commonService.retrieveActiveCommunity(
      affiliateProduct.communityObjectId
    ),
    AffiliateModel.findOne({
      communityObjectId: affiliateProduct.communityObjectId,
      learnerObjectId,
      status: AFFILIATE_STATUS.ACTIVE,
    }).lean(),
  ]);

  if (!affiliate) {
    throw new ResourceNotFoundError('Affiliate not found');
  }

  await commonService.retrieveSubscription({
    learnerObjectId,
    communityCode: community.code,
  });

  const updatedAffiliate = await this.updateAffiliateStatusToRemoved({
    affiliateObjectId: affiliate._id,
    communityObjectId: affiliate.communityObjectId,
  });

  return updatedAffiliate;
};

exports.generateAffiliatesCsvStream = async ({
  community,
  search,
  status,
  pageSize,
}) => {
  const csvStream = new Readable({
    objectMode: true,
    read() {},
  });

  const matchFilter = await this.generateRetrieveAffiliatesMatchFilter({
    search,
    status,
    communityObjectId: community._id,
  });

  csvStream.push(
    `Email,Full Name,Status,Sales,Commission Earning Currency,Commission Earning Amount,Joined Date\n`
  );

  let pageNo = 1;
  let hasNextPage = true;

  const getNextPage = async () => {
    const affiliates = await AffiliateModel.find(matchFilter)
      .skip((pageNo - 1) * pageSize)
      .limit(pageSize)
      .lean();

    const learnerObjectIds = affiliates.map(
      (affiliate) => affiliate.learnerObjectId
    );

    const learnerCache = await commonService.retrieveLearnerCache(
      learnerObjectIds
    );

    affiliates.forEach((affiliate) => {
      const {
        learnerObjectId,
        status: affiliateStatus,
        sales,
        commissionEarnings,
        joinedDate,
      } = affiliate;

      const learner = learnerCache.get(learnerObjectId.toString());

      const { currency = community.baseCurrency, amount = 0 } =
        commissionEarnings.length > 0
          ? commissionEarnings[commissionEarnings.length - 1]
          : {};

      csvStream.push(
        `${learner.email},${
          learner.fullName
        },${affiliateStatus},${sales},${currency},${
          amount / 100
        },${joinedDate.toISOString()}\n`
      );
    });

    pageNo += 1;
    hasNextPage = affiliates.length === pageSize;

    if (!hasNextPage) {
      csvStream.push(null);
    }
  };

  csvStream._read = () => {
    if (hasNextPage) {
      getNextPage();
    } else {
      csvStream.push(null);
    }
  };

  return csvStream;
};

exports.disableAffiliates = async ({
  communityObjectId,
  session = undefined,
}) => {
  await AffiliateModel.updateMany(
    {
      communityObjectId,
      status: AFFILIATE_STATUS.ACTIVE,
    },
    { status: AFFILIATE_STATUS.REMOVED },
    { session }
  );
};
