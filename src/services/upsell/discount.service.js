const { ObjectId } = require('mongoose').Types;

const {
  DISCOUNT_CATEGORY,
  DISCOUNT_VALUE_TYPE,
  UPSELL_SOURCE_ENTITY_TYPE,
  PURCHASE_TYPE,
} = require('../../constants/common');
const DiscountModel = require('../../communitiesAPI/models/communityDiscounts.model');
const CommunityModel = require('../../communitiesAPI/models/community.model');
const commonService = require('./common.service');
const {
  ParamError,
  ResourceNotFoundError,
  InternalError,
} = require('../../utils/error.util');
const codeGenerationUtil = require('../../utils/codeGeneration.util');
const discountService = require('../../communitiesAPI/services/common/communityDiscounts.service');
const { PRODUCT_TYPE } = require('../product/constants');

async function generateDiscountCode(community, attempts = 5) {
  if (attempts <= 0) {
    throw new InternalError(
      'Unable to generate a unique discount code after multiple attempts'
    );
  }

  const discountCode = codeGenerationUtil.generateCode(6);

  const existsDiscount = await DiscountModel.findOne({
    code: discountCode,
    communityCode: community.code,
  })
    .read('primary')
    .lean();

  if (existsDiscount) {
    return generateDiscountCode(community, attempts - 1);
  }

  return discountCode;
}

exports.createDiscount = async ({
  discount,
  upsellSourceEntity,
  upsellEntity,
  upsellCommunity,
  upsellEntityType,
  session,
}) => {
  const { value, effectiveTimeEnd, timezone, isActive, expiredDuration } =
    discount;

  if (!upsellSourceEntity) {
    throw new ResourceNotFoundError('Upsell source entity not found');
  }

  const discountCode = await generateDiscountCode(upsellCommunity);

  let linkedEntityType = upsellEntityType;
  if (
    [PRODUCT_TYPE.DIGITAL_FILES, PRODUCT_TYPE.COURSE].includes(
      upsellEntityType
    )
  ) {
    // eslint-disable-next-line no-param-reassign
    linkedEntityType = PURCHASE_TYPE.FOLDER;
  }

  const linkedEntity = {
    type: linkedEntityType,
    entityObjectId: new ObjectId(upsellEntity.entityObjectId),
    title: upsellEntity.title,
    slug: upsellEntity.slug,
  };

  // User can join the challenge before it starts,
  // hence discount start time is the creation time
  const effectiveTimeStart =
    upsellSourceEntity.sourceEntityType ===
    UPSELL_SOURCE_ENTITY_TYPE.CHECKPOINT
      ? upsellSourceEntity.startTime
      : new Date();

  const createdDiscount = await discountService.createCommunityDiscount({
    type: DISCOUNT_VALUE_TYPE.PERCENTAGE,
    value,
    communityObjectId: upsellCommunity._id,
    code: discountCode,
    linkedEntities: [linkedEntity],
    bypassEntityUpdate: true,
    discountCategory: DISCOUNT_CATEGORY.UPSELL,
    effectiveTimeStart,
    effectiveTimeEnd,
    timezone,
    isActive,
    expiredDuration,
    session,
  });

  return createdDiscount.toObject();
};

exports.updateDiscount = async ({
  discount,
  upsellCommunity,
  session,
}) => {
  const {
    _id: discountObjectId,
    value,
    effectiveTimeEnd,
    isActive,
  } = discount;

  const updatedDiscount = await discountService.updateCommunityDiscount({
    discountObjectId,
    type: DISCOUNT_VALUE_TYPE.PERCENTAGE,
    value,
    communityObjectId: upsellCommunity._id,
    effectiveTimeEnd,
    isActive,
    session,
  });

  return updatedDiscount;
};

exports.retrieveDiscount = async (
  discountObjectId,
  projection = {
    value: 1,
    type: 1,
    discountCategory: 1,
    effectiveTimeEnd: 1,
    timezone: 1,
    linkedEntities: 1,
    isActive: 1,
    expiredDuration: 1,
  }
) => {
  const discount = await DiscountModel.findById(
    discountObjectId,
    projection
  ).lean();

  if (!discount) {
    throw new ResourceNotFoundError('Discount not found');
  }

  return discount;
};

exports.updateDiscountIsActive = async ({
  discountObjectId,
  isActive = true,
  session,
}) => {
  return DiscountModel.findByIdAndUpdate(
    discountObjectId,
    {
      isActive,
    },
    { session, new: true }
  ).lean();
};
