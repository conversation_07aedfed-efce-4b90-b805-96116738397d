const {
  PURCHASE_TYPE,
  UPSELL_SOURCE_ENTITY_TYPE,
} = require('../../constants/common');
const {
  communityFolderTypesMap,
  COMMUNITY_EVENT_ACCESS_TYPES,
  COMMUNITY_FOLDER_PURCHASE_TYPES,
} = require('../../communitiesAPI/constants');
const {
  ACCESS_TYPE,
  PROGRAM_TYPE,
  PROGRAM_ITEM_TYPE,
} = require('../program/constants');

const CommunityModel = require('../../communitiesAPI/models/community.model');
const FolderModel = require('../../communitiesAPI/models/communityFolders.model');
const EventModel = require('../../communitiesAPI/models/communityEvents.model');
const ProgramModel = require('../../models/program/program.model');
const ProgramItemModel = require('../../models/program/programItem.model');

const { ResourceNotFoundError } = require('../../utils/error.util');
const { PRODUCT_TYPE } = require('../product/constants');

exports.retrieveActiveCommunity = async (communityObjectId) => {
  const community = await CommunityModel.findOne(
    {
      _id: communityObjectId,
      isActive: true,
      isDemo: { $ne: true },
    },
    { code: 1, isPaidCommunity: 1 }
  ).lean();

  if (!community) {
    throw new ResourceNotFoundError('Community not found');
  }

  return community;
};

exports.retrieveUpsellEntity = async ({
  communityObjectId,
  entityType,
  entityObjectId,
}) => {
  let result;

  switch (entityType) {
    case PURCHASE_TYPE.SUBSCRIPTION:
      result = await CommunityModel.findOne(
        {
          _id: entityObjectId,
          isPaidCommunity: true,
        },
        {
          _id: 0,
          entityObjectId: '$_id',
          title: 1,
          slug: '$link',
          type: PURCHASE_TYPE.SUBSCRIPTION,
          _version: 0,
          isPaidCommunity: 1,
        }
      ).lean();
      break;
    case PURCHASE_TYPE.EVENT:
      result = await EventModel.findOne(
        {
          _id: entityObjectId,
          communities: communityObjectId,
          access: COMMUNITY_EVENT_ACCESS_TYPES.PAID,
        },
        {
          _id: 0,
          entityObjectId: '$_id',
          title: 1,
          slug: 1,
          type: PURCHASE_TYPE.EVENT,
          _version: 0,
          isSoldOut: 1,
          startTime: 1,
          endTime: 1,
        }
      ).lean();
      break;
    case PURCHASE_TYPE.FOLDER:
    case PURCHASE_TYPE.SESSION:
    case PRODUCT_TYPE.COURSE:
    case PRODUCT_TYPE.DIGITAL_FILES:
      result = await FolderModel.findOne(
        {
          _id: entityObjectId,
          communityObjectId,
          access: COMMUNITY_FOLDER_PURCHASE_TYPES.PAID,
        },
        {
          _id: 0,
          entityObjectId: '$_id',
          title: 1,
          slug: '$resourceSlug',
          type: {
            $cond: {
              if: { $eq: ['$type', communityFolderTypesMap.SESSION] },
              then: PURCHASE_TYPE.SESSION,
              else: PURCHASE_TYPE.FOLDER,
            },
          },
          status: 1,
          isSoldOut: 1,
          stopAcceptingBookings: 1,
        }
      ).lean();
      break;
    case PURCHASE_TYPE.CHALLENGE:
      result = await ProgramModel.findOne(
        {
          _id: entityObjectId,
          communityObjectId,
          access: ACCESS_TYPE.PAID,
        },
        {
          _id: 0,
          entityObjectId: '$_id',
          title: 1,
          slug: 1,
          type: {
            $cond: {
              if: { $eq: ['$type', PROGRAM_TYPE.CHALLENGE] },
              then: PURCHASE_TYPE.CHALLENGE,
              else: 'COURSE', // Does not exists yet
            },
          },
          access: 1,
          _version: 0,
          status: 1,
          startTime: 1,
          endTime: 1,
          canJoinAfterStart: 1,
          isRegistrationClosed: 1,
        }
      ).lean();
      break;
    default:
      throw new Error(`${entityType} not supported`);
  }

  return result;
};

exports.retrieveUpsellSourceEntity = async ({
  communityObjectId,
  entityType,
  entityObjectId,
  databaseSession = undefined,
}) => {
  let result;

  switch (entityType) {
    case UPSELL_SOURCE_ENTITY_TYPE.EVENT:
      result = await EventModel.findOne({
        _id: entityObjectId,
        communities: communityObjectId,
      }).lean();
      break;
    case UPSELL_SOURCE_ENTITY_TYPE.FOLDER:
    case UPSELL_SOURCE_ENTITY_TYPE.SESSION:
      result = await FolderModel.findOne({
        _id: entityObjectId,
        communityObjectId,
      }).lean();
      break;
    case UPSELL_SOURCE_ENTITY_TYPE.CHALLENGE:
      result = await ProgramModel.findOne({
        _id: entityObjectId,
        communityObjectId,
      }).lean();
      break;
    case UPSELL_SOURCE_ENTITY_TYPE.CHECKPOINT:
      if (databaseSession) {
        result = await ProgramItemModel.findOne({
          _id: entityObjectId,
          type: PROGRAM_ITEM_TYPE.CHECKPOINT,
        })
          .session(databaseSession)
          .read('primary')
          .lean();
      } else {
        result = await ProgramItemModel.findOne({
          _id: entityObjectId,
          type: PROGRAM_ITEM_TYPE.CHECKPOINT,
        }).lean();
      }
      break;
    default:
      throw new Error(`${entityType} not supported`);
  }

  return result;
};
