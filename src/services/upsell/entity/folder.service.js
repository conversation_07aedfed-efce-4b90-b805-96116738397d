const { PRICE_TYPE, PURCHASE_TYPE } = require('../../../constants/common');
const {
  COMMUNITY_FOLDER_PURCHASE_TYPES,
  COMMUNITY_FOLDER_STATUS,
  communityFolderTypesMap,
} = require('../../../communitiesAPI/constants');
const FolderModel = require('../../../communitiesAPI/models/communityFolders.model');
const FolderPurchaseModel = require('../../../communitiesAPI/models/communityFolderPurchases.model');
const priceService = require('./common/price.service');
const EntityInfo = require('./common/EntityInfo');

exports.retrieveUpsells = async ({
  upsellCache,
  ip,
  learnerObjectId,
  communityCache,
  bypassCheck = false,
}) => {
  const folderObjectIds = [...upsellCache.keys()];

  const folderPurchases = await FolderPurchaseModel.find(
    {
      folderObjectId: { $in: folderObjectIds },
      learnerObjectId,
    },
    { folderObjectId: 1 }
  ).lean();

  const folderPurchaseCache = folderPurchases.reduce(
    (acc, folderPurchase) => {
      acc.set(folderPurchase.folderObjectId.toString(), folderPurchase);
      return acc;
    },
    new Map()
  );

  const filteredFolderObjectIds = folderObjectIds.filter(
    (folderObjectId) =>
      bypassCheck || !folderPurchaseCache.get(folderObjectId.toString())
  );

  const additionalMatchFilter = {
    communityObjectId: { $in: [...communityCache.keys()] },
    access: COMMUNITY_FOLDER_PURCHASE_TYPES.PAID,
    status: COMMUNITY_FOLDER_STATUS.PUBLISHED,
    isSoldOut: { $ne: true },
    type: {
      $in: [
        communityFolderTypesMap.DIGITAL_PRODUCT,
        communityFolderTypesMap.COURSE,
      ],
    },
  };

  const folders = await FolderModel.find({
    _id: { $in: filteredFolderObjectIds },
    ...(!bypassCheck && additionalMatchFilter),
  }).lean();

  const upsellsWithAdditionalInfo = await Promise.all(
    folders.map(async (folder) => {
      const upsell = upsellCache.get(folder._id.toString());
      const discount = upsell.discount;
      const community = communityCache.get(
        folder.communityObjectId.toString()
      );

      const priceDetails = await priceService.retrievePriceDetails({
        addon: folder,
        ip,
        communityObjectId: folder.communityObjectId,
        discount,
      });

      const entityInfo = new EntityInfo({
        priceDetails,
        entityObjectId: folder._id,
        title: folder.title,
        slug: folder.resourceSlug,
        entityType: PURCHASE_TYPE.FOLDER,
        thumbnail: folder.thumbnail,
        priceType: folder.pricingConfig?.priceType ?? PRICE_TYPE.FIXED,
        communityObjectId: community._id,
        communityCode: community.code,
        communityLink: community.link,
        communityConfig: community.config,
      });

      upsell.entityInfo = entityInfo;

      return upsell;
    })
  );

  return upsellsWithAdditionalInfo;
};
