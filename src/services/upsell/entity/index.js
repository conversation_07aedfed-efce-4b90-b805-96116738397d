/* eslint-disable no-param-reassign */
const {
  PURCHASE_TYPE,
  PAYABLE_PURCHASE_TYPES,
  UPSELL_SOURCE_ENTITY_TYPE,
} = require('../../../constants/common');
const { ParamError } = require('../../../utils/error.util');
const subscriptionService = require('./subscription.service');
const eventService = require('./event.service');
const folderService = require('./folder.service');
const sessionService = require('./session.service');
const challengeService = require('./challenge.service');
const checkpointService = require('./checkpoint.service');
const discountService = require('./common/discount.service');
const LearnerCacheService = require('./common/LearnerCacheService');
const communityService = require('./community.service');
const { PRODUCT_TYPE } = require('../../product/constants');

async function retrieveSourceUpsells({
  entityType,
  entityObjectId,
  learnerObjectId,
  specificItemObjectId = null,
  withChecks,
}) {
  switch (entityType) {
    case PURCHASE_TYPE.CHALLENGE:
      return challengeService.retrieveSourceUpsells({
        entityObjectId,
        learnerObjectId,
        specificItemObjectId,
        withChecks,
      });
    default:
      throw new ParamError(`${entityType} not supported`);
  }
}

function assignDiscountToUpsell(upsell, discountCache) {
  if (upsell.discountObjectId && discountCache) {
    upsell.discount = discountCache.get(
      upsell.discountObjectId.toString()
    );
  }
}

function compareDiscounts(upsell1, upsell2) {
  if (!upsell1.discount && !upsell2.discount) return 0;
  if (!upsell1.discount) return -1;
  if (!upsell2.discount) return 1;

  if (upsell1.discount.value !== upsell2.discount.value) {
    return upsell1.discount.value > upsell2.discount.value ? 1 : -1;
  }

  return upsell1.discount.effectiveTimeEnd >
    upsell2.discount.effectiveTimeEnd
    ? 1
    : -1;
}

function getBetterUpsell(upsell1, upsell2) {
  return compareDiscounts(upsell1, upsell2) >= 0 ? upsell1 : upsell2;
}

function groupByEntityTypeAndEntityObjectId(cache, upsell, discountCache) {
  assignDiscountToUpsell(upsell, discountCache);

  const { upsellEntityType, upsellEntityObjectId } = upsell;

  const key = `${upsellEntityType}-${upsellEntityObjectId}`;
  const existingUpsell = cache.get(key);

  cache.set(
    key,
    existingUpsell ? getBetterUpsell(upsell, existingUpsell) : upsell
  );

  return cache;
}

async function retrieveUpsellEntitiesInfo({
  upsells,
  upsellEntityType,
  learnerObjectId,
  ip,
  currentDate,
  learnerCacheService,
  bypassCheck,
}) {
  if (!upsells) {
    return null;
  }

  const communities = await communityService.retrieveActiveCommunities(
    upsells,
    bypassCheck
  );

  const communityCache = communities.reduce((acc, community) => {
    delete community.config.customFeeConfigs;
    acc.set(community._id.toString(), community);
    return acc;
  }, new Map());

  const upsellCache = upsells.reduce((acc, upsell) => {
    acc.set(upsell.upsellEntityObjectId.toString(), upsell);
    return acc;
  }, new Map());

  switch (upsellEntityType) {
    case PURCHASE_TYPE.SUBSCRIPTION:
      return subscriptionService.retrieveUpsells({
        learnerObjectId,
        upsellCache,
        learnerCacheService,
        communities,
        ip,
        bypassCheck,
      });
    case PURCHASE_TYPE.EVENT:
      return eventService.retrieveUpsells({
        learnerObjectId,
        upsellCache,
        ip,
        currentDate,
        communityCache,
        bypassCheck,
      });
    case PURCHASE_TYPE.FOLDER:
    case PRODUCT_TYPE.COURSE:
    case PRODUCT_TYPE.DIGITAL_FILES:
      return folderService.retrieveUpsells({
        learnerObjectId,
        upsellCache,
        communityCache,
        ip,
        bypassCheck,
      });
    case PURCHASE_TYPE.SESSION:
      return sessionService.retrieveUpsells({
        learnerObjectId,
        upsellCache,
        learnerCacheService,
        communityCache,
        ip,
        bypassCheck,
      });
    case PURCHASE_TYPE.CHALLENGE:
      return challengeService.retrieveUpsells({
        learnerObjectId,
        upsellCache,
        learnerCacheService,
        communityCache,
        ip,
        currentDate,
        bypassCheck,
      });
    default:
      throw new ParamError(`${upsellEntityType} not supported`);
  }
}

async function retrieveUpsellEntities({
  upsellCacheBySameProduct,
  learnerObjectId,
  ip,
  currentDate,
  bypassCheck,
}) {
  const upsellCacheByEntityType = new Map();

  for (const key of upsellCacheBySameProduct.keys()) {
    const [entityType] = key.split('-');

    const upsell = upsellCacheBySameProduct.get(key);

    if (!upsellCacheByEntityType.has(entityType)) {
      upsellCacheByEntityType.set(entityType, []);
    }

    upsellCacheByEntityType.get(entityType).push(upsell);
  }

  const learnerCacheService = new LearnerCacheService();

  const upsellEntities = await Promise.all(
    PAYABLE_PURCHASE_TYPES.map(async (entityType) => {
      const upsells = upsellCacheByEntityType.get(entityType);

      const upsellEntitiesInfo = await retrieveUpsellEntitiesInfo({
        upsells,
        upsellEntityType: entityType,
        learnerObjectId,
        ip,
        currentDate,
        learnerCacheService,
        bypassCheck,
      });

      return upsellEntitiesInfo;
    })
  );

  return upsellEntities
    .filter((upsellEntity) => upsellEntity != null)
    .flat();
}

exports.retrieveUpsellEntity = async ({
  upsell,
  learnerObjectId,
  ip,
  currentDate,
  bypassCheck,
}) => {
  const learnerCacheService = new LearnerCacheService();

  const upsellEntitiesInfo = await retrieveUpsellEntitiesInfo({
    upsells: [upsell],
    upsellEntityType: upsell.upsellEntityType,
    learnerObjectId,
    ip,
    currentDate,
    learnerCacheService,
    bypassCheck,
  });

  return upsellEntitiesInfo?.[0];
};

exports.retrieveSourceEntity = async (upsell) => {
  const { sourceEntityType } = upsell;

  switch (sourceEntityType) {
    case UPSELL_SOURCE_ENTITY_TYPE.CHALLENGE:
      return challengeService.retrieveSourceUpsell(upsell);
    case UPSELL_SOURCE_ENTITY_TYPE.CHECKPOINT:
      return checkpointService.retrieveSourceUpsell(upsell);
    default:
      throw new ParamError(`${sourceEntityType} not supported`);
  }
};

exports.retrieveUpsellsEntityInfo = async (upsells) => {
  const upsellCacheBySameProduct = upsells.reduce(
    (acc, upsell) => groupByEntityTypeAndEntityObjectId(acc, upsell, null),
    new Map()
  );

  const upsellsEntityInfo = await retrieveUpsellEntities({
    upsellCacheBySameProduct,
    ip: null,
    learnerObjectId: null,
    currentDate: new Date(),
    bypassCheck: true,
  });

  // Assign entity info back to upsells
  upsells.forEach((upsell) => {
    const key = `${upsell.upsellEntityType}-${upsell.upsellEntityObjectId}`;
    const upsellWithEntityInfo = upsellCacheBySameProduct.get(key);
    upsell.entityInfo = upsellWithEntityInfo.entityInfo;
  });

  return upsellsEntityInfo;
};

exports.retrieveAvailableUpsells = async ({
  entityType,
  entityObjectId,
  learnerObjectId,
  ip,
  specificItemObjectId = null,
  withChecks = true,
}) => {
  const upsells = await retrieveSourceUpsells({
    entityType,
    entityObjectId,
    learnerObjectId,
    specificItemObjectId,
    withChecks,
  });

  if (upsells.length === 0) {
    return [];
  }

  const currentDate = new Date();

  const discountCache = await discountService.retrieveDiscountCache(
    upsells,
    currentDate
  );

  const upsellCacheBySameProduct = upsells.reduce(
    (acc, upsell) =>
      groupByEntityTypeAndEntityObjectId(acc, upsell, discountCache),
    new Map()
  );

  const upsellsEntityInfo = await retrieveUpsellEntities({
    upsellCacheBySameProduct,
    ip,
    learnerObjectId,
    currentDate,
    bypassCheck: false,
  });

  return upsellsEntityInfo;
};
