const {
  PURCHASE_TYPE,
  UPSELL_SOURCE_ENTITY_TYPE,
} = require('../../../constants/common');
const {
  ResourceNotFoundError,
  ParamError,
  ToUserError,
} = require('../../../utils/error.util');
const { GENERIC_ERROR } = require('../../../constants/errorCode');
const subscriptionService = require('./subscription.service');
const eventService = require('./event.service');
const folderService = require('./folder.service');
const sessionService = require('./session.service');
const challengeService = require('./challenge.service');
const { PRODUCT_TYPE } = require('../../product/constants');

exports.validateUpsellSourceEntity = (
  upsellSourceEntity,
  upsellSourceEntityType
) => {
  const allowedEntityType = [
    UPSELL_SOURCE_ENTITY_TYPE.CHALLENGE,
    UPSELL_SOURCE_ENTITY_TYPE.CHECKPOINT,
  ];

  if (!allowedEntityType.includes(upsellSourceEntityType)) {
    throw new ToUserError(
      'Upsell not available',
      GENERIC_ERROR.UPSELL_GENERIC_ERROR
    );
  }
};

exports.validateUpsellEntity = ({
  upsellEntity,
  upsellEntityType,
  upsellCommunity,
}) => {
  if (!upsellEntity) {
    throw new ResourceNotFoundError('Upsell entity not found');
  }

  switch (upsellEntityType) {
    case PURCHASE_TYPE.SUBSCRIPTION:
      subscriptionService.validate(upsellEntity, upsellCommunity);
      break;
    case PURCHASE_TYPE.EVENT:
      eventService.validate(upsellEntity);
      break;
    case PURCHASE_TYPE.FOLDER:
    case PRODUCT_TYPE.COURSE:
    case PRODUCT_TYPE.DIGITAL_FILES:
      folderService.validate(upsellEntity);
      break;
    case PURCHASE_TYPE.SESSION:
      sessionService.validate(upsellEntity);
      break;
    case PURCHASE_TYPE.CHALLENGE:
      challengeService.validate(upsellEntity);
      break;
    default:
      throw new ParamError(`${upsellEntityType} not supported`);
  }
};
