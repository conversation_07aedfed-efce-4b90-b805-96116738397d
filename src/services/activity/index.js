const { DateTime } = require('luxon');
const ActivityModel = require('../../models/activities.model');
const CommunityRoleModel = require('../../communitiesAPI/models/communityRole.model');
const UserModel = require('../../models/users.model');
const MetadataCacheService = require('./metadataCache.service');
const LearnerService = require('./learner.service');
const EventService = require('./event.service');
const FolderService = require('./folder.service');
const ChallengeService = require('./challenge.service');
const MembershipService = require('./membership.service');
const VariableGenerationService = require('./variableGeneration.service');
const {
  ACTIVITY_TYPES,
  SUBSCRIPTION_ACTIVITY_TYPES,
  ADDON_ACTIVITY_TYPES,
} = require('../../constants/common');
const logger = require('../logger.service');
const { aclRoles } = require('../../communitiesAPI/constants');

function retrieveActivityInfo(activity) {
  const activityInfo = {
    activityObjectId: activity._id,
    key: activity.activityType,
    activityCreatedAt: activity.activityCreatedAt,
    image: activity.profileImage,
  };

  if (SUBSCRIPTION_ACTIVITY_TYPES.includes(activity.activityType)) {
    activityInfo.value =
      VariableGenerationService.generateUserActivityInfo(activity);
  } else if (ADDON_ACTIVITY_TYPES.includes(activity.activityType)) {
    activityInfo.value =
      VariableGenerationService.generateEntityActivityInfo(activity);
  }

  return activityInfo;
}

exports.retrieveLastNDays = async ({
  noOfDays = 30,
  communityObjectId,
  excludeManagers = false,
}) => {
  const currentUtcDateTIme = DateTime.utc();
  const startUtcDateTime = currentUtcDateTIme.minus({ days: noOfDays });

  const activities = await ActivityModel.find(
    {
      activityType: {
        $in: [
          ACTIVITY_TYPES.SUBSCRIPTION_JOINED,
          ACTIVITY_TYPES.SUBSCRIPTION_UNSUBSCRIBED,
        ],
      },
      communityObjectId,
      activityCreatedAt: {
        $gte: startUtcDateTime,
      },
    },
    {
      appliedToLearnerObjectId: 1,
      value: {
        $cond: {
          if: {
            $eq: ['$activityType', ACTIVITY_TYPES.SUBSCRIPTION_JOINED],
          },
          then: 1,
          else: -1,
        },
      },
    }
  ).lean();

  logger.info(
    `retrieveLastNDays: activities: ${JSON.stringify(activities)}`
  );

  if (excludeManagers) {
    const managerUserObjectIds = await CommunityRoleModel.find({
      communityObjectId,
      role: aclRoles.MANAGER,
    }).distinct('userObjectId');
    const managerlearnerObjectIdStrs =
      (
        await UserModel.find({
          _id: { $in: managerUserObjectIds },
        }).distinct('learner')
      ).map((id) => id.toString()) ?? [];

    const filteredActivities = activities.filter((activity) =>
      managerlearnerObjectIdStrs.includes(
        activity.appliedToLearnerObjectId.toString()
      )
    );
    return filteredActivities.reduce(
      (acc, activity) => acc + activity.value,
      0
    );
  }
  return activities.reduce((acc, activity) => acc + activity.value, 0);
};

exports.retrieveActivities = async ({
  communityId,
  activityTypes,
  lastActivityObjectId,
  limit = 20,
}) => {
  const filterQuery = {
    communityObjectId: communityId,
    // Add this in temporarily to ignore zero link cpmmunity activities
    activityType: { $in: Object.values(ACTIVITY_TYPES) },
  };

  const parsedActivityTypes = activityTypes?.split(',') ?? [];

  if (parsedActivityTypes.length > 0 && parsedActivityTypes[0] !== '') {
    filterQuery.activityType = {
      $in: parsedActivityTypes,
    };
  }

  if (lastActivityObjectId) {
    const lastActivity = await ActivityModel.findById(
      lastActivityObjectId,
      { activityCreatedAt: 1 }
    ).lean();

    if (lastActivity) {
      filterQuery.activityCreatedAt = {
        $lt: lastActivity.activityCreatedAt,
      };
    }
  }

  const activities = await ActivityModel.find(filterQuery)
    .sort({ activityCreatedAt: -1 })
    .limit(limit)
    .lean();

  const metadataCacheService = new MetadataCacheService(
    LearnerService,
    EventService,
    FolderService,
    ChallengeService,
    MembershipService
  );

  await metadataCacheService.init(activities);

  const activitiesInfo = activities.map((activity) => {
    const activityWithMetadata =
      metadataCacheService.retrieveMetadata(activity);

    const activityInfo = retrieveActivityInfo(activityWithMetadata);

    return activityInfo;
  });

  return activitiesInfo;
};
