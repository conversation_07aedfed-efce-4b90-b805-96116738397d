const logger = require('../../logger.service');
const PaymentBackendRpc = require('../../../rpc/paymentBackend');
const PayloadInformationService = require('../signup/payloadInformation');
const ValidationService = require('./validation');
const {
  PAYMENT_PROVIDER,
  PURCHASE_TYPE,
} = require('../../../constants/common');
const { ParamError } = require('../../../utils/error.util');

exports.discount = async ({
  ip,
  communityCode,
  upsellIdentityCode,
  items,
  learnerObjectId,
  memberInfo,
  paymentProvider,
}) => {
  logger.info(
    `getDiscount: ${JSON.stringify({
      ip,
      communityCode,
      upsellIdentityCode,
      items,
      learnerObjectId,
    })}`
  );

  const paymentBackendRpc = new PaymentBackendRpc();
  await paymentBackendRpc.init();

  const payloadInformation =
    await PayloadInformationService.retrievePayloadInformationForDiscount({
      communityCode,
      items,
      ip,
      upsellIdentityCode,
      paymentBackendRpc,
      memberInfo,
      paymentProvider,
      learnerObjectId,
    });

  await ValidationService.validatePayloadForDiscount(payloadInformation);

  const itemArray = payloadInformation.items;

  const { metadata } = itemArray[0];

  const { discount, priceDetails, priceDetail } = metadata;

  const {
    _id,
    code,
    value,
    type,
    effectiveTimeEnd,
    effectiveTimeStart,
    intervalCount,
    trialDays,
    linkedEntities,
    discountCategory,
  } = discount;

  return {
    priceDetails: priceDetails ?? priceDetail ?? {},
    _id,
    code,
    value,
    type,
    effectiveTimeEnd,
    effectiveTimeStart,
    intervalCount,
    trialDays,
    linkedEntities,
    discountCategory,
  };
};
