const { DateTime } = require('luxon');
const mongoose = require('mongoose');
const { PAYMENT_PROVIDER } = require('../../../../constants/common');
const PaymentBackendRpc = require('../../../../rpc/paymentBackend');
const PaymentProviderService = require('../paymentProvider');
const { TransactionLockService } = require('../common');
const {
  ParamError,
  InternalError,
} = require('../../../../utils/error.util');
const {
  TIER_CHANGE_CATEGORY,
  ORDER_STATUS,
  ORDER_PAYMENT_DETAIL_STATUSES,
} = require('../../../plan/constants');
const PlanOrderModel = require('../../../../models/plan/communityPlanOrder.model');
const communityPlanOrderModel = require('../../../../models/plan/communityPlanOrder.model');
const notificationService = require('../../../communityNotification');

exports.handlePlan = async ({
  metadata,
  email,
  customerId,
  planOrder,
  planOrderObjectId,
  paymentProvider,
  paymentMethodId,
  paymentMetadata,
}) => {
  if (!planOrderObjectId && !planOrder) {
    throw new ParamError(
      'Plan order object id does not exists in signup token'
    );
  }

  if (!planOrder) {
    // eslint-disable-next-line no-param-reassign
    planOrder =
      await TransactionLockService.retrieveAndManagePlanOrderLock(
        planOrderObjectId,
        true
      );
  }

  let result;

  if (paymentProvider === PAYMENT_PROVIDER.STRIPE) {
    result =
      await PaymentProviderService.StripeService.createSubscriptionForPlan(
        {
          planOrder,
          paymentMethodId,
          customerId,
          paymentMetadata,
          paymentProvider: PAYMENT_PROVIDER.STRIPE,
        }
      );
  } else if (paymentProvider === PAYMENT_PROVIDER.STRIPE_US) {
    result =
      await PaymentProviderService.StripeService.createSubscriptionForPlan(
        {
          planOrder,
          paymentMethodId,
          customerId,
          paymentMetadata,
          paymentProvider: PAYMENT_PROVIDER.STRIPE_US,
        }
      );
  } else if (paymentProvider === PAYMENT_PROVIDER.STRIPE_INDIA) {
    result =
      await PaymentProviderService.StripeIndiaService.createSubscriptionForPlan(
        {
          planOrder,
          paymentMethodId,
          customerId,
          paymentMetadata,
        }
      );
  } else if (paymentProvider === PAYMENT_PROVIDER.EBANX) {
    result = await PaymentProviderService.EbanxService.directCharge({
      transaction: planOrder,
      email,
      paymentMethodId,
      paymentMetadata,
      metadata,
    });
  } else {
    throw new ParamError(
      `Payment provider ${paymentProvider} not supported for subscription`
    );
  }

  return result;
};

async function handlePlanTierUpgrade({
  metadata,
  email,
  customerId,
  planOrder,
  paymentProvider,
  paymentMethodId,
  paymentMetadata,
}) {
  const { paymentProviderPriceId } = planOrder;

  let result;
  let trialPeriodDays;
  // Original Amount = 99
  // Currently paidAmount = 0
  // Next Billing paidAmount = 0 --> No need trialdays
  // Next Billing paidAmount = 40  or 99 (full/half payment)
  // if (
  //   previousPlanOrder &&
  //   creditDetails?.paidAmount === 0 &&
  //   creditDetails?.nextBillingCreditBreakdown?.paidAmount !== 0
  // ) {
  //   const nextBillingDate = DateTime.now().plus({
  //     [`${interval}s`]: intervalCount,
  //   });
  //   trialPeriodDays = Math.round(
  //     nextBillingDate.diffNow('days').toObject().days
  //   );
  // }
  if (
    paymentProvider === PAYMENT_PROVIDER.STRIPE ||
    paymentProvider === PAYMENT_PROVIDER.STRIPE_US
  ) {
    result =
      await PaymentProviderService.StripeService.createSubscriptionForTierChange(
        {
          paymentProviderPriceId,
          trialPeriodDays,
          paymentMethodId,
          customerId,
          paymentMetadata,
          paymentProvider,
        }
      );
  } else if (paymentProvider === PAYMENT_PROVIDER.STRIPE_INDIA) {
    result =
      await PaymentProviderService.StripeIndiaService.createSubscriptionForTierChange(
        {
          paymentProviderPriceId,
          trialPeriodDays,
          paymentMethodId,
          customerId,
          paymentMetadata,
        }
      );
  } else if (paymentProvider === PAYMENT_PROVIDER.EBANX) {
    result = await PaymentProviderService.EbanxService.directCharge({
      transaction: planOrder,
      email,
      paymentMethodId,
      paymentMetadata,
      metadata,
    });
  } else {
    throw new ParamError(
      `Payment provider ${paymentProvider} not supported for subscription`
    );
  }

  return result;
}

async function handlePreviousPlanOrderCancellation({
  planOrder,
  previousPlanOrder,
  session,
}) {
  const previousPlanOrderUpdateQuery = {
    status: ORDER_STATUS.CANCELLED,
    cancelledAt: previousPlanOrder.nextBillingDate,
    nextPlanOrder: {
      planOrderObjectId: planOrder._id,
      entityType: planOrder.entityType,
      interval: planOrder.interval,
      intervalCount: planOrder.intervalCount,
      startDate: previousPlanOrder.nextBillingDate,
    },
  };

  const paymentBackendRpc = new PaymentBackendRpc();
  await paymentBackendRpc.init();
  const cancellationReason = 'Tier Change';
  const paymentProvider = previousPlanOrder.paymentDetails.paymentProvider;

  const { paymentProviderSubscriptionId } = previousPlanOrder;

  switch (paymentProvider) {
    case PAYMENT_PROVIDER.STRIPE:
    case PAYMENT_PROVIDER.STRIPE_US:
    case PAYMENT_PROVIDER.STRIPE_INDIA:
      await paymentBackendRpc.cancelStripeSubscription(
        paymentProviderSubscriptionId,
        cancellationReason,
        paymentProvider,
        false,
        true
      );
      break;
    case PAYMENT_PROVIDER.EBANX:
      previousPlanOrderUpdateQuery.scheduledCancellation = true;
      break;
    default:
      throw new ParamError(
        `Payment provider ${paymentProvider} not supported`
      );
  }
  const updatedPlanOrder = await PlanOrderModel.findOneAndUpdate(
    { _id: previousPlanOrder._id, status: ORDER_STATUS.CURRENT },
    previousPlanOrderUpdateQuery,
    {
      new: true,
      session,
    }
  ).lean();

  // Send lark alert for current tier cancellation
  await notificationService.sendCancelledLarkNotification({
    planOrder: updatedPlanOrder,
  });
}

async function handlePlanTierDowngrade({
  metadata,
  email,
  customerId,
  planOrder,
  paymentProvider,
  paymentMethodId,
  paymentMetadata,
}) {
  const { paymentProviderPriceId } = planOrder;

  const previousPlanOrder = await PlanOrderModel.findById(
    planOrder.previousPlanOrder.planOrderObjectId
  ).lean();
  if (!previousPlanOrder) {
    throw new ParamError(`Previous plan order must exists`);
  }

  let result;
  const nextBillingDate = DateTime.fromJSDate(
    previousPlanOrder.nextBillingDate
  );
  const trialPeriodDays = Math.round(
    nextBillingDate.diffNow('days').toObject().days
  );

  if (
    paymentProvider === PAYMENT_PROVIDER.STRIPE ||
    paymentProvider === PAYMENT_PROVIDER.STRIPE_US
  ) {
    result =
      await PaymentProviderService.StripeService.createSubscriptionForTierChange(
        {
          paymentProviderPriceId,
          trialPeriodDays,
          paymentMethodId,
          customerId,
          paymentMetadata,
          paymentProvider,
        }
      );
  } else if (paymentProvider === PAYMENT_PROVIDER.STRIPE_INDIA) {
    result =
      await PaymentProviderService.StripeIndiaService.createSubscriptionForTierChange(
        {
          paymentProviderPriceId,
          trialPeriodDays,
          paymentMethodId,
          customerId,
          paymentMetadata,
        }
      );
  } else if (paymentProvider === PAYMENT_PROVIDER.EBANX) {
    // Ebanx do not need to handle zero payment flow
    const ebanxResult =
      await PaymentProviderService.EbanxService.directCharge({
        transaction: planOrder,
        email,
        paymentMethodId,
        paymentMetadata,
        metadata,
      });

    result = {
      paymentProviderSubscriptionId: ebanxResult.payment.hash,
      planNextBillingDate: previousPlanOrder.nextBillingDate,
    };
  } else {
    throw new ParamError(
      `Payment provider ${paymentProvider} not supported for subscription`
    );
  }

  const successTime = new Date();
  const session = await mongoose.startSession();
  session.startTransaction();
  try {
    // Downgrade do not have the concept of credit on the firstBilling.
    // So planOrder wont have prorationDetails and creditHistory
    // However, we have to update plan order to cater for future credit in renewal payments
    const planStartDate = previousPlanOrder.nextBillingDate;
    const planOrderUpdateQuery = {
      status: ORDER_STATUS.PENDING,
      nextBillingDate: result.planNextBillingDate,
      billingCycle: 1,
      paymentProviderSubscriptionId: result.paymentProviderSubscriptionId,

      // Payment Details
      'paymentDetails.status': ORDER_PAYMENT_DETAIL_STATUSES.SUCCESS,
      'paymentDetails.completedPayment': true,
      'paymentDetails.paymentProvider': paymentProvider,

      // previousPlanOrder (should not change)
      'previousPlanOrder.endDate': planStartDate,

      // FirstBilling Metadata (should not change)
      'metadata.firstBilling.paymentSuccessTime': successTime,
      'metadata.firstBilling.nextBillingDate': result.planNextBillingDate, // TO check
    };

    const planOrderUnsetQuery = {
      'paymentDetails.attemptCount': 1,
      'paymentDetails.nextChargeRetryDate': 1,
      previousAmountInLocalCurrency: 1,
      previousLocalCurrency: 1,
    };

    await communityPlanOrderModel.findByIdAndUpdate(
      planOrder._id,
      {
        $set: planOrderUpdateQuery,
        $unset: planOrderUnsetQuery,
      },
      { session }
    );
    // Immediate previousPlanOrder remains active until it reaches billing date
    await handlePreviousPlanOrderCancellation({
      planOrder,
      previousPlanOrder,
      latestUpdateTime: successTime,
      session,
    });
    await session.commitTransaction();
  } catch (err) {
    await session.abortTransaction();
    throw new Error(`Transaction failed: ${err.message}`);
  } finally {
    await session.endSession();
  }

  return result;
}
exports.handlePlanTierChange = async ({
  requirePayment,
  metadata,
  email,
  customerId,
  planOrderObjectId,
  paymentProvider,
  paymentMethodId,
  paymentMetadata,
}) => {
  if (!planOrderObjectId) {
    throw new ParamError(
      'Plan order object id does not exists in signup token'
    );
  }
  const planOrder =
    await TransactionLockService.retrieveAndManagePlanOrderLock(
      planOrderObjectId,
      true
    );

  if (!planOrder.previousPlanOrder) {
    throw new ParamError(
      'Previous plan order is missing from the planOrder'
    );
  }
  if (planOrder.tierChangeCategory === TIER_CHANGE_CATEGORY.UPGRADE) {
    await handlePlanTierUpgrade({
      requirePayment,
      metadata,
      email,
      customerId,
      planOrder,
      paymentProvider,
      paymentMethodId,
      paymentMetadata,
    });
  } else if (
    planOrder.tierChangeCategory === TIER_CHANGE_CATEGORY.DOWNGRADE
  ) {
    // Goes through normal payment flow for first Billing
    await handlePlanTierDowngrade({
      requirePayment,
      metadata,
      email,
      customerId,
      planOrder,
      paymentProvider,
      paymentMethodId,
      paymentMetadata,
    });
  } else {
    throw new ParamError('Invalid tier change category');
  }
};
