const httpContext = require('express-http-context');
const { v4: uuidV4 } = require('uuid');
const ObjectId = require('mongoose').Types.ObjectId;
const PaymentBackendRpc = require('../../../../rpc/paymentBackend');
const {
  ParamError,
  ToUserError,
} = require('../../../../utils/error.util');
const CountryCurrencyMappingModel = require('../../../../models/countryInfoMapping.model');
const UserPaymentTokenModel = require('../../../../models/userPaymentToken.model');
const CommunityPurchaseTransactionModel = require('../../../../communitiesAPI/models/communityPurchaseTransactions.model');
const AddonTransactionModel = require('../../../../communitiesAPI/models/communityAddonTransactions.model');
const PlanOrderModel = require('../../../../models/plan/communityPlanOrder.model');
const {
  INTERNAL_EBANX_NOTIFICATION_TYPE,
  EBANX_SUPPORTED_CURRENCY,
  PURCHASE_TYPE,
  PAYMENT_PROVIDER,
  EBANX_RECURRING_PAYMENT_TYPE,
  EBANX_PAYMENT_TYPE,
  USER_PAYMENT_TOKEN_STATUS,
  COMMUNITY_SUBSCRIPTION_STATUSES,
  SUBSCRIPTION_ACTION_TYPE,
  ADDON_PURCHASE_TYPES,
} = require('../../../../constants/common');
const logger = require('../../../logger.service');
const {
  sendMessageToSQSQueue,
} = require('../../../../handlers/sqs.handler');
const {
  SUBSCRIPTION_UPDATER_QUEUE_URL,
  NAS_IO_FRONTEND_URL,
} = require('../../../../config');
const {
  mapFailureCodeToPaymentError,
} = require('../../../../communitiesAPI/services/common/paymentErrorUtils');
const {
  ENTITY_TYPE: PLAN_ENTITY_TYPE,
} = require('../../../plan/constants');
const {
  isProductWhitelisted,
  getProductIdFromTransaction,
} = require('../../../ebanx/ebanxWhitelist.service');

async function retrieveCountryCode(localCurrency) {
  // Get country code by currency
  const localCurrencyMapping = await CountryCurrencyMappingModel.findOne({
    localisePrice: true,
    currencyCode: localCurrency,
  }).lean();

  if (!localCurrencyMapping) {
    throw new ParamError(`Currency code ${localCurrency} is not accepted`);
  }

  const countryCode = localCurrencyMapping.countryCode;

  return countryCode;
}

async function addLoyaltyFlagToMetadata(
  metadata,
  transaction,
  purchaseType
) {
  try {
    // Get product ID from transaction
    const productId = await getProductIdFromTransaction(
      transaction,
      purchaseType
    );

    // Check if product is whitelisted
    const isWhitelisted = await isProductWhitelisted(productId);

    if (isWhitelisted) {
      // Create a copy of metadata to avoid modifying the original parameter
      const enhancedMetadata = { ...metadata };
      // Add loyal_customer flag to metadata.risk
      if (!enhancedMetadata.risk) {
        enhancedMetadata.risk = {};
      }
      enhancedMetadata.risk.loyal_customer = true;

      logger.info(
        `EBANX Whitelist: Added loyal_customer flag for product ${productId}`
      );

      return enhancedMetadata;
    }

    return metadata;
  } catch (error) {
    logger.error(
      'EBANX Whitelist: Error adding loyalty flag to metadata:',
      error
    );
    // Return original metadata on error to not break payment flow
    return metadata;
  }
}

async function directChargeForAddon({
  addonTransaction,
  paymentMetadata,
  metadata,
  userPaymentToken,
  countryCode,
}) {
  const { description } = paymentMetadata;
  const { purchaseType } = paymentMetadata.metadata;

  const {
    local_amount: localAmount,
    local_currency: localCurrency,
    email,
    _id: addonTransactionObjectId,
  } = addonTransaction;

  let ebanxMetadata = {
    purchaseType,
    purchaseId: addonTransactionObjectId,
    description,
  };

  // Add loyalty flag for whitelisted products
  ebanxMetadata = await addLoyaltyFlagToMetadata(
    ebanxMetadata,
    addonTransaction,
    purchaseType
  );

  const paymentBackendRpc = new PaymentBackendRpc();
  await paymentBackendRpc.init();

  if (localAmount === 0) {
    throw new ParamError(`Direct charge 0 amount is not allowed`);
  }

  // Only enforce 3DS auth for Brazil payment
  const enforce3DS = localCurrency === EBANX_SUPPORTED_CURRENCY.BRL;

  const payment = await paymentBackendRpc.ebanxDirectCharge(
    localAmount,
    localCurrency,
    userPaymentToken.metadata.name,
    email,
    countryCode,
    ebanxMetadata,
    userPaymentToken.paymentToken,
    userPaymentToken.metadata.paymentTypeCode,
    userPaymentToken.metadata.cpfId ?? '',
    userPaymentToken.metadata.taxPayerId ?? '',
    metadata.redirectUrl,
    enforce3DS
  );

  // Add ebanx payment id in purchase transaction
  // To verify ebanx payment by api call from us
  await AddonTransactionModel.findByIdAndUpdate(addonTransactionObjectId, {
    'payment_details.paymentProvider': PAYMENT_PROVIDER.EBANX,
    'payment_details.userPaymentTokenId': userPaymentToken._id,
    'payment_details.paymentIntentId': payment?.payment?.hash,
    'payment_details.isDirectCharge': true,
  });

  return payment;
}

async function createPaymentPage({
  addonTransaction,
  metadata,
  paymentMetadata,
}) {
  const {
    local_amount: localAmount,
    local_currency: localCurrency,
    email,
  } = addonTransaction;

  const countryCode = await retrieveCountryCode(localCurrency);

  const { redirectUrl } = metadata;

  // 1. For Brazil and Mexico instalment
  // The extra fee that charged by Ebanx will be returned as interest fee to us
  // 2. For Colombia and Peru, fee of instalment will be charged by bank to member
  // Ebanx wont charge us any fee.
  // Its no harm for us to toggle on instalment for all BRL/MXN/COP/PEN payments.
  let instalments;
  switch (localCurrency) {
    case EBANX_SUPPORTED_CURRENCY.BRL:
    case EBANX_SUPPORTED_CURRENCY.MXN:
    case EBANX_SUPPORTED_CURRENCY.ARS:
      instalments = '1-12'; // MX/BR/AR can only support up to 12x
      break;
    case EBANX_SUPPORTED_CURRENCY.CLP:
      instalments = '1-48';
      break;
    default:
      instalments = '1-36';
  }

  const paymentBackendRpc = new PaymentBackendRpc();
  await paymentBackendRpc.init();

  if (!paymentMetadata.metadata?.name) {
    throw new ParamError(`Name is required`);
  }

  // Add loyalty flag for whitelisted products
  const purchaseType = paymentMetadata.metadata.purchaseType;
  const enhancedMetadata = await addLoyaltyFlagToMetadata(
    paymentMetadata.metadata,
    addonTransaction,
    purchaseType
  );

  return paymentBackendRpc.createEbanxPaymentPage(
    localCurrency,
    localAmount,
    email,
    enhancedMetadata.name,
    enhancedMetadata,
    countryCode.toLowerCase(),
    redirectUrl,
    instalments
  );
}

exports.createPayment = async ({
  addonTransaction,
  metadata,
  paymentMetadata,
  paymentMethodId,
}) => {
  if (paymentMethodId) {
    return this.directCharge({
      transaction: addonTransaction,
      paymentMethodId,
      paymentMetadata,
      metadata,
    });
  }

  return createPaymentPage({
    addonTransaction,
    metadata,
    paymentMetadata,
  });
};

exports.createOrSavePaymentToken = async (
  learnerObjectId,
  metadata,
  currency
) => {
  if (metadata.paymentTokenId) {
    const userPaymentToken = await UserPaymentTokenModel.findOne({
      _id: new ObjectId(metadata.paymentTokenId),
      learnerObjectId,
      status: USER_PAYMENT_TOKEN_STATUS.ACTIVE,
      savedForFutureUse: true,
    }).lean();

    if (userPaymentToken) {
      return userPaymentToken;
    }
  }
  let paymentType = '';
  if (
    EBANX_PAYMENT_TYPE.CREDIT_CARD.includes(
      metadata.paymentTypeCode.toUpperCase()
    ) ||
    EBANX_PAYMENT_TYPE.DEBIT_CARD.includes(
      metadata.paymentTypeCode.toUpperCase()
    )
  ) {
    paymentType = EBANX_RECURRING_PAYMENT_TYPE.CARD;
  } else {
    throw new Error(
      `Unsupported payment type code: ${metadata.paymentTypeCode}`
    );
  }

  const paymentToken = {
    learnerObjectId,
    paymentProvider: PAYMENT_PROVIDER.EBANX,
    paymentMethod: paymentType,
    paymentToken: metadata.paymentTokenDetails.token,
    // After user make succeed payment, the token will be changed to active in subscription-updater
    status: USER_PAYMENT_TOKEN_STATUS.INACTIVE,
    savedForFutureUse: metadata.savePaymentMethod,
    currency,
    metadata,
  };

  const userPaymentToken = await UserPaymentTokenModel.findOneAndUpdate(
    paymentToken,
    { $set: paymentToken },
    {
      upsert: true,
      new: true,
    }
  ).lean();

  return userPaymentToken;
};

async function freeTrialSubscriptionUpdate(purchaseTransactionObjectId) {
  const data = {
    purchaseTransactionObjectId,
    actionType: SUBSCRIPTION_ACTION_TYPE.SUCCESS_NEW,
    paymentProvider: PAYMENT_PROVIDER.EBANX,
    eventData: {
      status: COMMUNITY_SUBSCRIPTION_STATUSES.CURRENT,
      billingCycle: 1,
      created: Math.floor(new Date().getTime() / 1000),
      recurringPurchase: true,
      metadata: {},
    },
  };
  const requestId = httpContext.get('reqId');
  const messageBody = {
    data,
    requestor: 'LPBE',
    requestId,
  };
  logger.info(
    `Send to subscription-updater: ${JSON.stringify(messageBody)}`
  );
  await sendMessageToSQSQueue({
    queueUrl: SUBSCRIPTION_UPDATER_QUEUE_URL,
    messageBody,
  });
}

async function directChargeForSubscription({
  purchaseTransaction,
  paymentMetadata,
  metadata,
  userPaymentToken,
  countryCode,
}) {
  const { description } = paymentMetadata;

  const {
    local_amount: localAmount,
    local_currency: localCurrency,
    email,
    _id: purchaseTransactionObjectId,
  } = purchaseTransaction;

  let ebanxMetadata = {
    purchaseType: PURCHASE_TYPE.SUBSCRIPTION,
    purchaseId: purchaseTransactionObjectId,
    description,
  };

  // Add loyalty flag for whitelisted products
  ebanxMetadata = await addLoyaltyFlagToMetadata(
    ebanxMetadata,
    purchaseTransaction,
    PURCHASE_TYPE.SUBSCRIPTION
  );

  const paymentBackendRpc = new PaymentBackendRpc();
  await paymentBackendRpc.init();

  let payment = {};

  if (localAmount === 0) {
    if (localCurrency === EBANX_SUPPORTED_CURRENCY.BRL) {
      throw new Error(`Free trial via Ebanx is not allowed`);
    }
    await freeTrialSubscriptionUpdate(purchaseTransactionObjectId);
  } else {
    // Only enforce 3DS auth for Brazil payment
    const enforce3DS = localCurrency === EBANX_SUPPORTED_CURRENCY.BRL;

    payment = await paymentBackendRpc.ebanxDirectCharge(
      localAmount,
      localCurrency,
      userPaymentToken.metadata.name,
      email,
      countryCode,
      ebanxMetadata,
      userPaymentToken.paymentToken,
      userPaymentToken.metadata.paymentTypeCode,
      userPaymentToken.metadata.cpfId ?? '',
      userPaymentToken.metadata.taxPayerId ?? '',
      metadata.redirectUrl,
      enforce3DS
    );
  }

  // Add ebanx payment id in purchase transaction
  // To verify ebanx payment by api call from us
  await CommunityPurchaseTransactionModel.findByIdAndUpdate(
    purchaseTransactionObjectId,
    {
      'payment_details.paymentProvider': PAYMENT_PROVIDER.EBANX,
      'payment_details.userPaymentTokenId': userPaymentToken._id,
      'payment_details.isDirectCharge': true,
      stripeSubscriptionId: payment?.payment?.hash,
    }
  );

  return payment;
}

async function directChargeForPlan({
  planOrder,
  email,
  paymentMetadata,
  metadata,
  userPaymentToken,
  countryCode,
  isDowngrade,
}) {
  const { description } = paymentMetadata;

  const {
    amountInLocalCurrency,
    localCurrency,
    entityType,
    _id: planOrderObjectId,
    interval,
    intervalCount,
    billingCycle,
    creditHistory,
    paymentProviderSubscriptionId,
  } = planOrder;

  const billingCredit = creditHistory.find(
    (credit) => credit.billingCycle === billingCycle
  );

  const ebanxMetadata = {
    purchaseType: entityType,
    purchaseId: planOrderObjectId,
    description,
    interval,
    intervalCount,
    billingCycle,
  };

  // NOTE: Whitelist check not added for plan purchases as plans are subscription tiers,
  // not individual products that require anti-fraud whitelisting

  const paymentBackendRpc = new PaymentBackendRpc();
  await paymentBackendRpc.init();

  const chargeAmount = billingCredit?.paidAmount ?? amountInLocalCurrency;

  let payment = {};

  if (amountInLocalCurrency === 0) {
    throw new Error(`Free trial via Ebanx is not allowed`);
  } else if (isDowngrade) {
    // Downgrade flow dont need to direct charge, but will need to save the payment token
    payment = {
      payment: {
        hash: uuidV4(),
      },
    };
  } else if (chargeAmount === 0) {
    payment = await paymentBackendRpc.ebanxFetchAndUpdatePaymentStatus(
      paymentProviderSubscriptionId ?? planOrderObjectId,
      INTERNAL_EBANX_NOTIFICATION_TYPE.PLAN_ZERO_PAYMENT_SUCCESS,
      planOrderObjectId
    );
  } else {
    // Only enforce 3DS auth for Brazil payment
    const enforce3DS = localCurrency === EBANX_SUPPORTED_CURRENCY.BRL;

    payment = await paymentBackendRpc.ebanxDirectCharge(
      chargeAmount,
      localCurrency,
      userPaymentToken.metadata.name,
      email,
      countryCode,
      ebanxMetadata,
      userPaymentToken.paymentToken,
      userPaymentToken.metadata.paymentTypeCode,
      userPaymentToken.metadata.cpfId ?? '',
      userPaymentToken.metadata.taxPayerId ?? '',
      metadata.redirectUrl,
      enforce3DS
    );
  }

  // Add ebanx payment id in plan order
  // To verify ebanx payment by api call from us
  await PlanOrderModel.updateOne(
    {
      _id: planOrderObjectId,
    },
    {
      'paymentDetails.paymentProvider': PAYMENT_PROVIDER.EBANX,
      'paymentDetails.userPaymentTokenId': userPaymentToken._id,
      'paymentDetails.isDirectCharge': true,
      paymentProviderSubscriptionId: payment.payment.hash,
    }
  );

  return payment;
}

exports.directCharge = async ({
  transaction,
  email = null,
  isDowngrade,
  paymentMethodId,
  paymentMetadata,
  metadata = {},
}) => {
  const { fallbackPaymentMethodId } = paymentMetadata;
  if (!paymentMethodId && !fallbackPaymentMethodId) {
    throw new ParamError('Required payment method id');
  }

  logger.info(
    `Ebanx direct charge: paymentMetadata=${JSON.stringify(
      paymentMetadata
    )}, metadata=${JSON.stringify(metadata)}`
  );

  const { local_currency: localCurrency } = transaction;
  const {
    purchaseType,
    currency: localCurrencyInMetadata,
    name,
  } = paymentMetadata.metadata;

  const countryCode = await retrieveCountryCode(
    localCurrency ?? localCurrencyInMetadata
  );

  // Save payment token
  // eslint-disable-next-line no-param-reassign
  metadata.name = name;
  // eslint-disable-next-line no-param-reassign
  metadata.countryCode = countryCode;
  if (!metadata.redirectUrl) {
    // eslint-disable-next-line no-param-reassign
    metadata.redirectUrl = `${NAS_IO_FRONTEND_URL}/portal/settings?tab=NAS_IO_PRO&activeCommunityId=${transaction.communityObjectId.toString()}`;
  }

  if (!paymentMethodId) {
    // eslint-disable-next-line no-param-reassign
    metadata.paymentTokenId = fallbackPaymentMethodId;
  }
  const userPaymentToken = await this.createOrSavePaymentToken(
    transaction.learnerObjectId,
    metadata,
    localCurrency ?? localCurrencyInMetadata
  );

  let result;

  if (Object.values(PLAN_ENTITY_TYPE).includes(purchaseType)) {
    result = await directChargeForPlan({
      planOrder: transaction,
      email,
      paymentMetadata,
      metadata,
      userPaymentToken,
      countryCode,
      isDowngrade,
    });
  } else if (purchaseType === PURCHASE_TYPE.SUBSCRIPTION) {
    result = await directChargeForSubscription({
      purchaseTransaction: transaction,
      paymentMetadata,
      metadata,
      userPaymentToken,
      countryCode,
    });
  } else if (ADDON_PURCHASE_TYPES.includes(purchaseType)) {
    result = await directChargeForAddon({
      addonTransaction: transaction,
      paymentMetadata,
      metadata,
      userPaymentToken,
      countryCode,
    });
  } else {
    throw new ParamError(
      `${purchaseType} not supported for ebanx direct charge`
    );
  }

  if (result.failureCode) {
    const paymentError = mapFailureCodeToPaymentError(result.failureCode);
    throw new ToUserError(
      result.failureReason ?? 'Payment is failed',
      paymentError
    );
  }

  return result;
};
