const TemplateLibraryModel = require('@models/getInspired/templateLibrary.model');
const { ResourceNotFoundError } = require('@utils/error.util');

exports.retrieveTemplateLibraryInfo = async (templateLibraryId) => {
  if (!templateLibraryId) {
    return;
  }

  const templateLibrary = await TemplateLibraryModel.findById(
    templateLibraryId,
    { source: 1, sourceObjectId: 1, _id: 1 }
  ).lean();

  if (!templateLibrary) {
    throw new ResourceNotFoundError('Template library not found');
  }

  return templateLibrary;
};
