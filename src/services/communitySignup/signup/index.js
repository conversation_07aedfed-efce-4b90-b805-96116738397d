const logger = require('../../logger.service');
const PaymentBackendRpc = require('../../../rpc/paymentBackend');
const PayloadInformationService = require('./payloadInformation');
const ValidationService = require('./validation');
const ProcessTransactionService = require('./processTransaction');

exports.signup = async ({
  ip,
  userAgent,
  requestLanguagePreference,
  email,
  learnerObjectId,
  signupToken,
  communityCode,
  timezone,
  requestor,
  paymentProvider,
  trackingData,
  memberInfo,
  upsellIdentityCode,
  items,
  affiliateCode,
  cancelSubscription,
  isCheckoutToken,
}) => {
  logger.info(
    `signup: ${JSON.stringify({
      ip,
      userAgent,
      requestLanguagePreference,
      email,
      learnerObjectId,
      signupToken,
      communityCode,
      timezone,
      requestor,
      paymentProvider,
      trackingData,
      memberInfo,
      upsellIdentityCode,
      items,
      affiliateCode,
      cancelSubscription,
      isCheckoutToken,
    })}`
  );

  const paymentBackendRpc = new PaymentBackendRpc();
  await paymentBackendRpc.init();

  const payloadInformation =
    await PayloadInformationService.retrievePayloadInformation({
      communityCode,
      items,
      ip,
      memberInfo,
      learnerObjectId,
      signupToken,
      affiliateCode,
      upsellIdentityCode,
      paymentProvider,
      paymentBackendRpc,
    });

  await ValidationService.validatePayload({
    memberInfo,
    ...payloadInformation,
  });

  const transactionStatus =
    await ProcessTransactionService.processTransaction({
      ip,
      userAgent,
      timezone,
      requestor,
      trackingData,
      memberInfo,
      paymentBackendRpc,
      cancelSubscription,
      isCheckoutToken,
      ...payloadInformation,
    });

  return transactionStatus;
};

exports.signupForPlan = async ({
  ip,
  userAgent,
  requestLanguagePreference,
  email,
  memberInfo,
  learnerObjectId,
  signupToken,
  communityCode,
  timezone,
  requestor,
  paymentProvider,
  trackingData,
  items,
  communityReferralCode,
}) => {
  logger.info(
    `signupForPlan: ${JSON.stringify({
      ip,
      userAgent,
      requestLanguagePreference,
      email,
      memberInfo,
      learnerObjectId,
      signupToken,
      communityCode,
      timezone,
      requestor,
      paymentProvider,
      trackingData,
      items,
      communityReferralCode,
    })}`
  );

  const paymentBackendRpc = new PaymentBackendRpc();
  await paymentBackendRpc.init();

  const payloadInformation =
    await PayloadInformationService.retrievePayloadInformationForPlan({
      communityCode,
      items,
      ip,
      learnerObjectId,
      signupToken,
      paymentBackendRpc,
      communityReferralCode,
    });

  await ValidationService.validatePayloadForPlan({
    paymentProvider,
    ...payloadInformation,
  });

  const transactionStatus =
    await ProcessTransactionService.processTransactionForPlan({
      ip,
      userAgent,
      timezone,
      requestor,
      trackingData,
      paymentBackendRpc,
      paymentProvider,
      memberInfo,
      ...payloadInformation,
    });

  return transactionStatus;
};

exports.signupForPlanTierChange = async ({
  ip,
  userAgent,
  requestLanguagePreference,
  email,
  memberInfo,
  learnerObjectId,
  signupToken,
  communityCode,
  timezone,
  requestor,
  paymentProvider,
  trackingData,
  items,
  communityReferralCode,
}) => {
  logger.info(
    `signupForPlanTierChange: ${JSON.stringify({
      ip,
      userAgent,
      requestLanguagePreference,
      email,
      memberInfo,
      learnerObjectId,
      signupToken,
      communityCode,
      timezone,
      requestor,
      paymentProvider,
      trackingData,
      items,
      communityReferralCode,
    })}`
  );

  const paymentBackendRpc = new PaymentBackendRpc();
  await paymentBackendRpc.init();

  const payloadInformation =
    await PayloadInformationService.retrievePayloadInformationForPlan({
      communityCode,
      items,
      ip,
      learnerObjectId,
      signupToken,
      paymentBackendRpc,
      communityReferralCode,
    });

  await ValidationService.validatePayloadForPlan({
    paymentProvider,
    ...payloadInformation,
  });

  const transactionStatus =
    await ProcessTransactionService.processTransactionForPlanTierChange({
      ip,
      userAgent,
      timezone,
      requestor,
      trackingData,
      paymentBackendRpc,
      paymentProvider,
      memberInfo,
      ...payloadInformation,
    });

  return transactionStatus;
};
