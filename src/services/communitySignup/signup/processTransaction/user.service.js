const UserModel = require('../../../../models/users.model');

exports.updateFirstPurchaseInfo = async ({
  email,
  paymentStatus,
  entityStatus,
  enrollmentStatus,
  session,
}) => {
  // Update paid purchase info
  if (paymentStatus.requirePayment) {
    const firstPurchaseInfo = {
      signupId: paymentStatus.signupId,
      entityType: paymentStatus.type,
      entityObjectId: paymentStatus.entityObjectId,
    };

    await UserModel.updateOne(
      { email, isActive: true, isFirstPurchase: true },
      {
        $set: {
          firstPurchaseInfo,
        },
      },
      { session }
    );

    return firstPurchaseInfo;
  }

  const isAlreadyEnrolledCommunity =
    enrollmentStatus?.alreadyEnrolled ?? false;

  const isFreeEnrollCommunity = enrollmentStatus?.memberEnrolled ?? false;

  const isAlreadyEnrolledEntity =
    entityStatus?.alreadyRegistered ||
    entityStatus?.alreadyPurchased ||
    entityStatus?.alreadyBooked ||
    entityStatus?.alreadyJoined ||
    false;

  const isFreeEnrollEntity =
    entityStatus?.eventRegistered ||
    entityStatus?.folderPurchased ||
    entityStatus?.sessionBooked ||
    entityStatus?.challengeJoined ||
    false;

  let signupId;
  let entityType;
  let entityObjectId;

  // Update free purchase info
  if (isFreeEnrollEntity && !isAlreadyEnrolledEntity) {
    signupId = paymentStatus.signupId;
    entityType = paymentStatus.type;
    entityObjectId = paymentStatus.entityObjectId;
  } else if (isFreeEnrollCommunity && !isAlreadyEnrolledCommunity) {
    signupId = paymentStatus.signupId;
    entityType = paymentStatus.type;
    entityObjectId = paymentStatus.entityObjectId;
  }

  if (signupId && entityType && entityObjectId) {
    const firstPurchaseInfo = {
      signupId,
      entityType,
      entityObjectId,
    };

    await UserModel.updateOne(
      { email, isActive: true, isFirstPurchase: true },
      {
        $set: {
          firstPurchaseInfo,
          isFirstPurchase: false,
        },
      },
      { session }
    );

    return firstPurchaseInfo;
  }
};
