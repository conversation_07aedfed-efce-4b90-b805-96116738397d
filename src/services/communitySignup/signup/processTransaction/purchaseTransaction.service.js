const { ObjectId } = require('mongoose').Types;
const PurchaseTransactionModel = require('../../../../communitiesAPI/models/communityPurchaseTransactions.model');
const {
  PAYMENT_STATUSES,
} = require('../../../../communitiesAPI/constants');
const { DEFAULT_CURRENCY } = require('../../../../constants/common');
const logger = require('../../../logger.service');
const { ParamError } = require('../../../../utils/error.util');
const PaymentProviderUtils = require('../../../../utils/paymentProvider.util');

function retrievePostApprovalProcess(addon) {
  const postApprovalProcess = {
    type: addon.type.toLowerCase(),
    origin: new ObjectId(addon.entityId),
    quantity: addon.quantity ?? 1,
  };

  return postApprovalProcess;
}

function generatePurchaseTransaction({
  community,
  learner,
  countryInfo,
  timezone,
  requestor,
  trackingData,
  subscription,
  decodedSignupToken,
  cancelSubscription,
  stripeProductProvider,
}) {
  const { discountCode, priceId, metadata } = subscription;

  const {
    price,
    discount,
    discountedAmount,
    discountedAmountInUsd,
    isFree,
    priceDetails,
    affiliate,
    affiliateProduct,
    upsell,
  } = metadata;

  const {
    isPaidCommunity,
    code: communityCode,
    passOnTakeRate = false,
    passOnPaymentGatewayFee = false,
    config,
    requireApplication,
    templateLibrary,
  } = community;

  const {
    email,
    learnerId,
    phoneNumber,
    languagePreference,
    _id: learnerObjectId,
  } = learner;

  const { countryId, country } = countryInfo;

  let localAmount = 0;
  let localCurrency = DEFAULT_CURRENCY;
  let amountInUsd = 0;
  let interval;
  let intervalCount;

  if (isPaidCommunity) {
    localAmount = price.unitAmount;
    localCurrency = price.currency;
    amountInUsd = price.amountInUsd;

    const { recurring } = price;

    interval = recurring.interval;
    intervalCount = recurring.interval_count;
  }

  // DecodedSignupToken needed to ensure enrollments that needs approval do not auto enroll in
  // the first /signup api call.
  const completePayment =
    (isFree && requireApplication && decodedSignupToken) ||
    (isFree && !requireApplication);

  const purchaseTransaction = {
    community_code: communityCode,
    email,
    country_id: countryId,
    country,
    requestor,
    payment_details: {
      complete_payment: completePayment ? 1 : 0,
      status: completePayment
        ? PAYMENT_STATUSES.SUCCESS
        : PAYMENT_STATUSES.INCOMPLETE,
    },
    learnerId,
    learnerObjectId,
    amount: discount ? discountedAmountInUsd : amountInUsd,
    currency: DEFAULT_CURRENCY,
    local_amount: discount ? discountedAmount : localAmount,
    local_currency: localCurrency,
    languagePreference,
    passOnTakeRate,
    passOnPaymentGatewayFee,
    phoneNumber,
    isDemo: false,
    full_local_amount: discount ? localAmount : undefined,
    full_amount: discount ? amountInUsd : undefined,
    applyDiscount: discount != null,
    discountAvailed: discount != null,
    promoCodeStripeId: discount ? discountCode : undefined,
    discountObjectId: discount?._id ?? undefined,
    isFreeTrial: discount?.isFreeTrial ?? undefined,
    timezone,
    tracking_data: trackingData,
    interval,
    interval_count: intervalCount,
    priceDetails,
    stripePriceId: priceId,
    stripeProductProvider,
  };

  const purchaseTransactionMetadata = {};

  if (templateLibrary) {
    purchaseTransactionMetadata.templateLibrary = templateLibrary;
  }

  if (
    cancelSubscription &&
    config?.linkedCommunityCodesForOneSubscription?.length > 0
  ) {
    purchaseTransactionMetadata.cancelSubscription = cancelSubscription;
    purchaseTransactionMetadata.linkedCommunityCodesForOneSubscription =
      config.linkedCommunityCodesForOneSubscription;
  }

  const fixedSubscriptionDuration = config?.fixedSubscriptionDuration;

  if (fixedSubscriptionDuration) {
    purchaseTransactionMetadata.fixedSubscriptionDuration =
      fixedSubscriptionDuration;
  }

  if (affiliateProduct) {
    const {
      affiliateCode,
      customConfig,
      learnerObjectId: affiliateLearnerObjectId,
    } = affiliate;

    const affiliateCustomConfig = customConfig.find(
      ({ affiliateProductObjectId }) =>
        affiliateProductObjectId.toString() ===
        affiliateProduct._id.toString()
    );

    purchaseTransaction.affiliate = {
      commissionPercentage:
        affiliateCustomConfig?.commissionPercentage ??
        affiliateProduct.config.commissionPercentage,
      learnerObjectId: affiliateLearnerObjectId,
      code: affiliateCode,
    };
  }

  if (upsell) {
    purchaseTransaction.upsellObjectId = upsell._id;
  }

  purchaseTransaction.metadata = purchaseTransactionMetadata;

  return purchaseTransaction;
}

async function updatePurchaseTransaction(
  purchaseTransactionObjectId,
  updateData,
  session,
  ignoreFilter = false
) {
  const filter = {
    _id: purchaseTransactionObjectId,
  };

  let updatedPurchaseTransaction;

  if (ignoreFilter) {
    updatedPurchaseTransaction =
      await PurchaseTransactionModel.findOneAndUpdate(filter, updateData, {
        new: true,
        session,
      }).lean();
  } else {
    filter.$and = [
      { 'payment_details.status': { $ne: PAYMENT_STATUSES.PENDING } },
      { 'payment_details.status': { $ne: PAYMENT_STATUSES.SUCCESS } },
    ];

    updatedPurchaseTransaction =
      await PurchaseTransactionModel.findOneAndReplace(
        filter,
        updateData,
        {
          new: true,
          session,
        }
      ).lean();
  }

  if (!updatedPurchaseTransaction) {
    throw new ParamError('Purchase transaction cannot be updated');
  }

  return updatedPurchaseTransaction;
}

exports.linkSubscriptionToPurchaseTransaction = async (
  subscription,
  purchaseTransaction,
  session
) => {
  const { _id: subscriptionObjectId, subscriptionId } = subscription;
  const { _id: purchaseTransactionObjectId } = purchaseTransaction;

  const updateData = {
    subscriptionId,
    subscriptionObjectId,
  };

  return updatePurchaseTransaction(
    purchaseTransactionObjectId,
    updateData,
    session,
    true
  );
};

exports.linkApplicationToPurchaseTransaction = async (
  applicationObjectId,
  purchaseTransaction,
  session
) => {
  const { _id: purchaseTransactionObjectId } = purchaseTransaction;

  const updateData = {
    applicationObjectId,
  };

  return updatePurchaseTransaction(
    purchaseTransactionObjectId,
    updateData,
    session,
    true
  );
};

exports.removeStripeSubscriptionId = async (
  purchaseTransaction,
  session
) => {
  const { _id: purchaseTransactionObjectId } = purchaseTransaction;

  const updateData = {
    $unset: { stripeSubscriptionId: 1 },
  };

  return updatePurchaseTransaction(
    purchaseTransactionObjectId,
    updateData,
    session,
    true
  );
};

exports.linkDiscountToPurchaseTransaction = async (
  discountTransactionId,
  purchaseTransaction,
  session
) => {
  const { _id: purchaseTransactionObjectId } = purchaseTransaction;

  const updateData = {
    discountTransactionId,
  };

  return updatePurchaseTransaction(
    purchaseTransactionObjectId,
    updateData,
    session,
    true
  );
};

function isUpdateOperation(decodedSignupToken) {
  return decodedSignupToken?.purchaseTransactionObjectId != null;
}

exports.createOrUpdatePurchaseTransaction = async ({
  community,
  learner,
  countryInfo,
  timezone,
  requestor,
  trackingData,
  subscription,
  decodedSignupToken,
  session,
  cancelSubscription,
}) => {
  const communityPaymentProvider =
    await PaymentProviderUtils.retrievePaymentProvider(
      community.payment_methods
    );

  const generatedPurchaseTransaction = generatePurchaseTransaction({
    community,
    learner,
    countryInfo,
    timezone,
    requestor,
    trackingData,
    subscription,
    decodedSignupToken,
    cancelSubscription,
    stripeProductProvider: communityPaymentProvider,
  });

  logger.info(
    `createOrUpdatePurchaseTransaction: generated purchase transaction: ${JSON.stringify(
      generatedPurchaseTransaction
    )}`
  );

  const updateOperation = isUpdateOperation(decodedSignupToken);

  logger.info(
    `createOrUpdatePurchaseTransaction: updateOperation: ${updateOperation}`
  );

  let purchaseTransaction;

  if (updateOperation) {
    purchaseTransaction = await updatePurchaseTransaction(
      decodedSignupToken.purchaseTransactionObjectId,
      generatedPurchaseTransaction,
      session
    );
  } else {
    purchaseTransaction = (
      await PurchaseTransactionModel.create(
        [generatedPurchaseTransaction],
        { session, lean: true }
      )
    )[0];
  }

  return purchaseTransaction;
};

exports.updatePurchaseTransactionPostApprovalProcess = async (
  purchaseTransactionObjectId,
  addon,
  session
) => {
  if (!addon) {
    return;
  }

  const postApprovalProcess = retrievePostApprovalProcess(addon);

  const updateData = {
    post_approval_processes: [postApprovalProcess],
    event_source: postApprovalProcess.origin,
  };

  return updatePurchaseTransaction(
    purchaseTransactionObjectId,
    updateData,
    session,
    true
  );
};
