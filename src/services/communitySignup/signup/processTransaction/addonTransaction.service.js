/* eslint-disable no-param-reassign */
const logger = require('../../../logger.service');
const {
  COMMUNITY_ONE_TIME_PAYMENT_ENTITIES,
  COMMUNITY_ENTITY_ONE_TIME_PAYMENT_STATUSES,
} = require('../../../../communitiesAPI/constants');
const {
  PURCHASE_TYPE,
  DEFAULT_CURRENCY,
  PRODUCT_PURCHASE_TYPES,
} = require('../../../../constants/common');
const { ParamError } = require('../../../../utils/error.util');
const DiscountService = require('./discount.service');
const AddonTransactionModel = require('../../../../communitiesAPI/models/communityAddonTransactions.model');
const {
  createOrUpdateAbandonedCarts,
} = require('./abandonedCarts.service');

function generateAddonTransaction({
  community,
  learner,
  countryInfo,
  addon,
  memberEnrolled,
  decodedSignupToken,
  entityMetadata = null,
  requestor,
  trackingData,
}) {
  const { type, discountCode, metadata, quantity = 1 } = addon;

  const {
    entityInfo,
    priceDetail,
    amountInUsd,
    discount,
    discountedAmount,
    discountedAmountInUsd,
    isFree,
    affiliate,
    affiliateProduct,
    upsell,
  } = metadata;

  let entityCollection;

  if (PRODUCT_PURCHASE_TYPES.includes(type)) {
    entityCollection = COMMUNITY_ONE_TIME_PAYMENT_ENTITIES.FOLDER;
  } else {
    switch (type) {
      case PURCHASE_TYPE.EVENT:
        entityCollection = COMMUNITY_ONE_TIME_PAYMENT_ENTITIES.EVENT;
        break;
      case PURCHASE_TYPE.CHALLENGE:
        entityCollection = COMMUNITY_ONE_TIME_PAYMENT_ENTITIES.CHALLENGE;
        break;
      case PURCHASE_TYPE.ZERO_LINK:
        entityCollection = COMMUNITY_ONE_TIME_PAYMENT_ENTITIES.ZERO_LINK;
        break;
      default:
        throw new ParamError(`${type} is invalid type`);
    }
  }

  const {
    _id: entityObjectId,
    applicationConfigDataFields,
    pricingConfig,
    templateLibrary,
  } = entityInfo;

  const { email, learnerId, _id: learnerObjectId } = learner;

  // Use the pass on fee toggle from product level before community level
  // Lets say [zero link] has own toggle for pass on, then we should use product level one
  const passOnTakeRate =
    entityInfo.passOnTakeRate ?? community.passOnTakeRate ?? false;
  const passOnPaymentGatewayFee =
    entityInfo.passOnPaymentGatewayFee ??
    community.passOnPaymentGatewayFee ??
    false;

  const { countryId, country } = countryInfo;

  const {
    originalCheckoutAmount: localAmount,
    checkoutCurrency: localCurrency,
  } = priceDetail;

  const requireApplication = applicationConfigDataFields?.length > 0;

  const completePayment =
    isFree &&
    memberEnrolled &&
    (!requireApplication || decodedSignupToken);

  const addonTransaction = {
    communityObjectId: community._id,
    entityCollection,
    entityObjectId,
    email,
    countryId,
    payment_details: {
      status: completePayment
        ? COMMUNITY_ENTITY_ONE_TIME_PAYMENT_STATUSES.SUCCESS
        : COMMUNITY_ENTITY_ONE_TIME_PAYMENT_STATUSES.INCOMPLETE,
    },
    local_amount: discount ? discountedAmount : localAmount,
    local_currency: localCurrency,
    amount: discount ? discountedAmountInUsd : amountInUsd,
    currency: DEFAULT_CURRENCY,
    country,
    learnerId,
    learnerObjectId,
    applyDiscount: discount != null,
    discountAvailed: discount != null,
    promoCodeStripeId: discount ? discountCode : undefined,
    discount: discount?._id ?? undefined,
    full_local_amount: discount ? localAmount : undefined,
    full_amount: discount ? amountInUsd : undefined,
    requestor,
    tracking_data: trackingData,
    passOnTakeRate,
    passOnPaymentGatewayFee,
    priceDetails: priceDetail,
    quantity,
  };

  const addonMetadata = { ...(entityMetadata ?? {}) };

  addonMetadata.type = type;
  addonMetadata.communityObjectId = community._id;
  addonMetadata.pricingConfig = pricingConfig;

  if (templateLibrary) {
    addonMetadata.templateLibrary = templateLibrary;
  }

  addonTransaction.metadata = addonMetadata;

  if (affiliateProduct) {
    const {
      affiliateCode,
      customConfig,
      learnerObjectId: affiliateLearnerObjectId,
    } = affiliate;

    const affiliateCustomConfig = customConfig.find(
      ({ affiliateProductObjectId }) =>
        affiliateProductObjectId.toString() ===
        affiliateProduct._id.toString()
    );

    addonTransaction.affiliate = {
      commissionPercentage:
        affiliateCustomConfig?.commissionPercentage ??
        affiliateProduct.config.commissionPercentage,
      learnerObjectId: affiliateLearnerObjectId,
      code: affiliateCode,
    };
  }

  if (upsell) {
    addonTransaction.upsellObjectId = upsell._id;
  }

  return addonTransaction;
}

async function updateAddonTransaction(
  addonTransactionObjectId,
  updateData,
  session,
  ignoreFilter = false
) {
  const filter = {
    _id: addonTransactionObjectId,
  };

  let updatedAddonTransaction;

  if (ignoreFilter) {
    updatedAddonTransaction = await AddonTransactionModel.findOneAndUpdate(
      filter,
      updateData,
      {
        new: true,
        session,
      }
    ).lean();
  } else {
    filter.$and = [
      {
        'payment_details.status': {
          $ne: COMMUNITY_ENTITY_ONE_TIME_PAYMENT_STATUSES.PENDING,
        },
      },
      {
        'payment_details.status': {
          $ne: COMMUNITY_ENTITY_ONE_TIME_PAYMENT_STATUSES.SUCCESS,
        },
      },
    ];

    updatedAddonTransaction =
      await AddonTransactionModel.findOneAndReplace(filter, updateData, {
        new: true,
        session,
      }).lean();
  }

  if (!updatedAddonTransaction) {
    throw new ParamError('Addon transaction cannot be updated');
  }

  return updatedAddonTransaction;
}

async function linkDiscountToAddonTransaction(
  discountTransactionId,
  addonTransaction,
  session
) {
  const { _id: addonTransactionObjectId } = addonTransaction;

  const updateData = {
    discountTransactionId,
  };

  return updateAddonTransaction(
    addonTransactionObjectId,
    updateData,
    session,
    true
  );
}

function isUpdateOperation(decodedSignupToken) {
  return (
    decodedSignupToken?.addonTransactionObjectId != null &&
    decodedSignupToken.entityType !== PURCHASE_TYPE.SUBSCRIPTION
  );
}

exports.createOrUpdateAddonTransaction = async ({
  community,
  learner,
  countryInfo,
  addon,
  decodedSignupToken,
  memberEnrolled,
  session,
  entityMetadata = null,
  requestor,
  trackingData,
}) => {
  const generatedAddonTransaction = generateAddonTransaction({
    community,
    learner,
    countryInfo,
    addon,
    memberEnrolled,
    decodedSignupToken,
    entityMetadata,
    requestor,
    trackingData,
  });

  logger.info(
    `createOrUpdateAddonTransaction: generated addon transaction: ${JSON.stringify(
      generatedAddonTransaction
    )}`
  );

  const updateOperation = isUpdateOperation(decodedSignupToken);

  logger.info(
    `createOrUpdateAddonTransaction: updateOperation: ${updateOperation}`
  );

  let addonTransaction;

  if (updateOperation) {
    addonTransaction = await updateAddonTransaction(
      decodedSignupToken.addonTransactionObjectId,
      generatedAddonTransaction,
      session
    );
  } else {
    addonTransaction = (
      await AddonTransactionModel.create([generatedAddonTransaction], {
        session,
      })
    )[0];
  }

  const { type, metadata, quantity = 1 } = addon;
  const { discount } = metadata;

  const discountTransaction =
    await DiscountService.createDiscountTransaction({
      discount,
      community,
      transaction: addonTransaction,
      type,
      quantity,
      session,
    });

  if (discountTransaction) {
    logger.info(
      `createOrUpdateAddonTransaction: discountTransaction: ${JSON.stringify(
        discountTransaction
      )}`
    );

    addonTransaction = await linkDiscountToAddonTransaction(
      discountTransaction._id,
      addonTransaction,
      session
    );
  }

  // Create abandoned cart
  await createOrUpdateAbandonedCarts({
    addonTransaction,
    community,
    learner,
    entity: addon,
    session,
  });

  return addonTransaction;
};

exports.getAddonTransaction = async ({ addonTransactionObjectId }) => {
  return AddonTransactionModel.findById(addonTransactionObjectId).lean();
};
