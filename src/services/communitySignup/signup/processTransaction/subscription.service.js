const OperationCounter = require('../../../../models/platform/operationCounter.model');
const WhitelistedIP = require('../../../../models/fraud/whitelistedIPs.model');
const SubscriptionModel = require('../../../../communitiesAPI/models/communitySubscriptions.model');

const PurchaseTransactionService = require('./purchaseTransaction.service');
const ApplicationService = require('./application.service');
const DiscountService = require('./discount.service');
const DiscordService = require('./discord.service');
const WhatsappService = require('./whatsapp.service');
const PaymentMetadataService = require('./paymentMetadata');
const CommunityNotificationService = require('../../../communityNotification');
const ActionEventService = require('../../../actionEvent');
const LearnerService = require('./learner.service');
const CheckInternalMemberUtils = require('../../../../utils/checkInternalMember.util');
const ConfigService = require('../../../config.service');
const membershipService = require('../../../membership');
const membersLimitNotificationService = require('../../../common/membersLimitNotification.service');
const {
  addMembershipAbandonedCarts,
  removeMembershipAbandonedCartRecord,
} = require('./abandonedCarts.service');
const batchMetadataService = require('../../../batchMetadata');

const { RateLimitError } = require('../../../../utils/error.util');

const logger = require('../../../logger.service');

const {
  communityEnrolmentStatuses,
  COMMUNITY_MEMBER_TYPES,
  MEMBERSHIP_ACTION_EVENT_TYPES,
  BATCH_METADATA_MODEL_TYPE,
} = require('../../../../constants/common');
const {
  MEMBERSHIP_PLAN_INTERVAL,
} = require('../../../../communitiesAPI/constants');
const { CONFIG_TYPES } = require('../../../../constants/common');

async function createSubscription({
  community,
  purchaseTransaction,
  subscription,
  learner,
  isFullyDiscounted,
  status = communityEnrolmentStatuses.CURRENT,
  session,
}) {
  const {
    stripeProductId,
    code: communityCode,
    request_approval: requestApproval,
  } = community;

  const {
    email,
    learnerId,
    learnerObjectId,
    localAmount,
    local_currency: localCurrency,
    amount,
    currency,
    country,
    phoneNumber,
    _id: purchaseTransactionObjectId,
  } = purchaseTransaction;

  const isInternalDomain =
    CheckInternalMemberUtils.hasInternalDomain(email);

  const freeSubscription = {
    communityCode,
    email,
    learnerId,
    learnerObjectId,
    stripeProductId,
    stripePrice: localAmount,
    stripeCurrency: localCurrency,
    amount,
    currency,
    country,
    memberType: isInternalDomain
      ? COMMUNITY_MEMBER_TYPES.NASACADEMY
      : COMMUNITY_MEMBER_TYPES.FREE,
    status,
    invoice_details: {},
    isDemo: false,
    interval: MEMBERSHIP_PLAN_INTERVAL.MONTH,
    intervalCount: 1,
    isRepurchase: false,
    communitySignupId: purchaseTransactionObjectId,
    phoneNumber,
  };

  if (requestApproval && status === communityEnrolmentStatuses.CURRENT) {
    freeSubscription.applicationReviewDate = new Date();
  }

  const [createdSubscription] = await SubscriptionModel.create(
    [freeSubscription],
    { session }
  );

  await PurchaseTransactionService.linkSubscriptionToPurchaseTransaction(
    createdSubscription,
    purchaseTransaction,
    session
  );

  await PurchaseTransactionService.removeStripeSubscriptionId(
    purchaseTransaction,
    session
  );

  if (isFullyDiscounted) {
    await DiscountService.updateDiscountApplied({
      transaction: purchaseTransaction,
      item: subscription,
      session,
      community,
      learner,
    });
  }

  await WhatsappService.resetWhatsappRemovalFlag({
    community,
    subscription: createdSubscription,
    learner,
    session,
  });

  await ApplicationService.linkSubscriptionToApplication({
    subscription: createdSubscription,
    purchaseTransaction,
    session,
  });

  await batchMetadataService.add({
    batchMetadataModelType: BATCH_METADATA_MODEL_TYPE.SUBSCRIPTION,
    entityObjectId: community._id,
    communityObjectId: community._id,
    community,
    addedObjectId: createdSubscription._id,
  });

  DiscordService.assignDiscordMemberRole({
    subscription: createdSubscription,
    learner,
  });

  return createdSubscription;
}

async function handlePostApprovalProcess({
  purchaseTransactionObjectId,
  addon,
  session,
}) {
  if (addon && addon.metadata.isFree) {
    await PurchaseTransactionService.updatePurchaseTransactionPostApprovalProcess(
      purchaseTransactionObjectId,
      addon,
      session
    );

    return true;
  }

  return false;
}

async function updateExistingSubscription({ subscription, session }) {
  const { existingSubscription } = subscription.metadata;

  const { _id: subscriptionObjectId } = existingSubscription;

  const status = communityEnrolmentStatuses.INACTIVE;

  await SubscriptionModel.findByIdAndUpdate(
    subscriptionObjectId,
    {
      status,
    },
    { session }
  ).lean();

  logger.info(
    `updateExistingSubscription: ${subscriptionObjectId} status to ${status}`
  );
}

function getSubscriptionCountThreshold() {
  const { envVarData = null } = ConfigService.getConfigByTypeFromCacheSync(
    CONFIG_TYPES.LEARN_PORTAL_BACKEND_ENV_CONFIG_TYPE
  );
  const threshold =
    envVarData?.['FREE_SUBSCRIPTION_COUNT_PER_COMMUNITY_THRESHOLD'] ?? 3;

  return threshold;
}

// eslint-disable-next-line no-unused-vars
async function spamDetectionByIp({ ip, communityCode }) {
  const isWhitelisted = await WhitelistedIP.exists({
    ip,
  });
  if (isWhitelisted) {
    return;
  }

  const countThreshold = getSubscriptionCountThreshold();
  const counter = await OperationCounter.findOne({
    operation: 'signup_confirm_free',
    identifier: `${ip}_${communityCode}`,
  });

  if (counter && counter.count >= countThreshold) {
    throw new RateLimitError('Too many requests');
  }

  await OperationCounter.updateOne(
    {
      operation: 'signup_confirm_free',
      identifier: `${ip}_${communityCode}`,
    },
    {
      $inc: {
        count: 1,
      },
    },
    {
      upsert: true,
    }
  );
}

async function handleAutoEnrollment({
  ip,
  userAgent,
  community,
  memberInfo,
  subscription,
  learner,
  addon,
  purchaseTransaction,
  application,
  isSubscribed,
  paymentProvider,
  session,
}) {
  logger.info('handleAutoEnrollment: start');
  const {
    isPaidCommunity,
    request_approval: requestApproval,
    requireApplication,
    applicationConfig,
  } = community;

  const {
    _id: purchaseTransactionObjectId,
    local_currency: localCurrency,
  } = purchaseTransaction;

  let memberEnrolled = false;
  let subscriptionStatus = communityEnrolmentStatuses.CURRENT;
  let havePostApprovalProcess = false;

  const hasApplication = purchaseTransaction.applicationObjectId != null;
  const autoApproval = requestApproval // FE not shld not be using this anymore
    ? applicationConfig?.autoApproval ?? false
    : undefined;

  const {
    isFree: isFreeSubscription,
    requirePayment,
    discount,
    renewForOnetimeSubscription,
    existingSubscription,
  } = subscription.metadata;

  const { applicationData } = subscription;

  const isFullyDiscounted = isPaidCommunity && isFreeSubscription;

  let paymentMetadata;

  if (requireApplication && !hasApplication) {
    return {
      alreadyEnrolled: isSubscribed,
      requireApproval: requestApproval,
      requireApplication,
      autoApproval, // FE not shld not be using this anymore
      memberEnrolled: isSubscribed,
      requirePayment,
      havePostApprovalProcess,
      currency: localCurrency,
      purchaseTransactionObjectId,
    };
  }

  if (!requirePayment) {
    // await spamDetectionByIp({ ip, communityCode: code });

    let actionEventType =
      MEMBERSHIP_ACTION_EVENT_TYPES.SUBSCRIPTION_SUCCESS;

    if (requestApproval) {
      subscriptionStatus = communityEnrolmentStatuses.PENDING;
      actionEventType =
        MEMBERSHIP_ACTION_EVENT_TYPES.SUBSCRIPTION_PENDING_APPROVAL;

      // Add free addon to purchase transaction as post approval process
      // Approve api will handle it (subscription after approval)
      // If it is added to post approval process, it will not be handle again
      havePostApprovalProcess = await handlePostApprovalProcess({
        purchaseTransactionObjectId,
        addon,
        session,
      });
    }

    const createdSubscription = await createSubscription({
      community,
      purchaseTransaction,
      subscription,
      learner,
      isFullyDiscounted,
      status: subscriptionStatus,
      session,
    });

    const updatedLearner =
      await LearnerService.updateApplicationDataToLearner({
        applicationData,
        learner,
      });

    await membersLimitNotificationService.notifyMembersLimit({
      community,
      additionalEmails: 1,
    });

    await membershipService.enrollmentService.enrollMembership({
      community,
      subscription: createdSubscription,
      learner: updatedLearner,
      purchaseTransaction,
      session,
    });

    if (renewForOnetimeSubscription) {
      await updateExistingSubscription({ subscription, session });
    } else {
      await ActionEventService.sendMembershipActionEvent({
        actionEventType,
        purchaseTransaction,
        subscription: createdSubscription,
        community,
      });

      await CommunityNotificationService.sendEnrollmentNotification({
        community,
        learner: updatedLearner,
        purchaseTransaction,
        application,
      });
    }

    memberEnrolled = true;
  } else {
    // Add free addon to purchase transaction as post approval process
    // Either webhook (after purchase) or approve api will handle it (after approval)
    // If it is added to post approval process, it will not be handle again
    havePostApprovalProcess = await handlePostApprovalProcess({
      purchaseTransactionObjectId,
      addon,
      session,
    });

    paymentMetadata =
      PaymentMetadataService.generateSubscriptionPaymentMetadata({
        community,
        purchaseTransaction,
        memberInfo,
        learner,
        discount,
        renewForOnetimeSubscription,
        existingSubscription,
        ip,
        userAgent,
        paymentProvider,
      });
  }

  return {
    alreadyEnrolled: isSubscribed,
    requireApproval: requestApproval,
    requireApplication,
    autoApproval, // FE not shld not be using this anymore
    subscriptionStatus: memberEnrolled ? subscriptionStatus : undefined,
    memberEnrolled,
    requirePayment,
    havePostApprovalProcess,
    currency: localCurrency,
    purchaseTransactionObjectId,
    paymentMetadata,
  };
}

exports.handleSubscription = async ({
  ip,
  userAgent,
  community,
  memberInfo,
  learner,
  countryInfo,
  timezone,
  requestor,
  trackingData,
  subscription,
  addon,
  decodedSignupToken,
  paymentProvider,
  session,
  cancelSubscription,
}) => {
  const {
    requireApplication,
    request_approval: requestApproval,
    applicationConfig,
  } = community;

  const {
    isSubscribed,
    requirePayment,
    existingSubscription,
    renewForOnetimeSubscription,
  } = subscription.metadata;

  const hasPaidAddon = addon?.metadata?.requirePayment === true;
  const isApplicationBasedAddon =
    addon?.metadata?.requireApplication === true;
  const isFreePaidFlow =
    !requirePayment && (hasPaidAddon || isApplicationBasedAddon);
  const isInitialStageForFreePaidFlow =
    isFreePaidFlow && !decodedSignupToken;

  if (
    (isSubscribed && !renewForOnetimeSubscription) ||
    isInitialStageForFreePaidFlow
  ) {
    return {
      alreadyEnrolled: isSubscribed,
      requireApproval: requestApproval,
      requireApplication,
      autoApproval: requestApproval // FE not should not be using this anymore
        ? applicationConfig?.autoApproval ?? false
        : undefined,
      subscriptionStatus: existingSubscription?.status,
      memberEnrolled: isSubscribed,
      havePostApprovalProcess:
        existingSubscription?.status === communityEnrolmentStatuses.PENDING
          ? null
          : false,
      requirePayment,
    };
  }

  let purchaseTransaction =
    await PurchaseTransactionService.createOrUpdatePurchaseTransaction({
      community,
      learner,
      countryInfo,
      timezone,
      requestor,
      trackingData,
      subscription,
      decodedSignupToken,
      session,
      cancelSubscription,
    });

  // Handle linking of application to subscription
  const { applicationData } = subscription;
  const { _id: purchaseTransactionObjectId } = purchaseTransaction;

  const application = await ApplicationService.createApplication({
    community,
    applicationData,
    countryInfo,
    learner,
    purchaseTransactionObjectId,
    existingSubscription,
    renewForOnetimeSubscription,
    session,
    decodedSignupToken,
  });

  if (application) {
    purchaseTransaction =
      await PurchaseTransactionService.linkApplicationToPurchaseTransaction(
        application._id,
        purchaseTransaction,
        session
      );
  }

  // Handle linking of discount to subscription
  const { type, metadata } = subscription;
  const { discount } = metadata;

  const discountTransaction =
    await DiscountService.createDiscountTransaction({
      discount,
      community,
      transaction: purchaseTransaction,
      type,
      session,
    });

  if (discountTransaction) {
    purchaseTransaction =
      await PurchaseTransactionService.linkDiscountToPurchaseTransaction(
        discountTransaction._id,
        purchaseTransaction,
        session
      );
  }

  const enrollmentStatus = await handleAutoEnrollment({
    ip,
    userAgent,
    community,
    subscription,
    memberInfo,
    learner,
    addon,
    purchaseTransaction,
    application,
    isSubscribed,
    paymentProvider,
    session,
  });

  if (
    enrollmentStatus.requirePayment &&
    !enrollmentStatus.alreadyEnrolled
  ) {
    await addMembershipAbandonedCarts({
      community,
      purchaseTransaction,
      learner,
      session,
    });
  } else if (
    !enrollmentStatus.requirePayment ||
    enrollmentStatus.alreadyEnrolled
  ) {
    // Update the membership abandoned cart info if there is no payment required
    // - For member who use 100% discount forever
    await removeMembershipAbandonedCartRecord(
      purchaseTransaction,
      session
    );
  }
  return enrollmentStatus;
};
