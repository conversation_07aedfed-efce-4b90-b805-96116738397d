const AddonTransactionService = require('./addonTransaction.service');
const CheckInternalMemberUtils = require('../../../../utils/checkInternalMember.util');
const {
  COMMUNITY_FOLDER_PURCHASE_TYPES,
  PAYMENT_STATUSES,
} = require('../../../../communitiesAPI/constants');
const {
  communityEnrolmentStatuses,
  PURCHASE_TYPE,
} = require('../../../../constants/common');
const PostApprovalProcessService = require('./postApprovalProcess.service');
const CommunityFolderService = require('../../../../communitiesAPI/services/common/communityFolders.service');
const { upsertFullDiscountFolderViewers } = require('../../../folder');
const DiscountService = require('./discount.service');
const PaymentMetadataService = require('./paymentMetadata');

async function handleAutoPurchase({
  memberInfo,
  ip,
  userAgent,
  paymentProvider,
  learner,
  entity,
  addonTransaction,
  community,
}) {
  const { metadata, type } = entity;
  const { entityInfo, requirePayment, discount } = metadata;

  const { _id: addonTransactionObjectId, local_currency: localCurrency } =
    addonTransaction;

  const paymentMetadata =
    PaymentMetadataService.generateAddonPaymentMetadata({
      memberInfo,
      addonTransaction,
      entity: entityInfo,
      discount,
      ip,
      userAgent,
      paymentProvider,
      learner,
      community,
    });

  return {
    alreadyPurchased: false,
    requirePayment,
    addonTransactionObjectId,
    currency: localCurrency,
    type,
    paymentMetadata,
  };
}

exports.handleZeroLink = async ({
  memberInfo,
  ip,
  userAgent,
  paymentProvider,
  community,
  learner,
  countryInfo,
  entity,
  decodedSignupToken,
  memberEnrolled,
  requestor,
  trackingData,
  session,
}) => {
  const { addonTransactionObjectId } = decodedSignupToken ?? {};

  if (addonTransactionObjectId) {
    const existedAddonTransaction =
      await AddonTransactionService.getAddonTransaction({
        addonTransactionObjectId,
      });
    if (
      [PAYMENT_STATUSES.SUCCESS].includes(
        existedAddonTransaction.payment_details.status
      )
    ) {
      return {
        alreadyPurchased: true,
        requirePayment: false,
        type: entity.type,
      };
    }
  }

  const addonTransaction =
    await AddonTransactionService.createOrUpdateAddonTransaction({
      community,
      learner,
      countryInfo,
      addon: entity,
      decodedSignupToken,
      memberEnrolled,
      requestor,
      trackingData,
      session,
    });

  const purchaseStatus = await handleAutoPurchase({
    memberInfo,
    ip,
    userAgent,
    paymentProvider,
    learner,
    entity,
    addonTransaction,
    community,
  });

  return purchaseStatus;
};
