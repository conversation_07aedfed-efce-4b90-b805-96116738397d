const {
  UPSELL_SOURCE_ENTITY_TYPE,
  PURCHASE_TYPE,
} = require('../../../../constants/common');
const {
  PARTICIPANT_PROGRAM_ITEM_STATUS,
} = require('../../../program/constants');
const { ToUserError } = require('../../../../utils/error.util');
const { GENERIC_ERROR } = require('../../../../constants/errorCode');
const validationService = require('../../../upsell/validation');
const { PRODUCT_TYPE } = require('../../../product/constants');

function validateUpsellIsActive(upsell) {
  const { isActive } = upsell;

  if (!isActive) {
    throw new ToUserError(
      'Upsell not available',
      GENERIC_ERROR.UPSELL_GENERIC_ERROR
    );
  }
}

function validateJoinedChallenge(upsell) {
  const { sourceEntity } = upsell;
  const { metadata } = sourceEntity;
  const { participant } = metadata;

  if (!participant) {
    throw new ToUserError(
      'Upsell not available',
      GENERIC_ERROR.UPSELL_GENERIC_ERROR
    );
  }
}

function validateCompletedCheckpointSubmission(upsell) {
  const { sourceEntity } = upsell;
  const { metadata } = sourceEntity;
  const { participant } = metadata;

  if (!participant?.checkpointSubmission) {
    throw new ToUserError(
      'Upsell not available',
      GENERIC_ERROR.UPSELL_GENERIC_ERROR
    );
  }

  const { checkpointSubmission } = participant;
  const { completionStatus } = checkpointSubmission;

  const allowedCompletionStatus = [
    PARTICIPANT_PROGRAM_ITEM_STATUS.COMPLETED,
    PARTICIPANT_PROGRAM_ITEM_STATUS.LATE_COMPLETED,
  ];

  if (!allowedCompletionStatus.includes(completionStatus)) {
    throw new ToUserError(
      'Upsell not available',
      GENERIC_ERROR.UPSELL_GENERIC_ERROR
    );
  }
}

function validateSourceEntity(upsell) {
  const { sourceEntity, sourceEntityType } = upsell;

  validationService.validateUpsellSourceEntity(
    sourceEntity,
    sourceEntityType
  );

  switch (sourceEntityType) {
    case UPSELL_SOURCE_ENTITY_TYPE.CHALLENGE:
      validateJoinedChallenge(upsell);
      break;
    case UPSELL_SOURCE_ENTITY_TYPE.CHECKPOINT:
      validateCompletedCheckpointSubmission(upsell);
      break;
    default:
      throw new ToUserError(
        'Upsell not available',
        GENERIC_ERROR.UPSELL_GENERIC_ERROR
      );
  }
}

function validateUpsellEntity(item, community) {
  const { metadata, type } = item;
  const { upsell, entityInfo } = metadata;
  const { _id: communityObjectId } = community;

  const entityObjectId =
    type === PURCHASE_TYPE.SUBSCRIPTION
      ? communityObjectId
      : entityInfo._id;

  const { upsellEntityObjectId, upsellCommunityObjectId } = upsell;

  let upsellEntityType = upsell.upsellEntityType;
  if (
    [PRODUCT_TYPE.COURSE, PRODUCT_TYPE.DIGITAL_FILES].includes(
      upsellEntityType
    )
  ) {
    upsellEntityType = PURCHASE_TYPE.FOLDER;
  }

  if (upsellEntityType !== type) {
    throw new ToUserError(
      'Upsell not available',
      GENERIC_ERROR.UPSELL_GENERIC_ERROR
    );
  }

  if (
    upsellCommunityObjectId.toString() !== communityObjectId.toString()
  ) {
    throw new ToUserError(
      'Upsell not available',
      GENERIC_ERROR.UPSELL_GENERIC_ERROR
    );
  }

  if (upsellEntityObjectId.toString() !== entityObjectId.toString()) {
    throw new ToUserError(
      'Upsell not available',
      GENERIC_ERROR.UPSELL_GENERIC_ERROR
    );
  }
}

exports.validateUpsell = (item, community) => {
  const { metadata } = item;
  const { upsell } = metadata;

  if (!upsell) {
    return;
  }

  validateUpsellIsActive(upsell);
  validateSourceEntity(upsell);
  validateUpsellEntity(item, community);
};
