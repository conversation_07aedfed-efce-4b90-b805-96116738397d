const CommunityModel = require('../../../../communitiesAPI/models/community.model');
const SubscriptionUtil = require('./utils/subscription.util');
const { TemplateLibraryService } = require('../../common');
const { ResourceNotFoundError } = require('../../../../utils/error.util');

function checkForNoRequiredApplicationFields(community) {
  const {
    applicationConfigDataFields,
    request_approval: requestApproval,
  } = community;

  if (!requestApproval) {
    return false;
  }

  return !applicationConfigDataFields.find(
    (applicationField) =>
      applicationField.isRequired && !applicationField.isDeleted
  );
}

exports.retrieveCommunityInfo = async (communityCode) => {
  const community = await CommunityModel.findOne(
    {
      code: communityCode,
    },
    {
      description: 1,
      code: 1,
      title: 1,
      stripeProductId: 1,
      stripeUsProductId: 1,
      isActive: 1,
      isFreeCommunity: 1,
      countryCreatedIn: 1,
      applicationConfigDataFields: 1,
      isDemo: 1,
      bots: 1,
      platforms: 1,
      request_approval: 1,
      isPaidCommunity: 1,
      baseCurrency: 1,
      applicationConfig: 1,
      config: 1,
      passOnTakeRate: 1,
      passOnPaymentGatewayFee: 1,
      prices: 1,
      payoutFeeConfigs: 1,
      discountsApplied: 1,
      isWhatsappExperienceCommunity: 1,
      payment_methods: 1,
      link: 1,
      thumbnailImgData: 1,
      createdBy: 1,
      By: 1,
      restrictedInfo: 1,
      createdAt: 1,
      basePayoutFeeConfigs: 1,
      templateLibraryId: 1,
    }
  ).lean();

  if (!community) {
    throw new ResourceNotFoundError(
      `Community not found: ${communityCode}`
    );
  }

  if (community.templateLibraryId) {
    const templateLibrary =
      await TemplateLibraryService.retrieveTemplateLibraryInfo(
        community.templateLibraryId
      );
    community.templateLibrary = templateLibrary;
  }

  community.noRequiredApplicationFields =
    checkForNoRequiredApplicationFields(community);

  delete community._version;

  return community;
};

/**
 * Reassign request_approval and add new field requireApplication
 */
exports.reformatCommunityApplicationFields = (community) => {
  const newCommunity = {
    ...community,
    request_approval: community.request_approval || false,
  };
  const { applicationConfigDataFields = [] } = community;
  newCommunity.requireApplication = !!applicationConfigDataFields?.find(
    (element) => !element.isDeleted
  );

  // Handle backward compatibility with records that used to use autoApproval
  if (
    community.request_approval &&
    community.applicationConfig.autoApproval
  ) {
    newCommunity.request_approval = false;
  }

  return newCommunity;
};

exports.updateCommunityForNonRecurringSubscriptionRenewal = (
  community,
  existingSubscription
) => {
  const newCommunity = this.reformatCommunityApplicationFields(community);

  const renewForOnetimeSubscription =
    SubscriptionUtil.checkIfMemberCanRenewForOnetimeSubscription(
      existingSubscription
    );

  if (renewForOnetimeSubscription) {
    newCommunity.request_approval = false;
  }

  return newCommunity;
};
