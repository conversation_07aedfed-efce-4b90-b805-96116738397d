const { ObjectId } = require('mongoose').Types;
const {
  getAddonPriceInLocalCurrency,
} = require('../../../../communitiesAPI/services/common/communityAddonPrice.service');
const { PricingService, TemplateLibraryService } = require('../../common');
const {
  communityEnrolmentStatuses,
} = require('../../../../constants/common');
const ProgramModel = require('../../../../models/program/program.model');
const ProgramParticipantModel = require('../../../../models/program/programParticipant.model');
const { ResourceNotFoundError } = require('../../../../utils/error.util');
const CurrencyService = require('./currency.service');
const AffiliateService = require('./affiliate.service');
const UpsellService = require('./upsell.service');

const {
  PARTICIPANT_PROGRAM_STATUS,
  ACCESS_TYPE,
} = require('../../../program/constants');
const DiscountService = require('./discount.service');

const checkIfUserIsAParticipant = (
  challengeParticipant,
  challenge,
  existingSubscription
) => {
  const isParticipant =
    challengeParticipant?.status ===
    PARTICIPANT_PROGRAM_STATUS.PARTICIPATED;

  if (
    !existingSubscription?.postApprovalProcesses ||
    existingSubscription?.status !== communityEnrolmentStatuses.PENDING
  ) {
    return isParticipant;
  }

  const { postApprovalProcesses } = existingSubscription;

  const challengePurchasedFromApproval = postApprovalProcesses.find(
    (postApprovalProcess) => {
      return (
        postApprovalProcess.type === 'challenge' &&
        postApprovalProcess.origin.toString() === challenge._id.toString()
      );
    }
  );

  return isParticipant || challengePurchasedFromApproval != null;
};

exports.retrieveChallengeInfo = async ({
  item,
  ip,
  community,
  learnerObjectId,
  existingSubscription,
  affiliate,
  affiliateCode,
  upsell: upsellInfo = undefined,
  memberInfo,
  paymentProvider,
  paymentBackendRpc,
}) => {
  let upsell = upsellInfo;

  const { entityId: challengeObjectId, selectedAmount } = item;

  const { code: communityCode, _id: communityObjectId } = community;

  const [entityInfo, discount, challengeParticipant, affiliateProduct] =
    await Promise.all([
      ProgramModel.findOne({
        _id: new ObjectId(challengeObjectId),
        communityObjectId,
      }).lean(),
      DiscountService.retrieveDiscountInfo(item, communityCode),
      ProgramParticipantModel.findOne({
        communityObjectId,
        programObjectId: challengeObjectId,
        learnerObjectId,
        status: { $ne: PARTICIPANT_PROGRAM_STATUS.SYSTEM_REMOVED },
      }),
      AffiliateService.retrieveAffiliateProducts({
        community,
        item,
        affiliate,
      }),
    ]);

  if (!entityInfo) {
    throw new ResourceNotFoundError('Entity not found');
  }

  if (entityInfo.pricingConfig) {
    entityInfo.amount = entityInfo.pricingConfig.amount;
    entityInfo.currency = entityInfo.pricingConfig.currency;
  }

  const isAlreadyJoined = checkIfUserIsAParticipant(
    challengeParticipant,
    entityInfo,
    existingSubscription
  );

  const isKickedOut =
    challengeParticipant?.status === PARTICIPANT_PROGRAM_STATUS.KICKED_OUT;

  if (entityInfo.amount == null) {
    entityInfo.amount = 0;
  }

  if (entityInfo.templateLibraryId) {
    const templateLibrary =
      await TemplateLibraryService.retrieveTemplateLibraryInfo(
        entityInfo.templateLibraryId
      );
    entityInfo.templateLibrary = templateLibrary;
  }

  const { paymentMethodCountryCode } = memberInfo;

  const priceDetail = await getAddonPriceInLocalCurrency({
    ip,
    addon: entityInfo,
    communityObjectId,
    discount,
    selectedAmount,
    paymentMethodCountryCode,
    paymentProvider,
  });

  const amountInUsd = await CurrencyService.convertAmountToUsd(
    priceDetail.originalCheckoutAmount,
    priceDetail.checkoutCurrency,
    paymentBackendRpc
  );

  if (entityInfo.access === ACCESS_TYPE.FREE) {
    return {
      ...item,
      metadata: {
        entityInfo,
        priceDetail,
        amountInUsd,
        freeChallenge: true,
        isFree: true,
        isAlreadyJoined,
        requirePayment: false,
        challengeParticipant,
        existingSubscription,
        isKickedOut,
        affiliateCode,
        affiliate,
        affiliateProduct,
        upsell,
      },
    };
  }

  const discountedCurrency = priceDetail.checkoutCurrency;
  const discountedAmount = priceDetail.checkoutAmount;
  const discountedAmountInUsd = await CurrencyService.convertAmountToUsd(
    priceDetail.checkoutAmount,
    priceDetail.checkoutCurrency,
    paymentBackendRpc
  );

  const isFlexiblePricing = PricingService.isFlexiblePricing(entityInfo);

  const isFullyDiscounted =
    (discount?.isFullyDiscounted ?? false) ||
    (isFlexiblePricing &&
      priceDetail.checkoutAmount === 0 &&
      selectedAmount != null);

  delete entityInfo._version;

  if (!upsell) {
    upsell = await UpsellService.retrieveUpsellFromDiscount({
      discount,
      learnerObjectId,
    });
  }

  return {
    ...item,
    metadata: {
      entityInfo,
      discount,
      priceDetail,
      amountInUsd,
      discountedAmount,
      discountedCurrency,
      discountedAmountInUsd,
      freeChallenge: false,
      isFree: isFullyDiscounted,
      isFlexiblePricing,
      isAlreadyJoined,
      requirePayment: !isFullyDiscounted && !isAlreadyJoined,
      challengeParticipant,
      existingSubscription,
      isKickedOut,
      affiliateCode,
      affiliate,
      affiliateProduct,
      upsell,
    },
  };
};

exports.retrieveChallengeInfoForDiscount = async ({
  item,
  ip,
  community,
  upsell: upsellInfo = undefined,
  paymentBackendRpc,
  memberInfo,
  paymentProvider,
}) => {
  let upsell = upsellInfo;

  const { entityId: challengeObjectId, selectedAmount } = item;

  const { code: communityCode, _id: communityObjectId } = community;

  const [entityInfo, discount] = await Promise.all([
    ProgramModel.findOne({
      _id: new ObjectId(challengeObjectId),
      communityObjectId,
    }).lean(),
    DiscountService.retrieveDiscountInfo(item, communityCode),
  ]);

  if (!entityInfo) {
    throw new ResourceNotFoundError('Entity not found');
  }

  if (entityInfo.pricingConfig) {
    entityInfo.amount = entityInfo.pricingConfig.amount;
    entityInfo.currency = entityInfo.pricingConfig.currency;
  }

  if (entityInfo.amount == null) {
    entityInfo.amount = 0;
  }

  const { paymentMethodCountryCode } = memberInfo;

  const priceDetail = await getAddonPriceInLocalCurrency({
    ip,
    addon: entityInfo,
    communityObjectId,
    discount,
    selectedAmount,
    paymentMethodCountryCode,
    paymentProvider,
  });

  const amountInUsd = await CurrencyService.convertAmountToUsd(
    priceDetail.originalCheckoutAmount,
    priceDetail.checkoutCurrency,
    paymentBackendRpc
  );

  if (entityInfo.access === ACCESS_TYPE.FREE) {
    return {
      ...item,
      metadata: {
        entityInfo,
        priceDetail,
        amountInUsd,
        freeChallenge: true,
        isFree: true,
        requirePayment: false,
      },
    };
  }

  const discountedCurrency = priceDetail.checkoutCurrency;
  const discountedAmount = priceDetail.checkoutAmount;
  const discountedAmountInUsd = await CurrencyService.convertAmountToUsd(
    priceDetail.checkoutAmount,
    priceDetail.checkoutCurrency,
    paymentBackendRpc
  );

  const isFullyDiscounted = discount?.isFullyDiscounted ?? false;
  delete entityInfo._version;

  if (!upsell) {
    upsell = await UpsellService.retrieveUpsellFromDiscount({
      discount,
      learnerObjectId: null,
    });
  }

  return {
    ...item,
    metadata: {
      entityInfo,
      discount,
      priceDetail,
      amountInUsd,
      discountedAmount,
      discountedCurrency,
      discountedAmountInUsd,
      freeChallenge: false,
      isFree: isFullyDiscounted,
    },
  };
};
