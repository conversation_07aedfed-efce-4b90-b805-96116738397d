const CommonService = require('../../common');
const CommunityService = require('./community.service');
const SubscriptionService = require('./subscription.service');
const EventService = require('./event.service');
const FolderService = require('./folder.service');
const SessionService = require('./session.service');
const LearnerService = require('./learner.service');
const CountryService = require('./country.service');
const ChallengeService = require('./challenges.service');
const AffiliateService = require('./affiliate.service');
const UpsellService = require('./upsell.service');
const planService = require('./plan.service');
const ZeroLinkService = require('./zeroLink.service');
const {
  retrieveCommunityReferralDetails,
} = require('../../../plan/plan.service');
const {
  PURCHASE_TYPE,
  PAYMENT_PROVIDER,
} = require('../../../../constants/common');
const CommunitySubscriptionsModel = require('../../../../communitiesAPI/models/communitySubscriptions.model');
const {
  retrievePaymentProvider,
} = require('../../../../utils/paymentProvider.util');
const { WalletService } = require('../../../wallet');

async function retrieveItemsWithMetadata({
  items,
  ip,
  community,
  learnerObjectId,
  existingSubscription,
  affiliate,
  affiliateCode,
  upsell: upsellInfo,
  memberInfo,
  paymentProvider,
  paymentBackendRpc,
}) {
  const itemsWithMetadata = await Promise.all(
    items.map(async (item) => {
      const { type } = item;

      const upsell = UpsellService.retrieveUpsellIfBelongsToEntity({
        upsell: upsellInfo,
        item,
        community,
      });
      // eslint-disable-next-line no-param-reassign
      item.upsell = upsell;

      if (type === PURCHASE_TYPE.SUBSCRIPTION) {
        return SubscriptionService.retrieveSubscriptionInfo({
          item,
          community,
          learnerObjectId,
          existingSubscription,
          affiliate,
          affiliateCode,
          upsell,
          isSignup: true,
          memberInfo,
          paymentProvider,
          paymentBackendRpc,
        });
      }

      if (type === PURCHASE_TYPE.FOLDER) {
        return FolderService.retrieveFolderInfo({
          item,
          ip,
          community,
          learnerObjectId,
          existingSubscription,
          affiliate,
          affiliateCode,
          upsell,
          memberInfo,
          paymentProvider,
          paymentBackendRpc,
        });
      }

      if (type === PURCHASE_TYPE.EVENT) {
        return EventService.retrieveEventInfo({
          item,
          ip,
          community,
          learnerObjectId,
          existingSubscription,
          affiliate,
          affiliateCode,
          upsell,
          memberInfo,
          paymentProvider,
          paymentBackendRpc,
        });
      }

      if (type === PURCHASE_TYPE.SESSION) {
        return SessionService.retrieveSessionInfo({
          item,
          ip,
          community,
          learnerObjectId,
          existingSubscription,
          affiliate,
          affiliateCode,
          upsell,
          memberInfo,
          paymentProvider,
          paymentBackendRpc,
        });
      }

      if (type === PURCHASE_TYPE.CHALLENGE) {
        return ChallengeService.retrieveChallengeInfo({
          item,
          ip,
          community,
          learnerObjectId,
          existingSubscription,
          affiliate,
          affiliateCode,
          upsell,
          memberInfo,
          paymentProvider,
          paymentBackendRpc,
        });
      }

      if (type === PURCHASE_TYPE.ZERO_LINK) {
        return ZeroLinkService.retrieveZeroLinkInfo({
          item,
          ip,
          community,
          existingSubscription,
          memberInfo,
          paymentProvider,
          paymentBackendRpc,
        });
      }
    })
  );

  return itemsWithMetadata;
}

async function retrieveItemsWithMetadataForDiscount({
  items,
  ip,
  community,
  upsell: upsellInfo,
  existingSubscription,
  memberInfo,
  paymentProvider,
  paymentBackendRpc,
}) {
  const itemsWithMetadata = await Promise.all(
    items.map(async (item) => {
      const { type } = item;

      const upsell = UpsellService.retrieveUpsellIfBelongsToEntity({
        upsell: upsellInfo,
        item,
        community,
      });
      // eslint-disable-next-line no-param-reassign
      item.upsell = upsell;

      if (type === PURCHASE_TYPE.SUBSCRIPTION) {
        return SubscriptionService.retrieveSubscriptionInfoForDiscount({
          item,
          community,
          upsell,
          existingSubscription,
          memberInfo,
          paymentProvider,
          paymentBackendRpc,
        });
      }

      if (type === PURCHASE_TYPE.FOLDER) {
        return FolderService.retrieveFolderInfoForDiscount({
          item,
          ip,
          community,
          upsell,
          memberInfo,
          paymentProvider,
          paymentBackendRpc,
        });
      }

      if (type === PURCHASE_TYPE.EVENT) {
        return EventService.retrieveEventInfoForDiscount({
          item,
          ip,
          community,
          upsell,
          memberInfo,
          paymentProvider,
          paymentBackendRpc,
        });
      }

      if (type === PURCHASE_TYPE.SESSION) {
        return SessionService.retrieveSessionInfoForDiscount({
          item,
          ip,
          community,
          upsell,
          memberInfo,
          paymentProvider,
          paymentBackendRpc,
        });
      }

      if (type === PURCHASE_TYPE.CHALLENGE) {
        return ChallengeService.retrieveChallengeInfoForDiscount({
          item,
          ip,
          community,
          upsell,
          memberInfo,
          paymentProvider,
          paymentBackendRpc,
        });
      }
    })
  );

  return itemsWithMetadata;
}

async function retrieveItemsWithMetadataForPlan({
  items,
  countryInfo,
  community,
  paymentBackendRpc,
  communityReferralCode,
}) {
  const itemsWithMetadata = await Promise.all(
    items.map(async (item) => {
      const itemWithMetadata = await planService.retrievePlan({
        item,
        community,
        countryInfo,
        paymentBackendRpc,
      });

      if (communityReferralCode) {
        const { referrerCommunity, referralRewardTemplate, plan } =
          await retrieveCommunityReferralDetails({
            communityReferralCode,
            planType: item.entityType,
            refereeCommunityObjectId: community._id,
          });
        itemWithMetadata.referralDetails = {
          communityReferralCode,
          planObjectId: plan._id,
          referralRewardTemplateObjectId: referralRewardTemplate._id,
          referrerCommunityObjectId: referrerCommunity._id,
        };
      }

      return itemWithMetadata;
    })
  );

  return itemsWithMetadata;
}

exports.retrievePayloadInformation = async ({
  communityCode,
  items,
  ip,
  memberInfo,
  learnerObjectId,
  signupToken,
  affiliateCode,
  upsellIdentityCode,
  paymentProvider,
  paymentBackendRpc,
}) => {
  const [initialCommunity, countryInfo, existingSubscription, upsell] =
    await Promise.all([
      CommunityService.retrieveCommunityInfo(communityCode),
      CountryService.retrieveCountryInfo(ip),
      CommonService.SubscriptionService.retrieveExistingSubscription(
        learnerObjectId,
        communityCode
      ),
      UpsellService.retrieveUpsell({
        upsellIdentityCode,
        learnerObjectId,
      }),
    ]);

  const affiliate = await AffiliateService.retrieveAffiliate({
    community: initialCommunity,
    affiliateCode,
    learnerObjectId,
  });

  // One time subscription do not need to go through approval again
  const updatedCommunity =
    CommunityService.updateCommunityForNonRecurringSubscriptionRenewal(
      initialCommunity,
      existingSubscription
    );

  // Temp fix to hardcode the payment provider here,
  // BECAUSE FE CANNOT PASS CORRECT PAYMENT PROVIDER TO BACKEND
  if (paymentProvider === PAYMENT_PROVIDER.STRIPE) {
    // eslint-disable-next-line no-param-reassign
    paymentProvider = await retrievePaymentProvider(
      updatedCommunity.payment_methods
    );
  }

  const [itemsWithMetadata, learner] = await Promise.all([
    retrieveItemsWithMetadata({
      items,
      ip,
      community: updatedCommunity,
      learnerObjectId,
      existingSubscription,
      affiliate,
      affiliateCode,
      upsell,
      memberInfo,
      paymentProvider,
      paymentBackendRpc,
    }),
    LearnerService.retrieveLearnerInfo({ memberInfo, learnerObjectId }),
  ]);

  const decodedSignupToken =
    CommonService.SignupTokenService.decodeSignupToken(signupToken);

  return {
    community: updatedCommunity,
    items: itemsWithMetadata,
    learner,
    countryInfo,
    decodedSignupToken,
    existingSubscription,
    affiliate,
    paymentProvider,
  };
};

exports.retrievePayloadInformationForDiscount = async ({
  communityCode,
  items,
  ip,
  upsellIdentityCode,
  paymentBackendRpc,
  memberInfo,
  paymentProvider,
  learnerObjectId,
}) => {
  const [community, countryInfo, upsell, existingSubscription] =
    await Promise.all([
      CommunityService.retrieveCommunityInfo(communityCode),
      CountryService.retrieveCountryInfo(ip),
      UpsellService.retrieveUpsell({
        upsellIdentityCode,
        learnerObjectId,
      }),
      CommunitySubscriptionsModel.findOne({
        communityCode,
        learnerObjectId,
        status: 'Current',
      }),
    ]);

  const itemsWithMetadata = await retrieveItemsWithMetadataForDiscount({
    items,
    ip,
    community,
    upsell,
    existingSubscription,
    memberInfo,
    paymentProvider,
    paymentBackendRpc,
  });

  return {
    community,
    items: itemsWithMetadata,
    countryInfo,
  };
};

exports.retrievePayloadInformationForPlan = async ({
  communityCode,
  items,
  ip,
  learnerObjectId,
  signupToken,
  paymentBackendRpc,
  communityReferralCode,
}) => {
  const [community, countryInfo, existingSubscription, learner] =
    await Promise.all([
      CommunityService.retrieveCommunityInfo(communityCode),
      CountryService.retrieveCountryInfo(ip),
      CommonService.SubscriptionService.retrieveExistingSubscription(
        learnerObjectId,
        communityCode
      ),
      LearnerService.retrieveLearnerInfo({ learnerObjectId }),
    ]);

  const communityCreditBalance = await WalletService.getCreditBalance(
    community._id,
    learnerObjectId
  );

  const itemsWithMetadata = await retrieveItemsWithMetadataForPlan({
    communityReferralCode,
    items,
    countryInfo,
    community,
    paymentBackendRpc,
  });

  const decodedSignupToken =
    CommonService.SignupTokenService.decodeSignupToken(signupToken);

  return {
    communityCreditBalance,
    community,
    items: itemsWithMetadata,
    learner,
    countryInfo,
    decodedSignupToken,
    existingSubscription,
  };
};
