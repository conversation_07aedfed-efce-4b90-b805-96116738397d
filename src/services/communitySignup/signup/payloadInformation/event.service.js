const DiscountService = require('./discount.service');
const CurrencyService = require('./currency.service');
const AffiliateService = require('./affiliate.service');
const { PricingService, TemplateLibraryService } = require('../../common');
const CommunityEventsModel = require('../../../../communitiesAPI/models/communityEvents.model');
const EventAttendeeModel = require('../../../../communitiesAPI/models/eventAttendees.model');
const { ResourceNotFoundError } = require('../../../../utils/error.util');
const {
  getAddonPriceInLocalCurrency,
} = require('../../../../communitiesAPI/services/common/communityAddonPrice.service');
const {
  COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES,
  COMMUNITY_EVENT_ACCESS_TYPES,
} = require('../../../../communitiesAPI/constants');
const {
  communityEnrolmentStatuses,
} = require('../../../../constants/common');
const { ApplicationService } = require('../../common');
const UpsellService = require('./upsell.service');

async function retrieveEventAttendee(learnerObjectId, eventObjectId) {
  const eventAttendee = await EventAttendeeModel.findOne({
    eventObjectId,
    learnerObjectId,
  }).lean();

  return eventAttendee;
}

function checkIfUserRsvpToEvent(
  eventAttendee,
  eventObjectId,
  existingSubscription
) {
  const isRegistered =
    eventAttendee?.status ===
      COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.GOING ||
    eventAttendee?.status ===
      COMMUNITY_EVENT_ATTENDEE_STATUS_TYPES.PENDING;

  if (
    !existingSubscription?.postApprovalProcesses ||
    existingSubscription?.status !== communityEnrolmentStatuses.PENDING
  ) {
    return isRegistered;
  }

  const { postApprovalProcesses } = existingSubscription;

  const eventPurchasedFromApproval = postApprovalProcesses.find(
    (postApprovalProcess) => {
      return (
        postApprovalProcess.type === 'event' &&
        postApprovalProcess.origin.toString() === eventObjectId.toString()
      );
    }
  );

  return isRegistered || eventPurchasedFromApproval != null;
}

exports.retrieveEventInfo = async ({
  item,
  ip,
  community,
  learnerObjectId,
  existingSubscription,
  affiliate,
  affiliateCode,
  upsell: upsellInfo = undefined,
  memberInfo,
  paymentProvider,
  paymentBackendRpc,
}) => {
  let upsell = upsellInfo;

  const { entityId: eventObjectId, quantity = 1, selectedAmount } = item;
  const { code: communityCode, _id: communityObjectId } = community;

  const [entityInfo, discount, eventAttendee, affiliateProduct] =
    await Promise.all([
      CommunityEventsModel.findById(eventObjectId).lean(),
      DiscountService.retrieveDiscountInfo(item, communityCode),
      retrieveEventAttendee(learnerObjectId, eventObjectId),
      AffiliateService.retrieveAffiliateProducts({
        community,
        item,
        affiliate,
      }),
    ]);

  if (!entityInfo) {
    throw new ResourceNotFoundError('Entity not found');
  }

  const isRegistered = checkIfUserRsvpToEvent(
    eventAttendee,
    eventObjectId,
    existingSubscription
  );

  if (entityInfo.amount == null) {
    entityInfo.amount = 0;
  }

  if (entityInfo.templateLibraryId) {
    const templateLibrary =
      await TemplateLibraryService.retrieveTemplateLibraryInfo(
        entityInfo.templateLibraryId
      );
    entityInfo.templateLibrary = templateLibrary;
  }

  const { paymentMethodCountryCode } = memberInfo;

  const priceDetail = await getAddonPriceInLocalCurrency({
    ip,
    addon: entityInfo,
    communityObjectId,
    discount,
    quantity,
    selectedAmount,
    paymentMethodCountryCode,
    paymentProvider,
  });

  const amountInUsd = await CurrencyService.convertAmountToUsd(
    priceDetail.originalCheckoutAmount,
    priceDetail.checkoutCurrency,
    paymentBackendRpc
  );

  const requireApplication =
    ApplicationService.checkIfRequireApplication(entityInfo);

  if (entityInfo.access === COMMUNITY_EVENT_ACCESS_TYPES.FREE) {
    return {
      ...item,
      metadata: {
        entityInfo,
        priceDetail,
        amountInUsd,
        freeEvent: true,
        isFree: true,
        isRegistered,
        requirePayment: false,
        requireApplication,
        eventAttendee,
        existingSubscription,
        affiliateCode,
        affiliate,
        affiliateProduct,
        upsell,
      },
    };
  }

  const discountedCurrency = priceDetail.checkoutCurrency;
  const discountedAmount = priceDetail.checkoutAmount;
  const discountedAmountInUsd = await CurrencyService.convertAmountToUsd(
    priceDetail.checkoutAmount,
    priceDetail.checkoutCurrency,
    paymentBackendRpc
  );

  const isFlexiblePricing = PricingService.isFlexiblePricing(entityInfo);

  const isFullyDiscounted =
    (discount?.isFullyDiscounted ?? false) ||
    (isFlexiblePricing &&
      priceDetail.checkoutAmount === 0 &&
      selectedAmount != null);

  delete entityInfo._version;

  if (!upsell) {
    upsell = await UpsellService.retrieveUpsellFromDiscount({
      discount,
      learnerObjectId,
    });
  }

  return {
    ...item,
    metadata: {
      entityInfo,
      discount,
      priceDetail,
      amountInUsd,
      discountedAmount,
      discountedCurrency,
      discountedAmountInUsd,
      freeEvent: false,
      isFree: isFullyDiscounted,
      isFlexiblePricing,
      isRegistered,
      requirePayment: !isFullyDiscounted && !isRegistered,
      requireApplication,
      eventAttendee,
      existingSubscription,
      affiliateCode,
      affiliate,
      affiliateProduct,
      upsell,
    },
  };
};

exports.retrieveEventInfoForDiscount = async ({
  item,
  ip,
  community,
  upsell: upsellInfo = undefined,
  memberInfo,
  paymentProvider,
  paymentBackendRpc,
}) => {
  let upsell = upsellInfo;

  const { entityId: eventObjectId, quantity = 1, selectedAmount } = item;
  const { code: communityCode, _id: communityObjectId } = community;

  const [entityInfo, discount] = await Promise.all([
    CommunityEventsModel.findById(eventObjectId).lean(),
    DiscountService.retrieveDiscountInfo(item, communityCode),
  ]);

  if (!entityInfo) {
    throw new ResourceNotFoundError('Entity not found');
  }

  if (entityInfo.amount == null) {
    entityInfo.amount = 0;
  }

  const { paymentMethodCountryCode } = memberInfo;

  const priceDetail = await getAddonPriceInLocalCurrency({
    ip,
    addon: entityInfo,
    communityObjectId,
    discount,
    quantity,
    selectedAmount,
    paymentMethodCountryCode,
    paymentProvider,
  });

  const amountInUsd = await CurrencyService.convertAmountToUsd(
    priceDetail.originalCheckoutAmount,
    priceDetail.checkoutCurrency,
    paymentBackendRpc
  );

  if (entityInfo.access === COMMUNITY_EVENT_ACCESS_TYPES.FREE) {
    return {
      ...item,
      metadata: {
        entityInfo,
        priceDetail,
        amountInUsd,
        freeEvent: true,
        isFree: true,
        requirePayment: false,
      },
    };
  }

  const discountedCurrency = priceDetail.checkoutCurrency;
  const discountedAmount = priceDetail.checkoutAmount;
  const discountedAmountInUsd = await CurrencyService.convertAmountToUsd(
    priceDetail.checkoutAmount,
    priceDetail.checkoutCurrency,
    paymentBackendRpc
  );

  const isFullyDiscounted = discount?.isFullyDiscounted ?? false;

  delete entityInfo._version;

  if (!upsell) {
    upsell = await UpsellService.retrieveUpsellFromDiscount({
      discount,
      learnerObjectId: null,
    });
  }

  return {
    ...item,
    metadata: {
      entityInfo,
      discount,
      priceDetail,
      amountInUsd,
      discountedAmount,
      discountedCurrency,
      discountedAmountInUsd,
      freeEvent: false,
      isFree: isFullyDiscounted,
    },
  };
};
