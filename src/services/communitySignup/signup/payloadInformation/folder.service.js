const CommunityFoldersModel = require('../../../../communitiesAPI/models/communityFolders.model');
const DiscountService = require('./discount.service');
const CurrencyService = require('./currency.service');
const AffiliateService = require('./affiliate.service');
const { PricingService, TemplateLibraryService } = require('../../common');
const { ResourceNotFoundError } = require('../../../../utils/error.util');
const {
  getAddonPriceInLocalCurrency,
} = require('../../../../communitiesAPI/services/common/communityAddonPrice.service');
const FolderPurchaseModel = require('../../../../communitiesAPI/models/communityFolderPurchases.model');
const {
  communityEnrolmentStatuses,
} = require('../../../../constants/common');
const {
  COMMUNITY_FOLDER_PURCHASE_TYPES,
} = require('../../../../communitiesAPI/constants');
const UpsellService = require('./upsell.service');

async function checkIfFolderIsPurchased(
  folderObjectId,
  learnerObjectId,
  existingSubscription
) {
  const isFolderPurchased = !!(await FolderPurchaseModel.findOne({
    folderObjectId,
    learnerObjectId,
  }).lean());

  if (
    !existingSubscription?.postApprovalProcesses ||
    existingSubscription?.status !== communityEnrolmentStatuses.PENDING
  ) {
    return isFolderPurchased;
  }

  const { postApprovalProcesses } = existingSubscription;

  const folderPurchasedFromApproval = postApprovalProcesses.find(
    (postApprovalProcess) => {
      return (
        postApprovalProcess.type === 'folder' &&
        postApprovalProcess.origin.toString() === folderObjectId.toString()
      );
    }
  );

  return isFolderPurchased || folderPurchasedFromApproval != null;
}

exports.retrieveFolderInfo = async ({
  item,
  ip,
  community,
  learnerObjectId,
  existingSubscription,
  affiliate,
  affiliateCode,
  upsell: upsellInfo = undefined,
  memberInfo,
  paymentProvider,
  paymentBackendRpc,
}) => {
  let upsell = upsellInfo;

  const { entityId: folderObjectId, selectedAmount } = item;
  const { _id: communityObjectId, code: communityCode } = community;

  const [entityInfo, discount, isFolderPurchased, affiliateProduct] =
    await Promise.all([
      CommunityFoldersModel.findById(folderObjectId).lean(),
      DiscountService.retrieveDiscountInfo(item, communityCode),
      checkIfFolderIsPurchased(
        folderObjectId,
        learnerObjectId,
        existingSubscription
      ),
      AffiliateService.retrieveAffiliateProducts({
        community,
        item,
        affiliate,
      }),
    ]);

  if (!entityInfo) {
    throw new ResourceNotFoundError('Entity not found');
  }

  if (entityInfo.amount == null) {
    entityInfo.amount = 0;
  }

  if (entityInfo.templateLibraryId) {
    const templateLibrary =
      await TemplateLibraryService.retrieveTemplateLibraryInfo(
        entityInfo.templateLibraryId
      );
    entityInfo.templateLibrary = templateLibrary;
  }

  const { paymentMethodCountryCode } = memberInfo;

  const priceDetail = await getAddonPriceInLocalCurrency({
    ip,
    addon: entityInfo,
    communityObjectId,
    discount,
    selectedAmount,
    paymentMethodCountryCode,
    paymentProvider,
  });

  const amountInUsd = await CurrencyService.convertAmountToUsd(
    priceDetail.originalCheckoutAmount,
    priceDetail.checkoutCurrency,
    paymentBackendRpc
  );

  if (entityInfo.access === COMMUNITY_FOLDER_PURCHASE_TYPES.FREE) {
    return {
      ...item,
      metadata: {
        entityInfo,
        priceDetail,
        amountInUsd,
        freeFolder: true,
        isFree: true,
        isFolderPurchased: true,
        existingSubscription,
        affiliateCode,
        affiliate,
        affiliateProduct,
        upsell,
      },
    };
  }

  const discountedCurrency = priceDetail.checkoutCurrency;
  const discountedAmount = priceDetail.checkoutAmount;
  const discountedAmountInUsd = await CurrencyService.convertAmountToUsd(
    priceDetail.checkoutAmount,
    priceDetail.checkoutCurrency,
    paymentBackendRpc
  );

  const isFlexiblePricing = PricingService.isFlexiblePricing(entityInfo);

  const isFullyDiscounted =
    (discount?.isFullyDiscounted ?? false) ||
    (isFlexiblePricing &&
      priceDetail.checkoutAmount === 0 &&
      selectedAmount != null);

  delete entityInfo.__v;

  if (!upsell) {
    upsell = await UpsellService.retrieveUpsellFromDiscount({
      discount,
      learnerObjectId,
    });
  }

  return {
    ...item,
    metadata: {
      entityInfo,
      discount,
      priceDetail,
      amountInUsd,
      discountedAmount,
      discountedCurrency,
      discountedAmountInUsd,
      freeFolder: false,
      isFree: isFullyDiscounted,
      isFlexiblePricing,
      isFolderPurchased,
      requirePayment: !isFullyDiscounted && !isFolderPurchased,
      existingSubscription,
      affiliateCode,
      affiliate,
      affiliateProduct,
      upsell,
    },
  };
};

exports.retrieveFolderInfoForDiscount = async ({
  item,
  ip,
  community,
  upsell: upsellInfo = undefined,
  memberInfo,
  paymentProvider,
  paymentBackendRpc,
}) => {
  let upsell = upsellInfo;

  const { entityId: folderObjectId, selectedAmount } = item;
  const { _id: communityObjectId, code: communityCode } = community;

  const [entityInfo, discount] = await Promise.all([
    CommunityFoldersModel.findById(folderObjectId).lean(),
    DiscountService.retrieveDiscountInfo(item, communityCode),
  ]);

  if (!entityInfo) {
    throw new ResourceNotFoundError('Entity not found');
  }

  if (entityInfo.amount == null) {
    entityInfo.amount = 0;
  }

  const { paymentMethodCountryCode } = memberInfo;

  const priceDetail = await getAddonPriceInLocalCurrency({
    ip,
    addon: entityInfo,
    communityObjectId,
    discount,
    selectedAmount,
    paymentMethodCountryCode,
    paymentProvider,
  });

  const amountInUsd = await CurrencyService.convertAmountToUsd(
    priceDetail.originalCheckoutAmount,
    priceDetail.checkoutCurrency,
    paymentBackendRpc
  );

  if (entityInfo.access === COMMUNITY_FOLDER_PURCHASE_TYPES.FREE) {
    return {
      ...item,
      metadata: {
        entityInfo,
        priceDetail,
        amountInUsd,
        freeFolder: true,
        isFree: true,
        isFolderPurchased: true,
      },
    };
  }

  const discountedCurrency = priceDetail.checkoutCurrency;
  const discountedAmount = priceDetail.checkoutAmount;
  const discountedAmountInUsd = await CurrencyService.convertAmountToUsd(
    priceDetail.checkoutAmount,
    priceDetail.checkoutCurrency,
    paymentBackendRpc
  );

  const isFullyDiscounted = discount?.isFullyDiscounted ?? false;

  delete entityInfo.__v;

  if (!upsell) {
    upsell = await UpsellService.retrieveUpsellFromDiscount({
      discount,
      learnerObjectId: null,
    });
  }

  return {
    ...item,
    metadata: {
      entityInfo,
      discount,
      priceDetail,
      amountInUsd,
      discountedAmount,
      discountedCurrency,
      discountedAmountInUsd,
      freeFolder: false,
      isFree: isFullyDiscounted,
    },
  };
};
