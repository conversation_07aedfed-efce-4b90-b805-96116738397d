const DiscountService = require('./discount.service');
const CurrencyService = require('./currency.service');
const AffiliateService = require('./affiliate.service');
const { PricingService, TemplateLibraryService } = require('../../common');
const CommunityFoldersModel = require('../../../../communitiesAPI/models/communityFolders.model');
const SessionAttendeesModel = require('../../../../models/oneOnOneSessions/sessionAttendees.model');
const {
  ResourceNotFoundError,
  ToUserError,
} = require('../../../../utils/error.util');
const {
  getAddonPriceInLocalCurrency,
} = require('../../../../communitiesAPI/services/common/communityAddonPrice.service');
const {
  COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES,
} = require('../../../../communitiesAPI/constants');
const {
  communityEnrolmentStatuses,
  DEFAULT_CURRENCY,
  POST_APPROVAL_PROCESS_TYPE,
} = require('../../../../constants/common');
const { ACCESS_TYPE } = require('../../../program/constants');
const { ApplicationService } = require('../../common');
const {
  validateSlotIsNotBooked,
} = require('../../../calendar/manage.service');
const { PRODUCT_ERROR } = require('../../../../constants/errorCode');
const UpsellService = require('./upsell.service');

async function retrieveSessionAttendee(
  learnerObjectId,
  sessionObjectId,
  sessionStartTime,
  sessionEndTime
) {
  const sessionAttendee = await SessionAttendeesModel.findOne({
    sessionObjectId,
    attendeeLearnerObjectId: learnerObjectId,
    sessionStartTime,
    sessionEndTime,
    isPending: false,
  }).lean();

  return sessionAttendee;
}

function checkIfSessionIsBooked(
  sessionAttendee,
  sessionObjectId,
  existingSubscription
) {
  const isBooked = sessionAttendee
    ? sessionAttendee.status !==
      COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES.CANCELLED
    : false;

  if (
    !existingSubscription?.postApprovalProcesses ||
    existingSubscription?.status !== communityEnrolmentStatuses.PENDING
  ) {
    return isBooked;
  }

  const { postApprovalProcesses } = existingSubscription;

  const sessionBookedFromApproval = postApprovalProcesses.find(
    (postApprovalProcess) => {
      return (
        postApprovalProcess.type === POST_APPROVAL_PROCESS_TYPE.SESSION &&
        postApprovalProcess.origin.toString() ===
          sessionObjectId.toString()
      );
    }
  );

  return isBooked || sessionBookedFromApproval != null;
}

exports.retrieveSessionInfo = async ({
  item,
  ip,
  community,
  learnerObjectId,
  existingSubscription,
  affiliate,
  affiliateCode,
  upsell: upsellInfo = undefined,
  memberInfo,
  paymentProvider,
  paymentBackendRpc,
}) => {
  let upsell = upsellInfo;

  const {
    entityId: sessionObjectId,
    sessionStartTime,
    sessionEndTime,
    selectedAmount,
  } = item;
  const { code: communityCode, _id: communityObjectId } = community;

  const [entityInfo, discount, sessionAttendee, affiliateProduct] =
    await Promise.all([
      CommunityFoldersModel.findById(sessionObjectId).lean(),
      DiscountService.retrieveDiscountInfo(item, communityCode),
      retrieveSessionAttendee(
        learnerObjectId,
        sessionObjectId,
        sessionStartTime,
        sessionEndTime
      ),
      AffiliateService.retrieveAffiliateProducts({
        community,
        item,
        affiliate,
      }),
    ]);

  if (!entityInfo) {
    throw new ResourceNotFoundError('Entity not found');
  }

  const isBooked = checkIfSessionIsBooked(
    sessionAttendee,
    sessionObjectId,
    existingSubscription
  );

  if (!isBooked) {
    const isOccupied = await validateSlotIsNotBooked(
      entityInfo.hostInfo?.hostLearnerObjectId,
      sessionStartTime,
      sessionEndTime
    );
    if (isOccupied) {
      throw new ToUserError(
        'Session slot is already booked',
        PRODUCT_ERROR.ALREADY_BOOKED
      );
    }
  }

  if (entityInfo.amount == null) {
    entityInfo.amount = 0;
  }

  if (entityInfo.templateLibraryId) {
    const templateLibrary =
      await TemplateLibraryService.retrieveTemplateLibraryInfo(
        entityInfo.templateLibraryId
      );
    entityInfo.templateLibrary = templateLibrary;
  }

  const { paymentMethodCountryCode } = memberInfo;

  const priceDetail = await getAddonPriceInLocalCurrency({
    ip,
    addon: entityInfo,
    communityObjectId,
    discount,
    selectedAmount,
    paymentMethodCountryCode,
    paymentProvider,
  });

  const amountInUsd = await CurrencyService.convertAmountToUsd(
    priceDetail.originalCheckoutAmount,
    priceDetail.checkoutCurrency,
    paymentBackendRpc
  );

  const requireApplication =
    ApplicationService.checkIfRequireApplication(entityInfo);

  if (entityInfo.access === ACCESS_TYPE.FREE) {
    return {
      ...item,
      metadata: {
        entityInfo,
        priceDetail,
        amountInUsd,
        freeSession: true,
        isFree: true,
        isBooked,
        requirePayment: false,
        requireApplication,
        sessionAttendee,
        existingSubscription,
        affiliateCode,
        affiliate,
        affiliateProduct,
        upsell,
      },
    };
  }

  const discountedCurrency = priceDetail.checkoutCurrency;
  const discountedAmount = priceDetail.checkoutAmount;
  const discountedAmountInUsd = await CurrencyService.convertAmountToUsd(
    priceDetail.checkoutAmount,
    priceDetail.checkoutCurrency,
    paymentBackendRpc
  );

  const isFlexiblePricing = PricingService.isFlexiblePricing(entityInfo);

  const isFullyDiscounted =
    (discount?.isFullyDiscounted ?? false) ||
    (isFlexiblePricing &&
      priceDetail.checkoutAmount === 0 &&
      selectedAmount != null);

  delete entityInfo._version;

  if (!upsell) {
    upsell = await UpsellService.retrieveUpsellFromDiscount({
      discount,
      learnerObjectId,
    });
  }

  return {
    ...item,
    metadata: {
      entityInfo,
      discount,
      priceDetail,
      amountInUsd,
      discountedAmount,
      discountedCurrency,
      discountedAmountInUsd,
      freeSession: false,
      isFree: isFullyDiscounted,
      isFlexiblePricing,
      isBooked,
      requirePayment: !isFullyDiscounted && !isBooked,
      requireApplication,
      sessionAttendee,
      existingSubscription,
      affiliateCode,
      affiliate,
      affiliateProduct,
      upsell,
    },
  };
};

exports.retrieveSessionInfoForDiscount = async ({
  item,
  ip,
  community,
  upsell: upsellInfo = undefined,
  memberInfo,
  paymentProvider,
  paymentBackendRpc,
}) => {
  let upsell = upsellInfo;

  const {
    entityId: sessionObjectId,
    sessionStartTime,
    sessionEndTime,
    selectedAmount,
  } = item;
  const { code: communityCode, _id: communityObjectId } = community;

  const [entityInfo, discount] = await Promise.all([
    CommunityFoldersModel.findById(sessionObjectId).lean(),
    DiscountService.retrieveDiscountInfo(item, communityCode),
  ]);

  if (!entityInfo) {
    throw new ResourceNotFoundError('Entity not found');
  }

  if (entityInfo.amount == null) {
    entityInfo.amount = 0;
  }

  const { paymentMethodCountryCode } = memberInfo;

  const priceDetail = await getAddonPriceInLocalCurrency({
    ip,
    addon: entityInfo,
    communityObjectId,
    discount,
    selectedAmount,
    paymentMethodCountryCode,
    paymentProvider,
  });

  const amountInUsd = await CurrencyService.convertAmountToUsd(
    priceDetail.originalCheckoutAmount,
    priceDetail.checkoutCurrency,
    paymentBackendRpc
  );

  if (entityInfo.access === ACCESS_TYPE.FREE) {
    return {
      ...item,
      metadata: {
        entityInfo,
        priceDetail,
        amountInUsd,
        freeSession: true,
        isFree: true,
        requirePayment: false,
      },
    };
  }

  const discountedCurrency = priceDetail.checkoutCurrency;
  const discountedAmount = priceDetail.checkoutAmount;
  const discountedAmountInUsd = await CurrencyService.convertAmountToUsd(
    priceDetail.checkoutAmount,
    priceDetail.checkoutCurrency,
    paymentBackendRpc
  );

  const isFullyDiscounted = discount?.isFullyDiscounted ?? false;

  delete entityInfo._version;

  if (!upsell) {
    upsell = await UpsellService.retrieveUpsellFromDiscount({
      discount,
      learnerObjectId: null,
    });
  }

  return {
    ...item,
    metadata: {
      entityInfo,
      discount,
      priceDetail,
      amountInUsd,
      discountedAmount,
      discountedCurrency,
      discountedAmountInUsd,
      freeSession: false,
      isFree: isFullyDiscounted,
    },
  };
};
