const {
  AFFILIATE_STATUS,
  PURCHASE_TYPE,
} = require('../../../../constants/common');
const AffiliateModel = require('../../../../models/affiliate/affiliates.model');
const AffiliateProductModel = require('../../../../models/affiliate/affiliateProducts.model');
const commonService = require('../../common');
const { ParamError } = require('../../../../utils/error.util');
const { PRODUCT_TYPE } = require('../../../product/constants');

exports.retrieveAffiliate = async ({
  community,
  affiliateCode,
  learnerObjectId,
}) => {
  const { _id: communityObjectId, code: communityCode } = community;

  if (!affiliateCode) {
    return null;
  }

  const affiliate = await AffiliateModel.findOne({
    affiliateCode,
    communityObjectId,
    status: AFFILIATE_STATUS.ACTIVE,
  }).lean();

  if (!affiliate) {
    throw new ParamError(`Invalid affiliate code`);
  }

  if (
    learnerObjectId &&
    affiliate.learnerObjectId.toString() === learnerObjectId.toString()
  ) {
    throw new ParamError(
      `The affiliate is not allowed to purchase affiliate product.`
    );
  }

  const existingSubscription =
    await commonService.SubscriptionService.retrieveExistingSubscription(
      affiliate.learnerObjectId,
      communityCode
    );

  if (!existingSubscription) {
    throw new ParamError(`Invalid affiliate code`);
  }

  return affiliate;
};

exports.retrieveAffiliateProducts = async ({
  community,
  item,
  affiliate,
}) => {
  if (!affiliate) {
    return null;
  }

  const { _id: communityObjectId } = community;

  const {
    type: entityType,
    entityId: entityObjectId = communityObjectId,
  } = item;

  const entityTypeList = [entityType];
  if (entityType === PURCHASE_TYPE.FOLDER) {
    entityTypeList.push(
      ...[PRODUCT_TYPE.COURSE, PRODUCT_TYPE.DIGITAL_FILES]
    );
  }

  const affiliateProduct = await AffiliateProductModel.findOne({
    communityObjectId,
    entityType: { $in: entityTypeList },
    entityObjectId,
    isActive: true,
  }).lean();

  if (affiliateProduct) {
    const { customConfig = [] } = affiliate;

    const affiliateProductCustomConfig = customConfig.find(
      (config) =>
        config.affiliateProductObjectId.toString() ===
        affiliateProduct._id.toString()
    );

    affiliateProduct.config.commissionPercentage =
      affiliateProductCustomConfig?.commissionPercentage ??
      affiliateProduct.config.commissionPercentage;
  }

  return affiliateProduct;
};
