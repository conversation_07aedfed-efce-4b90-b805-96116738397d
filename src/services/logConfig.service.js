const LOG_LEVELS = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  verbose: 4,
  debug: 5,
  silly: 6,
};

class LogConfig {
  constructor() {
    this.config = this.loadConfig();
  }

  loadConfig() {
    const env = process.env.NODE_ENV || 'development';

    // Default configurations per environment
    const configs = {
      production: {
        logLevel: process.env.LOG_LEVEL || 'info',
        logFormat: process.env.LOG_FORMAT || 'legacy',
        enableRequestLogging:
          process.env.ENABLE_REQUEST_LOGGING !== 'false',
        enableResponseLogging:
          process.env.ENABLE_RESPONSE_LOGGING === 'true',
        enableBodyLogging: false, // Never log bodies in production
        maxBodyLength: 0,
        sensitiveFields: this.getSensitiveFields(),
        samplingRate: parseFloat(process.env.LOG_SAMPLING_RATE || '1.0'),
        enableStackTrace: false,
        enableSessionTracking:
          process.env.ENABLE_SESSION_TRACKING !== 'false',
      },
      development: {
        logLevel: process.env.LOG_LEVEL || 'debug',
        logFormat: process.env.LOG_FORMAT || 'legacy',
        enableRequestLogging: true,
        enableResponseLogging: true,
        enableBodyLogging: true,
        maxBodyLength: 5000, // 5KB for dev
        sensitiveFields: this.getSensitiveFields(),
        samplingRate: 1.0,
        enableStackTrace: true,
        enableSessionTracking: true,
      },
      test: {
        logLevel: process.env.LOG_LEVEL || 'error',
        logFormat: 'legacy',
        enableRequestLogging: false,
        enableResponseLogging: false,
        enableBodyLogging: false,
        maxBodyLength: 0,
        sensitiveFields: [],
        samplingRate: 0,
        enableStackTrace: false,
        enableSessionTracking: false,
      },
    };

    return configs[env] || configs.development;
  }

  // eslint-disable-next-line class-methods-use-this
  getSensitiveFields() {
    // Fields that should be masked in logs
    // TODO: need to update the list based on actual requirements
    const defaultFields = [
      'password',
      'password2',
      'token',
      'apiKey',
      'api_key',
      'user_id',
      'email',
      'secret',
      'authorization',
      'cookie',
      'ssn',
      'creditCard',
      'credit_card',
      'cvv',
      'pin',
      'accessToken',
      'refreshToken',
      'privateKey',
      'passphrase',
    ];
    // Add custom fields from environment
    const customFields = process.env.LOG_SENSITIVE_FIELDS
      ? process.env.LOG_SENSITIVE_FIELDS.split(',').map((f) => f.trim())
      : [];

    return [...defaultFields, ...customFields];
  }

  shouldLog(level) {
    const currentLevel = LOG_LEVELS[this.config.logLevel];
    const requestedLevel = LOG_LEVELS[level];
    return requestedLevel <= currentLevel;
  }

  shouldSample() {
    return Math.random() <= this.config.samplingRate;
  }

  // Getters for easy access
  get logLevel() {
    return this.config.logLevel;
  }

  get logFormat() {
    return this.config.logFormat;
  }

  get enableRequestLogging() {
    return this.config.enableRequestLogging;
  }

  get enableResponseLogging() {
    return this.config.enableResponseLogging;
  }

  get enableBodyLogging() {
    return this.config.enableBodyLogging;
  }

  get maxBodyLength() {
    return this.config.maxBodyLength;
  }

  get sensitiveFields() {
    return this.config.sensitiveFields;
  }

  get enableStackTrace() {
    return this.config.enableStackTrace;
  }

  get samplingRate() {
    return this.config.samplingRate;
  }

  get enableSessionTracking() {
    return this.config.enableSessionTracking;
  }

  // Dynamic config update (useful for feature flags)
  updateConfig(updates) {
    this.config = { ...this.config, ...updates };
  }

  // Helper method to get current config (for debugging)
  getCurrentConfig() {
    return { ...this.config };
  }
}

// Singleton instance
const logConfig = new LogConfig();

module.exports = logConfig;
