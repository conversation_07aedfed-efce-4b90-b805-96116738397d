const { Schema, model } = require('mongoose');
const { ORDER_STATUS } = require('../../services/plan/constants');

const earningAnalyticsSchema = new Schema(
  {
    quantity: { type: Number, required: true },
    revenueInUsd: { type: Number, required: true },
    revenueInLocalCurrency: { type: Number, required: true },
  },
  { _id: false }
);

const CommunityReferralRewardSchema = new Schema(
  {
    referralRewardTemplateObjectId: {
      type: Schema.Types.ObjectId,
      required: true,
    },
    referrerCommunityObjectId: {
      type: Schema.Types.ObjectId,
      required: true,
    },
    refereeCommunityObjectId: {
      type: Schema.Types.ObjectId,
      required: true,
    },
    refereePlanOrderObjectId: {
      type: Schema.Types.ObjectId,
      required: true,
    },
    currency: {
      type: String,
      required: true,
    },
    earningAnalytics: {
      type: earningAnalyticsSchema,
      required: true,
    },
    planOrderStatus: {
      type: String,
      required: true,
      enum: Object.values(ORDER_STATUS),
    },
    cancelledAt: {
      type: Date,
      required: false,
    },
    planType: {
      type: String,
      required: true,
    },
  },
  {
    collection: 'community_referral_rewards',
    timestamps: true,
  }
);

const CommunityReferralReward = model(
  'CommunityReferralRewardSchema',
  CommunityReferralRewardSchema
);

module.exports = CommunityReferralReward;
