const { Schema, model } = require('mongoose');
const {
  COMMUNITY_REFERRAL_REWARD_PLAN_TYPE,
  ALLOWED_CURRENCIES,
} = require('../../services/communityReferralRewardTemplate/constants');

const multiCurrencySchema = new Schema(
  {
    currency: {
      type: String,
      enum: Object.values(ALLOWED_CURRENCIES),
      required: true,
    },
    amount: { type: Number, required: true },
  },
  { _id: false }
);

const intervalRewardSchema = new Schema(
  {
    'month-1': {
      type: [multiCurrencySchema],
      default: [],
      required: true,
    },
    'year-1': { type: [multiCurrencySchema], default: [], required: true },
  },
  { _id: false }
);

const CommunityReferralRewardTemplateSchema = new Schema(
  {
    name: { type: String, required: true },
    planType: {
      type: String,
      enum: Object.values(COMMUNITY_REFERRAL_REWARD_PLAN_TYPE),
      required: true,
    },
    isDisabled: { type: Boolean, default: true, required: true },
    effectiveTimeStart: { type: Date, default: null },
    effectiveTimeEnd: { type: Date, default: null },
    applyToAll: { type: Boolean, default: true, required: true },
    communityObjectIds: {
      type: [Schema.Types.ObjectId],
      default: [],
      required: true,
    },
    planObjectId: {
      type: Schema.Types.ObjectId,
      ref: 'community_plans',
      required: true,
    },
    isRefereeTrialBillingPlanEnabled: {
      type: Boolean,
      default: false,
      required: true,
    },
    refereeRecurringReward: {
      type: intervalRewardSchema,
      required: true,
    },
    referrerRecurringReward: {
      type: intervalRewardSchema,
      required: true,
    },
    referrerUpfrontReward: {
      type: intervalRewardSchema,
      required: true,
    },
    createdBy: { type: String, required: true },
    editedBy: { type: String, required: true },
  },
  {
    collection: 'community_referral_reward_templates',
    timestamps: true,
  }
);

const CommunityReferralRewardTemplate = model(
  'CommunityReferralRewardTemplateSchema',
  CommunityReferralRewardTemplateSchema
);

module.exports = CommunityReferralRewardTemplate;
