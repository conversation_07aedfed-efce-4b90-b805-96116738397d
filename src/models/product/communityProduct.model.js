const mongoose = require('mongoose');
const ObjectId = require('mongoose').Types.ObjectId;
const { Schema, model } = require('mongoose');
const history = require('../../middleware/mongoose/history.middleware');
const { PRODUCT_STATUS } = require('../../services/product/constants');
const {
  coverMediaItemSchema,
} = require('../common/coverMediaItems.schema');

const earningAnalyticsSchema = new Schema(
  {
    quantity: { type: Number, required: true },
    revenueInUsd: { type: Number, required: true },
    revenueInLocalCurrency: { type: Number, required: true },
  },
  { _id: false }
);

const PricingConfigSchema = new Schema(
  {
    amount: { type: Number, required: false },
    currency: { type: String, required: false },
    priceType: { type: String, required: false },
    minAmount: { type: Number, required: false },
    suggestedAmount: { type: Number, required: false },
  },
  {
    _id: false,
  }
);

const CommunityProductSchema = new Schema(
  {
    communityObjectId: { type: ObjectId, required: true },
    entityObjectId: { type: ObjectId, required: true },
    productType: { type: String, required: true }, // event/digital_product/session/course/challenge
    title: { type: String, required: true },
    status: {
      type: String,
      required: true,
      enum: [
        PRODUCT_STATUS.DRAFT,
        PRODUCT_STATUS.PUBLISHED,
        PRODUCT_STATUS.DELETED,
      ],
    },
    amount: { type: Number, required: false },
    currency: { type: String, required: false },
    coverImg: { type: String, required: true },
    coverMediaItems: {
      type: [coverMediaItemSchema],
      required: false,
    },
    slug: { type: String, required: true },
    pricingConfig: {
      type: PricingConfigSchema,
      required: false,
    },
    priceType: { type: String, required: true },
    earningAnalytics: { type: earningAnalyticsSchema, required: false },
    metadata: { type: Object, required: false }, // product specific metadata
    createdAt: { type: Date, required: true },
    updatedAt: { type: Date, required: true },
  },
  {
    collection: 'community_product',
  }
);

CommunityProductSchema.index(
  {
    entityObjectId: 1,
    productType: 1,
  },
  {
    unique: true,
  }
);

CommunityProductSchema.plugin(history, {
  collection: 'community_product_history',
  mongoose,
});

const CommunityProductModel = model(
  'community_product',
  CommunityProductSchema
);

module.exports = CommunityProductModel;
