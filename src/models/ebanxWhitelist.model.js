const mongoose = require('mongoose');
const { Schema, model } = require('mongoose');
const history = require('../middleware/mongoose/history.middleware');
const { PRODUCT_TYPE } = require('../services/product/constants');

const collectionName = 'ebanx_whitelists';

// Local enum that extends PRODUCT_TYPE to include SUBSCRIPTION for whitelist-only use
const EBANX_PRODUCT_TYPE = {
  ...PRODUCT_TYPE,
  SUBSCRIPTION: 'SUBSCRIPTION',
};

const WHITELIST_STATUS = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
};

const EbanxWhitelistSchema = new Schema(
  {
    productId: {
      type: Schema.Types.ObjectId,
      required: true,
    },
    productType: {
      type: String,
      required: true,
      enum: Object.values(EBANX_PRODUCT_TYPE),
    },
    productLink: { type: String, required: true },
    communityCode: { type: String, required: true },
    status: {
      type: String,
      required: true,
      enum: Object.values(WHITELIST_STATUS),
      default: WHITELIST_STATUS.ACTIVE,
    },
    reason: { type: String, required: true },
    whitelistedBy: { type: String, required: true },
  },
  {
    collection: collectionName,
    timestamps: true,
  }
);

EbanxWhitelistSchema.plugin(history, {
  collection: collectionName + '_history',
  mongoose,
});

EbanxWhitelistSchema.index({ productId: 1 }, { unique: true });

module.exports = model('EbanxWhitelist', EbanxWhitelistSchema);
