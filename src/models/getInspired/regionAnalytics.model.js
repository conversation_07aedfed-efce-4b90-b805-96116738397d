const mongoose = require('mongoose');
const {
  COUNTRY_REGIONS,
  REGION_ANALYTICS_CATEGORY,
} = require('../../constants/common');

const RegionAnalyticsSchema = new mongoose.Schema(
  {
    region: {
      type: String,
      enum: Object.values(COUNTRY_REGIONS),
      required: true,
    },
    category: {
      type: String,
      enum: Object.values(REGION_ANALYTICS_CATEGORY),
      required: true,
    },
    averageMemberCount: { type: Number, required: true },
    averageTxns: { type: Number, required: true },
    averageGMVInUsd: { type: Number, required: true },
  },
  {
    collection: 'region_analytics',
    timestamps: true,
  }
);

const RegionAnalyticsModel = mongoose.model(
  'RegionAnalytics',
  RegionAnalyticsSchema
);

module.exports = RegionAnalyticsModel;
