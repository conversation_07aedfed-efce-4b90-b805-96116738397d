const mongoose = require('mongoose');
const {
  BLACKLISTED_USERS_IDENTIFIER,
} = require('../constants/blacklistedUsers.constant');

const { Schema } = mongoose;

const blacklistedUsersSchema = new Schema(
  {
    identifier: {
      type: String,
      enum: Object.values(BLACKLISTED_USERS_IDENTIFIER),
      required: false,
    },
    value: { type: String, required: true, unique: true, lowercase: true },
    reason: { type: String, required: false },
    createdBy: { type: String, required: false },
    createdAt: { type: Date, default: Date.now },
  },
  { collection: 'blacklisted_users' }
);

module.exports = mongoose.model(
  'BlacklistedUsers',
  blacklistedUsersSchema
);
