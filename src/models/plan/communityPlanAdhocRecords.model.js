const { Schema, model } = require('mongoose');

const communityPlanAdhocRecordsSchema = new Schema(
  {
    entityType: { type: String, required: true },
    communityObjectId: { type: Schema.Types.ObjectId, required: true },
    creatorEmail: { type: String, required: true },
    addedBy: { type: String, required: true },
    notes: { type: String, required: false },
    planObjectId: { type: Schema.Types.ObjectId, required: true },
    status: { type: String, required: true, enum: ['ACTIVE', 'INACTIVE'] },
  },
  {
    collection: 'community_plan_adhoc_records',
    timestamps: true,
  }
);

module.exports = model(
  'community_plan_adhoc_records',
  communityPlanAdhocRecordsSchema
);
