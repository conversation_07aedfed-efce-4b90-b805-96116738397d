const { Schema, model } = require('mongoose');

const translationSchema = new Schema(
  {
    en: { type: String, required: false },
    es_mx: { type: String, required: false },
    pt_br: { type: String, required: false },
    ja: { type: String, required: false },
  },
  {
    _id: false,
  }
);

const userInputSchema = new Schema(
  {
    placeholder: { type: translationSchema, required: true },
    isRequired: { type: Boolean, default: true, required: true },
  },
  {
    _id: false,
  }
);

const planCancellationSchema = new Schema(
  {
    key: { type: String, required: true },
    index: { type: Number, required: true },
    disable: { type: Boolean, required: true },
    label: { type: translationSchema, required: true },
    enableUserInput: { type: Boolean, required: true },
    userInput: { type: userInputSchema, required: false },
  },
  {
    collection: 'plan_cancellation_reasons',
    timestamps: true,
  }
);

module.exports = model(
  'plan_cancellation_reasons',
  planCancellationSchema
);
