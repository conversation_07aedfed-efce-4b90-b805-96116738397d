const mongoose = require('mongoose');

const collectionName = 'learner_socials';

const overviewSchema = new mongoose.Schema(
  {
    isHighQuality: {
      type: Boolean,
    },
    totalFollowersCount: {
      type: String,
    },
  },
  {
    _id: false,
  }
);
const LearnerSocialsSchema = new mongoose.Schema(
  {
    learnerObjectId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
    },
    instagram: {
      type: Object,
    },
    tiktok: {
      type: Object,
    },
    youtube: {
      type: Object,
    },
    linkedin: {
      type: Object,
    },
    x: {
      type: Object,
    },
    facebook: {
      type: Object,
    },
    customWebsite: {
      type: Object,
    },
    gptOverview: {
      type: Object,
    },
    overview: {
      type: overviewSchema,
    },
  },
  {
    timestamps: true,
    collection: collectionName,
  }
);

const LearnerSocialsModel = mongoose.model(
  'LearnerSocials',
  LearnerSocialsSchema
);

module.exports = LearnerSocialsModel;
