const { Schema, model } = require('mongoose');

const paymentMethodPropertySchema = new Schema(
  {
    key: { type: String, required: true },
    isRequired: { type: Boolean, required: true, default: false },
  },
  { _id: false }
);

const paymentMethodSchema = new Schema(
  {
    type: { type: String, required: true },
    subType: { type: String, required: false },
    icon: { type: String, required: false },
    isActive: { type: Boolean, required: true, default: true },
    properties: { type: [paymentMethodPropertySchema], required: false },
  },
  { _id: false }
);

const paymentOptionConfigSchema = new Schema(
  {
    checkoutCurrency: { type: String, required: true },
    checkoutType: {
      type: String,
      required: true,
    },
    isActive: { type: Boolean, required: true, default: true },
    isWhitelistMode: { type: Boolean },
    paymentProvider: { type: String, required: true },
    paymentMethods: {
      type: [paymentMethodSchema],
      required: true,
      default: [],
    },
    publicKey: { type: String, required: false },
  },
  {
    collection: 'payment_option_configs',
    timestamps: true,
  }
);

const PaymentOptionConfigModel = model(
  'paymentOptionConfig',
  paymentOptionConfigSchema
);

module.exports = PaymentOptionConfigModel;
