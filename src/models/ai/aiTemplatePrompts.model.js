const mongoose = require('mongoose');

const { Schema } = mongoose;

const collectionName = 'ai_template_prompts';

const aiModelSchema = new Schema(
  {
    provider: { type: String, required: true },
    model: { type: String, required: true },
  },
  { _id: false }
);

const aiTemplatePromptsSchema = new Schema(
  {
    type: { type: String, required: true },
    prompt: { type: String, required: true },
    source: { type: String, required: false },
    version: { type: Number, required: false },
    isActive: { type: Boolean, required: false },
    aiModel: { type: aiModelSchema, required: false },
  },
  {
    timestamps: true,
    collection: collectionName,
  }
);

module.exports = mongoose.model(
  'AiTemplatePrompts',
  aiTemplatePromptsSchema
);
