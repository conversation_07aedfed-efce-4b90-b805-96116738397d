const { Schema } = require('mongoose');
const mongoose = require('mongoose');
const { ZERO_LINK_STATUS } = require('../../services/zeroLink/constants');
const { PRICE_TYPE } = require('../../constants/common');

const PricingConfigSchema = new Schema(
  {
    priceType: {
      type: String,
      enum: Object.values(PRICE_TYPE),
      required: true,
    },
    minAmount: { type: Number, required: true, default: 0 },
  },
  {
    _id: false,
  }
);

const earningAnalyticsSchema = new mongoose.Schema(
  {
    quantity: { type: Number, default: 0, required: true },
    revenueInUsd: { type: Number, default: 0, required: true },
    revenueInLocalCurrency: { type: Number, default: 0, required: true },
  },
  { _id: false }
);

const zeroLinkSchema = new Schema(
  {
    communityObjectId: { type: Schema.Types.ObjectId, required: true },
    status: {
      type: String,
      required: true,
      enum: ZERO_LINK_STATUS,
      default: ZERO_LINK_STATUS.ACTIVE,
    },
    amount: { type: Number, required: false, default: 0 },
    currency: { type: String, required: true },
    pricingConfig: {
      type: PricingConfigSchema,
      required: true,
    },
    slug: { type: String, required: true },
    coverImg: { type: String, required: false }, // s3 url
    searchTitle: { type: String, required: false },
    title: { type: String, required: false },
    message: { type: String, required: false },
    redirectLink: { type: String, required: false },
    passOnTakeRate: { type: Boolean, default: false },
    passOnPaymentGatewayFee: { type: Boolean, default: false },
    metadata: {
      type: Object,
      required: false,
      default: {},
    },
    earningAnalytics: { type: earningAnalyticsSchema, required: false },
  },
  {
    collection: 'zero_link',
    timestamps: { createdAt: 'createdAt', updatedAt: 'updatedAt' },
  }
);

const ZeroLinkModel = mongoose.model('ZeroLink', zeroLinkSchema);

module.exports = ZeroLinkModel;
