const { Schema, model } = require('mongoose');
const Community = require('../../communitiesAPI/models/community.model');

const CommunityMagicReachStatsSchema = new Schema(
  {
    communityId: {
      type: Schema.Types.ObjectId,
      ref: Community,
      required: true,
    },
    window: { type: String, required: true },
    currentMonth: { type: String, required: false }, // "YYYY-MM" for monthly window queries
    start: { type: Schema.Types.Date, required: false },
    platform: { type: String, required: true },
    totalSent: { type: Number, required: false },
    memberSent: { type: Number, required: false },
    nonMemberSent: { type: Number, required: false },
  },
  {
    collection: 'community_magic_reach_stats',
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'lastModifiedTimeStamp',
    },
  }
);

const CommunityMagicReachStatsModel = model(
  'CommunityMagicReachStats',
  CommunityMagicReachStatsSchema
);

module.exports = CommunityMagicReachStatsModel;
