const { Schema, model } = require('mongoose');

const MetaAdsIntegrationSchema = new Schema(
  {
    adsAccountId: { type: String, required: true },
    pageId: { type: String, required: true },
    pixelId: { type: String, required: true },
    pageTitle: { type: String, required: true },
    pageDescription: { type: String, required: true },
    pageThumbnail: { type: String },
    communityObjectId: {
      type: Schema.Types.ObjectId,
      ref: 'Community',
      required: true,
    },
  },
  {
    collection: 'meta_ads_integrations',
    timestamps: true,
  }
);

const MetaAdsIntegration = model(
  'MetaAdsIntegration',
  MetaAdsIntegrationSchema
);

module.exports = MetaAdsIntegration;
