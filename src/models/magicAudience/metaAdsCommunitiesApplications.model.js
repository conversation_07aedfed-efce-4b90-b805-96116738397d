const { Schema, model } = require('mongoose');

// this model is used for timebeing when we get an approval from facebook then we will remove this model
const MetaAdsCommunitiesApplicationsSchema = new Schema(
  {
    communityObjectId: {
      type: Schema.Types.ObjectId,
      required: true,
    },
  },
  {
    collection: 'meta_ads_communities_applications',
    timestamps: true,
  }
);

const MetaAdsCommunitiesApplications = model(
  'MetaAdsCommunitiesApplications',
  MetaAdsCommunitiesApplicationsSchema
);
module.exports = MetaAdsCommunitiesApplications;
