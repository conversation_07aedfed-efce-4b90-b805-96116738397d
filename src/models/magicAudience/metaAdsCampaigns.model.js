const { Schema, model } = require('mongoose');
const {
  META_ADS_STATUS_LIST,
} = require('../../services/magicAudience/constants');

const MetaAdsCampaignsSchema = new Schema(
  {
    communityObjectId: {
      type: Schema.Types.ObjectId,
      ref: 'Community',
      required: true,
    },
    entityObjectId: {
      type: Schema.Types.ObjectId,
      required: true,
    },
    entityType: {
      type: String,
      required: true,
    },
    fbCampaignId: {
      type: String,
    },
    rejectedReason: {
      type: String,
    },
    campaignName: {
      type: String,
      required: true,
    },
    createdByLearnerObjectId: {
      type: Schema.Types.ObjectId,
      ref: 'LearnerSchema',
      required: true,
    },
    submissionDate: {
      type: Date,
    },
    status: {
      type: String,
      enum: META_ADS_STATUS_LIST,
      required: true,
    },
    totalSpend: {
      type: Number,
    },
    totalImpressions: {
      type: Number,
    },
    totalConvertedEvents: {
      type: Number,
    },
    facebookStatus: { type: String }, // for tracking FB status
    errorReason: { type: String }, // for tracking FB status
    errorFacebookReason: { type: String }, // for tracking FB status
    objective: {
      type: String,
    },
  },
  {
    collection: 'meta_ads_campaigns',
    timestamps: true,
  }
);

const MetaAdsCampaigns = model('MetaAdsCampaigns', MetaAdsCampaignsSchema);
module.exports = MetaAdsCampaigns;
