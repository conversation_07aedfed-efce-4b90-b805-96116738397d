const { Schema, model } = require('mongoose');

const NasioMetaAdsAccountsSchema = new Schema(
  {
    communityObjectId: {
      type: Schema.Types.ObjectId,
      ref: 'Community',
    },
    adsAccountId: { type: String, required: true },
    pixelId: { type: String, required: true },
  },
  {
    collection: 'nasio_meta_ads_accounts',
    timestamps: true,
  }
);

const NasioMetaAdsAccounts = model(
  'NasioMetaAdsAccounts',
  NasioMetaAdsAccountsSchema
);
module.exports = NasioMetaAdsAccounts;
