const { Schema, model } = require('mongoose');
const {
  META_ADS_STATUS_LIST,
} = require('../../services/magicAudience/constants');

const VideoInfoSchema = new Schema(
  {
    videoId: { type: String, required: true },
    videoUrl: { type: String, required: true },
    videoThumbnailUrl: { type: String }, // optional thumbnail URL
  },
  { _id: false } // don’t create an extra _id for each element
);
const MetaAdsObjectsSchema = new Schema(
  {
    adName: { type: String, required: true },
    /* ────────── FB & parent linkage ─────────────────────────────── */
    adSetId: {
      type: Schema.Types.ObjectId,
      ref: 'MetaAdsSets',
      required: true,
    },
    fbAdId: { type: String },
    campaignId: {
      type: Schema.Types.ObjectId,
      ref: 'MetaAdsCampaigns',
      required: true,
    },

    /* ────────── Variant info / status ───────────────────────────── */
    variantNumber: { type: Number, default: 1 }, // keep if you still AB‑label
    adFormat: {
      type: String,
      enum: ['SINGLE_IMAGE', 'SINGLE_VIDEO'],
      required: true,
    },
    status: { type: String, required: true, enum: META_ADS_STATUS_LIST },
    facebookStatus: { type: String }, // for tracking FB status
    errorReason: { type: String }, // for tracking FB status
    errorFacebookReason: { type: String }, // for tracking FB status
    /* ────────── Dynamic‑creative text arrays (1‑5 each) ────────── */
    primaryTexts: { type: [String], required: true },
    headlines: { type: [String], required: true },
    descriptions: { type: [String], required: true },
    linkUrls: { type: [String], required: true },
    /* ────────── Media assets (only one array is used) ───────────── */
    mediaUrls: { type: [String] }, // for SINGLE_IMAGE creatives
    videoInfo: { type: [VideoInfoSchema] }, // for SINGLE_VIDEO creatives
    createdByLearnerObjectId: {
      type: Schema.Types.ObjectId,
      ref: 'Learners',
      required: true,
    },
    communityObjectId: {
      type: Schema.Types.ObjectId,
      ref: 'Communities',
      required: true,
    },
  },
  {
    collection: 'meta_ads_objects',
    timestamps: true,
  }
);
const MetaAdsObjects = model('MetaAdsObjects', MetaAdsObjectsSchema);
module.exports = MetaAdsObjects;
