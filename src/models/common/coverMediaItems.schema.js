/* eslint-disable object-shorthand */
const mongoose = require('mongoose');
const {
  COVER_MEDIA_TYPES,
} = require('../../constants/coverMediaItems.constant');

const coverMediaItemSchema = new mongoose.Schema(
  {
    mediaType: {
      type: String,
      required: true,
      enum: Object.values(COVER_MEDIA_TYPES),
    },
    imgSrc: {
      type: String,
      required: function () {
        return this.mediaType === COVER_MEDIA_TYPES.IMAGE;
      },
    },
    unsplashMetadata: {
      type: Object,
      required: false,
    },
    folderItemId: {
      type: String,
      required: function () {
        return this.mediaType === COVER_MEDIA_TYPES.VIDEO;
      },
    },
  },
  { _id: false }
);

module.exports = {
  coverMediaItemSchema,
};
