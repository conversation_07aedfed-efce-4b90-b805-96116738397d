const { Schema, model } = require('mongoose');

const TopProductsDailySchema = new Schema(
  {
    snapshotDate: {
      type: Date,
      required: true,
    },
    productType: {
      type: String,
      required: true,
    },
    productObjectId: {
      type: Schema.Types.ObjectId,
      required: true,
    },
    communityObjectId: { type: Schema.Types.ObjectId, required: true },
    sales: { type: Number, required: true }, // units sold in last 24 h
    isFree: { type: Boolean, required: true },
    isMXMarket: { type: Boolean, required: true },
  },
  {
    collection: 'top_products_daily',
    timestamps: true,
  }
);

const TopProductsDailyModel = model(
  'TopProductsDaily',
  TopProductsDailySchema
);

module.exports = TopProductsDailyModel;
