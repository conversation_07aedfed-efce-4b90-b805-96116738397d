const { Schema, model } = require('mongoose');

const paymentDetailsSchema = new Schema(
  {
    status: {
      type: String,
      required: true,
    },
    latestUpdatedTime: { type: Date, required: true },
    paymentProvider: { type: String, required: false },
    requestId: { type: String, required: true },
    paymentId: { type: String, required: false },
    failureCode: { type: String, required: false },
    failureReason: { type: String, required: false },
    userPaymentTokenId: { type: String, required: false },
    isDirectCharge: { type: Boolean, required: false },
  },
  { _id: false }
);

const countrySchema = new Schema(
  {
    name: { type: String, required: true },
    code: { type: String, required: true },
  },
  { _id: false }
);

// Campaign = 100 USD
// fee = 10 usd
// walletDeductibleAmount = 40 usd
// checkout amount = 100 + 10 - 40 = 70 USD
const priceDetailsSchema = new Schema(
  {
    requestedAmount: { type: Number, required: true }, // 100 USD
    nasIOFee: { type: Number, required: true, default: 0 }, // 10 USD
    deductibleWalletBalance: { type: Number, required: true, default: 0 }, // 40 USD
    checkoutAmount: { type: Number, required: true }, // 70 USD
  },
  { _id: false }
);

const communityTopupOrderSchema = new Schema(
  {
    communityObjectId: { type: Schema.Types.ObjectId, required: true },
    learnerObjectId: { type: Schema.Types.ObjectId, required: true },
    paymentDetails: { type: paymentDetailsSchema, required: true },
    status: { type: String, required: true },
    topupType: { type: String, required: true },
    entityObjectId: { type: Schema.Types.ObjectId, required: true },
    country: { type: countrySchema, required: true },
    amountInUsd: { type: Number, required: true },
    amountInLocalCurrency: { type: Number, required: true },
    localCurrency: { type: String, required: true, uppercase: true },
    priceDetailsInUsd: {
      type: priceDetailsSchema,
      required: true,
    },
    priceDetailsInLocalCurrency: {
      type: priceDetailsSchema,
      required: true,
    },
  },
  {
    collection: 'community_topup_orders',
    timestamps: true,
  }
);

module.exports = model(
  'community_topup_orders',
  communityTopupOrderSchema
);
