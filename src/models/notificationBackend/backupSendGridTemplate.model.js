const { Schema } = require('mongoose');
const { notificationDbConnection } = require('../../handlers/mongodb');
const { SENDGRID_TEMPLATE_SOURCES } = require('./constants');

const BackupSendgridTemplateSchema = new Schema(
  {
    mailType: { type: String, required: true },
    mainTemplateId: { type: String, required: false },
    backupTemplateId: { type: String, required: false },
    mainTemplateSource: {
      type: String,
      enum: Object.values(SENDGRID_TEMPLATE_SOURCES),
      required: false,
    },
  },
  {
    collection: 'backup_sendgrid_template',
    timestamps: true,
  }
);

module.exports = notificationDbConnection.model(
  'backupSendgridTemplate',
  BackupSendgridTemplateSchema
);
