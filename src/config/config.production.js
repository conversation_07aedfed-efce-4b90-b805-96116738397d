module.exports = {
  CAMPAIGN_NAS_GIFT_AUG_2024_START_DATE:
    process.env.CAMPAIGN_NAS_GIFT_AUG_2024_START_DATE ||
    '2024-08-01T00:00:00.000Z',
  USA_CUSTOM_BASE_PAYOUT_FEE_START_DATE:
    process.env.USA_CUSTOM_BASE_PAYOUT_FEE_START_DATE ||
    '2025-01-15T00:00:00.000Z',
  FEEDBACK_URL:
    process.env.FEEDBACK_URL || 'https://feedback-service.nasacademy.com',
  NOTIFICATION_URL:
    process.env.NOTIFICATION_URL || 'https://notify.nasacademy.com',
  MAIN_WEBSITE_URL:
    process.env.MAIN_WEBSITE_URL || 'https://nasacademy.com',
  NAS_IO_BACKEND_URL:
    process.env.NAS_IO_BACKEND_URL || 'https://web3-api.nas.io',
  NAS_IO_FRONTEND_URL: process.env.NAS_IO_FRONTEND_URL || 'https://nas.io',
  FRONTEND_APP_LINK:
    process.env.FRONTEND_APP_LINK || 'https://nas.io/member', // deprecated
  REROUTE_MEMBER_LINK:
    process.env.REROUTE_MEMBER_LINK || 'https://nas.io/reroute-user',
  REROUTE_EMAIL_LINK:
    process.env.REROUTE_EMAIL_LINK || 'https://nas.io/email-reroute',
  URL_SHORTENER_LINK:
    process.env.URL_SHORTENER_LINK || 'https://community.nasacademy.com',
  MAIN_PAYMENT_BACKEND_URL:
    process.env.MAIN_PAYMENT_BACKEND_URL || 'https://main-cdn.nas.io',
  METAPHI_SDK_ACCOUNT_ID: process.env.METAPHI_SDK_ACCOUNT_ID || 28,
  LEARN_BACKEND_URL:
    process.env.LEARN_BACKEND_URL || 'https://api.nas.io/',
  MOBILE_NOTIFICATION_QUEUE_URL:
    process.env.MOBILE_NOTIFICATION_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/************/mobile-notificaiton-queue-prod',
  OAUTH_JWT_SECRET: process.env.OAUTH_JWT_SECRET,
  OAUTH_JWT_REFRESH_SECRET: process.env.OAUTH_JWT_REFRESH_SECRET,
  TELEGRAM_AUTH: process.env.TELEGRAM_AUTH,
  TELEGRAM_URL:
    process.env.TELEGRAM_URL ||
    'https://nas-telegram-social-bot.nasacademy.com/api/v1',
  SCRAPE_SOCIAL_MEDIA_INFO_SQS_QUEUE_URL:
    process.env.SCRAPE_SOCIAL_MEDIA_INFO_SQS_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/************/scrape-social-media-info-queue-prod',
  DISCORD_URL:
    process.env.DISCORD_URL || 'https://discord-bot.nasacademy.com',
  BULK_NOTIFICATION_QUEUE_URL:
    process.env.BULK_NOTIFICATION_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/************/bulk-notification-orchestrator-queue-prod',
  DISCORD_AUTH: process.env.DISCORD_AUTH,
  NAS_IO_APP_DYNAMIC_LINK:
    process.env.NAS_IO_APP_DYNAMIC_LINK || 'https://nasio.page.link',
  DISCORD_MEMBER_ROLE_SQS_QUEUE_URL:
    process.env.DISCORD_MEMBER_ROLE_SQS_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/************/w3-discord-member-role-assignment-prod.fifo',
  GENERATE_AI_TEMPLATE_SQS_QUEUE_URL:
    process.env.GENERATE_AI_TEMPLATE_SQS_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/************/generate-ai-template-queue-prod',
  WHATSAPP_SERVICE_SQS_QUEUE_URL:
    process.env.WHATSAPP_SERVICE_SQS_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/************/whatsapp-send-msg-queue-prod.fifo',
  MAGIC_REACH_SEND_EMAIL_QUEUE_URL:
    process.env.MAGIC_REACH_SEND_EMAIL_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/************/magic-reach-send-email-queue-prod',
  MAGIC_REACH_EMAIL_EVENT_QUEUE_URL:
    process.env.MAGIC_REACH_EMAIL_EVENT_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/************/magic-reach-email-event-queue-prod.fifo',
  MAGIC_REACH_SEND_WHATSAPP_QUEUE_URL:
    process.env.MAGIC_REACH_SEND_WHATSAPP_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/************/magic-reach-send-whatsapp-queue-prod',
  MAGIC_REACH_WHATSAPP_EVENT_QUEUE_URL:
    process.env.MAGIC_REACH_WHATSAPP_EVENT_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/************/magic-reach-whatsapp-event-queue-prod.fifo',
  NOTIFICATION_SERVICE_DB:
    process.env.NOTIFICATION_SERVICE_DB_NAME ||
    'notification_service_production',
  EMAIL_ICS_PATH: process.env.EMAIL_ICS_PATH || 'email_assets/ics',
  KLAVIYO_LIST_ID: process.env.KLAVIYO_LIST_ID || 'TeUEpR',
  AUTH_SERVICE_URL: process.env.AUTH_SERVICE_URL || 'https://auth.nas.io',
  REACTION_QUEUE_URL:
    process.env.REACTION_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/************/reactions-prod.fifo',
  CHAT_INVITE_LINK_QUEUE_URL:
    process.env.CHAT_INVITE_LINK_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/************/create-discord-chat-invite-link-prod',
  COMMENT_QUEUE_URL:
    process.env.COMMENT_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/************/comments-prod.fifo',
  WHATSAPP_SERVICE_QUEUE_BOT_URL:
    process.env.WHATSAPP_SERVICE_QUEUE_BOT_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/************/whatsapp-service-queue-prod.fifo',
  AXIOS_KEEP_ALIVE_MAX_SOCKETS:
    process.env.AXIOS_KEEP_ALIVE_MAX_SOCKETS || 128, // default 128 / os.cpus().length if running node across multiple CPUs
  AXIOS_KEEP_ALIVE_MAX_FREE_SOCKETS:
    process.env.AXIOS_KEEP_ALIVE_MAX_FREE_SOCKETS || 128, // default 128 / os.cpus().length if running node across multiple CPUs
  AXIOS_KEEP_ALIVE_TIMEOUT: process.env.AXIOS_KEEP_ALIVE_TIMEOUT || 45000, // default active socket keepalive for 10 mins
  AXIOS_KEEP_ALIVE_FREE_SOCKET_TIMEOUT:
    process.env.AXIOS_KEEP_ALIVE_FREE_SOCKET_TIMEOUT || 45000, // default free socket keepalive for 10 mins
  CLOUD_WATCH_NAMESPACE:
    process.env.CLOUD_WATCH_NAMESPACE || 'LearningPortalBEProd',
  CLOUD_WATCH_METRIC_INTERVAL:
    process.env.CLOUD_WATCH_METRIC_INTERVAL || 300,
  PROMETHEUS_COLLECT_METRIC_INTERVAL:
    process.env.PROMETHEUS_COLLECT_METRIC_INTERVAL || 30,
  ENABLE_CLOUD_WATCH_PUSH: process.env.ENABLE_CLOUD_WATCH_PUSH || 0,
  OPENAI_API_KEY:
    process.env.OPENAI_API_KEY ||
    '***************************************************',
  MAGIC_REACH_FRAUD_PROBABILITY_THRESHOLD:
    process.env.MAGIC_REACH_FRAUD_PROBABILITY_THRESHOLD || 75,
  MAGIC_REACH_BLOCK_ON_FRAUD: process.env.MAGIC_REACH_BLOCK_ON_FRAUD || 1,
  MAGIC_REACH_SEND_COUNT_BLOCK_THRESHOLD:
    process.env.MAGIC_REACH_SEND_COUNT_BLOCK_THRESHOLD || 50,
  COPS_JWT_SECRET: process.env.COPS_JWT_SECRET,
  STRIPE_KEY:
    process.env.STRIPE_KEY || '******************************************',
  XENDIT_SECRET_KEY_PH:
    process.env.XENDIT_SECRET_KEY_PH ||
    'xnd_production_FXyBnNhPRxye3tHMI9akYfp8e9ViWBNDgmCZzOaTaegl3zPsRPwCV3MSyzTGTb',
  FRESHDESK_URL:
    process.env.FRESHDESK_URL || 'https://nasacademy.freshdesk.com',
  FRESHDESK_SECRET: process.env.FRESHDESK_SECRET || '********************',
  INTERCOM_URL: process.env.INTERCOM_URL || 'https://api.intercom.io',
  PAYMENT_MAILER_URL:
    process.env.PAYMENT_MAILER_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/************/payment-mail-prod',
  ACTION_EVENT_QUEUE_URL:
    process.env.ACTION_EVENT_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/************/action-event-queue-prod',
  ACTION_EVENT_QUEUE_ARN:
    process.env.ACTION_EVENT_QUEUE_ARN ||
    'arn:aws:sqs:ap-southeast-1:************:action-event-queue-prod',
  FIND_IP_URL: process.env.FIND_IP_URL || 'https://api.findip.net',
  NEXT_JS_SECRET_TOKEN:
    process.env.NEXT_JS_SECRET_TOKEN || 'aC2xHhQ2sBdU074AtcwQWxAaRe0OHOiZ',
  SUBSCRIPTION_UPDATER_QUEUE_URL:
    process.env.SUBSCRIPTION_UPDATER_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/************/subscription-updater-queue-prod',
  SUBSCRIPTION_UPDATER_QUEUE_ARN:
    process.env.SUBSCRIPTION_UPDATER_QUEUE_ARN ||
    'arn:aws:sqs:ap-southeast-1:************:subscription-updater-queue-prod',
  REVENUE_TRANSACTION_QUEUE_URL:
    process.env.REVENUE_TRANSACTION_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/************/revenue-transaction-queue-prod',
  CHALLENGE_CHECKPOINT_NOTIFICATION_QUEUE_URL:
    'https://sqs.ap-southeast-1.amazonaws.com/************/checkpoint-reminder-prod',
  WALLET_ERROR_ALERT_LARK_WEBHOOK:
    process.env.WALLET_ERROR_ALERT_LARK_WEBHOOK ||
    'https://open.larksuite.com/open-apis/bot/v2/hook/b53ac9ef-a290-446a-ba11-79735e20aa11',
  PAYOUT_FAILED_ALERT_LARK_WEBHOOK:
    process.env.PAYOUT_FAILED_ALERT_LARK_WEBHOOK ||
    'https://open.larksuite.com/open-apis/bot/v2/hook/81c639ee-47c1-4574-b2e1-5a1e3d911166',
  S3_RECIPIENT_BASE_PATH:
    process.env.S3_RECIPIENT_BASE_PATH ||
    '/magic-reach-archived-recipients/prod',
  PAYPAL_DASHBOARD_DOMAIN:
    process.env.PAYPAL_DASHBOARD_DOMAIN || 'https://www.paypal.com',
  CHALLENGE_LEADERBOARD_POINTS_QUEUE_URL:
    process.env.CHALLENGE_LEADERBOARD_POINTS_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/************/challenge-leaderboard-points-prod.fifo',
  OPEN_AI_FRAUD_CHECK_QUEUE_URL:
    process.env.OPEN_AI_FRAUD_CHECK_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/************/open-ai-fraud-check-queue-prod',
  ERROR_ALERT_LARK_WEBHOOK:
    process.env.ERROR_ALERT_LARK_WEBHOOK ||
    'https://open.larksuite.com/open-apis/bot/v2/hook/d6efd93e-5e46-4897-9505-fd9083bce35c',
  SUPPORTING_TICKET_SENDER_QUEUE_URL:
    process.env.SUPPORTING_TICKET_SENDER_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/************/supporting-ticket-sender-queue-prod',
  CHAT_NOTIFICATION_QUEUE_ARN:
    process.env.CHAT_NOTIFICATION_QUEUE_ARN ||
    'arn:aws:sqs:ap-southeast-1:************:chat-notification-queue-prod',
  NASIO_PRO_LARK_WEBHOOK:
    process.env.NASIO_PRO_LARK_WEBHOOK ||
    'https://open.larksuite.com/open-apis/bot/v2/hook/b18c307d-16e1-406f-98d4-c1f069f4eeec',
  NASIO_PLATINUM_LARK_WEBHOOK:
    process.env.NASIO_PLATINUM_LARK_WEBHOOK ||
    'https://open.larksuite.com/open-apis/bot/v2/hook/8aa37dac-4bb4-4763-b78d-8a130e5b99ec',
  NASIO_PRO_CANCELLED_LARK_WEBHOOK:
    process.env.NASIO_PRO_CANCELLED_LARK_WEBHOOK ||
    'https://open.larksuite.com/open-apis/bot/v2/hook/ab4035fe-ff99-42bf-b549-49b3b4ba3ee3',
  NASIO_PLATINUM_CANCELLED_LARK_WEBHOOK:
    process.env.NASIO_PLATINUM_CANCELLED_LARK_WEBHOOK ||
    'https://open.larksuite.com/open-apis/bot/v2/hook/e07f9eeb-2a8f-4d21-b12e-0731dbea0f9e',
  NASIO_SUBSCRIPTION_LARK_ALERT_QUEUE_URL:
    process.env.NASIO_SUBSCRIPTION_LARK_ALERT_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/************/nas-subscription-lark-alert-queue-prod',
  CALENDAR_ERROR_LARK_WEBHOOK:
    process.env.CALENDAR_ERROR_LARK_WEBHOOK ||
    'https://open.larksuite.com/open-apis/bot/v2/hook/2bbdfaad-fc88-487f-82b1-ad300e458a5d',
  CACHE_REVALIDATE_QUEUE_ARN:
    process.env.CACHE_REVALIDATE_QUEUE_ARN ||
    'arn:aws:sqs:ap-southeast-1:************:cache-revalidate-queue-prod',
  ABANDONED_CHECKOUT_UPDATE_QUEUE_ARN:
    process.env.ABANDONED_CHECKOUT_UPDATE_QUEUE_ARN ||
    'arn:aws:sqs:ap-southeast-1:************:abandoned-checkout-update-queue-prod',
  GENERAL_PURPOSE_TASK_QUEUE_ARN:
    process.env.GENERAL_PURPOSE_TASK_QUEUE_ARN ||
    'arn:aws:sqs:ap-southeast-1:************:general-purpose-task-queue-prod',
  GENERAL_PURPOSE_TASK_QUEUE_URL:
    process.env.GENERAL_PURPOSE_TASK_QUEUE_URL ||
    'https://sqs.ap-southeast-1.amazonaws.com/************/general-purpose-task-queue-prod',
  NODE_ENV: process.env.NODE_ENV || 'production',
  CHECKOUT_TOKEN_SESSION_TYPE:
    process.env.CHECKOUT_TOKEN_SESSION_TYPE || '64e4ebf883b2',
  EMAIL_TOKEN_SESSION_TYPE:
    process.env.EMAIL_TOKEN_SESSION_TYPE || '5714917d90ac',
  authCookieDomain: process.env.AUTH_COOKIE_DOMAIN || 'nas.io',
  unsplashApiKey:
    process.env.UNSPLASH_API_KEY ||
    '*******************************************',
  META_APP_ACCESS_TOKEN:
    process.env.META_APP_ACCESS_TOKEN ||
    'EAAPAZAHESwRoBO8wzt9nNWBNgsUxj1s2jk3rsRjFMcFl2CO0eHdBY1zqLwlhmKourT3MER2X1ac5yUCmaqk4LfFsJ0gsQwSIuxrWSxMLf00t2sq3ZArAWTusfws2TcMAgYBAdQxaUNQijdKfanHz3FnQ1jdu1ZA2libBuHCAKn6TIXTLQmCY5DlITQbtAZDZD',
  META_AD_WEBHOOK_VERIFY_TOKEN:
    process.env.META_AD_WEBHOOK_VERIFY_TOKEN || '1',
  META_APP_SECRET_KEY:
    process.env.META_APP_SECRET_KEY || 'ff7d7d42b1374f116ef0346479b6e57e',
  META_APP_ID: process.env.META_APP_ID || '1055962556449050',
  META_BUSINESS_ID: process.env.BUSINESS_ID || '1395802151732715',
  META_SYSTEM_USER_ID:
    process.env.META_SYSTEM_USER_ID || '122094913484899535',
};
