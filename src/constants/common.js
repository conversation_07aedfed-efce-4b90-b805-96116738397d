const { PRODUCT_TYPE } = require('../services/product/constants');

const unitStatuses = {
  COMPLETED: 'completed',
  CURRENT: 'current',
  UPCOMING: 'upcoming',
};

const subunitOfferStatuses = {
  COMPLETED: 'completed',
  PENDING: 'pending',
};

const exerciseTypesMap = {
  EXERCISE: 'exercise',
  RESOURCE: 'resource',
  ACTIVITY: 'activity',
};

const userStatus = {
  ACTIVE: 'ACTIVE',
  PENDING: 'PENDING',
  INACTIVE: 'INACTIVE',
};

const courseOfferStatuses = {
  PENDING: 'Pending',
  INACTIVE: 'Inactive',
  ACTIVE: 'Active',
  CANCELLED: 'Cancelled',
};

const courseEnrolmentStatuses = {
  PENDING: 'Pending',
  CURRENT: 'Current',
  INACTIVE: 'Inactive',
  COMPLETED: 'Completed',
  TRANSFERRED: 'Transferred',
  WITHDRAWN: 'Withdrawn',
  CANCELLED: 'Cancelled',
};

const communityEnrolmentStatuses = {
  PENDING: 'Pending',
  CURRENT: 'Current',
  INACTIVE: 'Inactive',
  CANCELLED: 'Cancelled',
  REJECTED: 'Rejected',
  PROMOTED: 'Promoted',
  REMOVED: 'Removed',
};

const POST_APPROVAL_PROCESS_TYPE = {
  EVENT: 'event',
  FOLDER: 'folder',
  SESSION: 'session',
  CHALLENGE: 'challenge',
};

const assignmentStatuses = {
  ACTIVE: 'active',
  PENDING_REVIEW: 'pendingReview',
  REVIEWED: 'reviewed',
  COMPLETED: 'completed',
};

const eventStatuses = {
  ACTIVE: 'Active',
  CANCELLED: 'Cancelled',
};

const eventTimeIntervalInMinutes = {
  FIVE_MINS: 5,
  ONE_HOUR: 60,
  ONE_DAY: 1440,
};

const eventTimeInterval = {
  FIVE_MINS: 'FIVE_MINS',
  ONE_HOUR: 'ONE_HOUR',
  ONE_DAY: 'ONE_DAY',
};

const eventTimeIntervalEnum = [
  eventTimeInterval.FIVE_MINS,
  eventTimeInterval.ONE_HOUR,
  eventTimeInterval.ONE_DAY,
];

const MAX_QUANTITY_PER_PURCHASE = 100;

const EVENT_TYPES = {
  LIVE: 'live',
  INPERSON: 'inPerson',
};

const SESSION_TYPES = {
  ONLINE: 'online',
  IN_PERSON: 'inPerson',
};

const certificateStatuses = {
  PENDING: 'pending',
  COMPLETED: 'completed',
};

const DEFAULT_COMMUNITY_BANNER_IMAGE =
  'https://s3.ap-southeast-1.amazonaws.com/image-assets.nasdaily.com/event_default_banner.png';

const DEFAULT_COMMUNITY_PROFILE_IMAGE =
  'https://d2oi1rqwb0pj00.cloudfront.net/na-website/community-product-page/nas-io-homepage/NA+logo.jpeg';

const DEFAULT_COMMUNITY_HOST_PROFILE_IMAGE =
  'https://d2oi1rqwb0pj00.cloudfront.net/na-portal/default-community-host-profile.png';

const thresholdProgress = 75;
const progressiveUnlockingThreshold = 80;
const completeProgress = 100;

const sortParams = {
  REFERRER_EARNING: 'referrerEarning',
  CREATED_AT: 'createdAt',
  STUDENT_NAME: 'name',
  COUNTRY: 'country',
  EMAIL: 'email',
  PHONE_NUMBER: 'phoneNumber',
  JOIN_DATE: 'joinDate',
};

const sortingOrder = {
  ASCENDING: 'asc',
  DESCENDING: 'desc',
};

const sortingOrderMap = {
  asc: 1,
  desc: -1,
};

const sortOrderRegex = new RegExp(
  `${sortingOrder.ASCENDING}|${sortingOrder.DESCENDING}`
);

const studentSortByRegex = new RegExp(
  `${sortParams.STUDENT_NAME}|${sortParams.COUNTRY}|${sortParams.EMAIL}|${sortParams.PHONE_NUMBER}|${sortParams.JOIN_DATE}`
);

const dateFilter = {
  DAILY: 'day',
  WEEKLY: 'week',
  MONTHLY: 'month',
};

const monthNameMapping = {
  1: 'Jan',
  2: 'Feb',
  3: 'Mar',
  4: 'Apr',
  5: 'May',
  6: 'Jun',
  7: 'Jul',
  8: 'Aug',
  9: 'Sep',
  10: 'Oct',
  11: 'Nov',
  12: 'Dec',
};

// Map between the CTA button text, to the field in which to extract trainer-specific in trainer
const ctaBtnTextToTrainerSpecificFieldMap = {
  calendly: 'calendlyLink',
};
const defaultExerciseCtaText = 'Download';
const defaultExerciseCtaIcon =
  'https://d2oi1rqwb0pj00.cloudfront.net/icons/download.png';
const COHORT_DEFAULT_EXERCISE_CTA_ICON =
  'https://d2oi1rqwb0pj00.cloudfront.net/icons/download-blue.png';

const generalAlumniFacebookLink =
  'https://www.facebook.com/groups/649900375895632';

const FREE_WORKSHOP_FB_GROUP_LINK =
  'https://www.facebook.com/groups/nasacademyalumnimain';

const customErrorCodes = {
  ALREADY_SIGNED_UP: 10,
};
const thresholdFreeTrialProgressInMS = 300000;
const waveSelectionMailCourses = [
  'HPYPR',
  'DAN_MACE_FINDING_YOUR_VOICE',
  'NAS_DAILY_FINAL_CUT_BEGINNER',
  'SMILE_SQUAD_VIRAL_VIDEO',
  'IBDBPR',
  'DFYIPR',
  'NSPR',
  'NLPR',
  'MTIKPR',
  'DAPR',
  'TUCPR',
  'MKPR',
];

const ADMIN_EDITABLE_FIELDS_IN_COMMUNITY_APPLICATION = [
  'primarySocialMedia',
  'secondarySocialMedia',
  'followersCount',
  'discordUsername',
  'joiningReasonFull',
  'selectedNiche',
  'selectedNicheOptions',
  'telegramUsername',
  'reviewerNotes',
  'hasJoinedChat',
];

const COURSE_MODES = {
  LIVE: 'live',
  COHORT_LIVE: 'cohortlive',
  ON_DEMAND: 'ondemand',
  FREE_WORKSHOP: 'freeworkshop',
  VIDEO_ON_DEMAND: 'videoondemand',
  COHORT_ON_DEMAND: 'cohortondemand',
};

const COURSE_OFFER_TYPES = {
  LIVE: 'live',
  COHORT_LIVE: 'cohortLive',
  FREE_WORKSHOP: 'freeWorkshop',
  PRE_RECORDED: 'preRecorded',
  VIDEO_ON_DEMAND: 'videoOnDemand',
  COHORT_ON_DEMAND: 'cohortOnDemand',
};

const ON_DEMAND_MODES = [
  COURSE_MODES.ON_DEMAND,
  COURSE_MODES.VIDEO_ON_DEMAND,
  COURSE_MODES.COHORT_ON_DEMAND,
];

const ON_DEMAND_TYPES = [
  COURSE_OFFER_TYPES.PRE_RECORDED,
  COURSE_OFFER_TYPES.VIDEO_ON_DEMAND,
  COURSE_OFFER_TYPES.COHORT_ON_DEMAND,
];

const COURSES_DROPDOWN_DISPLAY = {
  [COURSE_MODES.LIVE]: 'Live',
  [COURSE_MODES.ON_DEMAND]: 'On-Demand',
  [COURSE_MODES.COHORT_ON_DEMAND]: 'Cohort On-Demand',
  [COURSE_MODES.VIDEO_ON_DEMAND]: 'Video On-Demand',
  [COURSE_MODES.FREE_WORKSHOP]: 'Free Workshop',
};

const LIVE_SESSION_DICT = {
  LAUNCH_PARTY: 'launch-party',
  Q_N_A: 'qna',
  GRADUATION: 'graduation',
  MEET_THE_CREATOR: 'meet-creator',
  CUSTOM_LIVE_SESSION: 'custom-live-session',
};

const FOCUS_VIEW_CUSTOM_CARDS = ['feedback', 'reminder'];

const BASIC_CUSTOM_CARDS = {
  REMINDER: 'reminder',
};

const BASIC_CUSTOM_CARDS_TYPES = [BASIC_CUSTOM_CARDS.REMINDER];

const BASIC_CUSTOM_CARDS_DATA = {
  [BASIC_CUSTOM_CARDS.REMINDER]: {
    type: BASIC_CUSTOM_CARDS.REMINDER,
    data: {
      title: 'Reminder: You Are Not Alone',
      description:
        'Remember to not do this alone! Take 5 minutes, drop your buddy and wave mates a message to check in on and support them!',
      imgData: {
        alt: 'reminder',
        mobileImgData: {
          src: 'https://d2oi1rqwb0pj00.cloudfront.net/na-portal/CohortOnDemandDayZeroCardThumbnails/NUSEIR_BETTER_WRITER/join-community-mobile.png',
        },
        desktopImgData: {
          src: 'https://d2oi1rqwb0pj00.cloudfront.net/na-portal/CohortOnDemandDayZeroCardThumbnails/NUSEIR_BETTER_WRITER/join-community-desktop.png',
        },
      },
    },
  },
};

const TODAY_PAGE_HIDDEN_CARDS = [BASIC_CUSTOM_CARDS.REMINDER];

const LIVE_SESSION_TYPES = [
  LIVE_SESSION_DICT.LAUNCH_PARTY,
  LIVE_SESSION_DICT.Q_N_A,
  LIVE_SESSION_DICT.GRADUATION,
  LIVE_SESSION_DICT.MEET_THE_CREATOR,
  LIVE_SESSION_DICT.CUSTOM_LIVE_SESSION,
];

const LEGACY_LIVE_SESSION_TYPES = [
  LIVE_SESSION_DICT.LAUNCH_PARTY,
  LIVE_SESSION_DICT.Q_N_A,
  LIVE_SESSION_DICT.GRADUATION,
  LIVE_SESSION_DICT.MEET_THE_CREATOR,
];

const FILTERED_LIVE_SESSION_TYPES = [
  LIVE_SESSION_DICT.Q_N_A,
  LIVE_SESSION_DICT.GRADUATION,
  LIVE_SESSION_DICT.CUSTOM_LIVE_SESSION,
];

const ZERO_DAY_CARDS = {
  LAUNCH_PARTY: 'launch-party',
  JOIN_COMMUNITY: 'join-community',
  MEET_FACILITATOR: 'meet-facilitator',
  CUSTOM_LIVE_SESSION: 'custom-live-session',
  MEET_CREATOR_NOTE: 'meet-creator-note',
};

const ZERO_DAY_CARD_TYPES = [
  ZERO_DAY_CARDS.LAUNCH_PARTY,
  ZERO_DAY_CARDS.JOIN_COMMUNITY,
  ZERO_DAY_CARDS.MEET_FACILITATOR,
  ZERO_DAY_CARDS.MEET_CREATOR_NOTE,
];

const ZERO_DAY_UNIT_OFFER_ID = -1;

const FIRST_NAME_REGEX = /{firstName}/g;

const LIVE_MODES = [
  COURSE_MODES.LIVE,
  COURSE_MODES.COHORT_LIVE,
  COURSE_MODES.FREE_WORKSHOP,
];

const LIVE_OFFER_TYPES = [
  COURSE_OFFER_TYPES.LIVE,
  COURSE_OFFER_TYPES.COHORT_LIVE,
  COURSE_OFFER_TYPES.FREE_WORKSHOP,
];

const VIDEO_LINK_REGEX = /{PRIVATE_VIDEO_BASE_URL}/g;
const NASG_VIDEO_LINK_REGEX = /{PRIVATE_NASG_VIDEO_BASE_URL}/g;

const NFT_CHECK_COURSE_LIST = ['BEN_YU_NFTS'];
const NFT_SUB_UNIT_CATEGORY_CHECK_LIST = ['postgrad'];

const COURSES_WITH_ZERO_PRICE = [
  'EPIDEMIC_SOUND_DESIGN',
  'VAYNER_10_STEPS_NFT',
];

const COMMUNITY_MEMBERSHIPS = {
  filters: {
    nasMembershipsFilter: 'nas',
    onlyUserCountryFilter: 'country',
  },
  nasEmailPatterns: [/@nasacademy.com/, /@nasdaily.com/],
};

const COMMUNITY_SHORT_CODE_LEN = 4;

const COMMUNITY_SUBSCRIPTION_STATUSES = {
  PENDING: 'Pending',
  CURRENT: 'Current',
  REJECTED: 'Rejected',
  CANCELLED: 'Cancelled',
  INTERESTED: 'Interested',
  PROMOTED: 'Promoted',
  REMOVED: 'Removed',
};

const COMMUNITY_INVITED_USERS_STATUSES = {
  INVITED: 'Invited',
  ACTIVE: 'Active',
};

const COMMUNITY_HOSTS_ROLE = {
  ADMIN: 'admin',
  MEMBER: 'member',
  GUEST: 'guest',
};

const COMMUNITY_APPLICATION_STATUS = {
  PENDING: 'Pending',
  APPROVED: 'Approved',
  REJECTED: 'Rejected',
  UNSUBSCRIBED: 'Unsubscribed',
  UNSUBSCRIBED_MEMBER: 'UnsubscribedMember',
  NO_APPLICATION: 'noApplication',
};

const INTERNAL_EMAIL_DOMAINS = [
  'nasdaily.com',
  'thenasacademy.com',
  'nasacademy.com',
  'nas.io',
];

const COMMUNITY_MEMBER_TYPES = {
  NASACADEMY: 'nasacademy',
  FREE: 'free',
  PAID: 'paid',
};

const RANDOM_PROFILE_PIC_URL_PREFIX =
  'https://d2yjtdaqamc55g.cloudfront.net/randomProfileImage';

const CALENDLY_EVENT_TYPES = {
  CREATED: 'invitee.created',
  CANCELED: 'invitee.canceled',
};

const PUBLIC_ENDPOINT_SUFFIXES = [
  'webhook',
  'log-in',
  'sign-up',
  'get-token',
  'mobile/verify-email',
  'mobile/version-compatibility',
  'mobile/minimum-app-version',
];

const FILEASSETBUCKET = 'file-assets.nasdaily.com';
const VIDEOASSETBUCKET = 'nasg-on-demand-public-video-source-3vt2cjnnsn0r';

const IMAGEASSETBUCKET = 'image-assets.nasdaily.com';

const IMAGEASSETBUCKET_S3_BASE_URL =
  'https://s3.ap-southeast-1.amazonaws.com/image-assets.nasdaily.com';

const IMAGEASSETBUCKET_CLOUDFRONT_BASE_URL =
  'https://d2oi1rqwb0pj00.cloudfront.net';

const NASACADEMYBUCKET_CLOUDFRONT_BASE_URL =
  'https://d1ln96rzxchhx.cloudfront.net';

const NASG_CLOUDFRONT_BASE_URL = 'https://d3rothxfhceob2.cloudfront.net';

const FILEASSETBUCKET_CLOUDFRONT_BASE_URL =
  'https://dkeexuinpcfb8.cloudfront.net';

const MOBILE_NOTIFICATION_PHASE = {
  ONBOARDING: 'onboarding',
  LIVE_SESSIONS: 'liveSessions',
  MEMBERS: 'members',
  LIBRARY: 'library',
  MEET_EXPERT: 'meetExpert',
  PROMOTIONS: 'promotions',
  NEW_LAUNCHES: 'newLaunches',
  ANNOUNCEMENTS: 'announcements',
  CHATS: 'chats',
};

const MOBILE_NOTIFICATION_TYPES = {
  ANNOUNCEMENT_ADDED: 'announcementAdded',
  RESOURCE_ADDED: 'resourceAdded',
  EVENT_ADDED: 'eventAdded',
  EVENT_UPDATED: 'eventUpdated',
  EVENT_STARTING_NOW: 'eventStartingNow',
  EVENT_STARTING_10_MINUTES: 'eventStartingIn10Minutes',
  EVENT_STARTING_3_HOURS: 'eventStartingIn3Hours',
  NEW_MEMBER_APPLICATION: 'newMemberApplication',
  APPLICATION_APPROVED: 'applicationApproved',
  NEW_EVENT_APPLICATION: 'newEventApplication',
  FIRST_EVENT_ATTENDANCE: 'firstEventAttendance',
  RENEWAL_FAILED_PAYMENT: 'retryPayment',
  CHALLENGE_STARTED: 'challengeStarted',
  CHALLENGE_ENDED: 'challengeEnded',
  CHECKPOINT_START: 'checkpointStart',
  CHECKPOINT_70_PERCENT_DONE: 'checkpoint70PercentDone',
  CHECKPOINT_ENDING_IN_1_HOUR: 'checkpointEndingIn1hour',
  CHECKPOINT_EVENT_24_HOUR: 'checkpointEvent24H',
  CHECKPOINT_EVENT_1_HOUR: 'checkpointEvent1H',
  CHECKPOINT_EVENT_NOW: 'checkpointEventNow',
  CM_ANNOUNCEMENT_PENDING_NOTIFY: 'cmPostPendingNotify',
  MEMBER_POST_APPROVED_NOTIFY: 'memberPostApprovedNotify',
  MEMBER_POST_REJECTED_NOTIFY: 'memberPostRejectedNotify',
  ALWAYS_ON_CHECKPOINT_START: 'alwaysOnCheckpointStart',
  NEW_CHAT_MESSAGE: 'newChatMessage',
  COMMUNITY_REFERRAL_SUCCESS: 'communityReferralSuccess',
  COMMUNITY_REFERRAL_RENEWED: 'communityReferralRenewed',
  MEMBERS_LIMIT_REACHING: 'membersLimitReaching',
  MEMBERS_LIMIT_REACHED: 'membersLimitReached',
};

const ANNOUNCEMENT_WHATSAPP_TEMPLATE_TYPE = {
  MEMBER_COMMUNITY_POST_APPROVED: 'MEMBER_COMMUNITY_POST_APPROVED',
  MEMBER_COMMUNITY_POST_REJECTED: 'MEMBER_COMMUNITY_POST_REJECTED',
  COMMUNITY_POST_ANNOUNCEMENT: 'COMMUNITY_POST_ANNOUNCEMENT',
};
const ANNOUNCEMENT_LOCALIZATION_KEY = {
  CM_ANNOUNCEMENT_PENDING_NOTIFY: {
    title: 'post-pending-notify-title',
    body: 'post-pending-notify-body',
  },
  MEMBER_POST_APPROVED_NOTIFY: {
    title: 'member-post-approved-notify-title',
    body: 'member-post-approved-notify-body',
  },
  MEMBER_POST_REJECTED_NOTIFY: {
    title: 'member-post-rejected-notify-title',
    body: 'member-post-rejected-notify-body',
  },
  ANNOUNCEMENT_ADDED: {
    title: 'announcement-added-title',
    body: 'announcement-added-body',
  },
};

const MOBILE_NOTIFICATION_RECIPIENTS_SCOPES = {
  GLOBAL: 'global',
  SINGLE: 'single',
};

const CHAT_PLATFORMS = {
  DISCORD: 'Discord',
  SLACK: 'Slack',
  FACEBOOK: 'Facebook',
  TELEGRAM: 'Telegram',
  WHATSAPP: 'Whatsapp',
  LINKEDIN: 'LinkedIn',
  LINE: 'Line',
};

const SOCIAL_CHAT_CONNECTION_TYPES = {
  FACEBOOK: 'facebook',
  LINKEDIN: 'linkedin',
  SLACK: 'slack',
  TELEGRAM: 'telegram',
};

const CONFIG_TYPES = {
  ZERO_LINK_WHITELIST_CONFIG_TYPE: 'zeroLinkWhitelistConfig',
  GET_INSPIRED_WHITELIST_CONFIG_TYPE: 'getInspiredWhitelistConfig',
  AI_SUMMARY_WHITELIST_CONFIG_TYPE: 'aiSummaryWhitelistConfig',
  AI_SUMMARY_BLACKLIST_CONFIG_TYPE: 'aiSummaryBlacklistConfig',
  CUSTOM_WELCOME_MESSAGE_BLACKLIST_CONFIG_TYPE:
    'customWelcomeMessageBlacklistConfig',
  LEARN_PORTAL_BACKEND_ENV_CONFIG_TYPE: 'LPBEEnvVars',
  PAYMENT_BACKEND_ENV_CONFIG_TYPE: 'paymentBEEnvVars',
  INDIA_PAYMENT_WHITELIST_CONFIG_TYPE: 'indiaPaymentWhitelistConfig',
  REVENUE_CONFIGURATION: 'REVENUE_CONFIGURATION',
  RANDOM_PROFILE_IMAGE: 'randomProfileImage',
  ONETIME_PAYMENT_PRICE_BOUND: 'onetimePaymentPriceBound',
  PAYMENT_FEE_STRUCTURE: 'paymentFeeStructure',
  ADS_CAMPAIGN_FEE_STRUCTURE: 'adsCampaignFeeStructure',
  PASSON_PAYMENT_GATEWAY_CONFIG_TYPE: 'passonPaymentGatewayConfig',
  PAYMENT_OPTION_WHITELIST_CONFIG: 'paymentOptionWhitelistConfig',
};

const CONTRACT_TYPES = { ERC1155: 'ERC1155', ERC721: 'ERC721' };

const NETWORK_TYPES = { ETH: 'eth', SOLANA: 'solana' };

const MAX_FILE_UPLOAD_SIZE = 5368709120;

const BILLING_CYCLE_DAYS = 30;

const SECONDS_IN_A_DAY = 1000 * 60 * 60 * 24;

// reaction types can be like, etc other verbs expressed with different emojis
const REACTION_TYPES = {
  LIKE: 'like',
  LOVE: 'love',
  GREAT_GOB: 'great_job',
  INSPIRED: 'inspired',
  CONGRATULATIONS: 'congratulations',
  AWESOME: 'awesome',
  CELEBRATE: 'celebrate',
};

// reaction entity collections defines entities/collections to associate reactions to, value must be same as db collection name
const REACTION_ENTITY_COLLECTIONS = {
  COMMUNITY_POST: 'community_posts',
  FEED_ENTRY: 'feed_entry',
  COMMENT: 'comments',
};

// reaction state maintains states each reaction can have (2 only for now)
const REACTION_STATES = {
  REACTED: 1,
  UNREACTED: 0,
};

// comment types differentiate a comment from a reply
const COMMENT_TYPES = {
  COMMENT: 'comment',
  REPLY: 'reply',
};

// comment categories define type of content in comment, text or html or media
const COMMENT_CATEGORIES = {
  TEXT: 'text',
  HTML: 'html',
};

// comment entity collections defines entities/collections to associate comments to, value must be same as db collection name
const COMMENT_ENTITY_COLLECTIONS = {
  COMMUNITY_POST: 'community_posts',
  FEED_ENTRY: 'feed_entry',
};

const ANONYMOUS_COMMUNITY_USER_NAME = 'Community Member';

const DEFAULT_COUNTRY = 'United States';
const DEFAULT_COUNTRY_CODE = 'US';
const DEFAULT_CURRENCY = 'USD';
const DEFAULT_CONVERSION_MULTIPLIER = 1;
const PERCENTAGE_DEDUCTION = {
  STRIPE_PROCESSING_FEE: 3.4,
  PH_GCASH_PROCESSING_FEE: 2.3,
  PH_PAYMAYA_PROCESSING_FEE: 1.8,
  PH_GRABPAY_PROCESSING_FEE: 2.0,
  PH_SHOPEEPAY_PROCESSING_FEE: 2.0,
  ID_DANA_PROCESSING_FEE: 1.5,
  ID_OVO_PROCESSING_FEE: 3.18,
  TRANSACTION_CHARGE: 10,
};

const PAYMENT_PROVIDER = {
  STRIPE: 'stripe',
  STRIPE_US: 'stripe-us',
  XENDIT: 'xendit',
  STRIPE_INDIA: 'stripe-india',
  VOLT: 'volt',
  RAZORPAY: 'razorpay',
  EBANX: 'ebanx',
  NAS: 'nas',
  PAYPAL: 'paypal',
};

const PAYOUT_GATEWAY = {
  STRIPE_US: 'stripe-us',
};

const PAYMENT_TYPE = {
  PH_GCASH: 'PH_GCASH',
  PH_PAYMAYA: 'PH_PAYMAYA',
  PH_GRABPAY: 'PH_GRABPAY',
  PH_SHOPEEPAY: 'PH_SHOPEEPAY',
  ID_DANA: 'ID_DANA',
  ID_OVO: 'ID_OVO',
};

const EBANX_SUPPORTED_CURRENCY = {
  BRL: 'BRL',
  MXN: 'MXN',
  COP: 'COP',
  PEN: 'PEN',
  ARS: 'ARS',
  CLP: 'CLP',
};

const EBANX_RECURRING_PAYMENT_TYPE = {
  CARD: 'card',
  EWALLET: 'ewallet',
};

const EBANX_PAYMENT_TYPE = {
  CREDIT_CARD: [
    'VISA',
    'MASTERCARD',
    'AMEX',
    'AURA',
    'DINERS',
    'DISCOVER',
    'ELO',
    'HIPERCARD',
    'CARNET',
    'CREDIMAS',
    'NARANJA',
    'CABAL',
    'MAGNA',
  ],
  DEBIT_CARD: ['DEBITCARD'],
  DIGITAL_WALLET: [
    'PICPAY',
    'NUPAY',
    'MERCADOPAGO',
    'PAYPAL',
    'MPCONNECT',
  ],

  // Brazil local payment
  PIX: ['PIX'],
  BOLETO: ['BOLETO'],

  // Mexico local payment
  SPEI: ['SPEI'],
  OXXO: ['OXXO'],
  OXXO_PAY: ['OXXOPAY'],

  // Colombia local payment
  PSE: ['EFT'],
  EFECTY: ['EFECTY'],
  NEQUI: ['NEQUI'],

  // Peru local payment
  PAGOEFECTIVO: ['PAGOEFECTIVO'],
  SAFETYPAY: ['SAFETYPAY', 'SAFETYPAY-CASH', 'SAFETYPAY-ONLINE'],
};

const EBANX_INTEGRATION_KEY = {
  ONETIME: 'EBANX_INTEGRATION_KEY',
  RECURRING: 'EBANX_RECURRING_INTEGRATION_KEY',
};

const INTERNAL_EBANX_NOTIFICATION_TYPE = {
  PLAN_ZERO_PAYMENT_SUCCESS: 'internal_plan_zero_payment_success',
};

const USER_PAYMENT_TOKEN_STATUS = {
  ACTIVE: 'Active',
  INACTIVE: 'Inactive',
};

const DATES_FOR_REVENUE_CHANGE = {
  AFTER_BETA_COMMUNITIES: new Date(Date.UTC(2022, 10, 25)),
};

const END_OF_BETA_DATE = '2022-11-25';
const NAS_STRUCTURE_LAUNCH_DATE = '2024-04-01';

const DEFAULT_PAYMENT_STRUCTURE = {
  REVENUE_SHARE: 8,
  GST_ON_REVENUE_SHARE: 0,
  GATEWAY_FEE: 4,
  GST_ON_GATEWAY_FEE: 0,
};

const DEFAULT_MAX_INVITE_COUNT = 5000;

const COUNTRY_REGIONS = {
  INDIA: 'INDIA',
  LATAM: 'LATAM',
  NORTH_AMERICA: 'NORTH_AMERICA',
  REST_OF_WORLD: 'ROW',
};

const REGION_ANALYTICS_CATEGORY = {
  LESS_THAN_100: 'LESS_THAN_100',
  FROM_100_TO_1000: 'FROM_100_TO_1000',
  MORE_THAN_1000: 'MORE_THAN_1000',
};

const TEMPLATE_LIBRARY_TYPES = {
  MEMBERSHIP: 'membership',
  EVENT: 'event',
  FOLDER: 'folder',
  COURSE: 'course',
  CHALLENGE: 'challenge',
  SESSION: 'session',
  COMMUNITY: 'community',
  DIGITAL_PRODUCT: 'digitalProduct',
};

const AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES = {
  MEMBERSHIP: TEMPLATE_LIBRARY_TYPES.MEMBERSHIP,
  EVENT: TEMPLATE_LIBRARY_TYPES.EVENT,
  FOLDER: TEMPLATE_LIBRARY_TYPES.FOLDER,
  COURSE: TEMPLATE_LIBRARY_TYPES.COURSE,
  SESSION: TEMPLATE_LIBRARY_TYPES.SESSION,
  CHALLENGE: TEMPLATE_LIBRARY_TYPES.CHALLENGE,
};

const TEMPLATE_SOURCE_TYPE = {
  AI_COFOUNDER: 'AI_COFOUNDER',
  GET_INSPIRED: 'GET_INSPIRED',
};

const AI_TEMPLATE_GENERATION_STATUS = {
  COMPLETED: 'completed',
  PROCESSING: 'processing',
  ERROR: 'error',
  ABORTED: 'aborted',
};

const MEMBERSHIP_TEMPLATE_DEFAULTS = {
  THUMBNAIL:
    'https://d2oi1rqwb0pj00.cloudfront.net/nasIO/portal/get-inspired/membershipDefaultThumbnail.jpg',
  TITLE: 'exclusive-membership',
};

const PURCHASE_TYPE = {
  SUBSCRIPTION: 'SUBSCRIPTION',
  EVENT: 'EVENT',
  FOLDER: 'FOLDER',
  CHALLENGE: 'CHALLENGE',
  REFERRAL_BONUS: 'REFERRAL_BONUS',
  ONE_DOLLAR_GIFT: 'ONE_DOLLAR_GIFT',
  CAMPAIGN_REWARD: 'CAMPAIGN_REWARD',
  SESSION: 'SESSION',
  REFUND: 'REFUND',
  ADJUSTMENT_ADD: 'ADJUSTMENT_ADD',
  ADJUSTMENT_DEDUCT: 'ADJUSTMENT_DEDUCT',
  PAYOUT: 'PAYOUT',
  CHARGEBACK: 'CHARGEBACK',
  MEMBER_PAYOUT: 'MEMBER_PAYOUT',
  ZERO_LINK: 'ZERO_LINK',
  REFERRAL_REWARD: 'REFERRAL_REWARD',
  CREDIT_BALANCE: 'CREDIT_BALANCE',
  ADS_CAMPAIGN_TOPUP: 'ADS_CAMPAIGN_TOPUP',
  ADS_CAMPAIGN_SPEND: 'ADS_CAMPAIGN_SPEND', // campaign ads spend
  ADS_CAMPAIGN_SPEND_REVERT: 'ADS_CAMPAIGN_SPEND_REVERT',
  ADS_CAMPAIGN_RETURN: 'ADS_CAMPAIGN_RETURN',
  ADS_AVAILABLE_TO_OUTGOING: 'ADS_AVAILABLE_TO_OUTGOING',
  ADS_REWARD_ADJUSTMENT_ADD: 'ADS_REWARD_ADJUSTMENT_ADD',
  ADS_REWARD_ADJUSTMENT_DEDUCT: 'ADS_REWARD_ADJUSTMENT_DEDUCT',
};

const CHECKOUT_TYPE = {
  RECURRING: 'recurring',
  ONETIME: 'onetime',
  PLAN_RECURRING: 'plan-recurring',
  TOPUP: 'topup',
};

const PENDING_TRANSACTION_ENTITY_TYPE = {
  SUBSCRIPTION: 'SUBSCRIPTION',
  EVENT: 'EVENT',
  FOLDER: 'FOLDER',
  SESSION: 'SESSION',
  CHALLENGE: 'CHALLENGE',
  DISCOUNT: 'DISCOUNT',
};

const DISCOUNT_STATUS_TYPE = {
  FULLY_REDEEMED: 'FULLY_REDEEMED',
  INACTIVE: 'INACTIVE',
  EXPIRED: 'EXPIRED',
  ACTIVE: 'ACTIVE',
};

const WALLET_TYPE = {
  COMMUNITY: 'community',
  NASIO: 'nasio',
  STRIPE: 'stripe',
  STRIPE_US: 'stripe-us',
  XENDIT: 'xendit',
  STRIPE_INDIA: 'stripe-india',
  RAZORPAY: 'razorpay',
  EBANX: 'ebanx',
  MEMBER: 'member',
};

const SUB_WALLET_TYPE = {
  AVAILABLE: 'available',
  POOL: 'pool',
  GATEWAY_FEE: 'gatewayFee',
  NASIO_FEE: 'nasIOFee',
  GST_ON_GATEWAY_FEE: 'gstOnGatewayFee',
  WHT: 'wht',
  GST_ON_NASIO_FEE: 'gstOnNasioFee',
  REFUND_PROCESSING_FEE: 'refundProcessingFee',
  ADS_CAMPAIGN_PROCESSING_FEE: 'adsCampaignProcessFee',
  AFFILIATE_COMMISSION_AMOUNT: 'affiliateCommissionAmount',
  OUTGOING: 'outgoing',
};

const WALLET_BALANCE_TYPE = {
  BALANCE: 'balance',
  ADS_BALANCE: 'adsBalance',
};

const PAYABLE_PURCHASE_TYPES = [
  PURCHASE_TYPE.SUBSCRIPTION,
  PURCHASE_TYPE.EVENT,
  PURCHASE_TYPE.FOLDER,
  PURCHASE_TYPE.SESSION,
  PURCHASE_TYPE.CHALLENGE,
  PURCHASE_TYPE.ZERO_LINK,
  PURCHASE_TYPE.ADS_CAMPAIGN_TOPUP,
  PRODUCT_TYPE.DIGITAL_FILES,
  PRODUCT_TYPE.COURSE,
];

const ADDON_PURCHASE_TYPES = [
  PURCHASE_TYPE.EVENT,
  PURCHASE_TYPE.FOLDER,
  PURCHASE_TYPE.SESSION,
  PURCHASE_TYPE.CHALLENGE,
  PURCHASE_TYPE.ZERO_LINK,
];

const PRODUCT_PURCHASE_TYPES = [
  PURCHASE_TYPE.FOLDER,
  PURCHASE_TYPE.SESSION,
];

const addonPurchaseTypeMapper = {
  event: PURCHASE_TYPE.EVENT,
  folder: PURCHASE_TYPE.FOLDER,
  session: PURCHASE_TYPE.SESSION,
  challenge: PURCHASE_TYPE.CHALLENGE,
  zeroLink: PURCHASE_TYPE.ZERO_LINK,
};

const CURRENCY_WITH_NON_DECIMAL_POINTS = [
  'BIF',
  'CLP',
  'DJF',
  'GNF',
  'JPY',
  'KMF',
  'KRW',
  'MGA',
  'PYG',
  'RWF',
  'UGX',
  'VND',
  'VUV',
  'XAF',
  'XOF',
  'XPF',
];

const DEFAULT_REVENUE_GOAL = {
  USD: {
    amount: 100000,
    currency: 'USD',
  },
  INR: {
    amount: 8000000,
    currency: 'INR',
  },
};

const WAIVED_STATUS = {
  WAIVED: 'Waived',
  PARTIALLY_WAIVED: 'Partially waived',
  NONE: undefined,
};

const SUBSCRIPTION_TYPE = {
  ANNUAL: 'ANNUAL',
  SEMI_ANNUALLY: 'SEMI_ANNUALLY',
  QUARTERLY: 'QUARTERLY',
  MONTHLY: 'MONTHLY',
  FULL_DISCOUNT: 'FULL_DISCOUNT',
  UNSUBSCRIBED: 'UNSUBSCRIBED',
  NEW_SUBSCRIPTION: 'NEW_SUBSCRIPTION',
  EXISTING_SUBSCRIPTION: 'EXISTING_SUBSCRIPTION',
};

const TRANSACTION_TYPE = {
  INBOUND: 'INBOUND',
  OUTBOUND: 'OUTBOUND',
};

const REQUESTED_REFUND_STATUS = {
  REQUESTED: 'REQUESTED',
  REFUNDED: 'REFUNDED',
};

const TRIAL_DAYS_FOR_APPLICATION_BASED_COMMUNITIES = 365;

const MILESTONE_ACTIVITY_TYPES = {
  MILESTONE_COMMUNITY_CREATED: 'activity-milestone-community-created',
  MILESTONE_FIRST_SALE: 'activity-milestone-first-sale',
  MILESTONE_FIRST_EVENT: 'activity-milestone-first-event',
  MILESTONE_FIRST_CONTENT: 'activity-milestone-first-content',
  MILESTONE_FIRST_POST: 'activity-milestone-first-post',
  MILESTONE_FIRST_SESSION: 'activity-milestone-first-session',
};

const ACTIVITY_TYPES = {
  SUBSCRIPTION_JOINED: 'activity-subscription-joined',
  SUBSCRIPTION_PENDING_APPROVAL: 'activity-subscription-pending-approval',
  SUBSCRIPTION_UNSUBSCRIBED: 'activity-subscription-unsubscribed',
  EVENT_REGISTERED: 'activity-event-registered',
  FOLDER_PURCHASED: 'activity-folder-purchased',
  SESSION_BOOKED: 'activity-session-booked',
  CHALLENGE_REGISTERED: 'activity-challenge-registered',
  ...MILESTONE_ACTIVITY_TYPES,
};

const SUBSCRIPTION_ACTIVITY_TYPES = [
  ACTIVITY_TYPES.SUBSCRIPTION_JOINED,
  ACTIVITY_TYPES.SUBSCRIPTION_PENDING_APPROVAL,
  ACTIVITY_TYPES.SUBSCRIPTION_UNSUBSCRIBED,
];

const ADDON_ACTIVITY_TYPES = [
  ACTIVITY_TYPES.EVENT_REGISTERED,
  ACTIVITY_TYPES.FOLDER_PURCHASED,
  ACTIVITY_TYPES.SESSION_BOOKED,
  ACTIVITY_TYPES.CHALLENGE_REGISTERED,
];

const ADDON_ENTITY_TYPE = {
  FOLDER: 'FOLDER',
  EVENT: 'EVENT',
  SESSION: 'SESSION',
  CHALLENGE: 'CHALLENGE',
};

const ACTIVITY_ENTITY_TYPES = {
  SUBSCRIPTION: 'SUBSCRIPTION',
  ...ADDON_ENTITY_TYPE,
  MILESTONE: 'MILESTONE',
};

const MEMBERSHIP_ACTION_EVENT_TYPES = {
  SUBSCRIPTION_SUCCESS: 'SUBSCRIPTION_SUCCESS',
  SUBSCRIPTION_APPROVED: 'SUBSCRIPTION_APPROVED',
  SUBSCRIPTION_PENDING_APPROVAL: 'SUBSCRIPTION_PENDING_APPROVAL',
  SUBSCRIPTION_RENEWAL: 'SUBSCRIPTION_RENEWAL',
  SUBSCRIPTION_UNSUBSCRIBE: 'SUBSCRIPTION_UNSUBSCRIBE',
  SUBSCRIPTION_REJECTED: 'SUBSCRIPTION_REJECTED',
  SUBSCRIPTION_PAYMENT_FAILURE: 'SUBSCRIPTION_PAYMENT_FAILURE',
  SUBSCRIPTION_REMOVED: 'SUBSCRIPTION_REMOVED',
  SUBSCRIPTION_CANCELLED: 'SUBSCRIPTION_CANCELLED',
  SUBSCRIPTION_CHANGE_PLAN: 'SUBSCRIPTION_CHANGE_PLAN',
};

const ADDON_ACTION_EVENT_TYPES = {
  FOLDER_SIGNUP: 'FOLDER_SIGNUP',
  EVENT_SIGNUP: 'EVENT_SIGNUP',
  SESSION_SIGNUP: 'SESSION_SIGNUP',
  CHALLENGE_SIGNUP: 'CHALLENGE_SIGNUP',
  FOLDER_PAYMENT_FAILURE: 'FOLDER_PAYMENT_FAILURE',
  EVENT_PAYMENT_FAILURE: 'EVENT_PAYMENT_FAILURE',
  SESSION_PAYMENT_FAILURE: 'SESSION_PAYMENT_FAILURE',
  CHALLENGE_PAYMENT_FAILURE: 'CHALLENGE_PAYMENT_FAILURE',
  FOLDER_ACCESS_REMOVED: 'FOLDER_ACCESS_REMOVED',
  EVENT_ACCESS_REMOVED: 'EVENT_ACCESS_REMOVED',
  SESSION_ACCESS_REMOVED: 'SESSION_ACCESS_REMOVED',
  CHALLENGE_ACCESS_REMOVED: 'CHALLENGE_ACCESS_REMOVED',
  ZERO_LINK_SUCCESS: 'ZERO_LINK_SUCCESS',
  ZERO_LINK_PAYMENT_FAILURE: 'ZERO_LINK_PAYMENT_FAILURE',
};

const TRANSACTION_ACTION_EVENT_TYPE = {
  TRANSACTION_CHARGED: 'TRANSACTION_CHARGED',
  TRANSACTION_REFUNDED: 'TRANSACTION_REFUNDED',
  TRANSACTION_CHARGEBACK: 'TRANSACTION_CHARGEBACK',
};

const PRODUCT_ACTIVITY_ENTITY_TYPES = [
  ACTIVITY_ENTITY_TYPES.FOLDER,
  ACTIVITY_ENTITY_TYPES.SESSION,
];

const DISCOUNT_VALUE_TYPE = {
  PERCENTAGE: 'percentage',
  FIXED: 'fixed',
};

const DISCOUNT_TYPE = {
  FOREVER: 'FOREVER',
  INTERVAL: 'INTERVAL',
  FREE_TRIAL: 'FREE_TRIAL',
};

const SUBSCRIPTION_ACTION_TYPE = {
  SUCCESS_NEW: 'SUCCESS_NEW',
  SUCCESS: 'SUCCESS',
  FAILURE: 'FAILURE',
  CANCEL: 'CANCEL',
};

const REFERRAL_SHARE_PERCENTAGE = 50;
const REFERRAL_DURATION_LIMIT_IN_MONTHS = 6;

const MEMBERSHIP_ACTIVITY_KEYS = {
  REGEX_IDENTIFIER: 'membership-activity',
  INTERESTED_IN_PAID_MEMBERSHIP:
    'membership-activity-interested-in-paid-membership',
  INTERESTED_IN: 'membership-activity-interested-in',
  LEAD_CREATED: 'membership-activity-lead-created',
  MEMBERSHIP_APPLIED: 'membership-activity-membership-applied',
  MEMBERSHIP_APPROVED: 'membership-activity-membership-approved',
  MEMBERSHIP_REJECTED: 'membership-activity-membership-rejected',
  MEMBERSHIP_JOINED: 'membership-activity-membership-joined',
  MEMBERSHIP_RENEWED: 'membership-activity-membership-renewed',
  MEMBERSHIP_UNSUBSCRIBED: 'membership-activity-membership-unsubscribed',
  ADDON_PRODUCT_PURCHASED: 'membership-activity-addon-product-purchased',
  ADDON_SESSION_BOOKED: 'membership-activity-addon-session-booked',
  ADDON_CHALLENGE_JOINED: 'membership-activity-addon-challenge-joined',
  ADDON_EVENT_REGISTERED: 'membership-activity-addon-event-registered',
  ADDON_EVENT_APPLIED: 'membership-activity-addon-event-applied',
  ADDON_EVENT_APPROVED: 'membership-activity-addon-event-approved',
  ADDON_EVENT_REJECTED: 'membership-activity-addon-event-rejected',
  INTERVAL_MONTHLY: 'membership-activity-interval-monthly',
  INTERVAL_QUANTERLY: 'membership-activity-interval-quarterly',
  INTERVAL_SEMI_ANNUAL: 'membership-activity-interval-semi-annual',
  INTERVAL_ANNUAL: 'membership-activity-interval-annual',
  ZERO_LINK_PURCHASED: 'membership-activity-zero-link-purchased',
  FLEXIBLE_PAYMENT: 'membership-activity-flexible-payment',
};

const PAYOUT_ID_TYPE = {
  SOCIAL_SECURITY_ID_CUIL: 'social_security_id_cuil',
  TAX_ID_CULT: 'tax_id_cult',
  NATIONAL_ID_CC: 'national_id_cc',
  NATIONAL_ID_CI: 'national_id_ci',
  PASSPORT: 'passport',
  TAX_ID_NIT: 'tax_id_nit',
  FOREIGN_ID_CE: 'foreign_id_ce',
  TAX_ID_RUT: 'tax_id_rut',
  TAX_ID_CI: 'tax_id_ci',
  FOREIGN_ID_DE: 'foreign_id_de',
  NATIONAL_ID_DNI: 'national_id_dni',
  TAX_ID_RUC: 'tax_id_ruc',
};

const PAYOUT_ACCOUNT_CATEGORY = {
  SAVING_ACCOUNT: 'saving_account',
  CHECKING_ACCOUNT: 'checking_account',
  VISTA_ACCOUNT: 'vista_account',
  MAESTRA_ACCOUNT: 'maestra_account',
};

const PAYMENT_RESPONSE_ACTION = {
  NONE: 'NONE',
  REDIRECT: 'REDIRECT',
};

const PRICE_TYPE = {
  FIXED: 'FIXED',
  FLEXIBLE: 'FLEXIBLE',
};

const ABANDONED_CARTS_STATUS = {
  LEAD: 'LEAD',
  PURCHASED: 'PURCHASED',
};

const NOTIFICATION_PREFERENCE_MANAGER_MAIL_TARGET = {
  MANAGER: 'MANAGER',
  MEMBER: 'MEMBER',
};

const VERIFY_PAYMENT_ACCESS_TYPE = {
  EMAIL: 'EMAIL',
  DIRECT: 'DIRECT',
};

const ACCESS_TOKEN_TYPE = {
  CHECKOUT: 'CHECKOUT',
  EMAIL: 'EMAIL',
  LOGIN: 'LOGIN',
};

const AFFILIATE_STATUS = {
  ACTIVE: 'ACTIVE',
  REMOVED: 'REMOVED',
};

const UPSELL_SOURCE_ENTITY_TYPE = {
  SUBSCRIPTION: 'SUBSCRIPTION',
  EVENT: 'EVENT',
  FOLDER: 'FOLDER',
  SESSION: 'SESSION',
  CHALLENGE: 'CHALLENGE',
  CHECKPOINT: 'CHECKPOINT',
};

const DISCOUNT_CATEGORY = {
  GENERAL: 'GENERAL',
  UPSELL: 'UPSELL',
};

const MAX_PAGINATION_LIMIT = 500;

const SIGNUP_REQUESTOR = {
  SIGNUP: 'signupRequestor',
  UPSELL_EMAIL: 'upsellEmailRequestor',
  ABANDONED_CARTS_EMAIL: 'abandonedCartsEmailRequestor',
  PRODUCT_CARD_EMAIL: 'productCardEmailRequestor',
  PRODUCT_CARD_FEED: 'productCardFeedRequestor',
  PRODUCT_CARD_WA: 'productCardWhatsappRequestor',
};

const MEMBER_PAYOUT_SOURCE = {
  AFFILIATE: 'AFFILIATE',
};

const WEBHOOK_TRIGGER_TYPE = {
  NEW_MEMBER: 'NEW_MEMBER',
  MEMBER_RENEW: 'MEMBER_RENEW',
  MEMBER_CANCELLED: 'MEMBER_CANCELLED',
  MEMBER_UNSUBSCRIBED: 'MEMBER_UNSUBSCRIBED',
  ADDON_ACCESS_GRANTED: 'ADDON_ACCESS_GRANTED',
  ADDON_ACCESS_REMOVED: 'ADDON_ACCESS_REMOVED',
  TRANSACTION_REFUNDED: 'TRANSACTION_REFUNDED',
  MEMBER_CHANGE_PLAN: 'MEMBER_CHANGE_PLAN',
};

const WEBHOOK_STATUS = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  PAUSE: 'PAUSE',
};

const WEBHOOK_SOURCE = {
  ZAPIER_APP: 'ZAPIER_APP',
};

const WEBHOOK_LOG_STATUS = {
  PENDING: 'PENDING',
  PROCESSING: 'PROCESSING',
  SUCCESS: 'SUCCESS',
  CANCELLED: 'CANCELLED',
  RETRY_LIMIT_REACHED: 'RETRY_LIMIT_REACHED',
};

const EMAIL_PROVIDER = {
  SENDGRID: 'sendgrid',
  KLAVIYO: 'KLAVIYO',
};

const FEE_CALCULATION_VERSION = {
  V2: 2,
};

const BATCH_METADATA_MODEL_TYPE = {
  MEMBERSHIP: 'MEMBERSHIP',
  EVENT_ATTENDEE: 'EVENT_ATTENDEE',
  FOLDER_VIEWER: 'FOLDER_VIEWER',
  SESSION_ATTENDEE: 'SESSION_ATTENDEE',
  PROGRAM_PARTICIPANT: 'PROGRAM_PARTICIPANT',
  SUBSCRIPTION: 'SUBSCRIPTION',
};

const SOCIAL_MEDIA_TYPES = {
  INSTAGRAM: 'instagram',
  YOUTUBE: 'youtube',
  TIKTOK: 'tiktok',
  TWITTER: 'twitter',
  SNAPCHAT: 'snapchat',
  DISCORD: 'discord',
  LINKEDIN: 'linkedin',
  FACEBOOK: 'facebook',
};

const SUPPORTED_SOCIAL_MEDIA_TYPES_FOR_TEMPLATES = [
  SOCIAL_MEDIA_TYPES.INSTAGRAM,
  SOCIAL_MEDIA_TYPES.YOUTUBE,
  SOCIAL_MEDIA_TYPES.TIKTOK,
  SOCIAL_MEDIA_TYPES.FACEBOOK,
];

const SOCIAL_MEDIA_DOMAINS = {
  INSTAGRAM: 'https://www.instagram.com',
  YOUTUBE: 'https://www.youtube.com',
  TIKTOK: 'https://www.tiktok.com',
  TWITTER: 'https://x.com',
  SNAPCHAT: 'https://www.snapchat.com',
  LINKEDIN: 'https://www.linkedin.com',
  FACEBOOK: 'https://www.facebook.com',
};

const ANNOUNCEMENT_VISIBILITY = {
  ALL: 'ALL',
  NAS_PRO_ONLY: 'NAS_PRO_ONLY',
  NON_NAS_PRO_ONLY: 'NON_NAS_PRO_ONLY',
};

const ANNOUNCEMENT_MARKET = {
  LATAM: 'LATAM',
  US: 'US',
  INR: 'INR',
  ROW: 'ROW',
};

const INTERVALS = {
  YEAR: 'year',
  MONTH: 'month',
  DAY: 'day',
  WEEK: 'week',
};

// Feature ID mapping (Enum-like)
const FEATURE_LIST_ID = {
  GET_INSPIRED: 1,
  ZERO_LINK: 2,
  MAGIC_ADS: 3,
  CUSTOM_PRODUCT_EMAILS: 4,
  MAGIC_REACH_PRODUCT_EMBED: 5,
  EMBED_BUSINESS_WIDGETS: 6,
  AFFILIATES: 7,
  PIXEL_TRACKING: 8,
  EVENT_QR_CODE: 9,
  TRAFFIC_ANALYTICS: 10,
  STORAGE: 11,
  MEMBER: 12,
  MANAGER: 13,
  MAGIC_REACH_WHATSAPP_MSG: 14,
  AI_COFOUNDER_MESSAGE: 15,
  AI_COFOUNDER_PRODUCT_CREATION: 16,
  PRODUCT_PUBLISH: 17,
};

// Feature Name mapping (Enum-like)
const FEATURE_LIST_NAME = {
  GET_INSPIRED: 'GET_INSPIRED',
  ZERO_LINK: 'ZERO_LINK',
  MAGIC_ADS: 'MAGIC_ADS',
  CUSTOM_PRODUCT_EMAILS: 'CUSTOM_PRODUCT_EMAILS',
  MAGIC_REACH_PRODUCT_EMBED: 'MAGIC_REACH_PRODUCT_EMBED',
  EMBED_BUSINESS_WIDGETS: 'EMBED_BUSINESS_WIDGETS',
  AFFILIATES: 'AFFILIATES',
  PIXEL_TRACKING: 'PIXEL_TRACKING',
  EVENT_QR_CODE: 'EVENT_QR_CODE',
  TRAFFIC_ANALYTICS: 'TRAFFIC_ANALYTICS',
  STORAGE: 'STORAGE',
  MEMBER: 'MEMBER',
  MANAGER: 'MANAGER',
  MAGIC_REACH_WHATSAPP_MSG: 'MAGIC_REACH_WHATSAPP_MSG',
  AI_COFOUNDER_MESSAGE: 'AI_COFOUNDER_MESSAGE',
  AI_COFOUNDER_PRODUCT_CREATION: 'AI_COFOUNDER_PRODUCT_CREATION',
  PRODUCT_PUBLISH: 'PRODUCT_PUBLISH',
};

// Map FEATURE_LIST_ID values to FEATURE_LIST_NAME values
const FEATURE_LIST_ID_TO_NAME = Object.fromEntries(
  Object.entries(FEATURE_LIST_ID).map(([key, value]) => [
    value,
    FEATURE_LIST_NAME[key],
  ])
);

module.exports = {
  DEVELOPMENT: 'development',
  STAGING: 'staging',
  PRODUCTION: 'production',
  ANNOUNCEMENT_VISIBILITY,
  ANNOUNCEMENT_MARKET,
  MEMBERSHIP_ACTIVITY_KEYS,
  NAS_STRUCTURE_LAUNCH_DATE,
  DATES_FOR_REVENUE_CHANGE,
  CHAT_PLATFORMS,
  PAYMENT_PROVIDER,
  PAYMENT_TYPE,
  PERCENTAGE_DEDUCTION,
  SOCIAL_CHAT_CONNECTION_TYPES,
  DEFAULT_COUNTRY,
  DEFAULT_CURRENCY,
  DEFAULT_CONVERSION_MULTIPLIER,
  BILLING_CYCLE_DAYS,
  SECONDS_IN_A_DAY,
  eventTimeIntervalInMinutes,
  eventTimeIntervalEnum,
  eventTimeInterval,
  unitStatuses,
  subunitOfferStatuses,
  exerciseTypesMap,
  thresholdProgress,
  progressiveUnlockingThreshold,
  completeProgress,
  userStatus,
  courseOfferStatuses,
  courseEnrolmentStatuses,
  assignmentStatuses,
  certificateStatuses,
  sortingOrder,
  dateFilter,
  monthNameMapping,
  sortParams,
  sortOrderRegex,
  studentSortByRegex,
  sortingOrderMap,
  ctaBtnTextToTrainerSpecificFieldMap,
  defaultExerciseCtaIcon,
  defaultExerciseCtaText,
  customErrorCodes,
  BASIC_CUSTOM_CARDS,
  BASIC_CUSTOM_CARDS_DATA,
  BASIC_CUSTOM_CARDS_TYPES,
  TODAY_PAGE_HIDDEN_CARDS,
  generalAlumniFacebookLink,
  thresholdFreeTrialProgressInMS,
  waveSelectionMailCourses,
  COURSE_MODES,
  COURSE_OFFER_TYPES,
  ON_DEMAND_MODES,
  ON_DEMAND_TYPES,
  LIVE_SESSION_DICT,
  LIVE_SESSION_TYPES,
  FILTERED_LIVE_SESSION_TYPES,
  ZERO_DAY_CARDS,
  ZERO_DAY_CARD_TYPES,
  COURSES_DROPDOWN_DISPLAY,
  FIRST_NAME_REGEX,
  ZERO_DAY_UNIT_OFFER_ID,
  COHORT_DEFAULT_EXERCISE_CTA_ICON,
  LIVE_MODES,
  LIVE_OFFER_TYPES,
  FREE_WORKSHOP_FB_GROUP_LINK,
  LEGACY_LIVE_SESSION_TYPES,
  VIDEO_LINK_REGEX,
  NASG_VIDEO_LINK_REGEX,
  FOCUS_VIEW_CUSTOM_CARDS,
  NFT_CHECK_COURSE_LIST,
  NFT_SUB_UNIT_CATEGORY_CHECK_LIST,
  eventStatuses,
  communityEnrolmentStatuses,
  COURSES_WITH_ZERO_PRICE,
  COMMUNITY_SHORT_CODE_LEN,
  COMMUNITY_MEMBERSHIPS,
  COMMUNITY_SUBSCRIPTION_STATUSES,
  COMMUNITY_APPLICATION_STATUS,
  INTERNAL_EMAIL_DOMAINS,
  RANDOM_PROFILE_PIC_URL_PREFIX,
  ADMIN_EDITABLE_FIELDS_IN_COMMUNITY_APPLICATION,
  COMMUNITY_MEMBER_TYPES,
  CALENDLY_EVENT_TYPES,
  PUBLIC_ENDPOINT_SUFFIXES,
  FILEASSETBUCKET,
  VIDEOASSETBUCKET,
  IMAGEASSETBUCKET,
  IMAGEASSETBUCKET_CLOUDFRONT_BASE_URL,
  IMAGEASSETBUCKET_S3_BASE_URL,
  NASACADEMYBUCKET_CLOUDFRONT_BASE_URL,
  NASG_CLOUDFRONT_BASE_URL,
  FILEASSETBUCKET_CLOUDFRONT_BASE_URL,
  EVENT_TYPES,
  COMMUNITY_HOSTS_ROLE,
  MOBILE_NOTIFICATION_PHASE,
  DEFAULT_COMMUNITY_BANNER_IMAGE,
  DEFAULT_COMMUNITY_PROFILE_IMAGE,
  DEFAULT_COMMUNITY_HOST_PROFILE_IMAGE,
  MOBILE_NOTIFICATION_TYPES,
  MOBILE_NOTIFICATION_RECIPIENTS_SCOPES,
  MAX_FILE_UPLOAD_SIZE,
  CONTRACT_TYPES,
  NETWORK_TYPES,
  CONFIG_TYPES,
  COMMUNITY_INVITED_USERS_STATUSES,
  REACTION_TYPES,
  REACTION_ENTITY_COLLECTIONS,
  REACTION_STATES,
  ANONYMOUS_COMMUNITY_USER_NAME,
  COMMENT_TYPES,
  COMMENT_CATEGORIES,
  COMMENT_ENTITY_COLLECTIONS,
  DEFAULT_MAX_INVITE_COUNT,
  PURCHASE_TYPE,
  CURRENCY_WITH_NON_DECIMAL_POINTS,
  DEFAULT_REVENUE_GOAL,
  DEFAULT_PAYMENT_STRUCTURE,
  WAIVED_STATUS,
  SUBSCRIPTION_TYPE,
  TRANSACTION_TYPE,
  PAYABLE_PURCHASE_TYPES,
  ADDON_PURCHASE_TYPES,
  PRODUCT_PURCHASE_TYPES,
  REQUESTED_REFUND_STATUS,
  VERIFY_PAYMENT_ACCESS_TYPE,
  ACCESS_TOKEN_TYPE,
  TRIAL_DAYS_FOR_APPLICATION_BASED_COMMUNITIES,
  ACTIVITY_TYPES,
  ACTIVITY_ENTITY_TYPES,
  MEMBERSHIP_ACTION_EVENT_TYPES,
  ADDON_ACTION_EVENT_TYPES,
  MILESTONE_ACTIVITY_TYPES,
  addonPurchaseTypeMapper,
  END_OF_BETA_DATE,
  POST_APPROVAL_PROCESS_TYPE,
  ADDON_ENTITY_TYPE,
  SUBSCRIPTION_ACTIVITY_TYPES,
  ADDON_ACTIVITY_TYPES,
  PRODUCT_ACTIVITY_ENTITY_TYPES,
  SESSION_TYPES,
  INTERNAL_EBANX_NOTIFICATION_TYPE,
  EBANX_SUPPORTED_CURRENCY,
  EBANX_RECURRING_PAYMENT_TYPE,
  EBANX_PAYMENT_TYPE,
  EBANX_INTEGRATION_KEY,
  DISCOUNT_VALUE_TYPE,
  DISCOUNT_TYPE,
  USER_PAYMENT_TOKEN_STATUS,
  SUBSCRIPTION_ACTION_TYPE,
  REFERRAL_SHARE_PERCENTAGE,
  REFERRAL_DURATION_LIMIT_IN_MONTHS,
  WALLET_TYPE,
  SUB_WALLET_TYPE,
  MAX_QUANTITY_PER_PURCHASE,
  PAYOUT_ID_TYPE,
  PAYOUT_ACCOUNT_CATEGORY,
  DISCOUNT_STATUS_TYPE,
  PENDING_TRANSACTION_ENTITY_TYPE,
  PAYMENT_RESPONSE_ACTION,
  PRICE_TYPE,
  UPSELL_SOURCE_ENTITY_TYPE,
  DISCOUNT_CATEGORY,
  MAX_PAGINATION_LIMIT,
  SIGNUP_REQUESTOR,
  ANNOUNCEMENT_LOCALIZATION_KEY,
  ABANDONED_CARTS_STATUS,
  AFFILIATE_STATUS,
  NOTIFICATION_PREFERENCE_MANAGER_MAIL_TARGET,
  MEMBER_PAYOUT_SOURCE,
  WEBHOOK_TRIGGER_TYPE,
  WEBHOOK_STATUS,
  WEBHOOK_SOURCE,
  WEBHOOK_LOG_STATUS,
  TRANSACTION_ACTION_EVENT_TYPE,
  EMAIL_PROVIDER,
  ANNOUNCEMENT_WHATSAPP_TEMPLATE_TYPE,
  BATCH_METADATA_MODEL_TYPE,
  SOCIAL_MEDIA_TYPES,
  SOCIAL_MEDIA_DOMAINS,
  TEMPLATE_LIBRARY_TYPES,
  AI_COFOUNDER_TEMPLATE_LIBRARY_TYPES,
  TEMPLATE_SOURCE_TYPE,
  AI_TEMPLATE_GENERATION_STATUS,
  MEMBERSHIP_TEMPLATE_DEFAULTS,
  COUNTRY_REGIONS,
  REGION_ANALYTICS_CATEGORY,
  SUPPORTED_SOCIAL_MEDIA_TYPES_FOR_TEMPLATES,
  FEE_CALCULATION_VERSION,
  DEFAULT_COUNTRY_CODE,
  PAYOUT_GATEWAY,
  CHECKOUT_TYPE,
  WALLET_BALANCE_TYPE,
  INTERVALS,
  FEATURE_LIST_ID,
  FEATURE_LIST_NAME,
  FEATURE_LIST_ID_TO_NAME,
};
