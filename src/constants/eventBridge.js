const REDIS_CACHE_ACTION = {
  FLUSH: 'flush',
};

const SCHEDULER_GROUP_NAME = {
  ACTION_EVENT_QUEUE: 'ActionEventQueue',
  CHAT_NOTIFICATION_QUEUE: 'ChatNotificationQueue',
  REDIS_CACHE_QUEUE: 'RedisCacheQueue',
  ABANDONED_CHECKOUT_UPDATE_QUEUE: 'AbandonedCheckoutUpdateQueue',
  SUBSCRIPTION_UPDATER_QUEUE: 'SubscriptionUpdaterQueue',
  GENERAL_PURPOSE_QUEUE: 'GeneralPurposeQueue',
};

const TASK_TYPE = {
  CUSTOM_API: 'custom_api',
  LANDING_PAGE_CACHE_REVALIDATION: 'landing_page_cache_revalidation',
};

module.exports = {
  REDIS_CACHE_ACTION,
  SCHEDULER_GROUP_NAME,
  TASK_TYPE,
};
