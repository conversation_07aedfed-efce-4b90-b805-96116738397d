const COVER_MEDIA_TYPES = {
  IMAGE: 'image',
  VIDEO: 'video',
};

const COVER_MEDIA_ENTITY_TYPES = {
  COMMUNITY: 'community',
  EVENT: 'event',
  SESSION: 'session',
  FOLDER: 'folder',
  CHALLENGE: 'challenge',
  DIGITAL_FILES: 'digital_files',
  COURSE: 'course',
};

const FOLDER_ITEM_COVER_MEDIA_FOLDER_TYPES = {
  COMMUNITY: 'community_cover_video',
  CHALLENGE: 'challenge_cover_video',
  EVENT: 'community_event_cover_video',
  SESSION: 'session_cover_video',
  FOLDER: 'folder_cover_video',
};

module.exports = {
  COVER_MEDIA_TYPES,
  COVER_MEDIA_ENTITY_TYPES,
  FOLDER_ITEM_COVER_MEDIA_FOLDER_TYPES,
};
