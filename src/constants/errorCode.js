/**
 * Each feature should have a unique range
 * Please help comment the start/end range for each feature
 */

/**
 * 1000 - 4999
 */
const GENERIC_ERROR = {
  INVALID_LINK: {
    name: 'INVALID_LINK',
    code: 1000,
    localizationKey: 'invalid-link',
  },
  INVALID_LINK_FOR_TYPE: {
    name: 'INVALID_LINK_FOR_TYPE',
    code: 1001,
    localizationKey: 'invalid-link-for-type',
  },
  INVALID_PHONE_NUMBER: {
    name: 'INVALID_PHONE_NUMBER',
    code: 1003,
    localizationKey: 'invalid-phone-number',
  },
  PASSWORD_NOT_STRONG_ENOUGH: {
    name: 'PASSWORD_NOT_STRONG_ENOUGH',
    code: 1006,
    localizationKey: 'password-not-strong-enough',
  },
  INCORRECT_CURRENT_PASSWORD: {
    name: 'INCORRECT_CURRENT_PASSWORD',
    code: 1007,
    localizationKey: 'incorrect-current-password',
  },
  PASSWORD_CHANGE: {
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>_CHANGE',
    code: 1008,
    localizationKey: 'password-change',
  },
  SOCIAL_LOG_IN_OR_FORGOT_PASSWORD: {
    name: 'SOCIAL_LOG_IN_OR_FORGOT_PASSWORD',
    code: 1009,
    localizationKey: 'social-log-in-or-forgot-password',
  },
  SAME_EMAIL_ADDRESS: {
    name: 'SAME_EMAIL_ADDRESS',
    code: 1010,
    localizationKey: 'same-email-address',
  },
  INVALID_TEST_RECIPIENT_ERROR: {
    name: 'INVALID_TEST_RECIPIENT_ERROR',
    code: 1011,
    localizationKey: 'invalid-test-recipient-error',
  },
  USER_ALREADY_ENROLLED: {
    name: 'USER_ALREADY_ENROLLED',
    code: 1012,
    localizationKey: 'user-already-enrolled',
  },
  USER_NOT_ENROLLED: {
    name: 'USER_NOT_ENROLLED',
    code: 1013,
    localizationKey: 'user-not-enrolled',
  },
  REQUIRE_APPROVAL: {
    name: 'REQUIRE_APPROVAL',
    code: 1014,
    localizationKey: 'require-approval',
  },
  PAYMENT_FAILURE: {
    name: 'PAYMENT_FAILURE',
    code: 1015,
    localizationKey: 'payment-failure',
  },
  CM_NOT_ELIGIBLE_TO_PURCHASE: {
    name: 'CM_NOT_ELIGIBLE_TO_PURCHASE',
    code: 1016,
    localizationKey: 'cm-not-eligible-to-purchase',
  },
  JWT_TOKEN_EXPIRY_ERROR: {
    name: 'JWT_TOKEN_EXPIRY_ERROR',
    code: 1017,
    localizationKey: 'jwt-token-expiry-error',
  },
  PAYMENT_FAILURE_3DS_FAILED: {
    name: 'PAYMENT_FAILURE_3DS_FAILED',
    code: 1018,
    localizationKey: 'payment-failure-3ds-failed',
  },
  PAYMENT_FAILURE_BANK_DECLINE: {
    name: 'PAYMENT_FAILURE_BANK_DECLINE',
    code: 1019,
    localizationKey: 'payment-failure-bank-decline',
  },
  PAYMENT_FAILURE_INSUFFICIENT_FUNDS: {
    name: 'PAYMENT_FAILURE_INSUFFICIENT_FUNDS',
    code: 1020,
    localizationKey: 'payment-failure-insufficient-funds',
  },
  PAYMENT_FAILURE_HIGH_RISK_TRANSACTION: {
    name: 'PAYMENT_FAILURE_HIGH_RISK_TRANSACTION',
    code: 1021,
    localizationKey: 'payment-failure-high-risk-transaction',
  },
  INVALID_LINK_DUE_TO_UPPERCASE: {
    name: 'INVALID_LINK_DUE_TO_UPPERCASE',
    code: 1022,
    localizationKey: 'invalid-link-due-to-uppercase',
  },
  INVALID_LINK_DUE_TO_EMPTY_SPACE: {
    name: 'INVALID_LINK_DUE_TO_EMPTY_SPACE',
    code: 1023,
    localizationKey: 'invalid-link-due-to-empty-space',
  },
  PAYMENT_FAILURE_INCORRECT_CUSTOMER_DATA: {
    name: 'PAYMENT_FAILURE_INCORRECT_CUSTOMER_DATA',
    code: 1024,
    localizationKey: 'payment-failure-incorrect_customer_data',
  },
  PAYMENT_FAILURE_INVALID_CARD_OR_CARD_TYPE: {
    name: 'PAYMENT_FAILURE_INVALID_CARD_OR_CARD_TYPE',
    code: 1025,
    localizationKey: 'payment-failure-invalid-card-or-card-type',
  },
  PAYMENT_FAILURE_TIMEOUT: {
    name: 'PAYMENT_FAILURE_TIMEOUT',
    code: 1026,
    localizationKey: 'payment-failure-timeout',
  },
  PAYMENT_FAILURE_NO_RESPONSE_FROM_ACQUIRER: {
    name: 'PAYMENT_FAILURE_NO_RESPONSE_FROM_ACQUIRER',
    code: 1027,
    localizationKey: 'payment-failure-no-response-from-acquirer',
  },
  PAYMENT_FAILURE_ACQUIRER_RESPONSE_ERROR: {
    name: 'PAYMENT_FAILURE_ACQUIRER_RESPONSE_ERROR',
    code: 1028,
    localizationKey: 'payment-failure-acquirer-response-error',
  },
  PAYMENT_FAILURE_BROKEN_COMMUNICATION: {
    name: 'PAYMENT_FAILURE_BROKEN_COMMUNICATION',
    code: 1029,
    localizationKey: 'payment-failure-broken-communication',
  },
  PAYMENT_FAILURE_COMMUNICATION_MISMATCH: {
    name: 'PAYMENT_FAILURE_COMMUNICATION_MISMATCH',
    code: 1030,
    localizationKey: 'payment-failure-communication-mismatch',
  },
  PAYMENT_FAILURE_UNKNOWN_ACQUIRER_ERROR: {
    name: 'PAYMENT_FAILURE_UNKNOWN_ACQUIRER_ERROR',
    code: 1031,
    localizationKey: 'payment-failure-unknown-acquirer-error',
  },
  PAYMENT_FAILURE_CANNOT_PROCESS_TRANSACTION_AT_THIS_MOMENT: {
    name: 'PAYMENT_FAILURE_CANNOT_PROCESS_TRANSACTION_AT_THIS_MOMENT',
    code: 1032,
    localizationKey:
      'payment-failure-cannot-process-transaction-at-this-moment',
  },
  PAYMENT_FAILURE_NOT_ACCEPTED: {
    name: 'PAYMENT_FAILURE_NOT_ACCEPTED',
    code: 1033,
    localizationKey: 'payment-failure-not-accepted',
  },
  SELECTED_AMOUNT_LESS_THAN_MIN_AMOUNT: {
    name: 'SELECTED_AMOUNT_LESS_THAN_MIN_AMOUNT',
    code: 1034,
    localizationKey: 'selected-amount-less-than-min-amount',
  },
  UPSELL_GENERIC_ERROR: {
    name: 'UPSELL_GENERIC_ERROR',
    code: 1035,
    localizationKey: 'upsell-generic-error',
  },
  PAYMENT_IN_PROCESSING_ERROR: {
    name: 'PAYMENT_IN_PROCESSING_ERROR',
    code: 1036,
    localizationKey: 'payment-in-processing-error',
  },
  PAYMENT_IN_PENDING_ERROR: {
    name: 'PAYMENT_IN_PENDING_ERROR',
    code: 1037,
    localizationKey: 'payment-in-pending-error',
  },
  PAYPAL_CHANGE_PLAN_ERROR: {
    name: 'PAYPAL_CHANGE_PLAN_ERROR',
    code: 1038,
    localizationKey: 'paypal-change-plan-error',
  },
  COMMUNITY_KYC_PENDING_ERROR: {
    name: 'COMMUNITY_KYC_PENDING_ERROR',
    code: 1039,
    localizationKey: 'community-kyc-pending-error',
  },
  RISKY_ACTION_ERROR: {
    name: 'RISKY_ACTION_ERROR',
    code: 1040,
    localizationKey: 'fraud-block-action-error',
  },
  PAYMENT_FAILURE_GATEWAY_REJECT: {
    name: 'PAYMENT_FAILURE_GATEWAY_REJECT',
    code: 1041,
    localizationKey: 'payment-failure-gateway-reject',
  },
  MEMBER_NOT_ELIGIBLE_TO_PURCHASE: {
    name: 'MEMBER_NOT_ELIGIBLE_TO_PURCHASE',
    code: 1042,
    localizationKey: 'member-not-eligible-to-purchase',
  },
  USER_NO_ACTIVE_SUBSCRIPTION: {
    name: 'USER_NO_ACTIVE_SUBSCRIPTION',
    code: 1043,
    localizationKey: 'you-are-not-part-of-any-community-yet',
  },
  OTP_REQUIRED: {
    name: 'OTP_REQUIRED',
    code: 1044,
    localizationKey: 'otp-required',
  },
  INVALID_COMMUNITY_REFERRAL_LINK: {
    name: 'INVALID_COMMUNITY_REFERRAL_LINK',
    code: 1045,
    localizationKey: 'invalid-community-referral-link',
  },
  CANNOT_USE_OWN_COMMUNITY_REFERRAL_LINK: {
    name: 'CANNOT_USE_OWN_COMMUNITY_REFERRAL_LINK',
    code: 1046,
    localizationKey: 'cannot-use-own-community-referral-link',
  },
};

/**
 * 5000 - 5999
 */
const COMMUNITY_ERROR = {
  INVALID_PRICE: {
    name: 'INVALID_PRICE',
    code: 5000,
    localizationKey: 'invalid-price',
  },
  INVALID_COMMUNITY: {
    name: 'INVALID_COMMUNITY',
    code: 5001,
    localizationKey: 'invalid-community',
  },
  CANNOT_ADD_COMMUNITY_APPLICATION: {
    name: 'CANNOT_ADD_COMMUNITY_APPLICATION',
    code: 5002,
    localizationKey: 'cannot-add-community-application',
  },
  APPLICATION_HAVE_PAYMENT_IN_PROCESSING_ERROR: {
    name: 'APPLICATION_HAVE_PAYMENT_IN_PROCESSING_ERROR',
    code: 5003,
    localizationKey: 'application-have-payment-in-processing-error',
  },
  CANNOT_ADD_COMMUNITY_APPROVAL: {
    name: 'CANNOT_ADD_COMMUNITY_APPROVAL',
    code: 5004,
    localizationKey: 'cannot-add-community-approval',
  },
  TAKEN_LINK: {
    name: 'TAKEN_LINK',
    code: 5005,
    localizationKey: 'taken-link',
  },
};

/**
 * 6000 - 6999
 */
const EVENT_ERROR = {
  INVALID_EVENT_LIVE_LINK: {
    name: 'INVALID_EVENT_LIVE_LINK',
    code: 6000,
    localizationKey: 'invalid-event-live-link',
  },
  INVALID_EVENT_RECORDING_LINK: {
    name: 'INVALID_EVENT_RECORDING_LINK',
    code: 6001,
    localizationKey: 'invalid-event-recording-link',
  },
  DUPLICATE_EVENT_SLUG: {
    name: 'DUPLICATE_EVENT_SLUG',
    code: 6002,
    localizationKey: 'duplicate-event-slug',
  },
  INVALID_CHAT_GROUP_LINK: {
    name: 'INVALID_CHAT_GROUP_LINK',
    code: 6003,
    localizationKey: 'invalid-event-chat-group-link',
  },
  INVALID_EVENT: {
    name: 'INVALID_EVENT',
    code: 6004,
    localizationKey: 'invalid-event-event',
  },
  CANNOT_REGISTER_PAST_EVENT: {
    name: 'CANNOT_REGISTER_PAST_EVENT',
    code: 6005,
    localizationKey: 'cannot-register-past-event',
  },
  CANNOT_REGISTER_PAID_EVENT: {
    name: 'CANNOT_REGISTER_PAID_EVENT',
    code: 6006,
    localizationKey: 'cannot-register-paid-event',
  },
  LEARNER_ALREADY_REGISTERED_FOR_THE_EVENT: {
    name: 'LEARNER_ALREADY_REGISTERED_FOR_THE_EVENT',
    code: 6007,
    localizationKey: 'learner-already-registered-for-the-event',
  },
  EVENT_CLOSED: {
    name: 'EVENT_CLOSED',
    code: 6008,
    localizationKey: 'event-closed',
  },
  EVENT_SOLD_OUT: {
    name: 'EVENT_SOLD_OUT',
    code: 6009,
    localizationKey: 'event-sold-out',
  },
  EVENT_CAPACITY_LIMIT_REACHED: {
    name: 'EVENT_CAPACITY_LIMIT_REACHED',
    code: 6010,
    localizationKey: 'event-capacity-limit-reached',
  },
  CANNOT_ADD_EVENT_APPLICATION: {
    name: 'CANNOT_ADD_EVENT_APPLICATION',
    code: 6011,
    localizationKey: 'cannot-add-event-application',
  },
  EVENT_PURCHASE_QUANTITY_LIMIT_REACHED: {
    name: 'EVENT_PURCHASE_QUANTITY_LIMIT_REACHED',
    code: 6012,
    localizationKey: 'event-purchase-quantity-limit-reached',
  },
  EVENT_SET_MAX_PURCHASE_QUANTITY_LIMIT_REACHED: {
    name: 'EVENT_SET_MAX_PURCHASE_QUANTITY_LIMIT_REACHED',
    code: 6013,
    localizationKey: 'event-set-max-purchase-quantity-limit-reached',
  },
  CANNOT_ADD_EVENT_APPROVAL: {
    name: 'CANNOT_ADD_EVENT_APPROVAL',
    code: 6014,
    localizationKey: 'cannot-add-event-approval',
  },
};

/**
 * 7000 - 7999
 */
const MEMBERSHIP_ERROR = {
  INVITE_LIMIT_REACHED: {
    name: 'INVITE_LIMIT_REACHED',
    code: 7000,
    localizationKey: 'invite-limit-reached',
  },
  MOBILE_SUBSCRIPTION_NOT_ABLE_TO_REMOVE: {
    name: 'MOBILE_SUBSCRIPTION_NOT_ABLE_TO_REMOVE',
    code: 7001,
    localizationKey: 'mobile-subscription-not-able-to-remove',
  },
  INVITE_FRAUD_NOT_TRANSPARENT: {
    name: 'INVITE_FRAUD_NOT_TRANSPARENT',
    code: 7002,
    localizationKey: 'invite-fraud-not-transparent',
  },
};

/**
 * 10000 - 10999
 */
const MAGIC_REACH_ERROR = {
  WA_DM_LIMIT_REACHED: {
    name: 'WA_DM_LIMIT_REACHED',
    code: 10000,
    localizationKey: 'magic-reach-send-limit-reached',
  },
};

/**
 * 11000 - 11999
 */
const DISCOUNT_ERROR = {
  DISCOUNT_CODE_EXISTS: {
    name: 'DISCOUNT_CODE_EXISTS',
    code: 11000,
    localizationKey: 'discount-code-exists',
  },
  DISCOUNT_CODE_OVER_MAX_REDEMPTION: {
    name: 'DISCOUNT_CODE_OVER_MAX_REDEMPTION',
    code: 11001,
    localizationKey: 'discount-code-over-max-redemption',
  },
  DISCOUNT_CODE_NOT_ACTIVE: {
    name: 'DISCOUNT_CODE_NOT_ACTIVE',
    code: 11002,
    localizationKey: 'discount-code-not-active',
  },
  DISCOUNT_CODE_BEFORE_EFFECTIVE_START_TIME: {
    name: 'DISCOUNT_CODE_BEFORE_EFFECTIVE_START_TIME',
    code: 11003,
    localizationKey: 'discount-code-before-effective-start-time',
  },
  DISCOUNT_CODE_AFTER_EFFECTIVE_END_TIME: {
    name: 'DISCOUNT_CODE_AFTER_EFFECTIVE_END_TIME',
    code: 11004,
    localizationKey: 'discount-code-after-effective-end-time',
  },
  INVALID_DISCOUNT_CODE: {
    name: 'INVALID_DISCOUNT_CODE',
    code: 11005,
    localizationKey: 'invalid-discount-code',
  },
  DISCOUNT_CODE_FOR_WRONG_INTERVAL_COUNT: {
    name: 'DISCOUNT_CODE_FOR_WRONG_INTERVAL_COUNT',
    code: 11006,
    localizationKey: 'discount-code-for-wrong-interval-count',
  },
  INVALID_DISCOUNT_CODE_DUE_TO_MIN_AMOUNT: {
    name: 'INVALID_DISCOUNT_CODE_DUE_TO_MIN_AMOUNT',
    code: 11007,
    localizationKey: 'invalid-discount-code-due-to-min-amount',
  },
  INVALID_DISCOUNT_CODE_FORMAT: {
    name: 'INVALID_DISCOUNT_CODE_FORMAT',
    code: 11008,
    localizationKey: 'invalid-discount-code-format',
  },
  INVALID_DISCOUNT_CODE_FOR_FREE_TRIAL: {
    name: 'INVALID_DISCOUNT_CODE_FOR_FREE_TRIAL',
    code: 11009,
    localizationKey: 'invalid-discount-code-for-free-trial',
  },
  INVALID_DISCOUNT_CODE_FOR_WRONG_ENTITY: {
    name: 'INVALID_DISCOUNT_CODE_FOR_WRONG_ENTITY',
    code: 11010,
    localizationKey: 'invalid-discount-code-for-wrong-entity',
  },
};

/**
 * 12000 - 12999
 */
const WHATSAPP_ERROR = {
  INVALID_WHATSAPP_NUMBER: {
    name: 'INVALID_WHATSAPP_NUMBER',
    code: 12000,
    localizationKey: 'invalid-whatsapp-number',
  },
  INVALID_WHATSAPP_BUSINESS_NUMBER: {
    name: 'INVALID_WHATSAPP_BUSINESS_NUMBER',
    code: 12001,
    localizationKey: 'invalid-whatsapp-business-number',
  },
  PHONE_NUMBER_TAKEN_ERROR: {
    name: 'PHONE_NUMBER_TAKEN_ERROR',
    code: 12002,
    localizationKey: 'phone-number-registered-with-another-learner',
  },
};

/**
 * 13000 - 13999
 */
const PRODUCT_ERROR = {
  DUPLICATE_SESSION_SLUG: {
    name: 'DUPLICATE_SESSION_SLUG',
    code: 13000,
    localizationKey: 'duplicate-event-slug',
  },
  AVAILABILITY_REQUIRED: {
    name: 'AVAILABILITY_REQUIRED',
    code: 13001,
    localizationKey: 'availability-required-session',
  },
  INTERVALS_REQUIRED: {
    name: 'INTERVALS_REQUIRED',
    code: 13002,
    localizationKey: 'intervals-required-session',
  },
  INTERVALS_OVERLAPPING: {
    name: 'INTERVALS_OVERLAPPING',
    code: 13003,
    localizationKey: 'intervals-overlapping-session',
  },
  INTERVAL_DURATION_INCORRECT: {
    name: 'INTERVAL_DURATION_INCORRECT',
    code: 13004,
    localizationKey: 'interval-duration-incorrect-session',
  },
  ALREADY_BOOKED: {
    name: 'ALREADY_BOOKED',
    code: 13005,
    localizationKey: 'already-booked-session',
  },
  UPCOMING_BOOKING_EXISTS_ALREADY: {
    name: 'UPCOMING_BOOKING_EXISTS_ALREADY',
    code: 13006,
    localizationKey: 'upcoming-booking-exists-already-session',
  },
  BOOKINGS_NOT_ALLOWED: {
    name: 'BOOKINGS_NOT_ALLOWED',
    code: 13007,
    localizationKey: 'bookings-not-allowed-session',
  },
  NOT_AVAILABLE: {
    name: 'NOT_AVAILABLE',
    code: 13008,
    localizationKey: 'not-available-session',
  },
  FOLDER_SOLD_OUT: {
    name: 'FOLDER_SOLD_OUT',
    code: 13009,
    localizationKey: 'folder-sold-out',
  },
  FOLDER_NOT_PUBLISHED: {
    name: 'FOLDER_NOT_PUBLISHED',
    code: 13010,
    localizationKey: 'folder-not-published',
  },
  SESSION_NOT_PUBLISHED: {
    name: 'SESSION_NOT_PUBLISHED',
    code: 13011,
    localizationKey: 'session-not-published',
  },
  SESSION_LOCATION_REQUIRED: {
    name: 'SESSION_LOCATION_REQUIRED',
    code: 13012,
    localizationKey: 'session-location-required',
  },
};

/**
 * 14000 - 14999
 */
const PROGRAM_ERROR = {
  DUPLICATE_SLUG: {
    name: 'DUPLICATE_SLUG',
    code: 14000,
    localizationKey: 'duplicate-slug',
  },
  INVALID_CHAT_GROUP_LINK: {
    name: 'INVALID_CHAT_GROUP_LINK',
    code: 14001,
    localizationKey: 'invalid-program-chat-group-link',
  },
  PAST_PROGRAM_ITEM_SUBMISSION_NOT_MODIFIABLE: {
    name: 'PAST_PROGRAM_ITEM_SUBMISSION_NOT_MODIFIABLE',
    code: 14002,
    localizationKey: 'past-program-item-submission-not-modifiable',
  },
  CANNOT_JOIN_CHALLENGE_AFTER_STARTED: {
    name: 'CANNOT_JOIN_CHALLENGE_AFTER_STARTED',
    code: 14003,
    localizationKey: 'cannot-join-challenge-after-started',
  },
  CANNOT_JOIN_CHALLENGE_AFTER_ENDED: {
    name: 'CANNOT_JOIN_CHALLENGE_AFTER_ENDED',
    code: 14004,
    localizationKey: 'cannot-join-challenge-after-ended',
  },
  CANNOT_JOIN_CHALLENGE_AFTER_REGISTRATION_CLOSED: {
    name: 'CANNOT_JOIN_CHALLENGE_AFTER_REGISTRATION_CLOSED',
    code: 14005,
    localizationKey: 'cannot-join-challenge-after-registration-closed',
  },
  CHALLENGE_NOT_PUBLISHED: {
    name: 'CHALLENGE_NOT_PUBLISHED',
    code: 14006,
    localizationKey: 'challenge-not-published',
  },
  CHALLENGE_CAPACITY_LIMIT_REACHED: {
    name: 'CHALLENGE_CAPACITY_LIMIT_REACHED',
    code: 14007,
    localizationKey: 'challenge-capacity-limit-reached',
  },
};

/**
 * 15000 - 15999
 */
const CALENDAR_ERROR = {
  GOOGLE_SCOPE_NOT_PROVIDED: {
    name: 'GOOGLE_SCOPE_NOT_PROVIDED',
    code: 15000,
    localizationKey: 'google-scope-not-provided',
  },
  GOOGLE_CONNECTION_FAILED: {
    name: 'GOOGLE_CONNECTION_FAILED',
    code: 15001,
    localizationKey: 'google-connection-failed',
  },
  GOOGLE_DISCONNECTED: {
    name: 'GOOGLE_DISCONNECTED',
    code: 15002,
    localizationKey: 'google-disconnected',
  },
};

/**
 * 16000 - 16999
 */
const ANNOUNCEMENET_ERROR = {
  PINNED_POST_LIMIT_REACHED: {
    name: 'PINNED_POST_LIMIT_REACHED',
    code: 16000,
    localizationKey: 'pinned-post-limit-reached',
  },
};

/**
 * 17000 - 17999
 */
const ZERO_LINK_ERROR = {
  ZERO_LINK_HAS_TRANSACTIONS: {
    name: 'ZERO_LINK_HAS_TRANSACTIONS',
    code: 17000,
    localizationKey: 'zero-link-has-transactions',
  },
};

/**
 * 18000 - 18999
 */
const COMMUNITY_REFERRAL_ERROR = {
  COMMUNITY_ALREADY_EXISTS_IN_ANOTHER_TEMPLATE: {
    name: 'COMMUNITY_ALREADY_EXISTS_IN_ANOTHER_TEMPLATE',
    code: 18000,
    localizationKey: 'community-already-exists-in-another-template',
  },
};

/**
 * 19000 - 19999 Feature Permission Errors
 */
const FEATURE_PERMISSION_ERROR = {
  STORAGE_LIMIT_EXCEEDED: {
    name: 'STORAGE_LIMIT_EXCEEDED',
    code: 19001,
    localizationKey: 'storage-limit-exceeded',
  },
  STORAGE_NOT_ALLOWED: {
    name: 'STORAGE_NOT_ALLOWED',
    code: 19002,
    localizationKey: 'storage-not-allowed',
  },
  FEATURE_NOT_ALLOWED: {
    name: 'FEATURE_NOT_AVAILABLE',
    code: 19003,
    localizationKey: 'feature-not-allowed',
  },
  FEATURE_LIMIT_REACHED: {
    name: 'FEATURE_LIMIT_REACHED',
    code: 19004,
    localizationKey: 'feature-limit-reached',
  },
  MAGIC_REACH_WHATSAPP_NOT_ALLOWED: {
    name: 'MAGIC_REACH_WHATSAPP_NOT_ALLOWED',
    code: 19005,
    localizationKey: 'magic-reach-whatsapp-not-allowed',
  },
  MAGIC_REACH_WHATSAPP_LIMIT_EXCEEDED: {
    name: 'MAGIC_REACH_WHATSAPP_LIMIT_EXCEEDED',
    code: 19006,
    localizationKey: 'magic-reach-whatsapp-limit-exceeded',
  },
  MANAGER_LIMIT_EXCEEDED: {
    name: 'MANAGER_LIMIT_EXCEEDED',
    code: 19007,
    localizationKey: 'manager-limit-exceeded',
  },
  MEMBER_LIMIT_EXCEEDED: {
    name: 'MEMBER_LIMIT_EXCEEDED',
    code: 19008,
    localizationKey: 'member-limit-exceeded',
  },
  MEMBER_LIMIT_EXCEEDED_SIGNUP: {
    name: 'MEMBER_LIMIT_EXCEEDED_SIGNUP',
    code: 19009,
    localizationKey: 'member-limit-exceeded-signup',
  },
};

module.exports = {
  CALENDAR_ERROR,
  GENERIC_ERROR,
  COMMUNITY_ERROR,
  MEMBERSHIP_ERROR,
  MAGIC_REACH_ERROR,
  DISCOUNT_ERROR,
  EVENT_ERROR,
  WHATSAPP_ERROR,
  PRODUCT_ERROR,
  PROGRAM_ERROR,
  ANNOUNCEMENET_ERROR,
  ZERO_LINK_ERROR,
  COMMUNITY_REFERRAL_ERROR,
  FEATURE_PERMISSION_ERROR,
};
