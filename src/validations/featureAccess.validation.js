const CommunityModel = require('../communitiesAPI/models/community.model');
const FeaturePermissionManager = require('../services/common/featurePermissionManager.service');
const {
  ForbiddenError,
  ResourceNotFoundError,
  ParamError,
} = require('../utils/error.util');

exports.featureAccessValidator =
  (featureId, projection = {}) =>
  async (req, res, next) => {
    const { communityId } = req.params;
    if (!communityId) {
      const err = new ParamError('CommunityId not provided in params');
      return next(err);
    }
    const communityProjection = projection;
    communityProjection.code = 1;
    communityProjection.config = 1;
    communityProjection.isActive = 1;
    communityProjection.baseCurrency = 1;
    communityProjection.featurePermissions = 1;
    const community = await CommunityModel.findById(
      communityId,
      communityProjection
    ).lean();
    if (!community || !community.isActive) {
      const err = new ResourceNotFoundError('Community not found');
      return next(err);
    }
    const planType = community.config?.planType;
    const featurePermissions = community.featurePermissions || [];

    const featurePermissionManager = new FeaturePermissionManager(
      planType,
      featurePermissions
    );
    // Adjust logic over here
    const isAuthorized = featurePermissionManager.isFeatureAllowed(
      featureId,
      community.baseCurrency
    );

    if (!isAuthorized) {
      const err = new ForbiddenError(
        'You do not have access to this feature'
      );
      return next(err);
    }
    delete community._version;
    req.metadata = { community };
    next();
  };
