const yup = require('yup');
const {
  COVER_MEDIA_TYPES,
} = require('../constants/coverMediaItems.constant');

const coverMediaItemSchema = yup.object().shape({
  mediaType: yup
    .string()
    .trim()
    .oneOf(Object.values(COVER_MEDIA_TYPES))
    .required(),
  imgSrc: yup.string().when('mediaType', {
    is: COVER_MEDIA_TYPES.IMAGE,
    then: yup.string().url().required(),
    otherwise: yup.string().strip(),
  }),
  unsplashMetadata: yup.object().notRequired(),
  folderItemId: yup.string().when('mediaType', {
    is: COVER_MEDIA_TYPES.VIDEO,
    then: yup.string().trim().required(),
    otherwise: yup.string().strip(),
  }),
});

const coverMediaItemsSchema = yup
  .array()
  .of(coverMediaItemSchema)
  .min(1, 'At least one cover media item is required')
  .max(10, 'Maximum of 10 cover media items allowed')
  .notRequired();

module.exports = {
  coverMediaItemsSchema,
};
