const { NAS_IO_FRONTEND_URL } = require('../config');
const logger = require('../services/logger.service');

const entityTypeToPrefixMap = {
  folder: '/products',
  event: '/events',
  session: '/products',
  course: '/products',
  digital_files: '/products',
  challenge: '/challenges',
};

exports.getEntityPublicPageUrl = ({
  communitySlug = '',
  entityType = '',
  entitySlug = '',
  withBasepath = true,
}) => {
  const entityTypePrefix = entityTypeToPrefixMap[entityType];

  if (!entityTypePrefix) {
    const errMsg = `Invalid entity type ${entityType}`;
    logger.error(errMsg);
    throw new Error(errMsg);
  }

  const entityTypePrefixWithoutSlash = entityTypeToPrefixMap[entityType]
    .replace(/\//g, '')
    .toLocaleLowerCase();
  const communitySlugWithoutSlash = communitySlug
    .replace(/\//g, '')
    .toLocaleLowerCase();
  const entitySlugWithoutSlash = entitySlug
    .replace(/\//g, '')
    .toLocaleLowerCase();

  const path = `/${communitySlugWithoutSlash}/${entityTypePrefixWithoutSlash}/${entitySlugWithoutSlash}`;

  return withBasepath ? `${NAS_IO_FRONTEND_URL}${path}` : path;
};
