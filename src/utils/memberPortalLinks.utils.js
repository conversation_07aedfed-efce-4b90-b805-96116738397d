const axios = require('axios');
const {
  NAS_IO_FRONTEND_URL,
  NEXT_JS_SECRET_TOKEN,
  GENERAL_PURPOSE_TASK_QUEUE_URL,
} = require('../config');
const logger = require('../services/logger.service');
const { sendMessageToSQSQueue } = require('../handlers/sqs.handler');
const { TASK_TYPE } = require('../constants/eventBridge');

const ENTITY_LANDING_PAGE = {
  DIGITAL_PRODUCT: 'digital_product',
  COURSE: 'course',
  EVENT: 'event',
  SESSION: 'session',
  CHALLENGE: 'challenge',
};

const entityTypeToPrefixMap = {
  [ENTITY_LANDING_PAGE.DIGITAL_PRODUCT]: '/digital-files',
  [ENTITY_LANDING_PAGE.COURSE]: '/courses',
  [ENTITY_LANDING_PAGE.EVENT]: '/events',
  [ENTITY_LANDING_PAGE.SESSION]: '/sessions',
  [ENTITY_LANDING_PAGE.CHALLENGE]: '/challenges',
};

exports.ENTITY_LANDING_PAGE = ENTITY_LANDING_PAGE;

exports.purgeFECacheSync = async (path) => {
  try {
    const pathToValidate = `${NAS_IO_FRONTEND_URL}/api/revalidate?path=${path}&secret=${NEXT_JS_SECRET_TOKEN}`;
    await axios(pathToValidate);
  } catch (err) {
    logger.error(
      'Something went wrong while revalidating landing page',
      err
    );
  }
};

const sendPurgeRequestToQueue = async (path) => {
  try {
    if (!path) return;

    await sendMessageToSQSQueue({
      queueUrl: GENERAL_PURPOSE_TASK_QUEUE_URL,
      messageBody: {
        tasks: [
          {
            type: TASK_TYPE.LANDING_PAGE_CACHE_REVALIDATION,
            taskDetails: {
              path,
            },
          },
        ],
      },
    });
    logger.info('Sent landing page purge request to queue for ', path);
  } catch (err) {
    logger.error(
      'Something went wrong while sending purge request to queue',
      err
    );
  }
};

exports.getEntityPublicPageUrl = ({
  communitySlug = '',
  entityType = '',
  entitySlug = '',
  withBasepath = true,
}) => {
  const entityTypePrefix = entityTypeToPrefixMap[entityType];

  if (!entityTypePrefix) {
    const errMsg = `Invalid entity type ${entityType}`;
    logger.error(errMsg);
    throw new Error(errMsg);
  }

  const entityTypePrefixWithoutSlash = entityTypeToPrefixMap[entityType]
    .replace(/\//g, '')
    .toLocaleLowerCase();
  const communitySlugWithoutSlash = communitySlug
    .replace(/\//g, '')
    .toLocaleLowerCase();
  const entitySlugWithoutSlash = entitySlug
    .replace(/\//g, '')
    .toLocaleLowerCase();

  const path = `/${communitySlugWithoutSlash}/${entityTypePrefixWithoutSlash}/${entitySlugWithoutSlash}`;

  return withBasepath ? `${NAS_IO_FRONTEND_URL}${path}` : path;
};

exports.purgeCommunityLandingPageCache = async (community) => {
  try {
    if (community.link) {
      await sendPurgeRequestToQueue(community.link);
    }
  } catch (e) {
    logger.error('Error purging cache for community', e);
  }
};

exports.purgeEntityLandingPageCache = async (params) => {
  try {
    const {
      community,
      entityType,
      entitySlug,
      purgeCommunityLandingPage = true,
    } = params;

    const linksToPurge = [];

    if (purgeCommunityLandingPage && community.link) {
      linksToPurge.push(community.link);
    }

    const entityLandingPagePath = this.getEntityPublicPageUrl({
      communitySlug: community.link,
      entityType,
      entitySlug,
      withBasepath: false,
    });

    if (entityLandingPagePath) {
      linksToPurge.push(entityLandingPagePath);
    }

    if (linksToPurge.length > 0) {
      await Promise.all(linksToPurge.map(sendPurgeRequestToQueue));
    }
  } catch (e) {
    logger.error('Error purging cache for community', e);
  }
};
