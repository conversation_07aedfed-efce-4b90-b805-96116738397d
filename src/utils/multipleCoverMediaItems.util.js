const { InternalError } = require('./error.util');
const logger = require('../services/logger.service');
const {
  COVER_MEDIA_TYPES,
  COVER_MEDIA_ENTITY_TYPES,
} = require('../constants/coverMediaItems.constant');
const {
  getFolderItemById,
  getFolderItemByIdWithVideoInfo,
} = require('../communitiesAPI/services/common/communityFolderItems/index.service');

const getBannerImageSrcFromMediaItem = async (
  communityId,
  coverMediaItem
) => {
  let bannerImageSrc;
  try {
    switch (coverMediaItem.mediaType) {
      case COVER_MEDIA_TYPES.VIDEO: {
        if (!coverMediaItem.folderItemId) {
          throw new Error('Invalid cover media item');
        }

        // get folder item by id
        const folderItem = await getFolderItemById(
          coverMediaItem.folderItemId,
          true
        );

        // set bannerImageSrc from thumbnail
        bannerImageSrc = folderItem?.thumbnail;
        break;
      }
      case COVER_MEDIA_TYPES.IMAGE: {
        bannerImageSrc = coverMediaItem.imgSrc;
        break;
      }
      default: {
        throw new Error('Invalid cover media item type');
      }
    }
    return bannerImageSrc;
  } catch (error) {
    logger.error(
      `Error getting banner image src from media item for community ${communityId}: `,
      coverMediaItem,
      error,
      error.stack
    );
    throw new InternalError(
      'Error getting banner image src from media item'
    );
  }
};

const getBannerImageSrcFromEntity = ({ entity, entityType }) => {
  let bannerImageSrc = '';

  switch (entityType) {
    case COVER_MEDIA_ENTITY_TYPES.COMMUNITY: {
      bannerImageSrc =
        entity.fullScreenBannerImgData?.mobileImgProps?.src || '';
      break;
    }
    case COVER_MEDIA_ENTITY_TYPES.EVENT: {
      bannerImageSrc = entity.bannerImg || '';
      break;
    }
    case COVER_MEDIA_ENTITY_TYPES.SESSION:
    case COVER_MEDIA_ENTITY_TYPES.FOLDER:
    case COVER_MEDIA_ENTITY_TYPES.DIGITAL_FILES:
    case COVER_MEDIA_ENTITY_TYPES.COURSE: {
      bannerImageSrc = entity.thumbnail || '';
      break;
    }
    case COVER_MEDIA_ENTITY_TYPES.CHALLENGE: {
      bannerImageSrc = entity.cover || '';
      break;
    }
    default: {
      logger.error(
        `[Fn:getBannerImageSrcFromEntity] Invalid entity type`,
        entityType
      );
      throw new InternalError('Invalid entity type');
    }
  }

  return bannerImageSrc;
};

const attachFolderItemsForVideoMediaItems = async ({
  coverMediaItems,
  isCommunityManager = false,
}) => {
  const videoMediaItems = coverMediaItems.filter(
    (item) => item.mediaType === COVER_MEDIA_TYPES.VIDEO
  );

  if (!videoMediaItems.length) return coverMediaItems; // if no videoMediaItems then return

  const folderItemsResults = await Promise.allSettled(
    videoMediaItems.map((item) =>
      getFolderItemByIdWithVideoInfo(item.folderItemId, isCommunityManager)
    )
  );

  const folderItemsMap = folderItemsResults.reduce(
    (acc, folderItemResult) => {
      if (folderItemResult.status === 'rejected') return acc;

      const folderItem = folderItemResult.value;
      acc[folderItem._id] = folderItem;
      return acc;
    },
    {}
  );

  return coverMediaItems.reduce((acc, item) => {
    if (item.mediaType === COVER_MEDIA_TYPES.VIDEO) {
      if (folderItemsMap[item.folderItemId]) {
        acc.push({
          ...item,
          folderItem: folderItemsMap[item.folderItemId],
        });
      }
    } else {
      acc.push(item);
    }
    return acc;
  }, []);
};

const generateCoverMediaItemsFromLegacyFields = async ({
  entity,
  entityType,
  isCommunityManager = false,
}) => {
  const coverMediaItems = [];
  const bannerImageSrc = getBannerImageSrcFromEntity({
    entity,
    entityType,
  });

  // Add cover video for challenge if it exists
  if (
    entityType === COVER_MEDIA_ENTITY_TYPES.CHALLENGE &&
    entity.coverVideo?.mediaObjectId
  ) {
    const [coverVideoMediaItem] =
      await attachFolderItemsForVideoMediaItems({
        coverMediaItems: [
          {
            mediaType: COVER_MEDIA_TYPES.VIDEO,
            folderItemId: entity.coverVideo.mediaObjectId,
          },
        ],
        isCommunityManager,
      });

    coverMediaItems.push(coverVideoMediaItem);
  }

  // Add image cover media item
  if (bannerImageSrc) {
    const coverMediaItem = {
      mediaType: COVER_MEDIA_TYPES.IMAGE,
      imgSrc: bannerImageSrc,
    };

    coverMediaItems.push(coverMediaItem);
  }
  return coverMediaItems;
};

const setBannerImgForLegacyFields = async ({
  communityId,
  entityType,
  newCoverMediaItems,
}) => {
  if (!Array.isArray(newCoverMediaItems) || !newCoverMediaItems.length) {
    logger.error(
      `[Fn:setBannerImgForLegacyFields] Invalid cover media items for community ${communityId}`,
      newCoverMediaItems
    );
    throw new Error('Invalid new cover media items');
  }

  const updateData = {};
  // get banner image src
  const firstMediaItem = newCoverMediaItems[0];
  const bannerImageSrc = await getBannerImageSrcFromMediaItem(
    communityId,
    firstMediaItem
  );

  // set banner image src in legacy fields for entityType
  switch (entityType) {
    case COVER_MEDIA_ENTITY_TYPES.COMMUNITY: {
      updateData.fullScreenBannerImgData = {
        mobileImgProps: { src: bannerImageSrc },
        desktopImgProps: { src: bannerImageSrc },
      };
      break;
    }
    case COVER_MEDIA_ENTITY_TYPES.EVENT: {
      updateData.bannerImg = bannerImageSrc;
      break;
    }
    case COVER_MEDIA_ENTITY_TYPES.SESSION:
    case COVER_MEDIA_ENTITY_TYPES.FOLDER: {
      updateData.thumbnail = bannerImageSrc;
      break;
    }
    case COVER_MEDIA_ENTITY_TYPES.CHALLENGE: {
      updateData.cover = bannerImageSrc;
      break;
    }
    default: {
      logger.error(
        `[Fn:getUpdateDataForMultipleCoverMediaItems] Invalid entity type for community ${communityId}`,
        entityType
      );
      throw new InternalError('Invalid entity type');
    }
  }

  return updateData;
};

// called when creating/updating entity. Fn will set legacy fields from coverMediaItems
exports.getUpdateDataForMultipleCoverMediaItems = async ({
  communityId,
  entityType,
  coverMediaItems,
  // oldCoverMediaItems = [],
}) => {
  const updateData = {
    coverMediaItems,
  };

  // validate coverMediaItems
  if (
    !coverMediaItems ||
    !Array.isArray(coverMediaItems) ||
    !coverMediaItems.length
  ) {
    logger.error('Invalid cover media items: ', coverMediaItems);
    throw new InternalError('Invalid cover media items');
  }

  // update legacy fields to have banner image
  const legacyFieldsUpdateData = await setBannerImgForLegacyFields({
    communityId,
    entityType,
    newCoverMediaItems: coverMediaItems,
  });
  Object.assign(updateData, legacyFieldsUpdateData);

  return updateData;
};

// called when fetching entity.
// Fn will generate coverMediaItems from legacy fields if coverMediaItems is not present
// Fn will fetch full folderItem from folderItemId for all video media types
exports.generateCoverMediaItems = async ({
  entity,
  entityType,
  isCommunityManager = false,
}) => {
  try {
    // if coverMediaItems exist, then add folderItem to video media items and return
    if (
      Array.isArray(entity.coverMediaItems) &&
      entity.coverMediaItems.length
    ) {
      if (!this.hasVideoCoverMediaItems(entity.coverMediaItems)) {
        return entity.coverMediaItems;
      }

      const coverMediaItems = await attachFolderItemsForVideoMediaItems({
        coverMediaItems: entity.coverMediaItems,
        isCommunityManager,
      });

      return coverMediaItems;
    }

    // generate coverMediaItems from legacy fields
    const coverMediaItems = await generateCoverMediaItemsFromLegacyFields({
      entity,
      entityType,
      isCommunityManager,
    });
    return coverMediaItems;
  } catch (error) {
    logger.error(
      `Error generating cover media items for entity ${entityType} ${entity._id}`,
      error,
      error.stack
    );
    throw new InternalError('Error generating cover media items');
  }
};

exports.hasVideoCoverMediaItems = (coverMediaItems = []) => {
  return (
    Array.isArray(coverMediaItems) &&
    coverMediaItems.some(
      (item) => item.mediaType === COVER_MEDIA_TYPES.VIDEO
    )
  );
};

// Generate cover media items from legacy fields to write to db
// Used in migration script
exports.generateCoverMediaItemsForDbWrite = ({ entity, entityType }) => {
  const coverMediaItems = [];
  const bannerImageSrc = getBannerImageSrcFromEntity({
    entity,
    entityType,
  });

  // Add cover video for challenge if it exists
  if (
    entityType === COVER_MEDIA_ENTITY_TYPES.CHALLENGE &&
    entity.coverVideo?.mediaObjectId
  ) {
    const videoCoverMediaItem = {
      mediaType: COVER_MEDIA_TYPES.VIDEO,
      folderItemId: entity.coverVideo.mediaObjectId,
    };
    coverMediaItems.push(videoCoverMediaItem);
  }

  // Add image cover media item
  if (bannerImageSrc) {
    const imgCoverMediaItem = {
      mediaType: COVER_MEDIA_TYPES.IMAGE,
      imgSrc: bannerImageSrc,
    };

    coverMediaItems.push(imgCoverMediaItem);
  }

  return coverMediaItems;
};
