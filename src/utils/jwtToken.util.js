const jwt = require('jsonwebtoken');
const {
  JWT_SECRET_KEY,
  CHECKOUT_TOKEN_SESSION_TYPE,
  EMAIL_TOKEN_SESSION_TYPE,
} = require('../config');
const logger = require('../services/logger.service');
const { ACCESS_TOKEN_TYPE } = require('../constants/common');
const { GENERIC_ERROR } = require('../constants/errorCode');
const { ToUserError, ParamError } = require('./error.util');

function decodeToken(token) {
  try {
    const decodedToken = jwt.verify(token, JWT_SECRET_KEY);

    logger.info(`decodeToken: ${JSON.stringify(decodedToken)}`);

    return decodedToken;
  } catch (err) {
    if (err.name === 'TokenExpiredError') {
      throw new ToUserError(
        'Sorry, the payment session has expired. Please refresh the page.',
        GENERIC_ERROR.JWT_TOKEN_EXPIRY_ERROR
      );
    }

    throw err;
  }
}

exports.retrieveAccessTokenType = (token) => {
  const decodedToken = decodeToken(token);

  const { sessionType } = decodedToken;

  if (!sessionType) {
    return ACCESS_TOKEN_TYPE.LOGIN;
  }

  switch (sessionType) {
    case CHECKOUT_TOKEN_SESSION_TYPE:
      return ACCESS_TOKEN_TYPE.CHECKOUT;
    case EMAIL_TOKEN_SESSION_TYPE:
      return ACCESS_TOKEN_TYPE.EMAIL;
    default:
      throw new ParamError('Invalid session type');
  }
};
