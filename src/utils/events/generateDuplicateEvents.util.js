const { DateTime } = require('luxon');
const {
  MAX_EVENTS,
  END_CONDITION,
  FREQUENCY,
  MONTH_RULE_TYPES,
} = require('../../api/v1/events/constants/eventDuplicationConstants');

const { ParamError } = require('../error.util');
const logger = require('../../services/logger.service');
const larkService = require('../../communitiesAPI/services/common/lark.service');
const { EVENT_DUPLICATION_ALERT_LARK_WEBHOOK } = require('../../config');

// Helper function to convert input to DateTime
const toDateTime = (date) => {
  if (!date) return null;
  if (date instanceof Date) return DateTime.fromJSDate(date);
  if (typeof date === 'string') return DateTime.fromISO(date);
  return date;
};

const validateFrequencyInterval = (frequencyInterval) => {
  if (!frequencyInterval || frequencyInterval < 1) {
    throw new ParamError(
      `Invalid frequency interval: ${frequencyInterval} `
    );
  }
};

const validateDaysOfWeek = (arrDays) => {
  if (!Array.isArray(arrDays)) {
    throw new ParamError('Invalid days of week');
  }

  // if any day is not a number or lt 1 or gt 7, then throw error.
  const invalidDays = arrDays.filter(
    (day) => typeof day !== 'number' || day < 1 || day > 7
  );

  if (invalidDays.length > 0) {
    throw new ParamError(`Invalid days of week ${invalidDays}`);
  }
};

const validateMonthDays = (days) => {
  if (!Array.isArray(days)) {
    throw new ParamError('Invalid days of month');
  }
  const invalidDays = days.filter(
    (day) => typeof day !== 'number' || day < 1 || day > 31
  );
  if (invalidDays.length > 0) {
    throw new ParamError(`Invalid days of month ${invalidDays}`);
  }
};

const validateCustomMonthRules = (customMonthRules) => {
  if (!customMonthRules) {
    throw new ParamError('Invalid custom month rules');
  }

  switch (customMonthRules?.type) {
    case MONTH_RULE_TYPES.NTH_DAY_OF_WEEK: {
      validateDaysOfWeek(customMonthRules.day);
      break;
    }
    case MONTH_RULE_TYPES.SPECIFIC_DAYS: {
      validateMonthDays(customMonthRules.days);
      break;
    }
    default: {
      throw new ParamError('Invalid custom month rule type');
    }
  }
};

const getEventTimeDetails = (event) => {
  const { startTime, endTime, timezoneId } = event;

  // Convert UTC times to the event's timezone
  const eventStartDateTime = toDateTime(startTime).setZone(timezoneId);
  const eventEndDateTime = toDateTime(endTime).setZone(timezoneId);

  return {
    timezoneId,
    eventTime: {
      hour: eventStartDateTime.hour,
      minute: eventStartDateTime.minute,
      second: eventStartDateTime.second,
      millisecond: eventStartDateTime.millisecond,
    },
    eventDuration: eventEndDateTime.diff(eventStartDateTime).milliseconds,
  };
};

const calculateNextDateForSimpleFrequency = (
  currentDate,
  frequency,
  frequencyInterval
) => {
  validateFrequencyInterval(frequencyInterval);

  if (!currentDate.isValid) {
    throw new ParamError('Invalid current date');
  }

  switch (frequency) {
    case FREQUENCY.DAILY:
      return currentDate.plus({ days: frequencyInterval });
    case FREQUENCY.WEEKLY:
      return currentDate.plus({ weeks: frequencyInterval });
    case FREQUENCY.MONTHLY: {
      const originalDay = currentDate.day;
      let nextDate = currentDate.plus({ months: frequencyInterval });

      // If original date was on 31st (or 30th/29th in February's case)
      // and next month doesn't have that many days,
      // keep moving forward until we find a month with enough days
      while (nextDate.daysInMonth < originalDay) {
        nextDate = nextDate.plus({ months: frequencyInterval });
      }

      return nextDate.set({ day: originalDay });
      // return currentDate.plus({ months: frequencyInterval });
    }
    default:
      throw new ParamError(`Invalid frequency: ${frequency}`);
  }
};

const calculateNextWeeklyDate = (
  currentDate,
  daysOfWeek,
  frequencyInterval
) => {
  validateDaysOfWeek(daysOfWeek);
  validateFrequencyInterval(frequencyInterval);

  let nextDate;
  const currentWeekday = currentDate.weekday;
  const shouldAddEvent = daysOfWeek.includes(currentWeekday);

  if (!shouldAddEvent) {
    // Find next valid weekday
    const nextValidDay =
      daysOfWeek.find((d) => d > currentWeekday) || daysOfWeek[0];
    const daysToAdd =
      nextValidDay > currentWeekday
        ? nextValidDay - currentWeekday
        : 7 * frequencyInterval - currentWeekday + nextValidDay;

    nextDate = currentDate.plus({ days: daysToAdd });

    return {
      nextDate,
      shouldAddEvent,
    };
  }

  const nextValidDay =
    daysOfWeek.find((d) => d > currentWeekday) || daysOfWeek[0];
  const daysToAdd =
    nextValidDay > currentWeekday
      ? nextValidDay - currentWeekday
      : 7 * frequencyInterval - currentWeekday + nextValidDay;

  nextDate = currentDate.plus({ days: daysToAdd });

  return {
    nextDate,
    shouldAddEvent,
  };
};

const findMonthlyOccurrences = (currentDate, day) => {
  const occurrences = [];
  let tempDate = currentDate.startOf('month');

  while (tempDate.month === currentDate.month) {
    if (day.includes(tempDate.weekday)) {
      occurrences.push(tempDate);
    }
    tempDate = tempDate.plus({ days: 1 });
  }

  return occurrences;
};

const handleMonthlyNthDayOfWeek = (
  currentDate,
  day,
  nth,
  eventTime,
  frequencyInterval
) => {
  validateDaysOfWeek(day);
  validateFrequencyInterval(frequencyInterval);

  if (!currentDate.isValid) {
    throw new ParamError('Invalid current date');
  }

  const occurrences = findMonthlyOccurrences(currentDate, day);
  const targetIndex = nth > 0 ? nth - 1 : occurrences.length + nth;

  if (occurrences[targetIndex]) {
    return {
      currentDate: occurrences[targetIndex].set(eventTime),
      nextDate: currentDate
        .plus({ months: frequencyInterval })
        .startOf('month'),
      shouldAddEvent: true,
    };
  }

  const nextMonthStartDate = currentDate
    .plus({ months: 1 })
    .startOf('month')
    .set(eventTime);

  return {
    currentDate: nextMonthStartDate,
    nextDate: nextMonthStartDate,
    shouldAddEvent: false,
  };
};

const handleMonthlySpecificDays = (
  currentDate,
  days,
  frequencyInterval
) => {
  validateMonthDays(days);
  validateFrequencyInterval(frequencyInterval);

  if (!currentDate.isValid) {
    throw new ParamError('Invalid current date');
  }

  const currentDateDay = currentDate.day;
  const shouldAddEvent = days.includes(currentDateDay);

  const nextDay = days.find(
    (d) => d > currentDateDay && d <= currentDate.daysInMonth
  );
  const nextDate = nextDay
    ? currentDate.set({ day: nextDay })
    : currentDate
        .plus({ months: frequencyInterval })
        .set({ day: days[0] });

  return { currentDate, nextDate, shouldAddEvent };
};

const calculateNextMonthlyDate = ({
  currentDate,
  customMonthRules,
  eventTime,
  frequencyInterval,
}) => {
  validateCustomMonthRules(customMonthRules);

  if (frequencyInterval === 2) {
    console.log('frequencyInterval is 2');
  }

  const { type, day, nth, days } = customMonthRules;

  switch (type) {
    case MONTH_RULE_TYPES.NTH_DAY_OF_WEEK:
      return handleMonthlyNthDayOfWeek(
        currentDate,
        day,
        nth,
        eventTime,
        frequencyInterval
      );

    case MONTH_RULE_TYPES.SPECIFIC_DAYS:
      return handleMonthlySpecificDays(
        currentDate,
        days,
        frequencyInterval
      );

    default: {
      throw new ParamError('Invalid custom month rule type');
    }
  }
};

const createNewEvent = (event, currentDate, eventDuration) => {
  const newStartTime = currentDate.toJSDate();
  const newEndTime = new Date(newStartTime.getTime() + eventDuration);

  const newEvent = {
    ...event,
    startTime: newStartTime.toISOString(),
    endTime: newEndTime.toISOString(),
    duplicationParentId: event._id,
  };

  // Delete unwanted keys
  const keysToDelete = [
    '_id',
    'timeBeforeStartTime',
    'isActive',
    'status',
    'slug',
    'isSoldOut',
    'eventId',
    'icsFileLink',
    'shortUrl',
    'createdAt',
    'chatGroupLink',
    'eventId',
    'discordEventId',
    'earningAnalytics',
  ];

  keysToDelete.forEach((key) => delete newEvent[key]);

  return newEvent;
};

const getInitialCurrentDate = (minStartDate, eventTime, timezoneId) => {
  let currentDate = toDateTime(minStartDate)
    .setZone(timezoneId)
    .set(eventTime);

  // if current date is less than minStartDate, add 1 day
  if (currentDate < toDateTime(minStartDate)) {
    currentDate = currentDate.plus({ days: 1 });
  }

  return currentDate;
};

const sendAlertToLark = async ({ header, contentList }) => {
  const webhookLink = EVENT_DUPLICATION_ALERT_LARK_WEBHOOK;

  const template = larkService.generateMessageTemplate(header);
  const content = contentList.map((item) =>
    larkService.generateTextPayload(item.title, item.value)
  );

  const payload = await larkService.insertContentToTemplate(
    template,
    content
  );
  const result = await larkService.sendPushNotificationToLark(
    webhookLink,
    payload
  );

  return result;
};

const sendMaxLoopCountReachedAlert = async ({
  event,
  duplicationCriteria,
}) => {
  try {
    const header = '[Fn:generateDuplicateEvents] Max loop count reached';
    const contentList = [
      {
        title: 'Event ID: ',
        value: `${event._id}`,
      },
      {
        title: 'Event Title: ',
        value: event.title,
      },
      {
        title: 'Event Start Time: ',
        value: toDateTime(event.startTime).toISO() || '',
      },
      {
        title: 'Event End Time: ',
        value: toDateTime(event.endTime).toISO() || '',
      },
      {
        title: 'Duplication Criteria: ',
        value: JSON.stringify(duplicationCriteria, null, 2),
      },
    ];
    sendAlertToLark({ header, contentList });
  } catch (e) {
    logger.error(
      'Error in sending max loop count reached alert to lark',
      e,
      e.stack
    );
  }
};

const generateDuplicateEvents = (event, duplicationCriteria) => {
  const {
    minStartDate,
    isCustomFrequency,
    frequency,
    frequencyInterval = 1,
    endCondition,
    maxOccurrences,
    endDate,
    daysOfWeek,
    customMonthRules,
  } = duplicationCriteria;

  const { timezoneId, eventTime, eventDuration } =
    getEventTimeDetails(event);
  const events = [];

  let currentDate = getInitialCurrentDate(
    minStartDate,
    eventTime,
    timezoneId
  );
  let nextDate = currentDate;

  if (!currentDate.isValid || !nextDate.isValid) {
    throw new Error('Invalid start date');
  }

  const endDateTime =
    endCondition === END_CONDITION.SPECIFIC_DATE
      ? toDateTime(endDate).setZone(timezoneId)
      : null;

  const maxEventsToGenerate = Math.min(
    maxOccurrences || MAX_EVENTS,
    MAX_EVENTS
  );

  const MAX_LOOP_COUNT = 50; // failsafe for infinite loop
  let loopCount = 0;

  while (
    events.length < maxEventsToGenerate &&
    loopCount < MAX_LOOP_COUNT // failsafe
  ) {
    loopCount++;

    if (loopCount > MAX_LOOP_COUNT) {
      logger.error(
        `[Fn:generateDuplicateEvents] Max loop count reached for event id: ${event._id} and criteria: ${duplicationCriteria}`
      );

      return {
        events: [],
        maxLoopCountReached: true,
      };
    }

    if (
      endCondition === END_CONDITION.SPECIFIC_DATE &&
      currentDate >= endDateTime
    ) {
      break;
    }

    const isFirstEvent = events.length === 0;
    let shouldAddEvent = false;

    if (!isCustomFrequency) {
      // Simple frequency case
      shouldAddEvent = true;
      if (!isFirstEvent) {
        currentDate = calculateNextDateForSimpleFrequency(
          currentDate,
          frequency,
          frequencyInterval
        );
      }
      nextDate = currentDate;
    } else {
      // custom frequency case
      switch (frequency) {
        case FREQUENCY.DAILY: {
          shouldAddEvent = true;
          if (!isFirstEvent) {
            currentDate = calculateNextDateForSimpleFrequency(
              currentDate,
              frequency,
              frequencyInterval
            );
          }
          nextDate = currentDate;
          break;
        }
        case FREQUENCY.WEEKLY: {
          const result = calculateNextWeeklyDate(
            currentDate,
            daysOfWeek,
            frequencyInterval
          );

          ({ nextDate, shouldAddEvent } = result);

          if (!shouldAddEvent) {
            currentDate = nextDate;
            // eslint-disable-next-line no-continue
            continue;
          }
          break;
        }
        case FREQUENCY.MONTHLY: {
          const result = calculateNextMonthlyDate({
            currentDate,
            customMonthRules,
            eventTime,
            frequencyInterval,
          });

          ({ currentDate, nextDate, shouldAddEvent } = result);

          if (!shouldAddEvent) {
            currentDate = nextDate;
            // eslint-disable-next-line no-continue
            continue;
          }
          break;
        }
        default: {
          throw new ParamError(`Invalid frequency: ${frequency}`);
        }
      }
    }

    // add event to array if applicable
    if (
      shouldAddEvent &&
      currentDate >= toDateTime(minStartDate) &&
      (endDateTime ? currentDate <= endDateTime : true)
    ) {
      const newEvent = createNewEvent(event, currentDate, eventDuration);
      events.push(newEvent);

      currentDate = nextDate; // for next loop
    }

    if (events.length >= MAX_EVENTS) break;
  }

  return {
    events,
    maxLoopCountReached: loopCount >= MAX_LOOP_COUNT,
  };
};

module.exports = {
  generateDuplicateEvents,
  getEventTimeDetails,
  calculateNextDateForSimpleFrequency,
  calculateNextWeeklyDate,
  calculateNextMonthlyDate,
  createNewEvent,
  sendMaxLoopCountReachedAlert,
};
