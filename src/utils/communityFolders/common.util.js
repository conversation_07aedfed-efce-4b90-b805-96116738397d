const { PRIVATE_VIDEO_BASE_URL } = require('../../config');
const {
  NASG_VIDEO_LINK_REGEX,
  NASG_CLOUDFRONT_BASE_URL,
  VIDEO_LINK_REGEX,
} = require('../../constants/common');

const { regexReplace } = require('../string_handling');

const getCorrectLinkUponRegexReplace = (link) => {
  if (link) {
    let newLink = regexReplace(
      link,
      NASG_VIDEO_LINK_REGEX,
      NASG_CLOUDFRONT_BASE_URL
    ).replace(/\s/g, '');
    newLink = regexReplace(
      newLink,
      VIDEO_LINK_REGEX,
      PRIVATE_VIDEO_BASE_URL
    ).replace(/\s/g, '');

    return newLink;
  }

  return null;
};

module.exports = {
  getCorrectLinkUponRegexReplace,
};
