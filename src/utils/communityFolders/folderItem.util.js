const {
  communityLibraryStatusMap,
} = require('../../communitiesAPI/constants');
const { getCorrectLinkUponRegexReplace } = require('./common.util');

exports.commonFilters = {
  NON_MANAGER_VIEW: {
    status: communityLibraryStatusMap.PUBLISHED,
  },
  MANAGER_VIEW: {
    status: {
      $nin: [
        communityLibraryStatusMap.DELETED,
        communityLibraryStatusMap.DRAFT,
      ],
    },
  },
};

exports.fixFolderItemLinks = (folderItemData) => {
  const formattedFolderItemData = { ...folderItemData };

  // fix links for following keys
  const keysToFix = ['thumbnail', 'link', 'mp4Link'];
  keysToFix.forEach((key) => {
    if (folderItemData[key]) {
      formattedFolderItemData[key] = getCorrectLinkUponRegexReplace(
        folderItemData[key]
      );
    }
  });

  return formattedFolderItemData;
};

exports.fixVideoDataLinks = (video) => {
  const formattedVideo = { ...video };

  // fix links for following keys
  const keysToFix = ['thumbnailLink', 'link', 'hlsLink'];
  keysToFix.forEach((key) => {
    if (video[key]) {
      formattedVideo[key] = getCorrectLinkUponRegexReplace(video[key]);
    }
  });

  return formattedVideo;
};
