const strongRegex = new RegExp(
  '^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.{8,})'
);

exports.isPasswordStrong = (password) => {
  return strongRegex.test(password);
};

exports.generateRandomPassword = (length = 10) => {
  const uppercaseChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const lowercaseChars = 'abcdefghijklmnopqrstuvwxyz';
  const digitChars = '0123456789';

  let password = '';

  password +=
    uppercaseChars[Math.floor(Math.random() * uppercaseChars.length)];
  password +=
    lowercaseChars[Math.floor(Math.random() * lowercaseChars.length)];
  password += digitChars[Math.floor(Math.random() * digitChars.length)];

  const remainingLength = length - 3;
  const allChars = uppercaseChars + lowercaseChars + digitChars;

  for (let i = 0; i < remainingLength; i += 1) {
    password += allChars[Math.floor(Math.random() * allChars.length)];
  }

  return password;
};
