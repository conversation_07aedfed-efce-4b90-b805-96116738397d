const { CONNECTION_METRICS } = require('./constants');
const { getMetric } = require('./metricRegistry');

let activeConnections = 0;

function trackConnections(server) {
  server.on('connection', (socket) => {
    activeConnections++;

    const gauge = getMetric(CONNECTION_METRICS.ACTIVE_CONNECTIONS);
    if (gauge) gauge.set(activeConnections);

    socket.on('close', () => {
      activeConnections = Math.max(0, activeConnections - 1);
      if (gauge) gauge.set(activeConnections);
    });
  });
}

function trackStreamingConnection(res) {
  const gauge = getMetric(CONNECTION_METRICS.ACTIVE_STREAM_CONNECTIONS);
  if (gauge) gauge.inc();

  res.on('close', () => {
    if (gauge) {
      const current = gauge.get().values?.[0]?.value ?? 1;
      gauge.set(Math.max(0, current - 1));
    }
  });
}

module.exports = {
  trackConnections,
  trackStreamingConnection,
};
