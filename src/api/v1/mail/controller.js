const schema = require('./schema');
const logger = require('../../../services/logger.service');
const service = require('../../../services/mail');
const { getLanguagePreference } = require('../../../utils/headers.util');

exports.getCommunityMail = async (req, res, next) => {
  try {
    const { communityId } = await schema.communityIdSchema.cast(
      req.params
    );

    const {
      mailType,
      mailCourseOffer,
      default: isDefault,
    } = await schema.getCommunityMailContentSchema.cast(req.query);

    const languagePreference = getLanguagePreference(req);

    const response = await service.getCommunityMail({
      mailType,
      mailCourseOffer,
      communityObjectId: communityId,
      isDefault,
      languagePreference,
    });

    return res.json({ data: response });
  } catch (err) {
    logger.error('getCommunityMailContent failed due to', err.message);
    return next(err);
  }
};

exports.updateCommunityMail = async (req, res, next) => {
  try {
    const { communityId } = await schema.communityIdSchema.cast(
      req.params
    );

    const languagePreference = getLanguagePreference(req);

    const { mailType, mailCourseOffer, mailSubject, content, isEnabled } =
      await schema.updateCommunityMailContentSchema.cast(req.body);

    const response = await service.updateCommunityMail({
      isEnabled,
      mailType,
      communityObjectId: communityId,
      mailSubject,
      content,
      mailCourseOffer,
      languagePreference,
    });

    return res.json({ data: response });
  } catch (err) {
    logger.error('updateCommunityMailContent failed due to', err.message);
    return next(err);
  }
};

exports.sendCommunityMail = async (req, res, next) => {
  try {
    const { communityId } = await schema.communityIdSchema.cast(
      req.params
    );

    const { mailType, mailCourseOffer, mailSubject, content, recipients } =
      await schema.sendCommunityMailContentSchema.cast(req.body);

    const languagePreference = getLanguagePreference(req);

    const response = await service.sendCommunityMail({
      mailType,
      mailCourseOffer,
      languagePreference,
      communityObjectId: communityId,
      mailSubject,
      content,
      recipients,
    });

    return res.json({ data: response });
  } catch (err) {
    logger.error('sendCommunityMailContent failed due to', err.message);
    return next(err);
  }
};
