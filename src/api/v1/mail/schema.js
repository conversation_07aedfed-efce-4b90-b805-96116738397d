const yup = require('yup');

exports.communityIdSchema = yup.object().shape({
  communityId: yup.string().required(),
});

exports.getCommunityMailContentSchema = yup.object().shape({
  mailType: yup.string().required().trim(),
  mailCourseOffer: yup.string().notRequired().trim(),
  default: yup.boolean().notRequired(),
});

exports.updateCommunityMailContentSchema = yup.object().shape({
  mailType: yup.string().required().trim(),
  mailCourseOffer: yup.string().notRequired().trim(),
  content: yup.object().required(),
  mailSubject: yup.string().required().trim(),
  isEnabled: yup.boolean().notRequired(),
});

exports.sendCommunityMailContentSchema = yup.object().shape({
  recipients: yup.array(yup.object().required()).required(),
  mailType: yup.string().required().trim(),
  mailCourseOffer: yup.string().notRequired().trim(),
  content: yup.object().required(),
  mailSubject: yup.string().required().trim(),
});
