const { Router } = require('express');
const schema = require('./schema');
const controller = require('./controller');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');
const {
  managerCommunityValidator,
} = require('../../../communitiesAPI/validations/community.validation');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const {
  validateAll,
} = require('../../../middleware/validator.middleware');

const router = Router({ mergeParams: true });

router
  .route('/')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    validateAll([
      { schema: schema.getCommunityMailContentSchema, location: 'query' },
      {
        schema: schema.communityIdSchema,
        location: 'params',
      },
    ]),
    controller.getCommunityMail
  )
  .put(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    validateAll([
      {
        schema: schema.updateCommunityMailContentSchema,
        location: 'body',
      },
      {
        schema: schema.communityIdSchema,
        location: 'params',
      },
    ]),
    controller.updateCommunityMail
  );

router.route('/send').post(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  validateAll([
    { schema: schema.sendCommunityMailContentSchema, location: 'body' },
    {
      schema: schema.communityIdSchema,
      location: 'params',
    },
  ]),
  controller.sendCommunityMail
);

module.exports = router;
