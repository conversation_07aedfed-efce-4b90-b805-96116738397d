/* eslint-disable no-unused-vars */
const {
  postRoutePreHandlerMiddleware,
} = require('../../../../middleware/request.middleware');

const {
  managerCommunityValidator,
} = require('../../../../communitiesAPI/validations/community.validation');
const tokenValidator = require('../../../../validations/token.validation');
const userValidation = require('../../../../validations/user.validation');

const { handlerWrapper } = require('../../../../utils/request.util');
const {
  rateLimitMiddleware,
} = require('../../../../utils/rateLimit.util');

const controller = require('./controller');
const schema = require('./schema');
const apiKeyValidation = require('../../../../validations/apiKey.validation');

const setupFeedEntriesRouter = function (router) {
  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/feed/entries/:entryId'
    )
    .post(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      handlerWrapper({
        handler: controller.editFeedEntry,
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/feed/create'
    )
    .post(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.createFeedEntry,
        requestValidators: {
          body: schema.createFeedEntrySchema,
        },
      })
    );
  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/feed/entries/:entryId'
    )
    .delete(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      handlerWrapper({
        handler: controller.deleteFeedEntry,
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/feed/entries'
    )
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      handlerWrapper({
        handler: controller.getFeedEntries,
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/feed/entries/:entryId/manage'
    )
    .post(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      handlerWrapper({
        handler: controller.manageFeedEntry,
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/feed/entries/:entryId/submission-preview'
    )
    .post(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      handlerWrapper({
        handler: controller.getFeedEntrySubmissionPreview,
      })
    );
};

const setupCommentAndReplyRouter = function (router) {
  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/feed/entries/:entryId/comments'
    )
    .put(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      handlerWrapper({
        handler: controller.commentOnFeedEntry,
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/feed/entries/:entryId/comments/:commentId/reply'
    )
    .post(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      handlerWrapper({
        handler: controller.replyToComment,
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/feed/entries/:entryId/comments/:commentId'
    )
    .delete(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      handlerWrapper({
        handler: controller.deleteCommentOrReply,
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/feed/entries/:entryId/comments'
    )
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      handlerWrapper({
        handler: controller.getFeedEntryComments,
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/feed/entries/:entryId/comments/:commentId/replies'
    )
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      handlerWrapper({
        handler: controller.getCommentReplies,
      })
    );
};

const setupReactRouter = function (router) {
  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/feed/entries/:entryId/reactions'
    )
    .put(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      handlerWrapper({
        handler: controller.reactToFeedEntry,
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/feed/entries/:entryId/reactions'
    )
    .delete(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      handlerWrapper({
        handler: controller.unreactToFeedEntry,
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/feed/entries/:entryId/reactions'
    )
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      handlerWrapper({
        handler: controller.getFeedEntryReactions,
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/feed/entries/:entryId/comments/:commentId/reactions'
    )
    .put(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      handlerWrapper({
        handler: controller.reactToComment,
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/feed/entries/:entryId/comments/:commentId/reactions'
    )
    .delete(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      handlerWrapper({
        handler: controller.unreactToComment,
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/feed/entries/:entryId/comments/:commentId/reactions'
    )
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      handlerWrapper({
        handler: controller.getCommentReactions,
      })
    );
};

const setupRouter = async function (router) {
  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/participants/:participantId/checkpoints/:checkpointId/participate-checkpoint'
    )
    .post(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      handlerWrapper({
        handler: controller.participateCheckpoint,
        requestValidators: {
          body: schema.participateCheckpointSchema,
        },
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/participant-checkpoints'
    )
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      handlerWrapper({
        handler: controller.getParticipantCheckpoints,
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/participants/:participantId/checkpoints-reminder-list'
    )
    .get(
      postRoutePreHandlerMiddleware,
      apiKeyValidation, // request from our internal services
      handlerWrapper({
        handler: controller.getParticipantCheckpointsReminders,
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/participants/:participantId/checkpoints/:checkpointId'
    )
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      handlerWrapper({
        handler: controller.getParticipantCheckpoint,
      })
    );

  setupFeedEntriesRouter(router);
  setupCommentAndReplyRouter(router);
  setupReactRouter(router);
};

module.exports = {
  setupRouter,
};
