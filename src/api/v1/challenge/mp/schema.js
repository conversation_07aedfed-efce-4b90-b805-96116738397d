const yup = require('yup');
const {
  QUESTION_TYPE,
} = require('../../../../services/program/constants');

const participateCheckpointSchema = yup.object().shape({
  submission: yup.array().of(
    yup.object().shape({
      question: yup.object().shape({
        questionText: yup.string().required(),
        type: yup.string().oneOf(Object.values(QUESTION_TYPE)),
      }),
      answer: yup.object().shape({
        answerText: yup.string(),
        answerFile: yup.string(),
        answerFileSizeInKB: yup.number(),
      }),
    })
  ),
  action: yup.string().required().oneOf(['complete', 'edit', 'draft']),
  caption: yup.string(),
  includeSubmissionInFeed: yup.boolean(),
});

const createFeedEntrySchema = yup.object().shape({
  title: yup.string().required(),
  contentData: yup.object().shape({
    data: yup.object().required(),
  }),
});

module.exports = {
  participateCheckpointSchema,
  createFeedEntrySchema,
};
