/* eslint-disable no-unused-vars */
const { ObjectId } = require('mongoose').Types;

const { DateTime } = require('luxon');
const { getUserIP } = require('../../../../utils/headers.util');

const Community = require('../../../../communitiesAPI/models/community.model');
const Learner = require('../../../../models/learners.model');
const User = require('../../../../models/users.model');

const programService = require('../../../../services/program');
const feedServices = require('../../../../services/feed');
const membershipService = require('../../../../services/membership');

const feedConstants = require('../../../../services/feed/constants');
const commonConstants = require('../../../../constants/common');

const {
  ParamError,
  UnauthorizedError,
  ResourceNotFoundError,
} = require('../../../../utils/error.util');
const {
  getLanguagePreference,
} = require('../../../../utils/headers.util');

const logger = require('../../../../services/logger.service');
const { createFeedEntrySchema } = require('./schema');

function getPaginationParam({ req, defaultLimit, defaultSkip }) {
  const limit = parseInt(req.query.pageSize, 10);
  if (!Number.isNaN(limit) && limit > 0) {
    let skip = 0;
    if (req.query.pageNo) {
      skip = (parseInt(req.query.pageNo, 10) - 1) * limit;
    }
    return { limit, skip };
  }
  if (defaultLimit !== undefined && defaultSkip !== undefined) {
    return { limit: defaultLimit, skip: defaultSkip };
  }
  return { limit: undefined, skip: undefined };
}

const getContentType = (caption) => {
  if (typeof caption === 'string') {
    return feedConstants.CONTENT_FORMAT.CUSTOM_OBJECT;
  }
  return feedConstants.CONTENT_FORMAT.LEXICAL;
};

const getLearner = async (email) => {
  const learner = await Learner.findOne(
    {
      email,
      isActive: true,
    },
    {
      _id: 1,
      email: 1,
      name: 1,
      language: 1,
    }
  );
  return learner;
};

const getUser = async (email) => {
  const user = await User.findOne(
    {
      email,
    },
    {
      _id: 1,
    }
  );
  return user;
};

async function getAuthor({ email, communityCode }) {
  const [learner, user, roles] = await Promise.all([
    getLearner(email),
    getUser(email),
    membershipService.getService.getCommunityMemberRoleFromSource({
      communityCode,
      email,
    }),
  ]);
  return {
    learnerObjectId: learner._id,
    userObjectId: user._id,
    roles,
  };
}

const getActiveCommunity = async (communityId) => {
  const community = await Community.findOne({
    _id: communityId,
    isActive: true,
  });
  if (!community) {
    throw new ParamError('Community not found');
  }
  return community;
};

const participateCheckpoint = async (req, res, next) => {
  const community = await getActiveCommunity(req.params.communityId);
  const [learner, user] = await Promise.all([
    getLearner(req.user.email),
    getUser(req.user.email),
  ]);

  const ip = getUserIP(req) || null;

  let includeSubmissionInFeed = req.body.includeSubmissionInFeed;
  if (includeSubmissionInFeed === undefined) {
    includeSubmissionInFeed = true;
  }
  const participantCheckpoint =
    await programService.participateService.participateProgramItem({
      community,
      learner,
      user,
      participantId: req.params.participantId,
      programId: req.params.challengeId,
      programItemId: req.params.checkpointId,
      submission: req.body.submission,
      action: req.body.action,
      includeSubmissionInFeed,
      caption: req.body.caption,
      ip,
    });
  return participantCheckpoint;
};

const getParticipantCheckpoint = async (req, res, next) => {
  const [community, learner] = await Promise.all([
    getActiveCommunity(req.params.communityId),
    getLearner(req.user.email),
  ]);

  const ip = getUserIP(req) || null;

  const participantCheckpoint =
    await programService.getParticipantsService.getParticipantProgramItem({
      community,
      learner,
      participantId: req.params.participantId,
      programId: req.params.challengeId,
      programItemId: req.params.checkpointId,
      ip,
    });

  return participantCheckpoint;
};

const getParticipantCheckpoints = async (req, res, next) => {
  const [community, learner] = await Promise.all([
    getActiveCommunity(req.params.communityId),
    getLearner(req.user.email),
  ]);
  const { limit, skip } = getPaginationParam({ req });

  const ip = getUserIP(req) || null;

  const participantCheckpointData =
    await programService.getParticipantProgramItemsService.getParticipantProgramItems(
      {
        community,
        learner,
        programId: req.params.challengeId,
        ip,
        limit,
        skip,
      }
    );

  return participantCheckpointData;
};

// Call from checkpoint reminder task to get the list of checkpoint reminder that need to send
const getParticipantCheckpointsReminders = async (req, res, next) => {
  const { communityId, challengeId, participantId } = req.params;
  const participantCheckpointReminderData =
    await programService.getParticipantProgramItemsService.getParticipantCheckpointReminderList(
      {
        communityId,
        challengeId,
        participantId,
      }
    );

  return participantCheckpointReminderData;
};

const deleteFeedEntry = async (req, res, next) => {
  const community = await getActiveCommunity(req.params.communityId);
  const entryId = req.params.entryId;
  const [learner, isManager, entry] = await Promise.all([
    getLearner(req.user.email),
    membershipService.getService.isCommunityManager({
      communityCode: community.code,
      email: req.user.email,
    }),
    feedServices.entryService.getEntry(
      {
        filters: { _id: entryId },
      },
      {
        author: 1,
      }
    ),
  ]);
  if (!entry) {
    throw new ParamError('Entry not found');
  }
  if (
    entry.author.learnerObjectId.toString() !== learner._id.toString() &&
    !isManager
  ) {
    throw new ParamError(
      'Logged-in user is neither CM nor the author of the entry'
    );
  }

  const result = await feedServices.entryService.deleteEntry({
    entryId,
  });
  return result;
};

const editFeedEntryVisibility = async (req, res, next) => {
  const community = await getActiveCommunity(req.params.communityId);
  const entryId = req.params.entryId;
  const [isManager, entry] = await Promise.all([
    membershipService.getService.isCommunityManager({
      communityCode: community.code,
      email: req.user.email,
    }),
    feedServices.entryService.getEntry(
      {
        filters: { _id: entryId },
      },
      {
        author: 1,
      }
    ),
  ]);
  if (!entry) {
    throw new ParamError('Entry not found');
  }
  if (!isManager) {
    throw new ParamError('Logged-in user is not a CM');
  }

  const result = await feedServices.entryService.deleteEntry({
    entryId,
  });
  return result;
};

async function getTagsForFilter({ req }) {
  const checkpointIdsToFilter = req.query.checkpointIdsToFilter
    ? (req.query.checkpointIdsToFilter?.split(',') || []).map(
        (id) => new ObjectId(id)
      )
    : [];

  return checkpointIdsToFilter;
}

const getFeedEntries = async (req, res, next) => {
  const community = await getActiveCommunity(req.params.communityId);
  const { limit, skip } = getPaginationParam({
    req,
    defaultLimit: 10,
    defaultSkip: 0,
  });

  const tags = await getTagsForFilter({ req });

  const postedBy = req.query.postedBy || '';

  const { entries, count } = await feedServices.entryService.getEntries({
    learner: req.user.learner,
    community,
    entityType: feedConstants.ENTITY_TYPES.PROGRAM,
    entityId: req.params.challengeId,
    limit,
    skip,
    tags,
    postedBy,
  });

  for (const entry of entries) {
    if (entry.hideContent === true) {
      delete entry.content;
      if (
        entry.author.learnerObjectId.toString() !==
        req.user.learner._id.toString()
      ) {
        delete entry.hideContentReason;
      }
    } else if (
      entry.content?.data?.items?.length > 0 &&
      entry.content?.data?.includeSubmissionInFeed !== true
    ) {
      delete entry.content.data.items;
    }
  }
  return {
    entries,
    meta: {
      total: count,
      limit,
      page: skip / limit + 1,
      pages: Math.ceil(count / limit),
    },
  };
};

const editFeedEntry = async (req, res, next) => {
  const entryId = req.params.entryId;
  const [learner, entry] = await Promise.all([
    getLearner(req.user.email),
    feedServices.entryService.getEntry(
      {
        filters: { _id: entryId },
      },
      {
        author: 1,
        content: 1,
      }
    ),
  ]);

  if (!entry) {
    throw new ParamError('Entry not found');
  }
  if (entry.author.learnerObjectId.toString() !== learner._id.toString()) {
    throw new ParamError('Logged-in user is not the author of the entry');
  }
  const caption = req.body.caption;
  const includeSubmissionInFeed = req.body.includeSubmissionInFeed;
  const newContentData = entry.content.data;
  if (caption) {
    newContentData.caption = caption;
  }
  if (includeSubmissionInFeed !== undefined) {
    newContentData.includeSubmissionInFeed = includeSubmissionInFeed;
  }

  const result = await feedServices.entryService.editEntryContent({
    filters: { _id: entryId },
    contentFormat: getContentType(caption ?? newContentData.caption),
    contentData: newContentData,
  });
  return result;
};

const createFeedEntry = async (req) => {
  const { title, contentData } = createFeedEntrySchema.cast(req.body);
  const { communityId, challengeId: programId } = req.params;
  const { email } = req.user;

  const [community, program, learner, user] = await Promise.all([
    getActiveCommunity(communityId),
    programService.commonService.getProgram(programId),
    getLearner(email),
    getUser(email),
  ]);

  if (!community || !program || !learner || !user) {
    throw new ResourceNotFoundError('Resource not found');
  }

  const entry = await feedServices.entryService.createEntry({
    title,
    community,
    entityType: feedConstants.ENTITY_TYPES.PROGRAM,
    entityId: program._id,
    tags: [], // keep empty because CM's post will not be tagged to a specific checkpoint
    contentFormat: feedConstants.CONTENT_FORMAT.LEXICAL,
    contentData: { caption: contentData.data },
    author: {
      learnerObjectId: learner._id,
      userObjectId: user._id,
    },
  });

  return entry;
};

const commentOnFeedEntry = async (req, res, next) => {
  const community = await getActiveCommunity(req.params.communityId);
  const entryId = req.params.entryId;
  const commentData = req.body.comment;
  const author = await getAuthor({
    email: req.user.email,
    communityCode: community.code,
  });
  const comment = await feedServices.commentService.commentOnFeedEntry({
    entryId,
    author,
    commentData,
  });
  return comment;
};

async function getDetailsForComment({ comment, learner }) {
  const { itemLearnerMap: authorLearnerMap, currentUserReactionMap } =
    await feedServices.commentService.getCommentsOrRepliesLearnerAndReactionMap(
      {
        items: [comment],
        learner,
        reactionEntityCollection:
          commonConstants.REACTION_ENTITY_COLLECTIONS.COMMENT,
      }
    );
  const processedComment = comment.toObject();
  processedComment.firstName =
    authorLearnerMap[comment.learnerObjectId].firstName;
  processedComment.lastName =
    authorLearnerMap[comment.learnerObjectId].lastName;
  processedComment.profileImage =
    authorLearnerMap[comment.learnerObjectId].profileImage;
  processedComment.country =
    authorLearnerMap[comment.learnerObjectId].country;
  processedComment.socialMedia =
    authorLearnerMap[comment.learnerObjectId].socialMedia;
  processedComment.currentUserReactionData =
    currentUserReactionMap[comment._id];
  return processedComment;
}

async function getDetailsForFeedEntry({ feedEntry, learner }) {
  const { authorLearnerMap, currentUserReactionMap } =
    await feedServices.entryService.getAuthorLearnerAndReactionData({
      entries: [feedEntry],
      learner,
    });
  const processedEntry = feedEntry.toObject();
  processedEntry.author.firstName =
    authorLearnerMap[feedEntry.author.learnerObjectId].firstName;
  processedEntry.author.lastName =
    authorLearnerMap[feedEntry.author.learnerObjectId].lastName;
  processedEntry.author.profileImage =
    authorLearnerMap[feedEntry.author.learnerObjectId].profileImage;
  processedEntry.author.country =
    authorLearnerMap[feedEntry.author.learnerObjectId].country;
  processedEntry.author.socialMedia =
    authorLearnerMap[feedEntry.author.learnerObjectId].socialMedia;
  processedEntry.currentUserReactionData =
    currentUserReactionMap[feedEntry._id];
  const someReactionProfileImages =
    await feedServices.commonService.getSomeReactionProfilePics(feedEntry);
  processedEntry.someReactionProfileImages = someReactionProfileImages;
  if (processedEntry.content?.data?.includeSubmissionInFeed !== true) {
    delete processedEntry.content.data.items;
  }
  if (processedEntry.hideContent === true) {
    delete processedEntry.content;
    if (
      processedEntry.author.learnerObjectId.toString() !==
      learner._id.toString()
    ) {
      delete processedEntry.hideContentReason;
    }
  }
  return processedEntry;
}

const getFeedEntryComments = async (req, res, next) => {
  const learner = req.user.learner;
  const entryId = req.params.entryId;
  const [community, feedEntry] = await Promise.all([
    getActiveCommunity(req.params.communityId),
    feedServices.entryService.getEntryById({
      entryId,
    }),
  ]);
  const processedEntry = await getDetailsForFeedEntry({
    feedEntry,
    learner,
  });

  const { limit, skip } = getPaginationParam({
    req,
    defaultLimit: 10,
    defaultSkip: 0,
  });
  const { comments, total, focusedComment } =
    await feedServices.commentService.getEntryComments({
      community,
      learner,
      entryId,
      limit,
      skip,
      focusedCommentId: req.query.focusedCommentId,
    });
  return {
    feedEntry: processedEntry,
    comments,
    focusedComment,
    meta: {
      total,
      limit,
      page: skip / limit + 1,
      pages: Math.ceil(total / limit),
    },
  };
};

const replyToComment = async (req, res, next) => {
  const community = await getActiveCommunity(req.params.communityId);
  const commentId = req.params.commentId;
  const entryId = req.params.entryId;
  const replyData = req.body.reply;
  const author = await getAuthor({
    email: req.user.email,
    communityCode: community.code,
  });
  const reply = await feedServices.commentService.replyToComment({
    community,
    entryId,
    commentId,
    replyData,
    author,
  });
  return reply;
};

const deleteCommentOrReply = async (req, res, next) => {
  const commentId = req.params.commentId;
  const entryId = req.params.entryId;
  const community = await getActiveCommunity(req.params.communityId);
  const [currentUser, comment, isManager, feedEntry] = await Promise.all([
    getAuthor({
      email: req.user.email,
      communityCode: community.code,
    }),
    feedServices.commentService.getCommentOrReply({
      filters: { _id: commentId, entityObjectId: entryId },
      projection: {
        _id: 1,
        learnerObjectId: 1,
      },
    }),
    membershipService.getService.isCommunityManager({
      communityCode: community.code,
      email: req.user.email,
    }),
    feedServices.entryService.getEntry(
      {
        filters: { _id: entryId },
      },
      {
        _id: 1,
        author: 1,
      }
    ),
  ]);
  if (!comment) {
    throw new ParamError('Comment not found');
  }
  if (
    comment.learnerObjectId.toString() !==
      currentUser.learnerObjectId.toString() &&
    !isManager &&
    feedEntry.author.learnerObjectId.toString() !==
      currentUser.learnerObjectId.toString()
  ) {
    throw new ParamError(
      'Logged-in user is not CM or the author of the comment or the author of the entry'
    );
  }
  const result = await feedServices.commentService.deleteCommentOrReply({
    commentId,
    entryId,
    author: currentUser,
  });
  return result;
};

const getCommentReplies = async (req, res, next) => {
  const entryId = req.params.entryId;
  const commentId = req.params.commentId;
  const learner = req.user.learner;
  const { limit, skip } = getPaginationParam({
    req,
    defaultLimit: 10,
    defaultSkip: 0,
  });
  const { replies, total, feedEntry, comment, focusedReply } =
    await feedServices.commentService.getCommentReplies({
      learner,
      entryId,
      commentId,
      limit,
      skip,
      focusedReplyId: req.query.focusedReplyId,
    });

  const [processedEntry, processedComment] = await Promise.all([
    getDetailsForFeedEntry({ feedEntry, learner }),
    getDetailsForComment({ comment, learner }),
  ]);

  return {
    feedEntry: processedEntry,
    comment: processedComment,
    replies,
    meta: {
      total,
      limit,
      page: skip / limit + 1,
      pages: Math.ceil(total / limit),
    },
    focusedReply,
  };
};

const reactToFeedEntry = async (req, res, next) => {
  const community = await getActiveCommunity(req.params.communityId);
  const author = await getAuthor({
    email: req.user.email,
    communityCode: community.code,
  });
  const entryId = req.params.entryId;
  const reaction = req.body.reaction;
  await feedServices.reactionService.reactToFeedEntry({
    author,
    entryId,
    reaction,
  });
};

const unreactToFeedEntry = async (req, res, next) => {
  const community = await getActiveCommunity(req.params.communityId);
  const author = await getAuthor({
    email: req.user.email,
    communityCode: community.code,
  });
  const entryId = req.params.entryId;
  const reaction = req.body.reaction;
  await feedServices.reactionService.unreactToFeedEntry({
    author,
    entryId,
    reaction,
  });
};

const getFeedEntryReactions = async (req, res, next) => {
  const learner = req.user.learner;
  const entryId = req.params.entryId;
  const { limit, skip } = getPaginationParam({
    req,
    defaultLimit: 10,
    defaultSkip: 0,
  });
  const { reactions, total } =
    await feedServices.reactionService.getEntryReactions({
      learner,
      entryId,
      limit,
      skip,
    });
  return {
    reactions,
    meta: {
      total,
      limit,
      page: skip / limit + 1,
      pages: Math.ceil(total / limit),
    },
  };
};

const reactToComment = async (req, res, next) => {
  const community = await getActiveCommunity(req.params.communityId);
  const entryId = req.params.entryId;
  const commentId = req.params.commentId;
  const reaction = req.body.reaction;
  const author = await getAuthor({
    email: req.user.email,
    communityCode: community.code,
  });
  await feedServices.reactionService.reactToComment({
    author,
    entryId,
    commentId,
    reaction,
  });
};

const unreactToComment = async (req, res, next) => {
  const community = await getActiveCommunity(req.params.communityId);
  const entryId = req.params.entryId;
  const commentId = req.params.commentId;
  const reaction = req.body.reaction;
  const author = await getAuthor({
    email: req.user.email,
    communityCode: community.code,
  });
  await feedServices.reactionService.unreactToComment({
    author,
    entryId,
    commentId,
    reaction,
  });
};

const getCommentReactions = async (req, res, next) => {
  const learner = req.user.learner;
  const commentId = req.params.commentId;
  const { limit, skip } = getPaginationParam({
    req,
    defaultLimit: 10,
    defaultSkip: 0,
  });
  const { reactions, total } =
    await feedServices.reactionService.getCommentReactions({
      learner,
      commentId,
      limit,
      skip,
    });
  return {
    reactions,
    meta: {
      total,
      limit,
      page: skip / limit + 1,
      pages: Math.ceil(total / limit),
    },
  };
};

const manageFeedEntry = async (req, res, next) => {
  const community = await getActiveCommunity(req.params.communityId);
  const entryId = req.params.entryId;
  const isManager = await membershipService.getService.isCommunityManager({
    communityCode: community.code,
    email: req.user.email,
  });
  if (!isManager) {
    throw new ParamError('Logged-in user is not a CM');
  }
  const action = req.body.action;
  switch (action) {
    case 'hide_content': {
      const hideContentReason = req.body.data?.hideContentReason;
      return feedServices.entryService.updateFeedEntry({
        filters: { _id: entryId },
        setData: {
          hideContent: true,
          hideContentReason,
        },
      });
    }
    default:
      break;
  }
};

const getFeedEntrySubmissionPreview = async (req, res, next) => {
  const learner = req.user.learner;
  const entry = await feedServices.entryService.getEntryById({
    entryId: req.params.entryId,
  });
  if (!entry) {
    throw new ParamError('Entry not found');
  }
  if (entry.author.learnerObjectId.toString() !== learner._id.toString()) {
    throw new UnauthorizedError(
      'Logged-in user is not the author of the entry'
    );
  }

  return entry;
};

module.exports = {
  participateCheckpoint,
  getParticipantCheckpoint,
  getParticipantCheckpoints,
  getParticipantCheckpointsReminders,
  getFeedEntries,
  editFeedEntry,
  commentOnFeedEntry,
  deleteFeedEntry,
  getFeedEntryComments,
  deleteCommentOrReply,
  replyToComment,
  getCommentReplies,
  reactToFeedEntry,
  unreactToFeedEntry,
  getFeedEntryReactions,
  reactToComment,
  unreactToComment,
  getCommentReactions,
  manageFeedEntry,
  getFeedEntrySubmissionPreview,
  createFeedEntry,
};
