const commonRouter = require('./common/router');
const cmpRouter = require('./cmp/router');
const mpRouter = require('./mp/router');
const copsRouter = require('./cops/router');

const setupRouter = async function (router) {
  await commonRouter.setupRouter(router);
  await cmpRouter.setupRouter(router);
  await mpRouter.setupRouter(router);
  await copsRouter.setupRouter(router);
};

module.exports = {
  setupRouter,
};
