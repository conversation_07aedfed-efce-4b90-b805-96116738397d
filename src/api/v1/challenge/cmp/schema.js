const yup = require('yup');
const {
  coverMediaItemsSchema,
} = require('../../../../validations/coverMediaItems.validation');
const {
  QUESTION_TYPE,
  PROGRAM_CHALLENGE_TYPE,
} = require('../../../../services/program/constants');
const {
  UPSELL_SOURCE_ENTITY_TYPE,
  PAYABLE_PURCHASE_TYPES,
} = require('../../../../constants/common');

const createChallengeSchema = yup.object().shape({
  templateLibraryId: yup.string().notRequired(),
  title: yup.string().required(),
  description: yup.object(),
  cover: yup.string().notRequired(), // legacy
  coverMediaItems: coverMediaItemsSchema,
  access: yup.string().notRequired(),
  challengeType: yup
    .string()
    .required()
    .default(PROGRAM_CHALLENGE_TYPE.FIXED),
  startTime: yup.date(),
  endTime: yup.date(),
  challengeDurationInDays: yup.number(),
  checkpointDurationInDays: yup.number(),
  pricingAmount: yup.number(),
  pricingCurrency: yup.string(),
  newDiscountsToApply: yup.array().default([]),
  enableCheckpointSubmissionAfterDeadline: yup.boolean(),
});

const updateChallengeSchema = yup.object().shape({
  title: yup.string(),
  description: yup.object(),
  instructions: yup.object(),
  rewards: yup.object(),
  startTime: yup.date(),
  endTime: yup.date(),
  status: yup.string(),
  slug: yup.string(),
  cover: yup.string(), // legacy
  coverMediaItems: coverMediaItemsSchema,
  access: yup.string(),
  pricingAmount: yup.number(),
  pricingCurrency: yup.string(),
  checkpointDurationInDays: yup.number(),
  currentDiscountsToDisable: yup.array(),
  newDiscountsToApply: yup.array(),
  chatGroupLink: yup.string().nullable(),
  canJoinAfterStartTime: yup.boolean(),
  priceType: yup.string().uppercase().trim().notRequired(),
  minAmount: yup.number().notRequired(),
  suggestedAmount: yup.number().notRequired(),
  enableCheckpointSubmissionAfterDeadline: yup.boolean(),
  hideParticipantCountOnLP: yup.boolean(),
  leaderboardConfigs: yup
    .array()
    .of(
      yup.object().shape({
        type: yup.string().trim().required(),
        enabled: yup.boolean().required(),
      })
    )
    .notRequired(),
});

const updateCheckpointEventSchema = yup.object().shape({
  title: yup.string().required(),
  type: yup.string().required(),
  startTime: yup.date().required(),
  endTime: yup.date().required(),
  timezoneId: yup.string().required(),
  inPersonLocation: yup.string().notRequired(),
  liveLink: yup.string().notRequired(),
});

const updateCheckpointUpsellSchema = yup.object().shape({
  upsellObjectId: yup.string(),
  sourceEntityType: yup
    .string()
    .trim()
    .oneOf(Object.values(UPSELL_SOURCE_ENTITY_TYPE))
    .notRequired(),
  sourceEntityObjectId: yup.string().trim().notRequired(),
  upsellCommunityObjectId: yup.string().trim().notRequired(),
  upsellEntityType: yup
    .string()
    .trim()
    .oneOf(PAYABLE_PURCHASE_TYPES)
    .notRequired(),
  upsellEntityObjectId: yup.string().trim().notRequired(),
  title: yup
    .string()
    .trim()
    .max(100, 'Title cannot be longer than 100 characters')
    .notRequired(),
  description: yup
    .string()
    .trim()
    .max(1000, 'Description cannot be longer than 1000 characters')
    .notRequired(),
  discount: yup
    .object()
    .shape({
      value: yup.number().notRequired(),
      effectiveTimeEnd: yup.date().nullable().notRequired(),
      timezone: yup.string().notRequired(),
      isActive: yup.boolean().default(false),
      expiredDuration: yup
        .object()
        .shape({
          unit: yup.string().required(), // days/hours
          value: yup.number().required(), // 10
        })
        .default(undefined),
    })
    .default(undefined),
});

const updateCheckpointsSchema = yup.object().shape({
  checkpoints: yup.array().of(
    yup.object().shape({
      title: yup.string(),
      description: yup.object(),
      startTime: yup.date(),
      endTime: yup.date(),
      attachmentItems: yup.array().of(yup.object()).notRequired(),
      coverVideo: yup.object().shape({
        mediaObjectId: yup.string(),
      }),
      submissionQuestions: yup.array().of(
        yup.object().shape({
          questionText: yup.string().required(),
          type: yup.string().oneOf(Object.values(QUESTION_TYPE)),
          required: yup.boolean(),
        })
      ),
    })
  ),
});

const checkpointSchema = yup.object().shape({
  _id: yup.string(),
  title: yup.string(),
  description: yup.object(),
  startTime: yup.date(),
  endTime: yup.date(),
  attachmentItems: yup.array().of(yup.object()).notRequired(),
  coverVideo: yup
    .object()
    .shape({
      mediaObjectId: yup.string(),
    })
    .default(undefined),
  submissionQuestions: yup.array().of(
    yup.object().shape({
      questionText: yup.string().required(),
      type: yup.string().oneOf(Object.values(QUESTION_TYPE)),
      required: yup.boolean(),
    })
  ),
  unlockAfterXDays: yup.number(), // for always on challenge
  createdEvent: updateCheckpointEventSchema.default(undefined),
  updatedEvent: updateCheckpointEventSchema.default(undefined),
  deletedEvent: yup.boolean(),
  isWelcomeCheckpoint: yup.boolean().notRequired(),
});

const bulkUpdateCheckpointsSchema = yup.object().shape({
  createdCheckpoints: yup.array().of(checkpointSchema),
  updatedCheckpoints: yup.array().of(checkpointSchema),
  duplicateCheckpoints: yup.array().of(
    yup.object().shape({
      _id: yup.string(),
      duplicateFromCheckpointId: yup.string(),
    })
  ),
  deletedCheckpoints: yup.array().of(yup.string()),
  reorderedCheckpoints: yup.array().of(yup.string()),
  createdUpsells: yup.array().of(updateCheckpointUpsellSchema),
  updatedUpsells: yup.array().of(updateCheckpointUpsellSchema),
  deletedUpsells: yup.array().of(yup.string()),
});

const duplicateCheckpointSchema = yup.object().shape({
  destination: yup.array().of(yup.string()),
  duplicateToAll: yup.boolean(),
});

const updateCheckpointDatesSchema = yup.object().shape({
  startTime: yup.date().required(),
  applyToAll: yup.boolean().default(false).required(),
  checkpoints: yup.array().of(
    yup.object().shape({
      _id: yup.string().required(),
      isWelcomeCheckpoint: yup.boolean().notRequired(),
      startTime: yup.date().when('isWelcomeCheckpoint', {
        is: true,
        then: yup.date().notRequired(),
        otherwise: yup.date().required(),
      }),
      endTime: yup.date().when('isWelcomeCheckpoint', {
        is: true,
        then: yup.date().notRequired(),
        otherwise: yup.date().required(),
      }),
    })
  ),
});

const challengeCheckpointReminderSchema = yup.object().shape({
  mailType: yup.string().notRequired(),
  challengeId: yup.string().required(),
  checkpointId: yup.string().notRequired(),
  jobObjectId: yup.string().required(),
});

const challengeMobileNotificationSchema = yup.object().shape({
  notificationBody: yup.object().required(),
  notificationId: yup.string().required(),
});

const declareWinnerSchema = yup.object().shape({
  winners: yup.array().of(
    yup.object().shape({
      participantObjectId: yup.string().required(),
      declareWinner: yup.boolean().required(),
    })
  ),
});

const removeParticipantsSchema = yup.object().shape({
  participants: yup.array().of(
    yup.object().shape({
      participantObjectId: yup.string().required(),
      removalReason: yup.string().notRequired(),
    })
  ),
});

const addParticipantsBodySchema = yup.array().of(
  yup.object().shape({
    email: yup.string().email().required(),
    subscriptionObjectId: yup.string(),
  })
);

const updateParticipantBodySchema = yup.object().shape({
  status: yup.string(),
});

const awardPointsSchema = yup.object().shape({
  reason: yup.string().trim().notRequired(),
  awardType: yup.string().trim().required(),
  notify: yup.boolean().required(),
});

const resetPointsSchema = yup.object().shape({
  reason: yup.string().trim().notRequired(),
  notify: yup.boolean().required(),
});

const communityIdSchema = { communityId: yup.string().trim().required() };
const challengeIdSchema = { challengeId: yup.string().trim().required() };
const participantIdSchema = {
  participantId: yup.string().trim().required(),
};
const checkpointIdSchema = {
  checkpointId: yup.string().trim().required(),
};

const checkpointParamsSchema = yup.object().shape({
  ...communityIdSchema,
  ...challengeIdSchema,
  ...participantIdSchema,
  ...checkpointIdSchema,
});

module.exports = {
  createChallengeSchema,
  updateChallengeSchema,
  updateCheckpointsSchema,
  updateCheckpointEventSchema,
  updateCheckpointDatesSchema,
  duplicateCheckpointSchema,
  declareWinnerSchema,
  removeParticipantsSchema,
  addParticipantsBodySchema,
  updateParticipantBodySchema,
  challengeCheckpointReminderSchema,
  challengeMobileNotificationSchema,
  updateCheckpointUpsellSchema,
  bulkUpdateCheckpointsSchema,
  awardPointsSchema,
  resetPointsSchema,
  checkpointParamsSchema,
};
