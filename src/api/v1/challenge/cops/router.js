const {
  postRoutePreHandlerMiddleware,
} = require('../../../../middleware/request.middleware');

const { handlerWrapper } = require('../../../../utils/request.util');

const controller = require('./controller');
const copsTokenMiddleware = require('../../../../middleware/cops-token.middleware');

const setupRouter = async function (router) {
  router.route('/challenge/cops/copy-video-from-folder-item').post(
    postRoutePreHandlerMiddleware,
    handlerWrapper({
      handler: controller.copyVideoFromFolderItem,
    })
  );

  router.route('/challenge/script').post(
    postRoutePreHandlerMiddleware,
    handlerWrapper({
      handler: controller.runScript,
    })
  );

  router.route('/challenge/cops/duplicate').post(
    postRoutePreHandlerMiddleware,
    copsTokenMiddleware,
    handlerWrapper({
      handler: controller.duplicateChallenge,
    })
  );
};

module.exports = {
  setupRouter,
};
