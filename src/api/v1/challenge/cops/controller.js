/* eslint-disable no-unused-vars */
const { ObjectId } = require('mongoose').Types;
const { DateTime } = require('luxon');
const programService = require('../../../../services/program/index');
const { ParamError } = require('../../../../utils/error.util');
const Community = require('../../../../communitiesAPI/models/community.model');
const {
  PROGRAM_TYPE,
  PARTICIPANT_PROGRAM_STATUS,
} = require('../../../../services/program/constants');
const ProgramModel = require('../../../../models/program/program.model');
const ProgramItemModel = require('../../../../models/program/programItem.model');

const communityFolderItemService = require('../../../../communitiesAPI/services/common/communityFolderItems.service');

const logger = require('../../../../services/logger.service');

function getArrayQueryParam(stringValue) {
  return stringValue && stringValue.trim() !== 0
    ? stringValue.split(',')
    : [];
}

const getActiveCommunity = async (communityId) => {
  const community = await Community.findOne({
    _id: communityId,
    isActive: true,
  });
  if (!community) {
    throw new ParamError('Community not found');
  }
  return community;
};

const runScript = async (req, res, next) => {
  const results = await programService.scriptsService.runScript(req.body);
  return results;
};

const copyVideoFromFolderItem = async (req, res, next) => {
  const { folderItemObjectId, programItemObjectId } = req.body;
  const folderItem = await communityFolderItemService.getFolderItemById(
    folderItemObjectId
  );
  if (!folderItem) {
    throw new ParamError('Folder item not found');
  }

  const programItem = await ProgramItemModel.findOne({
    _id: programItemObjectId,
  });
  if (!programItem) {
    throw new ParamError('Program item not found');
  }
  const duplicateFolderItem =
    await communityFolderItemService.duplicateFolderItem({
      folderItemObjectId,
      associatedCommunityFolderObjectIds: [programItem._id],
    });
  if (!duplicateFolderItem?.length > 0) {
    throw new Error('Failed to duplicate folder item');
  }
  const updatedProgramItem = await ProgramItemModel.findOneAndUpdate(
    {
      _id: programItem._id,
    },
    {
      coverVideo: {
        mediaObjectId: duplicateFolderItem[0]._id,
      },
    },
    {
      new: true,
    }
  );
  return updatedProgramItem;
};

const duplicateChallenge = async (req, res, next) => {
  const {
    targetCommunityObjectId,
    challengeId,
    duplicatedBy,
    targetChallengeType,
  } = req.body;
  const community = await getActiveCommunity(targetCommunityObjectId);

  const challenge =
    await programService.manageProgramsService.duplicateChallenge({
      community,
      challengeId,
      duplicatedBy,
      targetChallengeType,
    });

  return challenge;
};

module.exports = {
  runScript,
  copyVideoFromFolderItem,
  duplicateChallenge,
};
