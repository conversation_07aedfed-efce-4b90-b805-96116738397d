const yup = require('yup');

const addParticipantsBodySchema = yup.array().of(
  yup.object().shape({
    email: yup.string().email().required(),
    subscriptionObjectId: yup.string().required(),
  })
);
const addParticipantBodySchema = yup.object().shape({
  learnerObjectId: yup.string().required(),
  checkoutId: yup.string().notRequired(),
});

module.exports = {
  addParticipantBodySchema,
  addParticipantsBodySchema,
};
