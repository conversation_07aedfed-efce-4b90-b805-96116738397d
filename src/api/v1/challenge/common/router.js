/* eslint-disable no-unused-vars */
const {
  postRoutePreHandlerMiddleware,
} = require('../../../../middleware/request.middleware');
const schema = require('./schema');
const {
  managerCommunityValidator,
} = require('../../../../communitiesAPI/validations/community.validation');
const tokenValidator = require('../../../../validations/token.validation');
const tokenNoErrorValidator = require('../../../../validations/tokenNoError.validation');
const userValidation = require('../../../../validations/user.validation');
const userValidationWithoutError = require('../../../../validations/userValidationWithoutError.validation');
const {
  validateAll,
} = require('../../../../middleware/validator.middleware');
const apiKeyValidator = require('../../../../validations/apiKey.validation');

const { handlerWrapper } = require('../../../../utils/request.util');
const {
  rateLimitMiddleware,
} = require('../../../../utils/rateLimit.util');

const controller = require('./controller');

const setupRouter = async function (router) {
  router.route('/challenge/community/:communityId/challenges').get(
    postRoutePreHandlerMiddleware,
    tokenNoErrorValidator,
    userValidationWithoutError,
    handlerWrapper({
      handler: controller.getChallenges,
    })
  );

  router
    .route('/challenge/community/:communityId/challenges/:challengeId')
    .get(
      postRoutePreHandlerMiddleware,
      tokenNoErrorValidator,
      userValidationWithoutError,
      handlerWrapper({
        handler: controller.getChallenge,
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/upsells'
    )
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      handlerWrapper({
        handler: controller.retrieveAvailableUpsells,
      })
    );

  router.route('/challenge/get-challenge-by-slug').get(
    postRoutePreHandlerMiddleware,
    tokenNoErrorValidator,
    handlerWrapper({
      handler: controller.getChallengeBySlug,
    })
  );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/participants'
    )
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      handlerWrapper({
        handler: controller.getParticipants,
      })
    );

  // this route will be used by mainwebsitebackend from the success webhook
  router
    .route('/challenge/community/challenges/:challengeId/add-participant')
    .post(
      postRoutePreHandlerMiddleware,
      apiKeyValidator,
      validateAll([
        {
          schema: schema.addParticipantBodySchema,
          location: 'body',
        },
      ]),
      handlerWrapper({
        handler: controller.addParticipant,
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/participants/:participantId'
    )
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      handlerWrapper({
        handler: controller.getParticipant,
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/checkpoints'
    )
    .get(
      postRoutePreHandlerMiddleware,
      handlerWrapper({
        handler: controller.getCheckpoints,
      })
    );

  router
    .route(
      '/challenge/community/:communityId/challenges/:challengeId/checkpoints/:checkpointId'
    )
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      handlerWrapper({
        handler: controller.getCheckpoint,
      })
    );

  router.route('/challenge/check-participant-by-slug').get(
    postRoutePreHandlerMiddleware,
    tokenNoErrorValidator,
    handlerWrapper({
      handler: controller.checkParticipantBySlug,
    })
  );
};

module.exports = {
  setupRouter,
};
