const { ObjectId } = require('mongoose').Types;
const Community = require('../../../../communitiesAPI/models/community.model');
const Learner = require('../../../../models/learners.model');
const Membership = require('../../../../models/membership/membership.model');
const ProgramModel = require('../../../../models/program/program.model');

const schema = require('./schema');
const programService = require('../../../../services/program/index');
const communityService = require('../../../../services/community');
const upsellService = require('../../../../services/upsell');
const { getUserIP } = require('../../../../utils/headers.util');
const membershipService = require('../../../../services/membership');
const learnerUtils = require('../../../../services/user/utils/learner.utils');

const {
  ParamError,
  ResourceNotFoundError,
} = require('../../../../utils/error.util');

const {
  PROGRAM_TYPE,
  PROGRAM_ITEM_TYPE,
  PROGRAM_SCHEDULE_STATUS,
} = require('../../../../services/program/constants');

const commonConstants = require('../../../../constants/common');
const {
  MEMBERSHIP_STATUS,
} = require('../../../../services/membership/constants');
const {
  getEntityCampaignInfo,
} = require('../../../../services/magicAudience/magicAudienceCampaign.service');

function getPaginationParam({ req, defaultLimit, defaultSkip }) {
  const limit = parseInt(req.query.pageSize, 10);
  if (!Number.isNaN(limit) && limit > 0) {
    let skip = 0;
    if (req.query.pageNo) {
      skip = (parseInt(req.query.pageNo, 10) - 1) * limit;
    }
    return { limit, skip };
  }
  if (defaultLimit !== undefined && defaultSkip !== undefined) {
    return { limit: defaultLimit, skip: defaultSkip };
  }
  return { limit: undefined, skip: undefined };
}

function getArrayQueryParam(stringValue) {
  return stringValue && stringValue.trim() !== 0
    ? stringValue.split(',')
    : [];
}

const getActiveCommunity = async (communityId) => {
  const community = await Community.findOne({
    _id: communityId,
    isActive: true,
  });
  if (!community) {
    throw new ParamError('Community not found');
  }
  return community;
};

const getLearner = async (email) => {
  const learner = await Learner.findOne(
    {
      email,
      isActive: true,
    },
    {
      _id: 1,
      email: 1,
      firstName: 1,
      lastName: 1,
      language: 1,
      profileImage: 1,
    }
  ).lean();
  return learner;
};

async function getChallengeHost({ challenge, community }) {
  const host = await getLearner(
    challenge.hostEmail ||
      challenge.host ||
      challenge.createdBy ||
      community.createdBy
  );
  if (host) {
    host.name = learnerUtils.getFullName(host);
    return host;
  }

  if (challenge.customHostName) {
    return {
      name: challenge.customHostName,
      profileImage:
        challenge.customHostImage ??
        'https://d2yjtdaqamc55g.cloudfront.net/randomProfileImage11.jpg',
    };
  }
  return {
    name: 'Community',
    profileImage:
      'https://d2yjtdaqamc55g.cloudfront.net/randomProfileImage11.jpg',
  };
}

const isManager = async ({ email, community }) => {
  const membership = await Membership.findOne(
    {
      communityObjectId: community._id,
      email,
      status: MEMBERSHIP_STATUS.SUBSCRIBED,
    },
    {
      communityRole: 1,
    }
  );
  return membership?.communityRole?.length > 1;
};

const getParticipantCount = async (req) => {
  const countTypes = getArrayQueryParam(req.query.countTypes);
  const results = await programService.countService.countCommunityMembers({
    communityId: req.params.communityId,
    countTypes,
  });
  return results;
};

const addParticipant = async (req) => {
  const { challengeId } = req.params;

  const addParticipantBodyParams = schema.addParticipantBodySchema.cast(
    req.body
  );

  const challenge = await ProgramModel.findOne({
    _id: new ObjectId(challengeId),
  }).lean();

  if (!challenge) {
    throw new ParamError('Program not found');
  }

  const community = await getActiveCommunity(challenge.communityObjectId);

  const { learnerObjectId, checkoutId } = addParticipantBodyParams;

  const participant =
    await programService.manageParticipantsService.addParticipant({
      challenge,
      learnerObjectId,
      checkoutId,
      community,
      session: null,
      addedByLearnerObject: null,
    });

  return participant;
};

const getChallenges = async (req) => {
  const { limit, skip } = getPaginationParam({
    req,
    defaultLimit: 10,
    defaultSkip: 0,
  });
  const community = await getActiveCommunity(req.params.communityId);

  let isCommunityManager = false;
  let learner;
  if (req.user) {
    learner = await getLearner(req.user.email);
    isCommunityManager = await isManager({
      email: req.user.email,
      community,
    });
  }
  const statuses = getArrayQueryParam(req.query.statuses);
  const { results, total } =
    await programService.getProgramsService.getPrograms({
      community,
      learner,
      type: PROGRAM_TYPE.CHALLENGE,
      statuses,
      limit,
      skip,
      isCommunityManager,
    });
  return {
    challenges: results,
    meta: {
      total,
      limit,
      page: skip / limit + 1,
      pages: Math.ceil(total / limit),
    },
  };
};

const getChallenge = async (req) => {
  const {
    selectedAmount,
    affiliateCode,
    paymentMethodCountryCode,
    paymentProvider,
  } = req.query;

  const community = await getActiveCommunity(req.params.communityId);
  let learner;
  let isCommunityManager = false;
  if (req.user) {
    learner = await getLearner(req.user.email);
    isCommunityManager = await isManager({
      email: req.user.email,
      community,
    });
  }
  const needCheckpoints =
    !req.query.needCheckpoints ||
    req.query.needCheckpoints === '' ||
    req.query.needCheckpoints?.toLowerCase() === 'true';

  const showUpsell = req.query.showUpsell === 'true';

  const challenge = await programService.getProgramsService.getProgram({
    ip: getUserIP(req) || null,
    community,
    learner,
    programId: req.params.challengeId,
    needProgramItems: needCheckpoints,
    selectedAmount,
    showUpsell,
    affiliateCode,
    isCommunityManager,
    paymentMethodCountryCode,
    paymentProvider,
  });

  if (learner) {
    const checkpoints = challenge.items;
    delete challenge.items;
    challenge.checkpoints = checkpoints;
  } else {
    delete challenge.items;
    delete challenge.joined;
  }
  challenge.host = await getChallengeHost({ challenge, community });

  challenge.campaignInfo =
    (await getEntityCampaignInfo({
      entityObjectId: challenge._id,
      entityType: 'challenge',
    })) || null;
  return challenge;
};

const retrieveAvailableUpsells = async (req) => {
  const learnerObjectId = req.user.learner._id;
  const { challengeId } = req.params;
  const ip = getUserIP(req) || null;

  const result = await upsellService.retrieveAvailableUpsells({
    entityType: commonConstants.PURCHASE_TYPE.CHALLENGE,
    entityObjectId: challengeId,
    learnerObjectId,
    ip,
  });

  return result;
};

const getChallengeBySlug = async (req) => {
  const {
    communityLink,
    selectedAmount,
    affiliateCode,
    paymentMethodCountryCode,
    paymentProvider,
  } = req.query;

  if (!communityLink) {
    throw new ParamError('Community link is required');
  }
  const community =
    await communityService.getCommunityService.getCommunityByLink({
      link: communityLink,
    });
  if (!community) {
    throw new ResourceNotFoundError('Community not found');
  }
  let learner;
  let isCommunityManager = false;
  if (req.user) {
    [learner, isCommunityManager] = await Promise.all([
      getLearner(req.user.email),
      isManager({
        email: req.user.email,
        community,
      }),
    ]);
  }
  const challenge =
    await programService.getProgramsService.getProgramBySlug({
      ip: getUserIP(req) || null,
      community,
      slug: req.query.slug,
      learner,
      selectedAmount,
      affiliateCode,
      isCommunityManager,
      paymentMethodCountryCode,
      paymentProvider,
    });
  challenge.host = await getChallengeHost({ challenge, community });
  return challenge;
};

async function getFilteredCheckpoints({
  community,
  programId,
  filterProgramItemIds,
  checkpointIdStart,
}) {
  const checkpoints =
    await programService.getProgramItemsService.getProgramItems({
      community,
      programId,
      type: PROGRAM_ITEM_TYPE.CHECKPOINT,
      filters: {
        _id: { $in: filterProgramItemIds },
      },
      projection: {
        _id: 1,
        index: 1,
      },
    });
  if (checkpointIdStart) {
    const results = [];
    let count = 0;
    for (const checkpoint of checkpoints) {
      if (checkpoint._id.toString() === checkpointIdStart) {
        results.push(checkpoint);
        count += 1;
      } else if (count > 0 && count < 7) {
        results.push(checkpoint);
        count += 1;
      } else if (count >= 7) {
        break;
      }
    }
    return results;
  }
  return checkpoints;
}

async function getCheckpointPage({
  community,
  challengeId,
  checkpointIndexStartFromQuery,
}) {
  const challenge = await programService.commonService.getProgram(
    challengeId
  );
  const ongoingCheckpoint =
    await programService.commonService.getOngoingProgramItem({
      program: challenge,
      type: PROGRAM_ITEM_TYPE.CHECKPOINT,
      projection: {
        index: 1,
        _id: 1,
      },
    });
  let checkpointIndexStart = parseInt(checkpointIndexStartFromQuery, 10);
  if (!checkpointIndexStart || Number.isNaN(checkpointIndexStart)) {
    const challengeScheduleStatus =
      programService.commonService.getProgramScheduleStatus({
        program: challenge,
      });
    switch (challengeScheduleStatus) {
      case PROGRAM_SCHEDULE_STATUS.UPCOMING:
        checkpointIndexStart = 1;
        break;
      case PROGRAM_SCHEDULE_STATUS.ENDED:
        checkpointIndexStart =
          Math.floor((challenge.durationInDays - 1) / 7) * 7 + 1;
        break;
      default: {
        checkpointIndexStart =
          Math.floor(((ongoingCheckpoint?.index || 1) - 1) / 7) * 7 + 1;
        break;
      }
    }
  }

  const checkpoints =
    await programService.getProgramItemsService.getProgramItems({
      community,
      programId: challengeId,
      type: PROGRAM_ITEM_TYPE.CHECKPOINT,
      limit: 7,
      skip: checkpointIndexStart - 1,
      projection: {
        _id: 1,
        index: 1,
      },
    });
  return {
    checkpoints,
    ongoingCheckpoint,
  };
}

const getParticipants = async (req) => {
  // eslint-disable-next-line prefer-const
  let { limit, skip } = getPaginationParam({
    req,
    defaultLimit: 10,
    defaultSkip: 0,
  });
  const [community, challenge] = await Promise.all([
    getActiveCommunity(req.params.communityId),
    programService.commonService.getProgram(req.params.challengeId),
  ]);
  let isCommunityManager = false;
  if (req.user) {
    isCommunityManager = await isManager({
      email: req.user.email,
      community,
    });
  }
  const filterProgramItemIds = getArrayQueryParam(
    req.query.filterCheckpointIds
  );
  const filterProgramItemStatuses = getArrayQueryParam(
    req.query.filterCheckpointStatuses
  );
  const filterProgramStatuses = getArrayQueryParam(
    req.query.filterChallengeStatuses
  );

  let checkpoints;
  let ongoingCheckpoint;
  let checkpointCount;
  if (isCommunityManager) {
    if (filterProgramItemIds && filterProgramItemIds.length > 0) {
      checkpoints = await getFilteredCheckpoints({
        community,
        programId: req.params.challengeId,
        filterProgramItemIds,
        checkpointIdStart: req.query.checkpointIdStart,
      });
      checkpointCount = filterProgramItemIds.length;
    } else {
      const checkpointPageData = await getCheckpointPage({
        community,
        challengeId: req.params.challengeId,
        checkpointIndexStartFromQuery: req.query.checkpointIndexStart,
      });
      checkpoints = checkpointPageData.checkpoints;
      ongoingCheckpoint = checkpointPageData.ongoingCheckpoint;
      checkpointCount =
        await programService.getProgramItemsService.countProgramItems({
          programId: req.params.challengeId,
          type: PROGRAM_ITEM_TYPE.CHECKPOINT,
        });
    }
  }

  const results = {
    checkpoints,
    ongoingCheckpoint,
  };

  let sortOrder = req.query.sortOrder === 'asc' ? 1 : -1;
  if (challenge.isLeaderboardEnabled) {
    const currentParticipant =
      await programService.getParticipantsService.getParticipantRaw({
        filters: {
          learnerObjectId: req.user.learner._id,
          programObjectId: challenge._id,
        },
      });

    if (currentParticipant) {
      const { rank, points, numOfStreaks } =
        await programService.leaderboardService.getParticipantLeaderboardPointsAndRank(
          {
            program: challenge,
            participant: currentParticipant,
          }
        );

      delete currentParticipant.items;

      results.currentUserData = {
        leaderboardInfo: {
          rank,
          totalPoints: points,
          numOfStreaks,
        },
      };
      if (req.query.specificPage === 'current_user_leaderboard_page') {
        skip = Math.floor((rank - 1) / limit) * limit;
        // force sort order to be descending so the current page logic works
        sortOrder = -1;
      }
    }
  }

  const participantResults =
    await programService.getParticipantsService.getParticipants({
      community,
      program: challenge,
      searchString: req.query.searchString,
      programItemOffset: req.query.checkpointOffset,
      programItemLimit: req.query.checkpointLimit,
      filterProgramItemIds,
      filterProgramItemStatuses,
      filterProgramStatuses,
      limit,
      skip,
      programType: PROGRAM_TYPE.CHALLENGE,
      toReturnItems: checkpoints,
      isCommunityManager,
      sortBy: req.query.sortBy,
      sortOrder,
    });

  results.participants = participantResults.participants;
  results.meta = {
    checkpointCount,
    total: participantResults.total,
    limit,
    page: skip / limit + 1,
    pages: Math.ceil(participantResults.total / limit),
  };

  return results;
};

const getParticipant = async (req) => {
  const community = await getActiveCommunity(req.params.communityId);
  const participant =
    await programService.getParticipantsService.getParticipant({
      community,
      programId: req.params.challengeId,
      participantId: req.params.participantId,
    });
  return participant;
};

const getCheckpoint = async (req) => {
  const community = await getActiveCommunity(req.params.communityId);
  const isCommunityManager = await isManager({
    email: req.user.email,
    community,
  });

  const learnerObjectId = req.user.learner._id;
  const ip = getUserIP(req) || null;

  const checkpoint =
    await programService.getProgramItemsService.getProgramItem({
      community,
      filters: {
        _id: req.params.checkpointId,
      },
      isCommunityManager,
      learnerObjectId,
      ip,
    });
  if (!checkpoint) {
    throw new ParamError('Checkpoint not found');
  }
  return checkpoint;
};

const getCheckpoints = async (req) => {
  const { limit, skip } = getPaginationParam({ req });

  const showUpsell = req.query.showUpsell === 'true';
  const showCompletedCount = req.query.showCompletedCount === 'true';

  const checkpoints =
    await programService.getProgramItemsService.getProgramItems({
      programId: req.params.challengeId,
      type: PROGRAM_ITEM_TYPE.CHECKPOINT,
      limit,
      skip,
      showUpsell,
      showCompletedCount,
    });
  return checkpoints;
};

const checkParticipantBySlug = async (req) => {
  if (!req.user) {
    return {
      isParticipant: false,
      communityRoles: [],
    };
  }

  const communityLink = req.query.communityLink;
  if (!communityLink) {
    throw new ParamError('Community link is required');
  }
  const community =
    await communityService.getCommunityService.getCommunityByLink({
      link: communityLink,
    });
  if (!community) {
    throw new ResourceNotFoundError('Community not found');
  }
  const [challenge, learner, communityRoles] = await Promise.all([
    programService.getProgramsService.getProgramBySlugLite({
      community,
      slug: req.query.slug,
    }),
    getLearner(req.user.email),
    membershipService.getService.getCommunityMemberRoleFromSource({
      email: req.user.email,
      communityCode: community.code,
    }),
  ]);

  if (!challenge) {
    throw new ResourceNotFoundError('Challenge not found');
  }

  const isParticipant =
    await programService.getParticipantsService.isParticipant({
      programId: challenge._id,
      learnerObjectId: learner._id,
    });

  return {
    isParticipant,
    communityRoles,
  };
};

module.exports = {
  getChallenges,
  getChallenge,
  retrieveAvailableUpsells,
  getChallengeBySlug,
  getParticipants,
  getParticipant,
  getCheckpoint,
  getCheckpoints,
  getParticipantCount,
  addParticipant,
  checkParticipantBySlug,
};
