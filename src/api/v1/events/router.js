const { Router } = require('express');
const schema = require('./schema');
const controller = require('./controller');
const tokenValidator = require('../../../validations/token.validation');
const transientTokenValidator = require('../../../validations/transientToken.validation');
const userValidation = require('../../../validations/user.validation');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const {
  validator,
  validateAll,
} = require('../../../middleware/validator.middleware');
const {
  managerCommunityValidator,
  memberCommunityValidator,
} = require('../../../communitiesAPI/validations/community.validation');
const apiKeyValidation = require('../../../validations/apiKey.validation');
const { handlerWrapper } = require('../../../utils/request.util');
const copsTokenMiddleware = require('../../../middleware/cops-token.middleware');
const {
  MODULE_TYPE,
  // OPERATION,
  // IDENTIFIER_TYPE,
  // mongoRateLimitMiddleware,
  rateLimitMiddleware,
} = require('../../../utils/rateLimit.util');

const router = Router({ mergeParams: true });

router
  .route('/:id')
  .put(
    postRoutePreHandlerMiddleware,
    validator(schema.updateEventSchema, 'body'),
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    controller.updateCommunityEvent
  );

router
  .route('/:id/attendees')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    memberCommunityValidator,
    validateAll([
      { schema: schema.getEventAttendeesSchema, location: 'query' },
      {
        schema: schema.getEventAttendeesPathSchema,
        location: 'params',
      },
    ]),
    controller.getCommunityEventAndAttendees
  )
  .post(
    postRoutePreHandlerMiddleware,
    apiKeyValidation,
    validateAll([
      { schema: schema.postEventAttendeesSchema, location: 'body' },
      {
        schema: schema.getEventAttendeesPathSchema,
        location: 'params',
      },
    ]),
    controller.createEventAttendee
  );

router.route('/:id/add-multiple-attendees').post(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  validateAll([
    {
      schema: schema.postAddMultipleEventAttendeesByCMSchema,
      location: 'body',
    },
    {
      schema: schema.getEventAttendeesPathSchema,
      location: 'params',
    },
  ]),
  handlerWrapper({
    handler: controller.addMultipleAttendeesByCM,
  })
);

// For event attendee to fetch their own ticket (event_atendee doc)
// This API will create QRCode ticket for inPerson event if it doesn't exist.
router.route('/:id/attendees/:attendeeId/private').get(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  memberCommunityValidator,
  validateAll([
    {
      schema: schema.getEventAttendeesPathSchema,
      location: 'params',
    },
  ]),
  handlerWrapper({
    handler: controller.getSingleEventAttendee,
  })
);

router.route('/:id/attendees/csv').get(
  postRoutePreHandlerMiddleware,
  transientTokenValidator,
  validateAll([
    { schema: schema.getExportEventAttendeesSchema, location: 'query' },
    {
      schema: schema.getEventAttendeesPathSchema,
      location: 'params',
    },
  ]),
  controller.getExportEventAttendees
);

router.route('/:id/attendees/:attendeeId/checkIn').patch(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  validateAll([
    {
      schema: schema.checkInTicketReferencesSchema,
      location: 'body',
    },
    {
      schema: schema.getEventAttendeePathSchema,
      location: 'params',
    },
  ]),
  controller.checkInTicketReferences
);

router.route('/:id/qrScanCheckIn').patch(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  validateAll([
    {
      schema: schema.scanQrCheckInPathSchema,
      location: 'params',
    },
    {
      schema: schema.scanQrCheckInPayloadSchema,
      location: 'body',
    },
  ]),
  handlerWrapper({
    handler: controller.scanQrToCheckIn,
  })
);

router.route('/:id/attendees/:attendeeId/approval').post(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  validateAll([
    {
      schema: schema.approveOrRejectEventAttendeeSchema,
      location: 'body',
    },
    {
      schema: schema.getEventAttendeesPathSchema,
      location: 'params',
    },
  ]),
  controller.approveOrRejectEventAttendee
);

router.route('/:id/attendees/:attendeeId/not-going').put(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  memberCommunityValidator,
  validateAll([
    {
      schema: schema.putEventAttendeesPathSchema,
      location: 'params',
    },
  ]),
  controller.updateEventAttendeeStatus
);

router.route('/cops/attendees/update').put(
  postRoutePreHandlerMiddleware,
  copsTokenMiddleware,
  validateAll([
    {
      schema: schema.updateEventAttendeesStatusSchema,
      location: 'body',
    },
  ]),
  controller.updateEventAttendeeStatusFromCops
);

router
  .route('/')
  .post(
    postRoutePreHandlerMiddleware,
    validator(schema.createEventSchema, 'body'),
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    controller.createCommunityEvent
  );

router.route('/draft').post(
  postRoutePreHandlerMiddleware,
  validator(schema.createEventSchema, 'body'),
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  handlerWrapper({
    handler: controller.createCommunityEventDraft,
    requestValidators: {
      params: schema.communityIdPathSchema,
      body: schema.createEventSchema,
    },
  })
);

router.route('/:eventId/publish').put(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  handlerWrapper({
    handler: controller.publishCommunityEvent,
    requestValidators: {
      params: schema.singleEventPathSchema,
    },
  })
);

router.route('/:eventId/unpublish').put(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  handlerWrapper({
    handler: controller.unpublishCommunityEvent,
    requestValidators: {
      params: schema.singleEventPathSchema,
    },
  })
);

router.route('/:id/create-event-duplicates').post(
  postRoutePreHandlerMiddleware,
  // mongoRateLimitMiddleware({
  //   module: MODULE_TYPE.EVENT_DUPLICATION,
  //   operation: OPERATION.CREATE_EVENT_DUPLICATES,
  //   identifierFn: IDENTIFIER_TYPE.COMMUNITY_ID,
  // }),
  rateLimitMiddleware({
    module: MODULE_TYPE.EVENT_DUPLICATION,
  }),
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  handlerWrapper({
    handler: controller.createEventDuplicates,
    requestValidators: {
      params: schema.eventPathSchema,
      body: schema.eventDuplicationSchema,
    },
  })
);

module.exports = router;
