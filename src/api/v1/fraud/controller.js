/* eslint-disable no-unused-vars */

const fraudService = require('../../../services/fraud');

// DEPRECATED
const fraudCheckService = require('../../../services/fraud/fraudCheck');

const checkFraud = async (req, res, next) => {
  const {
    communityId,
    communityCode,
    event,
    entity,
    entityId,
    payload,
    callbackOnApprove,
    callbackOnReject,
    rules,
  } = req.body;

  const results = await fraudCheckService.checkFraud({
    communityId,
    communityCode,
    event,
    entity,
    entityId,
    payload,
    callbackOnApprove,
    callbackOnReject,
    rules,
  });
  const response = { rulesMatchedResult: results };
  return response;
};

const reviewCommunityTicket = async (req, res, next) => {
  const { ticketId, status, actionOnApprove, actionOnReject, operator } =
    req.body;

  const results = await fraudService.reviewCommunityTicket({
    ticketId,
    status,
    actionOnApprove,
    actionOnReject,
    operator,
  });

  return results;
};

const reviewIndividualTicket = async (req, res, next) => {
  const { ticketId, status, actionOnApprove, actionOnReject, operator } =
    req.body;

  const results = await fraudService.reviewIndividualTicket({
    ticketId,
    status,
    actionOnApprove,
    actionOnReject,
    operator,
  });
  return results;
};

module.exports = {
  checkFraud,
  reviewCommunityTicket,
  reviewIndividualTicket,
};
