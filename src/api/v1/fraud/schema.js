const yup = require('yup');

const FraudSchema = yup.object().shape({
  communityId: yup.string().trim().required(),
  communityCode: yup.string().trim().required(),
  event: yup
    .string()
    .trim()
    .oneOf(['create', 'update', 'reach', 'invite'])
    .required(),
  entity: yup
    .string()
    .trim()
    .oneOf(['community', 'event', 'content', 'post', 'challenge'])
    .required(),
  entityId: yup.string().trim().required(),
  payload: yup.object(),
  callbackOnApprove: yup.object().required(),
  callbackOnReject: yup.object().required(),
  rules: yup
    .array()
    .of(
      yup.object().shape({
        id: yup.number().integer().required(),
        description: yup.string().trim().required(),
        target: yup.array().of(yup.string().trim()),
      })
    )
    .required(),
});

module.exports = { FraudSchema };
