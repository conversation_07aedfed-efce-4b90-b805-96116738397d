const controller = require('./controller');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');

const copsTokenMiddleware = require('../../../middleware/cops-token.middleware');

const { handlerWrapper } = require('../../../utils/request.util');

const FRAUD_ROUTE = '/fraud';

const setupRouter = function (router) {
  router
    .route(`${FRAUD_ROUTE}/cops/review-community-ticket`)
    .post(
      postRoutePreHandlerMiddleware,
      copsTokenMiddleware,
      handlerWrapper({ handler: controller.reviewCommunityTicket })
    );

  router
    .route(`${FRAUD_ROUTE}/cops/review-individual-ticket`)
    .post(
      postRoutePreHandlerMiddleware,
      copsTokenMiddleware,
      handlerWrapper({ handler: controller.reviewIndividualTicket })
    );
};

module.exports = {
  setupRouter,
};
