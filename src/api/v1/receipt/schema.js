const yup = require('yup');

const generateReceiptSchema = yup.object().shape({
  receiptId: yup.string().required(),
  transactionCreatedAt: yup.date().required(),
  communityInfo: yup.object().shape({
    title: yup.string().required(),
    profileImage: yup.string().required(),
    ownerName: yup.string().notRequired(),
  }),
  paymentMethod: yup.object().shape({
    paymentBrand: yup.string().required(),
    cardLast4: yup.string().notRequired(),
  }),
  recipient: yup.object().shape({
    name: yup.string().notRequired(), // this name can be an empty string
    email: yup.string().email().required(),
    languagePreference: yup.string().required(),
    timezone: yup.string().required(),
  }),
  item: yup.object().shape({
    title: yup.string().required(),
    purchaseType: yup.string().required(),
    quantity: yup.number().required(),
    unitPrice: yup.number().required(),
    currency: yup.string().required(),
    totalAmount: yup.number().required(),
    discountInfo: yup
      .object()
      .shape({
        // discount info can be empty so thus not required
        amount: yup.number().notRequired(),
        percentage: yup.number().notRequired(),
        code: yup.string().notRequired(),
        interval: yup.string().notRequired(),
      })
      .notRequired()
      .nullable()
      .default(null),
    startDate: yup.date().notRequired(), // this will only be available in the case of subscription
    endDate: yup.date().notRequired(), // this will only be available in the case of subscription
    plan: yup
      .object()
      .shape({
        interval: yup.string().notRequired(),
        intervalCount: yup.number().notRequired(),
        nextBillingDate: yup.date().notRequired(),
        price: yup.number().notRequired(),
        originalPrice: yup.number().notRequired(),
        currency: yup.string().notRequired(),
      })
      .notRequired()
      .nullable()
      .default(null),
  }),
  subTotalAmount: yup.number().required(),
  processingFee: yup.number().notRequired(),
  paidAmount: yup.number().required(),
});

const regenerateReceiptSchema = yup.object().shape({
  rawTransactionObjectId: yup.string().required(),
  saveToRawTransaction: yup.boolean().notRequired().default(false),
});

module.exports = {
  generateReceiptSchema,
  regenerateReceiptSchema,
};
