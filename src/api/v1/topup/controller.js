const schema = require('./schema');
const {
  getUserIP,
  getUserAgent,
  getLanguagePreference,
} = require('../../../utils/headers.util');
const topupService = require('../../../services/topup/topupCreate.service');
const topupConfirmService = require('../../../services/topup/topupConfirm.service');
const topupPaymentUpdateService = require('../../../services/topup/topupPaymentUpdate.service');
const topupVerifyPaymentService = require('../../../services/topup/topupVerifyPayment.service');

exports.createTopupOrder = async (req) => {
  const { communityId } = req.params;
  const {
    signupToken,
    timezone,
    requestor,
    paymentProvider,
    trackingData,
    items,
  } = schema.createTopupOrderBodySchema.cast(req.body);

  const ip = getUserIP(req) || null;
  const userAgent = getUserAgent(req) || null;
  const requestLanguagePreference = getLanguagePreference(req);
  const email = req.user.email;
  const learnerObjectId = req.user.learner._id;

  const result = await topupService.createTopupOrder({
    ip,
    userAgent,
    requestLanguagePreference,
    email,
    learnerObjectId,
    signupToken,
    communityId,
    timezone,
    requestor,
    paymentProvider,
    trackingData,
    items,
  });

  return result;
};

exports.confirmTopupOrder = async (req) => {
  const { communityId } = req.params;
  const { signupToken, metadata, paymentMethodId, confirmType } =
    schema.confirmTopupOrderBodySchema.cast(req.body);

  const ip = getUserIP(req) || null;
  const userAgent = getUserAgent(req) || null;

  const result = await topupConfirmService.confirmTopupOrder({
    ip,
    userAgent,
    signupToken,
    metadata,
    confirmType,
    paymentMethodId,
  });

  return result;
};

exports.verifyTopupOrderPayment = async (req) => {
  const { communityId } = req.params;
  const { signupId } = schema.verifyTopupOrderPaymentBodySchema.cast(
    req.body
  );

  const result = await topupVerifyPaymentService.verifyPayment({
    signupId,
  });

  return result;
};

exports.updateTopupOrderPaymentStatus = async (req) => {
  const { communityId } = req.params;
  const { topupOrderId, status, failureCode, failureMessage, eventTime } =
    schema.updateTopupOrderPaymentStatusBodySchema.cast(req.body);

  const result =
    await topupPaymentUpdateService.topupOrderPaymentUpdatesWithSession({
      topupOrderId,
      status,
      failureCode,
      failureMessage,
      eventTime,
    });

  return result;
};
