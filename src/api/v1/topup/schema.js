const yup = require('yup');
const {
  PAYMENT_PROVIDER,
  TOPUP_TYPE,
  PAYABLE_PURCHASE_TYPES,
  PURCHASE_TYPE,
} = require('../../../constants/common');

exports.communityIdSchema = yup.object().shape({
  communityId: yup.string().required(),
});

exports.createTopupOrderBodySchema = yup.object().shape({
  signupToken: yup.string().notRequired().trim(),
  // communityCode: yup.string().required().trim(),
  timezone: yup.string().required().trim(),
  requestor: yup.string().required().trim(),
  paymentProvider: yup
    .string()
    .oneOf([
      PAYMENT_PROVIDER.STRIPE_US,
      PAYMENT_PROVIDER.STRIPE_INDIA,
      PAYMENT_PROVIDER.EBANX,
    ]),
  trackingData: yup.object().required(),
  items: yup.array().of(
    yup.object().shape({
      topupType: yup
        .string()
        .oneOf([PURCHASE_TYPE.ADS_CAMPAIGN_TOPUP])
        .required(),
      entityObjectId: yup.string().required(),
    })
  ),
});

exports.confirmTopupOrderBodySchema = yup.object().shape({
  signupToken: yup.string().required().trim(),
  metadata: yup.object().notRequired(),
  paymentMethodId: yup.string().notRequired().trim(),
  confirmType: yup.string().required().oneOf(PAYABLE_PURCHASE_TYPES),
});

exports.updateTopupOrderPaymentStatusBodySchema = yup.object().shape({
  topupOrderId: yup.string().required().trim(),
  status: yup.string().required().trim(),
  failureCode: yup.string().trim(),
  failureMessage: yup.string().trim(),
  eventTime: yup.date().required(),
});

exports.verifyTopupOrderPaymentBodySchema = yup.object().shape({
  signupId: yup.string().required().trim(),
});
