const schema = require('./schema');
const controller = require('./controller');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');
const {
  managerCommunityValidator,
} = require('../../../communitiesAPI/validations/community.validation');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const {
  rateLimitMiddleware,
  MODULE_TYPE,
} = require('../../../utils/rateLimit.util');
const payloadSignatureValidator = require('../../../validations/payloadSignature.validation');
const { handlerWrapper } = require('../../../utils/request.util');
const apiKeyValidation = require('../../../validations/apiKey.validation');

const setupRouter = async function (router) {
  router.route('/communities/:communityId/topup').post(
    postRoutePreHandlerMiddleware,
    rateLimitMiddleware({
      module: MODULE_TYPE.COMMUNITY_SIGNUP,
    }),
    payloadSignatureValidator,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    handlerWrapper({
      handler: controller.createTopupOrder,
      requestValidators: {
        body: schema.createTopupOrderBodySchema,
        params: schema.communityIdSchema,
      },
    })
  );

  router.route('/communities/:communityId/topup/confirm').post(
    postRoutePreHandlerMiddleware,
    rateLimitMiddleware({
      module: MODULE_TYPE.COMMUNITY_SIGNUP,
    }),
    payloadSignatureValidator,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    handlerWrapper({
      handler: controller.confirmTopupOrder,
      requestValidators: {
        body: schema.confirmTopupOrderBodySchema,
        params: schema.communityIdSchema,
      },
    })
  );

  router.route('/communities/:communityId/topup/verify-payment').post(
    postRoutePreHandlerMiddleware,
    rateLimitMiddleware({
      module: MODULE_TYPE.COMMUNITY_SIGNUP,
    }),
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    handlerWrapper({
      handler: controller.verifyTopupOrderPayment,
      requestValidators: {
        body: schema.verifyTopupOrderPaymentBodySchema,
        params: schema.communityIdSchema,
      },
    })
  );

  router
    .route('/communities/:communityId/topup/update-payment-status')
    .post(
      postRoutePreHandlerMiddleware,
      apiKeyValidation,
      handlerWrapper({
        handler: controller.updateTopupOrderPaymentStatus,
        requestValidators: {
          body: schema.updateTopupOrderPaymentStatusBodySchema,
          params: schema.communityIdSchema,
        },
      })
    );
};

module.exports = {
  setupRouter,
};
