const yup = require('yup');

exports.subscriptionIdSchema = yup.object().shape({
  subscriptionId: yup.string().notRequired(),
});

exports.postChangePlanSchema = yup.object().shape({
  priceId: yup.string().trim().required(),
  discountCode: yup.string().trim().notRequired(),
});

exports.changePlanUpdateSchema = yup.object().shape({
  purchaseTransactionObjectId: yup.string().trim().required(),
});

exports.findOrCreateFreeSubscriptionSchema = yup.object().shape({
  learnerObjectId: yup.string().trim().required(),
  communityObjectId: yup.string().trim().required(),
});
