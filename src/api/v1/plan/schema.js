const yup = require('yup');

const { PAYMENT_PROVIDER } = require('../../../constants/common');
const {
  ENTITY_TYPE,
  PLAN_ADHOC_STATUS,
} = require('../../../services/plan/constants');

exports.planTypeSchema = yup.object().shape({
  communityObjectId: yup.string().trim().required(),
  entityType: yup.string().trim().notRequired(),
  communityReferralCode: yup.string().uppercase().trim().notRequired(),
});

exports.cancelPlanSchema = yup.object().shape({
  communityObjectId: yup.string().trim().required(),
  cancellationReasons: yup
    .array()
    .of(
      yup.object().shape({
        key: yup.string().trim().required(),
        reason: yup.string().trim().notRequired(),
      })
    )
    .max(3, 'Only a maximum of 3 reasons can be selected'),
});

exports.changePlanSchema = yup.object().shape({
  communityReferralCode: yup.string().uppercase().trim().notRequired(),
  communityObjectId: yup.string().trim().required(),
  priceId: yup.string().trim().required(),
});

const adhocRecordSchema = yup.object().shape({
  communityCode: yup.string().trim().required(),
  creatorEmail: yup.string().trim().required(),
  notes: yup.string().trim().nullable().notRequired().default(''),
  status: yup
    .string()
    .trim()
    .required()
    .oneOf(Object.values(PLAN_ADHOC_STATUS)),
  entityType: yup
    .string()
    .trim()
    .required()
    .oneOf(Object.values(ENTITY_TYPE)),
});

exports.processAdhocEnrollmentsSchema = yup.array().of(adhocRecordSchema);

exports.cancelAdhocPlanSchema = yup.object().shape({
  email: yup.string().trim().required(),
});

exports.transferPlanSchema = yup.object().shape({
  transferFromCommunityObjectId: yup.string().trim().required(),
  transferToCommunityObjectId: yup.string().trim().required(),
  entityType: yup.string().trim().required(),
});

exports.updatePaymentMethodSchema = yup.object().shape({
  communityObjectId: yup.string().trim().required(),
  metadata: yup.object().notRequired(),
});

exports.updateClientInfoSchema = yup.object().shape({
  communityObjectId: yup.string().trim().required(),
  phoneNumber: yup.string().trim().notRequired(),
});

exports.communitySignupBodySchema = yup.object().shape({
  communityReferralCode: yup.string().uppercase().trim().notRequired(),
  signupToken: yup.string().notRequired().trim(),
  communityCode: yup.string().required().trim(),
  timezone: yup.string().required().trim(),
  requestor: yup.string().required().trim(),
  memberInfo: yup.object().notRequired().nullable().default(null),
  paymentProvider: yup
    .string()
    .required()
    .oneOf([
      PAYMENT_PROVIDER.STRIPE_US,
      PAYMENT_PROVIDER.STRIPE_INDIA,
      PAYMENT_PROVIDER.EBANX,
    ]),
  trackingData: yup.object().required(),
  items: yup.array().of(
    yup.object().shape({
      entityType: yup
        .string()
        .oneOf(Object.values(ENTITY_TYPE))
        .required(),
      planObjectId: yup.string().when('entityType', {
        is: (val) => Object.values(ENTITY_TYPE).includes(val),
        then: yup.string().trim().required(),
        otherwise: yup.string().trim().notRequired(),
      }),
      priceId: yup.string().when('entityType', {
        is: (val) => Object.values(ENTITY_TYPE).includes(val),
        then: yup.string().trim().required(),
        otherwise: yup.string().trim().notRequired(),
      }),
      nextBillingPriceId: yup.string().trim().notRequired(),
    })
  ),
});

exports.communitySignupConfirmBodySchema = yup.object().shape({
  signupToken: yup.string().required().trim(),
  metadata: yup.object().notRequired().nullable(),
  paymentMethodId: yup.string().notRequired().trim().nullable(),
  confirmType: yup.string().required().oneOf(Object.values(ENTITY_TYPE)),
});

exports.planOrderSchema = yup.object().shape({
  planObjectId: yup.string().trim().notRequired().nullable().default(null),
  planOrderObjectId: yup
    .string()
    .trim()
    .notRequired()
    .nullable()
    .default(null),
  communityObjectId: yup.string().trim().required(),
  fromAdhoc: yup.boolean().notRequired().default(false),
});

exports.handleProPlanPurchaseSuccessBodySchema = yup.object().shape({
  planOrderObjectId: yup.string().trim().required(),
});

exports.handleCancelCommunityPlanSuccessBodySchema = yup.object().shape({
  planOrderObjectId: yup.string().trim().required(),
});

exports.handleDeactivateAdhocProPlanRecordBodySchema = yup.object().shape({
  communityObjectId: yup.string().trim().required(),
  email: yup.string().trim().required(),
});

exports.changeTierBodySchema = yup.object().shape({
  communityReferralCode: yup.string().uppercase().trim().notRequired(),
  signupToken: yup.string().notRequired().trim(),
  communityCode: yup.string().required().trim(),
  timezone: yup.string().required().trim(),
  requestor: yup.string().required().trim(),
  memberInfo: yup.object().notRequired().nullable().default(null),
  paymentProvider: yup
    .string()
    .notRequired()
    .oneOf([
      PAYMENT_PROVIDER.STRIPE_US,
      PAYMENT_PROVIDER.STRIPE_INDIA,
      PAYMENT_PROVIDER.EBANX,
    ]),
  items: yup.array().of(
    yup.object().shape({
      entityType: yup
        .string()
        .oneOf(Object.values(ENTITY_TYPE))
        .required(),
      planObjectId: yup.string().when('entityType', {
        is: (val) => Object.values(ENTITY_TYPE).includes(val),
        then: yup.string().trim().required(),
        otherwise: yup.string().trim().notRequired(),
      }),
      priceId: yup.string().when('entityType', {
        is: (val) => Object.values(ENTITY_TYPE).includes(val),
        then: yup.string().trim().required(),
        otherwise: yup.string().trim().notRequired(),
      }),
    })
  ),
});

exports.changeTierConfirmBodySchema = yup.object().shape({
  signupToken: yup.string().required().trim(),
  metadata: yup.object().notRequired().nullable(),
  paymentMethodId: yup.string().trim().notRequired().nullable(),
  confirmType: yup.string().required().oneOf(Object.values(ENTITY_TYPE)),
});
