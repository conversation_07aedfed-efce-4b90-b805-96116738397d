const schema = require('./schema');
const service = require('../../../services/plan');
const changePlanService = require('../../../services/plan/changePlan');
const updatePaymentMethod = require('../../../services/plan/updatePaymentMethod');
const updateClientInfoService = require('../../../services/plan/clientInfo.service');
const signupService = require('../../../services/communitySignup/signup');
const signupConfirmService = require('../../../services/communitySignup/signupConfirm');
const {
  getCountryFromIP,
} = require('../../../services/countryFromIP/countryFromIP.service');
const {
  getUserIP,
  getUserAgent,
  getLanguagePreference,
} = require('../../../utils/headers.util');

exports.retrievePlanPrices = async (req) => {
  const ip = getUserIP(req);
  const country = await getCountryFromIP({ ip });
  const email = req.user.email;

  const { communityObjectId, entityType, communityReferralCode } =
    schema.planTypeSchema.cast(req.query);

  const planPrices = await service.retrievePlanPrices({
    communityObjectId,
    entityType,
    country,
    email,
    communityReferralCode,
  });

  return planPrices;
};

exports.changePlan = async (req) => {
  const learnerObjectId = req.user.learner._id;

  const { priceId, communityObjectId, communityReferralCode } =
    schema.changePlanSchema.cast(req.body);

  const result = await changePlanService.changePlan({
    communityObjectId,
    learnerObjectId,
    priceId,
    communityReferralCode,
  });

  return result;
};

exports.transferPlan = async (req) => {
  const {
    transferFromCommunityObjectId,
    transferToCommunityObjectId,
    entityType,
  } = schema.transferPlanSchema.cast(req.body);

  const result = await service.transferPlan({
    transferFromCommunityObjectId,
    transferToCommunityObjectId,
    entityType,
  });

  return result;
};

exports.updatePaymentMethod = async (req) => {
  const learnerObjectId = req.user.learner._id;

  const { metadata, communityObjectId } =
    schema.updatePaymentMethodSchema.cast(req.body);

  const result =
    await updatePaymentMethod.updateSubscriptionPaymentMethodForPlan({
      communityObjectId,
      learnerObjectId,
      metadata,
    });

  return result;
};

exports.updatePlanClientInfo = async (req) => {
  const learnerObjectId = req.user.learner._id;

  const { phoneNumber, communityObjectId } =
    schema.updateClientInfoSchema.cast(req.body);

  const result = await updateClientInfoService.updatePlanClientInfo({
    communityObjectId,
    learnerObjectId,
    phoneNumber,
  });

  return result;
};

exports.retrievePlan = async (req) => {
  const email = req.user.email;
  const learnerObjectId = req.user.learner._id;

  const { communityObjectId } = schema.planTypeSchema.cast(req.query);

  const planInfo = await service.retrievePlanInfo({
    communityObjectId,
    email,
    learnerObjectId,
  });

  return planInfo;
};

exports.cancelPlan = async (req) => {
  const email = req.user.email;

  const { communityObjectId, cancellationReasons } =
    schema.cancelPlanSchema.cast(req.body);

  const result = await service.cancelPlan({
    communityObjectId,
    cancellationReasons,
    email,
  });

  return result;
};

exports.processAdhocEnrollments = async (req) => {
  const addedBy = req.user.email;
  const adhocRecords = schema.processAdhocEnrollmentsSchema.cast(req.body);

  const result = await service.processAdhocEnrollments({
    adhocRecords,
    addedBy,
  });

  return result;
};

exports.cancelAdhocPlan = async (req) => {
  const { email } = schema.cancelAdhocPlanSchema.cast(req.body);

  const result = await service.cancelAllAdhocPlan({
    email,
  });

  return result;
};

exports.getPlanCancelReasons = async (req) => {
  const languagePreference = getLanguagePreference(req);
  const result = await service.retrievePlanCancelReasons({
    languagePreference,
  });

  return result;
};

exports.deletePlan = async (req) => {
  const email = req.user.email;

  const { communityObjectId } = schema.planTypeSchema.cast(req.query);

  const result = await service.cancelPlan({
    communityObjectId,
    email,
  });

  return result;
};

exports.signup = async (req) => {
  const {
    signupToken,
    communityCode,
    timezone,
    requestor,
    memberInfo,
    paymentProvider,
    trackingData,
    items,
    communityReferralCode,
  } = schema.communitySignupBodySchema.cast(req.body);

  const ip = getUserIP(req) || null;
  const userAgent = getUserAgent(req) || null;
  const requestLanguagePreference = getLanguagePreference(req);
  const email = req.user.email;
  const learnerObjectId = req.user.learner._id;

  const result = await signupService.signupForPlan({
    ip,
    userAgent,
    requestLanguagePreference,
    email,
    memberInfo,
    learnerObjectId,
    signupToken,
    communityCode,
    timezone,
    requestor,
    paymentProvider,
    trackingData,
    items,
    communityReferralCode,
  });

  return result;
};

exports.signupConfirm = async (req) => {
  const { signupToken, metadata, confirmType, paymentMethodId } =
    schema.communitySignupConfirmBodySchema.cast(req.body);

  const ip = getUserIP(req) || null;
  const userAgent = getUserAgent(req) || null;

  const result = await signupConfirmService.confirmForPlan({
    ip,
    userAgent,
    signupToken,
    metadata,
    confirmType,
    paymentMethodId,
  });

  return result;
};

exports.changeTier = async (req) => {
  const {
    signupToken,
    communityCode,
    timezone,
    requestor,
    memberInfo,
    paymentProvider,
    trackingData,
    items,
    communityReferralCode,
  } = schema.changeTierBodySchema.cast(req.body);

  const ip = getUserIP(req) || null;
  const userAgent = getUserAgent(req) || null;
  const requestLanguagePreference = getLanguagePreference(req);
  const email = req.user.email;
  const learnerObjectId = req.user.learner._id;

  const result = await signupService.signupForPlanTierChange({
    ip,
    userAgent,
    requestLanguagePreference,
    email,
    memberInfo,
    learnerObjectId,
    signupToken,
    communityCode,
    timezone,
    requestor,
    paymentProvider,
    trackingData,
    items,
    communityReferralCode,
  });

  return result;
};

exports.changeTierConfirm = async (req) => {
  const { signupToken, metadata, confirmType, paymentMethodId } =
    schema.communitySignupConfirmBodySchema.cast(req.body);

  const ip = getUserIP(req) || null;
  const userAgent = getUserAgent(req) || null;

  const result = await signupConfirmService.confirmForPlanTierChange({
    ip,
    userAgent,
    signupToken,
    metadata,
    confirmType,
    paymentMethodId,
  });

  return result;
};

exports.switchEnrollPlanOrder = async (req) => {
  const { planObjectId, planOrderObjectId, communityObjectId, fromAdhoc } =
    schema.planOrderSchema.cast(req.body);

  const result = await service.switchEnrollPlanOrder({
    planObjectId,
    planOrderObjectId,
    communityObjectId,
    fromAdhoc,
  });

  return result;
};

exports.enrollPlanOrder = async (req) => {
  const { planObjectId, planOrderObjectId, communityObjectId, fromAdhoc } =
    schema.planOrderSchema.cast(req.body);

  const result = await service.enrollPlanOrder({
    planObjectId,
    planOrderObjectId,
    communityObjectId,
    fromAdhoc,
  });

  return result;
};

exports.unenrollPlanOrder = async (req) => {
  const { planObjectId, planOrderObjectId, communityObjectId, fromAdhoc } =
    schema.planOrderSchema.cast(req.body);

  const result = await service.unenrollPlanOrder({
    planObjectId,
    planOrderObjectId,
    communityObjectId,
    fromAdhoc,
  });

  return result;
};

exports.handleProPlanPurchaseSuccess = async (req) => {
  const { planOrderObjectId } =
    schema.handleProPlanPurchaseSuccessBodySchema.cast(req.body);

  const result = await service.handleProPlanPurchaseSuccess({
    planOrderObjectId,
  });

  return result;
};

exports.handleCancelCommunityPlanSuccess = async (req) => {
  const { planOrderObjectId } =
    schema.handleCancelCommunityPlanSuccessBodySchema.cast(req.body);

  const result = await service.handleCancelCommunityPlanSuccess({
    planOrderObjectId,
  });

  return result;
};

exports.handleDeactivateAdhocProPlan = async (req) => {
  const { communityObjectId, email } =
    schema.handleDeactivateAdhocProPlanRecordBodySchema.cast(req.body);

  const result = await service.handleDeactivateAdhocProPlan({
    communityObjectId,
    email,
  });

  return result;
};
