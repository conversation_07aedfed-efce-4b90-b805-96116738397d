const { Router } = require('express');
const schema = require('./schema');
const controller = require('./controller');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');
const payloadSignatureValidator = require('../../../validations/payloadSignature.validation');
const copsTokenMiddleware = require('../../../middleware/cops-token.middleware');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const {
  MODULE_TYPE,
  rateLimitMiddleware,
} = require('../../../utils/rateLimit.util');
const apiKeyValidation = require('../../../validations/apiKey.validation');

const { handlerWrapper } = require('../../../utils/request.util');

const router = Router({ mergeParams: true });

router.route('/prices').get(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  handlerWrapper({
    handler: controller.retrievePlanPrices,
    requestValidators: {
      query: schema.planTypeSchema,
    },
  })
);

router.route('/change-plan').post(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  handlerWrapper({
    handler: controller.changePlan,
    requestValidators: {
      body: schema.changePlanSchema,
    },
  })
);

router.route('/change-tier').post(
  postRoutePreHandlerMiddleware,
  rateLimitMiddleware({
    module: MODULE_TYPE.COMMUNITY_SIGNUP,
  }),
  payloadSignatureValidator,
  tokenValidator(),
  userValidation,
  handlerWrapper({
    handler: controller.changeTier,
    requestValidators: {
      body: schema.changeTierBodySchema,
    },
  })
);

router.route('/change-tier/confirm').post(
  postRoutePreHandlerMiddleware,
  payloadSignatureValidator,
  tokenValidator(),
  userValidation,
  handlerWrapper({
    handler: controller.changeTierConfirm,
    requestValidators: {
      body: schema.changeTierConfirmBodySchema,
    },
  })
);

router.route('/cancel-plan').post(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  handlerWrapper({
    handler: controller.cancelPlan,
    requestValidators: {
      body: schema.cancelPlanSchema,
    },
  })
);

router.route('/cancellation-reasons').get(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  handlerWrapper({
    handler: controller.getPlanCancelReasons,
  })
);

router.route('/adhoc-enrollments/cops').post(
  postRoutePreHandlerMiddleware,
  copsTokenMiddleware,
  handlerWrapper({
    handler: controller.processAdhocEnrollments,
    requestValidators: {
      body: schema.processAdhocEnrollmentsSchema,
    },
  })
);

router.route('/cancel-adhoc-records').post(
  postRoutePreHandlerMiddleware,
  apiKeyValidation,
  handlerWrapper({
    handler: controller.cancelAdhocPlan,
    requestValidators: {
      body: schema.cancelAdhocPlanSchema,
    },
  })
);

router.route('/transfer-plan/cops').post(
  postRoutePreHandlerMiddleware,
  copsTokenMiddleware,
  handlerWrapper({
    handler: controller.transferPlan,
    requestValidators: {
      body: schema.transferPlanSchema,
    },
  })
);

router.route('/update-payment-method').post(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  handlerWrapper({
    handler: controller.updatePaymentMethod,
    requestValidators: {
      body: schema.updatePaymentMethodSchema,
    },
  })
);

router.route('/update-client-info').post(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  handlerWrapper({
    handler: controller.updatePlanClientInfo,
    requestValidators: {
      body: schema.updateClientInfoSchema,
    },
  })
);

router.route('/signup').post(
  postRoutePreHandlerMiddleware,
  rateLimitMiddleware({
    module: MODULE_TYPE.COMMUNITY_SIGNUP,
  }),
  payloadSignatureValidator,
  tokenValidator(),
  userValidation,
  handlerWrapper({
    handler: controller.signup,
    requestValidators: {
      body: schema.communitySignupBodySchema,
    },
  })
);

router.route('/signup/confirm').post(
  postRoutePreHandlerMiddleware,
  payloadSignatureValidator,
  tokenValidator(),
  userValidation,
  handlerWrapper({
    handler: controller.signupConfirm,
    requestValidators: {
      body: schema.communitySignupConfirmBodySchema,
    },
  })
);

router
  .route('/')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    handlerWrapper({
      handler: controller.retrievePlan,
      requestValidators: {
        query: schema.planTypeSchema,
      },
    })
  )
  .delete(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    handlerWrapper({
      handler: controller.deletePlan,
      requestValidators: {
        query: schema.planTypeSchema,
      },
    })
  );

router.route('/switch-enroll').post(
  postRoutePreHandlerMiddleware,
  apiKeyValidation,
  handlerWrapper({
    handler: controller.switchEnrollPlanOrder,
    requestValidators: {
      body: schema.planOrderSchema,
    },
  })
);

router.route('/enroll').post(
  postRoutePreHandlerMiddleware,
  apiKeyValidation,
  handlerWrapper({
    handler: controller.enrollPlanOrder,
    requestValidators: {
      body: schema.planOrderSchema,
    },
  })
);

router.route('/unenroll').post(
  postRoutePreHandlerMiddleware,
  apiKeyValidation,
  handlerWrapper({
    handler: controller.unenrollPlanOrder,
    requestValidators: {
      body: schema.planOrderSchema,
    },
  })
);

// Callback from Payment Backend on pro plan purchase success.
router.route('/handle-pro-plan-purchase-success').post(
  postRoutePreHandlerMiddleware,
  apiKeyValidation,
  handlerWrapper({
    handler: controller.handleProPlanPurchaseSuccess,
    requestValidators: {
      body: schema.handleProPlanPurchaseSuccessBodySchema,
    },
  })
);

// Callback after community plan cancellation from Payment Backend
router.route('/handle-cancel-community-plan-success').post(
  postRoutePreHandlerMiddleware,
  apiKeyValidation,
  handlerWrapper({
    handler: controller.handleCancelCommunityPlanSuccess,
    requestValidators: {
      body: schema.handleCancelCommunityPlanSuccessBodySchema,
    },
  })
);

// Called from AdminJS on deactivation of pro plan adhoc records
router.route('/handle-deactivate-adhoc-pro-plan-record').post(
  postRoutePreHandlerMiddleware,
  apiKeyValidation,
  handlerWrapper({
    handler: controller.handleDeactivateAdhocProPlan,
    requestValidators: {
      body: schema.handleDeactivateAdhocProPlanRecordBodySchema,
    },
  })
);

module.exports = router;
