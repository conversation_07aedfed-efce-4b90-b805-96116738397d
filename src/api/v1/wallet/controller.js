const schema = require('./schema');
const logger = require('../../../services/logger.service');
const { WalletService } = require('../../../services/wallet');

exports.getWalletBalance = async (req, res, next) => {
  try {
    const { communityObjectId } = await schema.getWalletSchema.cast(
      req.query
    );
    const learner = req.user.learner;

    const result = await WalletService.getWalletBalance(
      communityObjectId,
      learner._id
    );

    return res.json({ data: result });
  } catch (err) {
    logger.error('getWalletBalance failed due to', err.message);
    return next(err);
  }
};

exports.updateWalletBalance = async (req, res, next) => {
  try {
    const {
      communityObjectId,
      learnerObjectId,
      transactionId,
      transactionType,
      paymentProvider,
      amountBreakdownInBaseCurrency,
      amountBreakdownInUsd,
      transactionCreatedAt,
      revenueTransactionPurchasedId,
      affiliate,
      targetWalletType,
    } = await schema.updateWalletSchema.cast(req.body);

    const result = await WalletService.updateWalletBalance({
      communityObjectId,
      learnerObjectId,
      transactionId,
      transactionType,
      paymentProvider,
      amountBreakdownInBaseCurrency,
      amountBreakdownInUsd,
      transactionCreatedAt,
      revenueTransactionPurchasedId,
      affiliate,
      targetWalletType,
    });
    return res.json({ data: result });
  } catch (err) {
    logger.error(
      'updateWalletBalance failed due to',
      err.message,
      err.stack
    );
    return next(err);
  }
};

exports.resetWalletBalanceByTransaction = async (req, res, next) => {
  try {
    const { transactionId, targetWalletType } =
      await schema.resetWalletByTransactionSchema.cast(req.body);

    const result = await WalletService.resetWalletBalanceByTransaction({
      transactionId,
      targetWalletType,
    });
    return res.json({ data: result });
  } catch (err) {
    logger.error(
      'resetWalletBalanceByTransaction failed due to',
      err.message,
      err.stack
    );
    return next(err);
  }
};
