const schema = require('./schema');
const controller = require('./controller');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const {
  validateAll,
} = require('../../../middleware/validator.middleware');
const apiKeyValidation = require('../../../validations/apiKey.validation');

exports.setupRouter = function (router) {
  router
    .route('/wallets')
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      validateAll([
        {
          schema: schema.getWalletSchema,
          location: 'query',
        },
      ]),
      controller.getWalletBalance
    )
    .post(
      postRoutePreHandlerMiddleware,
      apiKeyValidation,
      validateAll([
        {
          schema: schema.updateWalletSchema,
          location: 'body',
        },
      ]),
      controller.updateWalletBalance
    );
  router.route('/wallets/resetByTransaction').post(
    postRoutePreHandlerMiddleware,
    apiKeyValidation,
    validateAll([
      {
        schema: schema.resetWalletByTransactionSchema,
        location: 'body',
      },
    ]),
    controller.resetWalletBalanceByTransaction
  );
};
