/* eslint-disable no-unused-vars */
const httpStatus = require('http-status');
const schema = require('./schema');
const logger = require('../../../services/logger.service');
const discountService = require('../../../communitiesAPI/services/common/communityDiscounts.service');
const communityProductService = require('../../../services/common/communityProducts.service');

const validateDiscountCode = async (req, res, next) => {
  try {
    const { code } = await schema.validateDiscountCodeSchema.cast(
      req.query
    );
    const community = await discountService.validateCommunityById(
      req.params.communityId,
      '_id code'
    );
    const response = await discountService.validateDiscountCode(
      code,
      community.code
    );
    return res
      .status(httpStatus.OK)
      .json({ message: response ? 'success' : 'failure' });
  } catch (err) {
    logger.error('getCommunityDiscounts failed due to', err);
    return next(err);
  }
};

const getCommunityDiscounts = async (req, res, next) => {
  try {
    const {
      pageNo,
      pageSize,
      sortBy,
      sortOrder,
      excludeEmptyEntities,
      entityType,
      search,
      status,
    } = await schema.getCommunityDiscountsSchema.cast(req.query);

    const response = await discountService.getDiscountsForCommunity({
      communityObjectId: req.params.communityId,
      pageNo,
      pageSize,
      sortBy,
      sortOrder,
      excludeEmptyEntities,
      entityType,
      search,
      status,
    });
    return res.status(httpStatus.OK).json({ data: response });
  } catch (err) {
    logger.error('getCommunityDiscounts failed due to', err);
    return next(err);
  }
};

const getSingleDiscountDetails = async (req, res, next) => {
  try {
    if (!req.params.id) {
      logger.info('Discount id not specified in params');
      const error = new Error('Discount ID not specified in params');
      error.status = httpStatus.BAD_REQUEST;
      return next(error);
    }

    const discountDetails = await discountService.getOneDiscountById(
      req.params.id
    );
    res.json({ data: discountDetails });
  } catch (err) {
    logger.error('getSingleDiscountDetails failed due to', err);
    return next(err);
  }
};

const createCommunityDiscount = async (req, res, next) => {
  const payload = await schema.createDiscountSchema.cast(req.body);

  payload.communityObjectId = req.params.communityId;
  payload.createdBy = req.user.email;

  try {
    const discount = await discountService.createCommunityDiscount(
      payload
    );
    res.json({ data: discount });
  } catch (err) {
    logger.error('createCommunityDiscount failed due to', err);
    return next(err);
  }
};

const updateCommunityDiscount = async (req, res, next) => {
  const payload = await schema.updateDiscountSchema.cast(req.body);
  if (!req.params.id) {
    logger.info('Discount id not specified in params');
    const error = new Error('Discount id not specified in params');
    error.status = httpStatus.BAD_REQUEST;
    return next(error);
  }

  payload.updatedBy = req.user.email;
  payload.discountObjectId = req.params.id;
  payload.communityObjectId = req.params.communityId;
  try {
    const discount = await discountService.updateCommunityDiscount(
      payload
    );
    res.json({ data: discount });
  } catch (err) {
    logger.error('updateCommunityDiscount failed due to', err);
    return next(err);
  }
};

const bulkGenerateDiscountCodes = async (req, res, next) => {
  const payload = await schema.bulkGenerateDiscountCodes.cast(req.body);

  payload['communityObjectId'] = req.params.communityId;
  const results = await discountService.bulkGenerateDiscountCodes(payload);
  return results;
};

const retrieveAllProducts = async (req, res) => {
  const { communityId: communityObjectId } = schema.communityIdSchema.cast(
    req.params
  );
  const { search } = schema.getDiscountProductsSchema.cast(req.query);

  const result = await communityProductService.retrieveAllProducts({
    communityObjectId,
    search,
  });

  return result;
};

const removeCommunityDiscount = async (req, res, next) => {
  const { discountId, communityId } =
    await schema.removeDiscountSchema.cast(req.params);
  const discount = await discountService.removeCommunityDiscount(
    discountId,
    communityId
  );
  return discount;
};

module.exports = {
  validateDiscountCode,
  getCommunityDiscounts,
  getSingleDiscountDetails,
  createCommunityDiscount,
  updateCommunityDiscount,
  bulkGenerateDiscountCodes,
  retrieveAllProducts,
  removeCommunityDiscount,
};
