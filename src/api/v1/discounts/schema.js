const yup = require('yup');
const { sortParams } = require('../../../constants/common');

exports.getCommunityDiscountsSchema = yup.object().shape({
  pageSize: yup.number().default(20).required().integer(),
  pageNo: yup.number().default(1).required().integer(),
  sortBy: yup.string().default(sortParams.CREATED_AT).notRequired().trim(),
  sortOrder: yup.number().default(-1).notRequired(),
  excludeEmptyEntities: yup.bool().default(false),
  entityType: yup.string().uppercase().trim().notRequired(),
  search: yup.string().uppercase().trim().notRequired(),
  status: yup.string().uppercase().trim().notRequired(),
});

exports.getDiscountProductsSchema = yup.object().shape({
  search: yup.string().uppercase().trim().notRequired(),
});

exports.communityIdSchema = yup.object().shape({
  communityId: yup.string().trim().required(),
});

exports.validateDiscountCodeSchema = yup.object().shape({
  code: yup
    .string()
    .trim()
    .matches(
      /^[A-Z0-9]*$/,
      'Discount code should only contain capitalised letters and numbers'
    )
    .max(20, 'Discount code cannot be more than 20 characters!')
    .required(),
});

exports.createDiscountSchema = yup.object().shape({
  isActive: yup.boolean().required(),
  code: yup
    .string()
    .trim()
    .matches(
      /^[A-Z0-9]*$/,
      'Discount code should only contain capitalised letters and numbers'
    )
    .max(20, 'Discount code cannot be more than 20 characters!')
    .required(),
  value: yup.number().required(),
  type: yup.string().trim().required(),
  maxRedemptions: yup.number().notRequired(),
  maxRedemptionsPerPerson: yup.number().notRequired(),
  effectiveTimeStart: yup.date().notRequired(),
  effectiveTimeEnd: yup.date().notRequired(),
  linkedEntities: yup.array().default([]).notRequired(),
  trialDays: yup.number().notRequired(),
  intervalCount: yup.number().notRequired(),
  timezone: yup.string().trim().notRequired(),
});

exports.updateDiscountSchema = yup.object().shape({
  // Frontend now will only pass this param
  isActive: yup.boolean().notRequired(),

  // code: yup
  //   .string()
  //   .trim()
  //   .matches(
  //     /^[A-Z0-9]*$/,
  //     'Discount code should only contain capitalised letters and numbers'
  //   )
  //   .max(20, 'Discount code cannot be more than 20 characters!')
  //   .notRequired(),
  // value: yup.number().notRequired(),
  // type: yup.string().trim().notRequired(),
  // maxRedemptions: yup.number().notRequired(),
  // maxRedemptionsPerPerson: yup.number().notRequired(),
  // effectiveTimeStart: yup.date().notRequired(),
  // effectiveTimeEnd: yup.date().notRequired(),
  // linkedEntities: yup.array().notRequired(),
  // timezone: yup.string().trim().notRequired(),
});

exports.bulkGenerateDiscountCodes = yup.object().shape({
  value: yup.number().required(),
  type: yup.string().trim().required(),
  maxRedemptions: yup.number().notRequired(),
  maxRedemptionsPerPerson: yup.number().notRequired(),
  effectiveTimeStart: yup.date().notRequired(),
  effectiveTimeEnd: yup.date().notRequired(),
  linkedEntities: yup.array().default([]).notRequired(),
  prefix: yup.string().trim().notRequired(),
  trialDays: yup.number().notRequired(),
  intervalCount: yup.number().notRequired(),
  numberOfCodes: yup.number().required(),
  codeLengthWithoutPrefix: yup.number().notRequired(),
});

exports.removeDiscountSchema = yup.object().shape({
  discountId: yup.string().trim().required(),
  communityId: yup.string().trim().required(),
});
