const schema = require('./schema');
const uploadService = require('../../../services/upload');

exports.createPresigned = async (req) => {
  const { communityObjectId, useCase, fileExtension } =
    schema.postPresignedSchema.cast(req.body);

  const result = await uploadService.generatePresignedPost({
    communityObjectId,
    useCase,
    fileExtension: fileExtension.toLowerCase(),
  });

  return result;
};
