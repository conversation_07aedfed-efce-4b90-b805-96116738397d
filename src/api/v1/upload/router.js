const { Router } = require('express');
const schema = require('./schema');
const controller = require('./controller');
const payloadSignatureValidator = require('../../../validations/payloadSignature.validation');
const tokenValidatorWithoutError = require('../../../validations/tokenNoError.validation');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const {
  rateLimitMiddleware,
  MODULE_TYPE,
} = require('../../../utils/rateLimit.util');

const { handlerWrapper } = require('../../../utils/request.util');

const router = Router({ mergeParams: true });

router.route('/presigned').post(
  postRoutePreHandlerMiddleware,
  rateLimitMiddleware({
    module: MODULE_TYPE.PUBLIC,
  }),
  payloadSignatureValidator,
  tokenValidatorWithoutError,
  handlerWrapper({
    handler: controller.createPresigned,
    requestValidators: {
      body: schema.postPresignedSchema,
    },
  })
);

module.exports = router;
