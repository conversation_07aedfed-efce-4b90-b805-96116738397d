const yup = require('yup');
const { UPLOAD_USE_CASE } = require('../../../services/upload/constants');

exports.postPresignedSchema = yup.object().shape({
  communityObjectId: yup
    .string()
    .trim()
    .notRequired()
    .nullable()
    .default(null),
  useCase: yup
    .string()
    .trim()
    .required()
    .oneOf(Object.values(UPLOAD_USE_CASE)),
  fileExtension: yup.string().trim().required(),
});
