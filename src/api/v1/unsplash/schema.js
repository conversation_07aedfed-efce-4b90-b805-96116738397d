const yup = require('yup');

exports.getUnsplashPhotosSchema = yup.object().shape({
  page: yup.number().min(1).max(100).default(1).required(),
  perPage: yup.number().min(1).max(40).default(10).required(),
  query: yup.string().required(),
  orientation: yup.string().oneOf(['portrait', 'landscape']).required(),
});

exports.trackDownloadSchema = yup.object().shape({
  downloadLocation: yup.string().required(),
});
