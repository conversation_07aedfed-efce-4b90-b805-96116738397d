const schema = require('./schema');
const {
  getUnsplashImages,
  trackDownload,
} = require('../../../services/common/unsplash.service');

exports.getUnsplashPhotos = async (req) => {
  const { query, page, perPage, orientation } =
    schema.getUnsplashPhotosSchema.cast(req.query);

  const response = await getUnsplashImages({
    query,
    page,
    perPage,
    orientation,
  });

  return response;
};

exports.trackDownload = async (req) => {
  const { downloadLocation } = schema.trackDownloadSchema.cast(req.body);

  const result = await trackDownload(downloadLocation);
  return result;
};
