const { Router } = require('express');
const controller = require('./controller');
const schema = require('./schema');
const { handlerWrapper } = require('../../../utils/request.util');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');

const router = Router({ mergeParams: true });

router.route('/').get(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  handlerWrapper({
    handler: controller.getUnsplashPhotos,
    requestValidators: {
      query: schema.getUnsplashPhotosSchema,
    },
  })
);

router.route('/track-download').post(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  handlerWrapper({
    handler: controller.trackDownload,
    requestValidators: {
      body: schema.trackDownloadSchema,
    },
  })
);

module.exports = router;
