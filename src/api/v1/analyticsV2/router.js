const { Router } = require('express');
const schema = require('./schema');
const controller = require('./controller');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');

const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const {
  managerCommunityValidator,
} = require('../../../communitiesAPI/validations/community.validation');

const { handlerWrapper } = require('../../../utils/request.util');

const router = Router({ mergeParams: true });

router.route('/').get(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  handlerWrapper({
    requestValidators: {
      query: schema.analyticsQuerySchema,
      params: schema.communityIdSchema,
    },
    handler: controller.getAnalytics,
  })
);

router.route('/monthly-overview').get(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  handlerWrapper({
    requestValidators: {
      query: schema.monthlyAnalyticsQuerySchema,
      params: schema.communityIdSchema,
    },
    handler: controller.getMonthlyAnalyticsOverview,
  })
);

module.exports = router;
