const analyticsService = require('../../../services/analyticsV2');
const schema = require('./schema');

const getAnalytics = async (req) => {
  const { communityId } = await schema.communityIdSchema.cast(req.params);
  const {
    filter,
    duration,
    startDate,
    endDate,
    productId,
    isCommunityPage,
  } = await schema.analyticsQuerySchema.cast(req.query);

  // Call the services to get the analytics data

  const analytics = await analyticsService.retriveAnalytics({
    communityId,
    filter,
    duration,
    startDate,
    endDate,
    productId,
    isCommunityPage,
  });

  return analytics;
};

const getMonthlyAnalyticsOverview = async (req) => {
  const { communityId } = await schema.communityIdSchema.cast(req.params);
  const { startDate } = await schema.monthlyAnalyticsQuerySchema.cast(
    req.query
  );

  // Call the services to get the analytics data

  const analytics = await analyticsService.getMonthlyAnalyticsOverview({
    communityId,
    startDate,
  });
  return analytics;
};

module.exports = {
  getAnalytics,
  getMonthlyAnalyticsOverview,
};
