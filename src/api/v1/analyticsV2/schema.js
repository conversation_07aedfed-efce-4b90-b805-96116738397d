const yup = require('yup');

exports.communityIdSchema = yup.object().shape({
  communityId: yup.string().required(),
});

exports.analyticsQuerySchema = yup.object().shape({
  filter: yup.string().required().oneOf(['Source', 'Products']),
  duration: yup.string().notRequired(),
  startDate: yup.string().notRequired(),
  endDate: yup.string().notRequired(),
  productId: yup.string().notRequired(),
  isCommunityPage: yup.boolean().notRequired(),
});

exports.monthlyAnalyticsQuerySchema = yup.object().shape({
  startDate: yup.string().required(),
});
