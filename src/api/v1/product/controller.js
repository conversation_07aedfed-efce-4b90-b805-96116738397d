const schema = require('./schema');
const GetProductDataService = require('../../../services/product/getProductData.service');

exports.getProducts = async (req) => {
  const { communityId } = req.params;
  const {
    pageNo,
    pageSize,
    sortBy,
    sortOrder,
    searchString,
    productType,
    status,
    priceTypes,
    timingStatus,
  } = schema.productQueryParamSchema.cast(req.query);

  const result = await GetProductDataService.getProducts({
    communityObjectId: communityId,
    pageNo,
    pageSize,
    sortBy,
    sortOrder,
    searchString,
    productType,
    status,
    priceTypes,
    timingStatus,
  });
  return result;
};
