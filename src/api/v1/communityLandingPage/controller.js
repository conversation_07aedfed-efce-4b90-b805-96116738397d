/* eslint-disable no-unused-vars */
const communityServices = require('../../../services/community/index');
const landingPageServices = require('../../../services/landingPages/index');
const membershipServices = require('../../../services/membership/index');

const { getUserIP } = require('../../../utils/headers.util');
const { ParamError } = require('../../../utils/error.util');

const prefetchCommunityLandingPage = async (req, res, next) => {
  const user = req.user;
  const communitySlug = req.query.communitySlug;
  const communityId = req.query.communityId;
  if (!communitySlug && !communityId) {
    throw new ParamError(
      'At least one of communitySlug or communityId is required'
    );
  }
  let community;
  if (communitySlug) {
    community =
      await communityServices.getCommunityService.getCommunityByLink({
        link: communitySlug,
        projection: {
          $project: {
            code: 1,
            isActive: 1,
            isDraft: 1,
            link: 1,
          },
        },
      });
  }
  if (communityId) {
    community =
      await communityServices.getCommunityService.getCommunityById({
        communityId,
        projection: {
          code: 1,
          isActive: 1,
          isDraft: 1,
          link: 1,
        },
      });
  }

  if (!community) {
    return {
      communityData: {
        exists: false,
      },
    };
  }
  if (community.isDraft || !community.isActive) {
    return {
      communityData: {
        exists: true,
        isActive: false,
        _id: community._id,
        link: community.link,
      },
    };
  }

  if (!user) {
    return {
      communityData: {
        exists: true,
        isActive: true,
        _id: community._id,
        link: community.link,
      },
    };
  }

  const userRoles =
    await membershipServices.getService.getCommunityMemberRoleFromSource({
      email: user.email,
      communityCode: community.code,
    });

  return {
    communityData: {
      exists: true,
      isActive: true,
      _id: community._id,
      link: community.link,
    },
    communityUserData: {
      roles: userRoles,
    },
  };
};

const getCommunityLandingPage = async (req, res, next) => {
  const { communitySlug, affiliateCode } = req.query;
  const userFromRequest = req.user;
  const ip = getUserIP(req) || null;
  if (!communitySlug) {
    throw new ParamError('communitySlug not passed');
  }

  const communityLandingPageData =
    await landingPageServices.communityLandingPageService.getCommunityLandingPageData(
      {
        communitySlug,
        userFromRequest,
        ip,
        affiliateCode,
      }
    );

  return {
    communityData: communityLandingPageData,
  };
};

const getCommunityEntities = async (req) => {
  const { communityId } = req.params;

  const entities =
    await landingPageServices.communityLandingPageService.getCommunityEntitiesInfo(
      communityId,
      req.user.learner._id
    );
  return entities;
};
const getCommunityUpcoming = async (req, res, next) => {
  const communityId = req.params.communityId;
  const userFromRequest = req.user;

  const upcomingList =
    await landingPageServices.communityLandingPageService.getCommunityUpcoming(
      {
        communityId,
        userFromRequest,
      }
    );
  return {
    upcomingItems: upcomingList,
  };
};

const getCommunityMembers = async (req, res, next) => {
  const { otherFilters } =
    await membershipServices.membershipSearchUtils.processGetMembersQueryParams(
      req.query
    );
  const communityId = req.params.communityId;
  let searchString = req.query.searchString;
  if (searchString) {
    searchString = decodeURIComponent(searchString);
  }

  const limit = parseInt(req.query.pageSize || 100, 10);
  let skip = 0;
  if (req.query.pageNo) {
    skip = (parseInt(req.query.pageNo, 10) - 1) * limit;
  }
  const results =
    await landingPageServices.communityLandingPageService.getCommunityMembers(
      {
        searchString,
        currentUser: req.user,
        communityId,
        otherFilters,
        skip,
        limit,
      }
    );
  return results;
};

const getCommunityMember = async (req, res, next) => {
  const communityObjectId = req.params.communityId;
  const learnerObjectId = req.params.learnerObjectId;

  const result =
    await landingPageServices.communityLandingPageService.getCommunityMember(
      {
        communityObjectId,
        learnerObjectId,
      }
    );

  return result;
};

const getCommunityProducts = async (req, res, next) => {
  const communityId = req.params.communityId;
  const limit = parseInt(req.query.pageSize || 100, 10);
  const userFromRequest = req.user;
  let skip = 0;
  if (req.query.pageNo) {
    skip = (parseInt(req.query.pageNo, 10) - 1) * limit;
  }

  if (req.query.searchString) {
    throw new Error(`Not support search string in this api`);
  }
  const results =
    await landingPageServices.communityLandingPageService.getCommunityProducts(
      {
        communityId,
        userFromRequest,
        type: req.query.type,
        skip,
        limit,
      }
    );
  return results;
};

const getCommunityLandingPageDynamicData = async (req, res, next) => {
  const communityId = req.params.communityId;
  const userFromRequest = req.user;
  const ip = getUserIP(req) || null;
  const dynamicData =
    await landingPageServices.communityLandingPageService.getCommunityLandingPageDynamicData(
      {
        communityId,
        userFromRequest,
        ip,
      }
    );
  return dynamicData;
};

const getCommunityMemberSummary = async (req, res, next) => {
  const communityId = req.params.communityId;
  const countries =
    await landingPageServices.communityLandingPageService.getCommunityMemberSummary(
      {
        communityId,
      }
    );
  return countries;
};

module.exports = {
  prefetchCommunityLandingPage,
  getCommunityLandingPage,
  getCommunityLandingPageDynamicData,
  getCommunityUpcoming,
  getCommunityMembers,
  getCommunityMember,
  getCommunityProducts,
  getCommunityEntities,
  getCommunityMemberSummary,
};
