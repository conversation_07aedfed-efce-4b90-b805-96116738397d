const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');

const tokenValidatorWithoutError = require('../../../validations/tokenNoError.validation');
const tokenValidator = require('../../../validations/token.validation');
const { handlerWrapper } = require('../../../utils/request.util');

const controller = require('./controller');
const userValidation = require('../../../validations/user.validation');
const {
  memberCommunityValidator,
} = require('../../../communitiesAPI/validations/community.validation');

const setupRouter = function (router) {
  router.route('/community-landing-page/prefetch').get(
    postRoutePreHandlerMiddleware,
    tokenValidatorWithoutError,
    handlerWrapper({
      handler: controller.prefetchCommunityLandingPage,
    })
  );

  router.route('/community-landing-page').get(
    postRoutePreHandlerMiddleware,
    tokenValidatorWithoutError,
    handlerWrapper({
      handler: controller.getCommunityLandingPage,
    })
  );

  router
    .route('/communityLandingPage/:communityId/get-community-entities')
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      memberCommunityValidator,
      handlerWrapper({
        handler: controller.getCommunityEntities,
      })
    );

  router.route('/community-landing-page/:communityId/member/summary').get(
    postRoutePreHandlerMiddleware,
    tokenValidatorWithoutError,
    handlerWrapper({
      handler: controller.getCommunityMemberSummary,
    })
  );

  router.route('/community-landing-page/:communityId/upcoming').get(
    postRoutePreHandlerMiddleware,
    tokenValidatorWithoutError,
    handlerWrapper({
      handler: controller.getCommunityUpcoming,
    })
  );

  router.route('/community-landing-page/:communityId/dynamic-data').get(
    postRoutePreHandlerMiddleware,
    tokenValidatorWithoutError,
    handlerWrapper({
      handler: controller.getCommunityLandingPageDynamicData,
    })
  );

  router.route('/community-landing-page/:communityId/members').get(
    postRoutePreHandlerMiddleware,
    tokenValidatorWithoutError,
    memberCommunityValidator,
    handlerWrapper({
      handler: controller.getCommunityMembers,
    })
  );

  router
    .route('/community-landing-page/:communityId/members/:learnerObjectId')
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      memberCommunityValidator,
      handlerWrapper({
        handler: controller.getCommunityMember,
      })
    );

  router.route('/community-landing-page/:communityId/products').get(
    postRoutePreHandlerMiddleware,
    tokenValidatorWithoutError,
    handlerWrapper({
      handler: controller.getCommunityProducts,
    })
  );
};

module.exports = {
  setupRouter,
};
