const logger = require('../../../services/logger.service');
const service = require('../../../services/currency/retrieveSupportedCurrencies.service');

exports.getCommunityBaseCurrencies = async (req, res, next) => {
  try {
    const response = await service.retrieveCommunityBaseCurrencies();

    return res.json({ data: response });
  } catch (err) {
    logger.error('getCommunityBaseCurrencies failed due to', err.message);
    return next(err);
  }
};

exports.getPayoutCurrencies = async (req, res, next) => {
  try {
    const response = await service.retrievePayoutCurrencies();

    return res.json({ data: response });
  } catch (err) {
    logger.error('getPayoutCurrencies failed due to', err.message);
    return next(err);
  }
};
