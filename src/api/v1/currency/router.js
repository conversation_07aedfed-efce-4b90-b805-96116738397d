const { Router } = require('express');
const controller = require('./controller');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');

const router = Router({ mergeParams: true });

router
  .route('/')
  .get(
    postRoutePreHandlerMiddleware,
    controller.getCommunityBaseCurrencies
  );

router
  .route('/payout')
  .get(postRoutePreHandlerMiddleware, controller.getPayoutCurrencies);

module.exports = router;
