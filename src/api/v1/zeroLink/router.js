const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const {
  featureAccessValidator,
} = require('../../../validations/featureAccess.validation');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');
const {
  managerCommunityValidator,
} = require('../../../communitiesAPI/validations/community.validation');
const schema = require('./schema');
const controller = require('./controller');
const { handlerWrapper } = require('../../../utils/request.util');
const tokenNoErrorValidator = require('../../../validations/tokenNoError.validation');
const tokenValidatorWithoutError = require('../../../validations/tokenNoError.validation');
const userValidationWithoutError = require('../../../validations/userValidationWithoutError.validation');
const { FEATURE_LIST_ID } = require('../../../constants/common');

const ZERO_LINK_PREFIX = '/zero-link';
const FEATURE_ID = FEATURE_LIST_ID.ZERO_LINK;
const communityProjection = {
  _id: 1,
  code: 1,
  config: 1,
  isActive: 1,
  baseCurrency: 1,
};

const setupRouter = async function (router) {
  router
    .route(`/communities/:communityId${ZERO_LINK_PREFIX}`)
    .post(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      featureAccessValidator(FEATURE_ID, communityProjection),
      handlerWrapper({
        handler: controller.createZeroLink,
        requestValidators: {
          params: schema.communityIdSchema,
          body: schema.createZeroLinkSchema,
        },
      })
    )
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      featureAccessValidator(FEATURE_ID, communityProjection),
      handlerWrapper({
        handler: controller.getZeroLinks,
        requestValidators: {
          params: schema.communityIdSchema,
          query: schema.getZeroLinksSchema,
        },
      })
    );

  router
    .route(`/communities/:communityId${ZERO_LINK_PREFIX}/:zeroLinkId`)
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidatorWithoutError,
      userValidationWithoutError,
      handlerWrapper({
        handler: controller.getZeroLinkById,
        requestValidators: {
          query: schema.getZeroLinkByIdSchema,
        },
      })
    )
    .put(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      featureAccessValidator(FEATURE_ID, communityProjection),
      handlerWrapper({
        handler: controller.updateZeroLink,
        requestValidators: {
          body: schema.updateZeroLinkSchema,
        },
      })
    )
    .delete(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      featureAccessValidator(FEATURE_ID, communityProjection),
      handlerWrapper({
        handler: controller.deleteZeroLink,
        requestValidators: {
          params: schema.singleZeroLinkPathSchema,
        },
      })
    );

  router.route(`${ZERO_LINK_PREFIX}/get-zeroLink-by-slug`).get(
    postRoutePreHandlerMiddleware,
    tokenNoErrorValidator,
    handlerWrapper({
      handler: controller.getZeroLinkBySlug,
      requestValidators: {
        query: schema.getZeroLinkBySlugSchema,
      },
    })
  );
};

module.exports = {
  setupRouter,
};
