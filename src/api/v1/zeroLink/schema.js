const yup = require('yup');
const {
  ZERO_LINK_STATUS,
} = require('../../../services/zeroLink/constants');
const { PRICE_TYPE, sortParams } = require('../../../constants/common');

exports.communityIdSchema = yup.object().shape({
  communityId: yup.string().required(),
});

exports.singleZeroLinkPathSchema = yup.object().shape({
  zeroLinkId: yup.string().required().trim(),
  communityId: yup.string().required().trim(),
});

exports.getZeroLinksSchema = yup.object().shape({
  pageSize: yup.number().default(20).required().integer(),
  pageNo: yup.number().default(1).required().integer(),
  sortBy: yup.string().default(sortParams.CREATED_AT).notRequired().trim(),
  sortOrder: yup.number().default(-1).notRequired(),
  status: yup
    .string()
    .oneOf(Object.values(ZERO_LINK_STATUS))
    .notRequired()
    .trim(),
});

exports.createZeroLinkSchema = yup.object().shape({
  amount: yup.number(),
  currency: yup
    .string()
    .required()
    .trim()
    .transform((value) => value.toUpperCase()),
  status: yup
    .string()
    .oneOf(Object.values(ZERO_LINK_STATUS))
    .default(ZERO_LINK_STATUS.ACTIVE),
  pricingConfig: yup.object().shape({
    priceType: yup
      .string()
      .oneOf(Object.values(PRICE_TYPE))
      .required()
      .trim(),
  }),
  coverImg: yup.string().trim().default(''),
  title: yup.string().trim().default(''),
  message: yup.string().trim().default(''),
  redirectLink: yup.string().trim().default(''),
  passOnTakeRate: yup.boolean().default(false),
  passOnPaymentGatewayFee: yup.boolean().default(false),
});

exports.updateZeroLinkSchema = yup.object().shape({
  amount: yup.number(),
  currency: yup
    .string()
    .trim()
    .transform((value) => value.toUpperCase()),
  status: yup.string().oneOf(Object.values(ZERO_LINK_STATUS)),
  pricingConfig: yup
    .object()
    .shape({
      priceType: yup
        .string()
        .required()
        .oneOf(Object.values(PRICE_TYPE))
        .trim(),
    })
    .default(undefined),
  coverImg: yup.string().trim(),
  title: yup.string().trim(),
  message: yup.string().trim(),
  redirectLink: yup.string().trim(),
  passOnTakeRate: yup.boolean(),
  passOnPaymentGatewayFee: yup.boolean().default(undefined),
});

exports.getZeroLinkBySlugSchema = yup.object().shape({
  slug: yup.string().required().trim(),
  communityLink: yup.string().required().trim(),
});

exports.getZeroLinkByIdSchema = yup.object().shape({
  selectedAmount: yup.number().notRequired(),
  paymentMethodCountryCode: yup.string().trim().notRequired(),
  paymentProvider: yup.string().trim().notRequired(),
});
