const ZeroLinkManageService = require('../../../services/zeroLink/manageZeroLink.service');
const ZeroLinkGetService = require('../../../services/zeroLink/getZeroLink.service');
const schema = require('./schema');

exports.createZeroLink = async (req) => {
  const { communityId: communityObjectId } =
    await schema.communityIdSchema.cast(req.params);

  const {
    amount,
    currency,
    status,
    pricingConfig,
    coverImg,
    title,
    message,
    redirectLink,
    passOnTakeRate,
    passOnPaymentGatewayFee,
  } = await schema.createZeroLinkSchema.cast(req.body);

  const zeroLink = await ZeroLinkManageService.createZeroLink({
    communityObjectId,
    amount,
    currency,
    status,
    pricingConfig,
    coverImg,
    title,
    message,
    redirectLink,
    passOnTakeRate,
    passOnPaymentGatewayFee,
  });

  return zeroLink;
};

exports.updateZeroLink = async (req) => {
  const communityObjectId = req.params.communityId;
  const zeroLinkObjectId = req.params.zeroLinkId;

  const {
    amount,
    currency,
    status,
    pricingConfig,
    coverImg,
    title,
    message,
    redirectLink,
    passOnTakeRate,
    passOnPaymentGatewayFee,
  } = await schema.updateZeroLinkSchema.cast(req.body);

  const zeroLink = await ZeroLinkManageService.updateZeroLink({
    communityObjectId,
    zeroLinkObjectId,
    params: {
      amount,
      currency,
      status,
      pricingConfig,
      coverImg,
      title,
      message,
      redirectLink,
      passOnTakeRate,
      passOnPaymentGatewayFee,
    },
  });

  return zeroLink;
};

exports.deleteZeroLink = async (req) => {
  const { communityId, zeroLinkId } =
    await schema.singleZeroLinkPathSchema.cast(req.params);

  await ZeroLinkManageService.deleteZeroLink(communityId, zeroLinkId);
};

exports.getZeroLinks = async (req) => {
  const { community } = req.metadata;

  const { status, pageSize, pageNo, sortBy, sortOrder } =
    await schema.getZeroLinksSchema.cast(req.query);
  const zeroLink = await ZeroLinkGetService.getZeroLinks({
    community,
    status,
    pageSize,
    pageNo,
    sortBy,
    sortOrder,
  });

  return zeroLink;
};

exports.getZeroLinkBySlug = async (req) => {
  const { communityLink, slug } =
    await schema.getZeroLinkBySlugSchema.cast(req.query);

  const zeroLink = await ZeroLinkGetService.getZeroLinkBySlug({
    communityLink,
    slug,
  });

  return zeroLink;
};

exports.getZeroLinkById = async (req) => {
  const { communityId, zeroLinkId } = req.params;
  const { selectedAmount, paymentMethodCountryCode, paymentProvider } =
    await schema.getZeroLinkByIdSchema.cast(req.query);

  const zeroLink = await ZeroLinkGetService.getZeroLinkById({
    communityId,
    zeroLinkId,
    selectedAmount,
    paymentMethodCountryCode,
    paymentProvider,
  });

  return zeroLink;
};
