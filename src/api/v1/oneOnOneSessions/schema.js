const yup = require('yup');
const { SESSION_TYPES } = require('../../../constants/common');
const {
  coverMediaItemsSchema,
} = require('../../../validations/coverMediaItems.validation');

exports.sessionSchema = yup.object().shape({
  templateLibraryId: yup.string().notRequired(),
  thumbnail: yup.string().notRequired(), // legacy
  coverMediaItems: coverMediaItemsSchema,
  title: yup.string().required(),
  description: yup.string().notRequired(),
  durationIntervalInMinutes: yup.number().required(),
  access: yup.string().notRequired(),
  amount: yup.number().notRequired(),
  currency: yup.string().notRequired(),
  resourceSlug: yup
    .string()
    .trim()
    .test('resourceSlug', 'public link should have / in it', (value) => {
      if (value) {
        return value.includes('/');
      }
      return true;
    }),
  location: yup.object().shape({
    type: yup.string().required(),
    locationValue: yup.string().notRequired(),
  }),
  showLocation: yup
    .boolean()
    .test(
      'showLocationValidation',
      'showLocation only available for inPerson',
      function (item) {
        if (this.parent.location.type !== SESSION_TYPES.IN_PERSON && item)
          return false;
        return true;
      }
    )
    .notRequired(),
  status: yup.string().required(),
  type: yup.string().required(),
  createdBy: yup.string().required(),
  discountsToAdd: yup.array().default([]),
  discountsToRemove: yup.array().default([]),
  discountsToDisable: yup.array().default([]),
  newDiscountsToApply: yup.array().default([]),
  index: yup.number().notRequired(),
  communityObjectId: yup.string().trim().required(),
  availability: yup.array().default([]),
  timezoneChosenForAvailability: yup.string().required(),
  hostInfo: yup.object().shape({
    hostTitle: yup.string().notRequired(),
    hostBio: yup.string().notRequired(),
    hostLearnerObjectId: yup.string().required(),
  }),
  // minimumNoticeInDaysForBooking: yup.number().required(),
  // minimumNoticeInDaysForBooking should not be more than 30 days
  minimumNoticeInDaysForBooking: yup
    .number()
    .required()
    .max(
      30,
      'minimumNoticeInDaysForBooking should not be more than 30 days'
    ),
  applicationConfigDataFields: yup.array().default([]),
  stopAcceptingBookings: yup.boolean().default(false),
  unAvailableDates: yup.array().default([]),
  duplicateFromSessionId: yup.string().notRequired(),
});

exports.sessionAttendeesSchema = yup.object().shape({
  sessionStartTime: yup.date().required(),
  sessionEndTime: yup.date().required(),
  timezoneOfAttendee: yup.string().required(),
  attendeeLearnerObjectId: yup.string().required(),
  hostLearnerObjectId: yup.string().notRequired(),
  communityObjectId: yup.string().notRequired(),
  applicationData: yup.object().notRequired(),
  isPending: yup.boolean().required(),
  checkoutId: yup.string().required(),
  status: yup.string().required(),
});

exports.sessionAttendeesCancelledBodySchema = yup.object().shape({
  requestRefund: yup.boolean().required(),
  reasonForRefund: yup.string().when('requestRefund', {
    is: true,
    then: yup.string().required(),
  }),
  bookingId: yup.string().required(),
  transactionId: yup.string().when('requestRefund', {
    is: true,
    then: yup.string().required(),
  }),
});

exports.sessionAttendeesParamsSchema = yup.object().shape({
  sessionId: yup.string().required(),
  communityId: yup.string().required(),
});

exports.sessionBookingsParamsSchema = yup.object().shape({
  sessionId: yup.string().required(),
  communityId: yup.string().required(),
});

// it will paginated and it will have type =UPCOMING,PAST,PENDING,CANCELLED
exports.sessionBookingsQuerySchema = yup.object().shape({
  type: yup.string().required(),
  page: yup.number().required(),
  limit: yup.number().required(),
  order: yup.string().required(),
  search: yup.string().notRequired().trim(),
  sortBy: yup.string().notRequired(),
  sortOrder: yup.string().notRequired(),
});

exports.getExportSessionsBookingQuerySchema = yup.object().shape({
  type: yup.string().oneOf(['UPCOMING', 'PAST', 'ALL']).required(),
  order: yup.string().oneOf(['asc', 'desc']).required(),
  search: yup.string().notRequired().trim(),
});

exports.getSessionBookingsQuerySchema = yup.object().shape({
  status: yup.string().required(),
  sessionId: yup.string().notRequired(),
});
