const {
  managerCommunityValidator,
} = require('../../../communitiesAPI/validations/community.validation');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const userValidation = require('../../../validations/user.validation');
const tokenValidator = require('../../../validations/token.validation');
const apiKeyValidator = require('../../../validations/apiKey.validation');
const controller = require('./controller');
const schema = require('./schema');
const { handlerWrapper } = require('../../../utils/request.util');
const {
  validateAll,
} = require('../../../middleware/validator.middleware');

const setupRouter = async function (router) {
  router.route('/communities/:communityId/session/create').post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    validateAll([
      {
        schema: schema.sessionSchema,
        location: 'body',
      },
    ]),
    handlerWrapper({
      handler: controller.createOneOnOneSession,
    })
  );

  router.route('/communities/:communityId/session/:sessionId').put(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    handlerWrapper({
      handler: controller.updateOneOnOneSession,
    })
  );

  router.route('/communities/:communityId/session/:sessionId/slots').get(
    postRoutePreHandlerMiddleware,
    handlerWrapper({
      handler: controller.getOneOnOneSessionSlots,
    })
  );

  router.route('/communities/:communityId/hostInfo/:hostObjectId').get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    handlerWrapper({
      handler: controller.getHostInfo,
    })
  );
  // internal API need to remove this router before we release
  router
    .route('/communities/:communityId/session/:sessionId/book-session')
    .post(
      postRoutePreHandlerMiddleware,
      apiKeyValidator,
      validateAll([
        {
          schema: schema.sessionAttendeesSchema,
          location: 'body',
        },
        {
          schema: schema.sessionAttendeesParamsSchema,
          location: 'params',
        },
      ]),
      // managerCommunityValidator,
      handlerWrapper({
        handler: controller.bookSession,
      })
    );

  router
    .route('/communities/:communityId/session/:sessionId/cancel-booking')
    .put(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      validateAll([
        {
          schema: schema.sessionAttendeesCancelledBodySchema,
          location: 'body',
        },
        {
          schema: schema.sessionAttendeesParamsSchema,
          location: 'params',
        },
      ]),
      handlerWrapper({
        handler: controller.cancelSessionBookings,
      })
    );

  router
    .route('/communities/:communityId/session/:sessionId/bookings')
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      validateAll([
        { schema: schema.sessionBookingsQuerySchema, location: 'query' },
        {
          schema: schema.sessionBookingsParamsSchema,
          location: 'params',
        },
      ]),
      handlerWrapper({
        handler: controller.getSessionBookings,
      })
    );

  router
    .route('/communities/:communityId/session/:sessionId/bookings-csv')
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      validateAll([
        {
          schema: schema.getExportSessionsBookingQuerySchema,
          location: 'query',
        },
        {
          schema: schema.sessionBookingsParamsSchema,
          location: 'params',
        },
      ]),
      controller.getSessionBookingsCSV
    );

  router.route('/communities/session/bookings').get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    validateAll([
      {
        schema: schema.getSessionBookingsQuerySchema,
        location: 'query',
      },
    ]),
    handlerWrapper({
      handler: controller.getBookings,
    })
  );

  router.route('/session-bookings-reminder').post(
    postRoutePreHandlerMiddleware,
    apiKeyValidator,
    handlerWrapper({
      handler: controller.sendSessionBookingsReminder,
    })
  );
};

module.exports = {
  setupRouter,
};
