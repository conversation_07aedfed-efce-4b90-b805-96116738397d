function testConvertIntervalsToUTC() {
  // Test Case 1: Interval within the same day
  const day1 = 'Monday';
  const fromTime1 = '05:00';
  const toTime1 = '13:00';
  const timezone1 = 'Asia/Kolkata';
  const result1 = convertIntervalsToUTCUsingDay(
    day1,
    fromTime1,
    toTime1,
    timezone1
  );

  // Test Case 2: Interval crossing midnight
  const day2 = 'Monday';
  const fromTime2 = '23:00';
  const toTime2 = '03:00';
  const timezone2 = 'Asia/Kolkata';
  const result2 = convertIntervalsToUTCUsingDay(
    day2,
    fromTime2,
    toTime2,
    timezone2
  );

  // Test Case 3: Interval within the same day, different timezone
  const day3 = 'Monday';
  const fromTime3 = '05:00';
  const toTime3 = '13:00';
  const timezone3 = 'America/New_York';
  const result3 = convertIntervalsToUTCUsingDay(
    day3,
    fromTime3,
    toTime3,
    timezone3
  );

  // Test Case 4: Interval crossing midnight, different timezone
  const day4 = 'Monday';
  const fromTime4 = '23:00';
  const toTime4 = '03:00';
  const timezone4 = 'America/New_York';
  const result4 = convertIntervalsToUTCUsingDay(
    day4,
    fromTime4,
    toTime4,
    timezone4
  );

  console.log('Test Case 1:');
  console.log(JSON.stringify(result1));
  console.log('Test Case 2:');
  console.log(JSON.stringify(result2));
  console.log('Test Case 3:');
  console.log(JSON.stringify(result3));
  console.log('Test Case 4:');
  console.log(JSON.stringify(result4));
}

// Compare this snippet from src/api/v1/oneOnOneSessions/validations.js:
