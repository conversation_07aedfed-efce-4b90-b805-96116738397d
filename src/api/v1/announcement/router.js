const { Router } = require('express');
const schema = require('./schema');
const controller = require('./controller');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');
const {
  managerCommunityValidator,
} = require('../../../communitiesAPI/validations/community.validation');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const {
  validateAll,
} = require('../../../middleware/validator.middleware');

const router = Router({ mergeParams: true });

router.route('/').get(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  validateAll([
    {
      schema: schema.communityIdSchema,
      location: 'params',
    },
  ]),
  controller.getAnnouncements
);

module.exports = router;
