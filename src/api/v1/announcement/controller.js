const schema = require('./schema');
const logger = require('../../../services/logger.service');
const service = require('../../../services/announcement');
const { getLanguagePreference } = require('../../../utils/headers.util');

exports.getAnnouncements = async (req, res, next) => {
  try {
    const { communityId } = await schema.communityIdSchema.cast(
      req.params
    );

    const languagePreference = getLanguagePreference(req);

    const response = await service.retrieveAnnouncements({
      languagePreference,
      communityId,
    });

    return res.json({ data: response });
  } catch (err) {
    logger.error('getAnnouncements failed due to', err.message, err.stack);
    return next(err);
  }
};
