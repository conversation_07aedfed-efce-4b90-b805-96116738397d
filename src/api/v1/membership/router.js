const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');

const {
  managerCommunityValidator,
} = require('../../../communitiesAPI/validations/community.validation');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');
const transientTokenValidator = require('../../../validations/transientToken.validation');
const apiKeyValidator = require('../../../validations/apiKey.validation');

const { handlerWrapper } = require('../../../utils/request.util');
const { rateLimitMiddleware } = require('../../../utils/rateLimit.util');

const membershipController = require('./controller');

const setupRouter = async function (router) {
  router.route('/membership/community/:communityId/count').get(
    postRoutePreHandlerMiddleware,
    rateLimitMiddleware({
      module: 'MEMBERSHIP',
    }),
    handlerWrapper({
      handler: membershipController.getCommunityMemberCount,
    })
  );

  router.route('/membership/change-stream-handler').post(
    postRoutePreHandlerMiddleware,
    handlerWrapper({
      handler: membershipController.changeStreamHandler,
    })
  );

  router.route('/membership/data-sync').post(
    postRoutePreHandlerMiddleware,
    handlerWrapper({
      handler: membershipController.dataSync,
    })
  );

  router.route('/membership/data-clear').post(
    postRoutePreHandlerMiddleware,
    handlerWrapper({
      handler: membershipController.dataClear,
    })
  );

  router.route('/membership/community/:communityId/enrollment').post(
    postRoutePreHandlerMiddleware,
    apiKeyValidator,
    handlerWrapper({
      handler: membershipController.enrollMembership,
    })
  );

  router.route('/membership/community/:communityId/segments').get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    handlerWrapper({
      handler: membershipController.getCommunitySegments,
    })
  );

  router.route('/membership/community/:communityId/members').get(
    postRoutePreHandlerMiddleware,
    rateLimitMiddleware({
      module: 'MEMBERSHIP',
    }),
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    handlerWrapper({
      handler: membershipController.getCommunityMembers,
    })
  );

  router
    .route('/membership/community/:communityId/members/count-with-filters')
    .get(
      postRoutePreHandlerMiddleware,
      rateLimitMiddleware({
        module: 'MEMBERSHIP',
      }),
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: membershipController.countCommunityMembersWithFilters,
      })
    );

  router
    .route('/membership/community/:communityId/member/:membershipId')
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: membershipController.getCommunityMember,
      })
    );

  router
    .route(
      '/membership/community/:communityId/member/:membershipId/activity'
    )
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: membershipController.getCommunityMemberActivity,
      })
    );

  router.route('/membership/community/:communityId/members/export').get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    handlerWrapper({
      handler: membershipController.exportCommunityMembers,
    })
  );

  router.route('/membership/ui-config').put(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    handlerWrapper({
      handler: membershipController.updateCommunityUIConfig,
    })
  );

  router.route('/membership/community/:communityId/members/csv').get(
    postRoutePreHandlerMiddleware,
    rateLimitMiddleware({
      module: 'MEMBERSHIP',
    }),
    transientTokenValidator,
    membershipController.exportCommunityMembers
  );

  router
    .route(
      '/membership/community/:communityId/members/bulkUpdateApplication'
    )
    .post(
      postRoutePreHandlerMiddleware,
      // tokenValidator(),
      // userValidation,
      // managerCommunityValidator,
      handlerWrapper({
        handler: membershipController.bulkUpdateApplication,
      })
    );
};

module.exports = {
  setupRouter,
};
