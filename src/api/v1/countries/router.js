const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');

const { handlerWrapper } = require('../../../utils/request.util');

const countryController = require('./controller');

const setupRouter = function (router) {
  router.route('/countries/').get(
    postRoutePreHandlerMiddleware,
    handlerWrapper({
      handler: countryController.getCountries,
    })
  );
};

module.exports = {
  setupRouter,
};
