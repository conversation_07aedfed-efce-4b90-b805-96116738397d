const countryService = require('../../../services/countryInfoMapping/countryInfoMapping.service');

const logger = require('../../../services/logger.service');

const getCountries = async (req, res, next) => {
  const countries = await countryService.getCountriesFromDB();
  results = countries.map((country) => {
    return {
      id: country.countryId,
      name: country.country,
      code: country.countryCode,
      currencyName: country.currency,
      currencyCode: country.currencyCode,
    };
  });
  return results;
};

module.exports = {
  getCountries,
};
