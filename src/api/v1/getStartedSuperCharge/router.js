const {
  managerCommunityValidator,
} = require('../../../communitiesAPI/validations/community.validation');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');
const {
  getCommunityGetStartedProgress,
  getCommunityProgressPercentage,
  updateCommunityGetStartedProgress,
} = require('./controller');

/*
GET /api/v1/community/:communityId/task-progress
this route is used to get the task progress of the community, everytime we hit this route, we will get the latest task progress of the community
*/
const setupRouter = function (router) {
  router
    .route('/communities/:communityId/task-progress')
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      getCommunityGetStartedProgress
    )
    .put(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      updateCommunityGetStartedProgress
    );

  router
    .route('/communities/:communityId/progress-percentage')
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      getCommunityProgressPercentage
    );
};

module.exports = {
  setupRouter,
};
