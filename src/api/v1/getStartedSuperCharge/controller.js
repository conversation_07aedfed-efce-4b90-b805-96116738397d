const httpStatus = require('http-status');
const yup = require('yup');
const Tasks = require('../../../models/getStartedSuperCharge/tasks.model');
const Localization = require('../../../models/platform/localization.model');
const { getCommunityService } = require('../../../services/community');
const logger = require('../../../services/logger.service');
const superChargeService = require('../../../services/getStartedSuperCharge.service');
const {
  CustomWelcomeMessageConfigService,
} = require('../../../services/config');
const { getLanguagePreference } = require('../../../utils/headers.util');

function processResources(entityId, resources = [], localeKeyMap) {
  return resources.map((resource, resourceIndex) => ({
    ...resource,
    title:
      localeKeyMap[`${entityId}-resources-${resourceIndex}-title`] ||
      resource.title,
  }));
}
function processTooltip(baseKey, tooltip, localeKeyMap) {
  if (!tooltip) return undefined;

  return {
    ...tooltip,
    title: localeKeyMap[`${baseKey}-tooltip-title`] || tooltip.title,
    desc: localeKeyMap[`${baseKey}-tooltip-desc`] || tooltip.desc,
  };
}

function processUnlocks(entityId, unlocks = [], localeKeyMap) {
  return unlocks.map((unlock, unlockIndex) => ({
    ...unlock,
    title:
      localeKeyMap[`${entityId}-unlocks-${unlockIndex}-title`] ||
      unlock.title,
    tooltip: processTooltip(
      `${entityId}-unlocks-${unlockIndex}`,
      unlock.tooltip,
      localeKeyMap
    ),
  }));
}

const getCommunityGetStartedProgress = async (req, res) => {
  try {
    const { communityId } = req.params;
    if (!communityId) {
      const error = new Error('communityId not specified in params');
      error.status = httpStatus.BAD_REQUEST; // 400
      throw error;
    }
    const languagePreference = getLanguagePreference(req);
    // get community Information from communityId
    const community = await getCommunityService.getCommunityById({
      communityId,
    });
    const communityCategory = community?.communityCategory;
    // we will make use of 'ALL_COMMUNITIES_TASK' for a common task for non categorized communities
    const types = [];
    let filter = [];
    // OTHERS = ALL_COMMUNITIES_TASK or can change this later and add a new type for this in the DB
    if (communityCategory?.id && communityCategory?.id !== 'OTHERS') {
      types.push(communityCategory.id);
    } else {
      types.push('ALL_COMMUNITIES_TASK');
    }
    filter = [
      {
        $match: {
          $or: [
            {
              type: {
                $in: types,
              },
            },
            {
              type: {
                $exists: false,
              },
            },
          ],
        },
      },
      {
        $sort:
          /**
           * Provide any number of field/order pairs.
           */
          {
            index: 1,
          },
      },
    ];
    // finding all the tasks for all the communities
    let allTasks = await Tasks.aggregate(filter);
    logger.info('All tasks for the community', allTasks);

    // Collect all localization keys
    const localizationKeys = new Set();

    // loop through everything and store the localization keys in a set
    allTasks.forEach((task) => {
      localizationKeys.add(`${task.id}-title`);
      task.resources?.forEach((_, resourceIndex) => {
        localizationKeys.add(
          `${task.id}-resources-${resourceIndex}-title`
        );
      });
      task.unlocks?.forEach((unlock, unlockIndex) => {
        localizationKeys.add(`${task.id}-unlocks-${unlockIndex}-title`);
        if (unlock.tooltip) {
          localizationKeys.add(
            `${task.id}-unlocks-${unlockIndex}-tooltip-title`
          );
          localizationKeys.add(
            `${task.id}-unlocks-${unlockIndex}-tooltip-desc`
          );
        }
      });
      task.subTasks?.forEach((subTask) => {
        if (subTask.id) {
          localizationKeys.add(`${subTask.id}-title`);
          localizationKeys.add(`${subTask.id}-description`);
          subTask.resources?.forEach((_, resourceIndex) => {
            localizationKeys.add(
              `${subTask.id}-resources-${resourceIndex}-title`
            );
          });
          subTask.unlocks?.forEach((unlock, unlockIndex) => {
            localizationKeys.add(
              `${subTask.id}-unlocks-${unlockIndex}-title`
            );
            if (unlock.tooltip) {
              localizationKeys.add(
                `${subTask.id}-unlocks-${unlockIndex}-tooltip-title`
              );
              localizationKeys.add(
                `${subTask.id}-unlocks-${unlockIndex}-tooltip-desc`
              );
            }
          });
        }
      });
    });

    // Fetch all localizations in a single query (FYI they are not more than 20-30 keys )
    const locales = await Localization.find({
      key: { $in: Array.from(localizationKeys) },
    }).lean();

    // Create a map for quick access
    const localeKeyMap = {};
    locales.forEach((locale) => {
      const value = locale[languagePreference] || locale['en'];
      localeKeyMap[locale.key] = value;
    });
    // Map the localization to the tasks
    allTasks = allTasks.map((task) => {
      const label = localeKeyMap[`${task.id}-title`] || task.label;

      const resources = processResources(
        task.id,
        task.resources,
        localeKeyMap
      );
      const unlocks = processUnlocks(task.id, task.unlocks, localeKeyMap);

      const subTasks = task.subTasks?.map((subTask) => {
        if (!subTask.id) return subTask;

        const title = localeKeyMap[`${subTask.id}-title`] || subTask.title;
        const description =
          localeKeyMap[`${subTask.id}-description`] || subTask.description;

        const subTaskResources = processResources(
          subTask.id,
          subTask.resources,
          localeKeyMap
        );
        const subTaskUnlocks = processUnlocks(
          subTask.id,
          subTask.unlocks,
          localeKeyMap
        );

        return {
          ...subTask,
          title,
          description,
          resources: subTaskResources,
          unlocks: subTaskUnlocks,
        };
      });

      return {
        ...task,
        label,
        resources,
        unlocks,
        subTasks,
      };
    });

    const isCommunityCodeBlacklistedForCustomMessage =
      await CustomWelcomeMessageConfigService.isCommunityCodeBlacklisted(
        community.code
      );
    if (isCommunityCodeBlacklistedForCustomMessage) {
      allTasks.forEach((task, index) => {
        if (task.taskId === 'LAUNCH_TASK') {
          const newSubTasks = task.subTasks.filter(
            (subTask) => subTask.taskUniqueId !== 'EDIT_WELCOME_MESSAGE'
          );
          allTasks[index].subTasks = newSubTasks;
        }
      });
    }

    // get the task information for the community and update that community information
    const taskInformationForCommunity =
      await superChargeService.getSuperChargeTasksInformation(
        allTasks,
        community,
        true
      );

    // destructuring the taskInformationForCommunity object to get the tasksProgress and percentageOfTasksCompleted
    const { tasksProgress, percentageOfTasksCompleted } =
      taskInformationForCommunity;

    logger.info(
      `task progress for the community ${communityId}`,
      tasksProgress
    );

    logger.info(
      `percentage of tasks completed for the community ${communityId}`,
      percentageOfTasksCompleted
    );

    logger.info(
      'Task Information for Community',
      taskInformationForCommunity
    );

    return res.status(httpStatus.OK).json({
      message: 'success',
      data: {
        tasksProgress,
        percentageOfTasksCompleted,
        tasks: allTasks,
      },
    });
    // custom logic for each and every task
  } catch (error) {
    logger.error(
      'getCommunityGetStartedProgress failed due to',
      error,
      error.stack
    );
    return res
      .status(error.status || httpStatus.INTERNAL_SERVER_ERROR)
      .json({
        message:
          error.message ||
          'Error in getting Progress for Get Started Page',
        errorMessage:
          error.errorMessage ||
          'Error in getting Progress for Get Started Page',
        errorCode: error.errorCode || 500,
        error,
        data: null,
      });
  }
};

const getCommunityProgressPercentage = async (req, res) => {
  try {
    const { communityId } = req.params;
    const { force: shouldGoThroughAllTasks } = req.query;

    logger.info(
      `getCommunityProgressPercentage for communityId: ${communityId} started`
    );

    const community = await getCommunityService.getCommunityById({
      communityId,
    });

    let percentage = community?.taskMetaData?.tasksCompletionPercentage;
    if (shouldGoThroughAllTasks || !percentage) {
      const allTasks = await Tasks.find({}).lean();
      logger.info('All tasks for the community', JSON.stringify(allTasks));
      const taskInformationForCommunity =
        await superChargeService.getSuperChargeTasksInformation(
          allTasks,
          community,
          false
        );
      percentage = taskInformationForCommunity?.percentageOfTasksCompleted;
    }

    return res.status(httpStatus.OK).json({
      message: 'success',
      data: {
        percentage,
      },
    });
  } catch (error) {
    logger.error(
      `getCommunityProgressPercentage failed due to ${error} and error stack `,
      error.stack
    );

    return res
      .status(error.status || httpStatus.INTERNAL_SERVER_ERROR)
      .json({
        message:
          error.message ||
          'Error in getting Progress for Get Started Page',
        errorMessage:
          error.errorMessage ||
          'Error in getting Progress for Get Started Page',
        errorCode: error.errorCode || 500,
        error,
        data: null,
      });
  }
};

/*
{
  "taskId": String,
    "subTaskId": String,
    "isCompleted": boolean
}
*/
const taskUpdateSchema = yup.object().shape({
  taskId: yup.string().required(),
  subTaskId: yup.string(),
  isCompleted: yup.boolean().required(),
});
const updateCommunityGetStartedProgress = async (req, res) => {
  try {
    const { communityId } = req.params;

    const { taskId, subTaskId, isCompleted } = req.body;
    logger.info(
      `updateCommunityGetStartedProgress for communityId: ${communityId} started`
    );

    if (!communityId) {
      logger.info('communityId not specified in params', communityId);
      const error = new Error('communityId not specified in params');
      error.status = httpStatus.BAD_REQUEST;
      throw error;
    }

    const isValid = await taskUpdateSchema.isValid({
      taskId,
      subTaskId,
      isCompleted,
    });

    if (!isValid) {
      logger.info('Invalid Request Body', req.body);
      const error = new Error('Invalid Request Body');
      error.status = httpStatus.BAD_REQUEST;
      throw error;
    }

    const updateCommunityData =
      await superChargeService.updateCommunityProgress({
        communityId,
        taskId,
        subTaskId,
        isCompleted,
      });

    return res.status(httpStatus.OK).json({
      message: 'success',
      data: updateCommunityData,
    });
  } catch (error) {
    res.status(error.status || httpStatus.INTERNAL_SERVER_ERROR).json({
      message:
        error.message || 'Error in updating Progress for Get Started Page',
      errorMessage:
        error.errorMessage ||
        'Error in updating Progress for Get Started Page',
      errorCode: error.errorCode || 500,
      error,
      data: null,
    });
  }
};
module.exports = {
  getCommunityGetStartedProgress,
  getCommunityProgressPercentage,
  updateCommunityGetStartedProgress,
};
