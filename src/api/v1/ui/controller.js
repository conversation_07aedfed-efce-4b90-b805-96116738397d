const schemas = require('./schema');

const uiService = require('../../../services/ui');

exports.dismissUIConfig = async (req) => {
  const { communityId } = await schemas.communityIdPathSchema.cast(
    req.params
  );
  const { ui, hide } = await schemas.dismissUIConfigSchema.cast(req.body);
  const results = await uiService.dismissUIConfig({
    communityId,
    ui,
    hide,
  });
  return results;
};
