const controller = require('./controller');
const schema = require('./schema');

const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');

const {
  managerCommunityValidator,
} = require('../../../communitiesAPI/validations/community.validation');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');
const { handlerWrapper } = require('../../../utils/request.util');

const setupRouter = function (router) {
  router.route('/communities/:communityId/ui/dismiss').put(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    handlerWrapper({
      handler: controller.dismissUIConfig,
      requestValidators: {
        params: schema.communityIdPathSchema,
        body: schema.dismissUIConfigSchema,
      },
    })
  );
};

module.exports = {
  setupRouter,
};
