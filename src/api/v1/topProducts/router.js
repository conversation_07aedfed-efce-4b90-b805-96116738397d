const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');

const controller = require('./controller');
const schema = require('./schema');
const { handlerWrapper } = require('../../../utils/request.util');

exports.setupRouter = function (router) {
  router.route('/top-products').get(
    postRoutePreHandlerMiddleware,
    handlerWrapper({
      handler: controller.getTopProducts,
      requestValidators: {
        query: schema.getTopProductsQuery,
      },
    })
  );

  router.route('/top-products/prompt').get(
    postRoutePreHandlerMiddleware,
    handlerWrapper({
      handler: controller.getPrompt,
      requestValidators: {
        query: schema.getPromptQuery,
      },
    })
  );
};
