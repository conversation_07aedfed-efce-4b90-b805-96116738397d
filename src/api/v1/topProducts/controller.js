const {
  getTopProductsService,
  getPromptTemplate,
} = require('../../../services/topProducts/getTopProducts.service');

const schema = require('./schema');

const getTopProducts = async (req) => {
  const { type, isMx, aggregationDays } = schema.getTopProductsQuery.cast(
    req.query
  );

  const topProducts = await getTopProductsService(
    type,
    isMx,
    aggregationDays
  );

  return topProducts;
};

const getPrompt = async (req) => {
  const { id: currentPromptId } = req.query;

  return getPromptTemplate(currentPromptId);
};

module.exports = {
  getTopProducts,
  getPrompt,
};
