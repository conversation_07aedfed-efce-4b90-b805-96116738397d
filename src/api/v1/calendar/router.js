const schema = require('./schema');
const controller = require('./controller');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const { validator } = require('../../../middleware/validator.middleware');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');
const { handlerWrapper } = require('../../../utils/request.util');

const CALENDAR_ROUTE = '/calendar';

const setupRouter = async function (router) {
  router.route(`${CALENDAR_ROUTE}/webhook/google`).post(
    postRoutePreHandlerMiddleware,
    handlerWrapper({
      handler: controller.webhookGoogle,
    })
  );

  router.route(`${CALENDAR_ROUTE}/connect`).post(
    postRoutePreHandlerMiddleware,
    validator(schema.connectSchema, 'body'),
    tokenValidator(),
    userValidation,
    handlerWrapper({
      handler: controller.connnect,
    })
  );

  router.route(`${CALENDAR_ROUTE}/disconnect`).put(
    postRoutePreHandlerMiddleware,
    validator(schema.disconnectSchema, 'body'),
    tokenValidator(),
    userValidation,
    handlerWrapper({
      handler: controller.disconnnect,
    })
  );

  router.route(`${CALENDAR_ROUTE}`).get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    handlerWrapper({
      handler: controller.getCalendars,
    })
  );

  router.route(`${CALENDAR_ROUTE}/write-events`).put(
    postRoutePreHandlerMiddleware,
    validator(schema.setCalendarForWriteEventsSchema, 'body'),
    tokenValidator(),
    userValidation,
    handlerWrapper({
      handler: controller.setCalendarForWriteEvents,
    })
  );

  router.route(`${CALENDAR_ROUTE}/deconflict`).put(
    postRoutePreHandlerMiddleware,
    validator(schema.setCalendarsForDeconflictingSchema, 'body'),
    tokenValidator(),
    userValidation,
    handlerWrapper({
      handler: controller.setCalendarsForDeconflicting,
    })
  );
};

module.exports = {
  setupRouter,
};
