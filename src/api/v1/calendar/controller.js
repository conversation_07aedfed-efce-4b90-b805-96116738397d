const schema = require('./schema');
const manageService = require('../../../services/calendar/manage.service');
const connectionService = require('../../../services/calendar/connection.service');
const webhookService = require('../../../services/calendar/webhook.service');

exports.webhookGoogle = async (req, res, next) => {
  const headers = req.headers;
  const params = {
    channelId: headers['x-goog-channel-id'],
    channelToken: headers['x-goog-channel-token'],
    channelExpirationDateTime: headers['x-goog-channel-expiration'], // In human-readable format. Present only if the channel expires.
    resourceId: headers['x-goog-resource-id'],
    resourceUri: headers['x-goog-resource-uri'],
    resourceState: headers['x-goog-resource-state'],
    messageNumber: headers['x-goog-message-number'],
  };
  if (params.resourceState === 'exists') {
    await webhookService.handleGooglePushNotifications(params);
  }
};

exports.connnect = async (req, res, next) => {
  const { origin } = req.headers;
  const params = schema.connectSchema.cast(req.body);
  const results = await connectionService.connect(
    req.userObject,
    origin,
    params
  );

  return results;
};

exports.disconnnect = async (req, res, next) => {
  const { calendarAccountObjectId } = schema.disconnectSchema.cast(
    req.body
  );
  const results = await connectionService.disconnect(
    req.userObject,
    calendarAccountObjectId
  );

  return results;
};

exports.getCalendars = async (req, res, next) => {
  const results = await manageService.getCalendars(req.userObject);

  return results;
};

exports.setCalendarForWriteEvents = async (req, res, next) => {
  const { calendarObjectId } = schema.setCalendarForWriteEventsSchema.cast(
    req.body
  );
  const results = await manageService.setCalendarForWriteEvents(
    req.userObject,
    calendarObjectId
  );

  return results;
};

exports.setCalendarsForDeconflicting = async (req, res, next) => {
  const { calendarAccountObjectId, calendarObjectIds } =
    schema.setCalendarsForDeconflictingSchema.cast(req.body);
  const results = await manageService.setCalendarsForDeconflicting(
    req.userObject,
    calendarAccountObjectId,
    calendarObjectIds
  );

  return results;
};
