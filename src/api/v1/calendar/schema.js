const yup = require('yup');
const {
  CALENDAR_PLATFORMS,
} = require('../../../services/calendar/constants');

exports.connectSchema = yup.object().shape({
  platform: yup
    .string()
    .oneOf([...Object.values(CALENDAR_PLATFORMS)])
    .required(),
  code: yup.string().required(),
});

exports.disconnectSchema = yup.object().shape({
  calendarAccountObjectId: yup.string().required(),
});

exports.setCalendarForWriteEventsSchema = yup.object().shape({
  calendarObjectId: yup.string().nullable().notRequired(),
});

exports.setCalendarsForDeconflictingSchema = yup.object().shape({
  calendarAccountObjectId: yup.string().required(),
  calendarObjectIds: yup.array().of(yup.string()).notRequired(),
});
