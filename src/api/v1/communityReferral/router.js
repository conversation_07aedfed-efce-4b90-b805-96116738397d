const schema = require('./schema');
const controller = require('./controller');
const copsTokenMiddleware = require('../../../middleware/cops-token.middleware');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const tokenValidator = require('../../../validations/token.validation');
const tokenValidatorWithoutError = require('../../../validations/tokenNoError.validation');
const apiKeyValidation = require('../../../validations/apiKey.validation');
const userValidation = require('../../../validations/user.validation');
const {
  managerCommunityValidator,
} = require('../../../communitiesAPI/validations/community.validation');
const { handlerWrapper } = require('../../../utils/request.util');

const COMMUNITY_REFERRAL_LINK_PREFIX = '/community-referral';
const COMMUNITY_REFERRAL_REWARD_TEMPLATE_LINK_PREFIX = `${COMMUNITY_REFERRAL_LINK_PREFIX}/reward-template`;

const setupRouter = async function (router) {
  router.route(`${COMMUNITY_REFERRAL_LINK_PREFIX}/referrer-rewards`).get(
    postRoutePreHandlerMiddleware,
    apiKeyValidation,
    handlerWrapper({
      handler: controller.getReferrerRewards,
      requestValidators: {
        query: schema.getReferrerRewardsSchema,
      },
    })
  );

  router.route(`${COMMUNITY_REFERRAL_LINK_PREFIX}/onboarding`).get(
    postRoutePreHandlerMiddleware,
    tokenValidatorWithoutError,
    handlerWrapper({
      handler: controller.getCommunityReferralDetails,
      requestValidators: {
        query: schema.getCommunityReferralDetailsSchema,
      },
    })
  );

  router
    .route(`/communities/:communityId${COMMUNITY_REFERRAL_LINK_PREFIX}`)
    .post(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.retrieveOrCreateCommunityReferralCode,
        requestValidators: {
          params: schema.communityIdSchema,
        },
      })
    );

  router
    .route(
      `/communities/:communityId${COMMUNITY_REFERRAL_LINK_PREFIX}/summary`
    )
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.getReferrerRewardSummary,
        requestValidators: {
          params: schema.communityIdSchema,
        },
      })
    );

  router
    .route(
      `/communities/:communityId${COMMUNITY_REFERRAL_LINK_PREFIX}/referees`
    )
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.getRefereeList,
        requestValidators: {
          params: schema.communityIdSchema,
          query: schema.getRefereeListSchema,
        },
      })
    );

  router
    .route(`${COMMUNITY_REFERRAL_REWARD_TEMPLATE_LINK_PREFIX}/cops`)
    .post(
      postRoutePreHandlerMiddleware,
      copsTokenMiddleware,
      handlerWrapper({
        handler: controller.createCommunityReferralRewardTemplate,
        requestValidators: {
          body: schema.createCommunityReferralRewardTemplateSchema,
        },
      })
    );

  router
    .route(
      `${COMMUNITY_REFERRAL_REWARD_TEMPLATE_LINK_PREFIX}/:templateId/cops`
    )
    .put(
      postRoutePreHandlerMiddleware,
      copsTokenMiddleware,
      handlerWrapper({
        handler: controller.updateCommunityReferralRewardTemplate,
        requestValidators: {
          body: schema.updateCommunityReferralRewardTemplateSchema,
          params: schema.templateIdSchema,
        },
      })
    );
};

module.exports = {
  setupRouter,
};
