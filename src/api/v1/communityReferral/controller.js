const schema = require('./schema');
const referralGetService = require('../../../services/communityReferral/get.service');
const referralListService = require('../../../services/communityReferral/list.service');
const rewardTemplateManageService = require('../../../services/communityReferralRewardTemplate/manage.service');

// const {
//   getCountryFromIP,
// } = require('../../../services/countryFromIP/countryFromIP.service');
// const { getUserIP } = require('../../../utils/headers.util');

exports.getReferrerRewards = async (req) => {
  const { communityReferralCode, planType, refereeCommunityObjectId } =
    await schema.getReferrerRewardsSchema.cast(req.query);

  const result = await referralGetService.retrieveReferrerRewards({
    communityReferralCode,
    planType,
    refereeCommunityObjectId,
  });

  return result;
};

exports.getCommunityReferralDetails = async (req) => {
  const { communityReferralCode, planType } =
    await schema.getCommunityReferralDetailsSchema.cast(req.query);
  // const ip = getUserIP(req);
  // const country = await getCountryFromIP({ ip });
  const result = await referralGetService.getReferralCodeDetails(
    communityReferralCode?.toLocaleUpperCase(),
    planType
    // country
  );
  return result;
};

exports.retrieveOrCreateCommunityReferralCode = async (req) => {
  const { communityId } = await schema.communityIdSchema.cast(req.params);
  const result =
    await referralGetService.retrieveOrCreateCommunityReferralCode(
      communityId
    );
  return result;
};

exports.getReferrerRewardSummary = async (req) => {
  const { communityId } = await schema.communityIdSchema.cast(req.params);
  const result = await referralGetService.getReferrerRewardSummary(
    communityId
  );
  return result;
};

exports.getRefereeList = async (req) => {
  const { communityId } = await schema.communityIdSchema.cast(req.params);
  const { status, pageSize, pageNo, sortBy, sortOrder } =
    await schema.getRefereeListSchema.cast(req.query);
  const result = await referralListService.getRefereeList({
    communityId,
    status,
    pageSize,
    pageNo,
    sortBy,
    sortOrder,
  });
  return result;
};

exports.createCommunityReferralRewardTemplate = async (req) => {
  const payload =
    await schema.createCommunityReferralRewardTemplateSchema.cast(
      req.body
    );
  payload.createdBy = req.user?.email?.toLocaleLowerCase().trim();
  payload.editedBy = req.user?.email?.toLocaleLowerCase().trim();
  const result =
    await rewardTemplateManageService.createCommunityReferralRewardTemplate(
      payload
    );
  return result;
};

exports.updateCommunityReferralRewardTemplate = async (req) => {
  const { templateId } = await schema.templateIdSchema.cast(req.params);
  const payload =
    await schema.updateCommunityReferralRewardTemplateSchema.cast(
      req.body
    );
  payload.editedBy = req.user?.email?.toLocaleLowerCase().trim();
  const result =
    await rewardTemplateManageService.updateCommunityReferralRewardTemplate(
      templateId,
      payload
    );
  return result;
};
