const controller = require('./controller');

const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');

const {
  managerCommunityValidator,
} = require('../../../communitiesAPI/validations/community.validation');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');
const apiKeyValidator = require('../../../validations/apiKey.validation');
const { handlerWrapper } = require('../../../utils/request.util');
const schema = require('./schema');

const setupRouter = function (router) {
  router.route('/communities/:communityId/cmp-home').get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    handlerWrapper({
      handler: controller.getCmpHomeDetails,
      requestValidators: {
        params: schema.communityIdPathSchema,
      },
    })
  );

  router
    .route('/cmp-home/:communityId/analytics')
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({ handler: controller.getAnalyticsData })
    );

  router.route('/entity/update-impressions').patch(
    postRoutePreHandlerMiddleware,
    apiKeyValidator,
    handlerWrapper({
      handler: controller.updateImpressionsController,
      requestValidators: {
        body: schema.updateImpressionsSchema,
      },
    })
  );

  router.route('/entity/:entityId').get(
    postRoutePreHandlerMiddleware,
    apiKeyValidator,
    handlerWrapper({
      handler: controller.getEntityById,
      requestValidators: {
        query: schema.getEntityByIdSchema,
        params: schema.getEntityByIdParamsSchema,
      },
    })
  );
};

module.exports = {
  setupRouter,
};
