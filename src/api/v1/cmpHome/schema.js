const yup = require('yup');
// const { PLATFORMS } = require('../../../services/magicReach/constants');

const communityIdPathSchema = yup.object().shape({
  communityId: yup.string().required(),
});

const GetAnalyticsDataSchema = yup.object().shape({
  communityId: yup.string().required(),
  duration: yup.string().lowercase().required().trim(),
});

const updateImpressionsSchema = yup.object().shape({
  entityType: yup.string().required(),
  entityId: yup.string().required(),
  totalImpressions: yup.number().required(),
  lastProcessedDate: yup.string().notRequired(),
});

const getEntityByIdSchema = yup.object().shape({
  entityType: yup.string().required(),
});

const getEntityByIdParamsSchema = yup.object().shape({
  entityId: yup.string().required(),
});

module.exports = {
  communityIdPathSchema,
  getEntityByIdParamsSchema,
  GetAnalyticsDataSchema,
  updateImpressionsSchema,
  getEntityByIdSchema,
};
