const schemas = require('./schema');

const getCommunityService =
  require('../../../services/community').getCommunityService;
const analyticsService = require('../../../services/analytics/analytics.service');
const whatsappService = require('../../../communitiesAPI/services/common/whatsapp.service');
const cmpHomeService = require('../../../services/cmpHome');

const getCmpHomeDetails = async (req) => {
  const { communityId } = await schemas.communityIdPathSchema.cast(
    req.params
  );
  const learnerObjectId = req.user.learner._id;

  const results = await cmpHomeService.getCmpHomeDetails(
    communityId,
    learnerObjectId
  );
  return results;
};

const getAnalyticsData = async (req) => {
  const { communityId } = req.params;
  const { duration } = req.query;

  const community = await getCommunityService.getCommunityById({
    communityId,
    projection: { isWhatsappExperienceCommunity: 1 },
  });

  const communityAnalytics = await analyticsService.getCommunityAnalytics({
    communityId,
    duration,
  });

  const result = {
    memberGrowth: communityAnalytics.memberGrowth ?? {},
    linkVisits: communityAnalytics.linkVisits ?? {},
  };

  if (community.isWhatsappExperienceCommunity) {
    const whatsappAnalyticsData =
      await whatsappService.getWhatsappAnalytics({
        communityId,
        duration,
      });
    // chatgroup-specific data
    result.chatMemberGrowth = whatsappAnalyticsData.memberGrowth;
    result.chatGroupEngagedMembers = whatsappAnalyticsData.engagedMembers;
    result.chatGroupLastReadMessage =
      whatsappAnalyticsData.lastReadMessage;
  }
  return result;
};

const updateImpressionsController = async (req) => {
  const { entityType, entityId, totalImpressions, lastProcessedDate } =
    schemas.updateImpressionsSchema.cast(req.body);

  const updateImpressions = await analyticsService.updateImpressions({
    entityId,
    entityType,
    totalImpressions,
    lastProcessedDate,
  });

  return updateImpressions;
};

const getEntityById = async (req) => {
  const { entityType } = schemas.getEntityByIdSchema.cast(req.query);
  const { entityId } = req.params;
  const entity = await analyticsService.getEntityById({
    entityType,
    entityId,
  });

  return entity;
};

module.exports = {
  getCmpHomeDetails,
  getAnalyticsData,
  updateImpressionsController,
  getEntityById,
};
