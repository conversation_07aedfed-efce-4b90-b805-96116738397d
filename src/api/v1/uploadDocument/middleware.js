const aws = require('aws-sdk');
const crypto = require('crypto');
const multer = require('multer');
const multerS3 = require('multer-s3');
const path = require('path');
const mime = require('mime');

const { v4: uuidv4 } = require('uuid');
const logger = require('../../../services/logger.service');

const {
  awsRegion,
  awsSecretKey,
  awsAccessKey,
  awsAccessLevel,
} = require('../../../config');
const {
  IMAGEASSETBUCKET,
  IMAGEASSETBUCKET_CLOUDFRONT_BASE_URL,
} = require('../../../constants/common');

const thresholdImageSize = 10000000;

const getBucketPath = (useCase, associatedEntityId, additionalPaths) => {
  const components = [];

  if (useCase) {
    components.push(useCase);
  }
  if (associatedEntityId) {
    components.push(associatedEntityId);
  }
  if (additionalPaths) {
    components.push(additionalPaths.join('/'));
  }
  return components.join('/');
};

const config = awsSecretKey
  ? {
      secretAccessKey: awsSecretKey,
      accessKeyId: awsAccessKey,
    }
  : { credentialProvider: new aws.CredentialProviderChain() };

aws.config.update({
  region: awsRegion,
  ...config,
});

const s3 = new aws.S3();

const fileFilter = (req, file, cb) => {
  const allowedTypes = [
    'application/pdf', // PDF
    'application/msword', // DOC
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // DOCX
    'application/vnd.ms-excel', // XLS
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // XLSX
    'application/vnd.ms-powerpoint', // PPT
    'application/vnd.openxmlformats-officedocument.presentationml.presentation', // PPTX
  ];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    const err = new Error(
      'Invalid file type. It has to be a PDF, DOC, DOCX, XLS, XLSX, PPT or PPTX document.'
    );
    err.status = 400;
    cb(err, false);
  }
};

function encodeUnicodeToURL(string) {
  let encodedString = encodeURIComponent(string);
  // Exclude space, hyphen and underscore from the encoding
  encodedString = encodedString
    .replace(/%20/g, ' ')
    .replace(/%2D/g, '-')
    .replace(/%5F/g, '_');
  return encodedString;
}

const checkFileExists = async (bucket, key) => {
  try {
    await s3.headObject({ Bucket: bucket, Key: key }).promise();
    return true;
  } catch (err) {
    return false;
  }
};

function addUuidToFilename(filepath) {
  const dirname = path.dirname(filepath);
  const extname = path.extname(filepath);
  const basename = path.basename(filepath, extname);

  const uuid = uuidv4().replace(/-/g, ''); // Remove hyphens
  const shortUuid = uuid.substring(0, 6); // First 6 characters

  const newFilename = `${basename}_${shortUuid}${extname}`;

  const newFilePath = path.join(dirname, newFilename);

  return newFilePath;
}

const uploadMultiPartDocument = () => {
  return multer({
    limits: { fileSize: 1024 * 1024 * 10 }, // 10 MB
    fileFilter: fileFilter,
    onError: function (err, next) {
      // eslint-disable-next-line no-param-reassign
      err.status = 500;
      next(err);
    },
    storage: multerS3({
      s3: s3,
      bucket: IMAGEASSETBUCKET,
      dest: (req, file, cb) => {
        const { useCase, associatedEntityId, additionalPaths } = req.body;
        const filePath = getBucketPath(
          useCase,
          associatedEntityId,
          additionalPaths
        );
        cb(null, filePath);
      },
      acl: awsAccessLevel,
      key: (req, file, cb) => {
        const { useCase, associatedEntityId, additionalPaths } = req.body;

        let extension = path.extname(file.originalname);
        if (!extension) {
          // If no extension, try to get it from the mimetype
          extension = mime.extension(file.mimetype)
            ? `.${mime.extension(file.mimetype)}`
            : '';
        }

        const key = `${getBucketPath(
          useCase,
          associatedEntityId,
          additionalPaths
        )}/${Date.now().toString()}-${uuidv4()}_submission_file${extension}`;
        cb(null, key);
      },
    }),
  });
};

module.exports = {
  uploadMultiPartDocument: uploadMultiPartDocument(),
};
