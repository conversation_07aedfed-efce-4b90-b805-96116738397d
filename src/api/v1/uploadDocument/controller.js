const httpStatus = require('http-status');
const logger = require('../../../services/logger.service');

const {
  IMAGEASSETBUCKET_CLOUDFRONT_BASE_URL,
} = require('../../../constants/common');

exports.returnFilePath = async (req, res, next) => {
  const docKey = req?.file?.key;
  const docUrl = `${IMAGEASSETBUCKET_CLOUDFRONT_BASE_URL}/${docKey}`;
  logger.info('Responded with document link: ', docUrl);
  return res.status(httpStatus.OK).json({ docUrl: docUrl });
};
