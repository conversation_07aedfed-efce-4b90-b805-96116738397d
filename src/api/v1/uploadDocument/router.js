const controller = require('./controller');
const middleware = require('./middleware');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');

exports.setupRouter = function (router) {
  router
    .route('/document/multipart')
    .post(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      middleware.uploadMultiPartDocument.single('document'),
      controller.returnFilePath
    );
};
