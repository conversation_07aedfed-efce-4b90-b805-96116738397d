const yup = require('yup');
const {
  PAYOUT_ACCOUNT_CATEGORY,
  PAYOUT_ID_TYPE,
} = require('../../../../constants/common');

exports.postAffiliateSchema = yup.object().shape({
  communityObjectId: yup.string().trim().required(),
});

exports.getAffiliateProductsSchema = yup.object().shape({
  pageSize: yup.number().default(20).required().integer(),
  pageNo: yup.number().default(1).required().integer(),
});

exports.affiliateProductIdSchema = yup.object().shape({
  affiliateProductId: yup.string().trim().required(),
});

exports.getAffiliatePayoutsSchema = yup.object().shape({
  pageSize: yup.number().default(20).required().integer(),
  pageNo: yup.number().default(1).required().integer(),
});

exports.createAffiliatePayoutAccountSchema = yup.object().shape({
  fullName: yup.string().trim().required(),
  email: yup.string().trim().required(),
  phoneNumber: yup.string().trim().notRequired(),
  country: yup.string().trim().required(),
  bankCountryCode: yup.string().trim().required(),
  bankName: yup.string().trim().required(),
  bankAddress: yup.string().trim().required(),
  bankPostalCode: yup.string().trim().required(),
  bankCity: yup.string().trim().required(),
  swiftCode: yup.string().trim().required(),
  accountName: yup.string().trim().required(),
  accountNumber: yup.string().trim().required(),
  iban: yup.string().trim().notRequired(),
  bsb: yup.string().trim().notRequired(),
  ifsc: yup.string().trim().notRequired(),
  intermediaryBank: yup.string().trim().notRequired(),
  intermediaryBankSwiftCode: yup.string().trim().notRequired(),
  notes: yup.string().trim().notRequired(),
  dialCode: yup.string().trim().notRequired(),
  phoneNumberWithoutDialCode: yup.string().trim().notRequired(),
  accountType: yup.string().trim().notRequired(),
  recipientAddress: yup.string().trim().required(),
  recipientCity: yup.string().trim().required(),
  recipientPostalCode: yup.string().trim().required(),
  payoutCurrency: yup.string().trim().required(),
  indiaPanHolderName: yup.string().trim(),
  indiaPanNumber: yup.string().trim(),
  transitCode: yup.string().trim(),
  institutionNumber: yup.string().trim(),
  cpfNumber: yup.string().trim(),
  cnpjNumber: yup.string().trim(),
  clabe: yup.string().trim(),
  accountCategory: yup
    .string()
    .trim()
    .oneOf(Object.values(PAYOUT_ACCOUNT_CATEGORY)),
  businessRegistrationNumber: yup.string().trim(),
  businessPhoneNumber: yup.string().trim(),
  businessDialCode: yup.string().trim().notRequired(),
  businessPhoneNumberWithoutDialCode: yup.string().trim().notRequired(),
  idType: yup.string().trim().oneOf(Object.values(PAYOUT_ID_TYPE)),
  idValue: yup.string().trim(),
});
