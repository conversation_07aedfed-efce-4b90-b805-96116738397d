const { Router } = require('express');
const schema = require('./schema');
const controller = require('./controller');
const tokenValidator = require('../../../../validations/token.validation');
const userValidation = require('../../../../validations/user.validation');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../../middleware/request.middleware');

const { handlerWrapper } = require('../../../../utils/request.util');
const { ACCESS_TOKEN_TYPE } = require('../../../../constants/common');

const router = Router({ mergeParams: true });

router.route('/').post(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  handlerWrapper({
    handler: controller.addAffiliate,
    requestValidators: {
      body: schema.postAffiliateSchema,
    },
  })
);

router.route('/products').get(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  handlerWrapper({
    handler: controller.getAffiliateProductsForMember,
    requestValidators: {
      query: schema.getAffiliateProductsSchema,
    },
  })
);

router.route('/products/:affiliateProductId').delete(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  handlerWrapper({
    handler: controller.leaveAffiliate,
    requestValidators: {
      params: schema.affiliateProductIdSchema,
    },
  })
);

router.route('/payouts').get(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  handlerWrapper({
    handler: controller.getAffiliatePayouts,
    requestValidators: {
      query: schema.getAffiliatePayoutsSchema,
    },
  })
);

router
  .route('/payout-account')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    handlerWrapper({
      handler: controller.getAffiliatePayoutAccount,
    })
  )
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator([ACCESS_TOKEN_TYPE.EMAIL], [ACCESS_TOKEN_TYPE.EMAIL]),
    userValidation,
    handlerWrapper({
      handler: controller.createAffiliatePayoutAccount,
      requestValidators: {
        body: schema.createAffiliatePayoutAccountSchema,
      },
    })
  )
  .patch(
    postRoutePreHandlerMiddleware,
    tokenValidator([ACCESS_TOKEN_TYPE.EMAIL], [ACCESS_TOKEN_TYPE.EMAIL]),
    userValidation,
    handlerWrapper({
      handler: controller.updateAffiliatePayoutAccount,
      requestValidators: {
        body: schema.createAffiliatePayoutAccountSchema,
      },
    })
  )
  .delete(
    postRoutePreHandlerMiddleware,
    tokenValidator([ACCESS_TOKEN_TYPE.EMAIL], [ACCESS_TOKEN_TYPE.EMAIL]),
    userValidation,
    handlerWrapper({
      handler: controller.deleteAffiliatePayoutAccount,
    })
  );

module.exports = router;
