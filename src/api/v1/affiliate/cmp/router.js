const { Router } = require('express');
const schema = require('./schema');
const controller = require('./controller');
const validator = require('./validator');
const tokenValidator = require('../../../../validations/token.validation');
const userValidation = require('../../../../validations/user.validation');
const transientTokenValidator = require('../../../../validations/transientToken.validation');
const {
  validateAll,
} = require('../../../../middleware/validator.middleware');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../../middleware/request.middleware');
const {
  managerCommunityValidator,
} = require('../../../../communitiesAPI/validations/community.validation');

const { handlerWrapper } = require('../../../../utils/request.util');

const router = Router({ mergeParams: true });

router
  .route('/')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    validator.validateNonAffiliateAccess,
    handlerWrapper({
      handler: controller.getAffiliates,
      requestValidators: {
        query: schema.getAffiliatesSchema,
        params: schema.communityIdSchema,
      },
    })
  )
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    validator.validateNonAffiliateAccess,
    handlerWrapper({
      handler: controller.addAffiliate,
      requestValidators: {
        body: schema.postAffiliateSchema,
        params: schema.communityIdSchema,
      },
    })
  );

router
  .route('/products')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    validator.validateNonAffiliateAccess,
    handlerWrapper({
      handler: controller.getAffiliateProducts,
      requestValidators: {
        query: schema.getAffiliateProductsSchema,
        params: schema.communityIdSchema,
      },
    })
  )
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    validator.validateNonAffiliateAccess,
    handlerWrapper({
      handler: controller.addAffiliateProduct,
      requestValidators: {
        body: schema.postAffiliateProductSchema,
        params: schema.communityIdSchema,
      },
    })
  );

router.route('/products/summary').get(
  postRoutePreHandlerMiddleware,
  handlerWrapper({
    handler: controller.getAffiliateProductsSummary,
    requestValidators: {
      params: schema.communityIdSchema,
    },
  })
);

router.route('/products/:affiliateProductId').patch(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  validator.validateNonAffiliateAccess,
  handlerWrapper({
    handler: controller.updateAffiliateProduct,
    requestValidators: {
      body: schema.updateAffiliateProductSchema,
      params: schema.affiliateProductIdSchema,
    },
  })
);

router.route('/csv').get(
  postRoutePreHandlerMiddleware,
  transientTokenValidator,
  validator.validateNonAffiliateAccess,
  validateAll([
    { schema: schema.getAffiliatesSchema, location: 'query' },
    {
      schema: schema.communityIdSchema,
      location: 'params',
    },
  ]),
  controller.exportAffiliates
);

router.route('/get-affiliate-info').get(
  postRoutePreHandlerMiddleware,
  handlerWrapper({
    handler: controller.getAffiliateInfo,
    requestValidators: {
      query: schema.getAffiliateInfoSchema,
      params: schema.communityIdSchema,
    },
  })
);

router
  .route('/:affiliateId')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    validator.validateNonAffiliateAccess,
    handlerWrapper({
      handler: controller.getAffiliate,
      requestValidators: {
        query: schema.getAffiliateSchema,
        params: schema.affiliateIdSchema,
      },
    })
  )
  .patch(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    validator.validateNonAffiliateAccess,
    handlerWrapper({
      handler: controller.updateAffiliate,
      requestValidators: {
        body: schema.updateAffiliateSchema,
        params: schema.affiliateIdSchema,
      },
    })
  )
  .delete(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    validator.validateNonAffiliateAccess,
    handlerWrapper({
      handler: controller.deleteAffiliate,
      requestValidators: {
        params: schema.affiliateIdSchema,
      },
    })
  );

module.exports = router;
