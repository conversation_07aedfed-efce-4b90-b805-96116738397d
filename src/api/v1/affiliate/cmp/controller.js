const schema = require('./schema');
const {
  affiliateProductService,
  affiliateService,
  commonService,
} = require('../../../../services/affiliate');

exports.getAffiliateProductsSummary = async (req, res) => {
  const { communityId: communityObjectId } = schema.communityIdSchema.cast(
    req.params
  );

  const result = await commonService.retrieveAffiliateProductsSummary({
    communityObjectId,
  });

  return result;
};

exports.getAffiliates = async (req, res) => {
  const { communityId: communityObjectId } = schema.communityIdSchema.cast(
    req.params
  );
  const { pageNo, pageSize, search, status } =
    schema.getAffiliatesSchema.cast(req.query);

  const result = await affiliateService.retrieveAffiliates({
    pageNo,
    pageSize,
    search,
    status,
    communityObjectId,
  });

  return result;
};

exports.addAffiliate = async (req, res) => {
  const { communityId: communityObjectId } = schema.communityIdSchema.cast(
    req.params
  );

  const affiliatesToBeAdded = schema.postAffiliateSchema.cast(req.body);

  const result = await affiliateService.addAffiliates({
    communityObjectId,
    affiliatesToBeAdded,
  });

  return result;
};

exports.exportAffiliates = async (req, res) => {
  const { communityId } = await schema.communityIdSchema.cast(req.params);

  const { search, status } = await schema.getAffiliatesSchema.cast(
    req.query
  );

  const [currentDate, currentTime] = new Date().toISOString().split('T');

  const community = await commonService.retrieveActiveCommunity(
    communityId
  );

  const fileName = encodeURIComponent(
    req.query?.fileName ??
      `${community.code}_affiliates_${currentDate.replace(
        /-/g,
        ''
      )}_${currentTime.split('.')[0].replace(/:/g, '')}.csv`
  );

  res.setHeader('Content-Disposition', `attachment; filename=${fileName}`);
  res.setHeader('Content-Type', 'text/csv');

  const pageSize = 100;

  const transactionsCsvStream =
    await affiliateService.generateAffiliatesCsvStream({
      community,
      search,
      status,
      pageSize,
    });

  transactionsCsvStream.pipe(res);
  transactionsCsvStream.on('end', () => {
    res.end();
  });
};

exports.getAffiliate = async (req, res) => {
  const {
    communityId: communityObjectId,
    affiliateId: affiliateObjectId,
  } = schema.affiliateIdSchema.cast(req.params);
  const { pageNo, pageSize } = schema.getAffiliateSchema.cast(req.query);

  const affiliate = await affiliateService.retrieveAffiliate({
    communityObjectId,
    affiliateObjectId,
  });

  const affiliateProducts =
    await affiliateProductService.retrieveAffiliateProducts({
      pageNo,
      pageSize,
      communityObjectId,
      affiliate,
    });

  const result = {
    affiliate,
    ...affiliateProducts,
  };

  return result;
};

exports.updateAffiliate = async (req, res) => {
  const {
    communityId: communityObjectId,
    affiliateId: affiliateObjectId,
  } = schema.affiliateIdSchema.cast(req.params);

  const affiliateCommissionsToBeUpdated =
    schema.updateAffiliateSchema.cast(req.body);

  const result = await affiliateService.updateAffiliate({
    communityObjectId,
    affiliateObjectId,
    affiliateCommissionsToBeUpdated,
  });

  return result;
};

exports.deleteAffiliate = async (req, res) => {
  const {
    communityId: communityObjectId,
    affiliateId: affiliateObjectId,
  } = schema.affiliateIdSchema.cast(req.params);

  const result = await affiliateService.updateAffiliateStatusToRemoved({
    communityObjectId,
    affiliateObjectId,
  });

  return result;
};

exports.getAffiliateProducts = async (req, res) => {
  const { communityId: communityObjectId } = schema.communityIdSchema.cast(
    req.params
  );
  const { pageNo, pageSize } = schema.getAffiliateProductsSchema.cast(
    req.query
  );

  const affiliateProducts =
    await affiliateProductService.retrieveAffiliateAndNonProducts({
      pageNo,
      pageSize,
      communityObjectId,
    });

  return affiliateProducts;
};

exports.addAffiliateProduct = async (req, res) => {
  const { communityId: communityObjectId } = schema.communityIdSchema.cast(
    req.params
  );

  const {
    entityObjectId,
    entityType,
    commissionPercentage,
    messageForAffiliates,
  } = schema.postAffiliateProductSchema.cast(req.body);

  const result = await affiliateProductService.addAffiliateProduct({
    entityObjectId,
    entityType,
    commissionPercentage,
    messageForAffiliates,
    communityObjectId,
  });

  return result;
};

exports.updateAffiliateProduct = async (req, res) => {
  const {
    affiliateProductId: affiliateProductObjectId,
    communityId: communityObjectId,
  } = schema.affiliateProductIdSchema.cast(req.params);

  const { isActive, commissionPercentage, messageForAffiliates } =
    schema.updateAffiliateProductSchema.cast(req.body);

  const result = await affiliateProductService.updateAffiliateProduct({
    affiliateProductObjectId,
    isActive,
    commissionPercentage,
    messageForAffiliates,
    communityObjectId,
  });

  return result;
};

// Use for public page to display affiliate name
exports.getAffiliateInfo = async (req, res) => {
  const { communityId: communityObjectId } = schema.communityIdSchema.cast(
    req.params
  );
  const { entityObjectId, entityType, affiliateCode } =
    schema.getAffiliateInfoSchema.cast(req.query);

  const affiliateInfo = await affiliateService.retrieveAffiliateInfo({
    communityObjectId,
    affiliateCode,
    entityObjectId,
    entityType,
  });

  const result = {
    affiliateInfo,
  };

  return result;
};
