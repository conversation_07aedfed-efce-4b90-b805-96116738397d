const schema = require('./schema');
const { affiliateService } = require('../../../../services/affiliate');
const {
  ForbiddenError,
  ParamError,
} = require('../../../../utils/error.util');

exports.validateNonAffiliateAccess = async (req, res, next) => {
  try {
    const { communityId: communityObjectId } =
      schema.communityIdSchema.cast(req.params);

    if (!communityObjectId) {
      throw new ParamError('Missing community object id');
    }

    const { _id: learnerObjectId } = req.user.learner;

    const existsActiveAffiliate =
      await affiliateService.existsActiveAffiliate({
        communityObjectId,
        learnerObjectId,
      });

    if (existsActiveAffiliate) {
      throw new ForbiddenError('Active affiliate forbidden to access');
    }

    next();
  } catch (error) {
    next(error);
  }
};
