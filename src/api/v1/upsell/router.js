const { Router } = require('express');

const schema = require('./schema');
const controller = require('./controller');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');
const {
  managerCommunityValidator,
} = require('../../../communitiesAPI/validations/community.validation');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const { handlerWrapper } = require('../../../utils/request.util');

const router = Router({ mergeParams: true });

router.route('/').post(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  handlerWrapper({
    handler: controller.createUpsell,
    requestValidators: {
      body: schema.upsellSchema,
      params: schema.communityIdSchema,
    },
  })
);

router.route('/products').get(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  handlerWrapper({
    handler: controller.getUpsellProducts,
    requestValidators: {
      query: schema.getUpsellProductsSchema,
      params: schema.communityIdSchema,
    },
  })
);

router
  .route('/:upsellId')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    handlerWrapper({
      handler: controller.getUpsell,
      requestValidators: {
        params: schema.upsellIdSchema,
      },
    })
  )
  .patch(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    handlerWrapper({
      handler: controller.updateUpsell,
      requestValidators: {
        body: schema.updateUpsellSchema,
        params: schema.upsellIdSchema,
      },
    })
  )
  .delete(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    handlerWrapper({
      handler: controller.disableUpsell,
      requestValidators: {
        params: schema.upsellIdSchema,
      },
    })
  );

module.exports = router;
