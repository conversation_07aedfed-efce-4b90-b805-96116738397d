const yup = require('yup');

const {
  UPSELL_SOURCE_ENTITY_TYPE,
  PAYABLE_PURCHASE_TYPES,
} = require('../../../constants/common');

exports.communityIdSchema = yup.object().shape({
  communityId: yup.string().required(),
});

exports.getUpsellProductsSchema = yup.object().shape({
  pageSize: yup.number().default(100).required().integer(),
  pageNo: yup.number().default(1).required().integer(),
});

exports.upsellIdSchema = yup.object().shape({
  communityId: yup.string().required(),
  upsellId: yup.string().required(),
});

exports.discountSchema = yup.object().shape({
  value: yup.number().required(),
  effectiveTimeEnd: yup.date().nullable().notRequired(),
  timezone: yup.string().required(),
  isActive: yup.boolean().required(),
});

exports.upsellSchema = yup.object().shape({
  sourceEntityType: yup
    .string()
    .trim()
    .oneOf(Object.values(UPSELL_SOURCE_ENTITY_TYPE))
    .required(),
  sourceEntityObjectId: yup.string().trim().required(),
  upsellCommunityObjectId: yup.string().trim().required(),
  upsellEntityType: yup
    .string()
    .trim()
    .oneOf(PAYABLE_PURCHASE_TYPES)
    .required(),
  upsellEntityObjectId: yup.string().trim().required(),
  title: yup
    .string()
    .trim()
    .max(150, 'Title cannot be longer than 150 characters')
    .required(),
  description: yup
    .string()
    .trim()
    .max(1000, 'Description cannot be longer than 1000 characters')
    .notRequired(),
});

exports.updateDiscountSchema = yup.object().shape({
  value: yup.number().notRequired(),
  effectiveTimeEnd: yup.date().nullable().notRequired(),
  timezone: yup.string().notRequired(),
  isActive: yup.boolean().required(),
});

exports.updateUpsellSchema = yup.object().shape({
  sourceEntityType: yup
    .string()
    .trim()
    .oneOf(Object.values(UPSELL_SOURCE_ENTITY_TYPE))
    .notRequired(),
  sourceEntityObjectId: yup.string().trim().notRequired(),
  upsellCommunityObjectId: yup.string().trim().notRequired(),
  upsellEntityType: yup
    .string()
    .trim()
    .oneOf(PAYABLE_PURCHASE_TYPES)
    .notRequired(),
  upsellEntityObjectId: yup.string().trim().notRequired(),
  title: yup
    .string()
    .trim()
    .max(100, 'Title cannot be longer than 100 characters')
    .notRequired(),
  description: yup
    .string()
    .trim()
    .max(1000, 'Description cannot be longer than 1000 characters')
    .notRequired(),
});
