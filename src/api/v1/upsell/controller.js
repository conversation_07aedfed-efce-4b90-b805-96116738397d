const schema = require('./schema');
const service = require('../../../services/upsell');
const { getUserIP } = require('../../../utils/headers.util');

exports.createUpsell = async (req, res) => {
  const email = req.user?.email;

  const { communityId: communityObjectId } = schema.communityIdSchema.cast(
    req.params
  );

  const {
    sourceEntityType,
    sourceEntityObjectId,
    upsellCommunityObjectId,
    upsellEntityType,
    upsellEntityObjectId,
    title,
    description = '',
    discount: discountWithoutValidation,
  } = schema.upsellSchema.cast(req.body);

  let discount;

  if (discountWithoutValidation) {
    await schema.discountSchema.validate(discountWithoutValidation);
    discount = schema.discountSchema.cast(discountWithoutValidation);
  }

  const result = await service.createUpsell({
    email,
    sourceCommunityObjectId: communityObjectId,
    sourceEntityType,
    sourceEntityObjectId,
    upsellCommunityObjectId,
    upsellEntityType,
    upsellEntityObjectId,
    title,
    description,
    discount,
    databaseSession: null,
  });

  return result;
};

exports.getUpsell = async (req, res) => {
  const { communityId: communityObjectId, upsellId: upsellObjectId } =
    schema.upsellSchema.cast(req.params);

  const ip = getUserIP(req) || null;

  const result = await service.getUpsell({
    communityObjectId,
    upsellObjectId,
    ip,
  });

  return result;
};

exports.updateUpsell = async (req, res) => {
  const email = req.user?.email;

  const { communityId: communityObjectId, upsellId: upsellObjectId } =
    schema.upsellSchema.cast(req.params);

  const {
    sourceEntityType,
    sourceEntityObjectId,
    upsellCommunityObjectId,
    upsellEntityType,
    upsellEntityObjectId,
    title,
    description,
    discount: discountWithoutValidation,
  } = schema.updateUpsellSchema.cast(req.body);

  let discount;

  if (discountWithoutValidation) {
    await schema.updateDiscountSchema.validate(discountWithoutValidation);
    discount = schema.updateDiscountSchema.cast(discountWithoutValidation);
  }

  const result = await service.updateUpsell({
    email,
    communityObjectId,
    sourceEntityType,
    sourceEntityObjectId,
    upsellObjectId,
    upsellCommunityObjectId,
    upsellEntityType,
    upsellEntityObjectId,
    title,
    description,
    discount,
  });

  return result;
};

exports.disableUpsell = async (req, res) => {
  const { communityId: communityObjectId, upsellId: upsellObjectId } =
    schema.upsellSchema.cast(req.params);

  const result = await service.disableUpsell({
    communityObjectId,
    upsellObjectId,
  });

  return result;
};

exports.getUpsellProducts = async (req, res) => {
  const { communityId: communityObjectId } = schema.upsellSchema.cast(
    req.params
  );

  const { pageNo, pageSize } = schema.getUpsellProductsSchema.cast(
    req.query
  );

  const result = await service.retrieveUpsellProducts({
    communityObjectId,
    pageNo,
    pageSize,
  });

  return result;
};
