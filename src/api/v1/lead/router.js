const { Router } = require('express');
const controller = require('./controller');
const schema = require('./schema');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const {
  validateAll,
} = require('../../../middleware/validator.middleware');

const router = Router({ mergeParams: true });

router.route('/feature').post(
  postRoutePreHandlerMiddleware,
  validateAll([
    {
      schema: schema.CreateLeadSchema,
      location: 'body',
    },
  ]),
  controller.createFeatureLead
);

module.exports = router;
