const schemas = require('./schema');
const httpStatus = require('http-status');

const featureLeadService = require('../../../services/leads/featureLeads');
const {
  getCountryFromIP,
} = require('../../../services/countryFromIP/countryFromIP.service');
const {
  getUserIP,
  getUserAgent,
  getLanguagePreference,
} = require('../../../utils/headers.util');
const { DEFAULT_COUNTRY } = require('../../../constants/common');

exports.createFeatureLead = async (req, res, next) => {
  const data = await schemas.CreateLeadSchema.cast(req.body);
  if (!data.metadata) {
    data.metadata = {};
  }
  const ip = getUserIP(req);
  data.metadata.ipAddress = ip;
  data.metadata.userAgent = getUserAgent(req);
  data.metadata.languagePreference = getLanguagePreference(req);
  data.metadata.country =
    (await getCountryFromIP({ ip })) || DEFAULT_COUNTRY;
  const featureLead = await featureLeadService.createFeatureLead(data);

  return res.status(httpStatus.OK).json({
    message: 'success',
    data: featureLead,
  });
};
