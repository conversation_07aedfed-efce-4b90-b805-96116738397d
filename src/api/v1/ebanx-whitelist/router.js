const controller = require('./controller');
const schema = require('./schema');
const copsTokenMiddleware = require('../../../middleware/cops-token.middleware');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const { handlerWrapper } = require('../../../utils/request.util');

const setupRouter = function (router) {
  router.route('/ebanx-whitelist/resolve-product').post(
    postRoutePreHandlerMiddleware,
    copsTokenMiddleware,
    handlerWrapper({
      handler: controller.resolveProduct,
      requestValidators: {
        body: schema.resolveProductSchema,
      },
    })
  );
};

module.exports = {
  setupRouter,
};
