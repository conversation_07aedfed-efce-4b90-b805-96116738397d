const yup = require('yup');
const {
  COMMUNITY_POSTS_VISIBILITY_TYPE_MAP,
} = require('../../../communitiesAPI/constants');
const { PLATFORMS } = require('../../../services/magicReach/constants');

const User = yup.object().shape({
  firstName: yup.string().nullable(),
  lastName: yup.string().nullable(),
  email: yup.string().email(),
  phoneNumber: yup.string().nullable(),
  inBuckets: yup.array().of(yup.string()).required(),
  subscriptionId: yup.string().nullable(),
  learnerId: yup.mixed().nullable(),
  profileImage: yup.string().nullable(),
});

const ComposeMessageData = yup.object().shape({
  isDemo: yup.boolean(),
  sentPlatforms: yup
    .array()
    .of(yup.string().oneOf(Object.values(PLATFORMS))),
  isDraft: yup.boolean(),
  title: yup.string(),
  author: yup.string(),
  content: yup.object(),
  templateIds: yup.object(),
  templateObjectId: yup.object(),
  selectedUsers: yup.array().of(User),
  unselectedUsers: yup.array().of(User),
  selectedBuckets: yup.array().of(yup.string()),
  bucketFilters: yup.object(),
  bucketMetas: yup.object(),
});

const ScheduleMessageData = yup.object().shape({
  dueAt: yup
    .date()
    .required()
    .min(new Date(new Date().getTime() + 5 * 60 * 1000)),
  isDemo: yup.boolean(),
  sentPlatforms: yup
    .array()
    .of(yup.string().oneOf(Object.values(PLATFORMS)))
    .required(),
  isDraft: yup.boolean(),
  title: yup.string().required(),
  author: yup.string(),
  content: yup.object(),
  templateIds: yup.object(),
  templateObjectId: yup.object(),
  selectedUsers: yup.array().of(User),
  unselectedUsers: yup.array().of(User),
  selectedBuckets: yup.array().of(yup.string()),
  bucketFilters: yup.object(),
  bucketMetas: yup.object(),
  visibilityType: yup
    .string()
    .oneOf(Object.values(COMMUNITY_POSTS_VISIBILITY_TYPE_MAP))
    .when('sentPlatforms', {
      is: (sentPlatforms) => {
        if (sentPlatforms.includes(PLATFORMS.ANNOUNCEMENT_V2)) {
          return true;
        }
        return false;
      },
      then: yup
        .string()
        .required('visibilityType required for announcement platforms'),
    }),
});

const SendMessageData = yup.object().shape({
  isDemo: yup.boolean(),
  sentPlatforms: yup
    .array()
    .of(yup.string().oneOf(Object.values(PLATFORMS)))
    .required(),
  isDraft: yup.boolean(),
  title: yup.string().required(),
  author: yup.string(),
  content: yup.object(),
  templateIds: yup.object(),
  templateObjectId: yup.object(),
  selectedUsers: yup.array().of(User),
  unselectedUsers: yup.array().of(User),
  selectedBuckets: yup.array().of(yup.string()),
  bucketFilters: yup.object(),
  bucketMetas: yup.object(),
  visibilityType: yup
    .string()
    .oneOf(Object.values(COMMUNITY_POSTS_VISIBILITY_TYPE_MAP))
    .when('sentPlatforms', {
      is: (sentPlatforms) => {
        if (sentPlatforms.includes(PLATFORMS.ANNOUNCEMENT_V2)) {
          return true;
        }
        return false;
      },
      then: yup
        .string()
        .required('visibilityType required for announcement platforms'),
    }),
});

const TestMessageData = yup.object().shape({
  platform: yup.string().oneOf(Object.values(PLATFORMS)).required(),
  title: yup.string().required(),
  author: yup.string(),
  content: yup.object(),
  templateIds: yup.object(),
  templateObjectId: yup.object(),
  recipients: yup.array().required(),
});

const ScheduleMessageBodySchema = ScheduleMessageData;
const SendMessageBodySchema = SendMessageData;
const CreateMessageBodySchema = ComposeMessageData;
const UpdateMessageBodySchema = ComposeMessageData;
const SendTestMessageBodySchema = TestMessageData;
const CountRecipientsBodySchema = yup.object().shape({
  selectedUsers: yup.array().of(User),
  unselectedUsers: yup.array().of(User),
  selectedBuckets: yup.array().of(yup.string()),
});

const GetLinksBodySchema = yup.object().shape({
  platform: yup.string().oneOf(Object.values(PLATFORMS)).required(),
  pageSize: yup.number().default(20).required().integer(),
  pageNo: yup.number().default(1).required().integer(),
  hasPaid: yup.boolean().notRequired(),
});

module.exports = {
  GetLinksBodySchema,
  ScheduleMessageBodySchema,
  SendMessageBodySchema,
  SendTestMessageBodySchema,
  UpdateMessageBodySchema,
  CreateMessageBodySchema,
  CountRecipientsBodySchema,
};
