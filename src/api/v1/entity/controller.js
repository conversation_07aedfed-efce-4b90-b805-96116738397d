const httpStatus = require('http-status');
const schema = require('./schema');
const logger = require('../../../services/logger.service');
const { getUserIP, getUserAgent } = require('../../../utils/headers.util');
const MainWebsiteBackendRpc = require('../../../rpc/mainWebsiteBackend');
const {
  getAbandonedCheckoutUsers,
} = require('../../../services/abandonedCarts/abandonedCarts.service');
const AddonTransactionService = require('../../../services/addonTransaction/addonTransactionUpdate.service');

exports.stripeIndiaCheckout = async (req, res, next) => {
  try {
    const { entitySignupId, entityType, couponCode } =
      await schema.stripeIndiaCheckoutSchema.cast(req.body);
    const { communityId } = await schema.communityIdSchema.cast(
      req.params
    );
    logger.info(communityId, entitySignupId, entityType, couponCode);

    const ip = getUserIP(req) || null;
    const userAgent = getUserAgent(req) || null;
    if (!entitySignupId || !entityType) {
      logger.error(
        'Error community entity stripe checkout due to Invalid Path Parameters'
      );
      const error = new Error('Invalid Path Parameters');
      error.status = httpStatus.BAD_REQUEST;
      return next(error);
    }
    const mwbRpc = new MainWebsiteBackendRpc();
    await mwbRpc.init();

    let statusCode;
    let result;
    try {
      result = await mwbRpc.stripeIndiaPaymentIntent(
        ip,
        userAgent,
        entitySignupId,
        entityType,
        couponCode
      );
      statusCode = httpStatus.CREATED;
    } catch (error) {
      const errorData = {};
      errorData.message =
        error?.response?.data?.info ||
        'Error creating community entity stripe checkout';
      errorData.status =
        error?.response?.data?.status || httpStatus.BAD_REQUEST;
      return next(errorData);
    }
    logger.info('Event Stripe Checkout Result: ', result);

    return res.status(statusCode).json({
      data: result,
    });
  } catch (err) {
    return next(err);
  }
};

exports.getAbandonedCheckout = async (req, res, next) => {
  try {
    const { communityId, entityObjectId } =
      schema.getAbandonedCheckoutPathSchema.cast(req.params);

    const { entityType, search, pageSize, pageNo, sortBy, sortOrder } =
      schema.getAbandonedCheckoutQuerySchema.cast(req.query);

    const result = await getAbandonedCheckoutUsers({
      communityId,
      entityObjectId,
      entityType,
      search,
      pageSize,
      pageNo,
      sortBy,
      sortOrder,
    });

    res.json({ data: result });
  } catch (err) {
    logger.error(
      'getCommunityEventAndAttendees failed due to',
      err,
      err.stack
    );
    return next(err);
  }
};

exports.addonTransactionUpdate = async (req, res, next) => {
  try {
    const {
      status,
      addonTransactionId,
      failureCode,
      failureReason,
      eventTime,
      paymentProvider,
    } = schema.addonTransactionUpdateSchema.cast(req.body);

    const result = await AddonTransactionService.updateAddonTransactions({
      status,
      addonTransactionId,
      failureCode,
      failureReason,
      eventTime,
      paymentProvider,
    });

    res.json({ data: result });
  } catch (err) {
    logger.error('addonTransactionUpdate failed due to', err, err.stack);
    return next(err);
  }
};
