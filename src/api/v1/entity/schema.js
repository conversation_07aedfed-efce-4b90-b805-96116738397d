const yup = require('yup');
const { sortParams } = require('../../../constants/common');

exports.stripeIndiaCheckoutSchema = yup.object().shape({
  entitySignupId: yup.string().required(),
  entityType: yup.string().required(),
  couponCode: yup.string().trim().uppercase().notRequired(),
});

exports.communityIdSchema = yup.object().shape({
  communityId: yup.string().required(),
});

exports.getAbandonedCheckoutPathSchema = yup.object().shape({
  communityId: yup.string().required(),
  entityObjectId: yup.string().required(),
});

exports.getAbandonedCheckoutQuerySchema = yup.object().shape({
  entityType: yup.string().uppercase().required().trim(),
  search: yup.string().uppercase().notRequired().trim(),
  pageSize: yup.number().default(20).required().integer(),
  pageNo: yup.number().default(1).required().integer(),
  sortBy: yup.string().default(sortParams.CREATED_AT).notRequired().trim(),
  sortOrder: yup.number().default(-1).notRequired(),
});

exports.addonTransactionUpdateSchema = yup.object().shape({
  status: yup.string().required(),
  addonTransactionId: yup.string().required(),
  paymentProvider: yup.string().required(),
  failureCode: yup.string(),
  failureReason: yup.string(),
  eventTime: yup.date(),
});
