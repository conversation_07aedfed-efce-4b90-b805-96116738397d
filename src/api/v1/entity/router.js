const { Router } = require('express');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');
const {
  memberCommunityValidator,
  managerCommunityValidator,
} = require('../../../communitiesAPI/validations/community.validation');
const controller = require('./controller');
const {
  validateAll,
} = require('../../../middleware/validator.middleware');
const schema = require('./schema');
const apiKeyValidation = require('../../../validations/apiKey.validation');

const router = Router({ mergeParams: true });

router.route('/stripe-india-checkout').post(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  memberCommunityValidator,
  validateAll([
    { schema: schema.stripeIndiaCheckoutSchema, location: 'body' },
    {
      schema: schema.communityIdSchema,
      location: 'params',
    },
  ]),
  controller.stripeIndiaCheckout
);

router.route('/:entityObjectId/abandoned-checkout').get(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  validateAll([
    {
      schema: schema.getAbandonedCheckoutPathSchema,
      location: 'params',
    },
    {
      schema: schema.getAbandonedCheckoutQuerySchema,
      location: 'query',
    },
  ]),
  controller.getAbandonedCheckout
);

router.route('/update-addon-transaction').post(
  postRoutePreHandlerMiddleware,
  apiKeyValidation,
  validateAll([
    {
      schema: schema.addonTransactionUpdateSchema,
      location: 'body',
    },
  ]),
  controller.addonTransactionUpdate
);

module.exports = router;
