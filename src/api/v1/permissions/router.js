const controller = require('./controller');
const schema = require('./schema');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');

const copsTokenMiddleware = require('../../../middleware/cops-token.middleware');
const tokenValidatorWithoutError = require('../../../validations/tokenNoError.validation');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');
const {
  managerCommunityValidator,
} = require('../../../communitiesAPI/validations/community.validation');

const { handlerWrapper } = require('../../../utils/request.util');

const setupRouter = function (router) {
  router.route(`/communities/:communityId/permissions`).get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    handlerWrapper({
      handler: controller.getCommunityPermissions,
      requestValidators: {
        params: schema.communityIdSchema,
        query: schema.getPermissionQuerySchema,
      },
    })
  );

  router
    .route(`/communities/:communityId/permissions/grandfather/cops`)
    .put(
      postRoutePreHandlerMiddleware,
      copsTokenMiddleware,
      handlerWrapper({
        handler: controller.updateGrandfatherFeaturePermissions,
        requestValidators: {
          params: schema.communityIdSchema,
          body: schema.updateGrandfatherFeaturePermissionsBodySchema,
        },
      })
    );

  router.route(`/permissions`).get(
    postRoutePreHandlerMiddleware,
    tokenValidatorWithoutError,
    handlerWrapper({
      handler: controller.getFeaturePlanPermissions,
    })
  );
};

module.exports = {
  setupRouter,
};
