const schema = require('./schema');

const featurePermissionsService = require('../../../services/featurePermissions');

exports.getCommunityPermissions = async (req) => {
  const { communityId } = schema.communityIdSchema.cast(req.params);
  const { includeUsage } = schema.getPermissionQuerySchema.cast(req.query);

  const results = await featurePermissionsService.getCommunityPermissions(
    communityId,
    includeUsage
  );

  return results;
};

exports.getFeaturePlanPermissions = async () => {
  const results =
    await featurePermissionsService.getFeaturePlanPermissions();

  return results;
};

exports.updateGrandfatherFeaturePermissions = async (req) => {
  const { communityId } = schema.communityIdSchema.cast(req.params);
  const payload =
    schema.updateGrandfatherFeaturePermissionsBodySchema.cast(req.body);

  const results =
    await featurePermissionsService.updateGrandfatherFeaturePermissions(
      communityId,
      payload
    );

  return results;
};
