const yup = require('yup');
const { FEATURE_LIST_NAME } = require('@constants/common');

exports.communityIdSchema = yup.object().shape({
  communityId: yup.string().trim().required(),
});

exports.getPermissionQuerySchema = yup.object().shape({
  includeUsage: yup.boolean().default(false).notRequired(),
});

exports.updateGrandfatherFeaturePermissionsBodySchema = yup
  .object()
  .shape({
    features: yup
      .array(
        yup
          .object()
          .shape({
            featureName: yup
              .string()
              .trim()
              .oneOf(Object.values(FEATURE_LIST_NAME))
              .required(),
            allowed: yup.boolean().default(false).required(),
            limit: yup.number().default(0).nullable().notRequired(),
            interval: yup.string().trim().nullable().notRequired(),
            intervalCount: yup
              .number()
              .nullable()
              .notRequired()
              .transform((value, originalValue) =>
                originalValue === 0 ? null : value
              ),
          })
          .required()
      )
      .default([])
      .notRequired(),
  });
