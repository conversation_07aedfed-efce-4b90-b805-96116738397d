const schema = require('./schema');
const magicAudienceService = require('../../../services/magicAudience/magicAudienceCampaign.service');
const {
  META_ADS_STATUS,
} = require('../../../services/magicAudience/constants');
const logger = require('../../../services/logger.service');
const { META_AD_WEBHOOK_VERIFY_TOKEN } = require('../../../config');
// const logger = require('../../../services/logger.service');

// GET API's
const getMagicAudienceCampaigns = async (req) => {
  const { communityId } = req.params;
  const { page, limit } = schema.getMagicAudienceCampaignsSchema.cast(
    req.query
  );

  const metaAdsCampaigns =
    await magicAudienceService.getMagicAudienceCampaigns({
      communityId,
      page,
      limit,
    });

  return metaAdsCampaigns;
};
const getMagicAudienceCampaignEstimate = async (req) => {
  const { communityId, campaignId } = req.params;
  const { targeting } = req.query;

  const estimatedDelivery =
    await magicAudienceService.getMagicAudienceCampaignEstimate({
      communityId,
      campaignId,
      targeting,
    });
  return estimatedDelivery;
};

const adsWebhook = async (req, res) => {
  const verifyToken = req.query['hub.verify_token'];
  if (verifyToken === META_AD_WEBHOOK_VERIFY_TOKEN) {
    if (req.query['hub.mode'] === 'subscribe') {
      res.send(req.query['hub.challenge']);
    }
  }

  logger.info('Meta Ads Webhook', req.body);
  return true;
};

const getMetaPages = async (req) => {
  const { code, redirectUri } = schema.getUserPagesSchema.cast(req.query);
  if (!code) {
    throw new Error('Code is required to get Meta Pages');
  }
  const pages = await magicAudienceService.getMetaPages({
    code,
    redirectUri,
  });

  return pages;
};

const connectUsersPageToBusiness = async (req) => {
  const { pageId, accessToken } =
    schema.connectUsersPageToBusinessSchema.cast(req.body);
  const { communityId } = req.params || {};

  await magicAudienceService.connectUsersPageToBusiness({
    pageId,
    accessToken,
    communityId,
  });
  return {
    message: 'Page connected to business successfully',
  };
};

const disconnectUsersPageFromBusiness = async (req) => {
  const { communityId } = req.params || {};

  await magicAudienceService.disconnectUsersPageFromBusiness({
    communityId,
  });

  return {
    message: 'Page disconnected from business successfully',
  };
};

const settleFundsForCompletedMagicAudienceCampaign = async (req) => {
  const { communityId, campaignId } = req.params;
  const { totalSpend } =
    schema.settleFundsForMagicAudienceCampaignSchema.cast(req.body);

  await magicAudienceService.settleFundsForCompletedMagicAudienceCampaign({
    communityId,
    campaignId,
    totalSpend,
  });

  return {
    message: 'Funds settled successfully for the campaign',
  };
};
const adsWebhookHandler = async (req, res) => {
  try {
    const { entry = [] } = req.body ?? {};
    if (!entry.length) {
      return res.status(400).json({ message: 'Invalid request' });
    }
    const FIELD_HANDLERS = {
      with_issues_ad_objects: async (v) =>
        magicAudienceService.reportedIssuesOnAdEntities({
          entityId: v.id,
          errorCode: v.error_code,
          errorMessage: v.error_message,
          errorSummary: v.error_summary,
          level: v.level,
        }),
      in_process_ad_objects: async (v) =>
        magicAudienceService.handleAdsWebhookInProcess({
          entityId: v.id,
          level: v.level,
          statusName: v.status_name,
        }),
    };

    const tasks = [];
    for (const { changes = [] } of entry) {
      for (const { field, value } of changes) {
        const handler = FIELD_HANDLERS[field];

        if (!handler) {
          logger.warn(`adsWebhookHandler ➜ unsupported field "${field}"`);
          // eslint-disable-next-line no-continue
          continue; // skip unknown fields
        }

        tasks.push(handler(value)); // store the Promise
      }
    }

    await Promise.all(tasks);

    return res.status(200).json({
      message: 'Webhook received successfully',
    });
  } catch (error) {
    logger.error('Error in adsWebhookHandler:', error);
    return res.status(500).json({
      message: 'Internal server error',
    });
  }
};
// CREATE (POST/PUT) API's
const createMagicAudienceCampaign = async (req) => {
  const { communityId } = req.params;

  const { _id: learnerObjectId } = req.user.learner;
  const createMetaAdsCampaignBody =
    schema.createMagicAudienceCampaignSchema.cast(req.body);

  const { campaignName, entityType, entityObjectId } =
    createMetaAdsCampaignBody;

  const createMetaAdsCampaign = {
    communityObjectId: communityId,
    entityObjectId,
    entityType,
    campaignName,
    status: META_ADS_STATUS.PAUSED,
    createdByLearnerObjectId: learnerObjectId,
  };

  const metaAdsCampaign =
    await magicAudienceService.createMagicAudienceCampaign(
      createMetaAdsCampaign
    );

  return metaAdsCampaign;
};

const createMagicAudienceAdObject = async (req) => {
  const { communityId, campaignId } = req.params;
  const { _id: learnerObjectId } = req.user.learner;
  const magicAudienceAdObjectBody =
    schema.createMagicAudienceAdObjectSchema.cast(req.body);

  const createMetaAdsObject =
    await magicAudienceService.createMagicAudienceAdObject({
      communityObjectId: communityId,
      campaignObjectId: campaignId,
      createdByLearnerObjectId: learnerObjectId,
      ...magicAudienceAdObjectBody,
    });

  return createMetaAdsObject;
};

const getMagicAudienceProducts = async (req) => {
  const { communityId } = req.params;
  const { search, pageSize } = schema.getMagicAudienceProductsSchema.cast(
    req.query
  );

  const magicAudienceProducts =
    await magicAudienceService.getMagicAudienceProducts({
      communityId,
      search,
      pageSize,
    });

  return magicAudienceProducts;
};

const getMagicAudienceRecommendedProducts = async (req) => {
  const { communityId } = req.params;

  const topRecommendedProducts =
    await magicAudienceService.getMagicAudienceRecommendedProducts({
      communityId,
    });
  return topRecommendedProducts;
};
const updateMagicAudienceCampaign = async (req) => {
  const { communityId, campaignId } = req.params;
  const updateMagicAudienceCampaignBody =
    schema.updateMagicAudienceCampaignSchema.cast(req.body);

  const { status } = updateMagicAudienceCampaignBody;

  if (status === META_ADS_STATUS.PAUSED) {
    return magicAudienceService.pauseAllAds({
      communityId,
      campaignId,
    });
  }

  if (status === META_ADS_STATUS.ACTIVE) {
    return magicAudienceService.activateAllAds({
      communityId,
      campaignId,
    });
  }

  if (status === META_ADS_STATUS.COMPLETED) {
    return magicAudienceService.completeAllAds({
      communityId,
      campaignId,
    });
  }
  return magicAudienceService.updateMagicAudienceCampaign({
    ...updateMagicAudienceCampaignBody,
    communityId,
    campaignId,
  });
};

const getMagicAudienceCampaignById = async (req) => {
  const { communityId, campaignId } = req.params;

  const { createAd } = req.query;

  const { dateStart, dateEnd } =
    schema.getMagicAudienceCampaignByIdSchema.cast(req.query);

  const metaAdsCampaign =
    await magicAudienceService.getMagicAudienceCampaignById({
      communityId,
      campaignId,
      dateEnd,
      dateStart,
      createAd,
    });

  return metaAdsCampaign;
};

const getMagicAudienceAllAdEntities = async (req) => {
  const { communityId, campaignId } = req.params;

  const magicAudienceAdObjects =
    await magicAudienceService.getMagicAudienceAllAdEntities({
      communityId,
      campaignId,
    });

  return magicAudienceAdObjects;
};
const updateMagicAudienceAdObject = async (req) => {
  const { adId, campaignId, communityId } = req.params;

  const updateAdBody = await schema.updateMagicAudienceAdObjectSchema.cast(
    req.body
  );

  if (updateAdBody.status === META_ADS_STATUS.PAUSED) {
    return magicAudienceService.pauseMagicAudienceAdObject({
      adObjectId: adId,
      communityId,
      campaignId,
    });
  }
  const updatedAdObject =
    await magicAudienceService.updateMagicAudienceAdObject({
      adObjectId: adId,
      updateAdBody,
      campaignId,
      communityId,
    });

  return updatedAdObject;
};

const approveOrRejectMagicAudienceCampaign = async (req) => {
  const { communityId, campaignId } = req.params;
  const approveOrRejectUpdateBody =
    schema.approveOrRejectMagicAudienceCampaignSchema.cast(req.body);

  const updatedMetaAdsCampaign =
    await magicAudienceService.approveOrRejectMagicAudienceCampaign({
      communityId,
      campaignId,
      ...approveOrRejectUpdateBody,
    });

  return updatedMetaAdsCampaign;
};

const createMagicAudiencePixel = async (req) => {
  const { adsAccountId } = req.body;
  const pixelId = await magicAudienceService.createMagicAudiencePixel(
    adsAccountId
  );
  return {
    message: 'Meta Ads Pixel created successfully',
    pixelId,
  };
};
const terminateMagicAudienceCampaign = async (req) => {
  const { communityId, campaignId } = req.params;

  const terminatedMetaAdsCampaign =
    await magicAudienceService.terminateMagicAudienceCampaign({
      communityId,
      campaignId,
    });

  return terminatedMetaAdsCampaign;
};

const approveOrRejectMagicAudienceAdObject = async (req) => {
  const { adId, campaignId, communityId } = req.params;
  const approveOrRejectUpdateBody =
    schema.approveOrRejectMagicAudienceAdObjectSchema.cast(req.body);

  const updatedMetaAdsCampaign =
    await magicAudienceService.approveOrRejectMagicAudienceAdObject({
      communityId,
      campaignId,
      adObjectId: adId,
      ...approveOrRejectUpdateBody,
    });

  return updatedMetaAdsCampaign;
};
const uploadVideoToMetaForVideoId = async (req) => {
  const { communityId } = req.params;

  const { videoLink } = schema.uploadVideoToMetaForVideoIdSchema.cast(
    req.body
  );
  const uploadVideoResponse = await magicAudienceService.uploadVideoToMeta(
    {
      communityId,
      videoLink,
    }
  );

  return uploadVideoResponse;
};

const getMagicAudienceVideoUploadStatus = async (req) => {
  const { videoId } = schema.getMagicAudienceVideoUploadStatusSchema.cast(
    req.query
  );

  const videoUploadStatus =
    await magicAudienceService.getMagicAudienceVideoUploadStatus({
      videoId,
    });

  return videoUploadStatus;
};

const deleteMagicAudienceCampaign = async (req) => {
  const { communityId, campaignId } = req.params;

  const deletedMetaAdsCampaign =
    await magicAudienceService.deleteMagicAudienceCampaign({
      communityId,
      campaignId,
    });

  return deletedMetaAdsCampaign;
};

// THIS IS AN INTERNAL API, NOT USED IN THE APP
const assignAdAccountToCommunity = async (req) => {
  const { communityId } = req.params;
  const { pageId, pageTitle, pageDescription } =
    schema.assignAdAccountToCommunitySchema.cast(req.body);

  await magicAudienceService.assignAdAccountToCommunity({
    communityId,
    pageId,
    pageTitle,
    pageDescription,
  });

  return {
    message: 'Ad account assigned to community successfully',
  };
};

const applyForMagicAudience = async (req) => {
  const { communityId } = req.params;

  await magicAudienceService.applyForMagicAudience({
    communityId,
    learner: req.user.learner,
  });

  return {
    message: 'Application for Magic Audience submitted successfully',
  };
};

// const connectMetaAdsAccount = async (req) => {
//   const { communityId } = req.params;

//   const adsAccountId = req.body.adsAccountId;

//   const facebookUserId = '<EMAIL>';
//   const pageTitle = 'testing page';
//   const pageDescription = 'testing page description';
//   const pageThumbnail =
//     'https://d2oi1rqwb0pj00.cloudfront.net/nasIO/onboarding/jpg/default-community-logo-blue.jpg';
//   const communityObjectId = communityId;
//   const pageId = req.body.pageId;

//   await MetaAdsIntegration.create({
//     adsAccountId,
//     pageId,
//     facebookUserId,
//     pageTitle,
//     pageDescription,
//     pageThumbnail,
//     communityObjectId,
//   });

//   return {
//     message: 'Meta Ads account connected successfully',
//   };
// };
module.exports = {
  getMagicAudienceRecommendedProducts,
  getMagicAudienceVideoUploadStatus,
  getMagicAudienceCampaigns,
  getMagicAudienceCampaignById,
  getMagicAudienceCampaignEstimate,
  getMagicAudienceAllAdEntities,
  disconnectUsersPageFromBusiness,
  getMagicAudienceProducts,
  createMagicAudienceCampaign,
  createMagicAudienceAdObject,
  updateMagicAudienceAdObject,
  uploadVideoToMetaForVideoId,
  updateMagicAudienceCampaign,
  approveOrRejectMagicAudienceCampaign,
  approveOrRejectMagicAudienceAdObject,
  deleteMagicAudienceCampaign,
  adsWebhook,
  adsWebhookHandler,
  assignAdAccountToCommunity,
  getMetaPages,
  connectUsersPageToBusiness,
  terminateMagicAudienceCampaign,
  applyForMagicAudience,
  settleFundsForCompletedMagicAudienceCampaign,
  createMagicAudiencePixel,
  // connectMetaAdsAccount,
};
