const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const userValidation = require('../../../validations/user.validation');
const tokenValidator = require('../../../validations/token.validation');
const {
  managerCommunityValidator,
} = require('../../../communitiesAPI/validations/community.validation');
const copsTokenMiddleware = require('../../../middleware/cops-token.middleware');
const { handlerWrapper } = require('../../../utils/request.util');
const apiKeyValidation = require('../../../validations/apiKey.validation');
const controller = require('./controller');
const schema = require('./schema');

const MAGIC_AUDIENCE_PREFIX = '/magic-audience';

const setupRouter = async function (router) {
  router
    .route(`/communities/:communityId${MAGIC_AUDIENCE_PREFIX}/campaign`)
    .post(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.createMagicAudienceCampaign,
        requestValidators: {
          body: schema.createMagicAudienceCampaignSchema,
        },
      })
    )
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.getMagicAudienceCampaigns,
        requestValidators: {
          query: schema.getMagicAudienceCampaignsSchema,
        },
      })
    );

  router
    .route(
      `/communities/:communityId${MAGIC_AUDIENCE_PREFIX}/campaign/:campaignId`
    )
    .put(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.updateMagicAudienceCampaign,
        requestValidators: {
          body: schema.updateMagicAudienceCampaignSchema,
        },
      })
    )
    .delete(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.deleteMagicAudienceCampaign,
      })
    )
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.getMagicAudienceCampaignById,
      })
    );

  router
    .route(
      `/communities/:communityId${MAGIC_AUDIENCE_PREFIX}/campaign/:campaignId/ad-entities`
    )
    .get(
      postRoutePreHandlerMiddleware,
      // copsTokenMiddleware,
      handlerWrapper({
        handler: controller.getMagicAudienceAllAdEntities,
      })
    );

  router
    .route(
      `/communities/:communityId${MAGIC_AUDIENCE_PREFIX}/campaign/:campaignId/approval`
    )
    .put(
      postRoutePreHandlerMiddleware,
      copsTokenMiddleware,
      handlerWrapper({
        handler: controller.approveOrRejectMagicAudienceCampaign,
        requestValidators: {
          body: schema.approveOrRejectMagicAudienceCampaignSchema,
        },
      })
    );

  router.route(`/communities${MAGIC_AUDIENCE_PREFIX}/create/pixel`).post(
    postRoutePreHandlerMiddleware,
    apiKeyValidation,
    handlerWrapper({
      handler: controller.createMagicAudiencePixel,
    })
  );

  router
    .route(
      `/communities/:communityId${MAGIC_AUDIENCE_PREFIX}/campaign/:campaignId/terminate`
    )
    .post(
      postRoutePreHandlerMiddleware,
      apiKeyValidation,
      handlerWrapper({
        handler: controller.terminateMagicAudienceCampaign,
      })
    );
  router
    .route(
      `/communities/:communityId${MAGIC_AUDIENCE_PREFIX}/campaign/:campaignId/ad/:adId/approval`
    )
    .put(
      postRoutePreHandlerMiddleware,
      copsTokenMiddleware,
      handlerWrapper({
        handler: controller.approveOrRejectMagicAudienceAdObject,
        requestValidators: {
          body: schema.approveOrRejectMagicAudienceAdObjectSchema,
        },
      })
    );

  router
    .route(
      `/communities/:communityId${MAGIC_AUDIENCE_PREFIX}/campaign/:campaignId/ad`
    )
    .post(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.createMagicAudienceAdObject,
        requestValidators: {
          body: schema.createMagicAudienceAdObjectSchema,
        },
      })
    );

  router
    .route(`/communities/:communityId${MAGIC_AUDIENCE_PREFIX}/products`)
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.getMagicAudienceProducts,
        requestValidators: {
          query: schema.getMagicAudienceProductsSchema,
        },
      })
    );

  router
    .route(
      `/communities/:communityId${MAGIC_AUDIENCE_PREFIX}/recommendations/products`
    )
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.getMagicAudienceRecommendedProducts,
      })
    );

  router
    .route(
      `/communities/:communityId${MAGIC_AUDIENCE_PREFIX}/campaign/:campaignId/ad/:adId`
    )
    .post(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.updateMagicAudienceAdObject,
        requestValidators: {
          body: schema.updateMagicAudienceAdObjectSchema,
        },
      })
    );

  router
    .route(
      `/communities/:communityId${MAGIC_AUDIENCE_PREFIX}/upload-video`
    )
    .post(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.uploadVideoToMetaForVideoId,
        requestValidators: {
          body: schema.uploadVideoToMetaForVideoIdSchema,
        },
      })
    )
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.getMagicAudienceVideoUploadStatus,
        requestValidators: {
          query: schema.getMagicAudienceVideoUploadStatusSchema,
        },
      })
    );

  router
    .route(
      `/communities/:communityId${MAGIC_AUDIENCE_PREFIX}/campaign/:campaignId/estimate`
    )
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.getMagicAudienceCampaignEstimate,
      })
    );

  router
    .route(`/communities${MAGIC_AUDIENCE_PREFIX}/ads-webhook`)
    .get(postRoutePreHandlerMiddleware, controller.adsWebhook)
    .post(postRoutePreHandlerMiddleware, controller.adsWebhookHandler);

  router
    .route(
      `/communities/:communityId${MAGIC_AUDIENCE_PREFIX}/get-meta-pages`
    )
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.getMetaPages,
        requestValidators: {
          query: schema.getUserPagesSchema,
        },
      })
    );

  router
    .route(
      `/communities/:communityId${MAGIC_AUDIENCE_PREFIX}/connect-facebook-page-to-business`
    )
    .post(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.connectUsersPageToBusiness,
        requestValidators: {
          body: schema.connectUsersPageToBusinessSchema,
        },
      })
    );

  router
    .route(
      `/communities/:communityId${MAGIC_AUDIENCE_PREFIX}/disconnect-facebook-page`
    )
    .delete(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.disconnectUsersPageFromBusiness,
      })
    );

  // TODO: to uncomment later
  router
    .route(
      `/communities/:communityId${MAGIC_AUDIENCE_PREFIX}/:campaignId/settle-funds`
    )
    .post(
      postRoutePreHandlerMiddleware,
      apiKeyValidation,
      handlerWrapper({
        handler: controller.settleFundsForCompletedMagicAudienceCampaign,
        requestValidators: {
          body: schema.settleFundsForMagicAudienceCampaignSchema,
        },
      })
    );

  router
    .route(
      `/communities/:communityId${MAGIC_AUDIENCE_PREFIX}/assign-ad-account`
    )
    .post(
      postRoutePreHandlerMiddleware,
      apiKeyValidation,
      handlerWrapper({
        handler: controller.assignAdAccountToCommunity,
        requestValidators: {
          body: schema.assignAdAccountToCommunitySchema,
        },
      })
    );

  router
    .route(
      `/communities/:communityId${MAGIC_AUDIENCE_PREFIX}/apply-for-magic-audience`
    )
    .post(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.applyForMagicAudience,
      })
    );
  // KEEPING THIS FOR FUTURE USE
  // router
  //   .route(
  //     '/communities/:communityId' +
  //       MAGIC_AUDIENCE_PREFIX +
  //       '/connect-ad-account'
  //   )
  //   .post(
  //     postRoutePreHandlerMiddleware,
  //     handlerWrapper({
  //       handler: controller.connectMetaAdsAccount,
  //     })
  //   );
};

module.exports = {
  setupRouter,
};
