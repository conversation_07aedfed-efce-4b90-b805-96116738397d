const yup = require('yup');
const {
  META_AD_FORMATS,
  META_ADS_STATUS,
} = require('../../../services/magicAudience/constants');

const createMagicAudienceCampaignSchema = yup.object().shape({
  campaignName: yup.string().required(),
  entityType: yup.string().required(),
  entityObjectId: yup.string().required(),
});

const getMagicAudienceCampaignsSchema = yup.object().shape({
  page: yup.number().min(1).default(1),
  limit: yup.number().min(1).max(100).default(10),
});

const getMagicAudienceCampaignByIdSchema = yup.object().shape({
  dateStart: yup.string().required(),
  dateEnd: yup.string().required(),
});
// helper → array of strings, min 1 max 5, length limit per item
const txtArr = (max) =>
  yup.array().of(yup.string().trim().max(max)).min(1).max(5).required();

const createMagicAudienceAdObjectSchema = yup.object({
  adName: yup.string().required(),
  primaryTexts: txtArr(240), // maps to bodies
  headlines: txtArr(40), // maps to titles
  descriptions: txtArr(40), // maps to descriptions
  adFormat: yup
    .string()
    .oneOf([META_AD_FORMATS.SINGLE_IMAGE, META_AD_FORMATS.SINGLE_VIDEO])
    .required(),
  mediaUrls: yup
    .array()
    .of(yup.string().url())
    .when('adFormat', {
      is: META_AD_FORMATS.SINGLE_IMAGE,
      then: (s) => s.min(1).max(10).required(),
      otherwise: (s) => s.strip(),
    }),
  videoInfo: yup
    .array()
    .of(
      yup.object({
        videoId: yup.string().notRequired(),
        videoUrl: yup.string().url().required(),
        videoThumbnailUrl: yup.string().url().notRequired(),
      })
    )
    .when('adFormat', {
      is: META_AD_FORMATS.SINGLE_VIDEO,
      then: (s) => s.min(1).max(10).required(),
      otherwise: (s) => s.strip(),
    }),
  variantNumber: yup.number().min(1).max(5),
  isAiGenerated: yup.boolean().default(false),
});

const updateMagicAudienceAdObjectSchema = yup.object({
  // optional arrays; same limits as before
  adName: yup.string().notRequired(),
  primaryTexts: txtArr(240).notRequired(),
  headlines: txtArr(40).notRequired(),
  descriptions: txtArr(40).notRequired(),
  status: yup
    .string()
    .oneOf([
      META_ADS_STATUS.DELETED,
      META_ADS_STATUS.PENDING,
      META_ADS_STATUS.PAUSED,
    ])
    .notRequired(),
  adFormat: yup
    .string()
    .oneOf([META_AD_FORMATS.SINGLE_IMAGE, META_AD_FORMATS.SINGLE_VIDEO])
    .notRequired(),
  mediaUrls: yup.array().of(yup.string().url()).notRequired(),
  videoInfo: yup
    .array()
    .of(
      yup.object({
        videoId: yup.string().notRequired(),
        videoUrl: yup.string().url().required(),
        videoThumbnailUrl: yup.string().url().notRequired(),
      })
    )
    .notRequired(),
});

const uploadVideoToMetaForVideoIdSchema = yup.object({
  videoLink: yup.string().url().required(),
});

const getMagicAudienceVideoUploadStatusSchema = yup.object().shape({
  videoId: yup.string().required(),
});

const connectUsersPageToBusinessSchema = yup.object().shape({
  pageId: yup.string().required(),
  accessToken: yup.string().required(),
});

const settleFundsForMagicAudienceCampaignSchema = yup.object().shape({
  totalSpend: yup.number().required(),
});

const assignAdAccountToCommunitySchema = yup.object().shape({
  pageId: yup.string().required(),
  pageTitle: yup.string().required(),
  pageDescription: yup.string().required(),
});

const getUserPagesSchema = yup.object().shape({
  code: yup.string().required(),
  redirectUri: yup.string().required(),
});
const approveOrRejectMagicAudienceCampaignSchema = yup.object().shape({
  status: yup.string().required(),
  // rejectedReason: yup.string().notRequired(),
  rejectedReason: yup.string().when('status', {
    is: (val) => val === META_ADS_STATUS.REJECTED,
    then: (s) => s.required(),
    otherwise: (s) => s.notRequired(),
  }),
});

const approveOrRejectMagicAudienceAdObjectSchema = yup.object().shape({
  status: yup.string().required(),
  rejectedReason: yup.string().when('status', {
    is: (val) => val === META_ADS_STATUS.REJECTED,
    then: (s) => s.required(),
    otherwise: (s) => s.notRequired(),
  }),
});

const updateMagicAudienceCampaignSchema = yup.object().shape({
  status: yup
    .string()
    .oneOf([
      META_ADS_STATUS.PAUSED,
      META_ADS_STATUS.ACTIVE,
      META_ADS_STATUS.COMPLETED,
    ])
    .notRequired(),
  dailyBudgetInUSD: yup
    .number()
    .when('status', {
      is: (val) => val === undefined,
      then: (s) => s.required(),
      otherwise: (s) => s.notRequired(),
    })
    .min(1),
  duration: yup
    .number()
    .when('status', {
      is: (val) => val === undefined,
      then: (s) => s.required(),
      otherwise: (s) => s.notRequired(),
    })
    .min(1),
  targeting: yup
    .object()
    .when('status', {
      is: (val) => val === undefined,
      then: (s) => s.required(),
      otherwise: (s) => s.notRequired(),
    })
    .notRequired(),
});

const getMagicAudienceProductsSchema = yup.object().shape({
  search: yup.string().notRequired(),
  pageSize: yup.string().required(),
});

module.exports = {
  getMagicAudienceVideoUploadStatusSchema,
  getMagicAudienceCampaignsSchema,
  getMagicAudienceCampaignByIdSchema,
  getMagicAudienceProductsSchema,
  getUserPagesSchema,
  createMagicAudienceCampaignSchema,
  createMagicAudienceAdObjectSchema,
  updateMagicAudienceAdObjectSchema,
  updateMagicAudienceCampaignSchema,
  uploadVideoToMetaForVideoIdSchema,
  approveOrRejectMagicAudienceCampaignSchema,
  approveOrRejectMagicAudienceAdObjectSchema,
  connectUsersPageToBusinessSchema,
  settleFundsForMagicAudienceCampaignSchema,
  assignAdAccountToCommunitySchema,
};
