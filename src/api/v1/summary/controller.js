const httpStatus = require('http-status');
const schema = require('./schema');
const logger = require('../../../services/logger.service');
const summaryService = require('../../../services/summary/summary.service');

const getSummary = async (req, res, next) => {
  try {
    const { date } = await schema.getSummarySchema.cast(req.query);
    const [state, data] = await summaryService.getSummary(
      req.user.learner._id,
      req.params.communityId,
      date
    );

    return res.status(httpStatus.OK).json({ state, data });
  } catch (err) {
    logger.error('getSummary failed due to', err);
    return next(err);
  }
};

const getSummaryExistence = async (req, res, next) => {
  try {
    const { month, year, timezone } =
      await schema.getSummaryExistenceSchema.cast(req.query);
    const response = await summaryService.getSummaryExistence(
      req.params.communityId,
      month,
      year,
      decodeURIComponent(timezone)
    );
    return res.status(httpStatus.OK).json({ data: response });
  } catch (err) {
    logger.error('getSummaryExistence failed due to', err);
    return next(err);
  }
};

const updateSummary = async (req, res, next) => {
  try {
    const body = await schema.updateSummarySchema.cast(req.body);
    const response = await summaryService.updateSummary(
      req.params.communityId,
      req.params.summaryId,
      req.user?.learner?._id,
      body
    );
    return res.status(httpStatus.OK).json({ data: response });
  } catch (err) {
    logger.error('updateSummary failed due to', err);
    return next(err);
  }
};

module.exports = {
  getSummary,
  getSummaryExistence,
  updateSummary,
};
