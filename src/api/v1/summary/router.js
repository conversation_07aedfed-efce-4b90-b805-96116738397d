const { Router } = require('express');
const schema = require('./schema');
const controller = require('./controller');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const { validator } = require('../../../middleware/validator.middleware');
const {
  managerCommunityValidator,
} = require('../../../communitiesAPI/validations/community.validation');

const router = Router({ mergeParams: true });

router
  .route('/')
  .get(
    postRoutePreHandlerMiddleware,
    validator(schema.getSummarySchema, 'query'),
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    controller.getSummary
  );

router
  .route('/exists')
  .get(
    postRoutePreHandlerMiddleware,
    validator(schema.getSummaryExistenceSchema, 'query'),
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    controller.getSummaryExistence
  );

router
  .route('/:summaryId')
  .put(
    postRoutePreHandlerMiddleware,
    validator(schema.updateSummarySchema, 'body'),
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    controller.updateSummary
  );

module.exports = router;
