const yup = require('yup');

exports.getSummarySchema = yup.object().shape({
  date: yup.date().notRequired(),
});

exports.getSummaryExistenceSchema = yup.object().shape({
  month: yup.number().required().integer(),
  year: yup.number().required().integer(),
  timezone: yup.string().default('utc'),
});

exports.updateSummarySchema = yup.object().shape({
  viewed: yup.boolean().notRequired(),
  liked: yup.boolean().notRequired(),
});
