const schema = require('./schema');
const logger = require('../../../services/logger.service');
const service = require('../../../services/activity');

exports.getActivities = async (req, res, next) => {
  try {
    const { communityId } = await schema.communityIdSchema.cast(
      req.params
    );

    const { activityTypes, lastActivityObjectId, limit } =
      await schema.activityQuerySchema.cast(req.query);

    const response = await service.retrieveActivities({
      communityId,
      activityTypes,
      lastActivityObjectId,
      limit,
    });

    return res.json({ data: response });
  } catch (err) {
    logger.error('getEarnings failed due to', err.message);
    return next(err);
  }
};
