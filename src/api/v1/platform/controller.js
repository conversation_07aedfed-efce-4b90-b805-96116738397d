const { DateTime } = require('luxon');

const Localization = require('../../../models/platform/localization.model');

const updateAccessedKeys = async (req) => {
  const keys = req.body.keys;
  const processedKeys = [];
  if (keys) {
    for (const key of keys) {
      if (key?.trim()) {
        processedKeys.push(key.trim());
      }
    }
  } else {
    return;
  }
  const lastAccessedDate = DateTime.utc().toJSDate();
  await Localization.updateMany(
    { key: { $in: processedKeys } },
    { lastAccessedDate }
  );
};

module.exports = {
  updateAccessedKeys,
};
