const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');

const { handlerWrapper } = require('../../../utils/request.util');

const controller = require('./controller');

const setupRouter = async function (router) {
  router.route('/platform/localization/keys-accessed').post(
    postRoutePreHandlerMiddleware,
    handlerWrapper({
      handler: controller.updateAccessedKeys,
    })
  );
};

module.exports = {
  setupRouter,
};
