const { Router } = require('express');
const schema = require('./schema');
const controller = require('./controller');
const tokenValidator = require('../../../validations/token.validation');
const tokenValidatorWithoutError = require('../../../validations/tokenNoError.validation');
const userValidation = require('../../../validations/user.validation');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const {
  validateAll,
} = require('../../../middleware/validator.middleware');
const {
  rateLimitMiddleware,
  MODULE_TYPE,
} = require('../../../utils/rateLimit.util');
const payloadSignatureValidator = require('../../../validations/payloadSignature.validation');
const { ACCESS_TOKEN_TYPE } = require('../../../constants/common');

const router = Router({ mergeParams: true });

router.route('/').post(
  postRoutePreHandlerMiddleware,
  rateLimitMiddleware({
    module: MODULE_TYPE.COMMUNITY_SIGNUP,
  }),
  payloadSignatureValidator,
  tokenValidator([ACCESS_TOKEN_TYPE.CHECKOUT]),
  userValidation,
  validateAll([
    { schema: schema.communitySignupBodySchema, location: 'body' },
  ]),
  controller.postCommunitySignup
);

router.route('/discount').post(
  postRoutePreHandlerMiddleware,
  rateLimitMiddleware({
    module: MODULE_TYPE.COMMUNITY_SIGNUP,
  }),
  payloadSignatureValidator,
  tokenValidatorWithoutError,
  validateAll([
    { schema: schema.communitySignupDiscountSchema, location: 'body' },
  ]),
  controller.postCommunitySignupDiscount
);

router.route('/confirm').post(
  postRoutePreHandlerMiddleware,
  payloadSignatureValidator,
  tokenValidator([ACCESS_TOKEN_TYPE.CHECKOUT]),
  userValidation,
  validateAll([
    {
      schema: schema.communitySignupConfirmBodySchema,
      location: 'body',
    },
  ]),
  controller.postCommunitySignupConfirm
);

router.route('/abort').post(
  postRoutePreHandlerMiddleware,
  validateAll([
    {
      schema: schema.communitySignupAbortBodySchema,
      location: 'body',
    },
  ]),
  controller.postCommunitySignupAbort
);

router.route('/payment-auth').post(
  postRoutePreHandlerMiddleware,
  validateAll([
    {
      schema: schema.communitySignupPaymentAuthBodySchema,
      location: 'body',
    },
  ]),
  controller.postCommunitySignupPaymentAuth
);

router
  .route('/update-payment-method')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    validateAll([{ schema: schema.communityIdSchema, location: 'query' }]),
    controller.getCommunityUpdatePaymentMethodInfo
  )
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    validateAll([
      { schema: schema.communityIdSchema, location: 'query' },
      {
        schema: schema.updatePaymentMethodBodySchema,
        location: 'body',
      },
    ]),
    controller.postCommunityUpdatePaymentMethodInfo
  );

module.exports = router;
