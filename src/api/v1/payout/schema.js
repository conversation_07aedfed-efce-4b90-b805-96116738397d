const yup = require('yup');
const {
  PAYOUT_TARGET_TYPE,
} = require('../../../services/payout/constants');

exports.communityIdSchema = yup.object().shape({
  communityId: yup.string().required(),
});

exports.filterQuerySchema = yup.object().shape({
  pageSize: yup.number().default(20).required().integer(),
  pageNo: yup.number().default(1).required().integer(),
});

exports.createPayoutTransactionSchema = yup.object().shape({
  payoutObjectId: yup.string().required().trim(),
});

exports.createAdvancePayoutSchema = yup.object().shape({
  communityCode: yup.string().required().trim(),
  currency: yup.string().required().trim(),
  transactionCreatedFrom: yup.string().required(),
  transactionCreatedTo: yup.string().required(),
  reason: yup.string().trim(),
  operator: yup.string().required().trim(),
});

exports.addAdjustmentSchema = yup.object().shape({
  communityCode: yup.string().required().trim(),
  amount: yup.number().required().positive(),
  currency: yup.string().required().trim(),
  transactionReferenceId: yup.string().required().trim(),
  reason: yup.string().required().trim(),
  operator: yup.string().required().trim(),
  adjustmentType: yup.string().required().trim(),
});

exports.updatePayoutToPaidSchema = yup.object().shape({
  payoutIds: yup.array(yup.string().required()).notRequired(),
  communityCodes: yup.array(yup.string().required()).notRequired(),
  payoutTargetType: yup.string().required().trim(),
  operator: yup.string().required().trim(),
});

exports.updatePayoutBankAccountSchema = yup.object().shape({
  payoutIds: yup.array(yup.string().required()).notRequired(),
  communityCodes: yup.array(yup.string().required()).notRequired(),
  operator: yup.string().required().trim(),
});

exports.updatePayoutByWebhookSchema = yup.object().shape({
  payoutObjectId: yup.string().required(),
  status: yup.string().required(),
  failureCode: yup.string(),
  failureReason: yup.string(),
  stripePayoutStatus: yup.string(),
  payoutDate: yup.date(),
  payoutTargetType: yup.string().default(PAYOUT_TARGET_TYPE.COMMUNITY),
});

exports.addCommunityPayoutAccountSchema = yup.object().shape({
  payoutAccountType: yup.string().required().trim(),
  metadata: yup.object().shape({
    refreshUrl: yup.string().trim(),
    returnUrl: yup.string().trim(),
  }),
});

exports.deleteCommunityPayoutAccountSchema = yup.object().shape({
  payoutAccountType: yup.string().required().trim(),
});

exports.stripeConnectWebhookEventsSchema = yup.object().shape({
  stripeAccountId: yup.string().required().trim(),
  accountDetails: yup.object(),
});
