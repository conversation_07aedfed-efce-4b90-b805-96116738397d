const { Router } = require('express');
const schema = require('./schema');
const controller = require('./controller');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');
const {
  managerCommunityValidator,
} = require('../../../communitiesAPI/validations/community.validation');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const {
  validateAll,
} = require('../../../middleware/validator.middleware');

const router = Router({ mergeParams: true });

router.route('/').post(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  validateAll([
    { schema: schema.postWebhooksSchema, location: 'body' },
    {
      schema: schema.communityIdSchema,
      location: 'params',
    },
  ]),
  controller.registerWebhook
);

router.route('/:webhookId').delete(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  validateAll([
    {
      schema: schema.webhookIdSchema,
      location: 'params',
    },
  ]),
  controller.unregisterWebhook
);

router.route('/samples/:source/:triggerType').get(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  validateAll([
    {
      schema: schema.webhookSampleSchema,
      location: 'params',
    },
  ]),
  controller.retrieveWebhookSampleEvent
);

module.exports = router;
