const schema = require('./schema');
const logger = require('../../../services/logger.service');
const service = require('../../../services/webhook');

exports.registerWebhook = async (req, res, next) => {
  try {
    const { communityId: communityObjectId } =
      await schema.communityIdSchema.cast(req.params);

    const { targetUrl, triggerType, source } =
      await schema.postWebhooksSchema.cast(req.body);

    const response = await service.registerWebhook({
      communityObjectId,
      targetUrl,
      triggerType,
      source,
    });

    return res.json(response);
  } catch (err) {
    logger.error('registerWebhook failed due to', err.message, err.stack);
    return next(err);
  }
};

exports.unregisterWebhook = async (req, res, next) => {
  try {
    const { communityId: communityObjectId, webhookId: webhookObjectId } =
      await schema.webhookIdSchema.cast(req.params);

    const response = await service.unregisterWebhook({
      communityObjectId,
      webhookObjectId,
    });

    return res.json(response);
  } catch (err) {
    logger.error(
      'unregisterWebhook failed due to',
      err.message,
      err.stack
    );
    return next(err);
  }
};

exports.retrieveWebhookSampleEvent = async (req, res, next) => {
  try {
    const {
      communityId: communityObjectId,
      triggerType,
      source,
    } = await schema.webhookSampleSchema.cast(req.params);

    const response = await service.retrieveSampleEvent({
      communityObjectId,
      triggerType,
      source,
    });

    return res.json(response);
  } catch (err) {
    logger.error(
      'retrieveWebhookSampleEvent failed due to',
      err.message,
      err.stack
    );
    return next(err);
  }
};
