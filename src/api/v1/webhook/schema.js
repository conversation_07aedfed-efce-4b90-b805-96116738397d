const yup = require('yup');

exports.communityIdSchema = yup.object().shape({
  communityId: yup.string().required(),
});

exports.webhookIdSchema = yup.object().shape({
  communityId: yup.string().required(),
  webhookId: yup.string().required(),
});

exports.webhookSampleSchema = yup.object().shape({
  communityId: yup.string().required(),
  triggerType: yup.string().required().trim(),
  source: yup.string().required().trim(),
});

exports.postWebhooksSchema = yup.object().shape({
  targetUrl: yup.string().required().trim(),
  triggerType: yup.string().required().trim(),
  source: yup.string().required().trim(),
});
