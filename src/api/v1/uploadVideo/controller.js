const httpStatus = require('http-status');
const aws = require('aws-sdk');
const logger = require('../../../services/logger.service');
const constant = require('./constant');
const {
  IMAGEASSETBUCKET_CLOUDFRONT_BASE_URL,
  IMAGEASSETBUCKET,
} = require('../../../constants/common');
const { ToUserError } = require('../../../utils/error.util');
const {
  awsSecretKey,
  awsAccessKey,
  awsRegion,
} = require('../../../config');
const { getExtension } = require('mime');

function initializeS3() {
  const awsConfig = awsSecretKey
    ? {
        secretAccessKey: awsSecretKey,
        accessKeyId: awsAccessKey,
      }
    : { credentialProvider: new aws.CredentialProviderChain() };

  aws.config.update({
    region: awsRegion,
    ...awsConfig,
  });

  aws.config.update({ useAccelerateEndpoint: true });

  return new aws.S3();
}
const bucketPath = (
  leadingBucket = 'community-videos',
  communityId,
  entityType,
  entityId,
  mailType,
  dynamicTrailingPaths = []
) => {
  const path = `nasIO/portal/${leadingBucket}`;
  const components = [];
  if (communityId) {
    components.push(communityId);
  }
  if (entityType) {
    components.push(entityType);
  }
  if (entityId) {
    components.push(entityId);
  }
  if (dynamicTrailingPaths.length > 0) {
    components.push(...dynamicTrailingPaths);
  } else {
    components.push('common');
  }

  return `${path}/${components.join('/')}`;
};
function cleanFileName(fileName) {
  // Remove leading and trailing whitespace
  let cleanedName = fileName.trim();
  // Replace spaces with underscores
  cleanedName = cleanedName.replace(/ /g, '_');
  // Remove special characters except for periods (.)
  cleanedName = cleanedName.replace(/[^\w.-]/g, '');
  // Remove leading periods and dashes
  cleanedName = cleanedName.replace(/^[-.]+/, '');
  // Ensure the filename is not empty after cleaning
  if (!cleanedName) {
    cleanedName = Date.now().toString() + '_file';
  }
  return cleanedName;
}

function getFileNameFromFile(originalname, extension) {
  const cleanName = cleanFileName(originalname[0]);
  const fileName = `${cleanName}_${Date.now().toString()}.${extension}`;
  return fileName;
}

function getLeadingBucket(entityType) {
  switch (entityType) {
    default:
      return 'community-videos';
  }
}

function getDynamicTrailingPaths(entityType, participantId, checkpointId) {
  switch (entityType) {
    case constant.entityTypes.CHALLENGE_CHECKPOINT_SUBMISSION: {
      return ['participants', participantId, 'checkpoints', checkpointId];
    }
    default:
      return [];
  }
}
exports.returnFilePath = async (req, res, next) => {
  const fileKey = req?.file?.key;
  const fileURL = `${IMAGEASSETBUCKET_CLOUDFRONT_BASE_URL}/${fileKey}`;

  logger.info('Responded with link: ', fileURL);
  return res.status(httpStatus.OK).json({ videoUrl: fileURL });
};

const fileFilter = (mimetype) => {
  if (
    mimetype === 'video/mp4' ||
    mimetype === 'video/webm' ||
    mimetype === 'video/x-m4v' ||
    mimetype === 'video/quicktime'
  ) {
    return true;
  }
  return false;
};

async function getSingleUploadUrl(bucket, key, mimetype, s3) {
  const url = await s3.getSignedUrl('putObject', {
    Bucket: bucket,
    Key: key,
    ContentType: mimetype,
    Expires: 3600,
  });
  return url;
}

exports.getSignedUrlForVideo = async (req, res, next) => {
  const {
    originalname,
    mimetype,
    entityType,
    entityId,
    mailType,
    participantId,
    checkpointId,
  } = req.query;

  if (!fileFilter(mimetype)) {
    throw new ToUserError(
      'Invalid file type. Only video files are allowed.'
    );
  }

  const extensionForFile = getExtension(mimetype);
  const fileName = getFileNameFromFile(
    originalname?.split('.'),
    extensionForFile
  );

  const key = `${bucketPath(
    getLeadingBucket(entityType),
    req.params.communityId,
    entityType,
    entityId,
    mailType,
    getDynamicTrailingPaths(entityType, participantId, checkpointId)
  )}/${fileName}`;

  const s3 = initializeS3();
  const url = await getSingleUploadUrl(
    IMAGEASSETBUCKET,
    key,
    mimetype,
    s3
  );

  return {
    url,
    fileKey: `${IMAGEASSETBUCKET_CLOUDFRONT_BASE_URL}/${key}`,
  };
};
