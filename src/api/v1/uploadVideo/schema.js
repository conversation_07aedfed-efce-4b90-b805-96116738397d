const yup = require('yup');
const constant = require('./constant');

const entityTypeEnums = Object.values(constant.entityTypes);

exports.uploadImagePathSchema = yup.object().shape({
  communityId: yup.string().required(),
});

exports.uploadVideoSchema = yup.object().shape({
  file: yup.object().required(),
  entityType: yup.mixed().oneOf(entityTypeEnums),
  entityId: yup.string().notRequired(),
});
