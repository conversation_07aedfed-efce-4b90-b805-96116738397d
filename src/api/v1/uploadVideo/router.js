const schema = require('./schema');
const controller = require('./controller');
const middleware = require('./middleware');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const {
  validateAll,
} = require('../../../middleware/validator.middleware');
const {
  managerCommunityValidator,
} = require('../../../communitiesAPI/validations/community.validation');
const { handlerWrapper } = require('../../../utils/request.util');

exports.setupRouter = function (router) {
  router
    .route('/video/community/:communityId/multipart')
    .post(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      middleware.uploadMultiPartVideo.single('video'),
      controller.returnFilePath
    );

  router.route('/video/community/:communityId/video-presigned-url').get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    handlerWrapper({
      handler: controller.getSignedUrlForVideo,
    })
  );
};
