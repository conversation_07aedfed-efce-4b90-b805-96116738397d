const aws = require('aws-sdk');
const crypto = require('crypto');
const constant = require('./constant');
const multer = require('multer');
const multerS3 = require('multer-s3');
const { ParamError } = require('../../../utils/error.util');

const {
  awsRegion,
  awsSecretKey,
  awsAccessKey,
  awsAccessLevel,
} = require('../../../config');
const {
  IMAGEASSETBUCKET,
  IMAGEASSETBUCKET_CLOUDFRONT_BASE_URL,
} = require('../../../constants/common');

const thresholdVideoSize = 100000000; // 100MB

const bucketPath = (
  leadingBucket = 'community-videos',
  communityId,
  entityType,
  entityId,
  mailType,
  dynamicTrailingPaths = []
) => {
  const path = `nasIO/portal/${leadingBucket}`;
  const components = [];
  if (communityId) {
    components.push(communityId);
  }
  if (entityType) {
    components.push(entityType);
  }
  if (entityId) {
    components.push(entityId);
  }
  if (dynamicTrailingPaths.length > 0) {
    components.push(...dynamicTrailingPaths);
  } else {
    components.push('common');
  }

  return `${path}/${components.join('/')}`;
};

const config = awsSecretKey
  ? {
      secretAccessKey: awsSecretKey,
      accessKeyId: awsAccessKey,
    }
  : { credentialProvider: new aws.CredentialProviderChain() };

aws.config.update({
  region: awsRegion,
  ...config,
});

const s3 = new aws.S3();

const fileFilter = (req, file, cb) => {
  if (
    file.mimetype === 'video/mp4' ||
    file.mimetype === 'video/mp' ||
    file.mimetype === 'video/webm' ||
    file.mimetype === 'video/x-m4v' ||
    file.mimetype === 'video/quicktime'
  ) {
    cb(null, true);
  } else {
    const err = new Error(
      'Invalid file type. Only video files are allowed.'
    );
    err.status = 400;
    cb(err, false);
  }
};

function validateParams(req) {
  const { entityType } = req.body;
  switch (entityType) {
    case constant.entityTypes.CHALLENGE_CHECKPOINT_SUBMISSION: {
      const { participantId, checkpointId } = req.body;
      if (!participantId || !checkpointId) {
        return new ParamError(
          `participantId and checkpointId are needed for ${constant.entityTypes.CHALLENGE_CHECKPOINT_SUBMISSION}`
        );
      }
      break;
    }
    default:
      break;
  }
}

function getLeadingBucket(req) {
  const { entityType } = req.body;
  switch (entityType) {
    default:
      return 'community-videos';
  }
}

function getDynamicTrailingPaths(req) {
  const { entityType } = req.body;
  switch (entityType) {
    case constant.entityTypes.CHALLENGE_CHECKPOINT_SUBMISSION: {
      const { participantId, checkpointId } = req.body;
      return ['participants', participantId, 'checkpoints', checkpointId];
    }
    default:
      return [];
  }
}

function cleanFileName(fileName) {
  // Remove leading and trailing whitespace
  let cleanedName = fileName.trim();
  // Replace spaces with underscores
  cleanedName = cleanedName.replace(/ /g, '_');
  // Remove special characters except for periods (.)
  cleanedName = cleanedName.replace(/[^\w.-]/g, '');
  // Remove leading periods and dashes
  cleanedName = cleanedName.replace(/^[-.]+/, '');
  // Ensure the filename is not empty after cleaning
  if (!cleanedName) {
    cleanedName = Date.now().toString() + '_file';
  }
  return cleanedName;
}

function getFileNameFromFile(file) {
  const nameParts = file.originalname.split('.');
  const extension = nameParts[nameParts.length - 1];
  const cleanName = cleanFileName(nameParts.slice(0, -1).join('.'));
  const fileName = `${cleanName}_${Date.now().toString()}.${extension}`;
  return fileName;
}

const uploadMultipartVideo = () => {
  return multer({
    limits: { fileSize: thresholdVideoSize },
    fileFilter: fileFilter,
    onError: function (err, next) {
      // eslint-disable-next-line no-param-reassign
      err.status = 500;
      next(err);
    },
    storage: multerS3({
      s3: s3,
      bucket: IMAGEASSETBUCKET,
      dest: (req, file, cb) => {
        const { entityType, entityId, mailType } = req.body;
        const path = bucketPath(
          getLeadingBucket(req),
          req.params.communityId,
          entityType,
          entityId,
          mailType,
          getDynamicTrailingPaths(req)
        );
        cb(null, path);
      },
      acl: awsAccessLevel,
      metadata: (req, file, cb) => {
        const fileName = getFileNameFromFile(file);
        cb(null, { name: fileName });
      },
      key: (req, file, cb) => {
        const { entityType, entityId, mailType } = req.body;
        const fileName = getFileNameFromFile(file);
        const key = `${bucketPath(
          getLeadingBucket(req),
          req.params.communityId,
          entityType,
          entityId,
          mailType,
          getDynamicTrailingPaths(req)
        )}/${fileName}`;
        cb(null, key);
      },
    }),
  });
};

module.exports = {
  uploadMultiPartVideo: uploadMultipartVideo(),
};
