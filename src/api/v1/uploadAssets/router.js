const controller = require('./controller');
const middleware = require('./middleware');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');

const { handlerWrapper } = require('../../../utils/request.util');

exports.setupRouter = function (router) {
  router.route('/assets/presigned-url').get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    middleware.formatFileRequest,
    handlerWrapper({
      handler: controller.getAssetSingleSignedUrl,
    })
  );
};
