const { v4: uuidv4 } = require('uuid');
const { getExtension } = require('mime');

const constant = require('./constant');
const { ParamError } = require('../../../utils/error.util');

function getFileCategory(mimetype) {
  if (constant.DOCUMENT_MIMETYPES.includes(mimetype)) {
    return constant.ASSETS_CATEGORIES.DOCUMENT;
  }
  if (constant.IMAGE_MIMETYPES.includes(mimetype)) {
    return constant.ASSETS_CATEGORIES.IMAGE;
  }
  throw new ParamError(`Invalid file type ${mimetype}!`);
}

function getLeadingFolderPath(defaultLeadingPath = '', query = {}) {
  const { useCase } = query;

  switch (useCase) {
    case constant.ASSETS_USE_CASES.MAGIC_REACH:
    case constant.ASSETS_USE_CASES.CUSTOM_WELCOME_EMAIL:
    case constant.ASSETS_USE_CASES.CUSTOM_EVENT_EMAIL:
      return 'email-images';
    default:
      return defaultLeadingPath;
  }
}

function getTrailingPath(query = {}) {
  const { useCase } = query;
  let dynamicTrailingPaths = [];
  switch (useCase) {
    case constant.ASSETS_USE_CASES.CHALLENGE_CHECKPOINT_SUBMISSION: {
      const { participantId, checkpointId } = query;
      if (!participantId || !checkpointId) {
        throw ParamError('participantId and checkpointId not specified!');
      }
      dynamicTrailingPaths = [
        'participants',
        participantId,
        'checkpoints',
        checkpointId,
      ];
      break;
    }
    default:
      break;
  }
  return dynamicTrailingPaths.join('/');
}

function generateFullFolderPath(
  leadingPath = '',
  query = {},
  trailingPath = ''
) {
  const { communityId, useCase, entityId } = query;
  const components = [];
  if (leadingPath !== '') {
    components.push(leadingPath);
  }
  if (communityId) {
    components[0] = `nasIO/portal/${leadingPath}/${communityId}`;
  }
  if (useCase) {
    components.push(useCase);
  }
  if (entityId) {
    components.push(entityId);
  }

  if (trailingPath !== '') {
    components.push(trailingPath);
  } else {
    components.push('common');
  }

  return components.join('/');
}

/**
 *
 * @param {*} assetStandards //constant.ASSETS_STANDARDS[category];
 * @param {*} query // req.query
 */
function getFileFormattedDetails(category, query = {}) {
  let defaultProperties;
  if (category === constant.ASSETS_CATEGORIES.DOCUMENT) {
    defaultProperties = constant.DOCUMENT_DEFAULT_PROPERTIES;
  }
  if (category === constant.ASSETS_CATEGORIES.IMAGE) {
    defaultProperties = constant.IMAGE_DEFAULT_PROPERTIES;
  }
  // FE will do the checking. Abit redundant for BE to check at this stage
  // if (query.fileSize > defaultProperties.fileSize) {
  //   throw new ParamError(
  //     `File is too large. File size should be less than ${defaultProperties.fileSize} bytes`
  //   );
  // }
  const dateString = Date.now().toString();
  const {
    needVariants,
    variantIdentifier,
    minFileSizeInBytes,
    maxFileSizeInBytes,
  } = defaultProperties;
  const { mimetype } = query;
  const leadingPath = getLeadingFolderPath(
    defaultProperties.leadingPath,
    query
  );
  const trailingPath = getTrailingPath(query);
  const fullFolderPath = generateFullFolderPath(
    leadingPath,
    query,
    trailingPath
  );
  const finalFileName = `${
    needVariants ? variantIdentifier.prefix : ''
  }${dateString}_${uuidv4()}${
    needVariants ? variantIdentifier.suffix : ''
  }`;
  const extension = getExtension(mimetype);
  const key = `${fullFolderPath}/${finalFileName}${
    needVariants ? '' : '.' + extension
  }`;

  return {
    bucket: defaultProperties.bucket,
    baseUrl: defaultProperties.cloudFrontBaseUrl,
    key,
    mimetype,
    minFileSizeInBytes,
    maxFileSizeInBytes,
  };
}

exports.formatFileRequest = (req, res, next) => {
  const { mimetype } = req.query;

  const category = getFileCategory(mimetype);
  const fileFormattedDetails = getFileFormattedDetails(
    category,
    req.query
  );

  req.fileFormattedDetails = fileFormattedDetails;

  next();
};
