const {
  IMAGEASSETBUCKET,
  IMAGEASSETBUCKET_CLOUDFRONT_BASE_URL,
} = require('../../../constants/common');

const DOCUMENT_MIMETYPES = [
  'application/pdf', // PDF
  'application/msword', // DOC
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // DOCX
  'application/vnd.ms-excel', // XLS
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // XLSX
  'application/vnd.ms-powerpoint', // PPT
  'application/vnd.openxmlformats-officedocument.presentationml.presentation', // PPTX
];

const IMAGE_MIMETYPES = [
  'image/jpeg',
  'image/jpg',
  'image/gif',
  'image/png',
  'image/webp',
];

const ASSETS_CATEGORIES = {
  IMAGE: 'image',
  DOCUMENT: 'document',
};

const IMAGE_DEFAULT_PROPERTIES = {
  minFileSizeInBytes: 1,
  maxFileSizeInBytes: 1024 * 1024 * 10, // 10 MB
  needVariants: true,
  variantIdentifier: {
    prefix: 'nio_',
    suffix: '_GV',
  },
  bucket: IMAGEASSETBUCKET,
  cloudFrontBaseUrl: IMAGEASSETBUCKET_CLOUDFRONT_BASE_URL,
  leadingPath: 'community-images',
  trailingPath: '',
};

const DOCUMENT_DEFAULT_PROPERTIES = {
  minFileSizeInBytes: 1,
  maxFileSizeInBytes: 1024 * 1024 * 10, // 10 MB
  needVariants: false,
  bucket: IMAGEASSETBUCKET,
  cloudFrontBaseUrl: IMAGEASSETBUCKET_CLOUDFRONT_BASE_URL,
  leadingPath: '',
  trailingPath: '',
};

const ASSETS_USE_CASES = {
  MAGIC_REACH: 'magicReach',
  CUSTOM_WELCOME_EMAIL: 'customWelcomeEmail',
  CUSTOM_EVENT_EMAIL: 'customEventEmail',
  ANNOUNCEMENT: 'announcement',
  COMMUNITY: 'community',
  CONTENT: 'content',
  SESSION: 'session',
  EVENT: 'event',
  USER: 'user',
  CHALLENGE: 'challenge',
  CHALLENGE_SUBMISSION: 'challengeSubmission',
  CHALLENGE_CHECKPOINT_SUBMISSION: 'challengeCheckPointSubmission',
  VIDEO_THUMBNAIL: 'video_thumbnail', //new
};

module.exports = {
  DOCUMENT_MIMETYPES,
  DOCUMENT_DEFAULT_PROPERTIES,
  IMAGE_MIMETYPES,
  IMAGE_DEFAULT_PROPERTIES,
  ASSETS_CATEGORIES,
  ASSETS_USE_CASES,
};
