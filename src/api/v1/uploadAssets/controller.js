const aws = require('aws-sdk');
const {
  awsSecretKey,
  awsAccessKey,
  awsRegion,
} = require('../../../config');

function initializeS3() {
  const awsConfig = awsSecretKey
    ? {
        secretAccessKey: awsSecretKey,
        accessKeyId: awsAccessKey,
      }
    : { credentialProvider: new aws.CredentialProviderChain() };

  aws.config.update({
    region: awsRegion,
    ...awsConfig,
  });

  aws.config.update({ useAccelerateEndpoint: true });

  return new aws.S3();
}

async function getSingleUploadUrl(
  bucket,
  key,
  mimetype,
  s3,
  minFileSizeInBytes = 1,
  maxFileSizeInBytes
) {
  const data = await s3.createPresignedPost({
    Bucket: bucket,
    Fields: {
      key,
      'Content-Type': mimetype,
    },
    Conditions: [
      ['content-length-range', minFileSizeInBytes, maxFileSizeInBytes],
      ['starts-with', '$key', ''], // Ensures key exists
      ['eq', '$Content-Type', mimetype],
    ],
    Expires: 3600,
  });
  return data;
}

// eslint-disable-next-line no-unused-vars
exports.getAssetSingleSignedUrl = async (req, res, next) => {
  const {
    bucket,
    key,
    mimetype,
    baseUrl,
    minFileSizeInBytes,
    maxFileSizeInBytes,
  } = req.fileFormattedDetails;

  const s3 = initializeS3();
  const postData = await getSingleUploadUrl(
    bucket,
    key,
    mimetype,
    s3,
    minFileSizeInBytes,
    maxFileSizeInBytes
  );

  return {
    assetUrl: `${baseUrl}/${key}`,
    ...postData,
  };
};
