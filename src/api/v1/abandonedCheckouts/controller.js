const schema = require('./schema');
const service = require('../../../services/abandonedCarts/abandonedCarts.service');

exports.getAbandonedCheckoutFilters = async (req, res, next) => {
  const { communityId } = schema.communityIdPathSchema.cast(req.params);

  const { entityType, search, pageSize, pageNo, sortBy, sortOrder } =
    schema.getAbandonedCheckoutsFilterQuerySchema.cast(req.query);

  const result = await service.getAbandonedCheckoutFilters({
    communityId,
    entityType,
    search,
    pageSize,
    pageNo,
    sortBy,
    sortOrder,
  });

  return result;
};

exports.updateAbandonedCheckoutRecord = async (req, res, next) => {
  const {
    purchaseType,
    purchaseTransactionId,
    addonTransactionId,
    communityId,
    learnerId,
    entity,
  } = schema.updateAbandonedCheckoutsDataSchema.cast(req.body);

  const result = await service.updateAbandonedCheckoutData({
    purchaseType,
    purchaseTransactionId,
    addonTransactionId,
    communityId,
    learnerId,
    entity,
  });

  return result;
};
