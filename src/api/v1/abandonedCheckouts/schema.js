const yup = require('yup');
const { sortParams } = require('../../../constants/common');

exports.communityIdPathSchema = yup.object().shape({
  communityId: yup.string().required(),
});

exports.getAbandonedCheckoutsFilterQuerySchema = yup.object().shape({
  entityType: yup.string().uppercase().required().trim(),
  search: yup.string().uppercase().notRequired().trim(),
  pageSize: yup.number().default(20).required().integer(),
  pageNo: yup.number().default(1).required().integer(),
  sortBy: yup.string().default(sortParams.CREATED_AT).notRequired().trim(),
  sortOrder: yup.number().default(-1).notRequired(),
});

exports.updateAbandonedCheckoutsDataSchema = yup.object().shape({
  purchaseType: yup.string().uppercase().required().trim(),
  communityId: yup.string().required().trim(),
  learnerId: yup.string().required().trim(),
  addonTransactionId: yup.string().trim(),
  purchaseTransactionId: yup.string().trim(),
  entity: yup.object(),
});
