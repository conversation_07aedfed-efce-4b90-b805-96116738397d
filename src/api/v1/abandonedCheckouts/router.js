const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');
const {
  managerCommunityValidator,
} = require('../../../communitiesAPI/validations/community.validation');
const { handlerWrapper } = require('../../../utils/request.util');
const controller = require('./controller');
const schema = require('./schema');
const apiKeyValidation = require('../../../validations/apiKey.validation');

const ABANDONED_CHECKOUT_PREFIX = '/abandoned-checkout';

const setupRouter = async function (router) {
  router
    .route(`/communities/:communityId${ABANDONED_CHECKOUT_PREFIX}/filters`)
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.getAbandonedCheckoutFilters,
        requestValidators: {
          query: schema.getAbandonedCheckoutsFilterQuerySchema,
          params: schema.communityIdPathSchema,
        },
      })
    );

  router.route(`/communities/abandoned-checkout/update`).post(
    postRoutePreHandlerMiddleware,
    apiKeyValidation,
    handlerWrapper({
      handler: controller.updateAbandonedCheckoutRecord,
      requestValidators: {
        body: schema.updateAbandonedCheckoutsDataSchema,
      },
    })
  );
};

module.exports = {
  setupRouter,
};
