const schema = require('./schema');
const controller = require('./controller');
const middleware = require('./middleware');
const entityTypeValidator = require('../../../validations/entityType.validation');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');
const { handlerWrapper } = require('../../../utils/request.util');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const {
  validateAll,
} = require('../../../middleware/validator.middleware');
const {
  managerCommunityValidator,
} = require('../../../communitiesAPI/validations/community.validation');

exports.setupRouter = function (router) {
  router.route('/image/community/:communityId').post(
    postRoutePreHandlerMiddleware,
    validateAll([
      {
        schema: schema.uploadCommunityImagePathSchema,
        location: 'params',
      },
      { schema: schema.uploadCommunityImageSchema, location: 'body' },
    ]),
    tokenValidator(),
    userValidation,
    middleware.uploadCommunityBase64Image,
    controller.returnFilePath
  );

  router
    .route('/image/base64')
    .post(
      postRoutePreHandlerMiddleware,
      validateAll([
        { schema: schema.uploadImageSchema, location: 'body' },
      ]),
      tokenValidator(),
      userValidation,
      middleware.uploadBase64Image,
      controller.returnFilePathAndGenerateVariants
    );

  router
    .route('/image/community/:communityId/multipart')
    .post(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      middleware.uploadCommunityMultiPartImage.single('image'),
      entityTypeValidator,
      controller.returnFilePath
    );

  router
    .route('/image/multipart')
    .post(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      middleware.uploadMultiPartImage.single('image'),
      controller.returnFilePathAndGenerateVariants
    );

  router.route('/image/optimize').post(
    postRoutePreHandlerMiddleware,
    handlerWrapper({
      handler: controller.compressAndGenerateImageVariants,
      wrapResponseInObject: false,
    })
  );

  router
    .route('/image/variants')
    .post(postRoutePreHandlerMiddleware, controller.generateImageVariants);
};
