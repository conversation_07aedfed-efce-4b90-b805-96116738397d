const yup = require('yup');
const constant = require('./constant');

const entityTypeEnums = Object.values(constant.entityTypes);

exports.uploadCommunityImagePathSchema = yup.object().shape({
  communityId: yup.string().required(),
});

exports.uploadCommunityImageSchema = yup.object().shape({
  base64: yup.string().required(),
  entityType: yup.mixed().oneOf(entityTypeEnums),
  entityId: yup.mixed(),
  mailType: yup.string().notRequired(),
});

exports.uploadImageSchema = yup.object().shape({
  base64: yup.string().required(),
  useCase: yup.string().required(),
  associatedEntityId: yup.string().required(),
  additionalPaths: yup.array().notRequired(),
});
