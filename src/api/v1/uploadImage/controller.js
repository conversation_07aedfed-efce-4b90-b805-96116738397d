const httpStatus = require('http-status');
const logger = require('../../../services/logger.service');
const generateImageVariantService = require('../../../services/upload/generateImageVariants.service');

const {
  IMAGEASSETBUCKET_CLOUDFRONT_BASE_URL,
  IMAGEASSETBUCKET_S3_BASE_URL,
} = require('../../../constants/common');

exports.returnFilePathAndGenerateVariants = async (req, res, next) => {
  const imageKey = req?.file?.key;
  const imageUrl = `${IMAGEASSETBUCKET_CLOUDFRONT_BASE_URL}/${imageKey}`;

  if (imageKey !== null && imageKey !== undefined) {
    generateImageVariantService.generateImageVariants(imageUrl);
    logger.info('Responded with link: ', imageUrl);
    return res.status(httpStatus.OK).json({ imageUrl: imageUrl });
  }
  const error = new Error(
    'Error on image url generation. Ensure image is attached'
  );
  error.status = 400;
  return next(error);
};

exports.returnFilePath = async (req, res, next) => {
  const imageKey = req?.file?.key;
  const imageUrl = `${IMAGEASSETBUCKET_CLOUDFRONT_BASE_URL}/${imageKey}`;
  logger.info('Responded with link: ', imageUrl);
  return res.status(httpStatus.OK).json({ imageUrl: imageUrl });
};

exports.generateImageVariants = async (req, res, next) => {
  const { imageUrl } = req?.body;
  try {
    await generateImageVariantService.generateImageVariants(imageUrl);
    logger.info('Successfully generated image variants');
    return res.status(httpStatus.OK).json({});
  } catch (error) {
    logger.error(
      'An error occured when generating image variants: ',
      error.message,
      error.stack
    );
    return next(error);
  }
};

async function optimizeImage(fileName, imageUrl) {
  const imageBuffer = await generateImageVariantService.downloadImage(
    imageUrl
  );
  await Promise.all([
    generateImageVariantService.compressImageFromBuffer(
      imageBuffer,
      fileName
    ),
  ]);
}

exports.compressAndGenerateImageVariants = async (req, res, next) => {
  const { imageUrl } = req?.body;

  if (!imageUrl.startsWith(IMAGEASSETBUCKET_S3_BASE_URL)) {
    logger.error('Invalid imageUrl:', imageUrl);
    return;
  }

  const fileName = imageUrl.replace(
    IMAGEASSETBUCKET_S3_BASE_URL + '/',
    ''
  );
  optimizeImage(fileName, imageUrl);
  console.log(
    'Compressing and generating image variant in the background, returning now.'
  );
  return {};
};
