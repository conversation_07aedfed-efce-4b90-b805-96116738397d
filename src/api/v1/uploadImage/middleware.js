const aws = require('aws-sdk');
const crypto = require('crypto');
const constant = require('./constant');
const multer = require('multer');
const multerS3 = require('multer-s3');
const { ParamError } = require('../../../utils/error.util');

const {
  awsRegion,
  awsSecretKey,
  awsAccessKey,
  awsAccessLevel,
} = require('../../../config');
const {
  IMAGEASSETBUCKET,
  IMAGEASSETBUCKET_CLOUDFRONT_BASE_URL,
} = require('../../../constants/common');

const thresholdImageSize = 10000000;

const bucketPath = (
  leadingBucket = 'community-images',
  communityId,
  entityType,
  entityId,
  mailType,
  dynamicTrailingPaths = []
) => {
  const path = `nasIO/portal/${leadingBucket}`;
  const components = [];
  if (communityId) {
    components.push(communityId);
  }
  if (entityType) {
    components.push(entityType);
  }
  if (entityId) {
    components.push(entityId);
  }
  if (mailType || dynamicTrailingPaths.length > 0) {
    if (mailType) {
      components.push(mailType);
    }
    if (dynamicTrailingPaths.length) {
      components.push(...dynamicTrailingPaths);
    }
  } else {
    components.push('common');
  }

  return `${path}/${components.join('/')}`;
};

const getBucketPath = (useCase, associatedEntityId, additionalPaths) => {
  const components = [];

  if (useCase) {
    components.push(useCase);
  }
  if (associatedEntityId) {
    components.push(associatedEntityId);
  }
  if (additionalPaths) {
    components.push(additionalPaths.join('/'));
  }
  return components.join('/');
};

const config = awsSecretKey
  ? {
      secretAccessKey: awsSecretKey,
      accessKeyId: awsAccessKey,
    }
  : { credentialProvider: new aws.CredentialProviderChain() };

aws.config.update({
  region: awsRegion,
  ...config,
});

const s3 = new aws.S3();

const fileFilter = (req, file, cb) => {
  if (
    file.mimetype === 'image/jpeg' ||
    file.mimetype === 'image/jpg' ||
    file.mimetype === 'image/gif' ||
    file.mimetype === 'image/png' ||
    file.mimetype === 'image/webp'
  ) {
    cb(null, true);
  } else {
    const err = new Error(
      'Invalid file type. It has to be a jpeg, png or webp file'
    );
    err.status = 400;
    cb(err, false);
  }
};

function validateParams(req) {
  const { entityType, entityId, mailType } = req.body;
  switch (entityType) {
    case constant.entityTypes.MAGIC_REACH:
      if (mailType) {
        return new ParamError(`mailType is not needed for ${entityType}`);
      }
      break;
    case constant.entityTypes.CUSTOM_WELCOME_EMAIL:
      if (entityId) {
        return new ParamError(
          `entityObjectId is not needed for ${entityType}`
        );
      }
      break;
    case constant.entityTypes.CUSTOM_EVENT_EMAIL:
      if (!entityId) {
        return new ParamError(
          `entityObjectId is needed for ${entityType}`
        );
      }
      break;
    case constant.entityTypes.CHALLENGE: {
      if (!entityId) {
        return new ParamError(
          `entityObjectId is needed for ${entityType}`
        );
      }
      break;
    }
    case constant.entityTypes.CHALLENGE_CHECKPOINT_SUBMISSION: {
      const { participantId, checkpointId } = req.body;
      if (!participantId || !checkpointId) {
        return new ParamError(
          `participantId and checkpointId are needed for ${entityType}`
        );
      }
      break;
    }
    case constant.entityTypes.CONTENT: {
      if (!entityId) {
        return new ParamError(
          `entityObjectId is needed for ${entityType}`
        );
      }
      break;
    }
    case constant.entityTypes.ANNOUNCEMENT: {
      if (!entityId) {
        return new ParamError(
          `entityObjectId is needed for ${entityType}`
        );
      }
      break;
    }
    case constant.entityTypes.SESSION: {
      if (!entityId) {
        return new ParamError(
          `entityObjectId is needed for ${entityType}`
        );
      }
      break;
    }
    case constant.entityTypes.EVENT: {
      if (!entityId) {
        return new ParamError(
          `entityObjectId is needed for ${entityType}`
        );
      }
      break;
    }
    case constant.entityTypes.COMMUNITY: {
      if (!entityId) {
        return new ParamError(
          `entityObjectId is needed for ${entityType}`
        );
      }
      break;
    }
    case constant.entityTypes.USER: {
      if (!entityId) {
        return new ParamError(
          `entityObjectId is needed for ${entityType}`
        );
      }
      break;
    }
    default:
      return new ParamError(
        `entityType must be one of the following: ${Object.values(
          constant.entityTypes
        )}`
      );
  }
}

function getLeadingBucket(req) {
  const { entityType } = req.body;
  switch (entityType) {
    case constant.entityTypes.MAGIC_REACH:
      return 'email-images';
    case constant.entityTypes.CUSTOM_WELCOME_EMAIL:
      return 'email-images';
    case constant.entityTypes.CUSTOM_EVENT_EMAIL:
      return 'email-images';
    default:
      return 'community-images';
  }
}

function getDynamicTrailingPaths(req) {
  const { entityType } = req.body;
  switch (entityType) {
    case constant.entityTypes.CHALLENGE_CHECKPOINT_SUBMISSION: {
      const { participantId, checkpointId } = req.body;
      return ['participants', participantId, 'checkpoints', checkpointId];
    }
    default:
      return [];
  }
}

function cleanFileName(fileName) {
  // Remove leading and trailing whitespace
  let cleanedName = fileName.trim();
  // Replace spaces with underscores
  cleanedName = cleanedName.replace(/ /g, '_');
  // Remove special characters except for periods (.)
  cleanedName = cleanedName.replace(/[^\w.-]/g, '');
  // Remove leading periods and dashes
  cleanedName = cleanedName.replace(/^[-.]+/, '');
  // Ensure the filename is not empty after cleaning
  if (!cleanedName) {
    cleanedName = Date.now().toString() + '_file';
  }
  return cleanedName;
}

function getFileNameInDateFormat(req = null) {
  const base64 = req?.body?.base64;
  if (base64) {
    return crypto
      .createHash('md5')
      .update(base64)
      .digest('hex')
      .slice(0, 24);
  }
  return Date.now().toString();
}

function getFileName(req) {
  const { entityType, originalFileName, base64 } = req.body;
  switch (entityType) {
    case constant.entityTypes.CHALLENGE_CHECKPOINT_SUBMISSION: {
      const extension = originalFileName.split('.').pop();
      return `${cleanFileName(
        originalFileName
      )}_${Date.now().toString()}.${extension}`;
    }
    default:
      if (base64) {
        const matches = base64.match(
          /^data:image\/([a-zA-Z0-9]+);base64,/
        );
        let extension = '';
        if (matches && matches[1]) {
          extension = '.' + matches[1];
        }
        return (
          crypto
            .createHash('md5')
            .update(base64)
            .digest('hex')
            .slice(0, 24) + extension
        );
      }
      return Date.now().toString();
  }
}

function getFileNameFromFile(file) {
  const nameParts = file.originalname.split('.');
  const extension = nameParts[nameParts.length - 1];
  const cleanName = cleanFileName(nameParts.slice(0, -1).join('.'));
  const fileName = `${cleanName}_${Date.now().toString()}.${extension}`;
  return fileName;
}

const uploadCommunityMultiPartImage = () => {
  return multer({
    limits: { fileSize: thresholdImageSize },
    fileFilter: fileFilter,
    onError: function (err, next) {
      // eslint-disable-next-line no-param-reassign
      err.status = 500;
      next(err);
    },
    storage: multerS3({
      s3: s3,
      bucket: IMAGEASSETBUCKET,
      dest: (req, file, cb) => {
        const { entityType, entityId, mailType } = req.body;
        const path = bucketPath(
          getLeadingBucket(req),
          req.params.communityId,
          entityType,
          entityId,
          mailType,
          getDynamicTrailingPaths(req)
        );
        cb(null, path);
      },
      acl: awsAccessLevel,
      metadata: (req, file, cb) => {
        const fileName = getFileNameFromFile(file);
        cb(null, { name: fileName });
      },
      key: (req, file, cb) => {
        const { entityType, entityId, mailType } = req.body;
        const fileName = getFileNameFromFile(file);
        const key = `${bucketPath(
          getLeadingBucket(req),
          req.params.communityId,
          entityType,
          entityId,
          mailType,
          getDynamicTrailingPaths(req)
        )}/${fileName}`;
        cb(null, key);
      },
    }),
  });
};

const uploadCommunityBase64Image = async (req, res, next) => {
  const { base64, entityType } = req.body;
  const { entityId, mailType } = req.body;

  const paramError = validateParams(req);
  if (paramError) {
    return next(paramError);
  }
  // eslint-disable-next-line new-cap
  const base64Data = new Buffer.from(
    base64.replace(/^data:image\/\w+;base64,/, ''),
    'base64'
  );

  if (base64Data.length > thresholdImageSize) {
    return next(new ParamError('File size is too big'));
  }

  // Getting the file type, ie: jpeg, png or gif
  const type = base64.split(';')[0].split('/')[1];
  const acceptedTypes = ['jpg', 'jpeg', 'png', 'webp', 'gif'];

  if (!acceptedTypes.includes(type)) {
    return next(
      new ParamError(
        'Invalid file type. It has to be a jpg, jpeg, png or webp file'
      )
    );
  }

  const leadingBucket = getLeadingBucket(req);
  const dynamicTrailingPaths = getDynamicTrailingPaths(req);
  const fileName = getFileName(req);

  const params = {
    Bucket: IMAGEASSETBUCKET,
    Key: `${bucketPath(
      leadingBucket,
      req.params.communityId,
      entityType,
      entityId,
      mailType,
      dynamicTrailingPaths
    )}/${fileName}`,
    Body: base64Data,
    ACL: awsAccessLevel,
    ContentEncoding: 'base64',
    ContentType: `image/${type}`,
  };

  try {
    const { Key } = await s3.upload(params).promise();

    req.file = {
      key: Key,
    };
    console.log(req.file);
    next();
  } catch (error) {
    next(error, false);
  }
};

const uploadBase64Image = async (req, res, next) => {
  const { useCase, associatedEntityId, base64, additionalPaths } =
    req.body;

  // eslint-disable-next-line new-cap
  const base64Data = new Buffer.from(
    base64.replace(/^data:image\/\w+;base64,/, ''),
    'base64'
  );

  if (base64Data.length > thresholdImageSize) {
    return next(new ParamError('File size is too big'));
  }

  // Getting the file type, ie: jpeg, png or gif
  const type = base64.split(';')[0].split('/')[1];
  const acceptedTypes = ['jpg', 'jpeg', 'png', 'webp', 'gif'];

  if (!acceptedTypes.includes(type)) {
    return next(
      new ParamError(
        'Invalid file type. It has to be a jpg, jpeg, png or webp file'
      )
    );
  }

  const fileName = 'nio_' + getFileNameInDateFormat(req);

  const params = {
    Bucket: IMAGEASSETBUCKET,
    Key: `${getBucketPath(
      useCase,
      associatedEntityId,
      additionalPaths
    )}/${fileName}`,
    Body: base64Data,
    ACL: awsAccessLevel,
    ContentEncoding: 'base64',
    ContentType: `image/${type}`,
  };

  try {
    const { Key } = await s3.upload(params).promise();

    req.file = {
      key: Key,
    };
    console.log(req.file);
    next();
  } catch (error) {
    next(error, false);
  }
};

const uploadMultiPartImage = () => {
  return multer({
    limits: { fileSize: thresholdImageSize },
    fileFilter: fileFilter,
    onError: function (err, next) {
      // eslint-disable-next-line no-param-reassign
      err.status = 500;
      next(err);
    },
    storage: multerS3({
      s3: s3,
      bucket: IMAGEASSETBUCKET,
      dest: (req, file, cb) => {
        const { useCase, associatedEntityId, additionalPaths } = req.body;
        const path = getBucketPath(
          useCase,
          associatedEntityId,
          additionalPaths
        );
        cb(null, path);
      },
      acl: awsAccessLevel,
      metadata: (req, file, cb) => {
        const fileName = 'nio_' + getFileNameInDateFormat();
        cb(null, { name: fileName });
      },
      key: (req, file, cb) => {
        const { useCase, associatedEntityId, additionalPaths } = req.body;
        const fileName = 'nio_' + getFileNameInDateFormat();
        const key = `${getBucketPath(
          useCase,
          associatedEntityId,
          additionalPaths
        )}/${fileName}`;
        cb(null, key);
      },
    }),
  });
};

module.exports = {
  uploadCommunityMultiPartImage: uploadCommunityMultiPartImage(),
  uploadMultiPartImage: uploadMultiPartImage(),
  uploadCommunityBase64Image,
  uploadBase64Image,
  validateParams,
};
