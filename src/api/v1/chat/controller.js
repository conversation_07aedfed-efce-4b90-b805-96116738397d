const schema = require('./schema');
const service = require('../../../services/chat');
const {
  getCountryFromIP,
} = require('../../../services/countryFromIP/countryFromIP.service');
const { getUserIP } = require('../../../utils/headers.util');
const { DEFAULT_COUNTRY } = require('../../../constants/common');

async function validateAndRetrieveLearnerObjectId(
  req,
  communityObjectId,
  bypassValidation = false
) {
  const { learnerObjectId: chatVisitorLearnerObjectId, hashedToken } =
    schema.chatVisitorSchema.cast(req.query);

  // Validate hashed token for chat visitor if not login
  if (!req.user?.learner?._id) {
    if (!hashedToken && bypassValidation) {
      return null;
    }

    await service.commonService.validateChatParticipantToken({
      communityObjectId,
      hashedToken,
      learnerObjectId: chatVisitorLearnerObjectId,
    });
  }

  const learnerObjectId =
    req.user?.learner?._id ?? chatVisitorLearnerObjectId;

  return learnerObjectId;
}

exports.updateMessageSettings = async (req) => {
  const { communityId: communityObjectId } = schema.communityIdSchema.cast(
    req.params
  );

  const { isEnabled, welcomeMessage, responseTime } =
    schema.updateMessageSettingsSchema.cast(req.body);

  const result = await service.settingService.updateMessageSettings({
    communityObjectId,
    isEnabled,
    welcomeMessage,
    responseTime,
  });

  return result;
};

exports.initializeChat = async (req) => {
  const { communityId: communityObjectId } = schema.communityIdSchema.cast(
    req.params
  );

  const ip = getUserIP(req);
  const country = (await getCountryFromIP({ ip })) ?? DEFAULT_COUNTRY;

  const { messages, senderLearnerObjectId } =
    schema.initializeChatSchema.cast(req.body);

  const learnerObjectId = req.user?.learner?._id ?? senderLearnerObjectId;

  const result = await service.chatService.initializeChat({
    communityObjectId,
    messages,
    learnerObjectId,
    country,
  });

  return result;
};

exports.getMessages = async (req) => {
  const { communityId: communityObjectId, chatId: chatObjectId } =
    schema.chatIdSchema.cast(req.params);

  const { batchSize, lastObjectId, previousObjectId } =
    schema.getMessagesSchema.cast(req.query);

  const learnerObjectId = await validateAndRetrieveLearnerObjectId(
    req,
    communityObjectId
  );

  const [community, chat, isManagerCache] = await Promise.all([
    service.commonService.retrieveActiveCommunity(communityObjectId),
    service.chatService.retrieveChat({
      communityObjectId,
      chatObjectId,
      withAdditionalInfo: true,
    }),
    service.commonService.retrieveIsManagerCache(communityObjectId),
  ]);

  await service.chatParticipantService.retrieveOrCreateManagerChatParticipant(
    {
      community,
      chat,
      learnerObjectId,
      isManagerCache,
    }
  );

  const result = await service.messageService.retrieveMessages({
    chat,
    communityObjectId,
    learnerObjectId,
    batchSize,
    lastObjectId,
    previousObjectId,
    isManagerCache,
  });

  return result;
};

exports.getMessage = async (req) => {
  const {
    communityId: communityObjectId,
    chatId: chatObjectId,
    messageId: messageObjectId,
  } = schema.messageIdSchema.cast(req.params);

  const { batchSize, lastObjectId } = schema.getMessagesSchema.cast(
    req.query
  );

  const learnerObjectId = await validateAndRetrieveLearnerObjectId(
    req,
    communityObjectId
  );

  const [community, chat, isManagerCache] = await Promise.all([
    service.commonService.retrieveActiveCommunity(communityObjectId),
    service.chatService.retrieveChat({
      communityObjectId,
      chatObjectId,
      withAdditionalInfo: true,
    }),
    service.commonService.retrieveIsManagerCache(communityObjectId),
  ]);

  await service.chatParticipantService.retrieveOrCreateManagerChatParticipant(
    {
      community,
      chat,
      learnerObjectId,
      isManagerCache,
    }
  );

  const result = await service.messageService.retrieveMessage({
    chat,
    communityObjectId,
    messageObjectId,
    batchSize,
    lastObjectId,
  });

  return result;
};

exports.createMessage = async (req) => {
  const { communityId: communityObjectId, chatId: chatObjectId } =
    schema.chatIdSchema.cast(req.params);

  const learnerObjectId = await validateAndRetrieveLearnerObjectId(
    req,
    communityObjectId
  );

  const message = schema.postMessageSchema.cast(req.body);
  message.senderLearnerObjectId = learnerObjectId; // Sender based on api access token

  const [community, chat, isManagerCache] = await Promise.all([
    service.commonService.retrieveActiveCommunity(communityObjectId, true),
    service.chatService.retrieveChat({ communityObjectId, chatObjectId }),
    service.commonService.retrieveIsManagerCache(communityObjectId),
  ]);

  await service.chatParticipantService.retrieveOrCreateManagerChatParticipant(
    {
      community,
      chat,
      learnerObjectId,
      isManagerCache,
    }
  );

  const result = await service.messageService.addMessages({
    community,
    chat,
    messages: [message],
    isManagerCache,
    learnerObjectId,
  });

  return result[0];
};

exports.getAvailableChat = async (req) => {
  const { communityId: communityObjectId } = schema.communityIdSchema.cast(
    req.params
  );

  const { sourceEntityType, sourceEntityObjectId } =
    schema.availableSourceSchema.cast(req.query);

  const learnerObjectId = await validateAndRetrieveLearnerObjectId(
    req,
    communityObjectId,
    true
  );

  const result = await service.chatService.retrieveAvailableChat({
    communityObjectId,
    learnerObjectId,
    sourceEntityType,
    sourceEntityObjectId,
  });

  return result;
};

exports.migrateChat = async (req) => {
  const ip = getUserIP(req);
  const country = (await getCountryFromIP({ ip })) ?? DEFAULT_COUNTRY;

  const { communityId: communityObjectId, chatId: chatObjectId } =
    schema.chatIdSchema.cast(req.params);

  const { migrateToLearnerObjectId } = schema.migrateChatSchema.cast(
    req.body
  );

  const previousLearnerObjectId = await validateAndRetrieveLearnerObjectId(
    req,
    communityObjectId
  );

  const result = await service.chatService.migrateChat({
    communityObjectId,
    chatObjectId,
    learnerObjectId: migrateToLearnerObjectId,
    previousLearnerObjectId,
    country,
  });

  return result;
};

exports.getChatsForCmp = async (req) => {
  const { communityId: communityObjectId } = schema.communityIdSchema.cast(
    req.params
  );

  const { search, batchSize, lastObjectId, onlyUnread } =
    schema.batchSchema.cast(req.query);

  const result = await service.chatService.retrieveChatsForCmp({
    communityObjectId,
    search,
    batchSize,
    lastObjectId,
    onlyUnread: onlyUnread === 'true',
  });

  return result;
};

exports.hasUnreadChat = async (req) => {
  const { communityId: communityObjectId } = schema.communityIdSchema.cast(
    req.params
  );

  const result = await service.chatService.hasUnreadChat({
    communityObjectId,
  });

  return { hasUnreadMessage: result };
};

exports.hasNewUnreadChat = async (req) => {
  const { communityId: communityObjectId } = schema.communityIdSchema.cast(
    req.params
  );

  const { lastObjectId } = schema.lastObjectIdSchema.cast(req.query);

  const result = await service.chatService.hasNewUnreadChat({
    communityObjectId,
    lastObjectId,
  });

  return { hasNewUnreadMessage: result };
};

exports.markMessagesAsRead = async (req) => {
  const { communityId: communityObjectId, chatId: chatObjectId } =
    schema.chatIdSchema.cast(req.params);

  const { lastReadMessageObjectId: messageObjectId } =
    schema.readMarkerSchema.cast(req.body);

  const learnerObjectId = await validateAndRetrieveLearnerObjectId(
    req,
    communityObjectId
  );

  const [community, chat, isManagerCache] = await Promise.all([
    service.commonService.retrieveActiveCommunity(communityObjectId, true),
    service.chatService.retrieveChat({ communityObjectId, chatObjectId }),
    service.commonService.retrieveIsManagerCache(communityObjectId),
  ]);

  await service.chatParticipantService.retrieveOrCreateManagerChatParticipant(
    {
      community,
      chat,
      learnerObjectId,
      isManagerCache,
    }
  );

  await service.chatMessageRecipientStatusService.markMessagesAsRead({
    chatObjectId,
    communityObjectId,
    recipientLearnerObjectId: learnerObjectId,
    messageObjectId,
  });

  const result =
    await service.chatMessageRecipientStatusService.hasUnreadMessage({
      chatObjectId,
      communityObjectId,
      recipientLearnerObjectId: learnerObjectId,
    });

  return { hasUnreadMessage: result };
};

exports.patchChatStatus = async (req) => {
  const { communityId: communityObjectId, chatId: chatObjectId } =
    schema.chatIdSchema.cast(req.params);

  const { status } = schema.updateChatStatusSchema.cast(req.body);

  const result = await service.chatService.updateChatStatus({
    communityObjectId,
    chatObjectId,
    status,
  });

  return result;
};
