const schema = require('./schema');
const service = require('../../../../services/chat');

exports.getChats = async (req) => {
  const { batchSize, lastObjectId, onlyNonManager } =
    schema.getChatSchema.cast(req.query);

  const learnerObjectId = req.user.learner._id;

  const result = await service.chatService.retrieveChatsForMp({
    learnerObjectId,
    batchSize,
    lastObjectId,
    onlyNonManager: onlyNonManager === 'true',
  });

  return result;
};

exports.hasUnreadChat = async (req) => {
  const learnerObjectId = req.user.learner._id;

  const result =
    await service.chatMessageRecipientStatusService.hasUnreadMessageRecipient(
      { learnerObjectId }
    );

  return { hasUnreadMessage: result };
};
