const { Router } = require('express');
const schema = require('./schema');
const controller = require('./controller');
const tokenValidator = require('../../../../validations/token.validation');
const userValidation = require('../../../../validations/user.validation');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../../middleware/request.middleware');
const {
  MODULE_TYPE,
  rateLimitMiddleware,
} = require('../../../../utils/rateLimit.util');

const { handlerWrapper } = require('../../../../utils/request.util');

const router = Router({ mergeParams: true });

router.route('/').get(
  postRoutePreHandlerMiddleware,
  rateLimitMiddleware({
    module: MODULE_TYPE.CHAT,
  }),
  tokenValidator(),
  userValidation,
  handlerWrapper({
    handler: controller.getChats,
    requestValidators: {
      query: schema.getChatSchema,
    },
  })
);

router.route('/unread').get(
  postRoutePreHandlerMiddleware,
  rateLimitMiddleware({
    module: MODULE_TYPE.CHAT,
  }),
  tokenValidator(),
  userValidation,
  handlerWrapper({
    handler: controller.hasUnreadChat,
  })
);

module.exports = router;
