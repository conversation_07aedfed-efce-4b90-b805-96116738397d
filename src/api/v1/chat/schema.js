const yup = require('yup');
const {
  MESSAGE_TYPE,
  CHAT_STATUS,
} = require('../../../services/chat/constants');

exports.communityIdSchema = yup.object().shape({
  communityId: yup.string().trim().required(),
});

exports.chatIdSchema = yup.object().shape({
  communityId: yup.string().trim().required(),
  chatId: yup.string().trim().required(),
});

exports.messageIdSchema = yup.object().shape({
  communityId: yup.string().trim().required(),
  chatId: yup.string().trim().required(),
  messageId: yup.string().trim().required(),
});

const chatVisitorFields = {
  email: yup.string().lowercase().trim().notRequired(),
  learnerObjectId: yup.string().notRequired(),
  hashedToken: yup.string().notRequired(),
};

exports.chatVisitorSchema = yup.object().shape(chatVisitorFields);

exports.availableSourceSchema = yup.object().shape({
  sourceEntityType: yup.string().trim().notRequired(),
  sourceEntityObjectId: yup.string().trim().notRequired(),
  ...chatVisitorFields,
});

exports.migrateChatSchema = yup.object().shape({
  migrateToLearnerObjectId: yup.string().trim().required(),
});

exports.getMessagesSchema = yup.object().shape({
  batchSize: yup.number().notRequired(),
  lastObjectId: yup.string().notRequired(),
  previousObjectId: yup.string().notRequired(),
  ...chatVisitorFields,
});

exports.updateMessageSettingsSchema = yup.object().shape({
  isEnabled: yup.boolean().notRequired(),
  welcomeMessage: yup.string().notRequired().trim(),
  responseTime: yup
    .object()
    .shape({
      frequencyCount: yup.number().notRequired(),
      frequencyUnit: yup.string().notRequired().trim(),
    })
    .notRequired()
    .nullable()
    .default(null),
});

const mentionProduct = yup.object().shape({
  entityObjectId: yup.string().trim().required(),
  entityType: yup.string().trim().required(),
});

const attachment = yup.object().shape({
  url: yup.string().trim().required(),
  type: yup.string().trim().required(),
  name: yup.string().trim().required(),
});

const messageSchema = yup.object().shape({
  message: yup.string().when('messageType', {
    is: MESSAGE_TYPE.TEXT,
    then: yup.string().trim().required(),
    otherwise: yup.string().trim().notRequired().default(''),
  }),
  messageType: yup
    .string()
    .trim()
    .oneOf(Object.values(MESSAGE_TYPE))
    .required(),
  attachments: yup.array().when('messageType', {
    is: (val) =>
      [MESSAGE_TYPE.IMAGE, MESSAGE_TYPE.VIDEO, MESSAGE_TYPE.FILE].includes(
        val
      ),
    then: yup.array(attachment).notRequired(),
    otherwise: yup.array(attachment).notRequired().nullable().default([]),
  }),
  mentionProduct: yup.object().when('messageType', {
    is: MESSAGE_TYPE.MENTION_PRODUCT,
    then: mentionProduct.required(),
    otherwise: mentionProduct.notRequired().nullable().default(null),
  }),
  messageObjectIds: yup.array().when('messageType', {
    is: MESSAGE_TYPE.TICKET,
    then: yup.array().required(),
    otherwise: yup.array().notRequired().nullable().default(null),
  }),
});

exports.initializeChatSchema = yup.object().shape({
  messages: yup.array(messageSchema).notRequired(),
  senderLearnerObjectId: yup.string().trim().notRequired(),
});

exports.postMessageSchema = messageSchema;

exports.batchSchema = yup.object().shape({
  search: yup.string().trim().notRequired(),
  batchSize: yup.number().notRequired().integer(),
  lastObjectId: yup.string().trim().notRequired(),
  onlyUnread: yup.string().trim().notRequired(),
});

exports.lastObjectIdSchema = yup.object().shape({
  lastObjectId: yup.string().trim().required(),
});

exports.readMarkerSchema = yup.object().shape({
  lastReadMessageObjectId: yup.string().trim().required(),
});

exports.updateChatStatusSchema = yup.object().shape({
  status: yup.string().trim().required().oneOf(Object.values(CHAT_STATUS)),
});
