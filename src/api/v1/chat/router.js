const { Router } = require('express');
const schema = require('./schema');
const controller = require('./controller');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');
const payloadSignatureValidator = require('../../../validations/payloadSignature.validation');
const tokenValidatorWithoutError = require('../../../validations/tokenNoError.validation');
const {
  managerCommunityValidator,
} = require('../../../communitiesAPI/validations/community.validation');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const {
  MODULE_TYPE,
  rateLimitMiddleware,
} = require('../../../utils/rateLimit.util');

const { handlerWrapper } = require('../../../utils/request.util');

const router = Router({ mergeParams: true });

router.route('/settings').patch(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  handlerWrapper({
    handler: controller.updateMessageSettings,
    requestValidators: {
      body: schema.updateMessageSettingsSchema,
      params: schema.communityIdSchema,
    },
  })
);

router.route('/unread').get(
  postRoutePreHandlerMiddleware,
  rateLimitMiddleware({
    module: MODULE_TYPE.CHAT,
  }),
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  handlerWrapper({
    handler: controller.hasUnreadChat,
    requestValidators: {
      params: schema.communityIdSchema,
    },
  })
);

router.route('/new').get(
  postRoutePreHandlerMiddleware,
  rateLimitMiddleware({
    module: MODULE_TYPE.CHAT,
  }),
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  handlerWrapper({
    handler: controller.hasNewUnreadChat,
    requestValidators: {
      params: schema.communityIdSchema,
      query: schema.lastObjectIdSchema,
    },
  })
);

router.route('/available').get(
  postRoutePreHandlerMiddleware,
  rateLimitMiddleware({
    module: MODULE_TYPE.CHAT,
  }),
  tokenValidatorWithoutError,
  handlerWrapper({
    handler: controller.getAvailableChat,
    requestValidators: {
      params: schema.communityIdSchema,
      query: schema.availableSourceSchema,
    },
  })
);

router
  .route('/')
  .get(
    postRoutePreHandlerMiddleware,
    rateLimitMiddleware({
      module: MODULE_TYPE.CHAT,
    }),
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    handlerWrapper({
      handler: controller.getChatsForCmp,
      requestValidators: {
        query: schema.batchSchema,
        params: schema.communityIdSchema,
      },
    })
  )
  .put(
    postRoutePreHandlerMiddleware,
    rateLimitMiddleware({
      module: MODULE_TYPE.CHAT,
    }),
    payloadSignatureValidator,
    tokenValidatorWithoutError,
    handlerWrapper({
      handler: controller.initializeChat,
      requestValidators: {
        body: schema.initializeChatSchema,
        params: schema.communityIdSchema,
      },
    })
  );

router.route('/:chatId/messages/:messageId').get(
  postRoutePreHandlerMiddleware,
  rateLimitMiddleware({
    module: MODULE_TYPE.CHAT,
  }),
  tokenValidatorWithoutError,
  handlerWrapper({
    handler: controller.getMessage,
    requestValidators: {
      query: schema.getMessagesSchema,
      params: schema.messageIdSchema,
    },
  })
);

router.route('/:chatId/migrate').post(
  postRoutePreHandlerMiddleware,
  rateLimitMiddleware({
    module: MODULE_TYPE.CHAT,
  }),
  payloadSignatureValidator,
  tokenValidatorWithoutError,
  handlerWrapper({
    handler: controller.migrateChat,
    requestValidators: {
      params: schema.chatIdSchema,
      query: schema.chatVisitorSchema,
      body: schema.migrateChatSchema,
    },
  })
);

router
  .route('/:chatId/messages')
  .get(
    postRoutePreHandlerMiddleware,
    rateLimitMiddleware({
      module: MODULE_TYPE.CHAT,
    }),
    tokenValidatorWithoutError,
    handlerWrapper({
      handler: controller.getMessages,
      requestValidators: {
        query: schema.getMessagesSchema,
        params: schema.chatIdSchema,
      },
    })
  )
  .post(
    postRoutePreHandlerMiddleware,
    rateLimitMiddleware({
      module: MODULE_TYPE.CHAT,
    }),
    payloadSignatureValidator,
    tokenValidatorWithoutError,
    handlerWrapper({
      handler: controller.createMessage,
      requestValidators: {
        body: schema.postMessageSchema,
        query: schema.chatVisitorSchema,
        params: schema.chatIdSchema,
      },
    })
  );

router.route('/:chatId/read-marker').post(
  postRoutePreHandlerMiddleware,
  rateLimitMiddleware({
    module: MODULE_TYPE.CHAT,
  }),
  tokenValidatorWithoutError,
  handlerWrapper({
    handler: controller.markMessagesAsRead,
    requestValidators: {
      body: schema.readMarkerSchema,
      params: schema.chatIdSchema,
    },
  })
);

router.route('/:chatId/status').patch(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  handlerWrapper({
    handler: controller.patchChatStatus,
    requestValidators: {
      body: schema.updateChatStatusSchema,
      params: schema.chatIdSchema,
    },
  })
);

module.exports = router;
