const schema = require('./schema');
const logger = require('../../../services/logger.service');
const service = require('../../../services/campaign');

exports.claimFiveDollarBonus = async (req, res, next) => {
  try {
    const { communityId } = await schema.communityIdSchema.cast(
      req.params
    );

    const response = await service.claimFiveDollarBonus({
      communityId,
    });

    return res.json({ data: response });
  } catch (err) {
    logger.error(
      'claimFiveDollarBonus failed due to',
      err.message,
      err.stack
    );
    return next(err);
  }
};

exports.sendFiveDollarBonusOneMonthReminder = async (req, res, next) => {
  try {
    const { communityObjectId, jobObjectId } =
      await schema.sendEmailSchema.cast(req.body);

    const response = await service.sendCampaignEmailReminder({
      communityObjectId,
      jobObjectId,
    });

    return res.json({ data: response });
  } catch (err) {
    logger.error(
      'sendFiveDollarBonusOneMonthReminder failed due to',
      err.message,
      err.stack
    );
    return next(err);
  }
};
