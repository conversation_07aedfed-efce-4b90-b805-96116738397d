const schema = require('./schema');
const controller = require('./controller');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');
const apiKeyValidator = require('../../../validations/apiKey.validation');
const {
  managerCommunityValidator,
} = require('../../../communitiesAPI/validations/community.validation');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const {
  validateAll,
} = require('../../../middleware/validator.middleware');

const setupRouter = async function (router) {
  router.route('/communities/:communityId/five-dollar').post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    validateAll([
      {
        schema: schema.communityIdSchema,
        location: 'params',
      },
    ]),
    controller.claimFiveDollarBonus
  );

  router.route('/campaign/five-dollar/send-email').post(
    postRoutePreHandlerMiddleware,
    apiKeyValidator,
    validateAll([
      {
        schema: schema.sendEmailSchema,
        location: 'body',
      },
    ]),
    controller.sendFiveDollarBonusOneMonthReminder
  );
};

module.exports = {
  setupRouter,
};
