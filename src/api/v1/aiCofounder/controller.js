const schema = require('./schema');
const { chatService } = require('../../../services/chat');
const aiCofounderService = require('../../../services/aiCofounder');
const aiCofounderCommonService = require('../../../services/aiCofounder/common.service');
const aiCofounderMessageService = require('../../../services/aiCofounder/message.service');
const aiCofounderChatService = require('../../../services/aiCofounder/chat.service');
const { moderateText } = require('../../../clients/openai.client');
const logger = require('../../../services/logger.service');
const connectionTracker = require('../../../monitoring/connectionTracker');
const { getLanguagePreference } = require('../../../utils/headers.util');

async function validateMessage(message) {
  const moderationResult = await moderateText(message);

  if (moderationResult?.results?.some((result) => result.flagged)) {
    throw new Error(
      'Your message was flagged due to inappropriate content. Please revise and try again.'
    );
  }
}

exports.createChat = async (req) => {
  const { communityId: communityObjectId } = schema.communityIdSchema.cast(
    req.params
  );

  const { message } = schema.createChatSchema.cast(req.body);

  const learnerObjectId = req.user.learner._id;

  const messageCountInfo =
    await aiCofounderMessageService.retrieveMessageCountInfo({
      communityObjectId,
    });

  aiCofounderMessageService.validateMessageCountWithinLimit({
    messageCountInfo,
  });

  await validateMessage(message);

  const result = await chatService.createChat({
    communityObjectId,
    message,
    learnerObjectId,
  });

  return result;
};

exports.getMessages = async (req) => {
  const { communityId: communityObjectId, chatId: chatObjectId } =
    schema.chatIdSchema.cast(req.params);

  const { batchSize, previousObjectId } = schema.getMessagesSchema.cast(
    req.query
  );

  const result = await aiCofounderMessageService.retrieveMessages({
    communityObjectId,
    chatObjectId,
    batchSize,
    previousObjectId,
  });

  return result;
};

exports.sendMessage = async (req, res) => {
  const { communityId: communityObjectId, chatId: chatObjectId } =
    schema.chatIdSchema.cast(req.params);

  const {
    message,
    attachments,
    intentType,
    responseId,
    templateObjectId,
    actionType,
  } = schema.postMessageSchema.cast(req.body);

  const learnerObjectId = req.user.learner._id;

  res.setHeader('Content-Type', 'text/event-stream');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Connection', 'keep-alive');
  res.flushHeaders();

  connectionTracker.trackStreamingConnection(res);

  let responseEnded = false;

  // Send keep-alive comment every 15 seconds
  const keepAliveInterval = setInterval(() => {
    res.write(': keep-alive\n\n');
  }, 15 * 1000); // 15 sec

  // eslint-disable-next-line no-undef
  const abortController = new AbortController();

  // To prevent double res.end() being called due to onDone and onError callback
  const safeEnd = () => {
    clearInterval(keepAliveInterval);
    abortController.abort();
    if (!responseEnded) {
      responseEnded = true;
      res.end();
    }
  };

  req.on('close', () => {
    logger.info(`sendMessage: Client disconnected`);
    safeEnd();
  });

  req.on('aborted', () => {
    logger.info(`sendMessage: Client aborted`);
    safeEnd();
  });

  req.on('error', (err) => {
    logger.info(`sendMessage: Client error ${err.message}`);
    safeEnd();
  });

  res.on('close', () => {
    logger.info(`sendMessage: Client disconnected`);
    safeEnd();
  });

  res.on('error', (err) => {
    logger.info(`sendMessage: Client error ${err.message}`);
    safeEnd();
  });

  try {
    const [community, chat] = await Promise.all([
      aiCofounderCommonService.retrieveActiveCommunity(communityObjectId),
      chatService.retrieveChat({
        communityObjectId,
        chatObjectId,
      }),
      validateMessage(message),
    ]);

    await aiCofounderService.sendMessage({
      community,
      chat,
      message,
      attachments,
      intentType,
      responseId,
      learnerObjectId,
      templateObjectId,
      actionType,
      abortController,
      onData: (data) => {
        res.write(`data: ${JSON.stringify(data)}\n\n`);
      },
      onDone: async (data) => {
        const {
          fullText,
          usage,
          templateObjectId: newTemplateObjectId,
          functionCallUsage,
          ...rest
        } = data;

        const isFirstMessage = !responseId;

        try {
          const [chatAiMessage, messageCountInfo] = await Promise.all([
            aiCofounderService.addAiMessage({
              community,
              chat,
              message: fullText,
              learnerObjectId,
              usage,
              functionCallUsage,
              responseId: rest.responseId,
              templateObjectId: newTemplateObjectId,
              possibleActions: rest.possibleActions,
              messageIntent: rest.messageIntent,
            }),
            aiCofounderMessageService.incrementMessageCount({
              community,
            }),
            isFirstMessage // Only update chat title for the first message
              ? aiCofounderService.updateChatTitleWithSummary({
                  community,
                  chat,
                  responseId: rest.responseId,
                })
              : null,
          ]);

          rest.messageObjectId = chatAiMessage._id;
          rest.messageCountInfo = messageCountInfo;
        } catch (error) {
          logger.error(`addAiMessage: ${error.message}, ${error.stack}`);
          res.write(
            `data: ${JSON.stringify({
              type: 'error',
              text: error.message,
            })}\n\n`
          );
          safeEnd();
        }

        res.write(`data: ${JSON.stringify(rest)}\n\n`);
        safeEnd();
      },
      onError: (error) => {
        res.write(`data: ${JSON.stringify(error)}\n\n`);
        safeEnd();
      },
    });
  } catch (error) {
    logger.error(`sendMessage: ${error.message}, ${error.stack}`);
    res.write(
      `data: ${JSON.stringify({ type: 'error', text: error.message })}\n\n`
    );
    safeEnd();
  }
};

exports.getChats = async (req) => {
  const { communityId: communityObjectId } = schema.communityIdSchema.cast(
    req.params
  );

  const { batchSize, lastObjectId } = schema.batchSchema.cast(req.query);

  const messageCountInfo =
    await aiCofounderMessageService.retrieveMessageCountInfo({
      communityObjectId,
    });

  const result = await aiCofounderChatService.retrieveChats({
    communityObjectId,
    batchSize,
    lastObjectId,
    messageCountInfo,
  });

  return result;
};

exports.retrieveTemplateViaVersion = async (req) => {
  const { communityId: communityObjectId, chatId: chatObjectId } =
    schema.chatIdSchema.cast(req.params);

  const { version } = schema.templateVersionSchema.cast(req.query);

  const result = await aiCofounderService.retrieveTemplateViaVersion({
    communityObjectId,
    chatObjectId,
    version,
  });

  return result;
};

exports.addNewTemplateVersion = async (req) => {
  const { communityId: communityObjectId, chatId: chatObjectId } =
    schema.chatIdSchema.cast(req.params);

  const { version } = schema.templateVersionSchema.cast(req.body);

  const [community, chat] = await Promise.all([
    aiCofounderCommonService.retrieveActiveCommunity(communityObjectId),
    chatService.retrieveChat({
      communityObjectId,
      chatObjectId,
    }),
  ]);

  const learnerObjectId = req.user.learner._id;

  const result = await aiCofounderService.addNewTemplateVersion({
    community,
    chat,
    version,
    learnerObjectId,
  });

  return result;
};

exports.updateTemplate = async (req) => {
  const { communityId: communityObjectId, templateId: templateObjectId } =
    schema.templateIdSchema.cast(req.params);

  const { thumbnailImgSrc, metadata, pricingConfig } =
    schema.updateTemplateSchema.cast(req.body);

  const result = await aiCofounderService.updateTemplate({
    communityObjectId,
    templateObjectId,
    thumbnailImgSrc,
    metadata,
    pricingConfig,
  });

  return result;
};

exports.shuffleInitialPrompt = async (req) => {
  const { communityId: communityObjectId } = schema.communityIdSchema.cast(
    req.params
  );

  const languagePreference = getLanguagePreference(req);

  const result = await aiCofounderService.shuffleInitialPrompt({
    communityObjectId,
    languagePreference,
  });

  return result;
};
