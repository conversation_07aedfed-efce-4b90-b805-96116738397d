const { Router } = require('express');
const schema = require('./schema');
const controller = require('./controller');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');
const payloadSignatureValidator = require('../../../validations/payloadSignature.validation');
const {
  managerCommunityValidator,
} = require('../../../communitiesAPI/validations/community.validation');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const {
  MODULE_TYPE,
  rateLimitMiddleware,
} = require('../../../utils/rateLimit.util');
const {
  validateAll,
} = require('../../../middleware/validator.middleware');

const { handlerWrapper } = require('../../../utils/request.util');

const router = Router({ mergeParams: true });

router
  .route('/chats')
  .get(
    postRoutePreHandlerMiddleware,
    rateLimitMiddleware({
      module: MODULE_TYPE.CHAT,
    }),
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    handlerWrapper({
      handler: controller.getChats,
      requestValidators: {
        query: schema.batchSchema,
        params: schema.communityIdSchema,
      },
    })
  )
  .post(
    postRoutePreHandlerMiddleware,
    rateLimitMiddleware({
      module: MODULE_TYPE.CHAT,
    }),
    payloadSignatureValidator,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    handlerWrapper({
      handler: controller.createChat,
      requestValidators: {
        body: schema.createChatSchema,
        params: schema.communityIdSchema,
      },
    })
  );

router
  .route('/chats/:chatId/messages')
  .get(
    postRoutePreHandlerMiddleware,
    rateLimitMiddleware({
      module: MODULE_TYPE.CHAT,
    }),
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    handlerWrapper({
      handler: controller.getMessages,
      requestValidators: {
        query: schema.getMessagesSchema,
        params: schema.chatIdSchema,
      },
    })
  )
  .post(
    postRoutePreHandlerMiddleware,
    rateLimitMiddleware({
      module: MODULE_TYPE.CHAT,
    }),
    payloadSignatureValidator,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    validateAll([
      { schema: schema.postMessageSchema, location: 'body' },
      {
        schema: schema.chatIdSchema,
        location: 'params',
      },
    ]),
    controller.sendMessage
  );

router
  .route('/chats/:chatId/templates')
  .get(
    postRoutePreHandlerMiddleware,
    rateLimitMiddleware({
      module: MODULE_TYPE.CHAT,
    }),
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    handlerWrapper({
      handler: controller.retrieveTemplateViaVersion,
      requestValidators: {
        params: schema.chatIdSchema,
        query: schema.templateVersionSchema,
      },
    })
  )
  .post(
    postRoutePreHandlerMiddleware,
    rateLimitMiddleware({
      module: MODULE_TYPE.CHAT,
    }),
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    handlerWrapper({
      handler: controller.addNewTemplateVersion,
      requestValidators: {
        params: schema.chatIdSchema,
        body: schema.templateVersionSchema,
      },
    })
  );

router.route('/prompts/shuffle').post(
  postRoutePreHandlerMiddleware,
  rateLimitMiddleware({
    module: MODULE_TYPE.CHAT,
  }),
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  handlerWrapper({
    handler: controller.shuffleInitialPrompt,
    requestValidators: {
      params: schema.communityIdSchema,
    },
  })
);

router.route('/templates/:templateId').put(
  postRoutePreHandlerMiddleware,
  rateLimitMiddleware({
    module: MODULE_TYPE.CHAT,
  }),
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  handlerWrapper({
    handler: controller.updateTemplate,
    requestValidators: {
      body: schema.updateTemplateSchema,
      params: schema.templateIdSchema,
    },
  })
);

module.exports = router;
