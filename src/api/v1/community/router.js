const { Router } = require('express');
const schema = require('./schema');
const controller = require('./controller');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const {
  validateAll,
} = require('../../../middleware/validator.middleware');
const copsTokenMiddleware = require('../../../middleware/cops-token.middleware');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');
const apiKeyValidation = require('../../../validations/apiKey.validation');
const {
  managerCommunityValidator,
} = require('../../../communitiesAPI/validations/community.validation');
const {
  MODULE_TYPE,
  rateLimitMiddleware,
} = require('../../../utils/rateLimit.util');
const { handlerWrapper } = require('../../../utils/request.util');

// prefix: api/v1/communities/:communityId
const router = Router({ mergeParams: true });

router.route('/owner/cops').patch(
  postRoutePreHandlerMiddleware,
  copsTokenMiddleware,
  validateAll([
    {
      schema: schema.communityIdSchema,
      location: 'params',
    },
    {
      schema: schema.changeCommunityOwnerLearnerSchema,
      location: 'body',
    },
  ]),
  controller.changeCommunityOwner
);

router
  .route('/processing-fee-preference')
  .patch(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    controller.changeProcessingFeesPreference
  )
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    controller.checkIfProcessingFeesPreferenceSet
  );

router
  .route('/get-payment-fee-rate')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    controller.getProcessFeeRate
  );

router.route('/currency').patch(
  postRoutePreHandlerMiddleware,
  tokenValidator(),
  userValidation,
  managerCommunityValidator,
  validateAll([
    {
      schema: schema.communityIdSchema,
      location: 'params',
    },
    {
      schema: schema.patchCurrencySchema,
      location: 'body',
    },
  ]),
  controller.changeCurrency
);

router.route('/currency/cops').patch(
  postRoutePreHandlerMiddleware,
  copsTokenMiddleware,
  validateAll([
    {
      schema: schema.communityIdSchema,
      location: 'params',
    },
    {
      schema: schema.patchCurrencySchema,
      location: 'body',
    },
  ]),
  controller.changeCurrency
);

router.route('/access').delete(
  postRoutePreHandlerMiddleware,
  apiKeyValidation,
  validateAll([
    {
      schema: schema.communityIdSchema,
      location: 'params',
    },
    {
      schema: schema.deleteAccessSchema,
      location: 'query',
    },
  ]),
  controller.removeAccess
);

router.route('/access/cops').delete(
  postRoutePreHandlerMiddleware,
  copsTokenMiddleware,
  validateAll([
    {
      schema: schema.communityIdSchema,
      location: 'params',
    },
    {
      schema: schema.deleteAccessSchema,
      location: 'query',
    },
  ]),
  controller.removeAccessViaCops
);

router.route('/community-products').get(
  postRoutePreHandlerMiddleware,
  rateLimitMiddleware({
    module: MODULE_TYPE.PUBLIC,
  }),
  controller.getCommunityProducts
);

router.route('/fee/cops').patch(
  postRoutePreHandlerMiddleware,
  copsTokenMiddleware,
  validateAll([
    {
      schema: schema.communityIdSchema,
      location: 'params',
    },
    {
      schema: schema.updateFeeSchema,
      location: 'body',
    },
  ]),
  controller.updatePayoutFeeConfig
);

router.route('/free-enrolment/cops').post(
  postRoutePreHandlerMiddleware,
  copsTokenMiddleware,
  handlerWrapper({
    handler: controller.addCommunityFreeEnrolment,
    requestValidators: {
      params: schema.communityIdSchema,
      body: schema.addCommunityFreeEnrolment,
    },
  })
);

router.route('/manager/cops').post(
  postRoutePreHandlerMiddleware,
  copsTokenMiddleware,
  handlerWrapper({
    handler: controller.addCommunityManager,
    requestValidators: {
      params: schema.communityIdSchema,
      body: schema.addCommunityManager,
    },
  })
);

module.exports = router;
