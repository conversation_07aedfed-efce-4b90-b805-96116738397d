const schema = require('./schema');
const logger = require('../../../services/logger.service');
const service = require('../../../services/community');

exports.changeCommunityOwner = async (req, res, next) => {
  try {
    const { communityId } = schema.communityIdSchema.cast(req.params);

    const { email } = schema.communityIdSchema.cast(req.body);

    await service.changeCommunityOwnerService.changeCommunityOwner({
      communityId,
      newCommunityOwnerEmail: email,
    });

    return res.json({ data: 'success' });
  } catch (err) {
    logger.error('getAnnouncements failed due to', err.message, err.stack);
    return next(err);
  }
};

exports.changeCurrency = async (req, res, next) => {
  try {
    const { communityId } = schema.communityIdSchema.cast(req.params);

    const { newCurrency } = schema.patchCurrencySchema.cast(req.body);

    await service.changeCurrencyService.changeCommunityBaseCurrency({
      communityId,
      newCurrency,
    });

    return res.json({ data: 'success' });
  } catch (err) {
    logger.error('changeCurrency failed due to', err.message, err.stack);
    return next(err);
  }
};

exports.changeProcessingFeesPreference = async (req, res, next) => {
  try {
    const { communityId } = schema.communityIdSchema.cast(req.params);

    const { passOnTakeRate } = req.body;
    const response =
      await service.processingFeesPreference.changePassOnToggle(
        communityId,
        passOnTakeRate
      );
    return res.status(200).json({ data: response });
  } catch (error) {
    logger.error(
      'changeProcessingFeesPreference failed due to',
      error.message,
      error.stack
    );
    return next(error);
  }
};

exports.updatePayoutFeeConfig = async (req, res, next) => {
  try {
    const { communityId } = schema.communityIdSchema.cast(req.params);

    const {
      startDate,
      endDate,
      freePlanFee,
      proPlanFee,
      platinumPlanFee,
      updatePayoutFeeConfigByPlanType,
    } = schema.updateFeeSchema.cast(req.body);

    const operator = req.user.email;
    await service.updatePayoutFeeConfigService.updatePayoutFeeConfig({
      communityObjectId: communityId,
      startDate,
      endDate,
      freePlanFee,
      proPlanFee,
      platinumPlanFee,
      operator,
      updatePayoutFeeConfigByPlanType,
    });

    return res.json({ data: 'success' });
  } catch (err) {
    logger.error(
      'updatePayoutFeeConfig failed due to',
      err.message,
      err.stack
    );
    return next(err);
  }
};

exports.checkIfProcessingFeesPreferenceSet = async (req, res, next) => {
  try {
    const { communityId } = schema.communityIdSchema.cast(req.params);

    const response =
      await service.processingFeesPreference.checkIfProcessingFeesPreferenceSetService(
        communityId
      );

    return res.status(200).json(response);
  } catch (error) {
    logger.error(
      'checkIfProcessingFeesPreferenceSet failed due to',
      error.message,
      error.stack
    );
    return next(error);
  }
};

exports.getProcessFeeRate = async (req, res, next) => {
  try {
    const { communityId } = schema.communityIdSchema.cast(req.params);
    const { purchaseType } = req.query;

    const response =
      await service.getPaymentFeeRateService.getPaymentFeeRate({
        communityId,
        purchaseType,
      });

    return res.status(200).json({ data: response });
  } catch (error) {
    logger.error(
      'checkIfProcessingFeesPreferenceSet failed due to',
      error.message,
      error.stack
    );
    return next(error);
  }
};

exports.removeAccess = async (req, res, next) => {
  try {
    const { communityId: communityObjectId } =
      schema.communityIdSchema.cast(req.params);

    const {
      entityType,
      entityObjectId,
      purchasedId,
      learnerObjectId,
      accessRemovalAtEnd,
      removalReason,
    } = schema.deleteAccessSchema.cast(req.query);

    await service.accessService.removeAccess({
      entityType,
      entityObjectId,
      purchasedId,
      learnerObjectId,
      communityObjectId,
      accessRemovalAtEnd,
      removalReason,
    });

    return res.json({ data: 'success' });
  } catch (err) {
    logger.error('removeAccess failed due to', err.message, err.stack);
    return next(err);
  }
};

exports.removeAccessViaCops = async (req, res, next) => {
  try {
    const { communityId: communityObjectId } =
      schema.communityIdSchema.cast(req.params);

    const {
      entityType,
      entityObjectId,
      purchasedId,
      learnerObjectId,
      accessRemovalAtEnd,
    } = schema.deleteAccessSchema.cast(req.query);

    await service.accessService.removeAccess({
      entityType,
      entityObjectId,
      purchasedId,
      learnerObjectId,
      communityObjectId,
      accessRemovalAtEnd,
      removalReason: 'Access removed via cops',
    });

    return res.json({ data: 'success' });
  } catch (err) {
    logger.error(
      'removeAccessViaCops failed due to',
      err.message,
      err.stack
    );
    return next(err);
  }
};

exports.getCommunityProducts = async (req, res, next) => {
  try {
    const { communityId: communityObjectId } =
      schema.communityIdSchema.cast(req.params);

    const { search, pageNo, pageSize, source } =
      schema.getCommunityProductsSchema.cast(req.query);

    const result = await service.productService.retrieveProducts({
      communityObjectId,
      search,
      pageNo,
      pageSize,
      source,
    });

    return res.json({ data: result });
  } catch (err) {
    logger.error(
      'getCommunityProducts failed due to',
      err.message,
      err.stack
    );
    return next(err);
  }
};

exports.addCommunityFreeEnrolment = async (req) => {
  const { communityId } = schema.communityIdSchema.cast(req.params);
  const { email, toSendEmail = true } =
    schema.addCommunityFreeEnrolment.cast(req.body);

  const result = await service.manageSubscriptions.addFreeSubscriber({
    email,
    communityId,
    toSendEmail,
  });

  return result;
};

exports.addCommunityManager = async (req) => {
  const { communityId } = schema.communityIdSchema.cast(req.params);
  const { email } = schema.addCommunityManager.cast(req.body);

  const result = await service.manageSubscriptions.addCommunityManager({
    email,
    communityId,
  });

  return result;
};
