const yup = require('yup');

exports.communityIdSchema = yup.object().shape({
  communityId: yup.string().required(),
});

exports.changeCommunityOwnerLearnerSchema = yup.object().shape({
  email: yup.string().email().lowercase().trim().required(),
});

exports.patchCurrencySchema = yup.object().shape({
  newCurrency: yup.string().uppercase().trim().required(),
});

exports.deleteAccessSchema = yup.object().shape({
  entityType: yup.string().trim().required(),
  entityObjectId: yup.string().trim().required(),
  purchasedId: yup.string().trim().required(),
  learnerObjectId: yup.string().trim().required(),
  accessRemovalAtEnd: yup.boolean().notRequired().nullable(),
  removalReason: yup.string().trim().notRequired().nullable(),
});

exports.getCommunityProductsSchema = yup.object().shape({
  search: yup.string().uppercase().notRequired().trim(),
  pageSize: yup.number().default(20).required().integer(),
  pageNo: yup.number().default(1).required().integer(),
  source: yup.string().trim().notRequired().nullable().default(null),
});

const feeDetailsSchema = {
  gatewayFeeInPercentage: yup.number().required(),
  processingFee: yup.number().required(),
  minGatewayFee: yup.number().required(),
  revenueShareInPercentage: yup.number().required(),
};

exports.updateFeeSchema = yup.object().shape({
  startDate: yup.date(),
  endDate: yup.date(),
  freePlanFee: yup
    .object()
    .shape(feeDetailsSchema)
    .notRequired()
    .nullable()
    .default(undefined),
  proPlanFee: yup
    .object()
    .shape({
      ...feeDetailsSchema,
      zeroLinkFee: yup
        .array()
        .of(yup.object().shape(feeDetailsSchema))
        .min(1, 'zeroLinkFee must have at least one item')
        .required('zeroLinkFee is required'),
    })
    .notRequired()
    .nullable()
    .default(undefined),
  platinumPlanFee: yup
    .object()
    .shape({
      ...feeDetailsSchema,
      zeroLinkFee: yup
        .array()
        .of(yup.object().shape(feeDetailsSchema))
        .min(1, 'zeroLinkFee must have at least one item')
        .required('zeroLinkFee is required'),
    })
    .notRequired()
    .nullable()
    .default(undefined),
  updatePayoutFeeConfigByPlanType: yup.boolean(),
});

exports.addCommunityFreeEnrolment = yup.object().shape({
  email: yup.string().required().email().lowercase().trim(),
  toSendEmail: yup.bool().required().default(true),
});

exports.addCommunityManager = yup.object().shape({
  email: yup.string().required().email().lowercase().trim(),
});
