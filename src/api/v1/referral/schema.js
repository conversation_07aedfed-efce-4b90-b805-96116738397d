const yup = require('yup');
const { sortParams } = require('../../../constants/common');

exports.getReferralDetailsSchema = yup.object().shape({
  referralCode: yup.string().trim().required(),
});

exports.getReferralCommunitiesSchema = yup.object().shape({
  pageSize: yup.number().default(20).required().integer(),
  pageNo: yup.number().default(1).required().integer(),
  sortBy: yup
    .string()
    .default(sortParams.REFERRER_EARNING)
    .notRequired()
    .trim(),
  sortOrder: yup.number().default(-1).notRequired(),
});
