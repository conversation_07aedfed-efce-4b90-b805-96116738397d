const { Router } = require('express');
const schema = require('./schema');
const controller = require('./controller');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const { validator } = require('../../../middleware/validator.middleware');

const router = Router({ mergeParams: true });

router
  .route('/')
  .get(
    postRoutePreHandlerMiddleware,
    validator(schema.getReferralDetailsSchema, 'query'),
    controller.getReferralCodeDetails
  )
  .post(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    controller.createReferralCodeForLearner
  );

router
  .route('/communities')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    validator(schema.getReferralCommunitiesSchema, 'query'),
    controller.getReferralCodeCommunities
  );

router
  .route('/earnings')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    controller.getReferralCodeEarnings
  );
module.exports = router;
