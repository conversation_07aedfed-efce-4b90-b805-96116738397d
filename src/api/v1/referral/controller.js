const httpStatus = require('http-status');
const schema = require('./schema');
const logger = require('../../../services/logger.service');
const referralService = require('../../../services/referral/referral.service');

const getReferralCodeDetails = async (req, res, next) => {
  try {
    const { referralCode } = await schema.getReferralDetailsSchema.cast(
      req.query
    );
    const response = await referralService.getReferralCodeDetails(
      referralCode?.toLocaleUpperCase()
    );
    return res.status(httpStatus.OK).json(response);
  } catch (err) {
    logger.error('getReferralCodeDetails failed due to', err);
    return next(err);
  }
};

const getReferralCodeEarnings = async (req, res, next) => {
  try {
    if (!req.user?.learner?._id) {
      throw new Error('Missing learner id');
    }
    const response =
      await referralService.getReferralCodeEarningsFromLearner(
        req.user?.learner?._id
      );
    return res.status(httpStatus.OK).json(response);
  } catch (err) {
    logger.error('getReferralCodeEarnings failed due to', err);
    return next(err);
  }
};

const getReferralCodeCommunities = async (req, res, next) => {
  try {
    if (!req.user?.learner?._id) {
      throw new Error('Missing learner id');
    }

    const { pageNo, pageSize, sortBy, sortOrder } =
      await schema.getReferralCommunitiesSchema.cast(req.query);

    const response =
      await referralService.getReferralCodeCommunitiesFromLearner({
        learnerObjectId: req.user?.learner?._id,
        skip: pageNo - 1,
        limit: pageSize,
        sortBy,
        sortOrder,
      });
    return res.status(httpStatus.OK).json(response);
  } catch (err) {
    logger.error('getReferralCodeCommunities failed due to', err);
    return next(err);
  }
};

const createReferralCodeForLearner = async (req, res, next) => {
  try {
    if (!req.user?.learner?._id) {
      throw new Error('Missing learner id');
    }
    const response = await referralService.createReferralCodeForLearner(
      req.user?.learner?._id
    );
    return res.status(httpStatus.CREATED).json(response);
  } catch (err) {
    logger.error('createReferralCodeForLearner failed due to', err);
    return next(err);
  }
};

module.exports = {
  getReferralCodeDetails,
  getReferralCodeEarnings,
  getReferralCodeCommunities,
  createReferralCodeForLearner,
};
