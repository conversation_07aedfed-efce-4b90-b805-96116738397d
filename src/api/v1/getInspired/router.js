const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const {
  featureAccessValidator,
} = require('../../../validations/featureAccess.validation');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');
const {
  managerCommunityValidator,
} = require('../../../communitiesAPI/validations/community.validation');
const { handlerWrapper } = require('../../../utils/request.util');
const {
  MODULE_TYPE,
  rateLimitMiddleware,
} = require('../../../utils/rateLimit.util');
const controller = require('./controller');
const schema = require('./schema');
const apiKeyValidation = require('../../../validations/apiKey.validation');
const { FEATURE_LIST_ID } = require('../../../constants/common');

const GET_INSPIRED_PREFIX = '/get-inspired';
const FEATURE_ID = FEATURE_LIST_ID.GET_INSPIRED;
const communityProjection = {
  _id: 1,
  code: 1,
  config: 1,
  communityIntent: 1,
  aiTemplateGenerationConfig: 1,
  title: 1,
  fullScreenBannerImgData: 1,
  isActive: 1,
  baseCurrency: 1,
};

const setupRouter = async function (router) {
  router
    .route(`/communities/:communityId${GET_INSPIRED_PREFIX}/generate`)
    .post(
      postRoutePreHandlerMiddleware,
      apiKeyValidation,
      handlerWrapper({
        handler: controller.generateAITemplate,
        requestValidators: {
          params: schema.communityIdPathSchema,
        },
      })
    );
  router.route(`/communities/:communityId${GET_INSPIRED_PREFIX}`).get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    featureAccessValidator(FEATURE_ID, communityProjection),
    handlerWrapper({
      handler: controller.getInspiredPage,
      requestValidators: {
        params: schema.communityIdPathSchema,
      },
    })
  );

  router
    .route(`/communities/:communityId${GET_INSPIRED_PREFIX}/templates`)
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      featureAccessValidator(FEATURE_ID, communityProjection),
      handlerWrapper({
        handler: controller.getInspiredTemplates,
        requestValidators: {
          query: schema.getInspiredTemplatesQuerySchema,
          params: schema.communityIdPathSchema,
        },
      })
    );

  router
    .route(
      `/communities/:communityId${GET_INSPIRED_PREFIX}/template/:templateId`
    )
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      featureAccessValidator(FEATURE_ID, communityProjection),
      handlerWrapper({
        handler: controller.getSingleTemplate,
        requestValidators: {
          params: schema.getSingleTemplatePathSchema,
        },
      })
    );

  router
    .route(`/communities/:communityId${GET_INSPIRED_PREFIX}/profile`)
    .put(
      postRoutePreHandlerMiddleware,
      rateLimitMiddleware({
        module: MODULE_TYPE.GET_INSPIRED,
      }),
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      featureAccessValidator(FEATURE_ID, communityProjection),
      handlerWrapper({
        handler: controller.updateGetInspiredProfile,
        requestValidators: {
          body: schema.updateGetInspiredProfileSchema,
          params: schema.communityIdPathSchema,
        },
      })
    );

  router
    .route(
      `/communities/:communityId${GET_INSPIRED_PREFIX}/stop-generation`
    )
    .put(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      featureAccessValidator(FEATURE_ID, communityProjection),
      handlerWrapper({
        handler: controller.stopAITemplateGeneration,
        requestValidators: {
          params: schema.communityIdPathSchema,
        },
      })
    );
};

module.exports = {
  setupRouter,
};
