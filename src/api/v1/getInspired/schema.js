const yup = require('yup');

exports.communityIdPathSchema = yup.object().shape({
  communityId: yup.string().required(),
});

exports.getSingleTemplatePathSchema = yup.object().shape({
  communityId: yup.string().required(),
  templateId: yup.string().required(),
});

exports.getInspiredTemplatesQuerySchema = yup.object().shape({
  type: yup.string().required().trim(),
  isAIGenerated: yup.boolean().notRequired(),
  pageSize: yup.number().default(20).required().integer(),
  pageNo: yup.number().default(1).required().integer(),
});

exports.updateGetInspiredProfileSchema = yup.object().shape({
  ownerProfileImage: yup.string().notRequired().trim(),
  ownerFirstName: yup.string().notRequired().trim(),
  ownerLastName: yup.string().notRequired().trim(),
  countryCode: yup.string().uppercase().notRequired().trim(),
  communityIntent: yup.string().notRequired().trim(),
  socialMedia: yup.array().notRequired(),
});
