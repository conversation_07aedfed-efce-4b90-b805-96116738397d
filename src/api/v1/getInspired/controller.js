const schema = require('./schema');
const service = require('../../../services/getInspired/getInspired.service');

exports.getInspiredPage = async (req) => {
  const { community } = req.metadata;

  const result = await service.getInspiredPage({
    community,
  });
  return result;
};

exports.generateAITemplate = async (req) => {
  const { communityId } = req.params;

  const result = await service.generateAITemplateForCommunity({
    communityId,
  });

  return result;
};

exports.getSingleTemplate = async (req) => {
  const { communityId, templateId } =
    schema.getSingleTemplatePathSchema.cast(req.params);

  const result = await service.getSingleTemplate({
    communityId,
    templateId,
  });
  return result;
};

exports.getInspiredTemplates = async (req) => {
  const { community } = req.metadata;

  const { type, isAIGenerated, pageSize, pageNo } =
    schema.getInspiredTemplatesQuerySchema.cast(req.query);

  const result = await service.getInspiredTemplates({
    community,
    type,
    isAIGenerated,
    pageSize,
    pageNo,
  });
  return result;
};

exports.updateGetInspiredProfile = async (req) => {
  const { community } = req.metadata;
  const body = schema.updateGetInspiredProfileSchema.cast(req.body);

  const result = await service.updateGetInspiredProfile({
    body,
    community,
  });
  return result;
};

exports.stopAITemplateGeneration = async (req) => {
  const { community } = req.metadata;

  const result = await service.stopAITemplateGeneration({
    community,
  });
  return result;
};
