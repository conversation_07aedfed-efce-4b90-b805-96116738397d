const { Router } = require('express');
const schema = require('./schema');
const controller = require('./controller');
const apiKeyValidation = require('../../../validations/apiKey.validation');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const { handlerWrapper } = require('../../../utils/request.util');

const router = Router({ mergeParams: true });

router.route('/').post(
  postRoutePreHandlerMiddleware,
  apiKeyValidation,
  handlerWrapper({
    handler: controller.addBatchMetadata,
    requestValidators: {
      body: schema.addBatchMetadataSchema,
      params: schema.communityIdSchema,
    },
  })
);

module.exports = router;
