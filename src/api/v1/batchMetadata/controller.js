const schema = require('./schema');
const batchMetadataService = require('../../../services/batchMetadata');

exports.addBatchMetadata = async (req, res) => {
  const { communityId } = schema.communityIdSchema.cast(req.params);

  const {
    batchMetadataModelType,
    entityObjectId,
    addedObjectId,
    maxBatchSize,
  } = schema.addBatchMetadataSchema.cast(req.body);

  await batchMetadataService.add({
    batchMetadataModelType,
    entityObjectId,
    communityObjectId: communityId,
    addedObjectId,
    maxBatchSize,
  });

  return { message: 'success' };
};
