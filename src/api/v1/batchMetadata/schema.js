const yup = require('yup');
const { BATCH_METADATA_MODEL_TYPE } = require('../../../constants/common');

exports.communityIdSchema = yup.object().shape({
  communityId: yup.string().trim().required(),
});

exports.addBatchMetadataSchema = yup.object().shape({
  batchMetadataModelType: yup
    .string()
    .oneOf(Object.values(BATCH_METADATA_MODEL_TYPE))
    .required(),
  entityObjectId: yup.string().trim().required(),
  addedObjectId: yup.string().trim().required(),
  maxBatchSize: yup.number().notRequired(),
});
