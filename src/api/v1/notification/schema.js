const yup = require('yup');
const {
  sortParams,
  NOTIFICATION_PREFERENCE_MANAGER_MAIL_TARGET,
} = require('../../../constants/common');

exports.unsubAndResubSchema = yup.object().shape({
  token: yup.string().required(),
  email: yup.string().required(),
  communityCode: yup.string().required(),
  mailType: yup.string().required(),
  entityType: yup.string().notRequired(),
  entityObjectId: yup.string().notRequired(),
  entityName: yup.string().notRequired(),
});

exports.enrollmentEmailSchema = yup.object().shape({
  community: yup.object().required(),
  learner: yup.object().required(),
  purchaseTransaction: yup.object().required(),
  application: yup.object().notRequired(),
});

exports.renewalFailureSchema = yup.object().shape({
  communityId: yup.string().required(),
  learnerObjectId: yup.string().required(),
  purchaseTransactionObjectId: yup.string().required(),
  paymentMetadata: yup.object().notRequired(),
});

exports.notificationPreferenceForCMSchema = yup.object().shape({
  mailTarget: yup
    .string()
    .default(NOTIFICATION_PREFERENCE_MANAGER_MAIL_TARGET.MANAGER)
    .notRequired()
    .trim(),
});

exports.affiliateSaleEmailSchema = yup.object().shape({
  communityObjectId: yup.string().trim().required(),
  learnerObjectId: yup.string().trim().required(),
  paidAmount: yup.string().trim().required(),
  currency: yup.string().trim().required(),
  commissionEarningAmount: yup.string().trim().required(),
  commissionEarningCurrency: yup.string().trim().required(),
  transactionCreatedAt: yup.string().trim().required(),
  entityTitle: yup.string().trim().required(),
});

exports.saleEmailSchema = yup.object().shape({
  communityObjectId: yup.string().trim().required(),
  learnerObjectId: yup.string().trim().required(),
  paidAmount: yup.string().trim().required(),
  currency: yup.string().trim().required(),
  transactionCreatedAt: yup.string().trim().required(),
  entityTitle: yup.string().trim().required(),
  entityLink: yup.string().trim().required(),
});

exports.purchasePlanNotificationSchema = yup.object().shape({
  planOrderObjectId: yup.string().trim().required(),
  communityObjectId: yup.string().trim().required(),
  learnerObjectId: yup.string().trim().required(),
  amount: yup.number().required(),
  currency: yup.string().trim().required(),
  nextBillingAmount: yup.number().notRequired(),
  sendMail: yup.boolean().notRequired().default(true),
  sendLarkNoti: yup.boolean().notRequired().default(false),
});

exports.cancelledPlanNotificationSchema = yup.object().shape({
  planOrderObjectId: yup.string().trim().required(),
  communityObjectId: yup.string().trim().required(),
  learnerObjectId: yup.string().trim().required(),
  subscriptionExpiryDate: yup.string().trim().required(),
  failureReason: yup.string().notRequired(),
  sendMail: yup.boolean().notRequired().default(true),
  sendLarkNoti: yup.boolean().notRequired().default(true),
});

exports.renewalFailurePlanNotificationSchema = yup.object().shape({
  planOrderObjectId: yup.string().trim().required(),
  communityObjectId: yup.string().trim().required(),
  learnerObjectId: yup.string().trim().required(),
  paymentMetadata: yup.object().notRequired(),
});

exports.planReferralRewardNotificationSchema = yup.object().shape({
  communityObjectId: yup.string().trim().required(),
  refereeCommunityObjectId: yup.string().trim().required(),
  rewardAmount: yup.number().required(),
  currency: yup.string().trim().required(),
  recurringRewardAmount: yup.number().required(),
  isFirstBillingCycle: yup.boolean().required(),
});

exports.membersLimitReachingNotificationSchema = yup.object().shape({
  communityObjectId: yup.string().trim().required(),
});

exports.membersLimitReachedNotificationSchema = yup.object().shape({
  communityObjectId: yup.string().trim().required(),
});
