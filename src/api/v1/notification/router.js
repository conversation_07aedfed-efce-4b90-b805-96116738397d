const schema = require('./schema');
const controller = require('./controller');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const accessTokenValidation = require('../../../validations/token.validation');
const {
  managerCommunityValidator,
  memberCommunityValidator,
} = require('../../../communitiesAPI/validations/community.validation');
const {
  validateAll,
} = require('../../../middleware/validator.middleware');
const apiKeyValidation = require('../../../validations/apiKey.validation');

const setupOptOutRouter = function (router) {
  router
    .route('/notification/unsubscribe-mail')
    .put(
      postRoutePreHandlerMiddleware,
      validateAll([
        { schema: schema.unsubAndResubSchema, location: 'body' },
      ]),
      controller.unsubscribeFromMailType
    );
  router
    .route('/notification/resubscribe-mail')
    .put(
      postRoutePreHandlerMiddleware,
      validateAll([
        { schema: schema.unsubAndResubSchema, location: 'body' },
      ]),
      controller.resubscribeToMailType
    );
};

const setupPreferencesRouter = function (router) {
  // GET API FOR NOTIFICATIONS FOR A USER - MEMBER
  router.get(
    '/notification/preferences/member',
    postRoutePreHandlerMiddleware,
    accessTokenValidation(),
    controller.getNotificationsPreferenceForMember
  );

  // GET API FOR NOTIFICATIONS FOR A USER - MANAGER
  router.get(
    '/communities/:communityId/notification/preferences/manager',
    postRoutePreHandlerMiddleware,
    validateAll([
      {
        schema: schema.notificationPreferenceForCMSchema,
        location: 'query',
      },
    ]),
    accessTokenValidation(),
    managerCommunityValidator,
    controller.getNotificationsPreferencesForCM
  );

  router.put(
    '/communities/:communityId/notification/preferences',
    postRoutePreHandlerMiddleware,
    accessTokenValidation(),
    memberCommunityValidator,
    controller.updateNotificationPreference
  );
};

const setupNotificationRouter = function (router) {
  router
    .route('/notification/enrollment')
    .post(
      postRoutePreHandlerMiddleware,
      apiKeyValidation,
      validateAll([
        { schema: schema.enrollmentEmailSchema, location: 'body' },
      ]),
      controller.sendEnrollmentNotification
    );

  router
    .route('/notification/affiliate-sale')
    .post(
      postRoutePreHandlerMiddleware,
      apiKeyValidation,
      validateAll([
        { schema: schema.affiliateSaleEmailSchema, location: 'body' },
      ]),
      controller.sendAffiliateSaleNotification
    );

  router
    .route('/notification/sale')
    .post(
      postRoutePreHandlerMiddleware,
      apiKeyValidation,
      validateAll([{ schema: schema.saleEmailSchema, location: 'body' }]),
      controller.sendSaleNotification
    );

  router
    .route('/notification/renewalFailure')
    .post(
      postRoutePreHandlerMiddleware,
      apiKeyValidation,
      validateAll([
        { schema: schema.renewalFailureSchema, location: 'body' },
      ]),
      controller.sendRenewalFailureNotification
    );

  router.route('/notification/plan/enroll').post(
    postRoutePreHandlerMiddleware,
    apiKeyValidation,
    validateAll([
      {
        schema: schema.purchasePlanNotificationSchema,
        location: 'body',
      },
    ]),
    controller.sendPurchasePlanNotification
  );

  router.route('/notification/plan/cancelled').post(
    postRoutePreHandlerMiddleware,
    apiKeyValidation,
    validateAll([
      {
        schema: schema.cancelledPlanNotificationSchema,
        location: 'body',
      },
    ]),
    controller.sendCancelledPlanNotification
  );

  router.route('/notification/plan/renewalFailure').post(
    postRoutePreHandlerMiddleware,
    apiKeyValidation,
    validateAll([
      {
        schema: schema.renewalFailurePlanNotificationSchema,
        location: 'body',
      },
    ]),
    controller.sendRenewalFailurePlanNotification
  );

  router.route('/notification/plan/referralReward').post(
    postRoutePreHandlerMiddleware,
    apiKeyValidation,
    validateAll([
      {
        schema: schema.planReferralRewardNotificationSchema,
        location: 'body',
      },
    ]),
    controller.sendPlanReferralRewardNotification
  );

  router.route('/notification/membersLimitReaching').post(
    postRoutePreHandlerMiddleware,
    apiKeyValidation,
    validateAll([
      {
        schema: schema.membersLimitReachingNotificationSchema,
        location: 'body',
      },
    ]),
    controller.sendMembersLimitReachingNotification
  );

  router.route('/notification/membersLimitReached').post(
    postRoutePreHandlerMiddleware,
    apiKeyValidation,
    validateAll([
      {
        schema: schema.membersLimitReachedNotificationSchema,
        location: 'body',
      },
    ]),
    controller.sendMembersLimitReachedNotification
  );
};

exports.setupRouter = function (router) {
  setupOptOutRouter(router);
  setupPreferencesRouter(router);
  setupNotificationRouter(router);
};
