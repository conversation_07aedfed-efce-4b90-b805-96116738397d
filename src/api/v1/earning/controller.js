const schema = require('./schema');
const logger = require('../../../services/logger.service');
const service = require('../../../services/earning');

exports.getEarnings = async (req, res, next) => {
  try {
    const { communityId } = await schema.communityIdSchema.cast(
      req.params
    );

    const { duration, startDate, endDate, addonLimit } =
      await schema.earningQuerySchema.cast(req.query);

    const response = await service.retrieveEarnings({
      duration,
      startDate,
      endDate,
      communityId,
      addonLimit,
    });

    return res.json({ data: response });
  } catch (err) {
    logger.error('getEarnings failed due to', err.message, err.stack);
    return next(err);
  }
};
