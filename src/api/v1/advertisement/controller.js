const schema = require('./schema');
const pixelService = require('../../../services/advertisement/pixel.service');
const magicAudienceService = require('../../../services/advertisement/magicAudience.service');
const communityProductService = require('../../../services/common/communityProducts.service');

exports.updateCommunityPixels = async (req, res, next) => {
  const { communityId } = await schema.communityIdSchema.cast(req.params);
  const body = await schema.updateCommunityPixelSchema.cast(req.body);

  const results = await pixelService.updateCommunityPixels(
    communityId,
    body
  );
  return results;
};

exports.getMagicAudienceProducts = async (req, res) => {
  const { communityId } = schema.communityIdSchema.cast(req.params);
  const { search, pageSize } = schema.getMagicAudienceProductsSchema.cast(
    req.query
  );

  const results = await communityProductService.retrieveAllProducts({
    communityObjectId: communityId,
    search,
    pageSize,
  });
  return results;
};

exports.createMagicAudienceApplication = async (req, res, next) => {
  const { communityId } = await schema.communityIdSchema.cast(req.params);
  const body = await schema.createMagicAudienceApplicationSchema.cast(
    req.body
  );

  const results =
    await magicAudienceService.createMagicAudienceApplication(
      communityId,
      req.user?.learner,
      body,
      req.user?.email
    );

  return results;
};
