const schema = require('./schema');
const controller = require('./controller');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');
const {
  managerCommunityValidator,
} = require('../../../communitiesAPI/validations/community.validation');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const { handlerWrapper } = require('../../../utils/request.util');

const ADS_ROUTE = '/advertisement';
const PIXEL_ROUTE = '/pixel';
const MAGIC_AUDIENCE_ROUTE = '/magic-audience';

const setupRouter = async function (router) {
  router.route(`/communities/:communityId${ADS_ROUTE}${PIXEL_ROUTE}`).put(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    handlerWrapper({
      handler: controller.updateCommunityPixels,
      requestValidators: {
        body: schema.updateCommunityPixelSchema,
        params: schema.communityIdSchema,
      },
    })
  );

  router
    .route(
      `/communities/:communityId${ADS_ROUTE}${MAGIC_AUDIENCE_ROUTE}/products`
    )
    .get(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.getMagicAudienceProducts,
        requestValidators: {
          query: schema.getMagicAudienceProductsSchema,
          params: schema.communityIdSchema,
        },
      })
    );

  router
    .route(`/communities/:communityId${ADS_ROUTE}${MAGIC_AUDIENCE_ROUTE}`)
    .post(
      postRoutePreHandlerMiddleware,
      tokenValidator(),
      userValidation,
      managerCommunityValidator,
      handlerWrapper({
        handler: controller.createMagicAudienceApplication,
        requestValidators: {
          body: schema.createMagicAudienceApplicationSchema,
          params: schema.communityIdSchema,
        },
      })
    );
};

module.exports = {
  setupRouter,
};
