const yup = require('yup');

exports.communityIdSchema = yup.object().shape({
  communityId: yup.string().required(),
});

exports.createMagicAudienceApplicationSchema = yup.object().shape({
  entities: yup.array(yup.object()).required(),
  goal: yup.string().required(),
  socialMediaLink: yup.string().required(),
  budget: yup.string().required(),
  // totalFollowers: yup.number().required(),
});

exports.getMagicAudienceProductsSchema = yup.object().shape({
  search: yup.string().uppercase().trim().notRequired(),
  pageSize: yup.number().notRequired(),
});

exports.updateCommunityPixelSchema = yup.object().shape({
  trackingPixels: yup.object().required(),
});
