const { Router } = require('express');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const {
  validateAll,
} = require('../../../middleware/validator.middleware');
const apiKeyValidation = require('../../../validations/apiKey.validation');
const schema = require('./schema');
const controller = require('./controller');

const router = Router({ mergeParams: true });

router
  .route('/')
  .get(
    postRoutePreHandlerMiddleware,
    apiKeyValidation,
    validateAll([
      {
        schema: schema.getRedisCacheDataSchema,
        location: 'query',
      },
    ]),
    controller.getRedisCacheData
  )
  .delete(
    postRoutePreHandlerMiddleware,
    apiKeyValidation,
    validateAll([
      {
        schema: schema.flushRedisCacheDataSchema,
        location: 'query',
      },
    ]),
    controller.flushRedisCacheData
  );

module.exports = router;
