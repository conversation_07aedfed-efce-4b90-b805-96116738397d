const logger = require('../../../services/logger.service');
const schema = require('./schema');

const RedisCacheService = require('../../../services/redisCache/redisCache.service');

exports.getRedisCacheData = async (req, res, next) => {
  try {
    const { key } = await schema.getRedisCacheDataSchema.cast(req.query);
    const data = await RedisCacheService.getDataByKey(key);
    return res.json({ data });
  } catch (err) {
    logger.error(`Failed to get redis data by key ${err.message}`);
    return next(err);
  }
};

exports.flushRedisCacheData = async (req, res, next) => {
  try {
    const { key } = await schema.flushRedisCacheDataSchema.cast(req.query);
    await RedisCacheService.flushDataByKey(key);
    return res.json({ data: {} });
  } catch (err) {
    logger.error(`Failed to flush redis data by key ${err.message}`);
    return next(err);
  }
};
