const controller = require('./controller');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');

const setupRouter = function (router) {
  router
    .route('/assistant/product/ask')
    .post(postRoutePreHandlerMiddleware, controller.onProductIssueMessage);

  router
    .route('/assistant/product/forward_report')
    .post(postRoutePreHandlerMiddleware, controller.forwardReport);

  router
    .route('/assistant/product/task')
    .post(
      postRoutePreHandlerMiddleware,
      controller.createProductLiveIssueTask
    );
};

module.exports = {
  setupRouter,
};
