/* eslint-disable no-unused-vars */
const { DateTime } = require('luxon');
const {
  askAQuestionToProductFeedbackBot,
  ingestDocDataToProductFeedbackBot,
} = require('../../../clients/openai.client');
const larkService = require('../../../communitiesAPI/services/common/lark.service');
const logger = require('../../../services/logger.service');
const productIssueTrackingService = require('../../../services/productIssue/tracking.service');
const {
  LARK_PRODUCT_ISSUE_BOT_VERIFICATION_TOKEN,
} = require('../../../config');

const PRODUCT_FEED_BACK_BOT_DOC_ID = 'UfAHdl1aGoGjvMxUpT5ujftAsFb';
const MESSAGE_TYPE_POST = 'post';
const TASK_LIST_GUID = 'aaf98a4d-4eea-470b-9732-fe62491f9ab3'; // test one

const COMMANDS = {
  INGEST_DATA: '#ingestDataForProductFeedback',
  FORCE_REPLY_IN_THREAD: '#force_reply',
  DO_NOT_REPLY: '#no_reply',
  RESOLVE: '#resolve',
  HELP: '#help',
};

const HELP_MESSAGE_STATUSES =
  productIssueTrackingService.ALL_PRODUCT_REPORT_STATUSES.join(' | ');
const HELP_MESSAGE_CATEGORIES =
  productIssueTrackingService.ALL_REPORT_CATEGORIES.join(' | ');
const HELP_MESSAGE_COMPONENTS =
  productIssueTrackingService.ALL_REPORT_COMPONENTS.join(' | ');
const HELP_MESSAGE_PRODUCT_DOMAIN =
  productIssueTrackingService.ALL_PRODUCT_DOMAINS.join(' | ');
const HELP_MESSAGE_COMMANDS =
  '- **#ingestDataForProductFeedback** - Ingest data from the doc to the product feedback bot\n' +
  '- **#force_reply** - Ask the bot to force reply in thread\n' +
  '- **#no_reply** - Ask the bot to not reply\n' +
  '- **#resolve [status] [reportCategories] [components] [productDomains] [solvableViaAdmin] [linkToAdminPage] [resolutionDetails]** - Record the issue resolution\n' +
  '- **#help** - Show this help message\n\n';

const HELP_MESSAGE_ACCEPTABLE_VALUES =
  '**Acceptable Values**\n' +
  '- **[status]** = `' +
  HELP_MESSAGE_STATUSES +
  '`\n' +
  '- **[reportCategories]** = `' +
  HELP_MESSAGE_CATEGORIES +
  '` (multi values supported, comma-separated, no spacing in between)\n' +
  '- **[components]** = `' +
  HELP_MESSAGE_COMPONENTS +
  '` (multi values supported, comma-separated, no spacing in between)\n' +
  '- **[productDomains]** = `' +
  HELP_MESSAGE_PRODUCT_DOMAIN +
  '` (multi values supported, comma-separated, no spacing in between)\n' +
  '- **[solvableViaAdmin]** = `true` or `false`\n' +
  '- **[linkToAdminPage]** = if solvableViaAdmin = true, specific link to the admin JS (`https://cops.nas.io/...` or `https://cops-admin.nas.io/...`) where the issue can be self serviced, else put N/A\n' +
  '- **[resolutionDetails]** = any text';

const PRODUCT_ISSUE_REPORT_FORWARDING_CHAT_ID =
  'oc_3ae2fe0034db3774acab7f32b866ace4';

async function verifyLarkMessage(body) {
  // Cannot easily using the Lark Signature header because
  // the `body` must be the raw version (cannot stringify the parsed JSON body).
  // Doing so will cause invalid signature check.
  return body.header.token === LARK_PRODUCT_ISSUE_BOT_VERIFICATION_TOKEN;
}

async function sendLarkReply(
  tenantAccessToken,
  message,
  sender,
  { title, content },
  replyInThread
) {
  const payload = {
    ...sender.sender_id,
    chat_id: message.chat_id,
    receive_id: message.chat_id,
    msg_type: MESSAGE_TYPE_POST,
    reply_in_thread: replyInThread,
    content: JSON.stringify({
      en_us: { title, content },
    }),
  };

  const result = await larkService.replyToMessageOnLark({
    payload,
    tenantAccessToken,
    receiveIdType: 'chat_id',
    messageId: message.message_id,
  });

  if (result.code !== larkService.LARK_SUCCESS_CODE) {
    throw new Error('Failed sending lark reply: ', result.msg);
  }

  return result.data;
}

async function generateHelpMessage(tenantAccessToken, message, sender) {
  await sendLarkReply(
    tenantAccessToken,
    message,
    sender,
    {
      title: 'Help',
      content: [
        [
          {
            tag: 'md',
            text: HELP_MESSAGE_COMMANDS,
          },
          {
            tag: 'hr',
          },
          {
            tag: 'md',
            text: HELP_MESSAGE_ACCEPTABLE_VALUES,
          },
        ],
      ],
    },
    true
  );
}

function generateIssueDescription(messageType, messageContent) {
  if (messageType !== MESSAGE_TYPE_POST) {
    return messageContent.text;
  }

  return messageContent.content
    .map((block) => {
      return block
        .map((item) => {
          switch (item.tag) {
            case 'text':
              return item.text;
            case 'a':
              return item.href;
            case 'md':
              return item.text.replace(/\n/g, '. ');
            case 'at':
              return `@${item.user_name}`;
            default:
              return '';
          }
        })
        .join(' ');
    })
    .join(' ')
    .replace(/\s+/g, ' ')
    .trim();
}

async function generateAIResponse(
  tenantAccessToken,
  message,
  sender,
  textContent
) {
  const aiResult = await askAQuestionToProductFeedbackBot({
    question: textContent,
  });

  let aiContent =
    'Unexpected AI response received: ' + JSON.stringify(aiResult);
  if (
    (aiResult ?? []).length !== 0 &&
    (aiResult[0]?.content ?? []).length !== 0
  ) {
    aiContent = aiResult[0]?.content[0]?.text?.value;
  }

  await sendLarkReply(
    tenantAccessToken,
    message,
    sender,
    {
      title: 'Report Analysis',
      content: [
        [
          {
            tag: 'md',
            text: aiContent,
          },
        ],
      ],
    },
    true
  );
}

async function ingestDataForProductFeedback(
  tenantAccessToken,
  message,
  sender
) {
  const docsData = await larkService.getDocsData({
    tenantAccessToken,
    docId: PRODUCT_FEED_BACK_BOT_DOC_ID,
  });

  const { data } = docsData;
  const { content } = data;

  // now we will add the data to the productfeedback bot
  await ingestDocDataToProductFeedbackBot({
    data: content,
  });

  await sendLarkReply(
    tenantAccessToken,
    message,
    sender,
    {
      title: 'Data Ingested Successfully!',
      content: [],
    },
    true
  );
}

function getThisMonday() {
  const now = DateTime.utc();
  const thisMonday = now.startOf('week');
  return thisMonday;
}

function getThisSunday() {
  const now = DateTime.utc();
  const thisSunday = now.endOf('week');
  return thisSunday;
}

async function createTaskListSectionIfNotExists({ tenantAccessToken }) {
  let sectionId;
  try {
    const thisMonday = getThisMonday();
    const sectionResponse = await larkService.getSectionsOfTaskList({
      tenantAccessToken,
      taskListId: TASK_LIST_GUID,
    });

    const latestSection = sectionResponse.items[0];
    const latestSectionDetails = await larkService.getSectionDetails({
      tenantAccessToken,
      sectionId: latestSection.guid,
    });

    const createdAt = parseInt(latestSectionDetails?.created_at, 10);
    const beforeThisMonday = createdAt < thisMonday.toSeconds() * 1000;

    sectionId = latestSection.guid;
    if (beforeThisMonday || latestSectionDetails.is_default === true) {
      const thisSunday = getThisSunday();
      const sectionFrom = thisMonday.toFormat('dd MMM');
      const sectionTo = thisSunday.toFormat('dd MMM');
      const sectionName = `${sectionFrom} - ${sectionTo}`;
      const newSectionResponse = await larkService.createSection({
        tenantAccessToken,
        taskListId: TASK_LIST_GUID,
        name: sectionName,
        insertBefore: latestSection.guid,
      });
      sectionId = newSectionResponse.guid;
    }
  } catch (error) {
    logger.error(
      'Error in creating lark task list section',
      error,
      error.stack
    );
  }

  return sectionId;
}

async function resolveIssue(
  tenantAccessToken,
  message,
  sender,
  textContent
) {
  const tokens = textContent.split(' ');
  if (tokens.length < 7) {
    throw new Error(
      'Resolve issue command should be in this format -> #resolve [status] [reportCategories] [components] [solvableViaAdmin] [linkToAdminPage] [resolutionDetails]. Run #help to see details.'
    );
  }

  const [
    _,
    status,
    reportCategory,
    components,
    productDomain,
    solvableViaAdmin,
    linkToAdminPage,
    ...resolutionDetails
  ] = tokens.map((value) => value.trim());

  if (resolutionDetails.length === 0) {
    throw new Error('Resolution details is required');
  }

  const processedCategories = reportCategory
    .split(',')
    .map((cat) => cat.trim().toLowerCase());

  const processedComponents = components
    .split(',')
    .map((cat) => cat.trim().toLowerCase());

  const processedProductDomains = productDomain
    .split(',')
    .map((cat) => cat.trim().toLowerCase());

  const processedSolvableViaAdmin = solvableViaAdmin.toLowerCase().trim();

  const resolutionDetailsText = resolutionDetails.join(' ');
  await productIssueTrackingService.resolveIssue({
    tenantAccessToken,
    parentMessageId: message.parent_id,
    status,
    resolverId: sender.sender_id.open_id,
    processedComponents,
    processedCategories,
    processedProductDomains,
    processedSolvableViaAdmin,
    linkToAdminPage,
    resolutionDetails: resolutionDetailsText,
    resolveTime: parseInt(message.create_time, 10),
  });

  const formattedCategories = processedCategories.join(', ');
  const formattedComponents = processedComponents.join(', ');
  const formattedProductDomains = processedProductDomains.join(', ');

  await sendLarkReply(
    tenantAccessToken,
    message,
    sender,
    {
      title: 'Issue Resolution Recorded!',
      content: [
        [
          {
            tag: 'md',
            text:
              `- **Status**: ${status}\n` +
              `- **Report Categories**: ${formattedCategories}\n` +
              `- **Components**: ${formattedComponents}\n` +
              `- **Product Domains**: ${formattedProductDomains}\n` +
              `- **Solvable Via Admin**: \`${processedSolvableViaAdmin}\`\n` +
              `- **Link To Admin Page**: \`${linkToAdminPage}\`\n` +
              `- **Resolution Details**: ${resolutionDetailsText}`,
          },
        ],
      ],
    },
    true
  );
}

async function createTask({
  tenantAccessToken,
  chatId,
  sectionId,
  issueText,
}) {
  try {
    const task = await larkService.createTask({
      tenantAccessToken,
      taskListId: TASK_LIST_GUID,
      sectionId,
      summary: 'Test task',
      extra: 'om_8fc1e7f5ad2d5d331556da5a2247ae38',
      due: {
        timestamp: (
          DateTime.utc().plus({ days: 7 }).toSeconds() * 1000
        ).toString(),
        is_all_day: true,
      },
      origin: {
        href: {
          title: 'Product Live Issue',
          url: 'https://applink.larksuite.com/client/thread/open?chatid=oc_4260d6c31d5f7f2db6755c03218ee5aa&threadid=7470306019004940294',
        },
        platform_i18n_name: {
          en_us: 'chat',
        },
      },
    });

    const taskMessage = {
      chat_id: 'oc_eaa5ddf982b14a7297f3a29bd4dccd2b',
      receive_id: 'oc_eaa5ddf982b14a7297f3a29bd4dccd2b',
      msg_type: 'text',
      reply_in_thread: true,
      content: JSON.stringify({
        text: '[Task](https://applink.larksuite.com/client/todo/detail?guid=40be92c0-8f82-4483-b792-bfff02c2071f&suite_entity_num=t114909)',
      }),
      // content: JSON.stringify({
      //   task_id: '40be92c0-8f82-4483-b792-bfff02c2071f',
      //   summary: 'Test task',
      // }),
    };
    await larkService.replyToMessageOnLark({
      payload: taskMessage,
      tenantAccessToken,
      receiveIdType: 'chat_id',
      messageId: 'om_8fc1e7f5ad2d5d331556da5a2247ae38',
    });
  } catch (error) {
    logger.error('Error in creating lark task', error);
  }
}

const createProductLiveIssueTask = async (req, res) => {
  const chatId = 'oc_4260d6c31d5f7f2db6755c03218ee5aa';
  const issueText = 'Test Task';
  const tenantAccessToken = await larkService.generateTenantAccessToken();
  const sectionId = await createTaskListSectionIfNotExists({
    tenantAccessToken,
  });
  await createTask({ tenantAccessToken, chatId, sectionId, issueText });
};

const onProductIssueMessage = async (req, res) => {
  const { challenge } = req.body;
  if (challenge) {
    return res.status(200).json({ challenge });
  }

  const { event } = req.body;
  const { message, sender } = event;
  const tenantAccessToken = await larkService.generateTenantAccessToken();

  try {
    if (!(await verifyLarkMessage(req.body))) {
      throw new Error('Invalid lark message signature');
    }

    res.status(200).json(true);
    if (message.chat_id === PRODUCT_ISSUE_REPORT_FORWARDING_CHAT_ID) {
      return;
    }

    let textContent = '';
    let messageContent = {};

    if (message) {
      messageContent = JSON.parse(message.content);
      textContent =
        message.message_type === MESSAGE_TYPE_POST
          ? JSON.stringify(messageContent.content)
          : messageContent?.text;
    }
    if (textContent?.includes(COMMANDS.DO_NOT_REPLY)) {
      return;
    }

    if (textContent?.startsWith(COMMANDS.HELP)) {
      await generateHelpMessage(tenantAccessToken, message, sender);
      return;
    }

    const isFirstMessage = !message.thread_id;
    if (isFirstMessage) {
      // We need this first message so we can get the thread ID.
      // The first message doesn't contain the thread ID.
      // We need the thread ID so we can forward the thread for review later.
      const processingMessage = await sendLarkReply(
        tenantAccessToken,
        message,
        sender,
        {
          title: 'Report Received!',
          content: [
            [
              {
                tag: 'text',
                text: 'We are currently processing your request...',
              },
            ],
          ],
        },
        true
      );

      await productIssueTrackingService.createProductIssueRecord({
        tenantAccessToken,
        reporterId: sender.sender_id.open_id,
        messageId: message.message_id,
        threadId: processingMessage.thread_id,
        content: generateIssueDescription(
          message.message_type,
          messageContent
        ),
        createTime: parseInt(message.create_time, 10),
      });

      await generateAIResponse(
        tenantAccessToken,
        message,
        sender,
        textContent
      );

      if (textContent?.includes(COMMANDS.INGEST_DATA)) {
        await ingestDataForProductFeedback(
          tenantAccessToken,
          message,
          sender
        );
      }
      return;
    }

    await productIssueTrackingService.updateFirstEngineerReply({
      tenantAccessToken,
      parentMessageId: message.parent_id,
      senderId: sender.sender_id.open_id,
      replyTime: parseInt(message.create_time, 10),
    });

    if (textContent?.includes(COMMANDS.FORCE_REPLY_IN_THREAD)) {
      await generateAIResponse(
        tenantAccessToken,
        message,
        sender,
        textContent
      );
      return;
    }

    if (textContent?.startsWith(COMMANDS.RESOLVE)) {
      await resolveIssue(tenantAccessToken, message, sender, textContent);
      return;
    }
  } catch (error) {
    logger.error('Error in handling Lark bot message: ', error.stack);

    await sendLarkReply(
      tenantAccessToken,
      message,
      sender,
      {
        title: 'Error Occurred',
        content: [
          [
            {
              tag: 'text',
              text: 'An error occurred while processing your request.',
            },
            {
              tag: 'code_block',
              language: 'javascript',
              text: error.stack,
            },
          ],
        ],
      },
      true
    );
  }
};

const forwardReport = async (req, res) => {
  try {
    const {
      report_thread_id: reportThreadId,
      receiver_chat_id: receiverChatId,
    } = req.body;

    if (!reportThreadId || !receiverChatId) {
      return res.status(400).json({
        error:
          'Missing required fields: report_thread_id or receiver_chat_id',
      });
    }

    const tenantAccessToken =
      await larkService.generateTenantAccessToken();

    await larkService.forwardThreadToGroupChat({
      tenantAccessToken,
      threadId: reportThreadId,
      chatId: receiverChatId,
    });

    return res.status(200).json({
      success: true,
      message: 'Report forwarded successfully',
    });
  } catch (error) {
    logger.error('Error in forwarding report:', error);
    return res.status(500).json({
      error: 'Failed to forward report',
      details: error.message,
    });
  }
};

module.exports = {
  generateIssueDescription,
  onProductIssueMessage,
  createProductLiveIssueTask,
  forwardReport,
  generateAIResponse,
};
