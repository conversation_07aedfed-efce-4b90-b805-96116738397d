const { REACTION_ACTION, REACTION_REPLY } = require('./constants');

const getQuestionTemplate = (
  actualQuestion,
  productName,
  productId,
  currentLocale = 'en'
) => {
  return `question asked by the user: """${actualQuestion}""", please do not give any FAQ's, this is asked in the context of this ${productName} with productId = ${productId}. reply the user in ${
    currentLocale === 'ja' ? 'japanese' : currentLocale
  }`;
};

const getFAQFollowUpQuestion = (
  question,
  productName,
  productId,
  currentLocale = 'en'
) => `Based on the user's question: """${question}""", analyze the query carefully and identify **3 FAQs** from your knowledge base related to the specified product:

- **Product Name**: ${productName}
- **Product ID**: ${productId}

If the question does not directly relate to the product, generate **general FAQs** from your knowledge base. Ensure that the FAQs are diverse and relevant to the context or topic of the user’s query, covering general information, benefit, eligibility, schedule, commitments and financial aspects where applicable.

**Rules**:
1. Do not include answers; only provide the FAQ questions.
2. Output exactly **3 FAQ questions** strictly in the following JSON format with 3 keys { faq1, faq2, faq3 }. Each key should contain a single question.
3. Use **${
  currentLocale === 'ja' ? 'Japanese' : currentLocale
}** for the language of the questions.
4. Do not include any additional commentary, symbols, or formatting beyond the questions themselves.

Output format:-
"""
{ 
  "faq1": "<First FAQ question>", 
  "faq2": "<Second FAQ question>", 
  "faq3": "<Third FAQ question>" 
}
"""`;

const getMessageContent = ({
  isFAQ,
  question,
  productName,
  productId,
  currentLocale,
}) => {
  if (isFAQ) {
    return getFAQFollowUpQuestion(
      question,
      productName,
      productId,
      currentLocale
    );
  }

  return getQuestionTemplate(
    question,
    productName,
    productId,
    currentLocale
  );
};

const getUpdateDataForReaction = ({ action, reaction, messageData }) => {
  let updateData;
  const messageId = messageData.messageId;
  switch (action) {
    case REACTION_ACTION.ADD: {
      switch (reaction) {
        case REACTION_REPLY.LIKE: {
          updateData = {
            [`likedMessages.${messageId}`]: messageData,
            $unset: { [`dislikedMessages.${messageId}`]: '' },
          };
          break;
        }
        case REACTION_REPLY.DISLIKE: {
          updateData = {
            [`dislikedMessages.${messageId}`]: messageData,
            $unset: { [`likedMessages.${messageId}`]: '' },
          };
          break;
        }
        default: {
          // Do nothing
        }
      }
      break;
    }
    case REACTION_ACTION.REMOVE: {
      switch (reaction) {
        case REACTION_REPLY.LIKE: {
          updateData = {
            $unset: { [`likedMessages.${messageId}`]: '' },
          };
          break;
        }
        case REACTION_REPLY.DISLIKE: {
          updateData = {
            $unset: { [`dislikedMessages.${messageId}`]: '' },
          };
          break;
        }
        default: {
          // Do nothing
        }
      }
      break;
    }
    default: {
      break;
    }
  }

  return updateData;
};

module.exports = {
  getMessageContent,
  getUpdateDataForReaction,
};
