const controller = require('./controller');
const schema = require('./schema');

const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const { handlerWrapper } = require('../../../utils/request.util');

const setupRouter = function (router) {
  router.route('/assistant/session/:sessionId').get(
    postRoutePreHandlerMiddleware,
    handlerWrapper({
      handler: controller.getBySessionId,
    })
  );

  router
    .route('/assistant/ask')
    .post(postRoutePreHandlerMiddleware, controller.askAssistant);

  router.route('/assistant/ask/faq').post(
    postRoutePreHandlerMiddleware,
    handlerWrapper({
      handler: controller.getFaqs,
      requestValidators: {
        body: schema.askFaqAssistantSchema,
      },
    })
  );

  router.route('/assistant/reply/reaction').post(
    postRoutePreHandlerMiddleware,
    handlerWrapper({
      handler: controller.reactToAssistantReply,
      requestValidators: {
        body: schema.reactToAssistantReplySchema,
      },
    })
  );
};

module.exports = {
  setupRouter,
};
