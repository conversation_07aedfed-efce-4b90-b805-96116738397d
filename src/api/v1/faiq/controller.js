const httpStatus = require('http-status');
const schemas = require('./schema');

const { getMessageContent, getUpdateDataForReaction } = require('./utils');
const { DUPLICATE_KEY_ERROR_CODE } = require('./constants');
const {
  fetchSalesAgentAndAskAQuestion,
  fetchSalesAgentAndGetFaqs,
} = require('../../../clients/openai.client');
const logger = require('../../../services/logger.service');
const communityAIAssistantModel = require('../../../communitiesAPI/models/communityAIAssistant.model');

const getBySessionId = async (req) => {
  const { sessionId } = req.params;

  const filters = { sessionId };
  const result = await communityAIAssistantModel.find(filters).lean();
  return result;
};

const askAssistant = async (req, res) => {
  try {
    const {
      messageContent,
      threadId,
      communityId,
      productType,
      productId,
      learnerId,
      clickedJoin,
      countryId,
      sessionId,
      faiqViewed,
      isFAQ,

      // new
      question,
      productName,
      currentLocale,
      purchased,
      productPageViewed,
    } = schemas.askAssistantSchema.cast(req.body);

    // Update doc to mark as entity purchased in a session
    if (purchased) {
      const filters = {
        sessionId,
        communityId,
        productId,
        isFAQ: false,
      };
      const updateData = { purchased: true };

      try {
        const updateRes = await communityAIAssistantModel.updateOne(
          filters,
          updateData,
          { upsert: true }
        );

        return res
          .status(200)
          .json({ message: 'Updated successfully', data: updateRes });
      } catch (error) {
        if (error.code === DUPLICATE_KEY_ERROR_CODE) {
          const updateRes = await communityAIAssistantModel.updateOne(
            filters,
            updateData
          );

          return res
            .status(200)
            .json({ message: 'Updated successfully', data: updateRes });
        }

        return res.status(500).json({ error: error.message });
      }
    }

    // Update doc to mark as faiq viewed or clicked join in FAIQ a session
    if (productPageViewed || clickedJoin || faiqViewed) {
      const updateData = {};

      if (productPageViewed) {
        updateData.productPageViewed = productPageViewed;
      }

      if (clickedJoin) {
        updateData.clickedJoin = clickedJoin;
      }

      if (faiqViewed) {
        updateData.faiqViewed = faiqViewed;
      }

      const filters = {
        sessionId,
        communityId,
        productId,
        productType,
        isFAQ: isFAQ ?? false,
      };

      try {
        await communityAIAssistantModel.findOneAndUpdate(
          filters,
          updateData,
          { upsert: true }
        );

        return res.status(200).json({ message: 'Updated successfully' });
      } catch (error) {
        if (error.code === DUPLICATE_KEY_ERROR_CODE) {
          await communityAIAssistantModel.findOneAndUpdate(
            filters,
            updateData
          );
          return res.status(200).json({ message: 'Updated successfully' });
        }

        return res.status(500).json({ error: error.message });
      }
    }

    // TODO - @AmanMinhas Move questions template to db.
    // Ask a question to the assistant.
    const prompt =
      messageContent ??
      getMessageContent({
        isFAQ,
        question,
        productName,
        productId,
        currentLocale,
      });

    await fetchSalesAgentAndAskAQuestion({
      messageContent: prompt,
      threadId,
      communityId,
      productType,
      productId,
      learnerId,
      sessionId,
      isFAQ,
      countryId,
      response: res,
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const getFaqs = async (req) => {
  const {
    threadId,
    communityId,
    productType,
    productId,
    learnerId,
    countryId,
    sessionId,
    question,
    productName,
    currentLocale,
  } = schemas.askFaqAssistantSchema.cast(req.body);

  // Ask a question to the assistant.
  const prompt = getMessageContent({
    isFAQ: true,
    question,
    productName,
    productId,
    currentLocale,
  });

  const response = await fetchSalesAgentAndGetFaqs({
    messageContent: prompt,
    threadId,
    communityId,
    productType,
    productId,
    learnerId,
    sessionId,
    countryId,
  });

  return response;
};

const reactToAssistantReply = async (req) => {
  const {
    communityId,
    productId,
    learnerId,
    sessionId,
    messageData,
    reaction,
    action,
  } = schemas.reactToAssistantReplySchema.cast(req.body);

  // Setup filters and updateData
  const filters = {
    communityId,
    productId,
    sessionId,
    ...(learnerId ? { learnerId } : {}), // Ensure learnerId is included only if it exists
    isFAQ: false,
  };

  const updateData = getUpdateDataForReaction({
    action,
    reaction,
    messageData,
  });

  if (!updateData) {
    const erroMsg = `Invalid value for action or reaction. Received ${JSON.stringify(
      { action, reaction }
    )}`;
    logger.info(erroMsg);
    const error = new Error(erroMsg);
    error.errorCode = httpStatus.BAD_REQUEST;
    throw error;
  }

  const updateRes = await communityAIAssistantModel.updateOne(
    filters,
    updateData
  );

  return updateRes;
};

module.exports = {
  getBySessionId,
  askAssistant,
  getFaqs,
  reactToAssistantReply,
};
