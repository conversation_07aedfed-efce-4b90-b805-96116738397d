const yup = require('yup');

const askAssistantSchema = yup.object().shape({
  messageContent: yup.string().notRequired(),
  question: yup.string().required(),
  threadId: yup.string().required(),
  communityId: yup.string().required(),
  productType: yup.string().required(),
  productId: yup.string().required(),
  learnerId: yup.string().notRequired(),
  clickedJoin: yup.boolean().notRequired().default(false),
  countryId: yup.string().notRequired(),
  isFAQ: yup.boolean().notRequired(),
  sessionId: yup.string().notRequired(),
  productPageViewed: yup.boolean().notRequired(),
  faiqViewed: yup.boolean().notRequired(),
  productName: yup.string().notRequired(),
  currentLocale: yup.string().notRequired(),
});

const askFaqAssistantSchema = yup.object().shape({
  threadId: yup.string().notRequired(),
  communityId: yup.string().required(),
  productType: yup.string().required(),
  productId: yup.string().required(),
  learnerId: yup.string().notRequired(),
  countryId: yup.string().notRequired(),
  sessionId: yup.string().notRequired(),
  question: yup.string().required(),
  productName: yup.string().notRequired(),
  currentLocale: yup.string().notRequired(),
});

const reactToAssistantReplySchema = yup.object().shape({
  communityId: yup.string().required(),
  productId: yup.string().required(),
  sessionId: yup.string().required(),
  learnerId: yup.string().notRequired(),
  messageData: yup.object().shape({
    messageId: yup.string().required(),
    text: yup.string(),
  }),
  reaction: yup.string().required(), // "like", "dislike"
  action: yup.string().required(), // "add" | "remove"
});

module.exports = {
  askAssistantSchema,
  askFaqAssistantSchema,
  reactToAssistantReplySchema,
};
