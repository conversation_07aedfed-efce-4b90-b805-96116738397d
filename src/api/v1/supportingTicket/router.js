const { Router } = require('express');
const controller = require('./controller');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const intercomValidation = require('../../../validations/intercom.validation');

const router = Router({ mergeParams: true });

router
  .route('/webhook/intercom')
  .post(
    postRoutePreHandlerMiddleware,
    intercomValidation,
    controller.intercomWebhook
  );

module.exports = router;
