const logger = require('../../../services/logger.service');
const service = require('../../../services/supportingTicket');

exports.intercomWebhook = async (req, res, next) => {
  try {
    const response = await service.intercomService.handleWebhookEvent(
      req.body
    );

    return res.json(response);
  } catch (err) {
    logger.error('intercomWebhook failed due to', err.message, err.stack);
    return next(err);
  }
};
