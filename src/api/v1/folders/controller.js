const schema = require('./schema');
const logger = require('../../../services/logger.service');
const service = require('../../../services/folder');
const PrimaryMongooseConnection = require('../../../rpc/primaryMongooseConnection');

exports.getFolderViewers = async (req, res, next) => {
  try {
    const { communityId, folderId } = schema.getViewersPathSchema.cast(
      req.params
    );

    const { search, pageSize, pageNo, sortBy, sortOrder, status } =
      schema.getViewersSchema.cast(req.query);

    const result = await service.retrieveFolderViewers({
      search,
      pageSize,
      pageNo,
      sortBy,
      sortOrder,
      status: status.split(','),
      communityId,
      folderId,
    });

    res.json({ data: result });
  } catch (err) {
    logger.error('getFolderViewers failed due to', err, err.stack);
    return next(err);
  }
};

exports.createFolderViewerAndAccess = async (req, res, next) => {
  try {
    const { folderId: folderObjectId } = schema.getViewersPathSchema.cast(
      req.params
    );

    const { learnerObjectId } = schema.postViewersSchema.cast(req.body);

    const primaryMongooseConnection =
      await PrimaryMongooseConnection.connect();

    const session = await primaryMongooseConnection.startSession();
    session.startTransaction();

    try {
      await service.setupDigitalProductAccess({
        learnerObjectId,
        folderObjectId,
        session,
      });

      await session.commitTransaction();
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      await session.endSession();
    }

    return res.json({ data: { message: 'success' } });
  } catch (err) {
    logger.error(
      'createFolderViewerAndAccess failed due to',
      err,
      err.stack
    );
    return next(err);
  }
};
