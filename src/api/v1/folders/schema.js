const yup = require('yup');

exports.getViewersPathSchema = yup.object().shape({
  communityId: yup.string().required(),
  folderId: yup.string().required(),
});

exports.getViewersSchema = yup.object().shape({
  search: yup.string().uppercase().notRequired().trim(),
  pageSize: yup.number().default(100).required().integer(),
  pageNo: yup.number().default(1).required().integer(),
  sortBy: yup.string().notRequired().trim(),
  sortOrder: yup.number().default(-1).notRequired(),
  status: yup.string().uppercase().notRequired().trim().default('ALL'),
});

exports.postViewersSchema = yup.object().shape({
  learnerObjectId: yup.string().required().trim(),
});
