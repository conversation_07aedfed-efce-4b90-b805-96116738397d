const { Router } = require('express');
const schema = require('./schema');
const controller = require('./controller');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const {
  validateAll,
} = require('../../../middleware/validator.middleware');
const {
  managerCommunityValidator,
} = require('../../../communitiesAPI/validations/community.validation');
const apiKeyValidation = require('../../../validations/apiKey.validation');

const router = Router({ mergeParams: true });

router
  .route('/:folderId/viewers')
  .get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    validateAll([
      { schema: schema.getViewersSchema, location: 'query' },
      {
        schema: schema.getViewersPathSchema,
        location: 'params',
      },
    ]),
    controller.getFolderViewers
  )
  .post(
    postRoutePreHandlerMiddleware,
    apiKeyValidation,
    validateAll([
      { schema: schema.postViewersSchema, location: 'body' },
      {
        schema: schema.getViewersPathSchema,
        location: 'params',
      },
    ]),
    controller.createFolderViewerAndAccess
  );

module.exports = router;
