const service = require('./service');
const TOKEN_EXPIRY = require('./constant');
const logger = require('../../../services/logger.service');

exports.generateToken = async (req, res, next) => {
  const communityObjectId = req.params.communityId;
  const email = req.user.email;
  const learnerObjectId = req.user.learner._id;

  const payload = {
    communityObjectId,
    email,
    learnerObjectId,
  };

  logger.info(`generateToken: payload: ${JSON.stringify(payload)}`);

  const token = await service.generateToken(payload, TOKEN_EXPIRY);

  return { token: token };
};
