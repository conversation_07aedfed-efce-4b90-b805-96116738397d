const jwt = require('jsonwebtoken');
const { CONFIG_TYPES } = require('../../../constants/common');
const {
  getConfigByTypeFromCache,
} = require('../../../services/config.service');

exports.generateToken = async (payload, expiry) => {
  const { envVarData = null } = await getConfigByTypeFromCache(
    CONFIG_TYPES.LEARN_PORTAL_BACKEND_ENV_CONFIG_TYPE
  );

  const secretKey = envVarData?.JWT_EXPORT_TOKEN_SECRET_KEY;

  if (!secretKey) {
    throw new Error('Missing key');
  }

  const token = jwt.sign(payload, secretKey, {
    expiresIn: expiry,
  });

  return token;
};
