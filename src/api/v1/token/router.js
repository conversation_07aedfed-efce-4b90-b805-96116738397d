const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');
const controller = require('./controller');
const { handlerWrapper } = require('../../../utils/request.util');
const tokenValidator = require('../../../validations/token.validation');
const userValidation = require('../../../validations/user.validation');
const {
  managerCommunityValidator,
} = require('../../../communitiesAPI/validations/community.validation');

exports.setupRouter = function (router) {
  router.route('/:communityId/token').get(
    postRoutePreHandlerMiddleware,
    tokenValidator(),
    userValidation,
    managerCommunityValidator,
    handlerWrapper({
      handler: controller.generateToken,
      wrapResponseInObject: false,
    })
  );
};
