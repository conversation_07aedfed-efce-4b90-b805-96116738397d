const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');

const tokenValidator = require('../../../validations/token.validation');

const { handlerWrapper } = require('../../../utils/request.util');

const userController = require('./controller');

const setupCOPSRouter = function (router) {
  router
    .route('/user/change-email-address')
    .post(
      postRoutePreHandlerMiddleware,
      handlerWrapper({ handler: userController.changeUserEmailAddress })
    );

  router
    .route('/user/remove-presence')
    .post(
      postRoutePreHandlerMiddleware,
      handlerWrapper({ handler: userController.removePresence })
    );
};

const setupRouter = function (router) {
  setupCOPSRouter(router);
};

module.exports = {
  setupRouter,
};
