const updateUserService = require('../../../services/user/updateUser.service');
const memberService = require('../../../services/membership/count.service');
const textSanitizerUtils = require('../../../utils/textSanitizer.util');

const changeUserEmailAddress = async (req, res, next) => {
  return updateUserService.changeUserEmailAddress({
    email: req.body.email.trim(),
    newEmail: textSanitizerUtils.removeInvisibleChars(
      req.body.newEmail.toLowerCase().trim()
    ),
  });
};

const getCommunityMemberCount = async (req, res, next) => {
  await memberService.countCommunityMembers({
    communityId: req.params.communityId,
  });
};

const removePresence = async (req, res, next) => {
  return updateUserService.removePresence({
    email: req.body.email.trim(),
  });
};

module.exports = {
  changeUserEmailAddress,
  getCommunityMemberCount,
  removePresence,
};
