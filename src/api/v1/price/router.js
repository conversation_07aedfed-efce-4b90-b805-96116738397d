const { Router } = require('express');
const schema = require('./schema');
const controller = require('./controller');
const {
  postRoutePreHandlerMiddleware,
} = require('../../../middleware/request.middleware');

const { handlerWrapper } = require('../../../utils/request.util');

const router = Router({ mergeParams: true });

router.route('/prices-calculation').get(
  postRoutePreHandlerMiddleware,
  handlerWrapper({
    handler: controller.retrievePrices,
    requestValidators: {
      params: schema.communityIdSchema,
      query: schema.priceCalculationSchema,
    },
  })
);

module.exports = router;
