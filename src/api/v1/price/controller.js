const schema = require('./schema');
const service = require('../../../services/pricing/priceCalculator.service');

exports.retrievePrices = async (req) => {
  const { communityId: communityObjectId } = schema.communityIdSchema.cast(
    req.params
  );

  const { itemPrice, isPassOn, entityType } =
    schema.priceCalculationSchema.cast(req.query);

  const parseIsPassOn = (value) => {
    if (value === true || value === 'true') return true;
    if (value === false || value === 'false') return false;
    return null;
  };

  const planPrices = await service.calculatePrice({
    communityObjectId,
    itemPrice,
    isPassOn: parseIsPassOn(isPassOn),
    entityType,
  });

  return planPrices;
};
