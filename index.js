require('module-alias/register');

const http = require('http');
const { app, setupApp } = require('./app');
const connectionTracker = require('./src/monitoring/connectionTracker');

const PORT = process.env.PORT || 3000;

setupApp()
  .then(() => {
    const server = http.createServer(app);

    connectionTracker.trackConnections(server);

    server.listen(PORT, () => {
      console.log(`Listening on ${PORT}!!`);
    });
  })
  .catch((error) => {
    console.log(`Failed to setup app: ${error} ${error.stack}`);
    process.exit();
  });
