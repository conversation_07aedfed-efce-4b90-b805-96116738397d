#!/bin/bash

# EBANX Whitelist API Test Script (Simple & Robust)
# Tests the POST /api/v1/ebanx-whitelist/resolve-product endpoint

BASE_URL="http://localhost:3003"
API_ENDPOINT="/api/v1/ebanx-whitelist/resolve-product"

echo "🚀 Testing EBANX Whitelist API"
echo "=============================="

# Check if server is running
echo "🔍 Checking if server is running..."
if ! curl -s "$BASE_URL" > /dev/null; then
    echo "❌ Server is not running at $BASE_URL"
    echo "   Please start the server first: npm run dev"
    exit 1
fi
echo "✅ Server is running"

# Generate JWT token
echo "🔑 Generating JWT token..."
TOKEN=$(node generate-jwt.js 2>/dev/null)
if [ $? -ne 0 ] || [ -z "$TOKEN" ]; then
    echo "❌ Failed to generate JWT token"
    echo "   Please ensure Node.js is installed and generate-jwt.js exists"
    exit 1
fi
echo "✅ JWT token generated"

# Simple test function
test_api() {
    local test_name="$1"
    local product_link="$2"
    local expected_community_code="$3"
    local expected_product_type="$4"
    
    echo ""
    echo "📋 Test: $test_name"
    echo "   URL: $product_link"
    echo "   Expected Community Code: $expected_community_code"
    echo "   Expected Product Type: $expected_product_type"
    echo "   ---"
    
    # Create temporary files for response
    response_file=$(mktemp)
    
    # Make the API call
    http_code=$(curl -s -w "%{http_code}" -o "$response_file" -X POST \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $TOKEN" \
        -d "{\"productLink\":\"$product_link\"}" \
        "$BASE_URL$API_ENDPOINT")
    
    response_body=$(cat "$response_file")
    rm "$response_file"
    
    echo "   HTTP Status: $http_code"
    echo "   Response: $response_body"
    
    # Check results
    if [ "$http_code" = "200" ]; then
        echo "   ✅ HTTP Status: PASS"
        
        if echo "$response_body" | grep -q "\"communityCode\":\"$expected_community_code\""; then
            echo "   ✅ Community Code: PASS"
        else
            echo "   ❌ Community Code: FAIL"
        fi
        
        if echo "$response_body" | grep -q "\"productType\":\"$expected_product_type\""; then
            echo "   ✅ Product Type: PASS"
        else
            echo "   ❌ Product Type: FAIL"
        fi
        
        if echo "$response_body" | grep -q "\"productId\":"; then
            echo "   ✅ Product ID: PASS"
        else
            echo "   ❌ Product ID: FAIL"
        fi
    else
        echo "   ❌ HTTP Status: FAIL (Expected 200, got $http_code)"
    fi
}

# Test error cases
test_error() {
    local test_name="$1"
    local product_link="$2"
    local expected_error="$3"
    
    echo ""
    echo "📋 Test: $test_name"
    echo "   URL: $product_link"
    echo "   Expected Error: $expected_error"
    echo "   ---"
    
    response_file=$(mktemp)
    
    http_code=$(curl -s -w "%{http_code}" -o "$response_file" -X POST \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $TOKEN" \
        -d "{\"productLink\":\"$product_link\"}" \
        "$BASE_URL$API_ENDPOINT")
    
    response_body=$(cat "$response_file")
    rm "$response_file"
    
    echo "   HTTP Status: $http_code"
    echo "   Response: $response_body"
    
    if echo "$response_body" | grep -q "$expected_error"; then
        echo "   ✅ Error Message: PASS"
    else
        echo "   ❌ Error Message: FAIL"
    fi
}

# First, let's test a simple endpoint to make sure the server is working
echo ""
echo "🔍 Testing server connectivity..."
response_file=$(mktemp)
server_test=$(curl -s -w "%{http_code}" -o "$response_file" "$BASE_URL/api/v1/server-time" 2>/dev/null)
server_response=$(cat "$response_file")
rm "$response_file"

echo "   Server time endpoint: $server_test"
echo "   Response: $server_response"

if [ "$server_test" = "200" ]; then
    echo "   ✅ Server is responding correctly"
else
    echo "   ❌ Server is not responding correctly"
fi

echo ""
echo "🔍 Testing EBANX Whitelist API endpoint..."

# Test 1: EVENT type - Creator Community
test_api \
    "Event - Creator Community" \
    "/creators-lalala/events/swae" \
    "NAS_DAILY_CREATOR_COMMUNITY" \
    "EVENT"

# Test 2: DIGITAL_FILES type - Free Content Community
test_api \
    "Digital Files - Free Content Community" \
    "/freecontentcommunity/products/tbvw" \
    "NAS_IO_FREE_CONTENT_COMMUNITY" \
    "DIGITAL_FILES"

# Test 3: CHALLENGE type - Dont Stop Believing Community
test_api \
    "Challenge - Dont Stop Believing Community" \
    "/dont-stop-believing/challenges/geekout-fake" \
    "DONT_STOP_BELIEVING" \
    "CHALLENGE"

echo ""
echo "🧪 Testing error cases..."

# Test 4: Invalid URL format
test_error \
    "Invalid URL Format" \
    "/invalid/format" \
    "Invalid product URL format"

# Test 5: Non-existent community
test_error \
    "Non-existent Community" \
    "/non-existent-community/products/test" \
    "Community not found"

# Test 6: Invalid product type
test_error \
    "Invalid Product Type" \
    "/creators-lalala/invalid-type/test" \
    "Invalid product type path"

echo ""
echo "🔐 Testing authentication..."

# Test 7: Missing Authorization
echo ""
echo "📋 Test: Missing Authorization"
echo "   Expected: Authentication error"
echo "   ---"

response_file=$(mktemp)
auth_test=$(curl -s -w "%{http_code}" -o "$response_file" -X POST \
    -H "Content-Type: application/json" \
    -d '{"productLink":"/creators-lalala/events/swae"}' \
    "$BASE_URL$API_ENDPOINT")

auth_response=$(cat "$response_file")
rm "$response_file"

echo "   HTTP Status: $auth_test"
echo "   Response: $auth_response"

if [ "$auth_test" = "401" ]; then
    echo "   ✅ Authentication check: PASS"
else
    echo "   ❌ Authentication check: FAIL (Expected 401, got $auth_test)"
fi

echo ""
echo "🎯 Test Summary Complete!"
echo "========================"
echo "📝 Tested real data from MongoDB database"
echo "🔐 Used COPS JWT authentication"
echo "🌐 API endpoint: $BASE_URL$API_ENDPOINT"
echo ""
echo "💡 If tests are failing:"
echo "   1. Check that the server is running: npm run dev"
echo "   2. Check the server logs for errors"
echo "   3. Verify the API endpoint is correctly implemented"
echo "   4. Check that the MongoDB connection is working"