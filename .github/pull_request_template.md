### Changes 
#### Summary (1 sentence minimum)


#### Checkpoints (check if applicable)
- Changes to API schema
  - [ ] Not applicable
  - [ ] Are all your API schema backward compatible (no rename, no type changing, no deletion)
- Changes to services
  - [ ] Not applicable
  - [ ] Have you tested all the flows that your services' changes affect?
- Changes to models
  - [ ] Not applicable
  - [ ] Are all your model schema changes backward compatible (no rename, no type changing, no deletion)
- Changes to remote calls
  - [ ] Not applicable
  - [ ] Have you handled all the possible response that the external services can return
- Changes to common code
  - [ ] Not applicable
  - [ ] Have you tested all the flows that your common code changes can affect

### Operation check points
- [ ] Full self-test end-to-end
- [ ] Full self-test at API-level
- [ ] Partial self-test (not all related flows tested)
- [ ] Updated necessary config (env, DB)
