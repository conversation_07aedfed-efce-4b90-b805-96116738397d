name: run-lint-nodejs-action
on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - develop
jobs:
  build:
    runs-on: ubuntu-latest
    timeout-minutes: 3
    strategy:
      matrix:
        node-version: [ 14.x ]
    steps:
      - uses: actions/checkout@v2
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v2
        with:
          node-version: ${{ matrix.node-version }}
      - name: Install Modules
        run: npm ci
      - name: Run Lint
        run: npm run lint