/* eslint-disable no-unused-vars */
require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

// TODO fix the setup, models should not be required in this file
require('./src/models/courseReferralScheme.model');
require('./src/models/referralTransaction.model');
require('./src/models/units.model');
require('./src/models/subunits.model');
const express = require('express');
const helmet = require('helmet');

const { spawnSync } = require('child_process');
const { register } = require('prom-client');
const rateLimit = require('express-rate-limit');
const httpContext = require('express-http-context');
const { v1 } = require('uuid');

const mongoClient = require('./src/mongoClient');

// NOTE: this has to be called before any mongoose models are required (which is in routes)
mongoClient.instrumentMongoose();

const routes = require('./src/routes/routes');
const { redisClient } = require('./src/redisClient');
const awsRedisClient = require('./src/awsRedisClient');
const {
  NO_OF_PROXIES,
  RATELIMIT,
  RATELIMIT_WINDOW,
  RATELIMIT_REQUESTS,
  RATELIMIT_MESSAGE,
  THIRTY_SEC_REFRESH_RATE,
  ONE_MIN_REFRESH_RATE,
  ONE_DAY_REFRESH_RATE,
  FIVE_MIN_REFRESH_RATE,
  IS_LOCAL,
} = require('./src/config');

//For logging service
const {
  correlationIdMiddleware,
} = require('./src/middleware/correlationId.middleware');
const logger = require('./src/services/logger.service');
const {
  setupMorganMiddleware,
} = require('./src/middleware/morgan.middleware');
const {
  responseBodyMiddleware,
} = require('./src/middleware/responseBody.middleware');
const {
  requestMetricsMiddleware,
} = require('./src/middleware/request.middleware');
const { cors } = require('./src/middleware/cors.middleware');
const metricRegistry = require('./src/monitoring/metricRegistry');
const {
  getConfigByType,
  refreshConfigByTypeInCache,
} = require('./src/services/config.service');
const stripeClient = require('./src/clients/stripe.client');
const errorUtil = require('./src/utils/error.util');
const {
  buildCountryInfoMemoryCache,
} = require('./src/services/countryInfoMapping/countryInfoMapping.service');
const fraudService = require('./src/services/fraud');
const currencyService = require('./src/services/currency/retrieveSupportedCurrencies.service');
const { CONFIG_TYPES } = require('./src/constants/common');

const getCurrentDiskSpaceUsedPercentage = () => {
  try {
    const result = spawnSync('df', ['/']);
    const lines = result.output.toString().split('\n');
    const line = lines[1].replace(/\s\s+/g, ' ').split(' ');
    return line[4].replace(/%/g, '');
  } catch (error) {
    return 0;
  }
};

const app = express();

function setupLimit() {
  app.use(express.json({ limit: '20mb' }));

  let limiter;
  if (RATELIMIT) {
    limiter = rateLimit({
      windowMs: RATELIMIT_WINDOW,
      max: RATELIMIT_REQUESTS,
      message: RATELIMIT_MESSAGE,
    });
  }

  if (RATELIMIT) {
    app.use(limiter);
  }
}

function setupContext() {
  app.use(httpContext.middleware);

  // This replaces your old manual correlation ID logic
  app.use(correlationIdMiddleware);

  app.use((req, res, next) => {
    httpContext.ns.bindEmitter(req);
    httpContext.ns.bindEmitter(res);
    // var requestId = req.headers["x-request-id"] || uuidv4();
    if (req.headers['correlationid']) {
      httpContext.set('reqId', req.headers['correlationid']);
    } else {
      httpContext.set('reqId', v1());
    }

    if (req.headers['cloudfront-viewer-country']) {
      httpContext.set(
        'cloudfront-viewer-country',
        req.headers['cloudfront-viewer-country']
      );
    }
    httpContext.set('reqIp', req.headers['x-forwarded-for']);
    next();
  });
}

function setupHealthcheck() {
  app.get('/ping', async (req, res) => {
    const serverTime = new Date().toString();
    res.status(200).json({
      message: `You have successfully pinged the server! ${serverTime} `,
    });
  });

  app.get('/ip', async (req, res) => {
    res.send(req.ip);
  });
}

async function setupInMemoryCache() {
  await buildCountryInfoMemoryCache();
  setInterval(buildCountryInfoMemoryCache, ONE_DAY_REFRESH_RATE);

  await refreshConfigByTypeInCache(
    CONFIG_TYPES.LEARN_PORTAL_BACKEND_ENV_CONFIG_TYPE
  );
  setInterval(
    refreshConfigByTypeInCache,
    ONE_MIN_REFRESH_RATE,
    CONFIG_TYPES.LEARN_PORTAL_BACKEND_ENV_CONFIG_TYPE
  );

  await refreshConfigByTypeInCache(
    CONFIG_TYPES.INDIA_PAYMENT_WHITELIST_CONFIG_TYPE
  );
  setInterval(
    refreshConfigByTypeInCache,
    THIRTY_SEC_REFRESH_RATE,
    CONFIG_TYPES.INDIA_PAYMENT_WHITELIST_CONFIG_TYPE
  );
  // TODO: Enable when ready to launch
  await refreshConfigByTypeInCache(CONFIG_TYPES.PAYMENT_FEE_STRUCTURE);
  setInterval(
    refreshConfigByTypeInCache,
    ONE_MIN_REFRESH_RATE,
    CONFIG_TYPES.PAYMENT_FEE_STRUCTURE
  );

  // TODO: Enable for passon phase 2
  await refreshConfigByTypeInCache(
    CONFIG_TYPES.PASSON_PAYMENT_GATEWAY_CONFIG_TYPE
  );
  setInterval(
    refreshConfigByTypeInCache,
    ONE_DAY_REFRESH_RATE,
    CONFIG_TYPES.PASSON_PAYMENT_GATEWAY_CONFIG_TYPE
  );

  await errorUtil.buildErrorMessageCache();
  setInterval(errorUtil.buildErrorMessageCache, ONE_DAY_REFRESH_RATE);

  await fraudService.fetchListsToCheck();
  setInterval(fraudService.fetchListsToCheck, FIVE_MIN_REFRESH_RATE);

  await fraudService.fetchFraudOpenAIPrompts();
  setInterval(fraudService.fetchFraudOpenAIPrompts, FIVE_MIN_REFRESH_RATE);

  await currencyService.refreshCache();
  setInterval(currencyService.refreshCache, ONE_DAY_REFRESH_RATE);
}

// function setupMorgan() {
//   morgan.token(':remote-addr', function getIp(req) {
//     return req?.headers['X-Forwarded-For'];
//   });
//
//   morgan.token('full-url', (req, res) => {
//     return `${req?.protocol}://${req.get('host')}${req?.originalUrl}`;
//   });
//
//   morgan.token('req-body', (req, res) => {
//     return JSON.stringify(req?.body);
//   });
//
//   morgan.token('req-query', (req, res) => {
//     return JSON.stringify(req?.query);
//   });
//
//   morgan.token('req-params', (req, res) => {
//     return JSON.stringify(req?.params);
//   });
//
//   morgan.token('api-route', (req, res) => {
//     let apiRoute = req.originalUrl.split('?')[0];
//
//     Object.entries(req.params).forEach(([key, value]) => {
//       apiRoute = apiRoute.replace(value, `:${key}`);
//     });
//
//     return apiRoute;
//   });
//
//   app.use(
//     morgan(
//       ':method|:api-route|:url|:full-url|HTTP/:http-version|:status|:res[content-length]|:referrer|:user-agent|:remote-addr|:req[header]|:req-query|:req-params|:req-body|:res[header]|:res-body|:response-time',
//       { stream: winston?.stream }
//     )
//   );
// }

function setupMonitoring() {
  metricRegistry.registerMetrics();
  app.use(requestMetricsMiddleware);
  app.get('/metrics', async (req, res) => {
    res.setHeader('Content-Type', register.contentType);
    const metrics = await register.metrics();
    metricRegistry.clearNecessaryMetricsAfterScrape();
    res.status(200).send(metrics);
  });
}

function setupRouter() {
  metricRegistry.registerMetrics();
  app.use(requestMetricsMiddleware);
  app.use('/api/v1', routes);
  app.get('/metrics', async (req, res) => {
    res.setHeader('Content-Type', register.contentType);
    const metrics = await register.metrics();
    // metricRegistry.clearNecessaryMetricsAfterScrape();
    res.status(200).send(metrics);
  });
  setupHealthcheck();

  app.use((req, res, next) => {
    const url = `${req.protocol}://${req.get('host')}${req.originalUrl}`;
    logger.error(`Route not found and url: ${url}`);
    const error = new Error('Route not found');
    error.status = 404;
    next(error);
  });

  app.use((error, req, res, next) => {
    const status = error.status || 500;
    return res.status(status).json({
      errorCode: error.errorCode ?? -1,
      errorMessage: error.message,
      error: error.error ?? error.name ?? 'INTERNAL_SERVER_ERROR',
      localizationDetails: error.localizationDetails,
      errorData: error.errorData,
    });
  });
}

const setupApp = async () => {
  app.enable('trust proxy');
  setupLimit();
  app.use(responseBodyMiddleware);
  setupContext();
  app.use(helmet());
  app.use(cors);
  await mongoClient.connect();

  await redisClient.connect();
  if (!IS_LOCAL) {
    await awsRedisClient.connectClient();
  }
  await setupInMemoryCache();
  await stripeClient.init();

  // setup morgan middleware with correlationId and maskSensitiveData
  setupMorganMiddleware(app);

  setupRouter();
};

module.exports = {
  app,
  setupApp,
};
