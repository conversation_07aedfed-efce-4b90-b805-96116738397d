{"name": "nasacademy-backend", "version": "0.0.1", "engines": {"node": "14.x"}, "description": "Backend for the NasAcademy Portal", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "NODE_ENV='test' jest --runInBand --config ./jest.config.json", "test:integration": "npm run test integration/", "test:unit": "npm run test unit/", "test:watch": "npm run test -- --watch", "test:create": "node scripts/generateTest.js", "test:coverage": "npm run test -- --coverage --collectCoverageFrom='src/**/*.js'", "lint": "eslint .", "lint:fix": "eslint --fix --ext .js .", "format": "prettier --check .", "format:fix": "prettier --write .", "fix:paidToFree:dev": "NODE_ENV=development node scripts/convertPaidMemberToFree.js", "fix:paidToFree:prod": "NODE_ENV=production node scripts/convertPaidMemberToFree.js", "fix:ticketReference:dev": "NODE_ENV=development node scripts/updateEventAttendeeTicketReference.js", "fix:ticketReference:prod": "NODE_ENV=production node scripts/updateEventAttendeeTicketReference.js", "fix:payoutCurrency:dev": "NODE_ENV=development node scripts/convertPayoutCurrency.js", "fix:payoutCurrency:prod": "NODE_ENV=production node scripts/convertPayoutCurrency.js", "fix:updateWalletFundmovements:dev": "NODE_ENV=development node scripts/updateWalletFundmovements.js", "sync:payoutCurrency": "node scripts/syncPayoutCurrency.js", "add:adjustment": "node scripts/addAdjustmentTransaction.js", "update:fee": "node scripts/updateFeeConfigs.js", "update:customFee": "node scripts/updateCustomFeeConfigsForPro.js", "migrate:challenge": "node scripts/migrateChallengesToAnotherCommunity.js", "send:paypalReminder": "node scripts/sendPaypalResubscribeReminderMail.js", "sync:unifiedProductData": "node scripts/syncAllProductData.js", "check:circular": "madge --circular src"}, "lint-staged": {"src/**/*.js": "eslint --cache"}, "husky": {"hooks": {"pre-commit": "npm run check:circular && lint-staged"}}, "repository": {"type": "git", "url": "git+https://github.com/The-Nas-Company/nasacademy-backend.git"}, "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/The-Nas-Company/nasacademy-backend/issues"}, "homepage": "https://github.com/The-Nas-Company/nasacademy-backend#readme", "dependencies": {"@aws-sdk/client-scheduler": "^3.699.0", "agentkeepalive": "^4.3.0", "apple-signin-auth": "^1.5.1", "aws-cloudfront-sign": "^3.0.2", "aws-sdk": "^2.1033.0", "axios": "^0.21.4", "bcrypt": "^5.0.0", "body-parser": "^1.19.0", "cheerio": "^1.0.0", "cors": "^2.8.5", "dd-trace": "^0.35.0", "dompurify": "^3.2.4", "dotenv": "^8.6.0", "express": "^4.17.1", "express-http-context": "^1.2.4", "express-rate-limit": "^5.2.3", "extend": "^3.0.2", "fbgraph": "^1.4.4", "file-type": "^19.0.0", "fs": "0.0.1-security", "getstream": "^8.0.0", "google-auth-library": "^7.14.1", "google-libphonenumber": "^3.2.32", "handlebars": "^4.7.7", "helmet": "^3.23.2", "hot-shots": "^8.5.1", "http-status": "^1.4.2", "ics": "^2.35.0", "immutable": "^5.0.0-beta.4", "jsdom": "^26.0.0", "jsonwebtoken": "^8.5.1", "klaviyo-api": "^13.0.0", "lodash": "^4.17.21", "luxon": "^1.28.1", "madge": "^7.0.0", "mime": "^2.5.2", "mime-types": "^2.1.35", "module-alias": "^2.2.3", "moment": "^2.30.1", "moment-timezone": "^0.5.47", "mongoose": "^7.6.5", "morgan": "^1.10.0", "multer": "^1.4.2", "multer-s3": "^2.10.0", "nanoid": "^3.3.1", "node-cache": "^5.1.2", "node-klaviyo": "^1.1.3", "nodemailer": "^6.6.1", "nodemon": "^2.0.19", "on-finished": "^2.4.1", "on-headers": "^1.0.2", "openai": "^5.3.0", "pdfkit": "^0.16.0", "phone": "^3.1.36", "pluralize": "^8.0.0", "prom-client": "^14.2.0", "qrcode": "^1.5.4", "redis": "^4.0.0", "redis-lock": "^1.0.0", "sharp": "^0.34.1", "stripe": "^12.6.0", "unsplash-js": "^7.0.19", "util": "^0.12.3", "uuid": "^8.3.2", "winston": "^3.3.3", "xendit-node": "^1.21.11", "yup": "^0.29.3", "zod": "^3.25.28"}, "_moduleAliases": {"@": ".", "@src": "./src", "@models": "./src/models", "@services": "./src/services", "@controllers": "./src/controllers", "@middlewares": "./src/middlewares", "@utils": "./src/utils", "@config": "./src/config", "@helpers": "./src/helpers", "@constants": "./src/constants"}, "devDependencies": {"airbnb": "0.0.2", "eslint": "^7.3.1", "eslint-config-airbnb-base": "^14.2.0", "eslint-config-prettier": "^6.11.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.1.4", "faker": "^5.5.3", "form-data": "^4.0.2", "husky": "^4.3.8", "jest": "^27.0.6", "lint-staged": "^11.1.2", "mongodb-memory-server": "^7.3.6", "node-fetch": "^2.7.0", "prettier": "2.3.2", "readline-sync": "^1.4.10", "supertest": "^6.1.6", "web-streams-polyfill": "^3.2.1"}}