#!/bin/bash

# EBANX Whitelist API Test Script
# Tests the POST /api/v1/ebanx-whitelist/resolve-product endpoint

BASE_URL="http://localhost:3003"
API_ENDPOINT="/api/v1/ebanx-whitelist/resolve-product"
JWT_SECRET="youShallNotPass_Cops1!"

echo "🚀 Testing EBANX Whitelist API"
echo "================================"

# Generate JWT token for COPS authentication
# Note: This is a simplified token generation. In production, use proper JWT library
generate_jwt() {
    # For testing, we'll create a simple token. In production, use proper JWT libraries
    echo "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkNPUFMgVXNlciIsImlhdCI6MTUxNjIzOTAyMn0.signature"
}

TOKEN=$(generate_jwt)

# Test function
test_api() {
    local test_name="$1"
    local product_link="$2"
    local expected_community_code="$3"
    local expected_product_type="$4"
    
    echo ""
    echo "📋 Test: $test_name"
    echo "   URL: $product_link"
    echo "   Expected Community Code: $expected_community_code"
    echo "   Expected Product Type: $expected_product_type"
    echo "   ---"
    
    response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $TOKEN" \
        -d "{\"productLink\":\"$product_link\"}" \
        "$BASE_URL$API_ENDPOINT")
    
    echo "   Response: $response"
    
    # Check if response contains expected values
    if echo "$response" | grep -q "\"communityCode\":\"$expected_community_code\""; then
        echo "   ✅ Community Code: PASS"
    else
        echo "   ❌ Community Code: FAIL"
    fi
    
    if echo "$response" | grep -q "\"productType\":\"$expected_product_type\""; then
        echo "   ✅ Product Type: PASS"
    else
        echo "   ❌ Product Type: FAIL"
    fi
    
    if echo "$response" | grep -q "\"productId\":"; then
        echo "   ✅ Product ID: PASS"
    else
        echo "   ❌ Product ID: FAIL"
    fi
    
    echo ""
}

# Test cases with real data from the database

echo "🔍 Testing with real data from MongoDB..."

# Test 1: EVENT type - Creator Community
test_api \
    "Event - Creator Community" \
    "/creators-lalala/events/swae" \
    "NAS_DAILY_CREATOR_COMMUNITY" \
    "EVENT"

# Test 2: DIGITAL_FILES type - Free Content Community  
test_api \
    "Digital Files - Free Content Community" \
    "/freecontentcommunity/products/tbvw" \
    "NAS_IO_FREE_CONTENT_COMMUNITY" \
    "DIGITAL_FILES"

# Test 3: CHALLENGE type - Dont Stop Believing Community
test_api \
    "Challenge - Dont Stop Believing Community" \
    "/dont-stop-believing/challenges/geekout-fake" \
    "DONT_STOP_BELIEVING" \
    "CHALLENGE"

# Test 4: DIGITAL_FILES with digital-files path
test_api \
    "Digital Files (digital-files path)" \
    "/freecontentcommunity/digital-files/aoqb" \
    "NAS_IO_FREE_CONTENT_COMMUNITY" \
    "DIGITAL_FILES"

echo "🧪 Testing error cases..."

# Test 5: Invalid URL format
echo ""
echo "📋 Test: Invalid URL Format"
echo "   URL: /invalid/format"
echo "   Expected: Error response"
echo "   ---"

response=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $TOKEN" \
    -d '{"productLink":"/invalid/format"}' \
    "$BASE_URL$API_ENDPOINT")

echo "   Response: $response"

if echo "$response" | grep -q "error"; then
    echo "   ✅ Error handling: PASS"
else
    echo "   ❌ Error handling: FAIL"
fi

# Test 6: Non-existent community
echo ""
echo "📋 Test: Non-existent Community"
echo "   URL: /non-existent-community/products/test"
echo "   Expected: Community not found error"
echo "   ---"

response=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $TOKEN" \
    -d '{"productLink":"/non-existent-community/products/test"}' \
    "$BASE_URL$API_ENDPOINT")

echo "   Response: $response"

if echo "$response" | grep -q "Community not found"; then
    echo "   ✅ Community not found: PASS"
else
    echo "   ❌ Community not found: FAIL"
fi

# Test 7: Invalid product type
echo ""
echo "📋 Test: Invalid Product Type"
echo "   URL: /creators-lalala/invalid-type/test"
echo "   Expected: Invalid product type error"
echo "   ---"

response=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $TOKEN" \
    -d '{"productLink":"/creators-lalala/invalid-type/test"}' \
    "$BASE_URL$API_ENDPOINT")

echo "   Response: $response"

if echo "$response" | grep -q "Invalid product type"; then
    echo "   ✅ Invalid product type: PASS"
else
    echo "   ❌ Invalid product type: FAIL"
fi

# Test 8: Missing Authorization
echo ""
echo "📋 Test: Missing Authorization"
echo "   Expected: Authentication error"
echo "   ---"

response=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -d '{"productLink":"/creators-lalala/events/swae"}' \
    "$BASE_URL$API_ENDPOINT")

echo "   Response: $response"

if echo "$response" | grep -q "Unauthorized\|Authentication"; then
    echo "   ✅ Authentication check: PASS"
else
    echo "   ❌ Authentication check: FAIL"
fi

echo ""
echo "🎯 Test Summary Complete!"
echo "================================"
echo "📝 All tests use real data from the MongoDB database"
echo "🔐 Authentication uses COPS JWT token"
echo "🌐 API endpoint: $BASE_URL$API_ENDPOINT"
echo ""
echo "💡 To run this test:"
echo "   1. Make sure the server is running on localhost:3003"
echo "   2. Run: chmod +x test.sh && ./test.sh"