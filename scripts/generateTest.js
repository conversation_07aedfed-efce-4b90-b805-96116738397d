/**
 * Scripts to help auto-generate test skeletons
 */

const fs = require('fs');
// eslint-disable-next-line import/no-unresolved
// eslint-disable-next-line import/no-extraneous-dependencies
const readline = require('readline-sync');
const getDirName = require('path').dirname;

/**
 * Returns test file name
 *
 * @param  {String} type
 * @param  {String} filename
 */
function getTestFilename(type, filename) {
  return `./tests/${type}/${filename.slice(7, -3)}.test.js`;
}

/**
 * Writes a file to disk synchronously
 *
 * @param  {String} path
 * @param  {String} contents
 * @param  {Function} cb
 * @returns void
 */
function writeFile(path, contents, cb) {
  fs.mkdir(getDirName(path), { recursive: true }, function (err) {
    if (err) return cb(err);

    fs.writeFile(path, contents, cb);
  });
}

/**
 * Returns a `describe` block
 *
 * @param  {String}} name
 * @param  {String} contents
 * @returns {String}
 */
function renderDescribeBlock(name, contents) {
  return `
        describe('${name}', () => {
            ${contents}
        }); 
  `;
}

/**
 * Returns a `test` block
 *
 * @param  {String} functionName
 * @param  {String} state
 * @param  {String} expectation
 * @returns {String}
 */
function renderTestBlock(functionName, state, expectation) {
  return `
        test('when ${state}, should ${expectation}', () => {
            const input = '';
            const expected = '';

            const result = ${functionName}(input);
            expect(result).toBe(expected);
        });
    `;
}

/**
 * Returns a set of test testBlocks
 * @param  {Array} cases
 * @returns {String}
 */
function renderAllTestBlocks(name, cases) {
  if (Array.isArray(cases)) {
    return cases.map((value) => renderTestBlock(name, ...value));
  }

  return [];
}

function getFunctionTestCases(name) {
  const funcObject = { name, cases: [] };

  let addAnother = true;
  while (addAnother) {
    console.log(`\nAdding testcase for ${name},`);
    const state = readline.question('When- ');
    const expect = readline.question('Should- ');
    funcObject.cases.push([state, expect]);

    addAnother = readline.question(`\nAdd another testcase for ${name}? `);
  }

  return funcObject;
}

/**
 * Returns test file contents for a module
 * @param  {Object} module
 * @returns {String}
 */
function getTestContents(filepath, module) {
  // get test name from user
  let testname = readline.question('Name of the test? ');

  const funcs = [];
  const functionNames =
    typeof module === 'object' ? Object.keys(module) : [module.name];
  functionNames.forEach((functionName) => {
    const funcObject = getFunctionTestCases(functionName);
    funcs.push(funcObject);
  });

  const testBlocks = funcs.map(({ name, cases }) => {
    return renderDescribeBlock(
      name,
      renderAllTestBlocks(name, cases).join('\n')
    );
  });

  const importString = `const { ${functionNames.join(
    ' ,'
  )} } = require('${filepath.slice(-3)}')\n`;
  const testString = renderDescribeBlock(testname, testBlocks.join('\n'));

  return importString + testString;
}

/**
 * Returns test file content
 *
 * @param  {String} filename
 * @param  {String: 'unit' | 'integration'} type
 */
function makeTestFile(filepath, type) {
  if (!filepath) {
    console.error('Unknown file');
    return;
  }

  // load module
  // eslint-disable-next-line import/no-dynamic-require
  const module = require(filepath); // eslint-disable-line global-require
  const specFilename = getTestFilename(type, filepath);

  // check if test file exists
  // TODO
  const hasTestFile = false;
  if (hasTestFile) {
    console.warn('Test already exists!');
    return;
  }

  // get test contents
  const testString = getTestContents(filepath, module);

  // create spec file
  writeFile(specFilename, testString, function (err) {
    if (err) return console.log(err);
    console.log('Generated test: ', specFilename);
  });
}

const filename = process.argv[2];
const type = process.argv[3] || 'unit';
makeTestFile(filename, type);
