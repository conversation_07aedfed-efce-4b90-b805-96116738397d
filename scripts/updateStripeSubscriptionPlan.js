require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});
const fs = require('fs').promises;
const mongoose = require('mongoose');

const { ObjectId } = mongoose.Types;

const mongoClient = require('../src/mongoClient');
const CommunityModel = require('../src/communitiesAPI/models/community.model');
const stripeClientService = require('../src/clients/stripe.client');
const SubscriptionModel = require('../src/communitiesAPI/models/communitySubscriptions.model');
const PurchaseTransactionModel = require('../src/communitiesAPI/models/communityPurchaseTransactions.model');
const DiscountTransactionModel = require('../src/communitiesAPI/models/communityDiscountTransactions.model');
const MembershipModel = require('../src/models/membership/membership.model');
const DiscountModel = require('../src/communitiesAPI/models/communityDiscounts.model');

async function retrievePurchaseTransactionsCache(subscriptions) {
  const purchaseTransactionObjectIds = subscriptions.map(
    (subscription) => new ObjectId(subscription.communitySignupId)
  );

  const purchaseTransactions = await PurchaseTransactionModel.find(
    {
      _id: { $in: purchaseTransactionObjectIds },
    },
    { createdAt: 0, updatedAt: 0 }
  ).lean();

  const purchaseTransactionsCache = purchaseTransactions.reduce(
    (acc, purchaseTransaction) => {
      acc.set(purchaseTransaction._id.toString(), purchaseTransaction);
      return acc;
    },
    new Map()
  );

  return purchaseTransactionsCache;
}

async function addNewDiscountTransaction(purchaseTransaction, discount) {
  const {
    _id: purchaseTransactionObjectId,
    applyDiscount,
    discountTransactionId,
    community_code: communityCode,
  } = purchaseTransaction;

  if (!applyDiscount && !discount) {
    return;
  }

  console.log(`Add new discount transaction: ${discountTransactionId}`);

  if (discount) {
    const discountTransaction = {
      purchaseTransactionObjectId,
      communityCode,
      communityDiscountObjectId: discount._id,
      status: 1,
    };

    const createdDiscountTransaction =
      await DiscountTransactionModel.create(discountTransaction);

    return createdDiscountTransaction.toObject();
  }
  const discountTransaction = await DiscountTransactionModel.findById(
    discountTransactionId
  ).lean();

  const duplicateDiscountTransaction = {
    ...discountTransaction,
    purchaseTransactionObjectId,
  };
  delete duplicateDiscountTransaction._id;

  const duplicatedDiscountTransaction =
    await DiscountTransactionModel.create(duplicateDiscountTransaction);

  return duplicatedDiscountTransaction.toObject();
}

async function addNewPurchaseTransaction(
  purchaseTransaction,
  community,
  priceId,
  interval,
  intervalCount,
  discount
) {
  const { applyDiscount } = purchaseTransaction;

  const { prices, passOnTakeRate, passOnPaymentGatewayFee } = community;

  const selectedPrice = prices.find(
    (price) =>
      price.interval === interval && price.intervalCount === intervalCount
  );

  console.log(
    `Add new purchase transaction: ${JSON.stringify(
      selectedPrice
    )}, passOnTakeRate: ${passOnTakeRate}, passOnPaymentGatewayFee: ${passOnPaymentGatewayFee}`
  );

  if (!selectedPrice) {
    throw new Error('No yearly price');
  }

  const priceDetails = {
    itemPrice: selectedPrice.cmSetPrice,
    checkoutCurrency: selectedPrice.currency,
    checkoutAmount: selectedPrice.stripePrice,
    feeDetails: selectedPrice.feeDetails,
  };

  const duplicatePurchaseTransaction = {
    ...purchaseTransaction,
    interval,
    interval_count: intervalCount,
    priceId,
    passOnTakeRate,
    passOnPaymentGatewayFee,
    priceDetails,
  };
  delete duplicatePurchaseTransaction._id;

  const duplicatedPurchaseTransaction =
    await PurchaseTransactionModel.create(duplicatePurchaseTransaction);

  if (applyDiscount || discount) {
    const newDiscountTransaction = await addNewDiscountTransaction(
      duplicatedPurchaseTransaction,
      discount
    );

    return PurchaseTransactionModel.findByIdAndUpdate(
      duplicatedPurchaseTransaction._id,
      {
        applyDiscount: true,
        discountAvailed: true,
        promoCodeStripeId:
          discount?.code ??
          duplicatedPurchaseTransaction.promoCodeStripeId,
        discountObjectId:
          newDiscountTransaction?.communityDiscountObjectId,
        discountTransactionId: newDiscountTransaction?._id,
      },
      { new: true }
    ).lean();
  }

  return duplicatedPurchaseTransaction.toObject();
}

async function updateMembershipPaymentInfo(
  subscription,
  interval,
  intervalCount,
  discount
) {
  console.log('Update membership');
  const { _id: subscriptionObjectId, communitySignupId } = subscription;

  const set = {
    'subscriptionInfo.paymentInfo.paymentInterval': interval,
    'subscriptionInfo.paymentInfo.paymentIntervalString': `${intervalCount}-${interval}`,
    'subscriptionInfo.isFreeTrial': false,
    'subscriptionInfo.purchaseTransactionObjectId': new ObjectId(
      communitySignupId
    ),
  };

  if (discount) {
    set['subscriptionInfo.paymentInfo.discountCodeUsed'] = discount.code;
    set['subscriptionInfo.paymentInfo.discountCodeUsedType'] =
      discount.type;
    set['subscriptionInfo.paymentInfo.discountCodeUsedValue'] =
      discount.value;
  }

  await MembershipModel.findOneAndUpdate({ subscriptionObjectId }, set);
}

async function updateSubscription(
  subscription,
  purchaseTransaction,
  priceId
) {
  const {
    _id: purchaseTransactionObjectId,
    interval,
    interval_count: intervalCount,
  } = purchaseTransaction;

  console.log(`Update subscription: ${purchaseTransactionObjectId}`);

  const updatedSubscription = await SubscriptionModel.findByIdAndUpdate(
    subscription._id,
    {
      interval,
      intervalCount,
      communitySignupId: purchaseTransactionObjectId.toString(),
      priceId,
    },
    { new: true }
  ).lean();

  return updatedSubscription;
}

async function updateStripeSubscription(
  subscription,
  purchaseTransaction,
  priceId,
  discount,
  stripeClient
) {
  console.log('Update stripe subscription');

  const { stripeSubscriptionId } = subscription;
  const { _id: purchaseTransactionObjectId } = purchaseTransaction;

  const stripeSubscription = await stripeClient.subscriptions.retrieve(
    stripeSubscriptionId
  );

  const stripeSubscriptionItemId = stripeSubscription.items.data[0].id;
  const currentBillingPeriodEnd = stripeSubscription.current_period_end;

  const updateQuery = {
    metadata: {
      communitySignupId: purchaseTransactionObjectId.toString(),
    },
    proration_behavior: 'none',
    items: [
      {
        id: stripeSubscriptionItemId,
        price: priceId,
      },
    ],
    trial_end: currentBillingPeriodEnd,
  };

  if (discount) {
    updateQuery.promotion_code = discount.promoCodeStripeId;
  }

  await stripeClient.subscriptions.update(
    stripeSubscriptionId,
    updateQuery
  );
}

async function updateDiscountUsage(discount) {
  console.log(`Update discount usage: ${discount?.code}`);
  const { totalRedemptions, maxRedemptions } = discount;

  const updateDiscountQuery = { $inc: { totalRedemptions: 1 } };

  if (maxRedemptions && (totalRedemptions ?? 0) + 1 >= maxRedemptions) {
    updateDiscountQuery.$set = {
      ...updateDiscountQuery.$set,
      isActive: false,
    };
  }

  await DiscountModel.findByIdAndUpdate(discount._id, updateDiscountQuery);
}

async function updateSubscriptionPlan(
  subscription,
  community,
  purchaseTransaction,
  priceId,
  interval,
  intervalCount,
  discount,
  stripeClient,
  currentTimestampInMs
) {
  const msg = `${subscription._id},${purchaseTransaction._id},${priceId},${
    discount?.code ?? ''
  }`;
  console.log(msg);

  const newPurchaseTransaction = await addNewPurchaseTransaction(
    purchaseTransaction,
    community,
    priceId,
    interval,
    intervalCount,
    discount
  );

  await updateStripeSubscription(
    subscription,
    newPurchaseTransaction,
    priceId,
    discount,
    stripeClient
  );

  const updatedSubscription = await updateSubscription(
    subscription,
    newPurchaseTransaction,
    priceId
  );

  if (discount) {
    await updateDiscountUsage(discount);
  }

  await updateMembershipPaymentInfo(
    updatedSubscription,
    interval,
    intervalCount,
    discount
  );

  await fs.appendFile(
    `logs/update_stripe_subscription_plan_${currentTimestampInMs}.log`,
    `${msg}\n`
  );
}

async function updateSubscriptionsPlan(
  subscriptions,
  community,
  purchaseTransactionsCache,
  priceId,
  interval,
  intervalCount,
  discount,
  stripeClient,
  currentTimestampInMs
) {
  for await (const subscription of subscriptions) {
    const purchaseTransaction = purchaseTransactionsCache.get(
      subscription.communitySignupId
    );

    await updateSubscriptionPlan(
      subscription,
      community,
      purchaseTransaction,
      priceId,
      interval,
      intervalCount,
      discount,
      stripeClient,
      currentTimestampInMs
    );
  }
}

async function retrieveAndValidateDiscount(discountCode, communityCode) {
  let discount;

  if (discountCode || discountCode !== '') {
    discount = await DiscountModel.findOne({
      code: discountCode,
      communityCode,
    }).lean();

    if (!discount) {
      throw new Error('Discount code not found');
    }

    if (!discount.isActive) {
      throw new Error('Discount code is not active');
    }

    if (discount.trialDays) {
      throw new Error('Cannot apply free trial discount');
    }

    if (
      discount.maxRedemptions &&
      discount.totalRedemptions === discount.maxRedemptions
    ) {
      throw new Error('Discount is already at max redemptions');
    }
  }

  return discount;
}

const start = async () => {
  await mongoClient.connect();

  await stripeClientService.init();

  const currentTimestampInMs = Date.now();

  const stripeClient = stripeClientService.getStripeGlobalClient();

  const communityCode = '';
  const priceId = '';
  const interval = 'year';
  const intervalCount = 1;
  const discountCode = '';

  const [community, subscriptions] = await Promise.all([
    CommunityModel.findOne(
      { code: communityCode },
      { prices: 1, passOnTakeRate: 1, passOnPaymentGatewayFee: 1 }
    ).lean(),
    SubscriptionModel.find({
      paymentProvider: 'stripe',
      communityCode,
      status: 'Current',
      interval: 'month',
      intervalCount: 1,
    }).lean(),
  ]);

  const purchaseTransactionsCache =
    await retrievePurchaseTransactionsCache(subscriptions);

  const discount = await retrieveAndValidateDiscount(
    discountCode,
    communityCode
  );

  await updateSubscriptionsPlan(
    subscriptions,
    community,
    purchaseTransactionsCache,
    priceId,
    interval,
    intervalCount,
    discount,
    stripeClient,
    currentTimestampInMs
  );

  console.log('Completed');
  process.exit(0);
};

start();
