require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const jwt = require('jsonwebtoken');
const moment = require('moment');
const momentTimezone = require('moment-timezone');
const logger = require('../src/services/logger.service');
const mongoClient = require('../src/mongoClient');
const CommunitySubscriptionModel = require('../src/communitiesAPI/models/communitySubscriptions.model');
const MembershipModel = require('../src/models/membership/membership.model');
const CommunityModel = require('../src/communitiesAPI/models/community.model');
const LearnerModel = require('../src/models/learners.model');
const {
  PAYMENT_PROVIDER,
  COMMUNITY_SUBSCRIPTION_STATUSES,
  DEFAULT_COMMUNITY_PROFILE_IMAGE,
} = require('../src/constants/common');
const {
  submitJob,
} = require('../src/services/mail/scheduledEmail.service');
const {
  WORKER_TYPES,
  RESULT_TYPE,
  USE_CASES,
} = require('../src/services/mail/constants');
const { NAS_IO_FRONTEND_URL } = require('../src/config');
const commonService = require('../src/services/communityNotification/email/common.service');
const {
  getFullName,
} = require('../src/services/user/utils/learner.utils');
const {
  MEMBERSHIP_STATUS,
} = require('../src/services/membership/constants');
const { getUser } = require('../src/services/user.service');
const { getLearners } = require('../src/services/learner.service');

const excludedFraudCommunities = [
  'ECOM_TOOLS_3',
  '__3303',
  'ECOMTOOLS_ARAB',
  'THEBIGSOCIALSAVER',
  'ECOM_TOOLS_2',
  'IMINTWEB_2',
  'STEALTHPACK',
  'SUPERECOM_HERRAMIENTAS_DE_ECOMMERCE',
  'WEALTH_SQUAD',
  'MARKET_DYNAMICS_MAVERICK_2',
  'ROB93Z20',
  'YAHANTEST',
  'MEXICODONTSTOPBELIEVING',
  'LAMADYA_IT_SERVICES_2',
  'IBOST_COMMUNITY',
  'DIGITAL_PRODUCTS_BUSINESS_COMMUNITY',
  'NAR_TUNISIA_2',
  'PROFITABLE_TEACHERS_NETWORK',
  'LONG_TONGUE_2',
  'THE_VILLAGE_COLLECTIVE_2',
  'GLAZIERS_IN_USA',
  'TETSTENTFRY',
  'OKNOOB_UNIVERSITY',
  'TWO_DICKS_FOR_HER',
  'HIGH_PROFILE_SOCIETY',
  'PAID_NO_APP',
];
const reformatCheapestPrice = (price) => {
  return {
    currency: price.currency,
    price: new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(price.cmSetPrice / 100),
    interval: price.interval,
    interval_count: price.intervalCount,
  };
};

const reformatDiscountPrice = (discountedPrice, price) => {
  const totalMonthsForDiscountedPrice =
    discountedPrice.intervalCount *
    (discountedPrice.interval === 'year' ? 12 : 1);
  const totalMonths =
    price.intervalCount * (price.interval === 'year' ? 12 : 1);

  const discountedPricePerMonth =
    discountedPrice.cmSetPrice / totalMonthsForDiscountedPrice;
  const pricePerMonth = price.cmSetPrice / totalMonths;

  const percentage = (
    ((pricePerMonth - discountedPricePerMonth) * 100) /
    pricePerMonth
  ).toFixed(0);
  return {
    discount_perc: percentage,
    discount_interval_count: discountedPrice.intervalCount,
    discount_interval: discountedPrice.interval,
  };
};

const sendPaypalSubscriptionEmail = async () => {
  const subscriptions = await CommunitySubscriptionModel.find({
    //email: '<EMAIL>',
    paymentProvider: PAYMENT_PROVIDER.PAYPAL,
    nextBillingDate: { $gt: new Date('2024-11-08'), $lt: new Date() },
    status: COMMUNITY_SUBSCRIPTION_STATUSES.CANCELLED,
    communityCode: {
      $nin: excludedFraudCommunities,
    },
  }).lean();

  const requestor = 'paypalResubscribeEmailRequestorSecondAttempt';

  let totalRecords = 0;
  for (const subscription of subscriptions) {
    const [community, membership, learner] = await Promise.all([
      CommunityModel.findOne({
        code: subscription.communityCode,
      }).lean(),
      MembershipModel.findOne({
        communityCode: subscription.communityCode,
        email: subscription.email,
        primaryIdentifier: 'email',
      }).lean(),
      LearnerModel.findOne({ email: subscription.email }).lean(),
    ]);
    if (membership.status === MEMBERSHIP_STATUS.SUBSCRIBED) {
      continue;
    }
    totalRecords += 1;
    logger.info(
      `Send mail for ${subscription.communityCode} - ${subscription.email}`
    );
    const prices = community.prices ?? [];

    prices.sort((a, b) => {
      const aNoOfMonths =
        a.intervalCount * (a.interval === 'year' ? 12 : 1);
      const bNoOfMonths =
        b.intervalCount * (b.interval === 'year' ? 12 : 1);

      if (aNoOfMonths < bNoOfMonths) {
        return -1;
      }
      return 1;
    });

    let priceWithLeastMonths = {};
    let discountPrice = {};

    if (prices.length > 0) {
      priceWithLeastMonths = reformatCheapestPrice(prices[0]);
      if (prices.length > 1) {
        discountPrice = reformatDiscountPrice(prices[1], prices[0]);
      }
    }

    const checkoutLink = `${NAS_IO_FRONTEND_URL}/checkout-global?communityId=${community._id}&communityCode=${community.code}&requestor=${requestor}`;
    const owner = await commonService.retrieveCommunityOwnerInfo(
      community.code
    );
    const emailData = {
      entity_checkout_link: checkoutLink,
      community_description: community.description,
      community_name: community.title,
      community_host: owner.name,
      community_profile_image:
        community?.thumbnailImgData?.mobileImgData?.src ??
        DEFAULT_COMMUNITY_PROFILE_IMAGE,
      ...priceWithLeastMonths,
      ...discountPrice,
      first_name: learner.firstName ?? '',
      community_link: `${NAS_IO_FRONTEND_URL}${community.link}`,
    };

    // Create email schedulers
    const data = {
      fromMailName: ['Nas.io Alerts'],
      fromMail: '<EMAIL>',
      toMail: [learner.email],
      toMailName: [getFullName(learner)],
      replyToMail: '<EMAIL>',
      replyToMailName: 'Nas.io Alerts',
      requesterServiceName: 'LPBE',
      data: emailData,
    };
    await submitJob(
      {
        dueAt: new Date(),
        useCase: USE_CASES.PAYPAL_UNSUBSCRIPTION_REMINDER,
        workerType: WORKER_TYPES.NOTI_SERVICE,
        workerDetails: {
          path: `/api/v1/send-email`,
          bodyParam: {
            mailType: 'PAYPAL_UNSUBSCRIPTION_REMINDER_MAIL_ATTEMPT_2',
            ...data,
          },
        },
        entityObjectId: membership._id,
        entityParentObjectId: community._id,
        resultType: RESULT_TYPE.SINGLE,
      },
      {}
    );
  }

  logger.info(`total subscriptions = ${totalRecords}`);
};

function getUserPayload({ user, learner, expiredAtTimestamp }) {
  return {
    user: {
      _id: user._id,
      learner_role: true,
      instructor_role: false,
      admin_role: false,
      status: 'ACTIVE',
      learner: {
        _id: learner._id,
        learnerId: learner.learnerId,
      },
      isActive: true,
      email: user.email,
      user_id: user.user_id,
      user_id_to_validate: user.user_id,
    },
    iat: expiredAtTimestamp,
    exp: expiredAtTimestamp,
  };
}

const generateAccessToken = async (email, expireInMins) => {
  if (!expireInMins) {
    expireInMins = 60 * 24;
  }
  const currentTime = new Date();
  const expiredAt = new Date(
    currentTime.getTime() + expireInMins * 60 * 1000
  );
  const expiredAtTimestamp = Math.floor(expiredAt.getTime() / 1000);
  const jwtSecretKey = process.env.AUTH_JWT_TOKEN;
  const user = await getUser({ email });
  const learner = await LearnerModel.findOne({ email })
    .select('learnerId')
    .lean();
  user.learner = {
    _id: learner._id,
    learnerId: learner.learnerId,
  };
  const userPayload = getUserPayload({
    user,
    learner,
    expiredAtTimestamp,
  });
  const accessToken = jwt.sign(userPayload, jwtSecretKey, {
    algorithm: 'HS256',
  });
  return accessToken;
};

const sendPaypalResubscribeSubscriptionEmailForActiveUser = async () => {
  const subscriptions = await CommunitySubscriptionModel.find({
    // email: new RegExp('yahantsaotw', 'i'),
    // email: '<EMAIL>',
    paymentProvider: PAYMENT_PROVIDER.PAYPAL,
    status: COMMUNITY_SUBSCRIPTION_STATUSES.CURRENT,
    nextBillingDate: { $lt: new Date() },
    communityCode: {
      $nin: excludedFraudCommunities,
    },
  }).lean();

  const requestor = 'paypalResubscribeEmailRequestor';

  let totalRecords = 0;
  for (const subscription of subscriptions) {
    const [community, learner] = await Promise.all([
      CommunityModel.findOne({
        code: subscription.communityCode,
      }).lean(),
      LearnerModel.findOne({ email: subscription.email }).lean(),
    ]);

    totalRecords += 1;
    logger.info(
      `Send mail for ${subscription.communityCode} - ${subscription.email}`
    );

    const accessToken = await generateAccessToken(
      subscription.email,
      11520
    ); // 8 days
    const checkoutLink = `${NAS_IO_FRONTEND_URL}/checkout-global?communityId=${community._id}&communityCode=${community.code}&requestor=${requestor}&renewal=true&accessToken=${accessToken}`;
    logger.info(checkoutLink);

    const emailData = {
      checkout_link: checkoutLink,
      community_name: community.title,
      first_name: learner.firstName ?? '',
      community_link: `${NAS_IO_FRONTEND_URL}${community.link}`,
      next_billing_date: momentTimezone(subscription.nextBillingDate)
        .tz(learner.timezone)
        .format('DD/MM/YYYY'),
    };

    // Create email schedulers
    const data = {
      fromMailName: ['Nas.io'],
      fromMail: '<EMAIL>',
      toMail: [learner.email],
      toMailName: [getFullName(learner)],
      replyToMail: '<EMAIL>',
      replyToMailName: 'Nas.io',
      requesterServiceName: 'LPBE',
      data: emailData,
    };
    logger.info(JSON.stringify(emailData));
    await submitJob(
      {
        dueAt: new Date(),
        useCase: USE_CASES.PAYPAL_UNSUBSCRIPTION_REMINDER,
        workerType: WORKER_TYPES.NOTI_SERVICE,
        workerDetails: {
          path: `/api/v1/send-email`,
          bodyParam: {
            mailType: 'PAYPAL_EXPIRED_RESUBSCRIPTION_REMINDER',
            ...data,
          },
        },
        entityObjectId: learner._id,
        entityParentObjectId: community._id,
        resultType: RESULT_TYPE.SINGLE,
      },
      {}
    );
  }

  logger.info(`total subscriptions = ${totalRecords}`);
};

const schedulePaypalResubscribeSubscriptionEmailForActiveUser =
  async () => {
    const subscriptions = await CommunitySubscriptionModel.find({
      // email: new RegExp('yahantsaotw', 'i'),
      email: '<EMAIL>',
      paymentProvider: PAYMENT_PROVIDER.PAYPAL,
      status: COMMUNITY_SUBSCRIPTION_STATUSES.CURRENT,
      nextBillingDate: { $gt: new Date() },
      communityCode: {
        $nin: excludedFraudCommunities,
      },
    }).lean();

    const requestor = 'paypalResubscribeEmailRequestor';

    let totalRecords = 0;
    for (const subscription of subscriptions) {
      const [community, learner] = await Promise.all([
        CommunityModel.findOne({
          code: subscription.communityCode,
        }).lean(),
        LearnerModel.findOne({ email: subscription.email }).lean(),
      ]);

      totalRecords += 1;
      logger.info(
        `Send mail for ${subscription.communityCode} - ${subscription.email}`
      );

      // Will generate access token that can be used til nextBillingDate + 7 days
      // Because the mail will be scheduled to send 7 days before billing date,
      // So access token wont be access before we send the mail
      // Target date: next billing date + 7
      const targetDate = moment(subscription.nextBillingDate).add(
        7,
        'days'
      );
      // Current date
      const now = moment();
      // Get difference in minutes
      const minutesDiff = targetDate.diff(now, 'minutes');
      const accessToken = await generateAccessToken(
        subscription.email,
        minutesDiff
      );

      const checkoutLink = `${NAS_IO_FRONTEND_URL}/checkout-global?communityId=${community._id}&communityCode=${community.code}&requestor=${requestor}&renewal=true&accessToken=${accessToken}`;
      logger.info(checkoutLink);

      const emailData = {
        checkout_link: checkoutLink,
        community_name: community.title,
        first_name: learner.firstName ?? '',
        community_link: `${NAS_IO_FRONTEND_URL}${community.link}`,
        next_billing_date: momentTimezone(subscription.nextBillingDate)
          .tz(learner.timezone || 'UTC')
          .format('DD/MM/YYYY'),
      };

      // Create email schedulers
      const data = {
        fromMailName: ['Nas.io'],
        fromMail: '<EMAIL>',
        toMail: [learner.email],
        toMailName: [getFullName(learner)],
        replyToMail: '<EMAIL>',
        replyToMailName: 'Nas.io',
        requesterServiceName: 'LPBE',
        data: emailData,
      };
      logger.info(JSON.stringify(emailData));
      await submitJob(
        {
          dueAt: moment(subscription.nextBillingDate)
            .add(-7, 'days')
            .toDate(),
          useCase: USE_CASES.PAYPAL_UNSUBSCRIPTION_REMINDER,
          workerType: WORKER_TYPES.NOTI_SERVICE,
          workerDetails: {
            path: `/api/v1/send-email`,
            bodyParam: {
              mailType: 'PAYPAL_NON_EXPIRED_RESUBSCRIPTION_REMINDER',
              ...data,
            },
          },
          entityObjectId: learner._id,
          entityParentObjectId: community._id,
          resultType: RESULT_TYPE.SINGLE,
        },
        {}
      );
    }

    logger.info(`total subscriptions = ${totalRecords}`);
  };

const start = async () => {
  await mongoClient.connect();

  await schedulePaypalResubscribeSubscriptionEmailForActiveUser();

  logger.info('Completed');
  process.exit(0);
};

start();
