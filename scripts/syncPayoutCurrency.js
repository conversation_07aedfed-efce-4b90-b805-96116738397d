require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});
const fs = require('fs').promises;

const mongoClient = require('../src/mongoClient');
const CommunityManagerPayout = require('../src/communitiesAPI/models/communityManagerPayout.model');
const CommunityModel = require('../src/communitiesAPI/models/community.model');
const CountryCurrencyMappingModel = require('../src/models/countryInfoMapping.model');

const ignoreCommunityCodeList = ['GLOBAL_COMMUNITY_4', 'BAR_AKADEMIJA'];

function mapBankCountry(country) {
  switch (country) {
    case 'Slovakia':
      return 'Slovakia (Slovak Republic)';
    case 'South Korea':
      return 'Korea South';
    case 'Palestine':
      return 'Palestinian Territory';
    case 'Gibraltar UK':
      return 'Gibraltar';
    case 'Libya':
      return 'Libyan Arab Jamahiriya';
    case 'North Macedonia':
      return 'Macedonia';
    default:
      return country;
  }
}

const syncPayoutCurrency = async (currentDate, toUpdate = false) => {
  const bankAccounts = await CommunityManagerPayout.aggregate([
    {
      $lookup: {
        from: 'community_manager_payout_history',
        localField: '_id',
        foreignField: '_id._id',
        as: 'history',
      },
    },
  ]);

  await Promise.all(
    bankAccounts.map(async (bankAccount) => {
      const communityId = bankAccount.communityId;
      const community = await CommunityModel.findById(communityId).lean();

      if (!community) {
        console.error(`Invalid community for ${communityId}`);
        return;
      }

      if (ignoreCommunityCodeList.includes(community.code)) {
        return;
      }

      const bankAccountCountryMap = mapBankCountry(bankAccount.country);
      const country = await CountryCurrencyMappingModel.findOne({
        country: bankAccountCountryMap,
      }).lean();

      const choseLocalPreviously =
        bankAccount.history.sort((a, b) => b._version - a._version)?.[0]
          ?.payoutCurrency === 'local';

      const bankAccountCountryCurrency =
        country?.currencyCode?.toLowerCase();

      if (!bankAccountCountryCurrency) {
        console.warn(
          bankAccount.country,
          bankAccountCountryMap,
          bankAccountCountryCurrency,
          community?.code,
          communityId
        );
      }

      if (
        choseLocalPreviously &&
        bankAccountCountryCurrency !== bankAccount.payoutCurrency
      ) {
        const messageLog = `${community?.code},${communityId.toString()},${
          bankAccount.payoutCurrency
        },${bankAccountCountryCurrency},${
          bankAccount.country
        } (${bankAccountCountryMap}),${bankAccount.history.length}`;

        if (toUpdate) {
          await CommunityManagerPayout.findByIdAndUpdate(bankAccount._id, {
            payoutCurrency: bankAccountCountryCurrency,
          });
        } else if (bankAccount.history.length > 1) {
          console.log(messageLog);
        }

        await fs.appendFile(
          `logs/syncPayoutCurrency_${currentDate.getTime()}.log`,
          `${messageLog}\n`
        );
      }
    })
  );
};

async function syncAllNonUsdToBankAccountCountryCurrency(
  currentDate,
  toUpdate = false
) {
  const bankAccounts = await CommunityManagerPayout.find({
    payoutCurrency: { $ne: 'usd' },
  }).lean();

  await Promise.all(
    bankAccounts.map(async (bankAccount) => {
      const communityId = bankAccount.communityId;

      const bankAccountCountryMap = mapBankCountry(bankAccount.country);
      const country = await CountryCurrencyMappingModel.findOne({
        country: bankAccountCountryMap,
      }).lean();

      const bankAccountCountryCurrency =
        country?.currencyCode?.toLowerCase();

      if (!bankAccountCountryCurrency) {
        console.warn(
          communityId,
          bankAccount.country,
          bankAccountCountryMap,
          bankAccountCountryCurrency
        );
      }

      if (bankAccountCountryCurrency !== bankAccount.payoutCurrency) {
        const messageLog = `${communityId.toString()},${
          bankAccount.payoutCurrency
        },${bankAccountCountryCurrency},${
          bankAccount.country
        } (${bankAccountCountryMap})`;

        if (toUpdate) {
          await CommunityManagerPayout.findByIdAndUpdate(bankAccount._id, {
            payoutCurrency: bankAccountCountryCurrency,
          });
        } else {
          console.log(messageLog);
        }

        await fs.appendFile(
          `logs/syncPayoutCurrency_${currentDate.getTime()}.log`,
          `${messageLog}\n`
        );
      }
    })
  );
}

const start = async () => {
  await mongoClient.connect();

  const currentDate = new Date();
  const toUpdate = false;

  await syncPayoutCurrency(currentDate, toUpdate);

  await syncAllNonUsdToBankAccountCountryCurrency(currentDate, toUpdate);

  console.log('Completed');
  process.exit(0);
};

start();
