require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});
const mongoose = require('mongoose');

const mongoClient = require('../src/mongoClient');
const CountryInfoMappingModel = require('../src/models/countryInfoMapping.model');
const RevenueTransactionModel = require('../src/models/revenueTransaction.model');
const EarningAnalyticsModel = require('../src/models/earningAnalytics.model');

async function retrieveCountryCodeCache() {
  const countries = await CountryInfoMappingModel.find({}).lean();

  const countryCodeCache = countries.reduce((acc, country) => {
    acc.set(country.country, country.countryCode);
    return acc;
  }, new Map());

  return countryCodeCache;
}

function retrieveCountryCode(countryCodeCache, country) {
  if (!countryCodeCache.has(country)) {
    throw new Error(`${country} not found`);
  }

  const countryCode = countryCodeCache.get(country);
  return countryCode;
}

async function retrieveRevenueTransactionsCommunityObjectIds() {
  const revenueTransactions = await RevenueTransactionModel.aggregate([
    {
      $match: {
        transactionCreatedAt: {
          $gte: new Date('2023-06-01'),
          $lt: new Date('2024-05-21'),
        },
        transactionType: 'INBOUND',
        purchaseType: {
          $in: ['EVENT', 'FOLDER', 'SESSION', 'CHALLENGE'],
        },
      },
    },
    {
      $sort: {
        communityObjectId: 1,
      },
    },
    {
      $group: {
        _id: '$communityObjectId',
      },
    },
  ]);

  return revenueTransactions;
}

async function retrieveCountryAggregations(communityObjectId) {
  const revenueTransactions = await RevenueTransactionModel.aggregate([
    {
      $match: {
        communityObjectId,
        transactionCreatedAt: {
          $gte: new Date('2023-06-01'),
          $lt: new Date('2024-05-21'),
        },
        transactionType: 'INBOUND',
        purchaseType: {
          $in: ['EVENT', 'FOLDER', 'SESSION', 'CHALLENGE'],
        },
      },
    },
    {
      $sort: {
        transactionCreatedAt: 1,
      },
    },
    {
      $lookup: {
        from: 'community_addon_transactions',
        localField: 'purchasedId',
        foreignField: '_id',
        as: 'addonTransaction',
      },
    },
    {
      $unwind: {
        path: '$addonTransaction',
        preserveNullAndEmptyArrays: false,
      },
    },
    {
      $project: {
        _id: 0,
        date: {
          $dateFromParts: {
            year: {
              $year: '$transactionCreatedAt',
            },
            month: {
              $month: '$transactionCreatedAt',
            },
            day: {
              $dayOfMonth: '$transactionCreatedAt',
            },
          },
        },
        transactionCreatedAt: 1,
        communityObjectId: 1,
        country: '$addonTransaction.country',
      },
    },
    {
      $group: {
        _id: {
          communityObjectId: '$communityObjectId',
          date: '$date',
          country: '$country',
        },
        count: {
          $sum: 1,
        },
      },
    },
    {
      $group: {
        _id: {
          communityObjectId: '$_id.communityObjectId',
          date: '$_id.date',
        },
        countries: {
          $push: {
            country: '$_id.country',
            quantity: '$count',
          },
        },
        count: {
          $sum: 1,
        },
      },
    },
  ]);

  return revenueTransactions;
}

function generateEarningsAnalyticsAddonCountriesQueryInfos(
  countryAggregations,
  countryCodeCache
) {
  const earningsAnalyticsQueryInfos = countryAggregations.map(
    (countryAggregation) => {
      const countries = countryAggregation.countries.map(
        ({ country, quantity }) => {
          const countryCode = retrieveCountryCode(
            countryCodeCache,
            country
          );

          const countryInfo = {
            countryCode,
            quantity,
          };

          return countryInfo;
        }
      );

      return {
        communityObjectId: countryAggregation._id.communityObjectId,
        date: countryAggregation._id.date,
        addonCountries: countries,
      };
    }
  );

  return earningsAnalyticsQueryInfos;
}

async function updateEarningAnalyticsAddonCountries(queryInfos) {
  await Promise.all(
    queryInfos.map(async (queryInfo) => {
      const { communityObjectId, date, addonCountries } = queryInfo;
      await EarningAnalyticsModel.findOneAndUpdate(
        { communityObjectId, date },
        { $set: { addonCountries } }
      );
    })
  );
}

const start = async () => {
  await mongoClient.connect();

  const countryCodeCache = await retrieveCountryCodeCache();

  const revenueTransactions =
    await retrieveRevenueTransactionsCommunityObjectIds();

  await Promise.all(
    revenueTransactions.map(async (revenueTransaction) => {
      const { _id: communityObjectId } = revenueTransaction;

      const countryAggregations = await retrieveCountryAggregations(
        communityObjectId
      );

      const queryInfos = generateEarningsAnalyticsAddonCountriesQueryInfos(
        countryAggregations,
        countryCodeCache
      );

      await updateEarningAnalyticsAddonCountries(queryInfos);

      console.log(`${communityObjectId} done`);
    })
  );

  console.log('Completed');
  process.exit(0);
};

start();
