// Task: https://applink.larksuite.com/client/todo/detail?guid=1479600f-37ac-42aa-bb9e-f1fa4c13995e&suite_entity_num=t114567
// PF Thread: https://applink.larksuite.com/client/message/link/open?token=AmMK6cDh2cAFZ5INbWMAgAw%3D

require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const mongoClient = require('../src/mongoClient');
const SubscriptionModel = require('../src/communitiesAPI/models/communitySubscriptions.model');
const LearnerModel = require('../src/models/learners.model');

const start = async () => {
  await mongoClient.connect();

  // get subscriptions where learnerObjectId is missing.
  const subscriptionsToUpdate = await SubscriptionModel.find({
    learnerId: { $exists: true },
    learnerObjectId: { $exists: false },
    $or: [
      { status: 'Current' },
      { status: 'Cancelled', cancelledAt: { $gte: new Date() } },
    ],
  }).lean(); // ~1861 records

  const learnerIds = subscriptionsToUpdate.map(
    (subscription) => subscription.learnerId
  );

  const uniqueLearnerIds = [...new Set(learnerIds)]; // ~1032 records

  // get learners data for all learnerIds
  const learners = await LearnerModel.find({
    learnerId: { $in: uniqueLearnerIds },
  }).lean();

  // Update subscriptions with learnerObjectId
  const promises = learners.map(async (learner) => {
    const learnerId = learner.learnerId;
    const learnerObjectId = learner._id;
    const result = await SubscriptionModel.updateMany(
      {
        learnerId,
        $or: [
          { status: 'Current' },
          { status: 'Cancelled', cancelledAt: { $gte: new Date() } },
        ],
      },
      {
        $set: {
          learnerObjectId,
        },
      }
    );

    return result;
  });

  const results = await Promise.all(promises);

  console.log('Done');
  console.log(results);
  process.exit(0);
};

start();
