/* eslint-disable no-continue */
// first get the all the emails from the (current) sendgrid account
// then get the all the emails from the backup sendgrid account
// then search for all the emails in the db for the current sendgrid account (search for the custom templateId as well)
// then find the same email in the backup sendgrid account and create the backup records

require('dotenv').config();
const axios = require('axios');
const mongoClient = require('../src/mongoClient');
const mailContentModel = require('../src/models/notificationBackend/mailContent.model');
const backupSendGridTemplateModel = require('../src/models/notificationBackend/backupSendGridTemplate.model');
const logger = require('../src/services/logger.service');

const sendGridAPI = 'https://api.sendgrid.com/v3';

const mainAccountToken = '';
const backupAccountToken = '';

const populateDBWithBackupSendgridAccountEmails = async () => {
  await mongoClient.connect();

  /* 1. Pull both template lists in parallel */
  const [{ data: oldRes }, { data: newRes }] = await Promise.all([
    axios.get(`${sendGridAPI}/templates`, {
      params: { generations: 'dynamic', page_size: 200 },
      headers: { Authorization: mainAccountToken },
    }),
    axios.get(`${sendGridAPI}/templates`, {
      params: { generations: 'dynamic', page_size: 200 },
      headers: { Authorization: backupAccountToken },
    }),
  ]);

  const oldTemplates = oldRes.result;
  const newTemplates = newRes.result;

  /* 2. Index backup templates by name + detect duplicates */
  const backupByName = {};
  const duplicates = {};

  for (const tpl of newTemplates) {
    if (!backupByName[tpl.name]) {
      backupByName[tpl.name] = tpl;
    } else {
      // first time we spot a dup, stash the original too
      duplicates[tpl.name] = duplicates[tpl.name]
        ? [...duplicates[tpl.name], tpl]
        : [backupByName[tpl.name], tpl];
      backupByName[tpl.name] = null; // because we don't want to use it (later we will manually enter these guys)
    }
  }

  /* 3. Bulk-fetch related DB docs (one round-trip each) */
  const templateIds = oldTemplates.map((t) => t.id);

  const [standardDocs, customDocs] = await Promise.all([
    mailContentModel
      .find({
        template: { $in: templateIds },
        mailCourse: 'All',
        mailCourseOffer: 'All',
      })
      .lean(),
    mailContentModel
      .find({
        customTemplate: { $in: templateIds },
        mailCourse: 'All',
        mailCourseOffer: 'All',
      })
      .lean(),
  ]);

  const standardById = Object.fromEntries(
    standardDocs.map((d) => [d.template, d])
  );
  const customById = Object.fromEntries(
    customDocs.map((d) => [d.customTemplate, d])
  );

  /* 4. Process each old template once */
  for await (const { id: oldId, name } of oldTemplates) {
    try {
      // skip if duplicate names exist in backup
      if (duplicates[name]) {
        logger.info(`Skipped "${name}" — multiple backup templates`);
        continue;
      }

      const backupTpl = backupByName[name];
      if (!backupTpl) {
        logger.info(` No backup template for "${name}"`);
        continue;
      }

      const info = standardById[oldId] || customById[oldId];
      if (!info) {
        logger.info(` No DB entry for template "${name}"`);
        continue;
      }

      // idempotent upsert
      await backupSendGridTemplateModel.updateOne(
        { mainTemplateId: oldId },
        {
          $set: {
            mailType: info.mailType,
            mainTemplateId: oldId,
            backupTemplateId: backupTpl.id,
          },
        },
        { upsert: true }
      );
    } catch (err) {
      logger.error(`Failed on "${name}" (${oldId}):`, err);
    }
  }

  /* 5. Report any dups we skipped */
  if (Object.keys(duplicates).length) {
    logger.info('\nDuplicate names in backup account:');
    console.table(
      Object.entries(duplicates).map(([n, arr]) => ({
        name: n,
        ids: arr.map((t) => t.id).join(', '),
      }))
    );
  }

  logger.info(' Done populating backup template mappings.');
};

populateDBWithBackupSendgridAccountEmails();
