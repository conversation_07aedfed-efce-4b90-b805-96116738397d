require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const mongoClient = require('../src/mongoClient');
const PostModel = require('../src/communitiesAPI/models/communityPost.model');
const productService = require('../src/services/common/communityProducts.service');

const { PURCHASE_TYPE } = require('../src/constants/common');
const { NAS_IO_FRONTEND_URL } = require('../src/config');

const getLink = (type, community, metadata) => {
  switch (type) {
    case PURCHASE_TYPE.EVENT:
      return `${NAS_IO_FRONTEND_URL}${community.slug}/events${metadata.slug}`;

    case PURCHASE_TYPE.CHALLENGE:
      return `${NAS_IO_FRONTEND_URL}${community.slug}/challenges${metadata.slug}`;

    case PURCHASE_TYPE.FOLDER:
    case PURCHASE_TYPE.SESSION:
      return `${NAS_IO_FRONTEND_URL}${community.slug}/products${metadata.slug}`;

    default:
      return `${NAS_IO_FRONTEND_URL}${community.slug}`;
  }
};

const bulkUpdateInParts = async (postsQueryResults) => {
  const subscriptionObjectIds = [];
  const eventObjectIds = [];
  const productObjectIds = [];
  const programObjectIds = [];

  postsQueryResults.forEach((community) => {
    subscriptionObjectIds.push(community._id);
    const posts = community.posts ?? [];
    posts.forEach((post) => {
      post.mentionedProducts.forEach((product) => {
        switch (product.type) {
          case PURCHASE_TYPE.SUBSCRIPTION:
            subscriptionObjectIds.push(product.productObjectId);
            break;
          case PURCHASE_TYPE.CHALLENGE:
            programObjectIds.push(product.productObjectId);
            break;
          case PURCHASE_TYPE.EVENT:
            eventObjectIds.push(product.productObjectId);
            break;
          case PURCHASE_TYPE.SESSION:
          case PURCHASE_TYPE.FOLDER:
            productObjectIds.push(product.productObjectId);
            break;
          default:
            break;
        }
      });
    });
  });

  const defaultChild = {
    format: 'center',
    linkType: 'landingPage',
    size: 'sm',
    type: 'product',
    version: 1,
  };

  const existingProductCache = await productService.retrieveEntitiesCache({
    subscriptionObjectIds,
    eventObjectIds,
    productObjectIds,
    programObjectIds,
    source: productService.SOURCE_TYPES.MAGIC_REACH,
  });

  const pipeline = [];
  postsQueryResults.forEach((community) => {
    const communityData = existingProductCache.get(
      `${PURCHASE_TYPE.SUBSCRIPTION}-${community._id}`
    );
    const posts = community.posts ?? [];
    posts.forEach((post) => {
      if (post.mentionedProducts?.length > 1) {
        return;
      }
      const mentionedProduct = post.mentionedProducts?.[0];
      const productInfo = existingProductCache.get(
        `${mentionedProduct.type}-${mentionedProduct.productObjectId}`
      );
      const link = getLink(
        mentionedProduct.type,
        communityData,
        productInfo
      );
      const filter = { _id: post._id };
      const content = post.content;
      const children = content.root.children ?? [];
      children.push({
        ...defaultChild,
        link,
        metadata: productInfo ?? {},
      });
      content.root.children = children;

      pipeline.push({
        updateMany: {
          filter,
          update: {
            $set: {
              modifiedReason: 'Update Post Content',
              content,
            },
          },
        },
      });
    });
  });

  await PostModel.bulkWrite(pipeline);
};

const run = async () => {
  await mongoClient.connect();

  const posts = await PostModel.aggregate([
    {
      $match: {
        'mentionedProducts.productObjectId': { $exists: true },
        'mentionedProducts.type': { $exists: true },
      },
    },
    {
      $addFields: {
        communities: { $first: '$communities' },
      },
    },
    {
      $group: {
        _id: '$communities',
        posts: {
          $push: {
            _id: '$_id',
            content: '$content',
            mentionedProducts: '$mentionedProducts',
          },
        },
      },
    },
  ]);

  const THRESHOLD = 100;
  const length = posts.length;
  const rounds = Math.ceil(length / 100);
  const queries = [];
  for (let i = 0; i < rounds; i++) {
    const start = i * THRESHOLD;
    let end = start + THRESHOLD;
    if (end > length) end = length;
    queries.push({ start, end });
  }

  await Promise.all(
    queries.map(async (item) => {
      await bulkUpdateInParts(posts.slice(item.start, item.end));
    })
  );

  console.log('Completed');
  process.exit(0);
};

run();
