require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const logger = require('../src/services/logger.service');

const mongoClient = require('../src/mongoClient');
const CommunityManagerPayout = require('../src/communitiesAPI/models/communityManagerPayout.model');
const CommunityModel = require('../src/communitiesAPI/models/community.model');

const {
  getLocalCurrencyByCountry,
} = require('../src/services/payout/common.service');

const convertPayoutCurrency = async (params = {}) => {
  const bankAccounts = await CommunityManagerPayout.find({
    payoutCurrency: 'local',
  }).lean();

  await Promise.all(
    bankAccounts.map(async (bankAccount) => {
      const communityId = bankAccount.communityId;
      const community = await CommunityModel.findById(communityId).lean();
      logger.info(
        `community = ${communityId}, bank account = ${bankAccount._id}`
      );

      // Get country currency mapping
      const localCurrency = community?.baseCurrency;

      await CommunityManagerPayout.findByIdAndUpdate(bankAccount._id, {
        payoutCurrency: localCurrency.toLowerCase(),
      });
    })
  );
};

const start = async () => {
  await mongoClient.connect();

  await convertPayoutCurrency({});

  logger.info('Completed');
  process.exit(0);
};

start();
