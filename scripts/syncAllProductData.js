require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const ObjectId = require('mongoose').Types.ObjectId;
const { promises: fs } = require('fs');
const mongoClient = require('../src/mongoClient');

const CommunityEventModel = require('../src/communitiesAPI/models/communityEvents.model');
const CommunityFolderModel = require('../src/communitiesAPI/models/communityFolders.model');
const ProgramModel = require('../src/models/program/program.model');
const SyncProductDataService = require('../src/services/product/syncProductData.service');
const { PRODUCT_TYPE } = require('../src/services/product/constants');
const {
  EVENT_STATUS,
  COMMUNITY_FOLDER_STATUS,
  communityFolderTypesMap,
} = require('../src/communitiesAPI/constants');
const CommunityProductModel = require('../src/models/product/communityProduct.model');
const { PROGRAM_STATUS } = require('../src/services/program/constants');
const logger = require('../src/services/logger.service');

async function getBatches(model, query = {}, lastId, BATCH_SIZE = 15) {
  const batchQuery = {
    ...query,
    ...(lastId && { _id: { $gt: lastId } }),
  };

  const batch = await model
    .find(batchQuery)
    .sort({ _id: 1 })
    .limit(BATCH_SIZE)
    .lean();
  return batch;
}

const syncEvents = async () => {
  const currentTimestampInMs = Date.now();
  let lastId = new ObjectId('6513037256cb8b0ceb61d27d');
  let batch = [];

  do {
    batch = await CommunityEventModel.aggregate([
      {
        $match:
          /**
           * query: The query in MQL.
           */
          {
            status: {
              $ne: 'deleted',
            },
            _id: { $gt: lastId },
          },
      },
      {
        $lookup:
          /**
           * from: The target collection.
           * localField: The local join field.
           * foreignField: The target join field.
           * as: The name for the results.
           * pipeline: Optional pipeline to run on the foreign collection.
           * let: Optional variables to use in the pipeline field stages.
           */
          {
            from: 'community_product',
            localField: '_id',
            foreignField: 'entityObjectId',
            as: 'product',
          },
      },
      {
        $match:
          /**
           * query: The query in MQL.
           */
          {
            $or: [
              {
                'product.0': {
                  $exists: false,
                },
              },
              {
                'product.0.productType': {
                  $ne: 'EVENT',
                },
              },
            ],
          },
      },
      {
        $limit:
          /**
           * Provide the number of documents to limit.
           */
          300,
      },
    ]);

    if (batch.length <= 0) {
      break;
    }
    try {
      logger.info(
        `Sync event: batch [${batch[0]._id}], createdAt = ${batch[0].createdAt}`
      );
      await SyncProductDataService.bulkCreateData({
        productType: PRODUCT_TYPE.EVENT,
        entityList: batch,
      });
      lastId = batch[0]._id;
    } catch (err) {
      const ids = batch.map((event) => event._id);
      await fs.appendFile(
        `logs/syncDataError_event_${currentTimestampInMs}.log`,
        `${ids}, ${err.message}\n`
      );
    }
  } while (batch.length > 0);
};

const syncDigitalProducts = async () => {
  const currentTimestampInMs = Date.now();
  let batch = [];

  do {
    batch = await CommunityFolderModel.aggregate([
      {
        $match:
          /**
           * query: The query in MQL.
           */
          {
            status: {
              $in: [
                COMMUNITY_FOLDER_STATUS.PUBLISHED,
                COMMUNITY_FOLDER_STATUS.UNPUBLISHED,
              ],
            },
            type: communityFolderTypesMap.DIGITAL_PRODUCT,
          },
      },
      {
        $lookup:
          /**
           * from: The target collection.
           * localField: The local join field.
           * foreignField: The target join field.
           * as: The name for the results.
           * pipeline: Optional pipeline to run on the foreign collection.
           * let: Optional variables to use in the pipeline field stages.
           */
          {
            from: 'community_product',
            localField: '_id',
            foreignField: 'entityObjectId',
            as: 'product',
          },
      },
      {
        $match:
          /**
           * query: The query in MQL.
           */
          {
            $or: [
              {
                'product.0': {
                  $exists: false,
                },
              },
              {
                'product.0.productType': {
                  $ne: PRODUCT_TYPE.DIGITAL_FILES,
                },
              },
            ],
          },
      },
      {
        $limit:
          /**
           * Provide the number of documents to limit.
           */
          300,
      },
    ]);

    if (batch.length <= 0) {
      break;
    }
    try {
      logger.info(
        `Sync digital products: batch [${batch[0]._id}], createdAt = ${batch[0].createdAt}`
      );
      await SyncProductDataService.bulkCreateData({
        productType: PRODUCT_TYPE.DIGITAL_FILES,
        entityList: batch,
      });
    } catch (err) {
      const ids = batch.map((event) => event._id);
      await fs.appendFile(
        `logs/syncDataError_digitalProducts_${currentTimestampInMs}.log`,
        `${ids}, ${err.message}\n`
      );
    }
  } while (batch.length > 0);
};

const syncSessions = async () => {
  const currentTimestampInMs = Date.now();
  let batch = [];

  do {
    batch = await CommunityFolderModel.aggregate([
      {
        $match:
          /**
           * query: The query in MQL.
           */
          {
            status: {
              $in: [
                COMMUNITY_FOLDER_STATUS.PUBLISHED,
                COMMUNITY_FOLDER_STATUS.UNPUBLISHED,
              ],
            },
            type: communityFolderTypesMap.SESSION,
          },
      },
      {
        $lookup:
          /**
           * from: The target collection.
           * localField: The local join field.
           * foreignField: The target join field.
           * as: The name for the results.
           * pipeline: Optional pipeline to run on the foreign collection.
           * let: Optional variables to use in the pipeline field stages.
           */
          {
            from: 'community_product',
            localField: '_id',
            foreignField: 'entityObjectId',
            as: 'product',
          },
      },
      {
        $match:
          /**
           * query: The query in MQL.
           */
          {
            $or: [
              {
                'product.0': {
                  $exists: false,
                },
              },
              {
                'product.0.productType': {
                  $ne: PRODUCT_TYPE.SESSION,
                },
              },
            ],
          },
      },
      {
        $limit:
          /**
           * Provide the number of documents to limit.
           */
          300,
      },
    ]);

    if (batch.length <= 0) {
      break;
    }
    try {
      logger.info(
        `Sync sessions: batch [${batch[0]._id}], createdAt = ${batch[0].createdAt}`
      );
      await SyncProductDataService.bulkCreateData({
        productType: PRODUCT_TYPE.SESSION,
        entityList: batch,
      });
    } catch (err) {
      const ids = batch.map((event) => event._id);
      await fs.appendFile(
        `logs/syncDataError_sessions_${currentTimestampInMs}.log`,
        `${ids}, ${err.message}\n`
      );
    }
  } while (batch.length > 0);
};

const syncChallenges = async () => {
  const currentTimestampInMs = Date.now();
  let batch = [];

  do {
    batch = await ProgramModel.aggregate([
      {
        $match:
          /**
           * query: The query in MQL.
           */
          {
            status: {
              $in: [PROGRAM_STATUS.PUBLISHED, PROGRAM_STATUS.DRAFT],
            },
          },
      },
      {
        $lookup:
          /**
           * from: The target collection.
           * localField: The local join field.
           * foreignField: The target join field.
           * as: The name for the results.
           * pipeline: Optional pipeline to run on the foreign collection.
           * let: Optional variables to use in the pipeline field stages.
           */
          {
            from: 'community_product',
            localField: '_id',
            foreignField: 'entityObjectId',
            as: 'product',
          },
      },
      {
        $match:
          /**
           * query: The query in MQL.
           */
          {
            $or: [
              {
                'product.0': {
                  $exists: false,
                },
              },
              {
                'product.0.productType': {
                  $ne: PRODUCT_TYPE.CHALLENGE,
                },
              },
            ],
          },
      },
      {
        $limit:
          /**
           * Provide the number of documents to limit.
           */
          300,
      },
    ]);

    if (batch.length <= 0) {
      break;
    }
    try {
      logger.info(
        `Sync challenge: batch [${batch[0]._id}], createdAt = ${batch[0].createdAt}`
      );
      await SyncProductDataService.bulkCreateData({
        productType: PRODUCT_TYPE.CHALLENGE,
        entityList: batch,
      });
    } catch (err) {
      const ids = batch.map((event) => event._id);
      await fs.appendFile(
        `logs/syncDataError_challenge_${currentTimestampInMs}.log`,
        `${ids}, ${err.message}\n`
      );
    }
  } while (batch.length > 0);
};

const syncAllProductData = async () => {
  const communityObjectId = new ObjectId('64212fefa1abf54df43dc37d');

  await Promise.all([
    syncEvents(),
    syncDigitalProducts(),
    syncSessions(),
    syncChallenges(),
  ]);
};

const start = async () => {
  await mongoClient.connect();

  await syncAllProductData();

  console.log('Completed');
  process.exit(0);
};

start();
