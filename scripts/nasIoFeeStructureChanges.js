require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const fs = require('fs').promises;
const logger = require('../src/services/logger.service');

const mongoClient = require('../src/mongoClient');
const Communities = require('../src/communitiesAPI/models/community.model');
const RevenueTransaction = require('../src/models/revenueTransaction.model');
const PurchaseTransaction = require('../src/communitiesAPI/models/communityPurchaseTransactions.model');
const AddonTransaction = require('../src/communitiesAPI/models/communityAddonTransactions.model');
const PaymentProviderUtils = require('../src/utils/paymentProvider.util');
const CommunityService = require('../src/communitiesAPI/services/common/community.service');
const Discount = require('../src/communitiesAPI/models/communityDiscounts.model');
const {
  retrieveProductWithPrices,
} = require('../src/communitiesAPI/services/common/community.service');
const { CENTS_PER_DOLLAR } = require('../src/communitiesAPI/constants');
const {
  normalizeAmountByCurrency,
} = require('../src/utils/currency.util');

async function updateCommunityStripePrices() {
  const csvStream = [];
  csvStream.push(
    `_id,code,stripeProductId,baseCurrency,passOntakeRate,payment_methods,createdAt,payoutFeeConfigs,productPrices,communityPricesForUpdate,updatedPricesDetail,status,error`
      .replace(/\n/g, '')
      .replace(/ /g, '')
  );
  const communities = await Communities.find(
    {
      isActive: true,
      isPaidCommunity: true,
      passOnTakeRate: true,
      'metadata.priceUpdated': { $ne: true },
    },
    {
      _id: 1,
      code: 1,
      stripeProductId: 1,
      baseCurrency: 1,
      passOnTakeRate: 1,
      prices: 1,
      payment_methods: 1,
      createdAt: 1,
      payoutFeeConfigs: 1,
    }
  )
    .limit()
    .lean();

  await Promise.allSettled(
    communities.map(async (community) => {
      const updateData = {};
      const paymentProvider =
        await PaymentProviderUtils.retrievePaymentProvider(
          community.payment_methods
        );

      const productPrices = await retrieveProductWithPrices(
        community.stripeProductId,
        community.baseCurrency,
        community.passOnTakeRate,
        community.prices,
        paymentProvider
      );

      const communityPricesForUpdate = productPrices.map((price) => ({
        currency: price.currency,
        amount: Math.ceil(price.amount * CENTS_PER_DOLLAR) / 100,
        interval: price.recurring.interval,
        intervalCount: price.recurring.intervalCount,
      }));

      const updatedPricesDetail = {};

      try {
        //Update the error thrown to give more details before running the following function
        // const updatedPricesDetail = await CommunityService.updatePrices(
        //   community.stripeProductId,
        //   community.createdAt,
        //   communityPricesForUpdate,
        //   community.baseCurrency,
        //   community.passOnTakeRate,
        //   community.payoutFeeConfigs,
        //   paymentProvider,
        //   false
        // );

        updateData.prices = updatedPricesDetail;
        if (updateData.metadata) {
          updateData.metadata.priceUpdated = true;
        } else {
          updateData.metadata = {
            priceUpdated: true,
          };
        }

        updateData.stripeProductId = community.stripeProductId;

        const csvRow =
          `${community._id ?? '-'},${community.code ?? '-'},${
            community.stripeProductId ?? '-'
          },${community.baseCurrency ?? '-'},${
            community.passOnTakeRate ?? '-'
          },` +
          `${
            JSON.stringify(community.payment_methods).replace(/,/g, '') ??
            '-'
          },${community.createdAt ?? '-'},${
            JSON.stringify(community.payoutFeeConfigs).replace(/,/g, '') ??
            '-'
          },${JSON.stringify(productPrices).replace(/,/g, '') ?? '-'},` +
          `${
            JSON.stringify(communityPricesForUpdate).replace(/,/g, '') ??
            '-'
          },${JSON.stringify(updateData).replace(/,/g, '') ?? '-'}`;

        csvStream.push(csvRow);
        // await Communities.findOneAndUpdate(
        //   { _id: community._id },
        //   updateData,
        //   { new: true }
        // ).lean();
      } catch (err) {
        const csvRow =
          `${community._id ?? '-'},${community.code ?? '-'},${
            community.stripeProductId ?? '-'
          },${community.baseCurrency ?? '-'},${
            community.passOnTakeRate ?? '-'
          },` +
          `${
            JSON.stringify(community.payment_methods).replace(/,/g, '') ??
            '-'
          },${community.createdAt ?? '-'},${
            JSON.stringify(community.payoutFeeConfigs).replace(/,/g, '') ??
            '-'
          },${JSON.stringify(productPrices).replace(/,/g, '') ?? '-'},` +
          `${
            JSON.stringify(communityPricesForUpdate).replace(/,/g, '') ??
            '-'
          },-,ERROR,${err.message.replace(/,/g, '')}`;
        csvStream.push(csvRow);
      }
    })
  );

  const data = csvStream.join('\n');
  await fs.writeFile(
    'updateCommunityStripePrices.csv',
    data,
    'utf-8',
    (err) => {
      if (err) console.log(err);
      else console.log('Data saved');
    }
  );
}

async function updateCommunityPayoutFeeConfigs() {
  const csvStream = [];
  csvStream.push(`_id,code,community.payoutFeeConfig,newPayoutFeeConfig`);
  // Update the community payoutFeeConfig All according to configs.
  const communities = await Communities.find(
    { isActive: true, payoutFeeConfigs: { $ne: [] } },
    { _id: 1, payoutFeeConfigs: 1, code: 1 }
  ).lean();

  await Promise.all(
    communities.map(async (community) => {
      if (!community.payoutFeeConfigs) return;
      const payoutFeeConfigs = community.payoutFeeConfigs.map(
        (payoutFeeConfig) => {
          const result = { ...payoutFeeConfig };
          if (payoutFeeConfig.paymentProviderFee) {
            if (!payoutFeeConfig.paymentProviderFee.all) {
              result.paymentProviderFee.all =
                payoutFeeConfig.paymentProviderFee.stripe;
            }
          }
          return result;
        }
      );
      // const updated = await Communities.findOneAndUpdate(
      //   { _id: community._id },
      //   { payoutFeeConfigs },
      //   { new: true }
      // );
      const csvRow =
        `${community._id ?? '-'},${community.code ?? '-'},` +
        `${
          JSON.stringify(community.payoutFeeConfigs).replace(/,/g, '') ??
          '-'
        },${JSON.stringify(payoutFeeConfigs).replace(/,/g, '') ?? '-'}`;

      csvStream.push(csvRow);
    })
  );

  const data = csvStream.join('\n');
  await fs.writeFile(
    'updateCommunityPayoutFeeConfigs.csv',
    data,
    'utf-8',
    (err) => {
      if (err) console.log(err);
      else console.log('Data saved');
    }
  );
}

async function updateCommunityCustomPayoutFeeConfigs(array) {
  const csvStream = [];
  csvStream.push(`_id,code,community.payoutFeeConfig,newPayoutFeeConfig`);
  // Update the community payoutFeeConfig All according to configs.
  const effectiveTimeStart = new Date(Date.UTC(2024, 3, 1, 0, 0, 0));
  const effectiveTimeEnd = new Date(Date.UTC(3000, 0, 1, 0, 0, 0));
  await Promise.all(
    array.map(async (details) => {
      const community = await Communities.findOne(
        { isActive: true, code: details.communityCode },
        { _id: 1, payoutFeeConfigs: 1, code: 1 }
      ).lean();

      if (!community) {
        const csvRow =
          `${'Not Found'},${details.communityCode ?? '-'},` +
          `${'-'},${'-'}`;
        csvStream.push(csvRow);
        return;
      }

      const newPayoutFeeConfig = {
        effectiveTimeStart,
        effectiveTimeEnd,
        paymentProviderFee: {
          all: {
            ALL: {},
          },
        },
      };

      if (details.revenueShareInPercentage) {
        const revenueShareInPercentage = parseFloat(
          details.revenueShareInPercentage.replace(/%/g, '')
        );
        newPayoutFeeConfig.revenueShareInPercentage =
          revenueShareInPercentage;
      }

      if (
        details.gatewayFeeInPercentage &&
        details.gatewayFeeInPercentage.replace(/ /g, '') !== 'standard'
      ) {
        const gatewayFeeInPercentage = parseFloat(
          details.gatewayFeeInPercentage.replace(/%/g, '')
        );
        newPayoutFeeConfig.paymentProviderFee.all.ALL.gatewayFeeInPercentage =
          gatewayFeeInPercentage;
      }

      const processingFee = parseFloat(details.processingFee);
      newPayoutFeeConfig.paymentProviderFee.all.ALL.processingFee =
        processingFee;

      const minGatewayFee = parseFloat(details.minGatewayFee);
      newPayoutFeeConfig.paymentProviderFee.all.ALL.minGatewayFee =
        minGatewayFee;

      const payoutFeeConfigs = [];
      (community.payoutFeeConfigs ?? []).forEach((payout) => {
        const result = { ...payout };
        if (
          payout.effectiveTimeStart.toISOString() ===
          effectiveTimeStart.toISOString()
        ) {
          return;
        }
        if (payout.paymentProviderFee?.stripe) {
          result.paymentProviderFee.all = payout.paymentProviderFee.stripe;
        }
        if (payout.effectiveTimeEnd > effectiveTimeStart) {
          result.effectiveTimeEnd = effectiveTimeStart;
        }

        payoutFeeConfigs.push(result);
      });

      payoutFeeConfigs.push(newPayoutFeeConfig);
      let csvRow = '';
      try {
        // await Communities.findOneAndUpdate(
        //   { _id: community._id },
        //   { payoutFeeConfigs },
        //   { new: true }
        // );

        csvRow =
          `${community._id ?? '-'},${community.code ?? '-'},` +
          `${
            JSON.stringify(community.payoutFeeConfigs).replace(/,/g, '') ??
            '-'
          },` +
          `${JSON.stringify(payoutFeeConfigs).replace(/,/g, '') ?? '-'}`;
      } catch (err) {
        csvRow =
          `${community._id ?? '-'},${community.code ?? '-'},` +
          `${
            JSON.stringify(community.payoutFeeConfigs).replace(/,/g, '') ??
            '-'
          },` +
          `${
            JSON.stringify(payoutFeeConfigs).replace(/,/g, '') ?? '-'
          },ERROR,${JSON.stringify(err).replace(/,/g, '')}`;
      }

      csvStream.push(csvRow);
    })
  );
  const data = csvStream.join('\n');
  await fs.writeFile(
    'updateCommunityCustomPayoutFeeConfigs.csv',
    data,
    'utf-8',
    (err) => {
      if (err) console.log(err);
      else console.log('Data saved');
    }
  );
}

async function updateRevenueTransactionItemPricesInBatch(limit = 1000) {
  const csvStream = [];
  const csvRow = `_id,amountBreakdownInUsd,amountBreakdownInLocalCurrency,toUpdate`;
  csvStream.push(csvRow);
  // Update the community prices revenueShare field.
  const revenueTransactions = await RevenueTransaction.find(
    {
      transactionType: 'INBOUND',
      'amountBreakdownInUsd.discountedItemPrice': { $exists: false },
      transactionCreatedAt: {
        $lt: new Date(Date.UTC(2024, 4, 1, 0, 0, 0)),
      },
    },
    {
      _id: 1,
      amountBreakdownInUsd: 1,
      amountBreakdownInLocalCurrency: 1,
      amountBreakdownInOriginalCurrency: 1,
      passOnTakeRate: 1,
      revenueSharePercentage: 1,
      discountCode: 1,
    }
  )
    .limit(limit)
    .lean();

  if (revenueTransactions.length === 0) {
    console.log('No more!');
    return;
  }

  const pipeline = [];

  await Promise.all(
    revenueTransactions.map(async (transaction) => {
      const toUpdate = {};
      const passOnTakeRate = transaction.passOnTakeRate;
      const revenueSharePercentage = transaction.revenueSharePercentage;
      let discount;
      if (transaction.discountCode) {
        discount = await Discount.findOne({
          code: transaction.discountCode,
        }).lean();
      }
      if (transaction.amountBreakdownInUsd) {
        const originalAmount =
          transaction.amountBreakdownInUsd.originalAmount; // 5400
        const itemPrice = Math.ceil(
          (originalAmount /
            (100 + (passOnTakeRate ? revenueSharePercentage : 0))) *
            100
        );

        let discountedItemPrice = itemPrice;
        if (discount) {
          discountedItemPrice =
            itemPrice - Math.ceil((itemPrice / 100) * discount.value);
        }
        toUpdate['amountBreakdownInUsd.discountedItemPrice'] =
          discountedItemPrice;
        toUpdate['amountBreakdownInUsd.itemPrice'] = itemPrice;
      }
      if (transaction.amountBreakdownInLocalCurrency) {
        const originalAmount =
          transaction.amountBreakdownInLocalCurrency.originalAmount; // 5400
        const itemPrice = Math.ceil(
          (originalAmount /
            (100 + (passOnTakeRate ? revenueSharePercentage : 0))) *
            100
        );

        let discountedItemPrice = itemPrice;
        if (discount) {
          discountedItemPrice =
            itemPrice - Math.ceil((itemPrice / 100) * discount.value);
        }
        toUpdate['amountBreakdownInLocalCurrency.discountedItemPrice'] =
          discountedItemPrice;
        toUpdate['amountBreakdownInLocalCurrency.itemPrice'] = itemPrice;
      }
      if (transaction.amountBreakdownInOriginalCurrency) {
        const originalAmount =
          transaction.amountBreakdownInOriginalCurrency.originalAmount; // 5400
        const itemPrice = Math.ceil(
          (originalAmount /
            (100 + (passOnTakeRate ? revenueSharePercentage : 0))) *
            100
        );

        let discountedItemPrice = itemPrice;
        if (discount) {
          discountedItemPrice =
            itemPrice - Math.ceil((itemPrice / 100) * discount.value);
        }
        toUpdate['amountBreakdownInOriginalCurrency.discountedItemPrice'] =
          discountedItemPrice;
        toUpdate['amountBreakdownInOriginalCurrency.itemPrice'] =
          itemPrice;
      }

      pipeline.push({
        updateOne: {
          filter: { _id: transaction._id },
          update: { $set: toUpdate },
        },
      });

      csvStream.push(
        `${transaction._id ?? '-'},${
          JSON.stringify(transaction.amountBreakdownInUsd).replace(
            /,/g,
            ''
          ) ?? '-'
        },` +
          `${
            JSON.stringify(
              transaction.amountBreakdownInLocalCurrency
            ).replace(/,/g, '') ?? '-'
          },` +
          `${JSON.stringify(toUpdate).replace(/,/g, '') ?? '-'},`
      );
    })
  );

  try {
    // await RevenueTransaction.bulkWrite(pipeline);
  } catch (err) {
    csvStream.push(`ERROR,${JSON.stringify(err).replace(/,/g, '')}`);
  }

  const data = csvStream.join('\n');
  await fs.writeFile(
    'updateRevenueTransactionItemPricesInBatch.csv',
    data,
    'utf-8',
    (err) => {
      if (err) console.log(err);
      else console.log('Data saved');
    }
  );
}

async function updateNewRevenueTransactionItemPricesInBatch(limit = 1000) {
  const csvStream = [];
  const csvRow = `_id,amountBreakdownInUsd,amountBreakdownInLocalCurrency,toUpdate`;
  csvStream.push(csvRow);
  // Update the community prices revenueShare field.
  const date = new Date(Date.UTC(2024, 3, 1, 0, 0, 0));
  const revenueTransactions = await RevenueTransaction.find(
    {
      transactionType: 'INBOUND',
      // 'amountBreakdownInUsd.discountedItemPrice': { $exists: false },
      transactionCreatedAt: {
        $gte: date,
        $lte: new Date(Date.UTC(2024, 3, 4, 2, 47, 58)),
      },
    },
    {
      _id: 1,
      amountBreakdownInUsd: 1,
      amountBreakdownInLocalCurrency: 1,
      amountBreakdownInOriginalCurrency: 1,
      passOnTakeRate: 1,
      revenueSharePercentage: 1,
      discountCode: 1,
      transactionCreatedAt: 1,
      purchasedId: 1,
      purchaseType: 1,
    }
  )
    .limit(limit)
    .lean();

  if (revenueTransactions.length === 0) {
    console.log('No more!');
    return;
  }

  const pipeline = [];

  await Promise.all(
    revenueTransactions.map(async (transaction) => {
      const toUpdate = {};
      const passOnTakeRate = transaction.passOnTakeRate;
      const revenueSharePercentage = transaction.revenueSharePercentage;
      let discount;
      if (transaction.discountCode) {
        discount = await Discount.findOne({
          code: transaction.discountCode,
        }).lean();
      }
      let purchaseTransaction;
      let itemPrice;
      if (transaction.purchaseType === 'SUBSCRIPTION') {
        purchaseTransaction = await PurchaseTransaction.findById(
          transaction.purchasedId
        );
        itemPrice = purchaseTransaction.priceDetails?.itemPrice;
      } else {
        purchaseTransaction = await AddonTransaction.findById(
          transaction.purchasedId
        );
        itemPrice = purchaseTransaction.priceDetails?.amount;
      }

      if (
        transaction.amountBreakdownInLocalCurrency.itemPrice !== itemPrice
      ) {
        let discountedItemPrice = itemPrice;
        if (discount) {
          discountedItemPrice =
            itemPrice -
            normalizeAmountByCurrency(
              Math.ceil((itemPrice / 100) * discount.value),
              transaction.amountBreakdownInLocalCurrency.currency
            );
        }

        if (
          itemPrice !== undefined &&
          itemPrice !== null &&
          discountedItemPrice !== undefined &&
          discountedItemPrice !== null
        ) {
          toUpdate['amountBreakdownInLocalCurrency.discountedItemPrice'] =
            discountedItemPrice;
          toUpdate['amountBreakdownInLocalCurrency.itemPrice'] = itemPrice;

          if (transaction.amountBreakdownInOriginalCurrency) {
            toUpdate[
              'amountBreakdownInOriginalCurrency.discountedItemPrice'
            ] = discountedItemPrice;
            toUpdate['amountBreakdownInOriginalCurrency.itemPrice'] =
              itemPrice;
          }

          let usdDiscountedPrice = discountedItemPrice;
          let usdPrice = itemPrice;
          if (
            transaction.amountBreakdownInLocalCurrency.currency !== 'USD'
          ) {
            usdDiscountedPrice = normalizeAmountByCurrency(
              Math.ceil(
                discountedItemPrice *
                  transaction.amountBreakdownInUsd.exchangeRate
              ),
              'USD'
            );
            usdPrice = normalizeAmountByCurrency(
              Math.ceil(
                itemPrice * transaction.amountBreakdownInUsd.exchangeRate
              ),
              'USD'
            );
          }

          toUpdate['amountBreakdownInUsd.discountedItemPrice'] =
            usdDiscountedPrice;
          toUpdate['amountBreakdownInUsd.itemPrice'] = usdPrice;

          pipeline.push({
            updateOne: {
              filter: { _id: transaction._id },
              update: { $set: toUpdate },
            },
          });
          // console.log(
          //   `${transaction.amountBreakdownInLocalCurrency.currency} ${itemPrice} <- ${
          //     transaction.amountBreakdownInLocalCurrency.itemPrice
          //   } | ${discountedItemPrice} <- ${
          //     transaction.amountBreakdownInLocalCurrency
          //       .discountedItemPrice
          //   } | USD ${usdPrice}  <- ${
          //     transaction.amountBreakdownInUsd.itemPrice
          //   } |  ${usdDiscountedPrice}  <- ${
          //     transaction.amountBreakdownInUsd.discountedItemPrice
          //   } | ${JSON.stringify(transaction._id)} `
          // );
        } else {
          console.log(
            `${
              transaction.amountBreakdownInLocalCurrency.currency
            } ${itemPrice} <- ${
              transaction.amountBreakdownInLocalCurrency.itemPrice
            } | ${discountedItemPrice} <- ${
              transaction.amountBreakdownInLocalCurrency
                .discountedItemPrice
            } |   <- ${transaction.amountBreakdownInUsd.itemPrice} |  <- ${
              transaction.amountBreakdownInUsd.discountedItemPrice
            } | ${JSON.stringify(transaction._id)} `
          );
        }
      }

      csvStream.push(
        `${transaction._id ?? '-'},${
          JSON.stringify(transaction.amountBreakdownInUsd).replace(
            /,/g,
            ''
          ) ?? '-'
        },` +
          `${
            JSON.stringify(
              transaction.amountBreakdownInLocalCurrency
            ).replace(/,/g, '') ?? '-'
          },` +
          `${JSON.stringify(toUpdate).replace(/,/g, '') ?? '-'},`
      );
    })
  );

  try {
    // await RevenueTransaction.bulkWrite(pipeline);
  } catch (err) {
    csvStream.push(`ERROR,${JSON.stringify(err).replace(/,/g, '')}`);
  }

  const data = csvStream.join('\n');
  await fs.writeFile(
    'updateNewRevenueTransactionItemPricesInBatch.csv',
    data,
    'utf-8',
    (err) => {
      if (err) console.log(err);
      else console.log('Data saved');
    }
  );
}

async function start() {
  await mongoClient.connect();
  // const array = [
  //   {
  //     communityCode: 'Test',
  //     revenueShareInPercentage: '5%',
  //     gatewayFeeInPercentage: '4%',
  //     processingFee: 0,
  //     minGatewayFee: 0,
  //   },
  // ];
  // await updateCommunityCustomPayoutFeeConfigs(array);
  // await updateCommunityPayoutFeeConfigs();
  // await updateRevenueTransactionItemPricesInBatch(1000);
  await updateNewRevenueTransactionItemPricesInBatch();

  // await updateCommunityStripePrices();
  logger.info('Completed');
  process.exit(0);
}

start();
