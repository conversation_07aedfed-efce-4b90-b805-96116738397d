require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const logger = require('../src/services/logger.service');
const mongoClient = require('../src/mongoClient');
const PlanOrderModel = require('../src/models/plan/communityPlanOrder.model');

const start = async () => {
  await mongoClient.connect();

  const planOrders = await PlanOrderModel.find({
    'paymentDetails.completedPayment': true,
    metadata: { $exists: true },
  }).lean();

  // PROD
  //   const STRIPE_US_FIRST_BILLING_PIRCE = 'price_1RLH3OGZrwG9MfzHE3qs1v9u';
  //   const STRIPE_US_YEARLY_PIRCE = 'price_1RLH5dGZrwG9MfzHAoRLqcPh';
  //   const STRIPE_US_MONTHLY_PIRCE = 'price_1RLH5dGZrwG9MfzHIMi9JUaZ';

  //   const STRIPE_INDIA_FIRST_BILLING_PIRCE =
  //     'price_1RLGJxSB6uplFIanRVqSm40x';
  //   const STRIPE_INDIA_YEARLY_PIRCE = 'price_1RLGLWSB6uplFIanqWw7oQSA';
  //   const STRIPE_INDIA_MONTHLY_PIRCE = 'price_1RLGLWSB6uplFIanjliKARLD';

  //DEV
  const STRIPE_US_FIRST_BILLING_PIRCE = 'price_1RHNVpGZrwG9MfzHyYq4gtp7';
  const STRIPE_US_YEARLY_PIRCE = 'price_1RLGyKGZrwG9MfzHj12ALLBP';
  const STRIPE_US_MONTHLY_PIRCE = 'price_1RH1K6GZrwG9MfzHrjpT7Fpv';

  const STRIPE_INDIA_FIRST_BILLING_PIRCE =
    'price_1RLFGWSB6uplFIanGTqWOXJb';
  const STRIPE_INDIA_YEARLY_PIRCE = 'price_1RH5lbSB6uplFIanWdh2tWoO';
  const STRIPE_INDIA_MONTHLY_PIRCE = 'price_1RH5l9SB6uplFIanwuxVedaS';

  const RECURRING_US_PRICES_FOR_14_DAY = [
    STRIPE_US_YEARLY_PIRCE,
    STRIPE_US_MONTHLY_PIRCE,
  ];
  const RECURRING_INDIA_PRICES_FOR_14_DAY = [
    STRIPE_INDIA_YEARLY_PIRCE,
    STRIPE_INDIA_MONTHLY_PIRCE,
  ];

  const CURRENCY_PRICE_MAPPING = {
    AED: 400,
    ARS: 129000,
    AUD: 100,
    BRL: 500,
    CAD: 100,
    CLP: 90000,
    COP: 500000,
    EUR: 100,
    GBP: 100,
    HUF: 40000,
    IDR: 1500000,
    ILS: 300,
    INR: 9900,
    JPY: 14900,
    MXN: 1999,
    MYR: 500,
    PEN: 400,
    PHP: 5000,
    SGD: 200,
    VND: 2500000,
  };

  const pipeline = [];
  planOrders.forEach((item) => {
    if (
      item.metadata.firstBilling &&
      item.metadata.firstBilling.paymentSuccessTime
    ) {
      return;
    }
    if (
      item.metadata.firstBilling &&
      !item.metadata.firstBilling.paymentSuccessTime
    ) {
      pipeline.push({
        updateOne: {
          filter: {
            _id: item._id,
          },
          update: {
            'metadata.firstBilling.paymentSuccessTime':
              item.paymentDetails.latestUpdatedTime,
          },
        },
      });
      return;
    }
    const { paymentProviderPriceId, localCurrency, billingCycle } = item;
    let priceId;
    if (RECURRING_US_PRICES_FOR_14_DAY.includes(paymentProviderPriceId)) {
      priceId = STRIPE_US_FIRST_BILLING_PIRCE;
    } else if (
      RECURRING_INDIA_PRICES_FOR_14_DAY.includes(paymentProviderPriceId)
    ) {
      priceId = STRIPE_INDIA_FIRST_BILLING_PIRCE;
    } else {
      return;
    }

    if (!CURRENCY_PRICE_MAPPING[localCurrency]) {
      return;
    }
    const firstBilling = {
      priceId,
      interval: 'day',
      intervalCount: 14,
      amountInLocalCurrency: CURRENCY_PRICE_MAPPING[localCurrency],
      localCurrency,
      paymentSuccessTime: item.paymentDetails.latestUpdatedTime,
    };
    const updateQuery = {};
    updateQuery['metadata.firstBilling'] = firstBilling;
    if (billingCycle === 1) {
      updateQuery.isOnTrial = true;
    }

    pipeline.push({
      updateOne: {
        filter: {
          _id: item._id,
        },
        update: updateQuery,
      },
    });
  });
  await PlanOrderModel.bulkWrite(pipeline);

  logger.info('Completed');
  process.exit(0);
};

start();
