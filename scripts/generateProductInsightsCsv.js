require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});
const fs = require('fs').promises;

const { ObjectId } = require('mongoose').Types;

const mongoClient = require('../src/mongoClient');
const CommunityModel = require('../src/communitiesAPI/models/community.model');
const EventModel = require('../src/communitiesAPI/models/communityEvents.model');
const FolderModel = require('../src/communitiesAPI/models/communityFolders.model');
const ProgramModel = require('../src/models/program/program.model');
const AddonTransactionsModel = require('../src/communitiesAPI/models/communityAddonTransactions.model');
const RevenueTransactionModel = require('../src/models/revenueTransaction.model');
const {
  COMMUNITY_ENTITY_ONE_TIME_PAYMENT_STATUSES,
  COMMUNITY_ONE_TIME_PAYMENT_ENTITIES,
  communityLibraryTypesMap,
} = require('../src/communitiesAPI/constants');
const { PROGRAM_TYPE } = require('../src/services/program/constants');
const { PURCHASE_TYPE } = require('../src/constants/common');

const gmvCache = new Map();

async function retrieveGmvInUsd(communityObjectId, startDate, endDate) {
  if (gmvCache.has(communityObjectId.toString())) {
    return gmvCache.get(communityObjectId.toString());
  }

  const revenueTransaction = await RevenueTransactionModel.aggregate([
    {
      $match: {
        transactionCreatedAt: {
          $gte: startDate,
          $lt: endDate,
        },
        purchaseType: {
          $in: [
            PURCHASE_TYPE.SUBSCRIPTION,
            PURCHASE_TYPE.EVENT,
            PURCHASE_TYPE.FOLDER,
            PURCHASE_TYPE.SESSION,
            PURCHASE_TYPE.CHALLENGE,
          ],
        },
        communityObjectId: new ObjectId(communityObjectId),
      },
    },
    {
      $project: {
        multiplier: {
          $cond: {
            if: {
              $eq: ['$transactionType', 'OUTBOUND'],
            },
            then: -1,
            else: 1,
          },
        },
        gmvInUsd: '$amountBreakdownInUsd.discountedItemPrice',
      },
    },
    {
      $project: {
        gmvInUsd: {
          $multiply: ['$gmvInUsd', '$multiplier'],
        },
      },
    },
    {
      $group: {
        _id: null,
        gmvInUsd: {
          $sum: '$gmvInUsd',
        },
      },
    },
    {
      $project: {
        gmvInUsd: {
          $divide: ['$gmvInUsd', 100],
        },
      },
    },
  ]);

  const gmvInUsd = revenueTransaction?.[0]?.gmvInUsd ?? 0;

  gmvCache.set(communityObjectId.toString(), gmvInUsd);

  return gmvInUsd;
}

async function retrieveAddonInfo(entityCollection, entityObjectId) {
  const addonInfo = {
    type: '',
    slug: '',
    price: 0,
    currency: '',
    communityCode: '',
  };

  switch (entityCollection) {
    case COMMUNITY_ONE_TIME_PAYMENT_ENTITIES.EVENT: {
      const event = await EventModel.findById(entityObjectId).lean();

      if (!event) {
        throw new Error('Event not found');
      }

      const community = await CommunityModel.findById(
        event.communities[0]
      ).lean();

      if (!community) {
        throw new Error('Community not found');
      }

      const productSlug = `https://nas.io${community.link}${event.slug}`;

      addonInfo.type = PURCHASE_TYPE.EVENT;
      addonInfo.slug = productSlug;
      addonInfo.price = event.amount ?? 0;
      addonInfo.currency = event.currency;
      addonInfo.communityCode = community.code;
      addonInfo.communityObjectId = community._id;
      addonInfo.baseCurrency = community.baseCurrency;
      addonInfo.communityCountry = community.countryCreatedIn;

      break;
    }
    case COMMUNITY_ONE_TIME_PAYMENT_ENTITIES.FOLDER: {
      const folder = await FolderModel.findById(entityObjectId).lean();

      if (!folder) {
        throw new Error('Folder not found');
      }

      const community = await CommunityModel.findById(
        folder.communityObjectId
      ).lean();

      if (!community) {
        throw new Error('Community not found');
      }

      const productSlug = `https://nas.io${community.link}${folder.resourceSlug}`;

      addonInfo.type =
        folder.type === communityLibraryTypesMap.DIGITAL_PRODUCT
          ? PURCHASE_TYPE.FOLDER
          : PURCHASE_TYPE.SESSION;
      addonInfo.slug = productSlug;
      addonInfo.price = folder.amount ?? 0;
      addonInfo.currency = folder.currency;
      addonInfo.communityCode = community.code;
      addonInfo.communityObjectId = community._id;
      addonInfo.baseCurrency = community.baseCurrency;
      addonInfo.communityCountry = community.countryCreatedIn;

      break;
    }
    case COMMUNITY_ONE_TIME_PAYMENT_ENTITIES.CHALLENGE: {
      const program = await ProgramModel.findById(entityObjectId).lean();

      if (!program) {
        throw new Error('Folder not found');
      }

      const community = await CommunityModel.findById(
        program.communityObjectId
      ).lean();

      if (!community) {
        throw new Error('Community not found');
      }

      const productSlug = `https://nas.io${community.link}${program.slug}`;

      addonInfo.type =
        program.type === PROGRAM_TYPE.CHALLENGE
          ? PURCHASE_TYPE.CHALLENGE
          : 'COURSE';
      addonInfo.slug = productSlug;
      addonInfo.price = program.pricingConfig?.amount ?? 0;
      addonInfo.currency = program.pricingConfig?.currency ?? '';
      addonInfo.startTime = program.startTime;
      addonInfo.communityCode = community.code;
      addonInfo.communityObjectId = community._id;
      addonInfo.baseCurrency = community.baseCurrency;
      addonInfo.communityCountry = community.countryCreatedIn;

      break;
    }
    default:
      throw new Error(`Invalid entity collection ${entityCollection}`);
  }

  return addonInfo;
}

async function retrieveAddonTransactions(startDate, endDate) {
  return AddonTransactionsModel.aggregate([
    {
      $match: {
        'payment_details.status':
          COMMUNITY_ENTITY_ONE_TIME_PAYMENT_STATUSES.SUCCESS,
        'payment_details.succeeded_time': {
          $gte: startDate,
          $lt: endDate,
        },
      },
    },
    {
      $group: {
        _id: {
          entityObjectId: '$entityObjectId',
          entityCollection: '$entityCollection',
        },
        firstDateOfPurchase: {
          $min: '$payment_details.succeeded_time',
        },
      },
    },
  ]);
}

const start = async () => {
  await mongoClient.connect();

  const currentTimestampInMs = Date.now();

  const startDate = new Date('2024-08-01');
  const endDate = new Date('2024-09-01');

  const addonTransactions = await retrieveAddonTransactions(
    startDate,
    endDate
  );

  for await (const addonTransaction of addonTransactions) {
    const { firstDateOfPurchase } = addonTransaction;
    const { entityObjectId, entityCollection } = addonTransaction._id;

    const addonInfo = await retrieveAddonInfo(
      entityCollection,
      entityObjectId
    );

    const {
      communityObjectId,
      type,
      slug,
      startTime,
      price,
      currency,
      communityCode,
      communityCountry,
      baseCurrency,
    } = addonInfo;

    const gmvInUsd = await retrieveGmvInUsd(
      communityObjectId,
      startDate,
      endDate
    );

    const message = `${communityCode},${type},${firstDateOfPurchase.toISOString()},${gmvInUsd},${
      price / 100
    },${currency},${
      startTime != null ? startTime.toISOString() : ''
    },${slug},${communityCountry},${baseCurrency}\n`;

    console.log(message);

    await fs.appendFile(
      `logs/products_insights_${currentTimestampInMs}.csv`,
      message
    );
  }

  console.log('Completed');
  process.exit(0);
};

start();
