/* eslint-disable no-unused-vars */
require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

// const ObjectId = require('mongoose').Types.ObjectId;

const mongoClient = require('../src/mongoClient');

const CommunityModel = require('../src/communitiesAPI/models/community.model');
const FolderModel = require('../src/communitiesAPI/models/communityFolders.model');
const FolderItemsModel = require('../src/communitiesAPI/models/communityFolderItems.model');
const PrimaryMongooseConnection = require('../src/rpc/primaryMongooseConnection');
const {
  communityLibraryStatusMap,
} = require('../src/communitiesAPI/constants');
const { generateSlug } = require('../src/utils/slug.util');

async function retrieveCommunities(communityCodes) {
  const communities = await CommunityModel.find({
    code: communityCodes,
  }).lean();

  if (communities.length !== communityCodes.length) {
    throw new Error(
      `Missing some community ${communities.length}/${communityCodes.length}`
    );
  }

  return communities;
}

async function retrieveFoldersAndItems(
  communityObjectId,
  extraFilters = {}
) {
  const folders = await FolderModel.find({
    ...extraFilters,
    communityObjectId,
    type: 'DIGITAL_PRODUCT',
    // access: 'free',
  }).lean();

  const folderObjectIds = folders.map((folder) => folder._id);

  // const somePaidFolder = folders.some(
  //   (folder) => folder.access === 'paid'
  // );

  // if (somePaidFolder) {
  //   throw new Error('Only support to copy free folders');
  // }

  const folderItems = await FolderItemsModel.find({
    communityFolderObjectId: {
      $in: folderObjectIds,
    },
  }).lean();

  const folderItemsCache = folderItems.reduce((acc, folderItem) => {
    const folderObjectId = folderItem.communityFolderObjectId.toString();

    if (!acc.has(folderObjectId)) {
      acc.set(folderObjectId, []);
    }

    const existingFolderItems = acc.get(folderObjectId);

    existingFolderItems.push(folderItem);

    acc.set(folderObjectId, existingFolderItems);

    return acc;
  }, new Map());

  return {
    folders,
    folderItemsCache,
  };
}

async function copyFolderItems({
  newFolder,
  folderObjectId,
  folderItemsCache,
  communityObjectId,
  parentSectionId = null,
  session,
}) {
  const folderItems = folderItemsCache.get(folderObjectId.toString());

  if (!folderItems) {
    return;
  }

  const newFolderObjectId = newFolder._id;

  const oldSectionToNewSectionMapper = {};

  const folderItemsWithoutSection = folderItems.filter(
    (folderItem) => folderItem.type !== 'section'
  );

  if (!parentSectionId) {
    const sectionFolderItems = folderItems.filter(
      (folderItem) => folderItem.type === 'section'
    );

    for await (const sectionFolderItem of sectionFolderItems) {
      const { _id, createdAt, updatedAt, longUrl, shortUrl, ...rest } =
        sectionFolderItem;

      rest.communityObjectId = communityObjectId;
      rest.communityFolderObjectId = newFolderObjectId;

      const [newSectionFolderItem] = await FolderItemsModel.create(
        [rest],
        {
          session,
        }
      );

      oldSectionToNewSectionMapper[sectionFolderItem._id] =
        newSectionFolderItem._id;
    }
  }

  for await (const folderItem of folderItemsWithoutSection) {
    const { _id, createdAt, updatedAt, longUrl, shortUrl, ...rest } =
      folderItem;

    rest.communityObjectId = communityObjectId;
    rest.communityFolderObjectId = newFolderObjectId;
    rest.resourceSlug = await generateSlug(null, FolderItemsModel, {
      communityObjectId,
    });

    if (parentSectionId) {
      rest.parentSectionId = parentSectionId;
    } else if (
      oldSectionToNewSectionMapper[folderItem.parentSectionId] != null
    ) {
      rest.parentSectionId =
        oldSectionToNewSectionMapper[folderItem.parentSectionId];
    }

    await FolderItemsModel.create([rest], { session });
  }
}

// eslint-disable-next-line no-unused-vars
async function copyFolderAndItems({
  copyToCommunity,
  folders,
  folderItemsCache,
  session,
}) {
  const communityObjectId = copyToCommunity._id;

  for await (const folder of folders) {
    const {
      _id: folderObjectId,
      createdAt,
      updatedAt,
      shortUrl,
      longUrl,
      ...rest
    } = folder;

    rest.accessCount = 0;
    rest.communityObjectId = communityObjectId;
    rest.status = communityLibraryStatusMap.UNPUBLISHED;
    rest.discountsApplied = [];
    rest.earningAnalytics = {
      quantity: 0,
      revenueInLocalCurrency: 0,
      revenueInUsd: 0,
    };
    rest.resourceSlug = await generateSlug(null, FolderModel, {
      communityObjectId,
    });

    const newFolder = (
      await FolderModel.create([rest], { session })
    )[0].toObject();

    await copyFolderItems({
      newFolder,
      folderObjectId,
      folderItemsCache,
      communityObjectId,
      session,
    });
  }
}

async function copyEverythingToASingleFolder({
  copyToCommunity,
  folders,
  folderItemsCache,
  session,
  newFolderObjectId,
}) {
  const communityObjectId = copyToCommunity._id;
  const newFolder = await FolderModel.findById(newFolderObjectId).lean();

  if (!newFolder) {
    throw Error('newFolder not found');
  }

  let index = 0;
  for await (const folder of folders) {
    const {
      _id: folderObjectId,
      createdAt,
      updatedAt,
      shortUrl,
      longUrl,
      ...rest
    } = folder;

    const section = (
      await FolderItemsModel.create(
        [
          {
            title: rest.title,
            description: rest.description,
            type: 'section',
            status: communityLibraryStatusMap.UNPUBLISHED,
            communityObjectId,
            communityFolderObjectId: newFolder._id,
            isDemo: false,
            index,
          },
        ],
        { session }
      )
    )[0].toObject();
    await copyFolderItems({
      newFolder,
      folderObjectId,
      folderItemsCache,
      communityObjectId,
      parentSectionId: section._id,
      session,
    });
    index += 1;
  }
}

const start = async () => {
  await mongoClient.connect();

  const copyFromCommunityCodes = [];
  const copyToCommunityCodes = [];

  const newFolderObjectId = null;
  const extraFilterForSpecifiedFolders = {
    // _id: {
    //   $in: [],
    // },
  };

  const [copyFromCommunities, copyToCommunities] = await Promise.all([
    retrieveCommunities(copyFromCommunityCodes),
    retrieveCommunities(copyToCommunityCodes),
  ]);

  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  try {
    for await (const copyFromCommunity of copyFromCommunities) {
      const { folders, folderItemsCache } = await retrieveFoldersAndItems(
        copyFromCommunity._id,
        extraFilterForSpecifiedFolders
      );

      for await (const copyToCommunity of copyToCommunities) {
        // await copyEverythingToASingleFolder({
        //   copyToCommunity,
        //   folders,
        //   folderItemsCache,
        //   session,
        //   newFolderObjectId,
        // });
        await copyFolderAndItems({
          copyToCommunity,
          folders,
          folderItemsCache,
          session,
        });
      }
    }

    await session.commitTransaction();
  } catch (error) {
    console.log(error.message, error.stack);
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }

  console.log('Completed');
  process.exit(0);
};

start();
