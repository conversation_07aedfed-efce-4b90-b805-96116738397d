require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const mongoClient = require('../src/mongoClient');

const {
  generateSlugForCommunityResources,
} = require('../src/communitiesAPI/services/web/communityPosts.service');
const Users = require('../src/models/users.model');
const Posts = require('../src/communitiesAPI/models/communityPost.model');
const Community = require('../src/communitiesAPI/models/community.model');
const { DateTime } = require('luxon');

const THRESHOLD = 10000;

async function getCommunities() {
  const date = DateTime.utc(2024, 8, 8, 10, 0, 0, 0).toJSDate();
  const communities = await Community.aggregate([
    {
      $match: {
        createdAt: { $lte: date },
        isDraft: false,
        isActive: true,
      },
    },
    {
      $sort: { createdAt: -1 },
    },
    {
      $lookup: {
        from: 'community_posts',
        localField: '_id',
        foreignField: 'communities',
        as: 'posts',
      },
    },
    {
      $match: {
        posts: { $size: 0 },
      },
    },
    {
      $lookup: {
        from: 'community_role',
        let: { id: '$_id' },
        pipeline: [
          {
            $match: {
              $and: [
                { $expr: { $eq: ['$$id', '$communityObjectId'] } },
                { $expr: { $in: ['owner', '$role'] } },
              ],
            },
          },
        ],
        as: 'role',
      },
    },

    {
      $project: {
        _id: 1,
        createdBy: 1,
        userObjectId: { $first: '$role.userObjectId' },
        owner: { $first: '$role.email' },
        createdAt: 1,
        link: 1,
      },
    },
    {
      $limit: 20000,
    },
  ]);

  return communities;
}

async function createDefaultAnnouncementPost(
  defaultPost = {},
  community,
  user,
  pipeline
) {
  const {
    title,
    content,
    visibilityType,
    blurImageUrl,
    localizationDetails,
  } = defaultPost;

  const slug = await generateSlugForCommunityResources(
    community.link,
    community._id
  );
  const postData = {
    metadata: { isGeneratedByDefault: true },
    title,
    content,
    visibilityType,
    blurImageUrl,
    isAnnouncement: true,
    communities: [community._id],
    slug: `/${slug}`,
    author: user.userObjectId,
  };

  if (user.languagePreference !== 'en') {
    postData.content =
      localizationDetails.content[user.languagePreference] ?? content;
    postData.title =
      localizationDetails.subject[user.languagePreference] ?? title;
  }

  pipeline.push({
    insertOne: {
      document: postData,
    },
  });
}

const start = async () => {
  await mongoClient.connect();
  const defaultPost = await Posts.findOne({ isDefault: true }).lean();
  if (!defaultPost) {
    return;
  }

  const communities = await getCommunities();
  const createdByEmails = communities.map(
    (community) => community.createdBy
  );
  const createdByUsers = communities.map(
    (community) => community.userObjectId
  );

  const users = await Users.aggregate([
    {
      $match: {
        $and: [
          { _id: { $in: createdByUsers } },
          // { email: { $in: createdByEmails } },
          // { isActive: true },
          // { learner: { $ne: null } },
        ],
      },
    },
    {
      $lookup: {
        from: 'learners',
        localField: 'learner',
        foreignField: '_id',
        as: 'learnerDetails',
      },
    },
    {
      $project: {
        _id: 1,
        email: 1,
        userObjectId: '$_id',
        learnerObjectId: { $first: '$learnerDetails._id' },
        languagePreference: {
          $first: '$learnerDetails.languagePreference',
        },
      },
    },
  ]);

  const userMap = new Map();
  users.forEach((user) => {
    userMap.set(user.email, user);
  });

  const length = communities.length;
  const rounds = Math.ceil(length / THRESHOLD);
  const queries = [];
  for (let i = 0; i < rounds; i++) {
    const begin = i * THRESHOLD;
    let end = begin + THRESHOLD;
    if (end > length) end = length;
    queries.push({ begin, end });
  }

  await Promise.all(
    queries.map(async ({ begin, end }) => {
      const pipeline = [];
      await Promise.all(
        communities.slice(begin, end).map(async (community) => {
          const email = community.owner ?? community.createdBy;
          if (!userMap.has(email)) {
            return;
          }
          await createDefaultAnnouncementPost(
            defaultPost,
            community,
            userMap.get(email),
            pipeline
          );
        })
      );
      await Posts.bulkWrite(pipeline);
    })
  );

  console.log('Completed');
  process.exit(0);
};

start();
