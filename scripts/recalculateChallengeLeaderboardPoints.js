require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const fs = require('fs').promises;
const logger = require('../src/services/logger.service');
const mongoClient = require('../src/mongoClient');
const leaderboardService = require('../src/services/program/leaderboard.service');
const ProgramModel = require('../src/models/program/program.model');
const ProgramParticipantModel = require('../src/models/program/programParticipant.model');
const ParticipantProgramItemModel = require('../src/models/program/participantProgramItem.model');
const PointBreakdown = require('../src/services/program/model/PointBreakdown');
const { redisClient } = require('../src/redisClient');
const { getLock } = require('../src/redisLock');

const {
  PROGRAM_CHALLENGE_TYPE,
  PARTICIPANT_PROGRAM_ITEM_STATUS,
  PROGRAM_STATUS,
} = require('../src/services/program/constants');
const mongodbUtils = require('../src/utils/mongodb.util');

async function retrievePrograms({
  programObjectIds,
  endDate = null,
  afterProgramObjectId = '',
}) {
  const matchFilter = {
    challengeType: PROGRAM_CHALLENGE_TYPE.FIXED,
    status: PROGRAM_STATUS.PUBLISHED,
    startTime: { $exists: true },
    endTime: { $exists: true },
  };

  if (programObjectIds.length > 0) {
    matchFilter._id = {
      $in: programObjectIds,
    };
  } else if (afterProgramObjectId !== '') {
    matchFilter._id = {
      $gt: afterProgramObjectId,
    };
  }

  if (endDate) {
    matchFilter.endTime = {
      $gt: endDate,
    };
  }

  const programs = await ProgramModel.find(matchFilter)
    .sort({ _id: 1 })
    .lean();

  return programs;
}

async function recalculatePointsForParticipant(
  program,
  participant,
  update,
  session
) {
  const checkpointsSubmission = await ParticipantProgramItemModel.find({
    participantObjectId: participant._id,
    completionStatus: {
      $in: [
        PARTICIPANT_PROGRAM_ITEM_STATUS.COMPLETED,
        PARTICIPANT_PROGRAM_ITEM_STATUS.LATE_COMPLETED,
      ],
    },
  }).lean();

  if (checkpointsSubmission.length === 0) {
    return;
  }

  let totalPointBreakdown;

  let hasUpdated = false;

  for await (const checkpointSubmission of checkpointsSubmission) {
    try {
      const pointBreakdown = await leaderboardService.recalculatePoints({
        participant,
        program,
        currentCompletedProgramItem: checkpointSubmission,
      });

      if (totalPointBreakdown) {
        totalPointBreakdown =
          totalPointBreakdown.addPointBreakdown(pointBreakdown);
      } else {
        totalPointBreakdown = pointBreakdown;
      }

      const currentPointBreakdown = new PointBreakdown(
        checkpointSubmission
      );

      const pointBreakdownDifferences =
        pointBreakdown.calculatePointsDifference(currentPointBreakdown);

      const hasPointDifferences = Object.values(
        pointBreakdownDifferences
      ).some((diff) => diff !== 0);

      if (hasPointDifferences) {
        if (update) {
          await ParticipantProgramItemModel.updateOne(
            { _id: checkpointSubmission._id },
            { $set: pointBreakdown },
            { session }
          );
          hasUpdated = true;
        } else {
          console.log(
            `Submission(${
              checkpointSubmission._id
            }) points current: ${JSON.stringify(currentPointBreakdown)}`
          );

          console.log(
            `Submission(${
              checkpointSubmission._id
            }) points  after: ${JSON.stringify(pointBreakdown)}`
          );
        }
      }
    } catch (err) {
      logger.error(err.message);
    }
  }

  if (!totalPointBreakdown) {
    return;
  }

  const currentPointBreakdown = new PointBreakdown(participant);

  const pointBreakdownDifferences =
    totalPointBreakdown.calculatePointsDifference(currentPointBreakdown);

  const hasPointDifferences = Object.values(
    pointBreakdownDifferences
  ).some((diff) => diff !== 0);

  if (hasPointDifferences) {
    if (update) {
      await ProgramParticipantModel.updateOne(
        { _id: participant._id },
        {
          $set: totalPointBreakdown,
        },
        { session }
      );
      hasUpdated = true;
    } else {
      console.log(
        `Participant(${participant._id}) points current: ${JSON.stringify(
          currentPointBreakdown
        )}`
      );

      console.log(
        `Participant(${participant._id}) points  after: ${JSON.stringify(
          totalPointBreakdown
        )}`
      );
    }
  }

  return hasUpdated;
}

async function recalculatePoints({
  program,
  participantObjectId = '',
  afterParticipantObjectId = '',
  update = false,
  firstProgramObjectId,
}) {
  const matchFilter = {
    programObjectId: program._id,
  };

  if (participantObjectId !== '') {
    matchFilter._id = participantObjectId;
  } else if (afterParticipantObjectId !== '') {
    matchFilter._id = {
      $gt: afterParticipantObjectId,
    };
  }

  const participants = await ProgramParticipantModel.find(matchFilter)
    .sort({ _id: 1 })
    .lean();

  for await (const participant of participants) {
    const message = `Recalculate first program(${firstProgramObjectId}) program(${program._id}) participant(${participant._id}) points: ${update}`;

    console.log(message);

    if (update) {
      const lock = await getLock();
      const releaseLock = await lock(participant._id, 5000); // 5 sec

      try {
        const hasUpdated = await mongodbUtils.withTransactionRetry(
          async (session) =>
            recalculatePointsForParticipant(
              program,
              participant,
              update,
              session
            )
        );

        if (hasUpdated) {
          await fs.appendFile(
            'logs/recalculateChallengeLeaderboard.log',
            `${message}\n`
          );
        }
      } finally {
        await releaseLock();
      }
    } else {
      await recalculatePointsForParticipant(program, participant, update);
    }
  }
}

async function* splitByChunk(list, chunkSize = 1000) {
  const size = list.length;

  for (let i = 0; i < size; i += chunkSize) {
    yield list.slice(i, i + chunkSize);
  }
}

const start = async () => {
  await mongoClient.connect();
  await redisClient.connect();

  const programObjectIds = [];
  const afterProgramObjectId = '';
  const participantObjectId = '';
  const afterParticipantObjectId = '';
  const endDate = null;
  const update = false;

  const programs = await retrievePrograms({
    programObjectIds,
    endDate,
    afterProgramObjectId,
  });

  for await (const programsByChunk of splitByChunk(programs)) {
    const firstProgramObjectId = programsByChunk[0]._id;

    await Promise.all(
      programsByChunk.map(async (program) => {
        await recalculatePoints({
          program,
          participantObjectId,
          afterParticipantObjectId,
          update,
          firstProgramObjectId,
        });
      })
    );
  }

  logger.info('Completed');
  process.exit(0);
};

start();
