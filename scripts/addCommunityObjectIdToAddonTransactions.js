require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});
const ObjectId = require('mongoose').Types.ObjectId;

const mongoClient = require('../src/mongoClient');
const FolderModel = require('../src/communitiesAPI/models/communityFolders.model');
const EventModel = require('../src/communitiesAPI/models/communityEvents.model');
const ProgramModel = require('../src/models/program/program.model');
const AddonTransactionModel = require('../src/communitiesAPI/models/communityAddonTransactions.model');

const {
  COMMUNITY_ONE_TIME_PAYMENT_ENTITIES,
} = require('../src/communitiesAPI/constants');

const updateAddonTransactions = async ({ entityCollection, Model }) => {
  const entityObjectIds = await AddonTransactionModel.find({
    communityObjectId: { $exists: false },
    entityCollection,
  }).distinct('entityObjectId');

  let entities = [];
  if (entityObjectIds.length > 0) {
    entities = await Model.find({ _id: { $in: entityObjectIds } })
      .select('communities communityObjectId')
      .lean();
  }

  await Promise.all(
    entities.map(async (entity) => {
      let { communityObjectId } = entity;
      if (entityCollection === COMMUNITY_ONE_TIME_PAYMENT_ENTITIES.EVENT) {
        communityObjectId = entity.communities?.[0];
      }
      if (communityObjectId) {
        await AddonTransactionModel.updateMany(
          {
            communityObjectId: { $exists: false },
            entityCollection,
            entityObjectId: entity._id,
          },
          {
            communityObjectId: new ObjectId(communityObjectId),
          }
        );
      }
    })
  );
};

const run = async () => {
  await mongoClient.connect();
  await updateAddonTransactions({
    entityCollection: COMMUNITY_ONE_TIME_PAYMENT_ENTITIES.EVENT,
    Model: EventModel,
  });
  await updateAddonTransactions({
    entityCollection: COMMUNITY_ONE_TIME_PAYMENT_ENTITIES.FOLDER,
    Model: FolderModel,
  });
  await updateAddonTransactions({
    entityCollection: COMMUNITY_ONE_TIME_PAYMENT_ENTITIES.CHALLENGE,
    Model: ProgramModel,
  });

  console.log('Completed');
  process.exit(0);
};

run();
