require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const logger = require('../src/services/logger.service');
const mongoClient = require('../src/mongoClient');
const CommunityApplicationModel = require('../src/communitiesAPI/models/communityApplications.model');
const CommunityModel = require('../src/communitiesAPI/models/community.model');
const GenericTrackingModel = require('../src/models/platform/genericTracking.model');

const {
  convertAnswerToArray,
} = require('../src/services/communitySignup/signup/processTransaction/application.service');

const PrimaryMongooseConnection = require('../src/rpc/primaryMongooseConnection');

const THRESHOLD = 10000;

function retrieveApplicationSubmission(
  applicationData,
  applicationConfigDataFields = []
) {
  const applicationSubmission = [];

  if (applicationData && applicationConfigDataFields?.length > 0) {
    applicationConfigDataFields.forEach((dataField) => {
      const { fieldName } = dataField;
      const answer = applicationData?.[fieldName];
      if (answer === null || answer === undefined) {
        return;
      }
      const convertedAnswer = convertAnswerToArray(dataField, answer);
      applicationSubmission.push({
        questionKey: fieldName,
        label: dataField?.label,
        fieldDataType: dataField?.fieldDataType,
        isRequired: dataField?.isRequired,
        answer: convertedAnswer,
      });
    });
  }

  return applicationSubmission;
}

function getPipelineForCommunityApplication(community, applications) {
  const pipeline = applications.map((applicationData) => {
    const applicationSubmission = retrieveApplicationSubmission(
      applicationData,
      community.applicationConfigDataFields
    );
    return {
      updateOne: {
        filter: {
          _id: applicationData._id,
        },
        update: {
          $set: { applicationSubmission },
        },
      },
    };
  });

  return pipeline;
}

function getPipelineForGenericTracking(community, applications) {
  const pipeline = applications.map((application) => {
    const applicationSubmission = retrieveApplicationSubmission(
      application,
      community.applicationConfigDataFields
    );

    return {
      updateOne: {
        filter: {
          entity: 'community_application',
          collectionName: 'community_application',
          entityObjectId: application._id,
        },
        update: {
          $set: {
            event: 'submitted',
            trackingInfo: {
              learnerObjectId: application.learnerObjectId,
              communityCode: application.communityCode,
              applicationSubmission,
            },
          },
        },
        upsert: true,
      },
    };
  });

  return pipeline;
}

const getBatchingLimits = async (applicationQuery) => {
  const total = await CommunityApplicationModel.countDocuments(
    applicationQuery
  );
  const rounds = Math.ceil(total / THRESHOLD);
  const queries = [];
  for (let i = 0; i < rounds; i++) {
    const skip = i * THRESHOLD;
    queries.push({ skip, limit: THRESHOLD });
  }
  return queries;
};

const bulkUpdatePipelines = async (Model, pipelines) => {
  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  try {
    for await (const pipeline of pipelines) {
      await Model.bulkWrite(pipeline, { session });
    }
    await session.commitTransaction();
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }
};

// eslint-disable-next-line no-unused-vars
const updateCommunityApplications = async ({
  community,
  applicationQuery,
}) => {
  const queries = await getBatchingLimits(applicationQuery);
  const pipelines = await Promise.all(
    queries.map(async (item) => {
      const { skip, limit } = item;
      const applications = await CommunityApplicationModel.find(
        applicationQuery
      )
        .skip(skip)
        .limit(limit)
        .lean();
      const pipeline = getPipelineForCommunityApplication(
        community,
        applications
      );
      return pipeline;
    })
  );
  await bulkUpdatePipelines(CommunityApplicationModel, pipelines);
};

const updateGenericTracking = async ({ community, applicationQuery }) => {
  const queries = await getBatchingLimits(applicationQuery);
  const pipelines = await Promise.all(
    queries.map(async (item) => {
      const { skip, limit } = item;
      const applications = await CommunityApplicationModel.find(
        applicationQuery
      )
        .skip(skip)
        .limit(limit)
        .lean();
      const pipeline = getPipelineForGenericTracking(
        community,
        applications
      );
      return pipeline;
    })
  );
  await bulkUpdatePipelines(GenericTrackingModel, pipelines);
};

const start = async () => {
  await mongoClient.connect();
  //   const communityCode = 'NAS.IO_ACADEMY_CHALLENGES';
  const communityCode = 'DONT_STOP_BELIEVING';
  const community = await CommunityModel.findOne({
    code: communityCode,
  })
    .select('_id code applicationConfigDataFields')
    .lean();

  if (!community) {
    logger.error(`Community not found: ${communityCode}`);
    process.exit(1);
  }

  // const applicationQueryForCommunityApplicationUpdate = {
  //   communityCode,
  //   applicationSubmission: { $exists: false },
  // };
  // await updateCommunityApplications({
  //   community,
  //   applicationQuery: applicationQueryForCommunityApplicationUpdate,
  // });

  const applicationQueryForGenericTrakingUpdate = {
    communityCode,
  };
  await updateGenericTracking({
    community,
    applicationQuery: applicationQueryForGenericTrakingUpdate,
  });

  logger.info('Completed');
  process.exit(0);
};

start();
