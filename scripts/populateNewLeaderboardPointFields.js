require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const logger = require('../src/services/logger.service');
const mongoClient = require('../src/mongoClient');
const ProgramModel = require('../src/models/program/program.model');
const ProgramParticipantModel = require('../src/models/program/programParticipant.model');
const ParticipantProgramItemModel = require('../src/models/program/participantProgramItem.model');

const {
  LEADERBOARD_TYPE,
  LEADERBOARD_RANKING_FIELD,
  PROGRAM_CHALLENGE_TYPE,
} = require('../src/services/program/constants');

async function assignNewFields() {
  for await (const pointField of Object.values(
    LEADERBOARD_RANKING_FIELD
  )) {
    const point =
      pointField === 'totalPoints' ? '$completionResponseTimePoint' : 0;

    logger.info(`Add ${pointField} to participant program item model`);
    await ParticipantProgramItemModel.updateMany(
      {
        [pointField]: { $exists: false },
      },
      [{ $set: { [pointField]: point } }]
    );

    logger.info(`Add ${pointField} to participant model`);
    await ProgramParticipantModel.updateMany(
      {
        [pointField]: { $exists: false },
      },
      [{ $set: { [pointField]: point } }]
    );
  }
}

async function migrateToLeaderboardConfigs() {
  const programs = await ProgramModel.find(
    {
      challengeType: PROGRAM_CHALLENGE_TYPE.FIXED,
      'leaderboardConfig.type': { $exists: true },
      'leaderboardConfig.enabled': true,
      leaderboardConfigs: { $exists: false },
    },
    { leaderboardConfig: 1 }
  ).lean();

  const bulkOps = programs.map((program) => {
    const isLeaderboardEnabled =
      program.leaderboardConfig?.enabled ?? false;

    const query = {
      updateOne: {
        filter: { _id: program._id },
        update: {
          $set: {
            leaderboardConfigs: [
              {
                type: LEADERBOARD_TYPE.COMPLETION,
                enabled: isLeaderboardEnabled,
              },
              {
                type: LEADERBOARD_TYPE.FASTEST_CHECKPOINT_COMPLETION,
                enabled: isLeaderboardEnabled,
              },
              {
                type: LEADERBOARD_TYPE.THREE_SEQUENTIAL_CONSECUTIVE_ON_TIME_COMPLETION,
                enabled: false,
              },
              {
                type: LEADERBOARD_TYPE.CELEBRATION_ON_FEED,
                enabled: false,
              },
              { type: LEADERBOARD_TYPE.COMMENT_ON_FEED, enabled: false },
            ],
          },
        },
      },
    };

    return query;
  });

  if (bulkOps.length > 0) {
    await ProgramModel.bulkWrite(bulkOps);
  }
}

const start = async () => {
  await mongoClient.connect();

  await assignNewFields();
  await migrateToLeaderboardConfigs();

  logger.info('Completed');
  process.exit(0);
};

start();
