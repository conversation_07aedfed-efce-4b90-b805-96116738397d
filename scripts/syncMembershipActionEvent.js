require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});
const fs = require('fs/promises');

const logger = require('../src/services/logger.service');
const mongoClient = require('../src/mongoClient');
const ActivityModel = require('../src/models/activities.model');
const CommunityModel = require('../src/communitiesAPI/models/community.model');
const SubscriptionModel = require('../src/communitiesAPI/models/communitySubscriptions.model');
const {
  LEGENDS_COMMUNITY_CODE,
  INDIAUPI_COMMUNITY_CODE,
  LATAM_COMMUNITY_CODE,
} = require('../src/communitiesAPI/constants');
const {
  communityEnrolmentStatuses,
  MEMBERSHIP_ACTION_EVENT_TYPES,
} = require('../src/constants/common');
const actionEventService = require('../src/services/actionEvent');

async function retrieveCommunityCache(communityCodes) {
  const communities = await CommunityModel.find(
    {
      code: {
        $in: communityCodes,
      },
    },
    { _id: 1, code: 1 }
  ).lean();

  const communityCache = communities.reduce((acc, community) => {
    acc.set(community.code, community);
    return acc;
  }, new Map());

  return communityCache;
}

async function retrieveSubscriptions(communityCodes, skip, limit) {
  const subscriptions = await SubscriptionModel.find(
    {
      createdAt: {
        $gte: new Date('2024-11-01'),
      },
      communityCode: {
        $in: communityCodes,
      },
      status: communityEnrolmentStatuses.CANCELLED,
      stripeSubscriptionId: { $exists: false },
    },
    {
      email: 1,
      status: 1,
      communityCode: 1,
      createdAt: 1,
      learnerObjectId: 1,
    }
  )
    .sort({ communityCode: 1, createdAt: 1 })
    .skip((skip - 1) * limit)
    .limit(limit)
    .lean();

  return subscriptions;
}

async function sendMembershipActionEvent(subscription, community) {
  await actionEventService.sendFreeMembershipActionEvent({
    actionEventType: MEMBERSHIP_ACTION_EVENT_TYPES.SUBSCRIPTION_SUCCESS,
    actionEventCreatedAt: subscription.createdAt,
    subscription,
    community,
  });
}

async function retrieveActivity(subscription, community) {
  const { _id: communityObjectId } = community;

  const activity = await ActivityModel.findOne({
    communityObjectId,
    appliedToLearnerObjectId: subscription.learnerObjectId,
    activityCreatedAt: subscription.createdAt,
  }).lean();

  return activity;
}

const start = async () => {
  await mongoClient.connect();

  const communityCodes = [
    LEGENDS_COMMUNITY_CODE,
    INDIAUPI_COMMUNITY_CODE,
    LATAM_COMMUNITY_CODE,
  ];

  const limit = 1000;

  const timestampInSeconds = Math.floor(Date.now() / 1000);

  const communityCache = await retrieveCommunityCache(communityCodes);

  const next = async (skip = 1) => {
    const subscriptions = await retrieveSubscriptions(
      communityCodes,
      skip,
      limit
    );

    logger.info(`${skip * limit}`);

    if (subscriptions.length === 0) {
      return;
    }

    await Promise.all(
      subscriptions.map(async (subscription) => {
        const community = communityCache.get(subscription.communityCode);

        const activity = await retrieveActivity(subscription, community);

        logger.info(
          `${subscription.communityCode},${subscription.email},${
            subscription.learnerObjectId
          },${!!activity}`
        );

        if (!activity) {
          const message = `${subscription.communityCode},${
            subscription.email
          },${subscription.learnerObjectId},${
            subscription.status
          },${subscription.createdAt.toISOString()}`;

          logger.info(message);
          await sendMembershipActionEvent(subscription, community);
          await fs.appendFile(
            `logs/sync_membership_action_event_${timestampInSeconds}.log`,
            `${message}\n`
          );
        }
      })
    );

    await next(skip + 1);
  };

  await next();

  logger.info('Completed');
  process.exit(0);
};

start();
