require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const ObjectId = require('mongoose').Types.ObjectId;

const logger = require('../src/services/logger.service');

const mongoClient = require('../src/mongoClient');

const WalletFundmovements = require('../src/models/wallet/walletFundMovements.model');

const PrimaryMongooseConnection = require('../src/rpc/primaryMongooseConnection');
const { SUB_WALLET_TYPE } = require('../src/constants/common');

const fixApplicationId = async (primaryMongooseConnection) => {
  const result = await WalletFundmovements.aggregate([
    {
      $match:
        /**
         * query: The query in MQL.
         */
        {
          amountBreakdown: {
            $exists: false,
          },
        },
    },
    {
      $group:
        /**
         * _id: The id of the group.
         * fieldN: The first field name.
         */
        {
          _id: {
            transactionObjectId: '$transactionObjectId',
            walletObjectId: '$walletObjectId',
          },
          funds: {
            $push: {
              _id: '$_id',
              walletType: '$walletType',
              currency: '$currency',
              transactionType: '$transactionType',
              amount: '$amount',
              subWalletType: '$subWalletType',
              approximateUSDAmount: '$approximateUSDAmount',
              createdAt: '$createdAt',
            },
          },
        },
    },
    {
      $limit:
        /**
         * Provide the number of documents to limit.
         */
        500,
    },
  ]);

  if (result.length === 0) {
    return;
  }

  await Promise.all(
    result.map(async (fundMovements) => {
      const idToDelete = [];
      const transformedData = {
        transactionObjectId: fundMovements._id.transactionObjectId,
        walletObjectId: fundMovements._id.walletObjectId,
        transactionType: fundMovements.funds[0].transactionType,
        walletType: fundMovements.funds[0].walletType,
        amountBreakdown: {
          [`${SUB_WALLET_TYPE.AVAILABLE}`]: 0,
          [`${SUB_WALLET_TYPE.POOL}`]: 0,
          [`${SUB_WALLET_TYPE.GATEWAY_FEE}`]: 0,
          [`${SUB_WALLET_TYPE.NASIO_FEE}`]: 0,
          [`${SUB_WALLET_TYPE.GST_ON_GATEWAY_FEE}`]: 0,
          [`${SUB_WALLET_TYPE.WHT}`]: 0,
          [`${SUB_WALLET_TYPE.GST_ON_NASIO_FEE}`]: 0,
          [`${SUB_WALLET_TYPE.REFUND_PROCESSING_FEE}`]: 0,
        },
        currency: fundMovements.funds[0].currency,
        approximateUSDAmountBreakdown: {
          [`${SUB_WALLET_TYPE.AVAILABLE}`]: 0,
          [`${SUB_WALLET_TYPE.POOL}`]: 0,
          [`${SUB_WALLET_TYPE.GATEWAY_FEE}`]: 0,
          [`${SUB_WALLET_TYPE.NASIO_FEE}`]: 0,
          [`${SUB_WALLET_TYPE.GST_ON_GATEWAY_FEE}`]: 0,
          [`${SUB_WALLET_TYPE.WHT}`]: 0,
          [`${SUB_WALLET_TYPE.GST_ON_NASIO_FEE}`]: 0,
          [`${SUB_WALLET_TYPE.REFUND_PROCESSING_FEE}`]: 0,
        },
        createdAt: fundMovements.funds[0].createdAt,
        updatedAt: fundMovements.funds[0].createdAt,
      };

      fundMovements.funds.forEach((fund) => {
        idToDelete.push(fund._id);
        transformedData.amountBreakdown[fund.subWalletType] = fund.amount;
        transformedData.approximateUSDAmountBreakdown[fund.subWalletType] =
          fund.approximateUSDAmount;
      });

      logger.info(`${JSON.stringify(transformedData)}`);
      logger.info(`${JSON.stringify({ idToDelete })}`);

      const session = await primaryMongooseConnection.startSession();
      session.startTransaction();
      try {
        await WalletFundmovements.create(transformedData);
        await WalletFundmovements.deleteMany({ _id: { $in: idToDelete } });
      } catch (error) {
        logger.error(
          `Failed to update: trxid=${JSON.stringify(
            fundMovements._id
          )}, ${error}`
        );
        await session.abortTransaction();
      } finally {
        await session.endSession();
      }
    })
  );

  await fixApplicationId(primaryMongooseConnection);
};

const start = async () => {
  await mongoClient.connect();
  const currentTimestampInMs = Date.now();

  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  await fixApplicationId(primaryMongooseConnection);

  logger.info('Completed');
  process.exit(0);
};

start();
