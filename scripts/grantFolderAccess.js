require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const fs = require('fs').promises;
const ObjectId = require('mongoose').Types.ObjectId;
const { DateTime } = require('luxon');

const PrimaryMongooseConnection = require('../src/rpc/primaryMongooseConnection');
const mongoClient = require('../src/mongoClient');
const FolderViewers = require('../src/models/product/folderViewers.model');
const Folders = require('../src/communitiesAPI/models/communityFolders.model');
const FolderPurchases = require('../src/communitiesAPI/models/communityFolderPurchases.model');
const Learners = require('../src/models/learners.model');
const Community = require('../src/communitiesAPI/models/community.model');

const {
  FOLDER_VIEWER_STATUS,
} = require('../src/services/folder/constants');

// const COMMUNITY_OBJECT_ID = '664272cdc7cc4f9f33029f18';
// const RESOURCE_SLUG = '/ecsc';

const COMMUNITY_OBJECT_ID = '637e0d21a27036737852ae08';
const RESOURCE_SLUG = '/yoco';

const NOTE = 'Imported from CSV via grantFolderAccess Script';
const THRESHOLD = 9000;

async function filterLearners(now, learners, folder) {
  const learnerObjectIds = learners.map((learner) => learner._id);
  const purchases = await FolderPurchases.find({
    folderObjectId: folder._id,
    learnerObjectId: { $in: learnerObjectIds },
  }).lean();

  const learnerObjectIdsWithPurchaseStr = [];
  const learnerObjectIdsWithPurchase = purchases.map((learner) => {
    learnerObjectIdsWithPurchaseStr.push(
      learner.learnerObjectId.toString()
    );
    return learner.learnerObjectId;
  });

  const data = await FolderPurchases.updateMany(
    {
      folderObjectId: folder._id,
      learnerObjectId: { $in: learnerObjectIdsWithPurchase },
      status: FOLDER_VIEWER_STATUS.FREE,
    },
    {
      status: FOLDER_VIEWER_STATUS.PAID,
      purchaseDate: now,
    }
  );

  return learners.filter(
    (learner) =>
      !learnerObjectIdsWithPurchaseStr.includes(learner._id.toString())
  );
}

async function setupDigitalProductAccess(
  now,
  learners,
  folder,
  baseCurrency
) {
  for await (const learner of learners) {
    await Promise.all([
      FolderViewers.findOneAndUpdate(
        { folderObjectId: folder._id, learnerObjectId: learner._id },
        {
          status: FOLDER_VIEWER_STATUS.PAID,
          purchaseDate: now,
          note: NOTE,
          scriptExecutedDate: now,
        },
        { upsert: true }
      ),
      FolderPurchases.findOneAndUpdate(
        { folderObjectId: folder._id, learnerObjectId: learner._id },
        {
          amount: 0,
          currency: baseCurrency,
          folderCheckoutId: new ObjectId(),
          local_amount: 0,
          local_currency: baseCurrency,
          purchaseType: 'free',
          note: NOTE,
          scriptExecutedDate: now,
        },
        { upsert: true }
      ),
      Folders.findOneAndUpdate(
        { _id: folder._id },
        { $inc: { accessCount: 1 } }
      ),
    ]);
  }

  // const folderViewerPipeline = [];
  // const folderPurchasePipeline = [];

  // learners.forEach((learner) => {
  //   folderViewerPipeline.push({
  //     updateOne: {
  //       filter: {
  //         folderObjectId: folder._id,
  //         learnerObjectId: learner._id,
  //       },
  //       update: {
  //         status: FOLDER_VIEWER_STATUS.PAID,
  //         purchaseDate: now,
  //         note: NOTE,
  //         scriptExecutedDate: now,
  //       },
  //       upsert: true,
  //     },
  //   });
  //   folderPurchasePipeline.push({
  //     updateOne: {
  //       filter: {
  //         folderObjectId: folder._id,
  //         learnerObjectId: learner._id,
  //       },
  //       update: {
  //         amount: 0,
  //         currency: baseCurrency,
  //         folderCheckoutId: new ObjectId(),
  //         local_amount: 0,
  //         local_currency: baseCurrency,
  //         purchaseType: 'free',
  //         note: NOTE,
  //         scriptExecutedDate: now,
  //       },
  //       upsert: true,
  //     },
  //   });
  // });

  // const length = folderViewerPipeline.length;
  // try {
  //   if (length > 0) {
  //     const [folderViewerResult, folderPurchaseResult, folderResult] =
  //       await Promise.all([
  //         FolderViewers.bulkWrite(folderViewerPipeline, { session }),
  //         FolderPurchases.bulkWrite(folderPurchasePipeline, { session }),
  //         Folders.findOneAndUpdate(
  //           { _id: folder._id },
  //           { $inc: { accessCount: length } },
  //           { session }
  //         ),
  //       ]);
  //   }
  //   await session.commitTransaction();
  // } catch (error) {
  //   console.log(`Failed to update, ${error}`);
  //   await session.abortTransaction();
  // } finally {
  //   await session.endSession();
  // }
}

async function processFile(filepath) {
  await mongoClient.connect();

  const [folder, community] = await Promise.all([
    Folders.findOne({
      communityObjectId: new ObjectId(COMMUNITY_OBJECT_ID),
      resourceSlug: RESOURCE_SLUG,
    }).lean(),
    Community.findOne({
      _id: new ObjectId(COMMUNITY_OBJECT_ID),
    }).lean(),
  ]);

  if (!folder) {
    throw new Error(`Folder does not exists`);
  }

  const file = await fs.readFile(filepath, 'utf-8');
  const emailArray = [];
  await Promise.all(
    file.split('\n').map(async (row, index) => {
      if (!row || index === 0) {
        return;
      }
      const [name, email] = row.split(',');
      return emailArray.push(email.trim());
    })
  );

  const length = emailArray.length;
  const rounds = Math.ceil(length / THRESHOLD);
  const queries = [];
  for (let i = 0; i < rounds; i++) {
    const start = i * THRESHOLD;
    let end = start + THRESHOLD;
    if (end > length) end = length;
    queries.push({ start, end });
  }

  const now = DateTime.now().toUTC();

  await Promise.all(
    queries.map(async ({ start, end }) => {
      const learners = await Learners.find({
        email: { $in: emailArray.slice(start, end) },
      })
        .select('_id ')
        .lean();

      const filteredLearners = await filterLearners(now, learners, folder);

      await setupDigitalProductAccess(
        now,
        filteredLearners,
        folder,
        community.baseCurrency
      );
    })
  );
  console.log('Completed');
  process.exit(0);
}

const filename = process.argv[2];
processFile(filename);
