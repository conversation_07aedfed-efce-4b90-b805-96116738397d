require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const logger = require('../src/services/logger.service');
const mongoClient = require('../src/mongoClient');
const AddAdjustmentServices = require('../src/services/payout/addAdjustment.service');

const addAdjustmentTrx = async () => {
  const map = [
    {
      communityCode: 'YAHAN_MEXICO_COMMUNITY',
      currency: 'MXN',
      adjustAmount: 161066,
    },
  ];

  for (const communityAdjust of map) {
    logger.info(
      `${communityAdjust.communityCode}: ${communityAdjust.adjustAmount}`
    );

    await AddAdjustmentServices.addAdjustment(
      communityAdjust.communityCode,
      communityAdjust.adjustAmount,
      communityAdjust.currency,
      `2024-08-15-CHARGEBACK-ADJUSTMENT-${communityAdjust.communityCode}`,
      'Chargeback return',
      '<EMAIL>'
    );
  }
};
const start = async () => {
  await mongoClient.connect();

  await addAdjustmentTrx();

  logger.info('Completed');
  process.exit(0);
};

start();
