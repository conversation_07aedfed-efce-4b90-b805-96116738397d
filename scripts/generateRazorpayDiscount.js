require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});
const fs = require('fs').promises;
const axios = require('axios');

const mongoClient = require('../src/mongoClient');
const DiscountPoolsModel = require('../src/models/discount/discountPools.model');

async function addDiscountToRazorpay(value, isUpi, isInterval) {
  const subscription = isInterval
    ? {
        redemption_type: 'cycle',
        applicable_on: 'both',
        no_of_cycles: '1',
      }
    : {
        redemption_type: 'forever',
        applicable_on: 'both',
      };

  const maxCashback = isInterval ? 999999800 : 999999900;

  const offer = {
    product_type: 'subscription',
    type: 'instant',
    name: `${value}%_${isUpi ? 'UPI' : 'CARD'}_${
      isInterval ? 'INTERVAL' : 'FOREVER'
    }`,
    display_text: `${value}% off on ${isUpi ? 'UPI' : 'card'}`,
    terms: 'None',
    percent_rate: Math.round(value * 100),
    max_cashback: maxCashback,
    payment_method: isUpi ? 'upi' : 'card',
    ends_at: 2027433599,
    block: 1,
    subscription,
  };

  console.log(`${value},${isUpi},${isInterval}`);

  const data = JSON.stringify(offer);

  const config = {
    method: 'post',
    url: 'https://dashboard.razorpay.com/merchant/api/test/offers',
    headers: {
      authority: 'dashboard.razorpay.com',
      baggage:
        'sentry-environment=production,sentry-release=81878db27c1f93ab5f14b2a414c47d609e2420b2,sentry-public_key=61a59743c0e94597a07ca7da50ae8f77,sentry-trace_id=c50e079fe8d94228bfb439f55668a978',
      'sentry-trace':
        'c50e079fe8d94228bfb439f55668a978-b69d25a68fcdd431-0',
      'x-requested-with': 'XMLHttpRequest',
      'x-xsrf-token':
        'eyJpdiI6ImRWazVDVkcxak1uRi9aUTBMTTRJQXc9PSIsInZhbHVlIjoiVTRoY1dIQUZQdzFFbzdMdDBHWHovRlhiTGpUSUN1YjE1ajd1bHI3QXVKTzdRRjgxVFBnYmIybHpaYlI3SnlwWUp1ckNvWFA4Qmc2UFFJZkNHRzFvRnREZFhDdVRKNDYrTkRiMTRpN1Q2MnA5cXJyWDZTcmxjOWF0TERabWtud2YiLCJtYWMiOiJhZTg3MzZlODAwYWU0YjcwZmQ3MWZlYzUwNmQ1MmRhN2RkY2Q2MWJmZDMzYzc1YjU4YzA4Y2E4OWYwYzg2ZWEyIiwidGFnIjoiIn0=',
      Cookie:
        'ab_user_id=a430db70-cc70-4d7d-99d5-2310ab45fc95; additional-cache-params={"isMobile":false,"isSafari":false,"countryCode":"IN","isTransparentVideoNotSupported":false,"isBot":false,"host":"razorpay.com","previewAsset":""}; visit_time_stamp=1704961433531; firstAttribUtm={"source":"google","medium":"organic"}; clientId=b73d00df-05cb-4b89-85fb-7ef040f24e05; _fbp=fb.1.1704961450665.1492627218; _gcl_au=1.1.668002325.1705383718; __adroll_fpc=d1fed7fff1cfc7ac323b97d244e8253d-1705383718391; _hjSessionUser_575141=eyJpZCI6IjU3ZGZkY2MxLWNmNWYtNTA0MC04YzZjLWZlMmM5MTg5ODU4OCIsImNyZWF0ZWQiOjE3MDQ5NjE0NDg1NDYsImV4aXN0aW5nIjp0cnVlfQ; hubspotutk=4a4d7bf35197eda7d8769344846ddaab; g_state={"i_l":0}; ajs_user_id=LdECHBGq2ZtaP4; WZRK_G=b2749149d272418d89bb4d634bdfacce; gclid=Cj0KCQiAw6yuBhDrARIsACf94RVSaIDBw3AD53qYb2lFJ8JA94jopu1sb_7vnzX4rbUaJRG8nAvV3OoaAg7XEALw_wcB; first_gclid=Cj0KCQiAw6yuBhDrARIsACf94RVSaIDBw3AD53qYb2lFJ8JA94jopu1sb_7vnzX4rbUaJRG8nAvV3OoaAg7XEALw_wcB; final_gclid=Cj0KCQiAw6yuBhDrARIsACf94RVSaIDBw3AD53qYb2lFJ8JA94jopu1sb_7vnzX4rbUaJRG8nAvV3OoaAg7XEALw_wcB; utm_params_analytics={%22utm_campaign%22:%22RPSME-RPPerf-GSearchBrand-Prospect-DWeb-Core-Product%22%2C%22utm_content%22:%22RPSME-Brand-010223%22%2C%22utm_keyword%22:%22%22%2C%22utm_medium%22:%22cpc%22%2C%22utm_source%22:%22google%22}; _gcl_aw=GCL.1707895439.Cj0KCQiAw6yuBhDrARIsACf94RVSaIDBw3AD53qYb2lFJ8JA94jopu1sb_7vnzX4rbUaJRG8nAvV3OoaAg7XEALw_wcB; lastAttribUtm={"utm_source":"google","utm_medium":"organic"}; __hssrc=1; midExists=true; rzp_mid=LTgJD9qW9Vs0MV; rzp_userid=LdECHBGq2ZtaP4; cto_bundle=XryxoV9JcXE1ZFYlMkJrVFpnQmwwSmpDcjVXYUtQdWxYY3BBalolMkJIS29BTHRpS2dHTmVUUWFmODRYejdIT1dWUUlmdlhYUVRGaERMJTJCUjJGTUxIdmlaWDNYdnhDR1F5ZUk3RFR4dzB4OHhtdEhoeUdQdnElMkZOQnhqbjNXRSUyQkJKZ2JPQkM3YiUyRmFFSTB6U25MYUxCNWxZSWlzV3Q3elhweTdleG42N1FaR0R5RmhxMW02NTRCSklVdWpuSEtxa2ViNzROaTNKejFqUGMya3l3cndQeGVEQjFaSEFFRVl3a2hNREVMNzQxQVJZZjBKWk5UUjl3M2hKSzM3MWxyVG1ncjJqZVJCYVJm; rzp_utm={"attributions":[{"utm_source":"google","utm_campaign":"","utm_medium":"organic","utm_term":"","utm_content":"","utm_adgroup":"","timestamp":"2024-01-11T13:54:07+05:30"},{"utm_source":"google","utm_campaign":null,"utm_medium":"organic","utm_term":null,"utm_content":null,"utm_adgroup":null,"timestamp":null}],"first_page":"dashboard.razorpay.com/","new_user":false,"website":"razorpay.com/pricing/","final_page":"razorpay.com/pricing/","fc_source":"direct","lc_source":"google"}; lastActivityTimeStamp=1708093524140; sessionId=1708093524140; commonSessionId=166f0cc0-6e2e-4e30-8a59-638681aa9a86; __ar_v4=%7CTJ37WOXRMNBN3E7GBHOOXB%3A20240217%3A1%7CKCGQOUBQ5VFKRM3XB5PB2U%3A20240217%3A1; _ga_F42RZ69DW8=GS1.1.1708093524.4.0.1708093533.51.0.0; _ga_1JR8GXL68Z=GS1.1.1708093524.2.0.1708093533.51.0.0; ajs_anonymous_id=1ee077f0-69a0-4bea-9027-1a5c52f939c2; session_token=5A10BD15C8400FEA8FFD68AB4E7E6E144D04A59B194512FFA6A9CC6390DB4F37C34768CA3E9062E19968135AD5D7FBB2C80E142062CD9A0EDD9963285391E1B7050425BD75E2563C65C4B1782572A2127AFB4683EFCF4C8BCA8095515E7F5B85AFD143A1F1AFD1F91B96BF60B37E250E0D7EA0879F04892D0F72A3905FF15159EFA77EA1F9AD93662DAEB039EDFC497F5D6CF9; _gid=GA1.2.1066107211.1710169521; _ga_DD6NQ1ZNV7=GS1.2.1710169523.21.0.1710169523.60.0.0; _ga_6BYES03TGC=GS1.2.1710169523.17.0.1710169523.60.0.0; rzp_usr_session=WlgZSFZIwXwMsXYzL6bQ7o4NqYim020OZY04iSyV; _ga_EFWRVJFRKV=GS1.1.1710170948.1.0.1710170949.0.0.0; __hstc=227020674.4a4d7bf35197eda7d8769344846ddaab.1705462547323.1710169523362.1710212940067.36; _clck=porrcy%7C2%7Cfk0%7C0%7C1520; campaignStartTime=Tue%2C%2012%20Mar%202024%2003%3A32%3A35%20GMT; _rdt_uuid=1704961447103.10d60063-5151-45fd-b1b5-961ff87be79b; _uetsid=c8f5fa40dfb811eeaa68c9f2eb1b9d91; _uetvid=56a12460d9de11ed8f90a350e19f534d; _ga=GA1.1.b73d00df-05cb-4b89-85fb-7ef040f24e05; _ga_8HTFJ5WZ20=GS1.1.1710212939.34.1.1710216854.0.0.0; _clsk=ki2zni%7C1710216855119%7C73%7C1%7Cp.clarity.ms%2Fcollect; _hjSession_575141=eyJpZCI6IjhmZTgwYjY3LWVlNjItNGQ3ZC05ZTg1LWY4Y2Q2MDZiNWE1NiIsImMiOjE3MTAyMTkzMTI1NzUsInMiOjAsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjowLCJzcCI6MX0; XSRF-TOKEN=eyJpdiI6ImRWazVDVkcxak1uRi9aUTBMTTRJQXc9PSIsInZhbHVlIjoiVTRoY1dIQUZQdzFFbzdMdDBHWHovRlhiTGpUSUN1YjE1ajd1bHI3QXVKTzdRRjgxVFBnYmIybHpaYlI3SnlwWUp1ckNvWFA4Qmc2UFFJZkNHRzFvRnREZFhDdVRKNDYrTkRiMTRpN1Q2MnA5cXJyWDZTcmxjOWF0TERabWtud2YiLCJtYWMiOiJhZTg3MzZlODAwYWU0YjcwZmQ3MWZlYzUwNmQ1MmRhN2RkY2Q2MWJmZDMzYzc1YjU4YzA4Y2E4OWYwYzg2ZWEyIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImpCclRCL0d4NlpwbWdscG1lOW9jMmc9PSIsInZhbHVlIjoidHlFMXN3ZEtsQkk4cHBJM1hSWENMR1BqQlZ3L0lwSjF2MUh2QUpUM1g4cEQ0ZjR1dFhVYzRaMGNvOUtkVGxGb1g2emFyUEJONVlpSXBYaFFhaEFHZUZOZlNIS29Id0xDb3lDMXBJekRPYVkycjVLVjFyNmp6bXZFN1pDS21HRnIiLCJtYWMiOiJiYjZkM2I1MjYzNGI5ZmJkMDFiOTg2MzcwMDVlNWU3NTkzMGI2M2NmMDVkMzI2OGRmZGM2NWFjZDBkZWIyNGJjIiwidGFnIjoiIn0%3D; rzp_usr_session=WlgZSFZIwXwMsXYzL6bQ7o4NqYim020OZY04iSyV',
      'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
      'content-type': 'application/json',
      Accept: '*/*',
      Host: 'dashboard.razorpay.com',
      Connection: 'keep-alive',
    },
    data,
  };

  try {
    const result = await axios(config).then((res) => res.data);

    if (result.success) {
      const offerId = `offer_${result.data[0].id}`;
      await fs.appendFile(
        'logs/discount_creation_success',
        `${value},${isUpi},${isInterval},${offerId}\n`
      );
    } else {
      throw new Error(`${result.errors[0]}`);
    }
  } catch (err) {
    if (
      err.message ===
      'Offer already exists. Please check the values and try again'
    ) {
      return;
    }

    if (
      err.message === 'Offers On Subscription not enabled for the merchant'
    ) {
      await addDiscountToRazorpay(value, isUpi, isInterval);
      return;
    }

    await fs.appendFile(
      'logs/discount_creation_failure',
      `${value},${isUpi},${isInterval},${err.message}\n`
    );
    throw err;
  }
}

function generateDiscountValues(
  minValue = 0.01,
  maxValue = 99.99,
  step = 0.01
) {
  const values = [];
  for (let value = minValue; value <= maxValue; value += step) {
    values.push(value.toFixed(2));
  }
  return values;
}

async function createDiscountToDiscountPool(
  discountValue,
  isUpi,
  isInterval,
  offerId
) {
  const discount = {
    paymentProviderDiscountId: offerId,
    paymentProvider: 'razorpay',
    discountValue: parseFloat(discountValue),
    discountValueType: 'percentage',
    discountType: isInterval === 'true' ? 'INTERVAL' : 'FOREVER',
    isActive: true,
    paymentMethods: isUpi === 'true' ? ['UPI'] : ['CARD'],
  };

  if (isInterval === 'true') {
    discount.metadata = {
      intervalCount: 1,
    };
  }

  await DiscountPoolsModel.create(discount);
}

async function populateDiscountPool() {
  const file = await fs.readFile('logs/discount_creation_success', 'utf8');
  await Promise.all(
    file.split('\n').map(async (row) => {
      if (!row) {
        return;
      }

      const [discountValue, isUpi, isInterval, offerId] = row.split(',');
      console.log(discountValue, isUpi, isInterval, offerId);

      return createDiscountToDiscountPool(
        discountValue,
        isUpi,
        isInterval,
        offerId
      );
    })
  );
}

const start = async () => {
  await mongoClient.connect();

  const generateDiscount = false;

  if (generateDiscount) {
    const discountValueArray = generateDiscountValues();

    for await (const discountValue of discountValueArray) {
      await addDiscountToRazorpay(discountValue, true, false);
      await addDiscountToRazorpay(discountValue, false, false);
      await addDiscountToRazorpay(discountValue, true, true);
      await addDiscountToRazorpay(discountValue, false, true);
    }
  } else {
    await populateDiscountPool();
  }

  console.log('Completed');
  process.exit(0);
};

start();
