require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const logger = require('../src/services/logger.service');
const mongoClient = require('../src/mongoClient');

const fs = require('fs').promises;

const {
  subscriptionPlanService,
} = require('../src/services/communitySubscription');
const {
  PAYMENT_PROVIDER,
  communityEnrolmentStatuses,
} = require('../src/constants/common');

const SubscriptionModel = require('../src/communitiesAPI/models/communitySubscriptions.model');

async function retrieveSubscriptions({
  emails,
  communityCode,
  allowedPaymentProviders,
}) {
  const subscriptions = await SubscriptionModel.find(
    {
      email: { $in: emails },
      status: communityEnrolmentStatuses.CURRENT,
      communityCode,
      stripeSubscriptionId: { $ne: null },
      paymentProvider: { $in: allowedPaymentProviders },
    },
    { _id: 1, learnerObjectId: 1, email: 1 }
  )
    .sort({ _id: 1 })
    .lean();

  return subscriptions;
}

const start = async () => {
  await mongoClient.connect();

  const communityCode = '';

  const emails = [];

  const priceId = '';
  const discountCode = null;

  const allowedPaymentProviders = [
    PAYMENT_PROVIDER.STRIPE,
    PAYMENT_PROVIDER.STRIPE_INDIA,
    PAYMENT_PROVIDER.EBANX,
  ];

  const subscriptions = await retrieveSubscriptions({
    emails,
    communityCode,
    allowedPaymentProviders,
  });

  for await (const subscription of subscriptions) {
    const {
      _id: subscriptionObjectId,
      learnerObjectId,
      email,
    } = subscription;

    const message = `${new Date().toISOString()},${email},${subscriptionObjectId},${communityCode},${priceId},${
      discountCode ?? ''
    }`;

    console.log(message);

    await subscriptionPlanService.changePlan({
      subscriptionObjectId,
      learnerObjectId,
      priceId,
      discountCode,
      allowSameInterval: true,
    });

    await fs.appendFile(
      'logs/updatePaymentProviderPlan.log',
      `${message}\n`
    );
  }

  logger.info('Completed');
  process.exit(0);
};

start();
