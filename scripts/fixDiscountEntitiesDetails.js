require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const fs = require('fs').promises;
const ObjectId = require('mongoose').Types.ObjectId;

const logger = require('../src/services/logger.service');
const MongodbUtils = require('../src/utils/mongodb.util');

const mongoClient = require('../src/mongoClient');
const CommunityDiscount = require('../src/communitiesAPI/models/communityDiscounts.model');
const CommunityEvent = require('../src/communitiesAPI/models/communityEvents.model');
const CommunityFolder = require('../src/communitiesAPI/models/communityFolders.model');

const CommunityEventService = require('../src/communitiesAPI/services/common/communityEvents.service');

const fixDiscountLinkedEntitiesDetails = async (params = {}) => {
  const discounts = await CommunityDiscount.find({
    linkedEntities: { $gt: { $size: 0 } },
    // 'linkedEntities.entityObjectId': new ObjectId('64ed8d79f7143e03c555160e'),
    // _id: new ObjectId("6523d8c934516a106b27a622")
  }).lean();

  await Promise.all(
    discounts.map(async (discount) => {
      const entitiesSet = new Set();
      const discountLinkedEntities = discount?.linkedEntities ?? [];
      discountLinkedEntities.forEach((entity) => {
        if (entity) {
          const value = JSON.stringify({
            type: entity.type,
            entityObjectId: entity.entityObjectId?.toString(),
          });
          if (!entitiesSet.has(value)) {
            entitiesSet.add(value);
          }
        }
      });
      const entitiesArray = [...entitiesSet];
      const linkedEntities = [];
      await Promise.all(
        entitiesArray.map(async (item) => {
          const entity = JSON.parse(item);
          if (entity.type === 'EVENT') {
            const event = await CommunityEvent.findById(
              entity.entityObjectId
            ).lean();
            if (event) {
              linkedEntities.push({
                type: 'EVENT',
                entityObjectId: event._id,
                title: event.title,
                slug: event.slug,
              });
            } else {
              console.log(`Event ${entity.entityObjectId} not found`);
            }
          } else if (entity.type === 'FOLDER') {
            const folder = await CommunityFolder.findById(
              entity.entityObjectId
            ).lean();
            if (folder) {
              linkedEntities.push({
                type: 'FOLDER',
                entityObjectId: folder._id,
                title: folder.title,
                slug: folder.resourceSlug,
              });
            } else {
              console.log(`Folder ${entity.entityObjectId} not found`);
            }
          } else if (entity.type === 'SUBSCRIPTION') {
            linkedEntities.push({
              type: 'SUBSCRIPTION',
              entityObjectId: new ObjectId(entity.entityObjectId),
              title: 'Membership',
            });
          }
        })
      );
      console.log(
        `Updating discount ${discount._id} with ${JSON.stringify(
          linkedEntities
        )}`
      );
      await CommunityDiscount.findByIdAndUpdate(discount._id, {
        linkedEntities,
      }).lean();
    })
  );
};

const start = async () => {
  await mongoClient.connect();
  const currentTimestampInMs = Date.now();

  await fixDiscountLinkedEntitiesDetails({
    currentTimestampInMs,
  });

  logger.info('Completed');
  process.exit(0);
};

start();
