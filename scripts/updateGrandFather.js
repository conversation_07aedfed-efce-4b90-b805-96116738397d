require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});
const fs = require('fs');
const path = require('path');
const _ = require('lodash');
const mongoClient = require('../src/mongoClient');
const CommunityModel = require('../src/communitiesAPI/models/community.model.js');
const FeaturePermissionManager = require('../src/services/common/featurePermissionManager.service');
const {
  FEATURE_LIST_ID,
  FEATURE_LIST_NAME,
} = require('../src/constants/common');

const FEATURE_PERM_TYPE = {
  FREE: 'FREE',
  PRO: 'PRO',
  PLATINUM: 'PLATINUM',
};

const getFeatureName = (featureId) => {
  const featureMap = {
    [FEATURE_LIST_ID.AFFILIATES]: 'AFFILIATES',
    [FEATURE_LIST_ID.EVENT_QR_CODE]: 'EVENT_QR_CODE',
    [FEATURE_LIST_ID.PIXEL_TRACKING]: 'PIXEL_TRACKING',
    [FEATURE_LIST_ID.MAGIC_REACH_WHATSAPP_MSG]:
      FEATURE_LIST_NAME.MAGIC_REACH_WHATSAPP_MSG,
  };
  return featureMap[featureId];
};
const parseArguments = () => {
  const args = process.argv.slice(2);

  if (args.includes('--help') || args.includes('-h')) {
    // Help text with all feature IDs
  }

  if (args.length < 2) {
    console.error('❌ Error: Feature ID and CSV filename are required');
    process.exit(1);
  }

  const featureId = parseInt(args[0], 10);
  const csvFilename = args[1];

  // Validation logic
  return { featureId, csvFilename };
};

/**
 * Read CSV file and parse community object IDs
 * @param {string} csvPath - Path to the CSV file
 * @returns {Array} Array of community object IDs
 */
const readCommunityIdsFromCSV = (csvPath) => {
  try {
    const csvContent = fs.readFileSync(csvPath, 'utf8');
    const lines = csvContent.split('\n').filter((line) => line.trim());

    if (lines.length === 0) return [];

    // Get headers from first line
    const headers = lines[0]
      .split(',')
      .map((h) => h.trim().toLowerCase().replace(/"/g, ''));

    // Find the column with community ID (flexible naming)
    const idColumnIndex = headers.findIndex(
      (header) =>
        header.includes('communityobjectid') ||
        header.includes('community_object_id') ||
        header.includes('communityid') ||
        header === '_id' ||
        header === 'id'
    );

    if (idColumnIndex === -1) {
      console.error(
        'No community ID column found. Expected columns: communityObjectId, community_object_id, communityId, _id, or id'
      );
      return [];
    }

    // Extract IDs from data rows
    const communityIds = lines
      .slice(1)
      .map((line) => {
        const columns = line
          .split(',')
          .map((col) => col.trim().replace(/"/g, ''));
        return columns[idColumnIndex];
      })
      .filter((id) => id && id.trim());

    console.log(`Found ${communityIds.length} community IDs in CSV`);
    return communityIds;
  } catch (error) {
    console.error('Error reading CSV:', error);
    return [];
  }
};

/**
 * Write results to CSV
 * @param {Array} results - Migration results
 * @param {string} outputPath - Output CSV path
 */
const writeResultsToCSV = (results, outputPath) => {
  try {
    // Create CSV rows
    const headers = [
      'communityShortCode',
      'Id',
      'planType',
      'success',
      'skipped',
      'error',
      'migratedAt',
    ];

    const rows = results.map((result) => [
      result.communityShortCode,
      result.objectId,
      result.planType,
      result.success,
      result.skipped || false,
      result.error || '',
      new Date().toISOString(),
    ]);

    // Combine header and rows
    const csvContent = [headers, ...rows]
      .map((row) => row.map((field) => `"${field}"`).join(','))
      .join('\n');

    fs.writeFileSync(outputPath, csvContent);
    console.log(`✅ Results written to ${outputPath}`);
  } catch (error) {
    console.error('Error writing results to CSV:', error);
  }
};
const hasFeature = (featurePermissions, featureId) => {
  for (const feature of featurePermissions) {
    if (feature.featureId === featureId) {
      return true;
    }
  }
  return false;
};
/**
 * Process a single community for migration
 * @param {Object} community - Community document
 * @returns {Object} Migration result
 */
const processCommunity = async (
  community,
  grandfatherFeature,
  featureId
) => {
  try {
    const featureName = getFeatureName(featureId);
    const result = {
      communityShortCode: community.communityShortCode,
      objectId: community._id.toString(),
      planType: community.config?.planType || 'FREE',
      success: true,
      skipped: false,
      error: null,
    };
    const communityfeaturePermission = community.featurePermissions || [];
    if (hasFeature(communityfeaturePermission, featureId)) {
      result.skipped = true;
      result.success = true;
      console.log(
        `⏭️  Skipping community ${community.communityShortCode} - ${featureName} feature already exists`
      );
      return result;
    }
    // ignore interval if there is not value
    communityfeaturePermission.push(grandfatherFeature);

    // Validation to ensure the feature was actually added
    if (!hasFeature(communityfeaturePermission, featureId)) {
      result.error = `Failed to add ${featureName} feature to array`;
      result.success = false;
      console.log(
        `❌ Failed to add ${featureName} feature for community ${community.communityShortCode}`
      );
      return result;
    }
    // Update the community document with new featurePermissions
    const updateResult = await CommunityModel.updateOne(
      { _id: community._id },
      {
        $set: {
          featurePermissions: communityfeaturePermission,
        },
      }
    );

    if (updateResult.matchedCount > 0) {
      result.success = true;
      console.log(
        `✅ Successfully updated community ${community.communityShortCode}`
      );
    } else {
      result.error = 'No document matched for update';
      console.log(
        `❌ Failed to update community ${community.communityShortCode}`
      );
    }

    return result;
  } catch (error) {
    console.error(
      `Error processing community ${community.communityShortCode}:`,
      error
    );
    return {
      communityShortCode: community.communityShortCode,
      objectId: community._id.toString(),
      success: false,
      error: error.message,
      skipped: false,
    };
  }
};

const start = async () => {
  try {
    const { featureId, csvFilename } = parseArguments();
    const featureName = getFeatureName(featureId);

    if (!featureName) {
      console.error(`❌ Invalid feature ID: ${featureId}`);
      process.exit(0);
    }

    console.log(`🚀 Starting ${featureName} feature migration...`); // dynamic feature name

    // read from csv that has community object id
    console.log('Starting grandfather features migration...');

    await mongoClient.connect();
    // Configuration
    const inputCsvPath = path.join(__dirname, `./data/${csvFilename}.csv`); //  dynamic filename
    const outputCsvPath = `./output/${csvFilename}_${Date.now()}.csv`; //  dynamic filename
    const batchSize = 100; // Process in batches to avoid memory issues

    // Ensure output directory exists
    const outputDir = path.dirname(outputCsvPath);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    let communityIds = [];

    // Read community IDs from CSV if provided
    if (fs.existsSync(inputCsvPath)) {
      console.log(`Reading community IDs from ${inputCsvPath}`);
      communityIds = readCommunityIdsFromCSV(inputCsvPath);
    }

    // If no CSV or empty CSV, finish early
    if (communityIds.length === 0) {
      console.log(' No CSV provided or empty CSV');
      process.exit(0);
    }

    console.log(
      ` Processing ${communityIds.length} communities in batches of ${batchSize}`
    );

    const allResults = [];
    let processedCount = 0;

    // Process in batches
    for (let i = 0; i < communityIds.length; i += batchSize) {
      const batch = communityIds.slice(i, i + batchSize);
      console.log(
        `\n🔄 Processing batch ${
          Math.floor(i / batchSize) + 1
        }/${Math.ceil(communityIds.length / batchSize)}`
      );

      // Get communities for this batch
      const communities = await CommunityModel.find({
        _id: { $in: batch },
      })
        .select(
          '_id communityShortCode featurePermissions config.planType'
        )
        .lean();

      // Process each community in the batch
      const permissionManager = new FeaturePermissionManager(
        FEATURE_PERM_TYPE.PLATINUM,
        []
      );

      const feature = _.omitBy(
        permissionManager.getFeature(featureId),
        _.isNil
      );
      const batchResults = await Promise.all(
        communities.map((community) =>
          processCommunity(
            community,
            _.omit(feature, ['isGrandfathered']),
            featureId
          )
        )
      );

      allResults.push(...batchResults);
      processedCount += communities.length;

      console.log(
        `✅ Batch completed. Processed ${processedCount}/${communityIds.length} communities`
      );

      //  Add a small delay between batches
      if (i + batchSize < communityIds.length) {
        await new Promise((resolve) => setTimeout(resolve, 100));
      }
    }

    // store the result in csv
    const successCount = allResults.filter((r) => r.success).length;
    const errorCount = allResults.filter((r) => !r.success).length;
    const skippedCount = allResults.filter((r) => r.skipped).length;
    const actuallyUpdatedCount = allResults.filter(
      (r) => r.success && !r.skipped
    ).length;

    console.log('\n📊 Migration Summary:');
    console.log(`✅ Successful migrations: ${successCount}`);
    console.log(
      `⏭️  Skipped (already had ${featureName}): ${skippedCount}`
    );
    console.log(`🔄 Actually updated: ${actuallyUpdatedCount}`);
    console.log(`❌ Failed migrations: ${errorCount}`);
    console.log(`📊 Total processed: ${allResults.length}`);

    // Write results to CSV
    writeResultsToCSV(allResults, outputCsvPath);
  } catch (error) {
    console.log(error);
    console.log('Error running for affiliates');
  } finally {
    process.exit(0);
  }
};

start();
