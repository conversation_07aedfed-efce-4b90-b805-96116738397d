require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const mongoClient = require('../src/mongoClient');

const MembershipModel = require('../src/models/membership/membership.model');
const LearnerModel = require('../src/models/learners.model');
const CountryCurrencyMappingModel = require('../src/models/countryInfoMapping.model');

const start = async () => {
  await mongoClient.connect();

  const totalCountries = await CountryCurrencyMappingModel.find({
    fallbackCountryId: { $exists: true },
  }).lean();

  await Promise.all(
    totalCountries.map(async (country) => {
      await LearnerModel.updateMany(
        { countryId: country.fallbackCountryId },
        { countryId: country.countryId }
      );
      await MembershipModel.updateMany(
        { 'countryInfo.id': country.fallbackCountryId },
        {
          countryInfo: {
            id: country.countryId,
            code: country.countryCode,
            name: country.country,
          },
        }
      );
    })
  );

  console.log('Completed');
  process.exit(0);
};

start();
