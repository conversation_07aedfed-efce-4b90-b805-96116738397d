require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const { DateTime } = require('luxon');

const mongoClient = require('../src/mongoClient');
const EarningAnalyticsModel = require('../src/models/earningAnalytics.model');
const CommunityModel = require('../src/communitiesAPI/models/community.model');
const FolderModel = require('../src/communitiesAPI/models/communityFolders.model');
const ProgramModel = require('../src/models/program/program.model');
const EventModel = require('../src/communitiesAPI/models/communityEvents.model');
const SessionAttendeesModel = require('../src/models/oneOnOneSessions/sessionAttendees.model');
const FolderViewersModel = require('../src/models/product/folderViewers.model');
const {
  COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES,
} = require('../src/communitiesAPI/constants');

const THRESHOLD = 1000;

const ENTITY_TYPE_PATHS = {
  FOLDER: 'folders',
  EVENT: 'events',
  SESSION: 'sessions',
  CHALLENGE: 'challenges',
  SUBSCRIPTION: 'subscriptions',
};

async function getEarningAnalyticsUpdateQueriesForSubscription(field) {
  const pathToUnwind = `$${field}`;
  const subscriptionType = `${field}.type`;
  const acceptTypes = ['NEW_SUBSCRIPTION', 'EXISTING_SUBSCRIPTION'];
  const groupId = '$communityObjectId';
  const quantity = `$${field}.quantity`;
  const revenueInUsd = `$${field}.revenueInUsd`;
  const revenueInLocalCurrency = `$${field}.revenueInLocalCurrency`;

  // change date accordingly
  const date = DateTime.utc(2024, 9, 17).startOf('day');

  const entities = await EarningAnalyticsModel.aggregate([
    {
      $match: {
        date: { $lt: date },
      },
    },
    {
      $unwind: {
        path: pathToUnwind,
        preserveNullAndEmptyArrays: false,
      },
    },
    {
      $match: {
        [subscriptionType]: {
          $in: acceptTypes,
        },
      },
    },
    {
      $group: {
        _id: groupId,
        totalQuantity: {
          $sum: quantity,
        },
        totalRevenueInUsd: {
          $sum: revenueInUsd,
        },
        totalRevenueInLocalCurrency: {
          $sum: revenueInLocalCurrency,
        },
      },
    },
  ]);

  const results = entities.map((entity) => {
    const updateQuery = {
      'earningAnalytics.revenueInUsd': entity.totalRevenueInUsd,
      'earningAnalytics.revenueInLocalCurrency':
        entity.totalRevenueInLocalCurrency,
    };

    if (
      field !== ENTITY_TYPE_PATHS.FOLDER &&
      field !== ENTITY_TYPE_PATHS.SESSION
    ) {
      updateQuery['earningAnalytics.quantity'] = entity.totalQuantity;
    }

    return {
      updateOne: {
        filter: { _id: entity._id },
        update: {
          $inc: updateQuery,
        },
      },
    };
  });

  console.log(JSON.stringify(results, null, 2));

  return results;
}

async function getEarningAnalyticsUpdateQueriesForAddon(field) {
  const pathToUnwind = `$${field}`;
  const groupId = `$${field}.entityObjectId`;
  const quantity = `$${field}.quantity`;
  const revenueInUsd = `$${field}.revenueInUsd`;
  const revenueInLocalCurrency = `$${field}.revenueInLocalCurrency`;

  // change date accordingly
  const date = DateTime.utc(2024, 9, 17).startOf('day');

  const entities = await EarningAnalyticsModel.aggregate([
    {
      $match: {
        date: { $lt: date },
      },
    },
    {
      $unwind: {
        path: pathToUnwind,
        preserveNullAndEmptyArrays: false,
      },
    },
    {
      $group: {
        _id: groupId,
        totalQuantity: {
          $sum: quantity,
        },
        totalRevenueInUsd: {
          $sum: revenueInUsd,
        },
        totalRevenueInLocalCurrency: {
          $sum: revenueInLocalCurrency,
        },
      },
    },
  ]);

  const results = entities.map((entity) => {
    const updateQuery = {
      'earningAnalytics.revenueInUsd': entity.totalRevenueInUsd,
      'earningAnalytics.revenueInLocalCurrency':
        entity.totalRevenueInLocalCurrency,
    };

    if (
      field !== ENTITY_TYPE_PATHS.FOLDER &&
      field !== ENTITY_TYPE_PATHS.SESSION
    ) {
      updateQuery['earningAnalytics.quantity'] = entity.totalQuantity;
    }

    return {
      updateOne: {
        filter: { _id: entity._id },
        update: {
          $inc: updateQuery,
        },
      },
    };
  });

  console.log(JSON.stringify(results, null, 2));

  return results;
}

async function getEarningAnalyticsUpdateQueries(field) {
  let result;

  if (field === ENTITY_TYPE_PATHS.SUBSCRIPTION) {
    result = await getEarningAnalyticsUpdateQueriesForSubscription(field);
  } else {
    result = await getEarningAnalyticsUpdateQueriesForAddon(field);
  }

  return result;
}

async function updateAccessCountForFolders() {
  const viewers = await FolderViewersModel.aggregate([
    {
      $group: {
        _id: {
          folderObjectId: '$folderObjectId',
          status: '$status',
        },
        count: { $sum: 1 },
      },
    },
    {
      $lookup: {
        from: 'community_folders',
        localField: '_id.folderObjectId',
        foreignField: '_id',
        as: 'folder',
      },
    },
    {
      $unwind: {
        path: '$folder',
        preserveNullAndEmptyArrays: false,
      },
    },
    {
      $project: {
        access: '$folder.access',
        _id: 1,
        free: {
          $cond: {
            if: { $eq: ['$_id.status', 'FREE'] },
            then: '$count',
            else: 0,
          },
        },
        paid: {
          $cond: {
            if: { $eq: ['$_id.status', 'PAID'] },
            then: '$count',
            else: 0,
          },
        },
        refunded: {
          $cond: {
            if: { $eq: ['$_id.status', 'REFUNDED'] },
            then: '$count',
            else: 0,
          },
        },
      },
    },
    {
      $group: {
        _id: '$_id.folderObjectId',
        access: { $first: '$access' },
        free: { $sum: '$free' },
        paid: { $sum: '$paid' },
        refunded: { $sum: '$refunded' },
      },
    },
  ]);

  return viewers.map((viewer) => {
    let accessCount = viewer.paid;
    if (viewer.access !== 'paid') {
      accessCount += viewer.free;
    }
    return {
      updateOne: {
        filter: { _id: viewer._id },
        update: { accessCount },
      },
    };
  });
}

async function updateAccessCountForSessions() {
  const sessions = await SessionAttendeesModel.aggregate([
    {
      $match: {
        status: COMMUNITY_SESSION_ATTENDEE_STATUS_TYPES.BOOKED,
      },
    },
    {
      $group: {
        _id: '$sessionObjectId',
        count: { $sum: 1 },
      },
    },
  ]);

  return sessions.map((session) => {
    const accessCount = session.count;
    return {
      updateOne: {
        filter: { _id: session._id },
        update: { accessCount },
      },
    };
  });
}

async function updateEntityStats(entityType, query) {
  let results = [];
  switch (query) {
    case 'earningAnalytics':
      results = await getEarningAnalyticsUpdateQueries(entityType);
      break;
    case 'accessCount': {
      if (entityType === ENTITY_TYPE_PATHS.FOLDER) {
        results = await updateAccessCountForFolders();
      } else if (entityType === ENTITY_TYPE_PATHS.SESSION) {
        results = await updateAccessCountForSessions();
      }
      break;
    }
    default:
      break;
  }

  const rounds = Math.ceil(results.length / THRESHOLD);
  const queries = [];
  for (let i = 0; i < rounds; i++) {
    const start = i * THRESHOLD;
    let end = start + THRESHOLD;
    if (end > results.length) end = results.length;
    queries.push({ start, end });
  }

  await Promise.all(
    queries.map(async ({ start, end }) => {
      switch (entityType) {
        case ENTITY_TYPE_PATHS.FOLDER:
        case ENTITY_TYPE_PATHS.SESSION:
          await FolderModel.bulkWrite(results.slice(start, end));
          break;
        case ENTITY_TYPE_PATHS.EVENT:
          await EventModel.bulkWrite(results.slice(start, end));
          break;
        case ENTITY_TYPE_PATHS.CHALLENGE:
          await ProgramModel.bulkWrite(results.slice(start, end));
          break;
        case ENTITY_TYPE_PATHS.SUBSCRIPTION:
          await CommunityModel.bulkWrite(results.slice(start, end));
          break;
        default:
          break;
      }
    })
  );
}

const start = async () => {
  await mongoClient.connect();

  // await Promise.all([
  //   updateEntityStats(ENTITY_TYPE_PATHS.FOLDER, 'earningAnalytics'),
  //   updateEntityStats(ENTITY_TYPE_PATHS.SESSION, 'earningAnalytics'),
  //   updateEntityStats(ENTITY_TYPE_PATHS.FOLDER, 'accessCount'),
  //   updateEntityStats(ENTITY_TYPE_PATHS.SESSION, 'accessCount'),
  // ]);

  await updateEntityStats(ENTITY_TYPE_PATHS.FOLDER, 'earningAnalytics');
  await updateEntityStats(ENTITY_TYPE_PATHS.SESSION, 'earningAnalytics');
  await updateEntityStats(ENTITY_TYPE_PATHS.EVENT, 'earningAnalytics');
  await updateEntityStats(ENTITY_TYPE_PATHS.CHALLENGE, 'earningAnalytics');
  await updateEntityStats(
    ENTITY_TYPE_PATHS.SUBSCRIPTION,
    'earningAnalytics'
  );

  console.log('Completed');
  process.exit(0);
};

start();
