require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const fs = require('fs').promises;
const moment = require('moment');
const mongoClient = require('../src/mongoClient');

const CommunityModel = require('../src/communitiesAPI/models/community.model');
const {
  updateFeeConfig,
} = require('../src/services/community/updatePayoutFeeConfig.service');
const logger = require('../src/services/logger.service');
const { ceilFixedNumber } = require('../src/utils/currency.util');
const { ENTITY_TYPE } = require('../src/services/plan/constants');
const {
  retrievePaymentFeeStructure,
} = require('../src/services/config/paymentFeeStructureConfig.service');
const FeeService = require('../src/communitiesAPI/services/common/fee.service');

const getZerolinkFee = async ({
  community,
  planType,
  customZerolinkFee,
}) => {
  const paymentFeeStructure = await retrievePaymentFeeStructure({
    baseCurrency: community.baseCurrency,
    planType,
  });

  const { feeConfig } =
    await FeeService.retrieveBasePayoutFeeConfigAndOtherSettings({
      community,
      paymentFeeStructure,
    });

  const { purchaseTypeConfigs } = feeConfig;

  if (!customZerolinkFee) {
    return purchaseTypeConfigs;
  }

  const updateZerolinkFee = purchaseTypeConfigs.ZERO_LINK.volumeTiers.map(
    (tier, index) => {
      const fee = customZerolinkFee[index];

      const newFee = { ...tier };
      newFee.revenueShareInPercentage =
        fee.nasFee ?? tier.revenueShareInPercentage;
      newFee.gatewayFeeInPercentage =
        fee.gatewayFee ?? tier.gatewayFeeInPercentage;
      newFee.processingFee = fee.processingFee ?? tier.processingFee;
      newFee.minGatewayFee = fee.minGatewayFee ?? tier.minGatewayFee;
      return newFee;
    }
  );

  purchaseTypeConfigs.ZERO_LINK = {
    revenueShareInPercentage:
      updateZerolinkFee[0].revenueShareInPercentage,
    gatewayFeeInPercentage: updateZerolinkFee[0].gatewayFeeInPercentage,
    processingFee: updateZerolinkFee[0].processingFee,
    minGatewayFee: updateZerolinkFee[0].minGatewayFee,
    volumeTiers: updateZerolinkFee,
  };
  return purchaseTypeConfigs;
};
const updateCommunityPayoutFeeConfig = async () => {
  // read the csv file
  // Guideline:
  // 1. Download sheet from
  // 2. change the start data and end data to M/D/YYYY format
  // 3. Update the header to be [communityCode,nasFee,gatewayFee,processingFee,minGatewayFee,proNasFee,proGatewayFee,proProcessingFee,proMinGatewayFee,proZerolinkR1NasFee,proZerolinkR1GatewayFee,proZerolinkR1ProcessingFee,proZerolinkR1MinGatewayFee,proZerolinkR2NasFee,proZerolinkR2GatewayFee,proZerolinkR2ProcessingFee,proZerolinkR2MinGatewayFee,proZerolinkR3NasFee,proZerolinkR3GatewayFee,proZerolinkR3ProcessingFee,proZerolinkR3MinGatewayFee,platinumNasFee,platinumGatewayFee,platinumProcessingFee,platinumMinGatewayFee,platinumZerolinkR1NasFee,platinumZerolinkR1GatewayFee,platinumZerolinkR1ProcessingFee,platinumZerolinkR1MinGatewayFee,platinumZerolinkR2NasFee,platinumZerolinkR2GatewayFee,platinumZerolinkR2ProcessingFee,platinumZerolinkR2MinGatewayFee,platinumZerolinkR3NasFee,platinumZerolinkR3GatewayFee,platinumZerolinkR3ProcessingFee,platinumZerolinkR3MinGatewayFee,startDate,expiryDate,Implemented,Internal Notes]
  const filename = 'COPY custom fee 2025_05_13 - 11 June.csv';
  let customFees = [];
  try {
    const data = await fs.readFile(filename, 'utf8');
    const lines = data.trim().split('\n');

    // Get the headers from the first line
    const headers = lines[0].split(',');

    // Parse each line as an object
    customFees = lines.slice(1).map((line) => {
      const values = line.split(',');

      // Create an object with the headers as keys
      const obj = {};
      headers.forEach((header, index) => {
        obj[header.trim()] = values[index].trim();
      });
      obj.proNasFee = obj.proNasFee
        ? parseFloat(obj.proNasFee.replace('%', ''))
        : 0;
      obj.proGatewayFee = obj.proGatewayFee
        ? parseFloat(obj.proGatewayFee.replace('%', ''))
        : 0;
      obj.proProcessingFee = obj.proProcessingFee
        ? ceilFixedNumber(
            parseFloat(obj.proProcessingFee.replace('$', '')) * 100
          )
        : 0;
      obj.proMinGatewayFee = obj.proMinGatewayFee
        ? ceilFixedNumber(
            parseFloat(obj.proMinGatewayFee.replace('$', '')) * 100
          )
        : 0;
      obj.nasFee = obj.nasFee
        ? parseFloat(obj.nasFee.replace('%', ''))
        : 0;
      obj.gatewayFee = obj.gatewayFee
        ? parseFloat(obj.gatewayFee.replace('%', ''))
        : 0;
      obj.processingFee = obj.processingFee
        ? ceilFixedNumber(
            parseFloat(obj.processingFee.replace('$', '')) * 100
          )
        : 0;
      obj.minGatewayFee = obj.minGatewayFee
        ? ceilFixedNumber(
            parseFloat(obj.minGatewayFee.replace('$', '')) * 100
          )
        : 0;

      // Handle proZerolink fees
      obj.proZerolink = ['R1', 'R2', 'R3'].map((tier) => {
        const nasFeeKey = `proZerolink${tier}NasFee`;
        const gatewayFeeKey = `proZerolink${tier}GatewayFee`;
        const processingFeeKey = `proZerolink${tier}ProcessingFee`;
        const minGatewayFeeKey = `proZerolink${tier}MinGatewayFee`;

        return {
          nasFee: obj[nasFeeKey]
            ? parseFloat(obj[nasFeeKey].replace('%', ''))
            : null,
          gatewayFee: obj[gatewayFeeKey]
            ? parseFloat(obj[gatewayFeeKey].replace('%', ''))
            : null,
          processingFee: obj[processingFeeKey]
            ? ceilFixedNumber(
                parseFloat(obj[processingFeeKey].replace('$', '')) * 100
              )
            : null,
          minGatewayFee: obj[minGatewayFeeKey]
            ? ceilFixedNumber(
                parseFloat(obj[minGatewayFeeKey].replace('$', '')) * 100
              )
            : null,
        };
      });

      obj.platinumNasFee = obj.platinumNasFee
        ? parseFloat(obj.platinumNasFee.replace('%', ''))
        : 0;
      obj.platinumGatewayFee = obj.platinumGatewayFee
        ? parseFloat(obj.platinumGatewayFee.replace('%', ''))
        : 0;
      obj.platinumProcessingFee = obj.platinumProcessingFee
        ? ceilFixedNumber(
            parseFloat(obj.platinumProcessingFee.replace('$', '')) * 100
          )
        : 0;
      obj.platinumMinGatewayFee = obj.platinumMinGatewayFee
        ? ceilFixedNumber(
            parseFloat(obj.platinumMinGatewayFee.replace('$', '')) * 100
          )
        : 0;

      obj.platinumZerolink = ['R1', 'R2', 'R3'].map((tier) => {
        const nasFeeKey = `platinumZerolink${tier}NasFee`;
        const gatewayFeeKey = `platinumZerolink${tier}GatewayFee`;
        const processingFeeKey = `platinumZerolink${tier}ProcessingFee`;
        const minGatewayFeeKey = `platinumZerolink${tier}MinGatewayFee`;

        return {
          nasFee: obj[nasFeeKey]
            ? parseFloat(obj[nasFeeKey].replace('%', ''))
            : null,
          gatewayFee: obj[gatewayFeeKey]
            ? parseFloat(obj[gatewayFeeKey].replace('%', ''))
            : null,
          processingFee: obj[processingFeeKey]
            ? ceilFixedNumber(
                parseFloat(obj[processingFeeKey].replace('$', '')) * 100
              )
            : null,
          minGatewayFee: obj[minGatewayFeeKey]
            ? ceilFixedNumber(
                parseFloat(obj[minGatewayFeeKey].replace('$', '')) * 100
              )
            : null,
        };
      });

      obj.startDate = moment.utc(obj.startDate, 'M/D/YYYY').toDate();
      obj.expiryDate = moment.utc(obj.expiryDate, 'M/D/YYYY').toDate();

      return obj;
    });
    for (const customFee of customFees) {
      logger.info(customFee);
      if (
        customFee.startDate > new Date() ||
        customFee.endDate < new Date()
      ) {
        throw new Error('Invalid date');
      }

      const community = await CommunityModel.findOne({
        code: customFee.communityCode,
      }).lean();

      const proZerolinkFee = await getZerolinkFee({
        community,
        planType: ENTITY_TYPE.PRO,
        customZerolinkFee: customFee.proZerolink,
      });

      const platinumZerolinkFee = await getZerolinkFee({
        community,
        planType: ENTITY_TYPE.PLATINUM,
        customZerolinkFee: customFee.platinumZerolink,
      });

      const newCustomPayoutFeeConfig = {
        effectiveTimeStart: new Date(),
        effectiveTimeEnd: customFee.expiryDate,
        default: {
          gatewayFeeInPercentage: customFee.gatewayFee,
          processingFee: customFee.processingFee,
          minGatewayFee: customFee.minGatewayFee,
          revenueShareInPercentage: customFee.nasFee,
        },
        [`${ENTITY_TYPE.PRO.toLowerCase()}`]: {
          gatewayFeeInPercentage: customFee.proGatewayFee,
          processingFee: customFee.proProcessingFee,
          minGatewayFee: customFee.proMinGatewayFee,
          revenueShareInPercentage: customFee.proNasFee,
          purchaseTypeConfigs: proZerolinkFee,
        },
        [`${ENTITY_TYPE.PLATINUM.toLowerCase()}`]: {
          gatewayFeeInPercentage: customFee.platinumGatewayFee,
          processingFee: customFee.platinumProcessingFee,
          minGatewayFee: customFee.platinumMinGatewayFee,
          revenueShareInPercentage: customFee.platinumNasFee,
          purchaseTypeConfigs: platinumZerolinkFee,
        },
      };
      const existedCustomFeeConfigs =
        community.config.customFeeConfigs ?? [];

      const lastFeeConfig = existedCustomFeeConfigs.pop();
      if (lastFeeConfig) {
        // For the effectiveTimeEnd that is in very far away future (like 2300-01-01)
        // We will update the effectiveTimeEnd of last config to be payoutFeeConfig.effectiveTimeStart
        if (
          lastFeeConfig.effectiveTimeEnd >
          newCustomPayoutFeeConfig.effectiveTimeStart
        ) {
          lastFeeConfig.effectiveTimeEnd =
            newCustomPayoutFeeConfig.effectiveTimeStart;
        }
        existedCustomFeeConfigs.push(
          lastFeeConfig,
          newCustomPayoutFeeConfig
        );
      } else {
        existedCustomFeeConfigs.push(newCustomPayoutFeeConfig);
      }

      const updateQuery = {
        'config.customFeeConfigs': existedCustomFeeConfigs,
      };
      logger.info(updateQuery);

      const updatedCommunity = await CommunityModel.findOneAndUpdate(
        { _id: community._id },
        updateQuery,
        { new: true }
      ).lean();

      await updateFeeConfig(updatedCommunity, true, new Date());
    }
  } catch (err) {
    logger.error(err.message);
    throw err;
  }
  console.log('Completed');
};

const updateOneCommunityPayoutFeeConfig = async () => {
  const community = await CommunityModel.findOne({
    code: 'VENDE_CON_PROPSITO',
  }).lean();
  await updateFeeConfig(community, true, new Date('2025-05-28'));
};

const start = async () => {
  await mongoClient.connect();
  await updateCommunityPayoutFeeConfig();
  // await updateOneCommunityPayoutFeeConfig();
  process.exit(0);
};

start();
