require('dotenv').config({
  path: process.env.NODE_ENV === 'development' ? '.env' : '.env.prod',
});

const logger = require('../src/services/logger.service');

const mongoClient = require('../src/mongoClient');

const CommunityFraud = require('../src/models/fraud/communityFraud.model');
const Community = require('../src/communitiesAPI/models/community.model');

const {
  findTotalGmv,
  countMembers,
} = require('../src/services/fraud/fraudCheck');

const fixDataInCommunityFraud = async () => {
  try {
    const communities = await Community.find(
      {},
      { link: 1, createdBy: 1, code: 1, isActive: 1 }
    );
    const communityMap = communities.reduce((acc, community) => {
      acc[community.code] = community;
      return acc;
    }, {});

    const communityFraudUpdateOperations = await CommunityFraud.find(
      {},
      { communityId: 1, communityCode: 1 }
    );

    const updatePromises = communityFraudUpdateOperations.map(
      async (updateOperation) => {
        const communityId = updateOperation.communityId?.toString();
        const communityCode = updateOperation.communityCode;

        const communityData = communityMap[communityCode];

        if (!communityData) {
          return null;
        }
        const { link, createdBy, isActive } = communityData;

        const [totalGmv, numOfMembers] = await Promise.all([
          findTotalGmv(communityId),
          countMembers(communityId),
        ]);

        return {
          updateOne: {
            filter: { communityCode },
            update: {
              $set: {
                createdBy,
                communityLink: link,
                isActive,
                totalGmv,
                numOfMembers,
              },
            },
          },
        };
      }
    );

    const resolvedUpdatePromises = await Promise.all(updatePromises);

    const filteredUpdatePromises = resolvedUpdatePromises.filter(Boolean);

    const result = await CommunityFraud.bulkWrite(filteredUpdatePromises);
    logger.log('CommunityFraud updated: ', result);
  } catch (error) {
    logger.error('Error fixing data in CommunityFraud: ', error);
  }
};

const start = async () => {
  await mongoClient.connect();

  await fixDataInCommunityFraud();

  logger.info('Completed');
  process.exit(0);
};

start();
