/* eslint-disable no-await-in-loop */
require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});
const fs = require('fs');
const mongoClient = require('../src/mongoClient');
const CommunityPlanOrder = require('../src/models/plan/communityPlanOrder.model');
const planService = require('../src/services/plan');
const { ORDER_STATUS } = require('../src/services/plan/constants');
const {
  sleepForSeconds,
} = require('../src/communitiesAPI/utils/timeOutRoutes');

const OUTPUT_FILE = `./logs/addPastProPlanPurchasersToProCommunity.output.txt`;

const MAX_DATE = '2025-03-19T19:00:00.00+00:00'; // will only deal with records before this date.

const writeToFile = (data) => {
  fs.writeFileSync(OUTPUT_FILE, data + '\n', {
    flag: 'a',
  });
  console.log(data);
};

const addPastProPlanPurchasersToProCommunity = async () => {
  const planOrders = await CommunityPlanOrder.find({
    $or: [
      { status: ORDER_STATUS.CURRENT },
      { status: ORDER_STATUS.CANCELLED, cancelledAt: { $gt: new Date() } },
    ],
    createdAt: { $lt: new Date(MAX_DATE) },
  }).lean();

  writeToFile(
    `Found following ${planOrders.length} active plan orders before ${MAX_DATE}`
  );
  writeToFile(
    JSON.stringify(
      planOrders.map((planOrder) => ({
        _id: planOrder._id,
        learnerObjectId: planOrder.learnerObjectId,
        communityObjectId: planOrder.communityObjectId,
        status: planOrder.status,
        createdAt: planOrder.createdAt,
      })),
      null,
      2
    )
  );

  const subscriptionIdToSubMap = {};

  for (const planOrder of planOrders) {
    await sleepForSeconds(0.1);
    try {
      const subscriptionsFetchedOrCreated =
        await planService.handleProPlanPurchaseSuccess({
          planOrderObjectId: planOrder._id,
        });

      // add to map
      subscriptionsFetchedOrCreated.forEach((subscription) => {
        subscriptionIdToSubMap[subscription._id] = subscription;
      });
      writeToFile(
        `Successfully processed plan order ${planOrder._id} for learner ${planOrder.learnerObjectId} and community ${planOrder.communityObjectId}`
      );
    } catch (error) {
      writeToFile(
        `Error processing plan order ${planOrder._id}: ${error.message}`
      );
    }
  }

  writeToFile('Unique subscriptions:');
  writeToFile(
    JSON.stringify(
      Object.values(subscriptionIdToSubMap).map((subscription) => ({
        _id: subscription._id,
        email: subscription.email,
        communityCode: subscription.communityCode,
        learnerObjectId: subscription.learnerObjectId,
        status: subscription.status,
      })),
      null,
      2
    )
  );
};

const start = async () => {
  await mongoClient.connect();

  await addPastProPlanPurchasersToProCommunity();

  writeToFile('Completed');
  process.exit(0);
};

start();
