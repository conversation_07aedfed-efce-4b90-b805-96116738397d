require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const logger = require('../src/services/logger.service');
const mongoClient = require('../src/mongoClient');
const CommunityModel = require('../src/communitiesAPI/models/community.model');
const LearnerModel = require('../src/models/learners.model');
const ProgramModel = require('../src/models/program/program.model');
const ProgramParticipantModel = require('../src/models/program/programParticipant.model');
const PrimaryMongooseConnection = require('../src/rpc/primaryMongooseConnection');

const service = require('../src/services/communitySignup');

const migrate = async () => {
  // get all communities
  const programs = await ProgramModel.find({
    slug: {
      $in: [
        '/build-your-audience-from-scratch',
        '/how-to-get-1000-subscribers-challenge',
        '/how-to-sell-without-being-salesy',
        '/scaling-with-paid-ads',
        '/the-community-coach-playbook-beyond-11-sessions',
        '/fixed-challenge-21-nov',
      ],
    },
  }).lean();

  const programIds = programs.map((item) => item._id);

  const newCommunity = await CommunityModel.findOne({
    link: '/nas.io-academy-challenges',
  }).lean();

  const participants = await ProgramParticipantModel.find({
    programObjectId: { $in: programIds },
  }).lean();

  logger.info(participants.length);
  const learnerObjectIds = new Set(
    participants.map((participant) => participant.learnerObjectId)
  );

  for (const learnerObjectId of learnerObjectIds) {
    const learner = await LearnerModel.findById(learnerObjectId).lean();
    logger.info(`Enroll ${learner.email} to ${newCommunity.code}`);
    const result = await service.SignupService.signup({
      communityCode: newCommunity.code,
      timezone: learner.timezone,
      trackingData: { source: 'organic' },
      requestor: 'signupRequestor',
      paymentProvider: 'stripe',
      memberInfo: {
        email: learner.email,
        languagePreference: learner.languagePreference,
      },
      items: [{ type: 'SUBSCRIPTION' }],
      email: learner.email,
      learnerObjectId,
      ip: '**************',
    });
  }

  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  try {
    await ProgramModel.updateMany(
      {
        _id: { $in: programIds },
      },
      { communityObjectId: newCommunity._id },
      { session }
    );

    await ProgramParticipantModel.updateMany(
      {
        programObjectId: { $in: programIds },
      },
      { communityObjectId: newCommunity._id },
      { session }
    );

    await session.commitTransaction();
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }
};

const start = async () => {
  await mongoClient.connect();

  await migrate();

  logger.info('Completed');
  process.exit(0);
};

start();
