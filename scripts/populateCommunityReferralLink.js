require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});
const mongoClient = require('../src/mongoClient');

const CommunityModel = require('../src/communitiesAPI/models/community.model');
const { generateUniqueCode } = require('../src/utils/codeGeneration.util');

const BATCH_LIMIT_COMMUNITIES = 1000;

const updateCommunities = async (query, page, limit) => {
  console.log(`Processing page ${page}, limit ${limit}`);
  const communities = await CommunityModel.find(query)
    .sort({ _id: 1 })
    .skip(page * limit)
    .limit(limit)
    .select('_id code title')
    .lean();
  const usedCodeSet = new Set();
  const bulkUpdatePipeline = [];
  let needsRerun = false;
  await Promise.all(
    communities.map(async (community) => {
      try {
        const finalCommunityReferralCode = await generateUniqueCode(
          community.title,
          CommunityModel,
          'communityReferralCode',
          {}
        );

        if (!usedCodeSet.has(finalCommunityReferralCode)) {
          usedCodeSet.add(finalCommunityReferralCode);
          bulkUpdatePipeline.push({
            updateOne: {
              filter: {
                _id: community._id,
              },
              update: {
                communityReferralCode: finalCommunityReferralCode,
                communityReferralCodeCreatedAt: new Date(),
                'config.showCommunityReferralLink': true,
              },
            },
          });
        }
      } catch (error) {
        console.error(
          `Error checking existing code for community ${community._id}: ${error}`
        );
        needsRerun = true;
      }
    })
  );

  if (bulkUpdatePipeline.length !== communities.length) {
    needsRerun = true;
  }

  const result = await CommunityModel.bulkWrite(bulkUpdatePipeline);
  console.log(
    `Updated ${result.modifiedCount} communities successfully. For ${bulkUpdatePipeline.length} out of ${communities.length} communities queried`
  );
  return needsRerun;
};

const start = async () => {
  try {
    await mongoClient.connect();
    const query = {
      isActive: true,
      isDraft: false,
      isDemo: false,
      communityReferralCode: { $exists: false },
    };

    const totalCommunities = await CommunityModel.countDocuments(query);
    const rounds = Math.ceil(totalCommunities / BATCH_LIMIT_COMMUNITIES);

    let needsRerun = false;

    for (let index = 0; index < rounds; index++) {
      // eslint-disable-next-line no-await-in-loop
      needsRerun = await updateCommunities(
        query,
        index,
        BATCH_LIMIT_COMMUNITIES
      );
    }
    if (needsRerun) {
      console.log(
        'Some communities need to be rerun. Run the script again.'
      );
    } else {
      console.log('Completed');
    }
  } catch (error) {
    console.error(`Error during the batch update: ${error.message}`);
  } finally {
    process.exit(0);
  }
};

start();
