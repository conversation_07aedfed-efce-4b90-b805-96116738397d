require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});
const fs = require('fs');
const path = require('path');
const _ = require('lodash');
const mongoClient = require('../src/mongoClient.js');
const CommunityModel = require('../src/communitiesAPI/models/community.model.js');
const {
  FEATURE_LIST_ID,
  FEATURE_LIST_NAME,
} = require('../src/constants/common.js');

const getFeatureName = (featureId) => {
  const featureMap = {
    [FEATURE_LIST_ID.AFFILIATES]: 'AFFILIATES',
    [FEATURE_LIST_ID.EVENT_QR_CODE]: 'EVENT_QR_CODE',
    [FEATURE_LIST_ID.PIXEL_TRACKING]: 'PIXEL_TRACKING',
  };
  return featureMap[featureId] || `FEATURE_${featureId}`;
};

const parseArguments = () => {
  const args = process.argv.slice(2);

  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
📋 Rollback Grandfather Features Script
=======================================

Usage: node rollbackGrandfather.js [FEATURE_ID] [CSV_FILENAME] [OPTIONS]

Arguments:
  FEATURE_ID    - The ID of the feature to rollback (e.g., 7 for AFFILIATES)
  CSV_FILENAME  - Name of the CSV file (without .csv extension)

Options:
  --dry-run     - Preview changes without applying them
  --help, -h    - Show this help message

Examples:
  node rollbackGrandfather.js 7 aff
  node rollbackGrandfather.js 7 aff --dry-run

Available Feature IDs:
  ${Object.entries(FEATURE_LIST_ID || {})
    .map(([name, id]) => `${id} - ${name}`)
    .join('\n  ')}
`);
    process.exit(0);
  }

  if (args.length < 2) {
    console.error('❌ Error: Feature ID and CSV filename are required');
    console.error(
      'Usage: node rollbackGrandfather.js [FEATURE_ID] [CSV_FILENAME]'
    );
    process.exit(1);
  }

  const featureId = parseInt(args[0], 10);
  const csvFilename = args[1];
  const isDryRun = args.includes('--dry-run');

  if (isNaN(featureId)) {
    console.error('❌ Error: Feature ID must be a number');
    process.exit(1);
  }

  return { featureId, csvFilename, isDryRun };
};

/**
 * Read community IDs from CSV file
 * @param {string} csvPath - Path to the CSV file
 * @returns {Array} Array of community object IDs
 */
const readCommunityIdsFromCSV = (csvPath) => {
  try {
    const csvContent = fs.readFileSync(csvPath, 'utf8');
    const lines = csvContent.split('\n').filter((line) => line.trim());

    if (lines.length === 0) return [];

    // Get headers from first line
    const headers = lines[0]
      .split(',')
      .map((h) => h.trim().toLowerCase().replace(/"/g, ''));

    // Find the column with community ID (flexible naming)
    const idColumnIndex = headers.findIndex(
      (header) =>
        header.includes('communityobjectid') ||
        header.includes('community_object_id') ||
        header.includes('communityid') ||
        header === '_id' ||
        header === 'id'
    );

    if (idColumnIndex === -1) {
      console.error(
        'No community ID column found. Expected columns: communityObjectId, community_object_id, communityId, _id, or id'
      );
      return [];
    }

    // Extract IDs from data rows
    const communityIds = lines
      .slice(1)
      .map((line) => {
        const columns = line
          .split(',')
          .map((col) => col.trim().replace(/"/g, ''));
        return columns[idColumnIndex];
      })
      .filter((id) => id && id.trim());

    console.log(`Found ${communityIds.length} community IDs in CSV`);
    return communityIds;
  } catch (error) {
    console.error('Error reading CSV:', error);
    return [];
  }
};

/**
 * Write rollback results to CSV
 * @param {Array} results - Rollback results
 * @param {string} outputPath - Output CSV path
 */
const writeResultsToCSV = (results, outputPath) => {
  try {
    const headers = [
      'communityShortCode',
      'Id',
      'planType',
      'success',
      'removed',
      'notFound',
      'error',
      'rolledBackAt',
    ];

    const rows = results.map((result) => [
      result.communityShortCode,
      result.objectId,
      result.planType,
      result.success,
      result.removed || false,
      result.notFound || false,
      result.error || '',
      new Date().toISOString(),
    ]);

    const csvContent = [headers, ...rows]
      .map((row) => row.map((field) => `"${field}"`).join(','))
      .join('\n');

    fs.writeFileSync(outputPath, csvContent);
    console.log(`✅ Results written to ${outputPath}`);
  } catch (error) {
    console.error('Error writing results to CSV:', error);
  }
};

/**
 * Check if feature exists in permissions array
 * @param {Array} featurePermissions - Current feature permissions
 * @param {number} featureId - Feature ID to check
 * @returns {boolean}
 */
const hasFeature = (featurePermissions, featureId) => {
  for (const feature of featurePermissions) {
    if (feature.featureId === featureId) {
      return true;
    }
  }
  return false;
};

/**
 * Process a single community for rollback
 * @param {Object} community - Community document
 * @param {number} featureId - Feature ID to remove
 * @param {boolean} isDryRun - Whether this is a dry run
 * @returns {Object} Rollback result
 */
const processCommunityRollback = async (
  community,
  featureId,
  isDryRun
) => {
  try {
    const featureName = getFeatureName(featureId);
    const result = {
      communityShortCode: community.communityShortCode,
      objectId: community._id.toString(),
      planType: community.config?.planType || 'FREE',
      success: true,
      removed: false,
      notFound: false,
      error: null,
    };

    const communityFeaturePermissions = community.featurePermissions || [];

    // Check if feature exists
    if (!hasFeature(communityFeaturePermissions, featureId)) {
      result.notFound = true;
      result.success = true;
      console.log(
        `⏭️  Skipping community ${community.communityShortCode} - ${featureName} feature not found`
      );
      return result;
    }

    // Filter out the feature
    const updatedPermissions = communityFeaturePermissions.filter(
      (feature) => feature.featureId !== featureId
    );

    // Verify feature was removed
    if (hasFeature(updatedPermissions, featureId)) {
      result.error = `Failed to remove ${featureName} feature from array`;
      result.success = false;
      console.log(
        `❌ Failed to remove ${featureName} feature for community ${community.communityShortCode}`
      );
      return result;
    }

    if (isDryRun) {
      result.success = true;
      result.removed = true;
      console.log(
        `🔍 [DRY RUN] Would remove ${featureName} from community ${community.communityShortCode}`
      );
      return result;
    }

    // Update the community document
    const updateResult = await CommunityModel.updateOne(
      { _id: community._id },
      {
        $set: {
          featurePermissions: updatedPermissions,
        },
      }
    );

    if (updateResult.matchedCount > 0) {
      result.success = true;
      result.removed = true;
      console.log(
        `✅ Successfully removed ${featureName} from community ${community.communityShortCode}`
      );
    } else {
      result.error = 'No document matched for update';
      result.success = false;
      console.log(
        `❌ Failed to update community ${community.communityShortCode}`
      );
    }

    return result;
  } catch (error) {
    console.error(
      `Error processing community ${community.communityShortCode}:`,
      error
    );
    return {
      communityShortCode: community.communityShortCode,
      objectId: community._id.toString(),
      planType: community.config?.planType || 'FREE',
      success: false,
      removed: false,
      notFound: false,
      error: error.message,
    };
  }
};

const start = async () => {
  try {
    const { featureId, csvFilename, isDryRun } = parseArguments();
    const featureName = getFeatureName(featureId);

    console.log(
      `🔄 Starting Rollback for ${featureName} feature (ID: ${featureId})`
    );
    if (isDryRun) {
      console.log('🔍 Running in DRY RUN mode - no changes will be made');
    }

    await mongoClient.connect();

    // Configuration
    const inputCsvPath = path.join(__dirname, `./data/${csvFilename}.csv`);
    const timestamp = Date.now();
    const outputCsvPath = path.join(
      __dirname,
      `./output/rollback_${csvFilename}_${timestamp}.csv`
    );
    const batchSize = 100;

    // Ensure output directory exists
    const outputDir = path.join(__dirname, './output');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Check if CSV file exists
    if (!fs.existsSync(inputCsvPath)) {
      console.error(`❌ CSV file not found: ${inputCsvPath}`);
      process.exit(1);
    }

    console.log(`Reading community IDs from ${inputCsvPath}`);
    const communityIds = readCommunityIdsFromCSV(inputCsvPath);

    if (communityIds.length === 0) {
      console.log('No community IDs found in CSV');
      process.exit(0);
    }

    console.log(
      `Processing ${communityIds.length} communities in batches of ${batchSize}`
    );

    const allResults = [];
    let processedCount = 0;

    // Process in batches
    for (let i = 0; i < communityIds.length; i += batchSize) {
      const batch = communityIds.slice(i, i + batchSize);
      console.log(
        `\n🔄 Processing batch ${
          Math.floor(i / batchSize) + 1
        }/${Math.ceil(communityIds.length / batchSize)}`
      );

      // Get communities for this batch
      const communities = await CommunityModel.find({
        _id: { $in: batch },
      })
        .select(
          '_id communityShortCode featurePermissions config.planType'
        )
        .lean();

      // Process each community in the batch
      const batchResults = await Promise.all(
        communities.map((community) =>
          processCommunityRollback(community, featureId, isDryRun)
        )
      );

      allResults.push(...batchResults);
      processedCount += communities.length;

      console.log(
        `✅ Batch completed. Processed ${processedCount}/${communityIds.length} communities`
      );

      // Add a small delay between batches
      if (i + batchSize < communityIds.length) {
        await new Promise((resolve) => setTimeout(resolve, 100));
      }
    }

    // Generate summary
    const successCount = allResults.filter((r) => r.success).length;
    const errorCount = allResults.filter((r) => !r.success).length;
    const removedCount = allResults.filter((r) => r.removed).length;
    const notFoundCount = allResults.filter((r) => r.notFound).length;

    console.log('\n📊 Rollback Summary:');
    console.log(`✅ Successful operations: ${successCount}`);
    console.log(`🗑️  Features removed: ${removedCount}`);
    console.log(`⏭️  Skipped (feature not found): ${notFoundCount}`);
    console.log(`❌ Failed operations: ${errorCount}`);
    console.log(`📊 Total processed: ${allResults.length}`);

    if (isDryRun) {
      console.log('\n⚠️  This was a DRY RUN - no changes were made');
    }

    // Write results to CSV
    writeResultsToCSV(allResults, outputCsvPath);
  } catch (error) {
    console.log(error);
    console.log('Error running rollback');
  } finally {
    process.exit(0);
  }
};

start();
