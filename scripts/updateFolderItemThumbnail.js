require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const mongoClient = require('../src/mongoClient');

const FolderItemModel = require('../src/communitiesAPI/models/communityFolderItems.model');

const start = async () => {
  await mongoClient.connect();

  await FolderItemModel.updateMany(
    {
      thumbnail: {
        $in: [
          'https://d2oi1rqwb0pj00.cloudfront.net/nasIO/library/png/video_thumbnail2.png',
          'https://d2oi1rqwb0pj00.cloudfront.net/nasIO/library/png/image.png',
          'https://d2oi1rqwb0pj00.cloudfront.net/nasIO/library/png/audio.png',
          'https://d2oi1rqwb0pj00.cloudfront.net/nasIO/library/png/file_thumbnail.png',
          'https://d2oi1rqwb0pj00.cloudfront.net/nasIO/library/png/external_link_thumbnail.png',
          'https://d2oi1rqwb0pj00.cloudfront.net/nasIO/library/png/feb12/thumbnail-files.png',
          'https://d2oi1rqwb0pj00.cloudfront.net/nasIO/library/png/feb12/thumbnail-link.png',
          'https://d2oi1rqwb0pj00.cloudfront.net/nasIO/library/png/feb12/thumbnail-video.png',
          'https://d2oi1rqwb0pj00.cloudfront.net/nasIO/library/png/feb12/thumbnail-image.png',
          'https://d2oi1rqwb0pj00.cloudfront.net/nasIO/library/png/feb12/thumbnail-audio.png',
        ],
      },
    },
    { thumbnail: null }
  ).lean();

  console.log('Completed');
  process.exit(0);
};

start();
