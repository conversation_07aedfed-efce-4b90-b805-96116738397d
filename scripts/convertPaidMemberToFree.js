require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const fs = require('fs').promises;
const axios = require('axios');

const logger = require('../src/services/logger.service');
const { getConfigByType } = require('../src/services/config.service');
const {
  CONFIG_TYPES,
  communityEnrolmentStatuses,
  COMMUNITY_MEMBER_TYPES,
  PURCHASE_TYPE,
  BATCH_METADATA_MODEL_TYPE,
  MEMBERSHIP_ACTION_EVENT_TYPES,
} = require('../src/constants/common');
const {
  MAIN_PAYMENT_BACKEND_URL,
  NOTIFICATION_URL,
  NAS_IO_FRONTEND_URL,
  NOTIFICATION_AUTH,
} = require('../src/config');
const CheckInternalMemberUtils = require('../src/utils/checkInternalMember.util');
const NameUtils = require('../src/utils/name.util');
const MailUtils = require('../src/utils/mail.util');
const MongodbUtils = require('../src/utils/mongodb.util');

const mongoClient = require('../src/mongoClient');
const CommunitySubscriptionModel = require('../src/communitiesAPI/models/communitySubscriptions.model');
const WhatsappParticipantsModel = require('../src/communitiesAPI/models/whatsappParticipants.model');
const CommunityModel = require('../src/communitiesAPI/models/community.model');
const LearnerModel = require('../src/models/learners.model');
const CommunityRole = require('../src/communitiesAPI/models/communityRole.model');
const batchMetadataService = require('../src/services/batchMetadata');
const accessService = require('../src/services/community/accessService');
const actionEventService = require('../src/services/actionEvent');

async function removeSubscription(
  currentTimestampInMs,
  subscription,
  community
) {
  let isRemoved = false;

  try {
    await accessService.removeAccess({
      entityType: PURCHASE_TYPE.SUBSCRIPTION,
      entityObjectId: community._id,
      learnerObjectId: subscription.learnerObjectId,
      purchasedId: subscription.communitySignupId,
      communityObjectId: community._id,
      removalReason: 'Change paid to free',
    });

    isRemoved = true;
  } catch (err) {
    logger.error(
      'removeSubscription failed due to',
      err.message,
      err.stack
    );
  }

  await fs.appendFile(
    `logs/paid_to_free_${currentTimestampInMs}`,
    `REMOVED,${subscription.stripeSubscriptionId},${isRemoved}\n`
  );

  return isRemoved;
}

async function retrieveCommunityOwnerInfo(communityCode) {
  const query = [
    {
      $match: {
        communityCode,
        role: 'owner',
      },
    },
    ...MongodbUtils.lookupAndUnwind(
      'users',
      'userObjectId',
      '_id',
      'user'
    ),
    ...MongodbUtils.lookupAndUnwind(
      'learners',
      'user.learner',
      '_id',
      'learner'
    ),
    {
      $project: {
        profileImage: '$learner.profileImage',
        firstName: '$learner.firstName',
        lastName: '$learner.lastName',
        email: 1,
        _id: 0,
      },
    },
  ];

  const communityOwnerLearner = (await CommunityRole.aggregate(query))[0];
  return communityOwnerLearner;
}

async function sendMail(
  communityOwnerInfo,
  community,
  subscription,
  learner
) {
  try {
    const name = NameUtils.getName(
      learner.firstName,
      learner.lastName,
      learner.email
    );

    const communityOwnerEmail = communityOwnerInfo.email;
    const communityOwnerName = NameUtils.getName(
      communityOwnerInfo.firstName,
      communityOwnerInfo.lastName,
      communityOwnerInfo.email
    );

    const managerEmailConfig = MailUtils.retrieveManagerMailConfig(
      community.title,
      community.link,
      communityOwnerEmail,
      communityOwnerName
    );

    logger.info(
      `sendMail: manager email config: ${JSON.stringify(
        managerEmailConfig
      )}`
    );

    const communityProfileImage =
      community.thumbnailImgData?.mobileImgData?.src ??
      'https://d2oi1rqwb0pj00.cloudfront.net/na-website/community-product-page/nas-io-homepage/NA+logo.jpeg';

    const hostProfileImage = communityOwnerInfo.profileImage;

    const emailData = {
      name: learner.firstName ?? '',
      community_name: community.title,
      community_profile_image: communityProfileImage,
      community_link: `${NAS_IO_FRONTEND_URL}/${community.link}`,
      community_host: communityOwnerName,
      host_profile_image: hostProfileImage,
      community_code: subscription.communityCode,
      student_header_name: 'Welcome',
      student_name: learner.firstName ?? '',
    };

    logger.info(`sendMail: email data: ${JSON.stringify(emailData)}`);

    const data = {
      mailType: 'COMMUNITY_ENROLMENT_WITHOUT_APPLICATION',
      mailCourse: subscription.communityCode,
      fromMailName: managerEmailConfig.fromMailName,
      fromMail: managerEmailConfig.fromMail,
      toMail: [subscription.email],
      toMailName: [name],
      replyToMail: managerEmailConfig.replyToMail,
      replyToMailName: managerEmailConfig.replyToMailName,
      data: emailData,
      requesterServiceName: 'LPBE',
    };

    logger.info(`sendMail: data: ${JSON.stringify(data)}`);

    await axios.post(`${NOTIFICATION_URL}/api/v1/send-email`, data, {
      headers: {
        Authorization: `Bearer ${NOTIFICATION_AUTH}`,
      },
    });
  } catch (error) {
    logger.error(`sendMail: ${error}`);
  }
}

async function createFreeSubscription(
  currentTimestampInMs,
  subscription,
  createdBy,
  communityObjectId,
  communityOwnerInfo,
  community
) {
  const subscriptionExists = await CommunitySubscriptionModel.exists({
    email: subscription.email,
    communityCode: community.code,
    status: communityEnrolmentStatuses.CURRENT,
  });

  if (subscriptionExists) {
    logger.warn('Subscription already exists');

    await fs.appendFile(
      `logs/paid_to_free_${currentTimestampInMs}`,
      `SUBSCRIPTION_EXISTS_ERROR,${subscription.email},${subscription.communityCode},${subscription.learnerId},${subscription.learnerObjectId}\n`
    );

    return null;
  }

  const learner = await LearnerModel.findById(
    subscription.learnerObjectId
  ).lean();

  const isInternalMember = CheckInternalMemberUtils.hasInternalDomain(
    subscription.email
  );

  const freeSubscription = {
    communityCode: subscription.communityCode,
    email: subscription.email,
    phoneNumber: subscription.phoneNumber,
    learnerId: subscription.learnerId,
    learnerObjectId: subscription.learnerObjectId,
    status: communityEnrolmentStatuses.CURRENT,
    country: subscription.country,
    createdBy,
    memberType: isInternalMember
      ? COMMUNITY_MEMBER_TYPES.NASACADEMY
      : COMMUNITY_MEMBER_TYPES.FREE,
  };

  const newSubscription = await CommunitySubscriptionModel.create(
    freeSubscription
  );

  await batchMetadataService.add({
    batchMetadataModelType: BATCH_METADATA_MODEL_TYPE.SUBSCRIPTION,
    entityObjectId: community._id,
    communityObjectId: community._id,
    community,
    addedObjectId: newSubscription._id,
  });

  await actionEventService.sendFreeMembershipActionEvent({
    actionEventType: MEMBERSHIP_ACTION_EVENT_TYPES.SUBSCRIPTION_SUCCESS,
    actionEventCreatedAt: newSubscription.createdAt,
    subscription: newSubscription,
    community,
  });

  logger.info(`createFreeSubscription: ${newSubscription.email}`);

  await fs.appendFile(
    `logs/paid_to_free_${currentTimestampInMs}`,
    `CREATE_SUBSCRIPTION,${subscription.email},${subscription.communityCode},${newSubscription._id},${subscription.learnerId},${subscription.learnerObjectId}\n`
  );

  if (subscription.phoneNumber && subscription.phoneNumber !== '') {
    const whatsappNumber = subscription.phoneNumber.replace('+', '');
    await WhatsappParticipantsModel.findOneAndUpdate(
      { number: whatsappNumber, communityObjectId },
      {
        subscriptionObjectId: newSubscription._id,
        learnerId: subscription.learnerId,
        learnerObjectId: subscription.learnerObjectId,
        removeWhatsappStatus: null,
        removeWhatsappDate: null,
      }
    ).lean();

    logger.info(
      `createFreeSubscription: update whatsapp ${whatsappNumber}`
    );

    await fs.appendFile(
      `logs/paid_to_free_${currentTimestampInMs}`,
      `UPDATE_WHATSAPP,${subscription.email},${subscription.communityCode},${whatsappNumber},${newSubscription._id}\n`
    );
  }

  // await sendMail(communityOwnerInfo, community, subscription, learner);
}

async function start() {
  await mongoClient.connect();

  let communityCode;
  let actionEmail;

  const emails = [];

  const args = process.argv.slice(2); // Exclude 'node' and the script filename

  const communityCodeIndex = args.findIndex((arg) => arg === '-c');
  const actionEmailIndex = args.findIndex((arg) => arg === '-e');

  if (communityCodeIndex !== -1 && communityCodeIndex + 1 < args.length) {
    communityCode = args[communityCodeIndex + 1];
  } else {
    throw new Error('Require community code, use -c <communityCode>');
  }

  if (actionEmailIndex !== -1 && actionEmailIndex + 1 < args.length) {
    actionEmail = args[actionEmailIndex + 1];
  } else {
    throw new Error('Require action email, use -e <action email>');
  }

  const currentTimestampInMs = Date.now();

  const matchFilter = {
    communityCode,
    status: communityEnrolmentStatuses.CURRENT,
    stripeSubscriptionId: { $exists: true },
  };

  if (emails.length) {
    matchFilter.email = { $in: emails };
  }

  const [subscriptions, community, communityOwnerInfo] = await Promise.all(
    [
      CommunitySubscriptionModel.find(matchFilter).lean(),
      CommunityModel.findOne({
        code: communityCode,
      }).lean(),
      retrieveCommunityOwnerInfo(communityCode),
    ]
  );

  if (!community) {
    throw new Error(`Community not found: ${communityCode}`);
  }

  logger.info(subscriptions.length);

  for await (const subscription of subscriptions) {
    if (subscription.stripeSubscriptionId) {
      const isRemoved = await removeSubscription(
        currentTimestampInMs,
        subscription,
        community
      );

      if (isRemoved) {
        await createFreeSubscription(
          currentTimestampInMs,
          subscription,
          actionEmail,
          community._id,
          communityOwnerInfo,
          community
        );
      }
    }
  }

  logger.info('Completed');
  process.exit(0);
}

start();
