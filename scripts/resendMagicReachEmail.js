require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const fs = require('fs').promises;

const logger = require('../src/services/logger.service');
const mongoClient = require('../src/mongoClient');
const { magicReachSend } = require('../src/services/magicReach');
const MagicReachModel = require('../src/models/magicReach/communityMagicReachEmail.model');

async function retrieveDraftMagicReachEmail(magicReachEmailObjectIds) {
  const magicReachEmails = await MagicReachModel.find({
    _id: { $in: magicReachEmailObjectIds },
    status: 'draft',
  }).lean();

  return magicReachEmails;
}

const start = async () => {
  await mongoClient.connect();

  const magicReachEmailObjectIds = [];

  const magicReachEmails = await retrieveDraftMagicReachEmail(
    magicReachEmailObjectIds
  );

  for await (const magicReachEmail of magicReachEmails) {
    const magicReachEmailStringifiedJson = JSON.parse(
      JSON.stringify(magicReachEmail)
    );

    const communityId = magicReachEmailStringifiedJson.communityId;
    const messageId = magicReachEmailStringifiedJson._id;

    const message = `${magicReachEmail._id}`;
    console.log(message);

    try {
      await magicReachSend.sendMessage({
        communityId,
        messageId,
        messageData: magicReachEmailStringifiedJson,
      });

      await fs.appendFile(
        'logs/resendMagicReachEmail.log',
        `${message}\n`
      );
    } catch (err) {
      console.error(err);

      await fs.appendFile(
        'logs/resendMagicReachEmail_error.log',
        `${message},${err}\n`
      );
    }
  }

  logger.info('Completed');
  process.exit(0);
};

start();
