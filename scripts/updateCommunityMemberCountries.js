require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});
const ObjectId = require('mongoose').Types.ObjectId;
const mongoClient = require('../src/mongoClient');

const MembershipModel = require('../src/models/membership/membership.model');
const CommunityModel = require('../src/communitiesAPI/models/community.model');
const CountryCurrencyMappingModel = require('../src/models/countryInfoMapping.model');
const membershipService = require('../src/services/membership');
const membershipConstants = require('../src/services/membership/constants');
const {
  getMongoServerTimeoutConfig,
} = require('../src/services/config.service');
const PurchaseTransactionModel = require('../src/communitiesAPI/models/communityPurchaseTransactions.model');
const LearnerModel = require('../src/models/learners.model');

const BATCH_LIMIT_COMMUNITIES = 1000;
const BATCH_LIMIT_COUNTRIES = 100;

const BULK_UPDATE_COMMUNITIES_DEFAULT_QUERY = {
  isActive: true,
  isDraft: false,
  countriesUpdated: { $ne: true },
};

const updateCommunityCountries = async (
  query,
  page,
  limit,
  defaultCountryMappingBatches = [],
  fallbackCountriesMap
) => {
  const communities = await CommunityModel.find(query)
    .sort({ _id: 1 })
    .skip(page * limit)
    .limit(limit)
    .select(
      '_id code title memberCountries countriesUpdated bots isWhatsappExperienceCommunity'
    )
    .lean();

  const bulkUpdatePipeline = await Promise.all(
    communities.map(async (community) => {
      const memberCountries = [];

      let countryMappingBatches = defaultCountryMappingBatches;

      if (community.memberCountries?.length > 0) {
        countryMappingBatches = [];
        let currentBatch = [];
        community.memberCountries.forEach((country, index) => {
          if (country.id && country.name && country.code) {
            currentBatch.push(country);
          } else if (fallbackCountriesMap.get(country.id)) {
            currentBatch.push(fallbackCountriesMap.get(country.id));
          }

          if (
            currentBatch.length === BATCH_LIMIT_COUNTRIES ||
            index === community.memberCountries.length - 1
          ) {
            countryMappingBatches.push(currentBatch);
            currentBatch = [];
          }
        });
      }

      for await (const countries of countryMappingBatches) {
        const status = [
          membershipConstants.MEMBERSHIP_STATUS.SUBSCRIBED,
          membershipConstants.MEMBERSHIP_STATUS.NOT_ON_NASIO,
        ];
        const maxTimeMS = getMongoServerTimeoutConfig({
          collectionName: 'membership',
          operation: 'aggregate',
        });
        await Promise.all(
          countries.map(async (country) => {
            const pipeline =
              membershipService.membershipSearchUtils.buildSearchMetaPipeline(
                {
                  community,
                  status,
                  otherFilters: {
                    'countryInfo.id': country.id,
                  },
                  returnStoredSource: true,
                }
              );
            const countryMeta = await MembershipModel.aggregate(pipeline)
              .option({ maxTimeMS })
              .read('sp');

            if (countryMeta[0].count.total > 0) {
              memberCountries.push({
                ...country,
                count: countryMeta[0].count.total,
              });
            }
          })
        );
      }

      return {
        updateOne: {
          filter: {
            _id: community._id,
          },
          update: {
            memberCountries,
            countriesUpdated: true,
          },
        },
      };
    })
  );

  await CommunityModel.bulkWrite(bulkUpdatePipeline);
};

// eslint-disable-next-line no-unused-vars
const bulkUpdateCommunityCountry = async (
  countryMappingBatches,
  fallbackCountriesMap,
  communityObjectId = null
) => {
  const query = { ...BULK_UPDATE_COMMUNITIES_DEFAULT_QUERY };
  if (communityObjectId) {
    query._id = communityObjectId;
    delete query.countriesUpdated;
  }

  const totalCommunities = await CommunityModel.countDocuments(query);
  const rounds = Math.ceil(totalCommunities / BATCH_LIMIT_COMMUNITIES);

  for (let index = 0; index < rounds; index++) {
    // eslint-disable-next-line no-await-in-loop
    await updateCommunityCountries(
      query,
      index,
      BATCH_LIMIT_COMMUNITIES,
      countryMappingBatches,
      fallbackCountriesMap
    );
  }
};

const updateLearnerAndMembershipFromTransactions = async (
  query,
  page,
  limit,
  countryMappingMap,
  fallbackCountriesMap
) => {
  const transactions = await PurchaseTransactionModel.aggregate([
    { $match: query },
    {
      $sort: { _id: 1 },
    },
    {
      $skip: page * limit,
    },
    {
      $limit: limit,
    },
    {
      $lookup: {
        from: 'community_subscriptions',
        localField: 'subscriptionObjectId',
        foreignField: '_id',
        as: 'sub',
      },
    },
    {
      $project: {
        _id: 1,
        country_id: 1,
        email: 1,
        learnerObjectId: 1,
        subscriptionLearnerObjectId: { $first: '$sub.learnerObjectId' },
      },
    },
  ]);

  const membershipPipelineMap = new Map();
  const learnerPipelineMap = new Map();
  const transactionWithoutLearnerSet = new Set();

  transactions.forEach((transaction) => {
    const countryId = parseInt(transaction.country_id, 10);
    if (!Number.isInteger(countryId)) {
      transactionWithoutLearnerSet.add(transaction._id.toString());
      return;
    }
    const learnerObjectId =
      transaction?.learnerObjectId ??
      transaction?.subscriptionLearnerObjectId;
    if (!learnerObjectId) {
      transactionWithoutLearnerSet.add(transaction._id.toString());
      return;
    }
    const key = learnerObjectId.toString();
    if (!learnerPipelineMap.get(key)) {
      learnerPipelineMap.set(key, {
        updateOne: {
          filter: {
            _id: learnerObjectId,
          },
          update: {
            countryId,
          },
        },
      });
      const countryInfo =
        countryMappingMap.get(countryId) ??
        fallbackCountriesMap.get(countryId);
      membershipPipelineMap.set(key, {
        updateMany: {
          filter: { learnerObjectId },
          update: {
            countryInfo,
          },
        },
      });
    }
  });

  await LearnerModel.bulkWrite([...learnerPipelineMap.values()]);
  await MembershipModel.bulkWrite([...membershipPipelineMap.values()]);
};

const fixLearnerAndMembershipCountries = async (
  countryMappingMap,
  fallbackCountriesMap,
  communityObjectId = null
) => {
  const community = await CommunityModel.findById(communityObjectId, {
    code: 1,
  }).lean();
  const query = {
    community_code: community.code,
    'payment_details.complete_payment': 1,
  };
  const totalTransactions = await PurchaseTransactionModel.countDocuments(
    query
  );

  const rounds = Math.ceil(totalTransactions / BATCH_LIMIT_COMMUNITIES);

  for (let index = 0; index < rounds; index++) {
    // eslint-disable-next-line no-await-in-loop
    await updateLearnerAndMembershipFromTransactions(
      query,
      index,
      BATCH_LIMIT_COMMUNITIES,
      countryMappingMap,
      fallbackCountriesMap
    );
  }
};

const start = async () => {
  await mongoClient.connect();

  const totalCountries = await CountryCurrencyMappingModel.find({}).lean();

  const countryMappingBatches = [];
  let currentBatch = [];
  const fallbackCountriesMap = new Map();
  const countryMappingMap = new Map();

  totalCountries.forEach((country, index) => {
    const result = {
      id: country.countryId,
      code: country.countryCode,
      name: country.country,
    };
    countryMappingMap.set(country.countryId, result);
    currentBatch.push(result);
    if (country.fallbackCountryId) {
      fallbackCountriesMap.set(country.fallbackCountryId, result);
    }

    if (
      currentBatch.length === BATCH_LIMIT_COUNTRIES ||
      index === totalCountries.length - 1
    ) {
      countryMappingBatches.push(currentBatch);
      currentBatch = [];
    }
  });

  const communityObjectId = new ObjectId('634829ee766d6cac9dedbffc');

  await fixLearnerAndMembershipCountries(
    countryMappingMap,
    fallbackCountriesMap,
    communityObjectId
  );
  // Not needed as change stream will handle the analytics update via change stream on memebrships
  // await bulkUpdateCommunityCountry(
  //   countryMappingBatches,
  //   fallbackCountriesMap,
  //   communityObjectId
  // );
  console.log('Completed');
  process.exit(0);
};

start();
