/* eslint-disable no-await-in-loop */
/* eslint-disable no-continue */
require('dotenv').config();
const axios = require('axios');
const mongoClient = require('../src/mongoClient');
const mailContentModel = require('../src/models/notificationBackend/mailContent.model');
const backupSendGridTemplateModel = require('../src/models/notificationBackend/backupSendGridTemplate.model');
const {
  SENDGRID_TEMPLATE_SOURCES,
} = require('../src/models/notificationBackend/constants');
// const {
//   hasInternalDomain,
// } = require('../src/utils/checkInternalMember.util');

const sendGridAPI = 'https://api.sendgrid.com/v3';

// Configure these values
const mailTypes = [
  // 'MEMBER_COMMUNITY_ENROLMENT_WITHOUT_APPLICATION',
  // 'MEMBER_COMMUNITY_ENROLMENT_APPLICATION_APPROVED',
  // 'MEMBER_COMMUNITY_EVENT_REMINDER_24H',
  // 'MEMBER_COMMUNITY_EVENT_REMINDER_1H',
  // Add more mail types as needed
];

const customAndMktEmailToken = 'Bearer SG.your-custom-mkt-token';
const internalEmailToken = 'Bearer SG.your-internal-email-token';

/**
 * Fetch templates from a SendGrid account
 * @param {string} token - SendGrid API token
 * @returns {Promise<Array>} - List of templates
 */
async function fetchSendGridTemplates(token) {
  const { data } = await axios.get(`${sendGridAPI}/templates`, {
    params: { generations: 'dynamic', page_size: 200 },
    headers: { Authorization: token },
  });
  return data.result;
}

/**
 * Find mail content for a specific mail type
 * @param {string} mailType - Mail type to find
 * @returns {Promise<Object|null>} - Mail content document or null if not found
 */
async function findMailContent(mailType) {
  const mailContent = await mailContentModel
    .findOne({
      mailType,
      recordType: 'general',
    })
    .lean();

  return mailContent;
}

/**
 * Get template version details and content
 * @param {string} templateId - Template ID
 * @param {string} token - SendGrid API token
 * @returns {Promise<Object|null>} - Template content or null if no versions found
 */
async function getTemplateContent(templateId, token) {
  // Get template versions
  const { data: versionData } = await axios.get(
    `${sendGridAPI}/templates/${templateId}/versions`,
    { headers: { Authorization: token } }
  );

  if (!versionData.versions || versionData.versions.length === 0) {
    return null;
  }

  // Find active version or use the first one
  const activeVersion =
    versionData.versions.find((v) => v.active) || versionData.versions[0];

  // Get full template content
  const { data: templateContent } = await axios.get(
    `${sendGridAPI}/templates/${templateId}/versions/${activeVersion.id}`,
    { headers: { Authorization: token } }
  );

  return {
    activeVersion,
    templateContent,
  };
}

/**
 * Create a new template in the internal email account
 * @param {Object} templateData - Template data to create
 * @param {string} token - SendGrid API token
 * @returns {Promise<string>} - New template ID
 */
async function createInternalTemplate(templateData, token) {
  const { templateName, activeVersion, templateContent } = templateData;

  // Create new template
  const { data: newTemplate } = await axios.post(
    `${sendGridAPI}/templates`,
    {
      name: templateName,
      generation: 'dynamic',
    },
    { headers: { Authorization: token } }
  );

  // Create version for the new template
  await axios.post(
    `${sendGridAPI}/templates/${newTemplate.id}/versions`,
    {
      name: activeVersion.name,
      subject: templateContent.subject,
      html_content: templateContent.html_content,
      plain_content: templateContent.plain_content,
      active: 1,
    },
    { headers: { Authorization: token } }
  );

  return newTemplate.id;
}

/**
 * Update mail content with new template ID
 * @param {string} mailContentId - Mail content ID
 * @param {string} newTemplateId - New template ID
 * @returns {Promise<void>}
 */
async function updateMailContent(mailContentId, newTemplateId) {
  await mailContentModel.updateOne(
    { _id: mailContentId },
    {
      $set: {
        template: newTemplateId,
        isInternalEmail: true,
      },
    }
  );
}

/**
 * Update backup SendGrid template record
 * @param {string} mailType - Mail type
 * @param {string} oldTemplateId - Old template ID
 * @param {string} newTemplateId - New template ID
 * @returns {Promise<boolean>} - Whether a record was updated
 */
async function updateBackupSendGridTemplate(
  mailType,
  oldTemplateId,
  newTemplateId
) {
  const result = await backupSendGridTemplateModel.updateOne(
    {
      mailType,
      mainTemplateId: oldTemplateId,
    },
    {
      $set: {
        mainTemplateSource: SENDGRID_TEMPLATE_SOURCES.INTERNAL_EMAIL,
        mainTemplateId: newTemplateId,
      },
    }
  );

  return result.matchedCount > 0;
}

/**
 * Process a single mail type
 * @param {string} mailType - Mail type to process
 * @param {Object} customAndMktTemplatesById - Templates indexed by ID
 * @returns {Promise<void>}
 */
async function processMailType(mailType, customAndMktTemplatesById) {
  console.log(`\nProcessing mail type: ${mailType}`);

  // Find mail content for this mail type
  const mailContent = await findMailContent(mailType);

  if (!mailContent) {
    console.log(`No mail content found for ${mailType}, skipping`);
    return;
  }

  if (
    mailContent.mailContentSource !== 'SENDGRID' ||
    !mailContent.template
  ) {
    console.log(
      `Mail content for ${mailType} is not using SendGrid or has no template, skipping`
    );
    return;
  }

  const oldTemplateId = mailContent.template;
  const customAndMktTemplate = customAndMktTemplatesById[oldTemplateId];

  if (!customAndMktTemplate) {
    console.log(
      `Template ${oldTemplateId} not found in custom/marketing account, skipping`
    );
    return;
  }

  console.log(
    `Found template "${customAndMktTemplate.name}" (${oldTemplateId}) in custom/marketing account`
  );

  // Get template content
  console.log(`Getting template content...`);
  const templateVersionData = await getTemplateContent(
    oldTemplateId,
    customAndMktEmailToken
  );

  if (!templateVersionData) {
    console.log(
      `No versions found for template ${oldTemplateId}, skipping`
    );
    return;
  }

  // Create new template in internal account
  console.log(`Creating template in internal email account...`);
  const newTemplateId = await createInternalTemplate(
    {
      templateName: customAndMktTemplate.name,
      activeVersion: templateVersionData.activeVersion,
      templateContent: templateVersionData.templateContent,
    },
    internalEmailToken
  );

  console.log(`Created new template with ID: ${newTemplateId}`);

  // Update mail content with new template ID
  await updateMailContent(mailContent._id, newTemplateId);
  console.log(`Updated mail content with new template ID`);

  // Update backup SendGrid template record
  const backupUpdated = await updateBackupSendGridTemplate(
    mailType,
    oldTemplateId,
    newTemplateId
  );

  if (backupUpdated) {
    console.log(`Updated backup SendGrid template record`);
  } else {
    console.log(`No backup SendGrid template record found to update`);
  }
}

/**
 * Main function to migrate templates for internal emails
 */
const migrateTemplatesForInternalEmails = async () => {
  try {
    await mongoClient.connect();
    console.log('Connected to MongoDB');

    // 1. Fetch templates from both SendGrid accounts
    console.log('Fetching templates from SendGrid accounts...');
    const [internalEmailTemplates, customAndMktEmailTemplates] =
      await Promise.all([
        fetchSendGridTemplates(internalEmailToken),
        fetchSendGridTemplates(customAndMktEmailToken),
      ]);

    console.log(
      `Found ${internalEmailTemplates.length} templates in internal account`
    );
    console.log(
      `Found ${customAndMktEmailTemplates.length} templates in custom/marketing account`
    );

    // 2. Index templates by ID for easier lookup
    const customAndMktTemplatesById = {};
    customAndMktEmailTemplates.forEach((template) => {
      customAndMktTemplatesById[template.id] = template;
    });

    // 3. Process each mail type
    for (const mailType of mailTypes) {
      await processMailType(mailType, customAndMktTemplatesById);
    }

    console.log('\nTemplate migration completed successfully');
  } catch (error) {
    console.error('Error during template migration:', error);
  } finally {
    process.exit(0);
  }
};

migrateTemplatesForInternalEmails();
