require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const mongoClient = require('../src/mongoClient');
const CommunityModel = require('../src/communitiesAPI/models/community.model');
const LearnerModel = require('../src/models/learners.model');

const start = async () => {
  await mongoClient.connect();

  const referralCode = '';
  const communityCode = '';

  if (referralCode === '' || communityCode === '') {
    throw new Error('Invalid referral code or community code');
  }

  const learner = await LearnerModel.findOne(
    { referralCode },
    { email: 1 }
  ).lean();

  if (!learner) {
    throw new Error('Invalid referralCode');
  }

  await CommunityModel.findOneAndUpdate(
    { code: communityCode },
    {
      referralCodeUsed: referralCode,
      referrerEmail: learner.email,
      referrerObjectId: learner._id,
    },
    { new: true }
  ).lean();

  console.log('Completed');
  process.exit(0);
};

start();
