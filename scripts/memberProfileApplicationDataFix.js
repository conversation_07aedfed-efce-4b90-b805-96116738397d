require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});
// const ObjectId = require('mongoose').Types.ObjectId;

const fs = require('fs').promises;
const axios = require('axios');
const mongoClient = require('../src/mongoClient');
const Community = require('../src/communitiesAPI/models/community.model');
const {
  NEXT_JS_SECRET_TOKEN,
  NAS_IO_FRONTEND_URL,
} = require('../src/config');

const purgeFECache = async (results) => {
  const threshold = 50;
  const rounds = Math.ceil(results.length / threshold);
  const queries = [];
  for (let i = 0; i < rounds; i++) {
    const start = i * threshold;
    let end = start + threshold;
    if (end > results.length) end = results.length;
    queries.push(results.slice(start, end));
  }

  const errorLinks = [];

  for await (const communities of queries) {
    await Promise.all(
      communities.map(async (community) => {
        try {
          const pathToValidate = `${NAS_IO_FRONTEND_URL}/api/revalidate?path=${community.link}&secret=${NEXT_JS_SECRET_TOKEN}`;
          // eslint-disable-next-line no-unused-vars
          const response = await axios(pathToValidate);
        } catch (err) {
          console.log(
            `Something went wrong while revalidating landing page ${community.link}`
          );
          errorLinks.push(community.link);
        }
      })
    );
  }
  console.log(errorLinks);
  await fs.writeFile('errors.csv', errorLinks, 'utf-8');
};

const run = async () => {
  await mongoClient.connect();
  // Can run anytime, ideally before launch
  await Community.updateMany(
    {
      //   _id: new ObjectId('626b2628674ff457a1ff7cec'),
      request_approval: false,
      'applicationConfigDataFields.isDeleted': false,
    },
    {
      $set: {
        'applicationConfigDataFields.$[element].isDeleted': true,
        'applicationConfigDataFields.$[element].notes':
          'soft deleted from script',
      },
    },
    { arrayFilters: [{ 'element.isDeleted': false }] }
  );

  const results = await Community.find({
    'applicationConfigDataFields.notes': 'soft deleted from script',
  })
    .select('link')
    .lean();

  await purgeFECache(results);

  // To be run only after launch
  //   await Community.updateMany(
  //     { request_approval: true, 'applicationConfig.autoApproval': true },
  //     {
  //       $set: {
  //         request_approval: false,
  //         notes: 'request_approval adjusted from script',
  //       },
  //       $unset: {
  //         'applicationConfig.autoApproval': 1,
  //       },
  //     }
  //   );

  //   const results = await Community.find({
  //     isActive: true,
  //     notes: 'request_approval adjusted from script',
  //   })
  //     .select('link')
  //     .lean();

  //   await purgeFECache(results);
  console.log('Completed');
  process.exit(0);
};

run();
