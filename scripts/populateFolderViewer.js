require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const mongoClient = require('../src/mongoClient');
const FolderViewersModel = require('../src/models/product/folderViewers.model');
const FolderAccessLogsModel = require('../src/communitiesAPI/models/communityFolderAccessLogs.model');
const RevenueTransactionModel = require('../src/models/revenueTransaction.model');
const AddonTransactionModel = require('../src/communitiesAPI/models/communityAddonTransactions.model');
const {
  FOLDER_VIEWER_STATUS,
} = require('../src/services/folder/constants');

const THRESHOLD = 1000;

async function getFolderAcessLogsUpdateQueries() {
  const folderAccessLogs = await FolderAccessLogsModel.aggregate([
    {
      $sort: {
        communityFolderObjectId: 1,
        learnerObjectId: 1,
        createdAt: 1,
      },
    },
    {
      $group: {
        _id: {
          learnerObjectId: '$learnerObjectId',
          folderObjectId: '$communityFolderObjectId',
        },
        createdAt: {
          $first: '$createdAt',
        },
      },
    },
    {
      $group: {
        _id: {
          folderObjectId: '$_id.folderObjectId',
        },
        access: {
          $push: {
            learnerObjectId: '$_id.learnerObjectId',
            createdAt: '$createdAt',
          },
        },
      },
    },
  ]);

  const results = [];

  folderAccessLogs.forEach((log) => {
    const status = FOLDER_VIEWER_STATUS.FREE;
    const folderObjectId = log._id.folderObjectId;
    log.access.forEach((access) =>
      results.push({
        updateOne: {
          filter: {
            folderObjectId,
            learnerObjectId: access.learnerObjectId,
          },
          update: {
            accessDate: access.createdAt,
            status,
          },
          upsert: true,
        },
      })
    );
  });

  return results;
}

async function getPaidAndRefundFolderViewerQuery() {
  const revenueTransactions = await RevenueTransactionModel.aggregate([
    {
      $match: {
        transactionType: 'INBOUND',
        purchaseType: 'FOLDER',
      },
    },
    {
      $lookup: {
        from: 'community_addon_transactions',
        localField: 'purchasedId',
        foreignField: '_id',
        as: 'transaction',
      },
    },
    {
      $unwind: {
        path: '$transaction',
        preserveNullAndEmptyArrays: false,
      },
    },
    {
      $lookup: {
        from: 'revenue_transactions',
        localField: 'refundedTransactionReferenceId',
        foreignField: 'transactionReferenceId',
        as: 'refund',
      },
    },
    {
      $unwind: {
        path: '$refund',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $project: {
        purchasedId: 1,
        learnerObjectId: 1,
        originalPaidAmount: 1,
        originalCurrency: 1,
        transactionCreatedAt: 1,
        folderObjectId: '$transaction.entityObjectId',
        refundTransactionCreatedAt: '$refund.transactionCreatedAt',
      },
    },
  ]);

  const results = revenueTransactions.map((transaction) => {
    let status = FOLDER_VIEWER_STATUS.PAID;
    if (transaction.refundTransactionCreatedAt) {
      status = FOLDER_VIEWER_STATUS.REFUNDED;
    }
    return {
      updateOne: {
        filter: {
          folderObjectId: transaction.folderObjectId,
          learnerObjectId: transaction.learnerObjectId,
        },
        update: {
          status,
          revenueTransactionObjectId: transaction._id,
          addonTransactionObjectId: transaction.purchasedId,
          purchaseDate: transaction.transactionCreatedAt,
          refundDate: transaction.refundTransactionCreatedAt,
          localAmount: transaction.originalPaidAmount,
          localCurrency: transaction.originalCurrency,
          amountInUsd: transaction.amountInUsd,
        },
        upsert: true,
      },
    };
  });

  return results;
}

async function getFullDiscountFolderViewerQuery() {
  const addonTransactions = await AddonTransactionModel.aggregate([
    {
      $match: {
        entityCollection: 'community_folders',
        'payment_details.status': 'success',
        amount: 0,
        applyDiscount: true,
      },
    },
  ]);

  const results = addonTransactions.map((addon) => {
    let status = FOLDER_VIEWER_STATUS.PAID;

    return {
      updateOne: {
        filter: {
          folderObjectId: addon.entityObjectId,
          learnerObjectId: addon.learnerObjectId,
        },
        update: {
          status,
          addonTransactionObjectId: addon._id,
          purchaseDate: addon.updatedAt,
          localAmount: addon.local_amount,
          localCurrency: addon.local_currency,
          amountInUsd: addon.amount,
        },
        upsert: true,
      },
    };
  });

  return results;
}

async function populateFolderViewer(results) {
  const rounds = Math.ceil(results.length / THRESHOLD);
  const queries = [];
  for (let i = 0; i < rounds; i++) {
    const start = i * THRESHOLD;
    let end = start + THRESHOLD;
    if (end > results.length) end = results.length;
    queries.push({ start, end });
  }

  await Promise.all(
    queries.map(async ({ start, end }) => {
      await FolderViewersModel.bulkWrite(results.slice(start, end));
    })
  );
}

const start = async () => {
  await mongoClient.connect();

  const [folderAccessQuery, paidAndRefundQuery, fullDiscountQuery] =
    await Promise.all([
      getFolderAcessLogsUpdateQueries(),
      getPaidAndRefundFolderViewerQuery(),
      getFullDiscountFolderViewerQuery(),
    ]);

  await populateFolderViewer(folderAccessQuery);

  await Promise.all([
    populateFolderViewer(paidAndRefundQuery),
    populateFolderViewer(fullDiscountQuery),
  ]);

  console.log('Completed');
  process.exit(0);
};

start();
