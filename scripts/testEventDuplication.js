const fs = require('fs');
const {
  generateDuplicateEvents,
} = require('../src/utils/events/generateDuplicateEvents.util');

const testEvent = {
  _id: '67ca62e48be8930170cec882',
  title: 'test',
  description: 'test',
  startTime: '2025-03-08T10:00:00Z',
  endTime: '2025-03-08T11:00:00Z',
  timeBeforeStartTime: [
    {
      type: 'FIVE_MINS',
      date: {
        $date: '2025-03-08T03:55:00.000Z',
      },
    },
    {
      type: 'ONE_HOUR',
      date: {
        $date: '2025-03-08T03:00:00.000Z',
      },
    },
    {
      type: 'ONE_DAY',
      date: {
        $date: '2025-03-07T04:00:00.000Z',
      },
    },
  ],
  customDateText: null,
  liveLink: '',
  inPersonLocation: '',
  hideLocation: true,
  isActive: true,
  status: 'published',
  type: 'live',
  communities: ['67c99d53b8b20cc5437d66d1'],
  host: {
    profileImage:
      'https://d2oi1rqwb0pj00.cloudfront.net/user/nio_1733725557112',
    firstName: 'Pikachu',
    lastName: '',
    role: ['member', 'admin', 'manager', 'owner'],
  },
  resources: [],
  chatGroupLink: '',
  slug: '/test',
  bannerImg:
    'https://d2oi1rqwb0pj00.cloudfront.net/nasIO/community-events/event-cover-image-1.png',
  requiresApproval: false,
  applicationConfigDataFields: [
    {
      label: '2122',
      fieldName: 't2E1h5rQjHp_IVXKSZFTc',
      fieldDataType: 'text',
      inputSectionKey: 'text',
      isEditable: false,
      isRequired: false,
      isVisible: true,
      isDisabled: false,
      placeholder: '',
      options: [],
      isSunlightUrl: false,
      isDeleted: false,
    },
  ],
  access: 'paid',
  amount: 100,
  currency: 'SGD',
  paymentMethods: [
    {
      value: 'stripe',
      label: 'stripe',
      icon: 'https://d2oi1rqwb0pj00.cloudfront.net/na-website/Payment/svg/bank.svg',
    },
  ],
  isDemo: false,
  isSoldOut: false,
  createdByLearnerObjectId: '6412da5b7e037c1bb8ec4fb9',
  hideAttendeesCount: false,
  discountsApplied: [],
  attendeeLimit: 100,
  timezoneId: 'Asia/Singapore',
  isCapacitySet: false,
  maxQuantityPerPurchase: 20,
  bulkPurchaseEnabled: true,
  pricingConfig: {
    priceType: 'FIXED',
  },
  countryWisePrice: [
    {
      country: 'Singapore',
      currency: 'SGD',
      amount: null,
      localiseBasePrice: true,
    },
    {
      country: 'India',
      currency: 'INR',
      amount: null,
      localiseBasePrice: true,
    },
    {
      country: 'Philippines',
      currency: 'PHP',
      amount: null,
      localiseBasePrice: true,
    },
    {
      country: 'Indonesia',
      currency: 'IDR',
      amount: null,
      localiseBasePrice: true,
    },
  ],
  createdAt: '2025-03-07T03:07:16.548Z',
  lastModifiedTimeStamp: '2025-03-07T03:08:49.340Z',
  eventId: 8514,
  icsFileLink: 'https://dev-nas.io/ce_67ca62e48be8930170cec882',
  shortUrl: 'https://dev-nas.io/test-get-inspired/ogjl',
};

const testCases = {
  // Simple frequency - Daily
  dailyEveryDay: {
    duplicationCriteria: {
      minStartDate: '2025-06-01T00:00:00Z',
      isCustomFrequency: false,
      frequency: 'daily',
      frequencyInterval: 1,
      endCondition: 'occurrence',
      maxOccurrences: 5,
    },
    // output verified
    expectedOutput: [
      {
        title: 'test',
        startTime: '2025-06-01T10:00:00.000Z',
        endTime: '2025-06-01T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-02T10:00:00.000Z',
        endTime: '2025-06-02T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-03T10:00:00.000Z',
        endTime: '2025-06-03T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-04T10:00:00.000Z',
        endTime: '2025-06-04T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-05T10:00:00.000Z',
        endTime: '2025-06-05T11:00:00.000Z',
      },
    ],
  },
  // Simple frequency - Every 2 days
  dailyEveryTwoDays: {
    duplicationCriteria: {
      minStartDate: '2025-06-01T00:00:00Z',
      isCustomFrequency: false,
      frequency: 'daily',
      frequencyInterval: 2,
      endCondition: 'specificDate',
      endDate: '2025-07-10T10:00:00Z',
    },
    // output verified
    expectedOutput: [
      {
        title: 'test',
        startTime: '2025-06-01T10:00:00.000Z',
        endTime: '2025-06-01T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-03T10:00:00.000Z',
        endTime: '2025-06-03T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-05T10:00:00.000Z',
        endTime: '2025-06-05T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-07T10:00:00.000Z',
        endTime: '2025-06-07T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-09T10:00:00.000Z',
        endTime: '2025-06-09T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-11T10:00:00.000Z',
        endTime: '2025-06-11T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-13T10:00:00.000Z',
        endTime: '2025-06-13T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-15T10:00:00.000Z',
        endTime: '2025-06-15T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-17T10:00:00.000Z',
        endTime: '2025-06-17T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-19T10:00:00.000Z',
        endTime: '2025-06-19T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-21T10:00:00.000Z',
        endTime: '2025-06-21T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-23T10:00:00.000Z',
        endTime: '2025-06-23T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-25T10:00:00.000Z',
        endTime: '2025-06-25T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-27T10:00:00.000Z',
        endTime: '2025-06-27T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-29T10:00:00.000Z',
        endTime: '2025-06-29T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-07-01T10:00:00.000Z',
        endTime: '2025-07-01T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-07-03T10:00:00.000Z',
        endTime: '2025-07-03T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-07-05T10:00:00.000Z',
        endTime: '2025-07-05T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-07-07T10:00:00.000Z',
        endTime: '2025-07-07T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-07-09T10:00:00.000Z',
        endTime: '2025-07-09T11:00:00.000Z',
      },
    ],
  },
  // Simple frequency - Weekly
  weeklyEveryWeek: {
    duplicationCriteria: {
      minStartDate: '2025-06-01T00:00:00Z',
      isCustomFrequency: false,
      frequency: 'weekly',
      frequencyInterval: 1,
      endCondition: 'occurrence',
      maxOccurrences: 4,
    },
    // output verified
    expectedOutput: [
      {
        title: 'test',
        startTime: '2025-06-01T10:00:00.000Z',
        endTime: '2025-06-01T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-08T10:00:00.000Z',
        endTime: '2025-06-08T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-15T10:00:00.000Z',
        endTime: '2025-06-15T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-22T10:00:00.000Z',
        endTime: '2025-06-22T11:00:00.000Z',
      },
    ],
  },
  // Simple frequency - Monthly
  monthlyEveryMonthSmall: {
    duplicationCriteria: {
      minStartDate: '2025-06-01T00:00:00Z',
      isCustomFrequency: false,
      frequency: 'monthly',
      frequencyInterval: 1,
      endCondition: 'specificDate',
      endDate: '2025-08-01T10:00:00Z',
    },
    // output verified
    expectedOutput: [
      {
        title: 'test',
        startTime: '2025-06-01T10:00:00.000Z',
        endTime: '2025-06-01T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-07-01T10:00:00.000Z',
        endTime: '2025-07-01T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-08-01T10:00:00.000Z',
        endTime: '2025-08-01T11:00:00.000Z',
      },
    ],
  },
  // Simple frequency - Monthly
  monthlyEveryMonth: {
    duplicationCriteria: {
      minStartDate: '2025-06-01T00:00:00Z',
      isCustomFrequency: false,
      frequency: 'monthly',
      frequencyInterval: 1,
      endCondition: 'specificDate',
      endDate: '2026-06-01T10:00:00Z',
    },
    // output verified
    expectedOutput: [
      {
        title: 'test',
        startTime: '2025-06-01T10:00:00.000Z',
        endTime: '2025-06-01T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-07-01T10:00:00.000Z',
        endTime: '2025-07-01T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-08-01T10:00:00.000Z',
        endTime: '2025-08-01T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-09-01T10:00:00.000Z',
        endTime: '2025-09-01T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-10-01T10:00:00.000Z',
        endTime: '2025-10-01T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-11-01T10:00:00.000Z',
        endTime: '2025-11-01T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-12-01T10:00:00.000Z',
        endTime: '2025-12-01T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2026-01-01T10:00:00.000Z',
        endTime: '2026-01-01T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2026-02-01T10:00:00.000Z',
        endTime: '2026-02-01T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2026-03-01T10:00:00.000Z',
        endTime: '2026-03-01T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2026-04-01T10:00:00.000Z',
        endTime: '2026-04-01T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2026-05-01T10:00:00.000Z',
        endTime: '2026-05-01T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2026-06-01T10:00:00.000Z',
        endTime: '2026-06-01T11:00:00.000Z',
      },
    ],
  },

  // Simple frequency - Monthly on 31st
  monthlyEveryMonth31st: {
    duplicationCriteria: {
      minStartDate: '2025-07-31T00:00:00Z',
      isCustomFrequency: false,
      frequency: 'monthly',
      frequencyInterval: 1,
      endCondition: 'occurrence',
      maxOccurrences: 6,
    },
    // output verified
    expectedOutput: [
      {
        title: 'test',
        startTime: '2025-07-31T10:00:00.000Z',
        endTime: '2025-07-31T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-08-31T10:00:00.000Z',
        endTime: '2025-08-31T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-10-31T10:00:00.000Z',
        endTime: '2025-10-31T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-12-31T10:00:00.000Z',
        endTime: '2025-12-31T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2026-01-31T10:00:00.000Z',
        endTime: '2026-01-31T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2026-03-31T10:00:00.000Z',
        endTime: '2026-03-31T11:00:00.000Z',
      },
    ],
  },

  // Custom frequency - Weekly (Mon, Wed, Fri)
  customWeeklySpecificDays: {
    duplicationCriteria: {
      minStartDate: '2025-06-01T00:00:00Z',
      isCustomFrequency: true,
      frequency: 'weekly',
      frequencyInterval: 1,
      // daysOfWeek: [0, 2, 4], // Monday, Wednesday, Friday
      daysOfWeek: [1, 3, 5], // Monday, Wednesday, Friday
      endCondition: 'occurrence',
      maxOccurrences: 10,
    },
    // output verified
    expectedOutput: [
      {
        title: 'test',
        startTime: '2025-06-02T10:00:00.000Z',
        endTime: '2025-06-02T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-04T10:00:00.000Z',
        endTime: '2025-06-04T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-06T10:00:00.000Z',
        endTime: '2025-06-06T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-09T10:00:00.000Z',
        endTime: '2025-06-09T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-11T10:00:00.000Z',
        endTime: '2025-06-11T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-13T10:00:00.000Z',
        endTime: '2025-06-13T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-16T10:00:00.000Z',
        endTime: '2025-06-16T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-18T10:00:00.000Z',
        endTime: '2025-06-18T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-20T10:00:00.000Z',
        endTime: '2025-06-20T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-23T10:00:00.000Z',
        endTime: '2025-06-23T11:00:00.000Z',
      },
    ],
  },
  // Custom frequency - Every 2 weeks (Wed)
  customEvery2WeeksSpecificDays: {
    duplicationCriteria: {
      minStartDate: '2025-06-03T00:00:00Z',
      isCustomFrequency: true,
      frequency: 'weekly',
      frequencyInterval: 2,
      daysOfWeek: [3], // Monday, Wednesday, Friday
      endCondition: 'occurrence',
      maxOccurrences: 4,
    },
    // output verified
    expectedOutput: [
      {
        title: 'test',
        startTime: '2025-06-04T10:00:00.000Z',
        endTime: '2025-06-04T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-18T10:00:00.000Z',
        endTime: '2025-06-18T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-07-02T10:00:00.000Z',
        endTime: '2025-07-02T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-07-16T10:00:00.000Z',
        endTime: '2025-07-16T11:00:00.000Z',
      },
    ],
  },
  // Custom frequency - Monthly (every first Monday)
  customMonthlyFirstMonday: {
    duplicationCriteria: {
      minStartDate: '2025-06-01T00:00:00Z',
      isCustomFrequency: true,
      frequency: 'monthly',
      frequencyInterval: 1,
      customMonthRules: {
        type: 'nth_day_of_week',
        // day: [0], // Monday
        day: [1], // Monday
        nth: 1, // First occurrence
      },
      endCondition: 'occurrence',
      maxOccurrences: 6,
    },
    // output verified
    expectedOutput: [
      {
        title: 'test',
        startTime: '2025-06-02T10:00:00.000Z',
        endTime: '2025-06-02T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-07-07T10:00:00.000Z',
        endTime: '2025-07-07T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-08-04T10:00:00.000Z',
        endTime: '2025-08-04T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-09-01T10:00:00.000Z',
        endTime: '2025-09-01T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-10-06T10:00:00.000Z',
        endTime: '2025-10-06T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-11-03T10:00:00.000Z',
        endTime: '2025-11-03T11:00:00.000Z',
      },
    ],
  },
  // Custom frequency - every 2 months (first Monday)
  customMonthlyFirstMondayEvery2Months: {
    duplicationCriteria: {
      minStartDate: '2025-06-01T00:00:00Z',
      isCustomFrequency: true,
      frequency: 'monthly',
      frequencyInterval: 2,
      customMonthRules: {
        type: 'nth_day_of_week',
        // day: [0], // Monday
        day: [1], // Monday
        nth: 1, // First occurrence
      },
      endCondition: 'occurrence',
      maxOccurrences: 3,
    },
    // output verified
    expectedOutput: [
      {
        title: 'test',
        startTime: '2025-06-02T10:00:00.000Z',
        endTime: '2025-06-02T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-08-04T10:00:00.000Z',
        endTime: '2025-08-04T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-10-06T10:00:00.000Z',
        endTime: '2025-10-06T11:00:00.000Z',
      },
    ],
  },
  // Custom frequency - Monthly (every last Sunday)
  customMonthlyLastSunday: {
    duplicationCriteria: {
      minStartDate: '2025-06-01T00:00:00Z',
      isCustomFrequency: true,
      frequency: 'monthly',
      frequencyInterval: 1,
      customMonthRules: {
        type: 'nth_day_of_week',
        // day: [6], // Sunday
        day: [7], // Sunday
        nth: -1, // Last occurrence
      },
      endCondition: 'specificDate',
      endDate: '2025-09-30T10:00:00Z',
    },
    // output verified
    expectedOutput: [
      {
        title: 'test',
        startTime: '2025-06-29T10:00:00.000Z',
        endTime: '2025-06-29T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-07-27T10:00:00.000Z',
        endTime: '2025-07-27T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-08-31T10:00:00.000Z',
        endTime: '2025-08-31T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-09-28T10:00:00.000Z',
        endTime: '2025-09-28T11:00:00.000Z',
      },
    ],
  },
  // Custom frequency - Monthly (specific dates)
  customMonthlySpecificDates: {
    duplicationCriteria: {
      minStartDate: '2025-06-01T00:00:00Z',
      isCustomFrequency: true,
      frequency: 'monthly',
      frequencyInterval: 1,
      customMonthRules: {
        type: 'specific_days_of_month',
        days: [1, 15, 30], // 1st, 15th and 30th of each month
      },
      endCondition: 'occurrence',
      maxOccurrences: 6,
    },
    // Output verified.
    expectedOutput: [
      {
        title: 'test',
        startTime: '2025-06-01T10:00:00.000Z',
        endTime: '2025-06-01T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-15T10:00:00.000Z',
        endTime: '2025-06-15T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-30T10:00:00.000Z',
        endTime: '2025-06-30T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-07-01T10:00:00.000Z',
        endTime: '2025-07-01T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-07-15T10:00:00.000Z',
        endTime: '2025-07-15T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-07-30T10:00:00.000Z',
        endTime: '2025-07-30T11:00:00.000Z',
      },
    ],
  },
  // Custom frequency - every 2 months (specific dates)
  customEvery2MonthsSpecificDates: {
    duplicationCriteria: {
      minStartDate: '2025-06-01T00:00:00Z',
      isCustomFrequency: true,
      frequency: 'monthly',
      frequencyInterval: 2,
      customMonthRules: {
        type: 'specific_days_of_month',
        days: [1, 15, 30], // 1st, 15th and 30th of each month
      },
      endCondition: 'occurrence',
      maxOccurrences: 6,
    },
    // Output verified.
    expectedOutput: [
      {
        title: 'test',
        startTime: '2025-06-01T10:00:00.000Z',
        endTime: '2025-06-01T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-15T10:00:00.000Z',
        endTime: '2025-06-15T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-30T10:00:00.000Z',
        endTime: '2025-06-30T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-08-01T10:00:00.000Z',
        endTime: '2025-08-01T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-08-15T10:00:00.000Z',
        endTime: '2025-08-15T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-08-30T10:00:00.000Z',
        endTime: '2025-08-30T11:00:00.000Z',
      },
    ],
  },
  // Custom frequency - every 2 months (specific dates)
  customEvery2MonthsSpecificDatesWith31: {
    duplicationCriteria: {
      minStartDate: '2025-06-01T00:00:00Z',
      isCustomFrequency: true,
      frequency: 'monthly',
      frequencyInterval: 2,
      customMonthRules: {
        type: 'specific_days_of_month',
        days: [15, 31], // 1st, 15th and 30th of each month
      },
      endCondition: 'occurrence',
      maxOccurrences: 4,
    },
    // Output verified.
    expectedOutput: [
      {
        title: 'test',
        startTime: '2025-06-15T10:00:00.000Z',
        endTime: '2025-06-15T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-08-15T10:00:00.000Z',
        endTime: '2025-08-15T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-08-31T10:00:00.000Z',
        endTime: '2025-08-31T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-10-15T10:00:00.000Z',
        endTime: '2025-10-15T11:00:00.000Z',
      },
    ],
  },
  // Edge case - Max events limit test
  maxEventsTest: {
    duplicationCriteria: {
      minStartDate: '2025-06-01T00:00:00Z',
      isCustomFrequency: false,
      frequency: 'daily',
      frequencyInterval: 1,
      endCondition: 'occurrence',
      maxOccurrences: 50, // Should still only generate 30 events
    },
    // output verified
    expectedOutput: [
      {
        title: 'test',
        startTime: '2025-06-01T10:00:00.000Z',
        endTime: '2025-06-01T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-02T10:00:00.000Z',
        endTime: '2025-06-02T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-03T10:00:00.000Z',
        endTime: '2025-06-03T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-04T10:00:00.000Z',
        endTime: '2025-06-04T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-05T10:00:00.000Z',
        endTime: '2025-06-05T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-06T10:00:00.000Z',
        endTime: '2025-06-06T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-07T10:00:00.000Z',
        endTime: '2025-06-07T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-08T10:00:00.000Z',
        endTime: '2025-06-08T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-09T10:00:00.000Z',
        endTime: '2025-06-09T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-10T10:00:00.000Z',
        endTime: '2025-06-10T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-11T10:00:00.000Z',
        endTime: '2025-06-11T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-12T10:00:00.000Z',
        endTime: '2025-06-12T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-13T10:00:00.000Z',
        endTime: '2025-06-13T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-14T10:00:00.000Z',
        endTime: '2025-06-14T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-15T10:00:00.000Z',
        endTime: '2025-06-15T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-16T10:00:00.000Z',
        endTime: '2025-06-16T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-17T10:00:00.000Z',
        endTime: '2025-06-17T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-18T10:00:00.000Z',
        endTime: '2025-06-18T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-19T10:00:00.000Z',
        endTime: '2025-06-19T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-20T10:00:00.000Z',
        endTime: '2025-06-20T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-21T10:00:00.000Z',
        endTime: '2025-06-21T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-22T10:00:00.000Z',
        endTime: '2025-06-22T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-23T10:00:00.000Z',
        endTime: '2025-06-23T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-24T10:00:00.000Z',
        endTime: '2025-06-24T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-25T10:00:00.000Z',
        endTime: '2025-06-25T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-26T10:00:00.000Z',
        endTime: '2025-06-26T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-27T10:00:00.000Z',
        endTime: '2025-06-27T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-28T10:00:00.000Z',
        endTime: '2025-06-28T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-29T10:00:00.000Z',
        endTime: '2025-06-29T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-30T10:00:00.000Z',
        endTime: '2025-06-30T11:00:00.000Z',
      },
    ],
  },

  // end condition test
  endDateTest: {
    duplicationCriteria: {
      minStartDate: '2025-06-01T00:00:00Z',
      isCustomFrequency: false,
      frequency: 'daily',
      frequencyInterval: 1,
      endCondition: 'specificDate',
      endDate: '2025-06-05T23:59:59Z',
    },
    // Output verified.
    expectedOutput: [
      {
        title: 'test',
        startTime: '2025-06-01T10:00:00.000Z',
        endTime: '2025-06-01T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-02T10:00:00.000Z',
        endTime: '2025-06-02T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-03T10:00:00.000Z',
        endTime: '2025-06-03T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-04T10:00:00.000Z',
        endTime: '2025-06-04T11:00:00.000Z',
      },
      {
        title: 'test',
        startTime: '2025-06-05T10:00:00.000Z',
        endTime: '2025-06-05T11:00:00.000Z',
      },
    ],
  },
};

const OUTPUT_FILE = `./scripts/output/testEventDuplication.output.txt`;

const writeToFile = (data) => {
  fs.writeFileSync(OUTPUT_FILE, data + '\n', {
    flag: 'a',
  });
  console.log(data);
};

// Helper function to clean event for display
const cleanEventForDisplay = (event) => {
  return {
    title: event.title,
    startTime: event.startTime,
    endTime: event.endTime,
  };
};

// Function to run test for a single case
function runTest(testName, recurringCriteria) {
  writeToFile(`\n=== Testing: ${testName} ===`);
  writeToFile(`Criteria: ${JSON.stringify(recurringCriteria, null, 2)}`);

  const { events: generatedEvents } = generateDuplicateEvents(
    testEvent,
    recurringCriteria
  );

  writeToFile(`Generated ${generatedEvents.length} events:`);
  writeToFile(
    JSON.stringify(
      generatedEvents.map((x) => cleanEventForDisplay(x)),
      null,
      2
    )
  );

  return generatedEvents;
}

// Run all tests
function runAllTests() {
  writeToFile(`\n\n\n\n=== Test Run at ${new Date().toISOString()} ===`);
  writeToFile('=== Event Duplication Test ===');
  writeToFile('Event');
  writeToFile(JSON.stringify(testEvent, null, 2));

  const results = {};

  for (const [testName, { duplicationCriteria }] of Object.entries(
    testCases
  )) {
    results[testName] = runTest(testName, duplicationCriteria);
  }

  // Print summary
  writeToFile('\n=== Summary ===');
  Object.entries(results).forEach(([testName, events]) => {
    writeToFile(`${testName}: Generated ${events.length} events`);
  });

  // Print test results
  let passCount = 0;
  let failCount = 0;
  const failedTestNames = [];

  writeToFile('\n=== Test Results ===');
  Object.entries(testCases).forEach(([testName, { expectedOutput }]) => {
    const generatedEvents = results[testName];
    // test length of the output and the startTime and endTime for each row.
    // If ok, write to file [PASS] ...
    // If failed, write to file [Fail] ...

    if (generatedEvents.length !== expectedOutput.length) {
      writeToFile(
        `[FAIL] ${testName}: Expected ${expectedOutput.length} events, but got ${generatedEvents.length} events`
      );
      failCount++;
      failedTestNames.push(testName);
    } else {
      let passed = true;
      for (let i = 0; i < generatedEvents.length; i++) {
        if (
          generatedEvents[i].startTime !== expectedOutput[i].startTime ||
          generatedEvents[i].endTime !== expectedOutput[i].endTime
        ) {
          writeToFile(
            `[FAIL] ${testName}: Expected event ${
              i + 1
            } to have startTime ${
              expectedOutput[i].startTime
            } and endTime ${
              expectedOutput[i].endTime
            }, but got startTime ${
              generatedEvents[i].startTime
            } and endTime ${generatedEvents[i].endTime}`
          );
          failCount++;
          passed = false;
          failedTestNames.push(testName);
          break;
        }
      }

      if (passed) {
        writeToFile(
          `[PASS] ${testName}: All events match expected output`
        );
        passCount++;
      }
    }
  });

  if (failCount > 0) {
    // print output of each failed test
    writeToFile(`\n=== Failed Tests ===`);
    failedTestNames.forEach((testName) => {
      writeToFile(`\n=== ${testName} ===`);
      // write testing criteria
      writeToFile(
        `Criteria: ${JSON.stringify(
          testCases[testName].duplicationCriteria,
          null,
          2
        )}`
      );
      writeToFile(
        JSON.stringify(
          results[testName].map((x) => cleanEventForDisplay(x)),
          null,
          2
        )
      );
    });
  }

  writeToFile('\n=== Test Results Summary ===');
  writeToFile(`Total tests: ${Object.keys(testCases).length}`);
  writeToFile(`Passed: ${passCount}`);
  writeToFile(`Failed: ${failCount}`);
}

// Execute tests
runAllTests();
