require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const fs = require('fs').promises;

const mongoClient = require('../src/mongoClient');

const CommunityModel = require('../src/communitiesAPI/models/community.model');
const EventModel = require('../src/communitiesAPI/models/communityEvents.model');
const EventAttendeeModel = require('../src/communitiesAPI/models/eventAttendees.model');
const SubscriptionModel = require('../src/communitiesAPI/models/communitySubscriptions.model');
const UserModel = require('../src/models/users.model');
const LearnerModel = require('../src/models/learners.model');
const communityEventsService = require('../src/communitiesAPI/services/web/communityEvents.service');

const {
  splitFullNameIntoFirstAndLastName,
} = require('../src/utils/name.util');
const {
  COMMUNITY_SUBSCRIPTION_STATUSES,
} = require('../src/constants/common');

const start = async () => {
  await mongoClient.connect();

  const communityObjectId = '66ecf7dc4befa28d16ab4d79';
  const eventObjectId = '66ecf90768fe58144f28a302';

  const dataImports = ['Magda Carolina Acosta,<EMAIL>'];

  const [community, event] = await Promise.all([
    CommunityModel.findById(communityObjectId).lean(),
    EventModel.findById(eventObjectId).lean(),
  ]);

  if (!community) {
    throw new Error('Community not found');
  }

  if (!event) {
    throw new Error('Event not found');
  }

  await Promise.all(
    dataImports.map(async (data) => {
      const [fullName, dataEmail] = data.split(',');

      const email = dataEmail.trim().toLowerCase();

      const name = splitFullNameIntoFirstAndLastName(fullName);

      let [user, learner, subscription, eventAttendee] = await Promise.all(
        [
          UserModel.findOne({ email, isActive: true }).lean(),
          LearnerModel.findOne({
            email,
            isActive: true,
          }).lean(),
          SubscriptionModel.findOne({
            email,
            communityCode: community.code,
            status: COMMUNITY_SUBSCRIPTION_STATUSES.CURRENT,
          }).lean(),
          EventAttendeeModel.findOne({
            email,
            eventObjectId,
            communityObjectId,
          }).lean(),
        ]
      );

      const createLearner = !learner;
      const createUser = !user;
      const createSubscription = !subscription;
      const createEventAttendee = !eventAttendee;

      if (!learner) {
        const learnerInfo = {
          email,
          firstName: name?.firstName,
          lastName: name?.lastName,
          isActive: true,
          profileImage:
            'https://d2yjtdaqamc55g.cloudfront.net/default-user-image-red.jpg',
        };

        learner = (await LearnerModel.create(learnerInfo)).toObject();
      }

      if (!user) {
        const userInfo = {
          email,
          user_id: learner.learnerId,
          learner: learner._id,
          learner_role: true,
          community_admin: false,
          instructor_role: false,
          admin_role: false,
          creator: null,
          trainer: null,
          status: 'PENDING',
          isActive: true,
          accessMethods: [
            {
              type: 'password',
              id: '5119909',
            },
          ],
          creator_role: false,
          deviceTokens: [],
        };

        user = (await UserModel.create(userInfo)).toObject();
      }

      if (!subscription) {
        const subscriptionInfo = {
          email,
          communityCode: community.code,
          learnerId: learner.learnerId,
          learnerObjectId: learner._id,
          status: COMMUNITY_SUBSCRIPTION_STATUSES.CURRENT,
          createdBy: '<EMAIL>',
          memberType: 'free',
        };

        subscription = (
          await SubscriptionModel.create(subscriptionInfo)
        ).toObject();
      }

      if (!eventAttendee) {
        await communityEventsService.autoRegisterEvent({
          learnerObjectId: learner._id,
          eventObjectId,
          addonTransactionObjectId: null,
          quantity: 1,
        });
      }

      const message = `${email},${community.code},${eventObjectId},${createLearner},${createUser},${createSubscription},${createEventAttendee}`;

      await fs.appendFile(
        'logs/populate_user_learner_subscription_eventAttendee.log',
        `${message}\n`
      );

      console.log(`${message}`);
    })
  );

  console.log('Completed');
  process.exit(0);
};

start();
