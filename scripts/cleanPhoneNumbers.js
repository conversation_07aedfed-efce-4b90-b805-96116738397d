require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const fs = require('fs').promises;

const logger = require('../src/services/logger.service');
const mongoClient = require('../src/mongoClient');

const {
  checkPhoneNumber,
  getFormattedNumber,
} = require('../src/utils/phoneNumber.util');

const CommunityModel = require('../src/communitiesAPI/models/community.model');
const MembershipModel = require('../src/models/membership/membership.model');
const LearnerModel = require('../src/models/learners.model');

async function retrieveWhatsappCommunities() {
  const communities = await CommunityModel.find(
    {
      isActive: true,
      isDemo: { $ne: true },
      isWhatsappExperienceCommunity: true,
    },
    { code: 1 }
  )
    .sort({ _id: 1 })
    .lean();

  return communities;
}

async function retrieveMembershipsLearnerObjectId(communityCodes) {
  const memberships = await MembershipModel.find(
    {
      communityCode: { $in: communityCodes },
    },
    { learnerObjectId: 1 }
  ).lean();

  const learnerObjectIds = memberships.map(
    (membership) => membership.learnerObjectId
  );

  return learnerObjectIds;
}

async function retrieveLearners(learnerObjectIds) {
  const learners = await LearnerModel.find(
    {
      _id: { $in: learnerObjectIds },
    },
    { phoneNumber: 1 }
  ).lean();

  return learners;
}

function cleanPhoneNumber(phoneNumber) {
  const formattedPhoneNumber = getFormattedNumber(phoneNumber);

  if (!checkPhoneNumber(phoneNumber)) {
    throw new Error(
      `Invalid phone number: ${phoneNumber} - ${formattedPhoneNumber}`
    );
  }

  return formattedPhoneNumber;
}

const start = async () => {
  await mongoClient.connect();

  const communityCodes = [];

  const learnerObjectIds = await retrieveMembershipsLearnerObjectId(
    communityCodes
  );

  if (learnerObjectIds.length === 0) {
    throw new Error('No learner found');
  }

  const learners = await retrieveLearners(learnerObjectIds);

  await Promise.all(
    learners.map(async (learner) => {
      if (!learner.phoneNumber) return;

      const cleanedPhoneNumber = cleanPhoneNumber(learner.phoneNumber);

      if (learner.phoneNumber !== cleanedPhoneNumber) {
        const message = `${learner.phoneNumber},${cleanedPhoneNumber}`;
        console.log(message);

        await LearnerModel.updateOne(
          {
            _id: learner._id,
          },
          {
            $set: {
              phoneNumber: cleanedPhoneNumber,
            },
          }
        );

        await MembershipModel.updateMany(
          {
            learnerObjectId: learner._id,
          },
          {
            $set: {
              phoneNumber: cleanedPhoneNumber,
            },
          }
        );

        await fs.appendFile('logs/cleanPhoneNumbers.log', `${message}\n`);
      }
    })
  );

  logger.info('Completed');
  process.exit(0);
};

start();
