require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const mongoClient = require('../src/mongoClient');
const CountryInfoMappingModel = require('../src/models/countryInfoMapping.model');
const CommunityManagerPayoutModel = require('../src/communitiesAPI/models/communityManagerPayout.model');

async function retrievePayoutSupportedCurrencyCodes() {
  const countries = await CountryInfoMappingModel.find(
    {
      payoutCurrencySupported: true,
    },
    { currencyCode: 1 }
  ).lean();

  const currencyCodes = countries.map((country) =>
    country.currencyCode.toLowerCase()
  );

  const distinctCurrencyCodes = [...new Set(currencyCodes)];

  return distinctCurrencyCodes;
}

const start = async () => {
  await mongoClient.connect();

  const payoutSupportedCurrencyCodes =
    await retrievePayoutSupportedCurrencyCodes();

  console.log(payoutSupportedCurrencyCodes);

  const result = await CommunityManagerPayoutModel.updateMany(
    {
      payoutCurrency: { $nin: payoutSupportedCurrencyCodes },
    },
    { $set: { payoutCurrency: 'usd' } }
  );

  console.log(result);

  console.log('Completed');
  process.exit(0);
};

start();
