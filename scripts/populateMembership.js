require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const mongoClient = require('../src/mongoClient');

const PaymentBackendRpc = require('../src/rpc/paymentBackend');
const Membership = require('../src/models/membership/membership.model');
const RevenueTransactionModel = require('../src/models/revenueTransaction.model');
const Community = require('../src/communitiesAPI/models/community.model');

const {
  normalizeAmountByCurrency,
} = require('../src/utils/currency.util');

async function retrieveRevenueTransactionsByMembership(page, limit) {
  const revenueTransactions = await RevenueTransactionModel.aggregate([
    {
      $match: { learnerObjectId: { $exists: true } },
    },
    {
      $sort: { _id: 1 },
    },
    {
      $skip: page * limit,
    },
    {
      $limit: limit,
    },
    {
      $group: {
        _id: {
          communityObjectId: '$communityObjectId',
          learnerObjectId: '$learnerObjectId',
          currency: '$amountBreakdownInLocalCurrency.currency',
          transactionType: '$transactionType',
        },
        discountedItemPrice: {
          $sum: '$amountBreakdownInLocalCurrency.discountedItemPrice',
        },
      },
    },
    {
      $addFields: {
        amount: {
          $cond: {
            if: {
              $eq: ['$_id.transactionType', 'INBOUND'],
            },
            then: '$discountedItemPrice',
            else: {
              $multiply: [-1, '$discountedItemPrice'],
            },
          },
        },
      },
    },
    {
      $group: {
        _id: {
          communityObjectId: '$_id.communityObjectId',
          learnerObjectId: '$_id.learnerObjectId',
          currency: '$_id.currency',
        },
        amount: {
          $sum: '$amount',
        },
      },
    },
    {
      $group: {
        _id: {
          communityObjectId: '$_id.communityObjectId',
          learnerObjectId: '$_id.learnerObjectId',
        },
        lifeTimeValues: {
          $push: { amount: '$amount', currency: '$_id.currency' },
        },
      },
    },
    {
      $group: {
        _id: '$_id.communityObjectId',
        learners: {
          $push: {
            learnerObjectId: '$_id.learnerObjectId',
            lifeTimeValues: '$lifeTimeValues',
          },
        },
      },
    },
  ]);

  return revenueTransactions;
}

const getLearnerLtvAmount = async (
  paymentBackendRpc,
  lifeTimeValues,
  outputCurrency,
  learnerObjectId,
  communityObjectId
) => {
  let amount = 0;
  await Promise.all(
    lifeTimeValues.map(async (ltv) => {
      if (ltv.currency === outputCurrency) {
        amount += ltv.amount;
      } else {
        const conversionRateData =
          await paymentBackendRpc.getConversionRate(
            ltv.currency,
            outputCurrency
          );
        if (
          conversionRateData.sellCurrency !== ltv.currency ||
          conversionRateData.buyCurrency !== outputCurrency
        ) {
          console.log(
            `Curency conversion does not match! ${learnerObjectId} ${communityObjectId} - ${JSON.stringify(
              ltv
            )}`
          );
          return;
        }
        const exchangeRateToBaseCurrency =
          conversionRateData.conversionRate;
        const convertedAmount = normalizeAmountByCurrency(
          Math.ceil(exchangeRateToBaseCurrency * ltv.amount),
          outputCurrency
        );
        amount += convertedAmount;
      }
    })
  );
  return amount;
};

const retrieveUpdatePipelineQuery = async (aggregatedResults) => {
  const communityObjectIds = aggregatedResults.map((i) => i._id);
  const communities = await Community.find({
    _id: { $in: communityObjectIds },
  }).select('baseCurrency');

  const baseCurrencyMap = new Map();
  communities.forEach((i) =>
    baseCurrencyMap.set(i._id.toString(), i.baseCurrency)
  );

  const paymentBackendRpc = new PaymentBackendRpc();
  await paymentBackendRpc.init();

  const pipeline = [];

  await Promise.all(
    aggregatedResults.map((item) => {
      const communityObjectId = item._id;
      const outputCurrency =
        baseCurrencyMap.get(communityObjectId.toString()) ?? 'USD';

      item.learners.map(async (learner) => {
        const { learnerObjectId, lifeTimeValues } = learner;
        const amount = await getLearnerLtvAmount(
          paymentBackendRpc,
          lifeTimeValues,
          outputCurrency
        );
        pipeline.push({
          updateOne: {
            filter: {
              communityObjectId: communityObjectId,
              learnerObjectId: learnerObjectId,
            },
            update: {
              $set: { 'lifeTimeValue.currency': outputCurrency },
              $inc: { 'lifeTimeValue.amount': amount },
            },
          },
        });
      });
    })
  );
  return pipeline;
};

const updateMembership = async (page, limit) => {
  const aggregatedResults = await retrieveRevenueTransactionsByMembership(
    page,
    limit
  );
  if (aggregatedResults.length > 0) {
    const pipeline = await retrieveUpdatePipelineQuery(aggregatedResults);
    await Membership.bulkWrite(pipeline);
  }
};

const start = async () => {
  await mongoClient.connect();

  const limit = 10000;
  const totalTransactions = await RevenueTransactionModel.countDocuments({
    learnerObjectId: { $exists: true },
  });
  const rounds = Math.ceil(totalTransactions / limit);

  const arr = new Array(rounds);
  await Membership.updateMany({}, { 'lifeTimeValue.amount': 0 });
  await Promise.all(
    [...arr].map(async (item, index) => {
      await updateMembership(index, limit);
    })
  );

  console.log('Completed');
  process.exit(0);
};

start();
