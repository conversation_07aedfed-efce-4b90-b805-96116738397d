require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const fs = require('fs').promises;
const mongoClient = require('../src/mongoClient');

const CommunityModel = require('../src/communitiesAPI/models/community.model');
const ProgramModel = require('../src/models/program/program.model');
const ProgramParticipantModel = require('../src/models/program/programParticipant.model');
const { manageParticipantsService } = require('../src/services/program');

function chunkArray(array, chunkSize) {
  const result = [];
  for (let i = 0; i < array.length; i += chunkSize) {
    result.push(array.slice(i, i + chunkSize));
  }
  return result;
}

const start = async () => {
  await mongoClient.connect();

  const fromChallengeObjectId = '66d1d493ba1c66a2bd6413f5';
  const toChallengeObjectId = '670710b34b4c85079b04072f';
  const communityObjectId = '664272cdc7cc4f9f33029f18';

  const [fromChallengeParticipants, toChallenge, community] =
    await Promise.all([
      ProgramParticipantModel.find({
        programObjectId: fromChallengeObjectId,
      }).lean(),
      ProgramModel.findById(toChallengeObjectId).lean(),
      CommunityModel.findById(communityObjectId).lean(),
    ]);

  const timestamp = Date.now();

  const limit = 1000;

  const chunkArrays = chunkArray(fromChallengeParticipants, limit);

  const totalSize = fromChallengeParticipants.length;

  let counter = 0;

  for await (const chunk of chunkArrays) {
    counter += 1 * limit;
    console.log(`Start: ${counter}/${totalSize}`);

    await Promise.all(
      chunk.map(async (fromChallengeParticipant) => {
        const { learnerObjectId } = fromChallengeParticipant;

        await manageParticipantsService.addParticipant({
          challenge: toChallenge,
          learnerObjectId,
          community,
        });
      })
    );
  }

  console.log('Completed');
  process.exit(0);
};

start();
