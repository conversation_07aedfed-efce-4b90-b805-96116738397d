require('module-alias/register');
require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const fs = require('fs/promises');

const logger = require('../src/services/logger.service');
const mongoClient = require('../src/mongoClient');
const AddonTransactionModel = require('../src/communitiesAPI/models/communityAddonTransactions.model');
const EventModel = require('../src/communitiesAPI/models/communityEvents.model');
const FolderModel = require('../src/communitiesAPI/models/communityFolders.model');
const ProgramModel = require('../src/models/program/program.model');
const TemplateLibraryModel = require('../src/models/getInspired/templateLibrary.model');
const { TEMPLATE_LIBRARY_TYPES } = require('../src/constants/common');

async function updateAddonTransactionMetadata({
  communityObjectId,
  entityObjectId,
  templateLibraryMetadata,
}) {
  return AddonTransactionModel.updateMany(
    { entityObjectId, communityObjectId },
    {
      $set: {
        'metadata.templateLibrary': templateLibraryMetadata,
      },
    }
  );
}

async function processTemplateLibraryEntity({
  communityObjectId,
  templateLibraryObjectId,
  type,
  templateLibraryMetadata,
}) {
  switch (type) {
    case TEMPLATE_LIBRARY_TYPES.EVENT: {
      const event = await EventModel.findOne({
        communityObjectId,
        templateLibraryId: templateLibraryObjectId,
      }).lean();

      if (!event) {
        return;
      }

      return updateAddonTransactionMetadata({
        communityObjectId,
        entityObjectId: event._id,
        templateLibraryMetadata,
      });
    }
    case TEMPLATE_LIBRARY_TYPES.FOLDER:
    case TEMPLATE_LIBRARY_TYPES.COURSE:
    case TEMPLATE_LIBRARY_TYPES.SESSION: {
      const folder = await FolderModel.findOne({
        communityObjectId,
        templateLibraryId: templateLibraryObjectId,
      }).lean();

      if (!folder) {
        return;
      }

      return updateAddonTransactionMetadata({
        communityObjectId,
        entityObjectId: folder._id,
        templateLibraryMetadata,
      });
    }
    case TEMPLATE_LIBRARY_TYPES.CHALLENGE: {
      const challenge = await ProgramModel.findOne({
        communityObjectId,
        templateLibraryId: templateLibraryObjectId,
      }).lean();

      if (!challenge) {
        return;
      }

      return updateAddonTransactionMetadata({
        communityObjectId,
        entityObjectId: challenge._id,
        templateLibraryMetadata,
      });
    }
    default:
      break;
  }
}

async function populateTransactionMetadata({
  nextTemplateLibraryObjectId = null,
  limit = 10000,
}) {
  const matchFilter = {
    type: {
      $in: [
        TEMPLATE_LIBRARY_TYPES.EVENT,
        TEMPLATE_LIBRARY_TYPES.FOLDER,
        TEMPLATE_LIBRARY_TYPES.COURSE,
        TEMPLATE_LIBRARY_TYPES.SESSION,
        TEMPLATE_LIBRARY_TYPES.CHALLENGE,
      ],
    },
  };

  if (nextTemplateLibraryObjectId) {
    matchFilter._id = { $gt: nextTemplateLibraryObjectId };
  }

  const templateLibraries = await TemplateLibraryModel.find(matchFilter, {
    communityObjectId: 1,
    source: 1,
    sourceObjectId: 1,
    _id: 1,
    type: 1,
  })
    .sort({ _id: 1 })
    .limit(limit)
    .lean();

  if (templateLibraries.length === 0) {
    return null;
  }

  await Promise.all(
    templateLibraries.map(async (templateLibrary) => {
      const {
        _id: templateLibraryObjectId,
        type,
        communityObjectId,
        source,
        sourceObjectId,
      } = templateLibrary;

      const templateLibraryMetadata = {
        _id: templateLibraryObjectId,
        source,
        sourceObjectId,
      };

      const result = await processTemplateLibraryEntity({
        communityObjectId,
        templateLibraryObjectId,
        type,
        templateLibraryMetadata,
      });

      if (result?.modifiedCount && result.modifiedCount > 0) {
        const message = `${templateLibraryObjectId},${type},${communityObjectId},${result.modifiedCount}`;
        console.log(message);
        await fs.appendFile(
          'logs/populateTransactionMetadata.log',
          `${message}\n`
        );
      }
    })
  );

  const lastTemplateLibraryObjectId =
    templateLibraries[templateLibraries.length - 1]?._id;

  console.log(
    `lastTemplateLibraryObjectId: ${lastTemplateLibraryObjectId}`
  );

  return lastTemplateLibraryObjectId;
}

const start = async () => {
  await mongoClient.connect();

  let nextTemplateLibraryObjectId;

  while (true) {
    // eslint-disable-next-line no-await-in-loop
    nextTemplateLibraryObjectId = await populateTransactionMetadata({
      nextTemplateLibraryObjectId,
    });

    if (!nextTemplateLibraryObjectId) {
      break;
    }
  }

  logger.info('Completed');
  process.exit(0);
};

start();
