require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});
const fs = require('fs').promises;

const logger = require('../src/services/logger.service');
const MongodbUtils = require('../src/utils/mongodb.util');

const mongoClient = require('../src/mongoClient');
const EventAttendeesModel = require('../src/communitiesAPI/models/eventAttendees.model');
const AddonTransactionsModel = require('../src/communitiesAPI/models/communityAddonTransactions.model');
const EventModel = require('../src/communitiesAPI/models/communityEvents.model');

const CommunityEventService = require('../src/communitiesAPI/services/common/communityEvents.service');
const EventService = require('../src/services/event');

async function updateEventAttendeesWithoutTicketReference(
  currentTimestampInMs,
  limit
) {
  const query = [
    {
      $match: {
        isActive: true,
        isDemo: false,
        startTime: {
          $gt: new Date(),
        },
      },
    },
    {
      $sort: {
        startTime: 1,
      },
    },
    ...MongodbUtils.lookupAndUnwind(
      'event_attendees',
      '_id',
      'eventObjectId',
      'eventAttendee',
      false
    ),
    {
      $match: {
        'eventAttendee.ticketReference': {
          $exists: false,
        },
      },
    },
    {
      $limit: limit,
    },
  ];

  const events = await EventModel.aggregate(query);

  for await (const event of events) {
    const { eventAttendee } = event;

    const ticketReference =
      CommunityEventService.getEventTicketReferenceForAttendee({
        eventObjectId: eventAttendee.eventObjectId,
        learnerObjectId: eventAttendee.learnerObjectId,
      });

    logger.info(
      `updateEventAttendeesWithoutTicketReference: ticket reference: ${ticketReference}`
    );

    await EventAttendeesModel.findByIdAndUpdate(eventAttendee._id, {
      ticketReference,
    });

    await fs.appendFile(
      `logs/ticket_reference_${currentTimestampInMs}`,
      `UPDATE_TICKET_REFERENCE,${event._id},${eventAttendee._id},${eventAttendee.email},${eventAttendee.learnerObjectId},${ticketReference}\n`
    );

    const addonTransaction = await AddonTransactionsModel.findById(
      eventAttendee.eventCheckoutId
    ).lean();

    try {
      await EventService.sendEventEmail({
        event,
        eventAttendeeObjectId: eventAttendee._id,
        addonTransactionObjectId: addonTransaction?._id,
        learnerObjectId: eventAttendee.learnerObjectId,
        eventAttendeePurchaseType: eventAttendee.purchaseType,
        ticketReference,
        // TODO: @AmanMinhas verify why ticketReferences is missing.
      });
      await fs.appendFile(
        `logs/ticket_reference_${currentTimestampInMs}`,
        `SEND_RSVP_MAIL_SUCCESS,${event._id},${eventAttendee._id},${eventAttendee.email},${eventAttendee.learnerObjectId},${ticketReference}\n`
      );
    } catch (err) {
      logger.error(
        `updateEventAttendeesWithoutTicketReference: send rsvp email error: ${err}`
      );

      await fs.appendFile(
        `logs/ticket_reference_${currentTimestampInMs}`,
        `SEND_RSVP_MAIL_ERROR,${event._id},${eventAttendee._id},${eventAttendee.email},${eventAttendee.learnerObjectId},${ticketReference}\n`
      );
    }
  }

  if (events.length !== 0) {
    await updateEventAttendeesWithoutTicketReference(
      currentTimestampInMs,
      limit
    );
  }
}

async function start() {
  await mongoClient.connect();

  const LIMIT = 100;
  const currentTimestampInMs = Date.now();

  await updateEventAttendeesWithoutTicketReference(
    currentTimestampInMs,
    LIMIT
  );

  logger.info('Completed');
  process.exit(0);
}

start();
