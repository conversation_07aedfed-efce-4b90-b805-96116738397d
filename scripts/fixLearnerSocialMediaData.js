require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});
const mongoClient = require('../src/mongoClient');

const LearnerModel = require('../src/models/learners.model');
const {
  getFormattedSocialMedia,
} = require('../src/services/update_profile.service');

const BATCH_LIMIT_LEARNERS = 1000;
const QUERY_LEARNER = {
  isActive: true,
  socialMedia: { $gt: { $size: 0 } },
};

const updateLearnerSocialMedia = async (page, limit) => {
  const learners = await LearnerModel.find(QUERY_LEARNER)
    .sort({ _id: 1 })
    .skip(page * limit)
    .limit(limit)
    .select('_id socialMedia')
    .lean();

  const bulkUpdatePipeline = await Promise.all(
    learners.map(async (learner) => {
      const socialMedia = learner.socialMedia ?? [];
      const newSocialMedia = [];
      socialMedia.forEach((socialMediaDetails) => {
        if (socialMediaDetails.link && socialMediaDetails.type) {
          const data = getFormattedSocialMedia(socialMediaDetails);
          newSocialMedia.push(data);
        }
      });

      return {
        updateOne: {
          filter: {
            _id: learner._id,
          },
          update: {
            socialMedia: newSocialMedia,
          },
        },
      };
    })
  );

  await LearnerModel.bulkWrite(bulkUpdatePipeline);
};

const start = async () => {
  await mongoClient.connect();

  const total = await LearnerModel.countDocuments(QUERY_LEARNER);
  const rounds = Math.ceil(total / BATCH_LIMIT_LEARNERS);

  for (let index = 0; index < rounds; index++) {
    // eslint-disable-next-line no-await-in-loop
    await updateLearnerSocialMedia(index, BATCH_LIMIT_LEARNERS);
  }

  console.log('Completed');
  process.exit(0);
};

start();
