require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});
const ObjectId = require('mongoose').Types.ObjectId;

const logger = require('../src/services/logger.service');
const mongoClient = require('../src/mongoClient');
const EventAttendeeModel = require('../src/communitiesAPI/models/eventAttendees.model');

const PrimaryMongooseConnection = require('../src/rpc/primaryMongooseConnection');

const migrate = async () => {
  // get all communities

  const sourceEventObjectId = new ObjectId('675a84ad79967e3da915771e');
  const destEventObjectId = new ObjectId('678116ac9ff17fa2b5cb0b30');
  const events = await EventAttendeeModel.find({
    eventObjectId: sourceEventObjectId,
    purchaseType: 'free',
    status: 'PENDING',
  }).lean();

  const sourceEventAttendeesObjectId = [];
  const newEventAttendees = events.map((attendee) => {
    sourceEventAttendeesObjectId.push(attendee._id);
    const newRecord = { ...attendee, eventObjectId: destEventObjectId };
    delete newRecord._id;
    return newRecord;
  });

  const primaryMongooseConnection =
    await PrimaryMongooseConnection.connect();

  const session = await primaryMongooseConnection.startSession();
  session.startTransaction();

  try {
    await EventAttendeeModel.insertMany(newEventAttendees, { session });
    await EventAttendeeModel.updateMany(
      { _id: { $in: sourceEventAttendeesObjectId } },
      { status: 'NOT_GOING' },
      { session }
    );

    await session.commitTransaction();
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }
};

const start = async () => {
  await mongoClient.connect();

  await migrate();

  logger.info('Completed');
  process.exit(0);
};

start();
