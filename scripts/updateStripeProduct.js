require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});
const fs = require('fs').promises;

const mongoClient = require('../src/mongoClient');
const CommunityModel = require('../src/communitiesAPI/models/community.model');
const CommunityService = require('../src/communitiesAPI/services/common/community.service');
const PaymentProviderUtils = require('../src/utils/paymentProvider.util');
const {
  CURRENCY_WITH_NON_DECIMAL_POINTS,
} = require('../src/constants/common');
const { CENTS_PER_DOLLAR } = require('../src/communitiesAPI/constants');

function getAmountInDollarOrCents(amount, currency) {
  if (amount == null || amount === 0) return amount;

  const canDivideBy100 =
    !CURRENCY_WITH_NON_DECIMAL_POINTS.includes(currency);

  return canDivideBy100 ? amount / CENTS_PER_DOLLAR : amount;
}

async function retrievePassOnPaidCommunities() {
  const communities = await CommunityModel.find({
    passOnPaymentGatewayFee: true,
    isActive: true,
    isDemo: { $ne: true },
    isPaidCommunity: true,
  }).lean();

  return communities;
}

async function updatePrice(community, currentTimestampInMs) {
  const paymentProvider =
    await PaymentProviderUtils.retrievePaymentProvider(
      community.payment_methods
    );

  if (community.prices && community.prices.length > 0) {
    const priceArray = community.prices.map((price) => {
      return {
        currency: price.currency,
        amount: getAmountInDollarOrCents(price.cmSetPrice, price.currency),
        interval: price.interval,
        intervalCount: price.intervalCount,
      };
    });

    let message = `${community.code},${paymentProvider},${community.stripeProductId},${community.prices.length}`;

    console.log(message, priceArray);

    try {
      await CommunityService.updatePricesForToggleChanges(
        community.stripeProductId,
        community.createdAt,
        priceArray,
        community.baseCurrency,
        community.passOnTakeRate,
        community.payoutFeeConfigs,
        community.basePayoutFeeConfigs,
        paymentProvider,
        community.passOnPaymentGatewayFee,
        community.countryCreatedIn
      );

      await fs.appendFile(
        `logs/updateStripeProduct_${currentTimestampInMs}.log`,
        `${message}\n`
      );
    } catch (err) {
      message = `${message},${err.message}`;
      await fs.appendFile(
        `logs/updateStripeProductError_${currentTimestampInMs}.log`,
        `${message}\n`
      );
    }
  }
}

function chunkArray(array, chunkSize) {
  if (chunkSize <= 0) {
    throw new Error('Chunk size must be greater than zero');
  }

  const chunks = [];
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize));
  }
  return chunks;
}

const start = async () => {
  await mongoClient.connect();

  const currentTimestampInMs = Date.now();

  const chunkSize = 100;

  const communities = await retrievePassOnPaidCommunities();

  const chunkedCommunities = chunkArray(communities, chunkSize);

  for await (const chunkedSizeCommunities of chunkedCommunities) {
    await Promise.all(
      chunkedSizeCommunities.map(async (community) => {
        await updatePrice(community, currentTimestampInMs);
      })
    );
  }

  console.log('Completed');
  process.exit(0);
};

start();
