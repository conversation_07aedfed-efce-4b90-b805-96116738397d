require('dotenv').config();
const mongoClient = require('../src/mongoClient');
const backupSendgridTemplate = require('../src/models/notificationBackend/backupSendGridTemplate.model');
const {
  SENDGRID_TEMPLATE_SOURCES,
} = require('../src/models/notificationBackend/constants');

/**
 * This script is used to add new field mainTemplateSource field to the
 *    backup_sendgrid_template collection & initialize to CUSTOM_AND_MKT_EMAIL
 * The mainTemplateSource field is used to determine
 *    in which sendgrid account does the template in mainTemplateId exist.
 * The source can be one of CUSTOM_AND_MKT_EMAIL or INTERNAL_EMAIL.
 */
const updateBackupSendgridTemplates = async () => {
  try {
    await mongoClient.connect();

    console.log(
      'Starting to update backup_sendgrid_template documents...'
    );

    const result = await backupSendgridTemplate.updateMany(
      { mainTemplateSource: { $exists: false } },
      {
        $set: {
          mainTemplateSource:
            SENDGRID_TEMPLATE_SOURCES.CUSTOM_AND_MKT_EMAIL,
        },
      }
    );

    console.log(`Updated ${result.modifiedCount} documents successfully.`);
    console.log(`${result.matchedCount} documents matched the query.`);

    if (result.matchedCount === 0) {
      console.log('No documents found that need updating.');
    }

    console.log('Update completed successfully.');
  } catch (error) {
    console.error(
      'Error updating backup_sendgrid_template documents:',
      error
    );
  } finally {
    process.exit(0);
  }
};

updateBackupSendgridTemplates();
