require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const fs = require('fs').promises;

const mongoClient = require('../src/mongoClient');

const CommunityModel = require('../src/communitiesAPI/models/community.model');
const {
  CENTS_PER_DOLLAR,
  latamCountriesArray,
  COUNTRY_CREATED,
} = require('../src/communitiesAPI/constants');
const {
  retrievePaymentFeeStructure,
} = require('../src/services/config/paymentFeeStructureConfig.service');
const {
  updateFeeConfig,
} = require('../src/services/community/updatePayoutFeeConfig.service');

async function retrieveActiveCommunitiesByBatch(
  targetSpecificCommunities,
  communityCodes,
  currency,
  additionalMatchFilter,
  skip,
  limit
) {
  const filter = {
    isActive: true,
    isDemo: { $ne: true },
    ...additionalMatchFilter,
  };
  if (currency) {
    filter.baseCurrency = currency;
  }

  if (targetSpecificCommunities) {
    if (communityCodes.length === 0) {
      return [];
    }

    filter.code = {
      $in: communityCodes,
    };
  }

  // const communities = await CommunityModel.find(filter)
  //   .sort({ _id: 1 })
  //   .skip((skip - 1) * limit)
  //   .limit(limit)
  //   .lean();

  const communities = await CommunityModel.aggregate([
    {
      $match:
        /**
         * query: The query in MQL.
         */
        {
          'basePayoutFeeConfigs.0': {
            $exists: true,
          },
          isActive: true,
          isDemo: { $ne: true },
          baseCurrency: {
            $nin: ['UYU'],
          },
          // 'config.planType': 'PRO',
        },
    },
    {
      $project:
        /**
         * specifications: The fields to
         *   include or exclude.
         */
        {
          lastElement: {
            $arrayElemAt: ['$basePayoutFeeConfigs', -1],
          },
          baseCurrency: 1,
          countryCreatedIn: 1,
          code: 1,
          basePayoutFeeConfigs: 1,
          config: 1,
        },
    },
    {
      $match: {
        'lastElement.effectiveTimeStart': {
          $lt: new Date('2025-05-01'),
        },
      },
    },
    //{ $skip: (skip - 1) * limit }, // Replace 0 with (pageNumber - 1) * pageSize
    { $limit: limit }, // Replace 10 with your desired pageSize
  ]);

  return communities;
}

const updateCommunityPayoutFeeConfig = async () => {
  const targetSpecificCommunities = false;
  const communityCodes = [
    'ACADEMIA_HERO_INSIDE',
    'ALL_IN_90',
    //'CONVENCER',
    'LEGIO_DE_LDERES',
    //'ALL_IN_PRO',
    'ALL_IN_PRO',
  ];

  const additionalMatchFilter = {
    // 'basePayoutFeeConfigs.effectiveTimeStart': {
    //   $ne: startDate,
    // },
    'basePayoutFeeConfigs.0': { $exists: true },
    // baseCurrency: 'INR',
    // countryCreatedIn: COUNTRY_CREATED.UNITED_STATES,
    // 'basePayoutFeeConfigs.-1.purchaseTypeConfigs': { $exists: false },
  };

  const next = async (skip = 1, limit = 1000) => {
    const communities = await retrieveActiveCommunitiesByBatch(
      targetSpecificCommunities,
      communityCodes,
      null,
      additionalMatchFilter,
      skip,
      limit
    );

    if (communities.length === 0) {
      return;
    }

    const maxSize = communities.length;
    let counter = (skip - 1) * maxSize;

    await Promise.all(
      communities.map(async (community) => {
        counter += 1;
        console.log(
          `Updating fee config (${counter}/${skip * maxSize}): ${
            community.code
          }`
        );

        try {
          await updateFeeConfig(
            community,
            null,
            null,
            new Date('2025-05-01')
          );
          await fs.appendFile(
            'logs/updateFeeConfigs.log',
            `SUCCESS,${community.code},${
              community.stripeProductId ?? ''
            },\n`
          );
          console.log(`Updated fee config: ${community.code}`);
        } catch (err) {
          await fs.appendFile(
            'logs/updateFeeConfigs.log',
            `FAILED,${community.code},${community.stripeProductId ?? ''},${
              err.message
            }\n`
          );
        }
      })
    );

    await next(skip + 1, limit);
  };

  await next();

  console.log('Completed');
};

function retrieveCustomBasePayoutFeeConfig(
  customBaseFeeType,
  baseCurrency,
  paymentFeeStructure
) {
  const { customLogicFee } = paymentFeeStructure;

  const customBaseFeeStructure = customLogicFee?.[customBaseFeeType];

  if (!customBaseFeeStructure) {
    throw new Error('Fee is not found');
  }

  const { purchaseTypeConfigs } = customBaseFeeStructure;
  const purchaseTypeConfigsOfBaseCurrency = {};
  for (const key in purchaseTypeConfigs) {
    if (purchaseTypeConfigs[key]?.[baseCurrency]) {
      purchaseTypeConfigsOfBaseCurrency[key] = {
        ...purchaseTypeConfigs[key]?.[baseCurrency],
      };
    }
  }

  return purchaseTypeConfigsOfBaseCurrency;
}

const CUSTOM_BASE_FEE_TYPE = {
  LATAM: 'latam',
  USA: 'usa',
};

const attachPurchaseTypeFeeInExistingFeeConfig = async (community) => {
  const { baseCurrency, countryCreatedIn, config } = community;

  const { planType } = config;

  const paymentFeeStructure = await retrievePaymentFeeStructure({
    baseCurrency,
    planType,
  });

  let finalPurchaseTypeConfigs;
  // For latam community + (USD or EUR)
  if (
    latamCountriesArray.includes(countryCreatedIn) &&
    ['USD', 'EUR'].includes(baseCurrency)
  ) {
    finalPurchaseTypeConfigs = retrieveCustomBasePayoutFeeConfig(
      CUSTOM_BASE_FEE_TYPE.LATAM,
      baseCurrency,
      paymentFeeStructure
    );
  } else if (
    // For US community + USD
    countryCreatedIn === COUNTRY_CREATED.UNITED_STATES &&
    ['USD'].includes(baseCurrency)
  ) {
    finalPurchaseTypeConfigs = retrieveCustomBasePayoutFeeConfig(
      CUSTOM_BASE_FEE_TYPE.USA,
      baseCurrency,
      paymentFeeStructure
    );
  } else {
    const { purchaseTypeConfigs } = paymentFeeStructure;
    if (!purchaseTypeConfigs) {
      return;
    }

    for (const key in purchaseTypeConfigs) {
      if (purchaseTypeConfigs[key]?.[baseCurrency]) {
        finalPurchaseTypeConfigs = {
          [`${key}`]: {
            ...purchaseTypeConfigs[key]?.[baseCurrency],
          },
        };
      }
    }
  }
  if (!finalPurchaseTypeConfigs) {
    return;
  }

  // Update the last item in payoutFeeConfigs
  if (
    community.payoutFeeConfigs &&
    community.payoutFeeConfigs.length > 0
  ) {
    const lastIndex = community.payoutFeeConfigs.length - 1;
    await CommunityModel.updateOne(
      { _id: community._id },
      {
        $set: {
          [`payoutFeeConfigs.${lastIndex}.purchaseTypeConfigs`]:
            finalPurchaseTypeConfigs,
        },
      }
    );
  }
};

// For custom fee rate communities
const updatePurchaseTypeFeeInPayoutFeeConfig = async () => {
  const targetSpecificCommunities = false;
  const communityCodes = [];

  const additionalMatchFilter = {
    $or: [
      {
        'payoutFeeConfigs.0': { $exists: true },
        'payoutFeeConfigs.-1.purchaseTypeConfigs': { $exists: false },
      },
    ],
  };

  const next = async (skip = 1, limit = 10000) => {
    const communities = await retrieveActiveCommunitiesByBatch(
      targetSpecificCommunities,
      communityCodes,
      null,
      additionalMatchFilter,
      skip,
      limit
    );

    if (communities.length === 0) {
      return;
    }

    const maxSize = communities.length;
    let counter = (skip - 1) * maxSize;

    for await (const community of communities) {
      counter += 1;

      console.log(
        `Updating fee config (${counter}/${skip * maxSize}): ${
          community.code
        }`
      );

      try {
        await attachPurchaseTypeFeeInExistingFeeConfig(community);

        await fs.appendFile(
          'logs/updateFeeConfigs.log',
          `SUCCESS,${community.code}\n`
        );
      } catch (err) {
        await fs.appendFile(
          'logs/updateFeeConfigs.log',
          `FAILED,${community.code},${err.message}\n`
        );
      }
    }

    await next(skip + 1, limit);
  };

  await next();
};

const start = async () => {
  await mongoClient.connect();
  await updateCommunityPayoutFeeConfig();
  process.exit(0);
};

start();
