/* eslint-disable no-await-in-loop */
require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const fs = require('fs').promises;

const CommunityModel = require('../src/communitiesAPI/models/community.model');
const MembershipModel = require('../src/models/membership/membership.model');
const EventAttendeeModel = require('../src/communitiesAPI/models/eventAttendees.model');
const FolderViewerModel = require('../src/models/product/folderViewers.model');
const SessionAttendeeModel = require('../src/models/oneOnOneSessions/sessionAttendees.model');
const ProgramParticipantModel = require('../src/models/program/programParticipant.model');
const MembershipBatchMetadataModel = require('../src/models/batchMetadata/membershipBatchMetadata.model');
const EventAttendeeBatchMetadataModel = require('../src/models/batchMetadata/eventAttendeeBatchMetadata.model');
const FolderViewerBatchMetadataModel = require('../src/models/batchMetadata/folderViewerBatchMetadata.model');
const SessionAttendeeBatchMetadataModel = require('../src/models/batchMetadata/sessionAttendeeBatchMetadata.model');
const ProgramParticipantBatchMetadataModel = require('../src/models/batchMetadata/programParticipantBatchMetadata.model');
const SubscriptionBatchMetadataModel = require('../src/models/batchMetadata/subscriptionBatchMetadata.model');
const EventModel = require('../src/communitiesAPI/models/communityEvents.model');
const ProductModel = require('../src/communitiesAPI/models/communityFolders.model');
const ProgramModel = require('../src/models/program/program.model');
const SubscriptionModel = require('../src/communitiesAPI/models/communitySubscriptions.model');
const {
  communityFolderTypesMap,
} = require('../src/communitiesAPI/constants');
const { BATCH_METADATA_MODEL_TYPE } = require('../src/constants/common');

const mongoClient = require('../src/mongoClient');

const BATCH_SIZE = 500;

async function retrieveCommunities(lastObjectId) {
  const matchFilter = {
    isActive: true,
    isDemo: { $ne: true },
    'config.batchMetadataEnabledType': { $exists: false },
  };

  if (lastObjectId) {
    matchFilter._id = { $gt: lastObjectId };
  }

  const communities = await CommunityModel.find(matchFilter)
    .sort({ _id: 1 })
    .limit(100000)
    .lean();

  return communities;
}

async function* getBatches(model, query = {}) {
  let lastId = null;

  while (true) {
    const batchQuery = {
      ...query,
      ...(lastId && { _id: { $gt: lastId } }),
    };

    const batch = await model
      .find(batchQuery)
      .sort({ _id: 1 })
      .limit(BATCH_SIZE)
      .lean();

    if (batch.length === 0) {
      yield null;
      break;
    }

    lastId = batch[batch.length - 1]._id;

    yield {
      firstId: batch[0]._id,
      lastId,
      count: batch.length,
    };
  }
}

const modelConfig = {
  membership: {
    getBatches: (communityObjectId) =>
      getBatches(MembershipModel, { communityObjectId }),
    metadataModel: MembershipBatchMetadataModel,
  },
  eventAttendee: {
    getBatches: (eventObjectId) =>
      getBatches(EventAttendeeModel, { eventObjectId }),
    metadataModel: EventAttendeeBatchMetadataModel,
  },
  folderViewer: {
    getBatches: (folderObjectId) =>
      getBatches(FolderViewerModel, { folderObjectId }),
    metadataModel: FolderViewerBatchMetadataModel,
  },
  sessionAttendee: {
    getBatches: (sessionObjectId) =>
      getBatches(SessionAttendeeModel, { sessionObjectId }),
    metadataModel: SessionAttendeeBatchMetadataModel,
  },
  programParticipant: {
    getBatches: (programObjectId) =>
      getBatches(ProgramParticipantModel, {
        programObjectId,
      }),
    metadataModel: ProgramParticipantBatchMetadataModel,
  },
  subscription: {
    getBatches: (communityCode) =>
      getBatches(SubscriptionModel, { communityCode }),
    metadataModel: SubscriptionBatchMetadataModel,
  },
};

async function getEventObjectIds(communityObjectId) {
  const events = await EventModel.find({
    communities: communityObjectId,
  })
    .select('_id')
    .lean();

  return events.map((event) => event._id);
}

async function getFolderObjectIds(communityObjectId) {
  const folders = await ProductModel.find({
    communityObjectId,
    type: communityFolderTypesMap.DIGITAL_PRODUCT,
  })
    .select('_id')
    .lean();

  return folders.map((folder) => folder._id);
}

async function getSessionObjectIds(communityObjectId) {
  const sessions = await ProductModel.find({
    communityObjectId,
    type: communityFolderTypesMap.SESSION,
  })
    .select('_id')
    .lean();

  return sessions.map((session) => session._id);
}

async function getProgramObjectIds(communityObjectId) {
  const programs = await ProgramModel.find({
    communityObjectId,
  })
    .select('_id')
    .lean();

  return programs.map((program) => program._id);
}

const entityIdGetters = {
  membership: async (communityObjectId) => [communityObjectId],
  eventAttendee: getEventObjectIds,
  folderViewer: getFolderObjectIds,
  sessionAttendee: getSessionObjectIds,
  programParticipant: getProgramObjectIds,
  subscription: async (communityCode) => [communityCode],
};

async function populateBatchMetadata(community, modelType) {
  const config = modelConfig[modelType];
  if (!config) return;

  const { _id: communityObjectId, code: communityCode } = community;

  const isSubscription = modelType === 'subscription';

  const entityIdentifier = isSubscription
    ? communityCode
    : communityObjectId;

  const entityObjectIds = await entityIdGetters[modelType](
    entityIdentifier
  );

  await Promise.all(
    entityObjectIds.map(async (entityObjectId) => {
      // Reset data
      await config.metadataModel.deleteMany({
        communityObjectId,
        entityObjectId: isSubscription
          ? communityObjectId
          : entityObjectId,
      });

      let batchNumber = 0;

      for await (const batch of config.getBatches(entityObjectId)) {
        batchNumber += 1;

        if (!batch) {
          break;
        }

        const existingBatch = await config.metadataModel
          .findOne({
            communityObjectId,
            entityObjectId: isSubscription
              ? communityObjectId
              : entityObjectId,
            startObjectId: batch.firstId,
            maxBatchSize: BATCH_SIZE,
          })
          .lean();

        if (!existingBatch) {
          await config.metadataModel.create({
            communityObjectId,
            entityObjectId: isSubscription
              ? communityObjectId
              : entityObjectId,
            startObjectId: batch.firstId,
            endObjectId: batch.lastId,
            currentSize: batch.count,
            maxBatchSize: BATCH_SIZE,
            batchNumber,
          });
        }
      }
    })
  );
}

async function populateDifferentModelBatchMetadata(community) {
  await Promise.all(
    Object.keys(modelConfig).map(async (modelType) =>
      populateBatchMetadata(community, modelType)
    )
  );

  const batchMetadataEnabledType = Object.keys(
    BATCH_METADATA_MODEL_TYPE
  ).reduce((acc, modelType) => {
    acc[modelType] = true;
    return acc;
  }, {});

  try {
    await CommunityModel.updateOne(
      { _id: community._id },
      {
        $set: {
          'config.batchMetadataEnabledType': batchMetadataEnabledType,
        },
      }
    );

    return fs.appendFile(
      'logs/populateDifferentModelBatchMetadata.log',
      `success: ${community._id}\n`
    );
  } catch (err) {
    console.log(`error: ${community._id} ${err}`);
    return fs.appendFile(
      'logs/populateDifferentModelBatchMetadata.error.log',
      `error: ${community._id},${err}\n`
    );
  }
}

const start = async () => {
  await mongoClient.connect();

  let lastObjectId;

  do {
    const communities = await retrieveCommunities(lastObjectId);

    console.log(
      `Populating batch metadata for ${communities.length} communities`
    );

    await Promise.all(
      communities.map(async (community) =>
        populateDifferentModelBatchMetadata(community)
      )
    );

    lastObjectId =
      communities.length > 0
        ? communities[communities.length - 1]._id
        : null;

    console.log(`Next lastObjectId: ${lastObjectId}`);
  } while (lastObjectId);

  console.log('Completed');
  process.exit(0);
};

start();
