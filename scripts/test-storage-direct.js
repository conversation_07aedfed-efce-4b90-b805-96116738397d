require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const mongoClient = require('../src/mongoClient');
const CommunityModel = require('../src/communitiesAPI/models/community.model');

const TEST_COMMUNITY_ID = '62f74d331b2d4ada9be30f4e';

async function testStorageDirect() {
  try {
    await mongoClient.connect();
    console.log('Connected to MongoDB');

    console.log(
      `\nTesting Long storage with community: ${TEST_COMMUNITY_ID}`
    );
    console.log('================================================');

    // Step 1: Clear field
    console.log('\n1. Clearing currentStorageUsageInBytes field...');
    await CommunityModel.updateOne(
      { _id: TEST_COMMUNITY_ID },
      { $unset: { currentStorageUsageInBytes: '' } }
    );

    // Step 2: Set small value
    const smallValue = 1 * 1024 * 1024 * 1024; // 1GB
    console.log(`\n2. Setting small value (1GB = ${smallValue} bytes)...`);

    await CommunityModel.updateOne(
      { _id: TEST_COMMUNITY_ID },
      { currentStorageUsageInBytes: smallValue }
    );

    let community = await CommunityModel.findById(
      TEST_COMMUNITY_ID
    ).lean();
    console.log(`   Stored: ${community.currentStorageUsageInBytes}`);
    console.log(
      `   Correct: ${community.currentStorageUsageInBytes === smallValue}`
    );

    // Step 3: Set large value (100GB)
    const largeValue = 100 * 1024 * 1024 * 1024; // 100GB
    console.log(
      `\n3. Setting large value (100GB = ${largeValue} bytes)...`
    );
    console.log(`   Note: This exceeds Int32 max (${2147483647})`);

    await CommunityModel.updateOne(
      { _id: TEST_COMMUNITY_ID },
      { currentStorageUsageInBytes: largeValue }
    );

    community = await CommunityModel.findById(TEST_COMMUNITY_ID).lean();
    console.log(`   Stored: ${community.currentStorageUsageInBytes}`);
    console.log(
      `   Correct: ${community.currentStorageUsageInBytes === largeValue}`
    );

    // Step 4: Test arithmetic
    console.log(`\n4. Testing arithmetic operations...`);
    const currentValue = community.currentStorageUsageInBytes;
    const addValue = 50 * 1024 * 1024 * 1024; // 50GB
    const newValue = currentValue + addValue;

    await CommunityModel.updateOne(
      { _id: TEST_COMMUNITY_ID },
      { currentStorageUsageInBytes: newValue }
    );

    community = await CommunityModel.findById(TEST_COMMUNITY_ID).lean();
    console.log(
      `   After adding 50GB: ${community.currentStorageUsageInBytes}`
    );
    console.log(`   Expected (150GB): ${newValue}`);
    console.log(
      `   Correct: ${community.currentStorageUsageInBytes === newValue}`
    );

    console.log(`\n================================================`);
    console.log(`IMPORTANT: Check in MongoDB Compass now!`);
    console.log(`1. Find community ID: ${TEST_COMMUNITY_ID}`);
    console.log(`2. Check currentStorageUsageInBytes field type`);
    console.log(`3. Should show "Int64" or "Long", NOT "Int32"`);
    console.log(`4. Value should be 161061273600 (150GB)`);
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    console.log('\nDisconnected from MongoDB');
    process.exit(0);
  }
}

testStorageDirect();
