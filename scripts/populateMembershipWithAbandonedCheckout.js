require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});
const { DateTime } = require('luxon');
const ObjectId = require('mongoose').Types.ObjectId;

const mongoClient = require('../src/mongoClient');
const PurchaseTransactionModel = require('../src/communitiesAPI/models/communityPurchaseTransactions.model');
const Membership = require('../src/models/membership/membership.model');
const AbandonedCartsModel = require('../src/models/abandonedCarts/abandonedCarts.model');
const CommunityModel = require('../src/communitiesAPI/models/community.model');

const {
  ABANDONED_CARTS_STATUS,
  PURCHASE_TYPE,
} = require('../src/constants/common');

const retrieveAbandonedMembershipByCommunityMap = async (skip, limit) => {
  const transactions = await PurchaseTransactionModel.aggregate([
    {
      $match: {
        email: { $nin: [null, ''] },
        $or: [{ applyDiscount: true }, { amount: { $gt: 0 } }],
      },
    },
    { $sort: { _id: 1 } },
    {
      $group: {
        _id: { email: '$email', communityCode: '$community_code' },
        createdAt: { $push: '$createdAt' },
      },
    },
    { $skip: skip },
    { $limit: limit },
  ]);

  const orConditions = transactions.map((i) => ({
    email: i._id.email,
    communityCode: i._id.communityCode,
  }));

  const completedPayments = await PurchaseTransactionModel.find({
    $and: [
      { 'payment_details.complete_payment': 1 },
      { $or: [{ applyDiscount: true }, { amount: { $gt: 0 } }] },
      { $or: orConditions },
    ],
  })
    .sort({ createdAt: 1 })
    .select('_id email community_code')
    .lean();

  const completedPaymentMap = new Map();
  completedPayments.forEach((i) => {
    const createdAt = completedPaymentMap.get(
      `${i.email}_${i.community_code}`
    );
    if (createdAt && createdAt < i.createdAt) {
      return;
    }
    completedPaymentMap.add(`${i.email}_${i.community_code}`, i.createdAt);
  });

  const abandoned = new Map();
  transactions.forEach((i) => {
    const completedCreatedAt = completedPaymentMap.get(
      `${i._id.email}_${i._id.communityCode}`
    );
    if (completedCreatedAt) {
      const oneHourLater = DateTime.fromJSDate(i.createdAt[0]).plus({
        hours: 1,
      });
      const signUpDate = DateTime.fromJSDate(completedCreatedAt);

      if (signUpDate >= oneHourLater) {
        return;
      }
    }
    const value = abandoned.get(i._id.communityCode) ?? [];
    value.push({
      email: i._id.email,
      communityCode: i._id.communityCode,
      createdAt: i.createdAt[0],
    });
    abandoned.set(i._id.communityCode, value);
  });

  return abandoned;
};

const getMembershipAbandonedCartPipeline = (
  abandonedMembershipByCommunityCache,
  communities,
  abandonedCartCache
) => {
  const communityCache = new Map();
  communities.forEach((i) => communityCache.set(i.code, i._id.toString()));

  const pipeline = [];
  abandonedMembershipByCommunityCache.forEach((value, key) => {
    value.forEach((i) => {
      const communityObjectId = communityCache.get(key);
      const abandonedCart = abandonedCartCache.get(
        `${communityObjectId}_${i.email}`
      );
      const productsNotPurchased =
        abandonedCart?.productsNotPurchased ?? [];
      productsNotPurchased.push({
        entityObjectId: new ObjectId(communityObjectId),
        entityType: PURCHASE_TYPE.SUBSCRIPTION,
        createdAt: i.createdAt,
      });
      abandonedCartCache.delete(`${communityObjectId}_${i.email}`);
      if (productsNotPurchased.length === 0) {
        return;
      }

      const firstAbandonedCheckout = productsNotPurchased.sort(
        (a, b) => a.createdAt - b.createdAt
      )[0];

      pipeline.push({
        updateMany: {
          filter: {
            communityObjectId: new ObjectId(communityObjectId),
            email: i.email,
          },
          update: {
            $set: {
              'abandonedCheckoutInfo.firstAbandonedCheckout':
                firstAbandonedCheckout,
              'abandonedCheckoutInfo.productsNotPurchased':
                productsNotPurchased,
            },
          },
        },
      });
    });
  });
  return pipeline;
};

async function retrieveAbandonedCarts() {
  const abandonedCarts = await AbandonedCartsModel.aggregate([
    {
      $match: {
        status: ABANDONED_CARTS_STATUS.LEAD,
      },
    },
    {
      $lookup: {
        from: 'learners',
        localField: 'learnerObjectId',
        foreignField: '_id',
        as: 'learner',
      },
    },
    {
      $group: {
        _id: {
          communityObjectId: '$communityObjectId',
          email: { $first: '$learner.email' },
          learnerObjectId: '$learnerObjectId',
        },
        productsNotPurchased: {
          $push: {
            entityObjectId: '$entityObjectId',
            entityType: '$entityType',
            createdAt: '$createdAt',
          },
        },
      },
    },
  ]);

  return abandonedCarts;
}

const getAbandonedCartPipeline = (abandonedCarts) => {
  return abandonedCarts.map((item) => {
    const filter = item._id;
    const firstAbandonedCheckout = item.productsNotPurchased.sort(
      (a, b) => a.createdAt - b.createdAt
    )[0];
    return {
      updateMany: {
        filter,
        update: {
          $set: {
            'abandonedCheckoutInfo.firstAbandonedCheckout':
              firstAbandonedCheckout,
            'abandonedCheckoutInfo.productsNotPurchased':
              item.productsNotPurchased,
          },
        },
      },
    };
  });
};

const bulkUpdateInParts = async (results, threshold) => {
  const rounds = Math.ceil(results.length / threshold);
  const queries = [];
  for (let i = 0; i < rounds; i++) {
    const start = i * threshold;
    let end = start + threshold;
    if (end > results.length) end = results.length;
    queries.push({ start, end });
  }

  await Promise.all(
    queries.map(async (item) => {
      await Membership.bulkWrite(results.slice(item.start, item.end));
    })
  );
};

const run = async () => {
  await mongoClient.connect();

  const THRESHOLD = 1000;
  const abandonedCarts = await retrieveAbandonedCarts();
  const abandonedCartCache = new Map();

  abandonedCarts.forEach((item) => {
    abandonedCartCache.set(
      `${item._id.communityObjectId.toString()}_${item._id.email}`,
      item
    );
  });

  const transactions = await PurchaseTransactionModel.aggregate([
    {
      $match: {
        $or: [{ applyDiscount: true }, { amount: { $gt: 0 } }],
      },
    },
    {
      $group: {
        _id: { email: '$email', communityCode: '$community_code' },
        createdAt: { $push: '$createdAt' },
      },
    },
    { $project: { _id: 1 } },
  ]);

  const total = transactions.length;
  // const total = 430946;
  const limit = 300;
  const rounds = Math.ceil(total / limit);

  const queries = [];
  for (let i = 0; i < rounds; i++) {
    const skip = limit * i;
    queries.push(skip);
  }
  for await (const skip of queries) {
    const abandonedMembershipByCommunityCache =
      await retrieveAbandonedMembershipByCommunityMap(skip, limit);
    const communityCodes = [...abandonedMembershipByCommunityCache.keys()];
    const communities = await CommunityModel.find({
      code: { $in: communityCodes },
    })
      .select('_id code')
      .lean();

    const pipeline = getMembershipAbandonedCartPipeline(
      abandonedMembershipByCommunityCache,
      communities,
      abandonedCartCache
    );
    await Membership.bulkWrite(pipeline);
  }

  if (abandonedCartCache.size > 0) {
    const remaining = await getAbandonedCartPipeline([
      ...abandonedCartCache.values(),
    ]);
    await bulkUpdateInParts(remaining, THRESHOLD);
  }

  console.log('Completed');
  process.exit(0);
};

run();
