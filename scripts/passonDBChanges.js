require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});
const fs = require('fs').promises;

const logger = require('../src/services/logger.service');

const mongoClient = require('../src/mongoClient');
const Communities = require('../src/communitiesAPI/models/community.model');
const { ObjectId } = require('mongoose').Types;
const {
  updatePricesForToggleChanges,
} = require('../src/communitiesAPI/services/common/community.service');
const {
  retrieveCommunityProductPrices,
} = require('../src/communitiesAPI/services/web/community.service');
const PaymentProviderUtils = require('../src/utils/paymentProvider.util');

async function updateCommunityTakeRate() {
  // eslint-disable-next-line no-unused-vars
  const excludedCommunitiesFromPassOnGatewayUpdate = [];
  const communitiesToIncludeForBothPassOn = [];
  // await Promise.all([
  //   Communities.updateMany(
  //     {
  //       isActive: true,
  //       passOnTakeRate: true,
  //       // code: { $nin: excludedCommunitiesFromPassOnGatewayUpdate },
  //       code: { $in: communitiesToIncludeForBothPassOn },
  //     },
  //     { passOnPaymentGatewayFee: true }
  //   ),
  //   Communities.updateMany(
  //     {
  //       isActive: true,
  //       passOnTakeRate: false,
  //       // code: { $nin: excludedCommunitiesFromPassOnGatewayUpdate },
  //       code: { $in: communitiesToIncludeForBothPassOn },
  //     },
  //     { passOnPaymentGatewayFee: false }
  //   ),
  // ]);

  const communities = await Communities.find({
    stripeProductId: { $ne: null },
    isActive: true,
    passOnTakeRate: true,
    passOnPaymentGatewayFee: true,
  }).lean();
  const csvStream = [];
  csvStream.push(
    `_id,code,isFreeCommunity,isPaidCommunity,stripeProductId,status,prices,stripePrices,updatedPrices`
      .replace(/\n/g, '')
      .replace(/ /g, '')
  );

  await Promise.allSettled(
    communities.map(async (community) => {
      const passOnPaymentGatewayFee = true;
      const passOnTakeRate = true;

      const stripeProductId = community.stripeProductId;
      const communityPrices = community.prices;

      if (!stripeProductId) {
        const csvRow =
          `${community._id ?? '-'},${community.code ?? '-'},${
            community.isFreeCommunity ?? '-'
          },${community.isPaidCommunity ?? '-'},${
            community.stripeProductId ?? '-'
          },` +
          `NA,` +
          `${JSON.stringify(communityPrices).replace(/,/g, '') ?? '-'},`;
        csvStream.push(csvRow);
        return;
      }

      let pricingInformation = {};

      try {
        pricingInformation = await retrieveCommunityProductPrices(
          community._id
        );
      } catch (err) {
        const csvRow =
          `${community._id ?? '-'},${community.code ?? '-'},${
            community.isFreeCommunity ?? '-'
          },${community.isPaidCommunity ?? '-'},${
            community.stripeProductId ?? '-'
          },` +
          `ERROR,` +
          `${JSON.stringify(communityPrices).replace(/,/g, '') ?? '-'},` +
          `${'-'},` +
          `${'-'}` +
          `${JSON.stringify(err.message).replace(/,/g, '') ?? '-'}`;
        csvStream.push(csvRow);
        return;
      }

      const { prices } = pricingInformation;

      if (prices.length !== communityPrices.length) {
        const csvRow =
          `${community._id ?? '-'},${community.code ?? '-'},${
            community.isFreeCommunity ?? '-'
          },${community.isPaidCommunity ?? '-'},${
            community.stripeProductId ?? '-'
          },` +
          `Prices length Dont match,` +
          `${JSON.stringify(communityPrices).replace(/,/g, '') ?? '-'},` +
          `${JSON.stringify(prices).replace(/,/g, '') ?? '-'}`;
        csvStream.push(csvRow);
        // return;
      }
      const communityPriceArray = communityPrices.map((price) => {
        return {
          currency: price.currency,
          amount: parseFloat(price.cmSetPrice / 100),
          interval: price.interval,
          intervalCount: price.intervalCount,
        };
      });

      const paymentProvider =
        await PaymentProviderUtils.retrievePaymentProvider(
          community.payment_methods
        );
      let updatedPricesDetail;
      try {
        updatedPricesDetail = await updatePricesForToggleChanges(
          stripeProductId,
          community.createdAt,
          communityPriceArray,
          community.baseCurrency,
          passOnTakeRate,
          community.payoutFeeConfigs,
          community.basePayoutFeeConfigs,
          paymentProvider,
          passOnPaymentGatewayFee,
          community.countryCreatedIn
        );

        const updatedCommunity = await Communities.findOneAndUpdate(
          { _id: community._id },
          {
            passOnTakeRate,
            passOnPaymentGatewayFee,
            prices: updatedPricesDetail,
            'config.hasSetProcessingFeePreference': true,
          },
          { new: true }
        );
        const csvRow =
          `${community._id ?? '-'},${community.code ?? '-'},${
            community.isFreeCommunity ?? '-'
          },${community.isPaidCommunity ?? '-'},${
            community.stripeProductId ?? '-'
          },` +
          `SUCCESS,` +
          `${JSON.stringify(communityPrices).replace(/,/g, '') ?? '-'},` +
          `${JSON.stringify(prices).replace(/,/g, '') ?? '-'},` +
          `${
            JSON.stringify(updatedPricesDetail).replace(/,/g, '') ?? '-'
          }`;
        csvStream.push(csvRow);
      } catch (err) {
        const csvRow =
          `${community._id ?? '-'},${community.code ?? '-'},${
            community.isFreeCommunity ?? '-'
          },${community.isPaidCommunity ?? '-'},${
            community.stripeProductId ?? '-'
          },` +
          `ERROR,` +
          `${JSON.stringify(communityPrices).replace(/,/g, '') ?? '-'},` +
          `${JSON.stringify(prices).replace(/,/g, '') ?? '-'},` +
          `${
            JSON.stringify(updatedPricesDetail).replace(/,/g, '') ?? '-'
          }` +
          `${JSON.stringify(err.message).replace(/,/g, '') ?? '-'}`;
        csvStream.push(csvRow);
      }
    })
  );

  const data = csvStream.join('\n');
  await fs.writeFile(
    'updateCommunityTakeRate.csv',
    data,
    'utf-8',
    (err) => {
      if (err) console.log(err);
      else console.log('Data saved');
    }
  );
}

async function start() {
  await mongoClient.connect();

  await updateCommunityTakeRate();

  logger.info('Completed');
  process.exit(0);
}

start();
