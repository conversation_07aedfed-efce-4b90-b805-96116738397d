require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const mongoClient = require('../src/mongoClient');

const EventModel = require('../src/communitiesAPI/models/communityEvents.model');
const { EVENT_STATUS } = require('../src/communitiesAPI/constants');

const updateWithLimit = async (query, updateQuery, total, limit) => {
  const rounds = Math.ceil(total / limit);
  const queries = [];
  for (let i = 0; i < rounds; i++) {
    queries.push(i);
  }

  // eslint-disable-next-line no-unused-vars
  for await (const i of queries) {
    const events = await EventModel.find(query).select('_id').limit(limit);
    const ids = events.map((event) => event._id);
    await EventModel.bulkWrite([
      {
        updateMany: {
          filter: {
            _id: { $in: ids },
            ...query,
          },
          update: {
            $set: updateQuery,
          },
        },
      },
    ]);
  }
};

const start = async () => {
  await mongoClient.connect();

  const deletedQuery = { isActive: false, status: 'Active' };
  const deletedUpdateQuery = { status: EVENT_STATUS.DELETED };
  const publishedQuery = { isActive: true, status: 'Active' };
  const publishedUpdateQuery = { status: EVENT_STATUS.PUBLISHED };

  const totalDeleted = await EventModel.countDocuments(deletedQuery);
  const totalPublished = await EventModel.countDocuments(publishedQuery);

  const updateManyThreshold = 500;

  await updateWithLimit(
    deletedQuery,
    deletedUpdateQuery,
    totalDeleted,
    updateManyThreshold
  );
  await updateWithLimit(
    publishedQuery,
    publishedUpdateQuery,
    totalPublished,
    updateManyThreshold
  );

  console.log('Completed');
  process.exit(0);
};

start();
