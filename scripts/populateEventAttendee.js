require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});

const mongoClient = require('../src/mongoClient');

const CommunityEvents = require('../src/communitiesAPI/models/communityEvents.model');
const EventAttendees = require('../src/communitiesAPI/models/eventAttendees.model');

async function retrieveCommunitiesWithEventObjectIds() {
  const communities = await CommunityEvents.aggregate([
    {
      $unwind: {
        path: '$communities',
        preserveNullAndEmptyArrays: false,
      },
    },
    {
      $group: {
        _id: '$communities',
        eventObjectIds: {
          $push: '$_id',
        },
      },
    },
  ]);

  return communities;
}

const retrieveUpdatePipelineQuery = (aggregatedResults) => {
  const pipeline = aggregatedResults.map((item) => {
    return {
      updateMany: {
        filter: {
          eventObjectId: { $in: item.eventObjectIds },
        },
        update: {
          $set: { communityObjectId: item._id },
        },
      },
    };
  });
  return pipeline;
};

const updateEventAttendees = async () => {
  const aggregatedResults = await retrieveCommunitiesWithEventObjectIds();
  const pipeline = retrieveUpdatePipelineQuery(aggregatedResults);
  await EventAttendees.bulkWrite(pipeline);
};

const start = async () => {
  await mongoClient.connect();

  await updateEventAttendees();

  console.log('Completed');
  process.exit(0);
};

start();
