files:
  "/etc/systemd/system/prometheus-node-exporter.service":
    content: |
      [Unit]
      Description=Prometheus Node Exporter Service
      After=network.target

      [Service]
      User=ec2-user
      Group=ec2-user
      Type=simple
      ExecStart=/usr/local/bin/node_exporter

      [Install]
      WantedBy=multi-user.target
  "/etc/systemd/system/mtail.service":
    content: |
      [Unit]
      Description=Mtail Service
      After=network.target

      [Service]
      User=ec2-user
      Group=ec2-user
      Type=simple
      ExecStart=/usr/local/bin/mtail --progs /etc/nginx.mtail --logs /var/log/nginx/access.log

      [Install]
      WantedBy=multi-user.target
container_commands:
  01-check-status:
    command: systemctl is-active prometheus-node-exporter || echo "prometheus-node-exporter not active"
    ignoreErrors: true
  02-download-and-start-if-inactive:
    command: |
      if sudo systemctl is-active prometheus-node-exporter; then
        sudo systemctl restart prometheus-node-exporter
      else
        wget https://github.com/prometheus/node_exporter/releases/download/v1.5.0/node_exporter-1.5.0.linux-amd64.tar.gz
        tar xzf node_exporter-1.5.0.linux-amd64.tar.gz
        sudo cp node_exporter-1.5.0.linux-amd64/node_exporter /usr/local/bin/node_exporter
        sudo systemctl daemon-reload
        sudo systemctl enable prometheus-node-exporter
        sudo systemctl start prometheus-node-exporter
      fi
    ignoreErrors: true
  03-check-mtail-status:
    command: systemctl is-active mtail || echo "mtail not active"
    ignoreErrors: true
  04-download-and-start-mtail-if-inactive:
    command: |
      if sudo systemctl is-active mtail; then
        sudo systemctl restart mtail
      else
        wget https://github.com/google/mtail/releases/download/v3.0.5/mtail_3.0.5_linux_amd64.tar.gz
        tar xzf mtail_3.0.5_linux_amd64.tar.gz 
        sudo cp mtail /usr/local/bin/mtail
        sudo echo 'counter http_requests_total by status
          /^(\S+) (\S+) (\S+) \[(\d+\/\w+\/\d+:\d+:\d+:\d+ \+\d+)\] "(\S+) (\S+) (\S+)" (\d{3}) (\d+) "(\S+)" "([^"]*)" "([^"]*)"/ {
          http_requests_total[$8]++
        }' > /etc/nginx.mtail
        sudo systemctl daemon-reload
        sudo systemctl enable mtail
        sudo systemctl start mtail
        sudo systemctl status mtail
      fi
    ignoreErrors: true
